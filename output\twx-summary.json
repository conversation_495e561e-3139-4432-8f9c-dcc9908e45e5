{"metadata": {"project": {"id": "2066.ac303e4d-473d-47d0-8c50-2337baf99edf", "name": "NBE ODC Processes", "shortName": "NBEODCR", "description": "", "isToolkit": false, "isSystem": false}, "branch": {"id": "2063.be263739-2390-4bdc-9654-7b1c99f9fa73", "name": "Main", "description": ""}, "snapshot": {"id": "2064.712d7eff-cd2c-4ccf-bc3b-f6db9d6589b6", "name": "ODC_v1", "description": "<HTML><HEAD></HEAD><BODY>Data for the TWX awesome parser</BODY></HTML>", "creationDate": "2025-06-11T11:26:18.009+03:00"}, "buildInfo": {"buildId": "BPM8600-20211214-184954", "buildVersion": "8.6.3", "buildDescription": "IBM Business Process Manager V8.6.3.21030 - 20211214_2112 - BPM8600-20211214-184954"}}, "statistics": {"totalObjects": 280, "objectTypes": 11, "toolkits": 0, "extractedAt": "2025-06-11T11:15:57.424Z", "sourceFile": "TWX Example.twx"}, "objectsByType": [{"typeName": "Process", "count": 120, "objects": [{"id": "1.028269d3-7705-46be-b7bb-3893e93d0240", "name": "Query BC contract 2", "versionId": "1b81c58c-4961-4b95-a8fa-3422edd86cbe", "hasDetails": true}, {"id": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "name": "ClosureACT02 - Review ODC Closure Request by Compliance Rep", "versionId": "21577f8c-2df9-4566-867c-ec0b2d8182db", "hasDetails": true}, {"id": "1.09d158a9-e4e4-4987-827f-4a4923c76843", "name": "Get HUB name by code", "versionId": "5705639a-56b2-4fad-b807-141f3552a058", "hasDetails": true}, {"id": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "retrieve multiTenorDates", "versionId": "91cf47a3-59bb-4063-9c71-0c4efd9afc23", "hasDetails": true}, {"id": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "name": "Retrieve ProductCode DB", "versionId": "41ebe9f1-593e-4b17-a752-6b1b929d7bf0", "hasDetails": true}, {"id": "1.13b84d91-6118-4776-9c68-8accac7d1220", "name": "Validate Required Documents", "versionId": "317a030d-7fc8-4bf7-97b2-157878cd7c75", "hasDetails": true}, {"id": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "name": "Get ODC charges 2", "versionId": "33995fae-589a-4b95-bb3c-8abada244c7e", "hasDetails": true}, {"id": "1.159431e7-0daa-4266-819a-968be03f82ff", "name": "testASA", "versionId": "7b68ed25-1cc4-4606-b79a-63185f7456fe", "hasDetails": true}, {"id": "1.1b3eba82-0c58-41be-ba53-c071d336f323", "name": "Retrieve Search Result Data", "versionId": "d14e6e91-2dc3-4d75-aaf4-7c52bd9ebb44", "hasDetails": true}, {"id": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "name": "ClosureACT06 - ODC Closure Execution Review", "versionId": "8004b27f-ff8f-461d-bd60-38d9fb4d7368", "hasDetails": true}, {"id": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "Initiate ODC Process", "versionId": "7e52ed01-6dbe-4b2e-b2e1-421b774f9c5c", "hasDetails": true}, {"id": "1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8", "name": "Test Date", "versionId": "ef91abe5-1a3c-439b-88b8-8efbc4c61758", "hasDetails": true}, {"id": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "name": "Get Actions for screens", "versionId": "481d6826-1a12-4925-b771-84e41e86c71b", "hasDetails": true}, {"id": "1.2af38b7e-9461-4887-9ad2-24d4565ee49b", "name": "ClosureACT01 - Create ODC Closure Request", "versionId": "2e4dd8db-74dc-496c-b70b-cbc0710d127c", "hasDetails": true}, {"id": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exception Handling", "versionId": "2fd5fb36-d939-4309-899f-4e0ac13819e5", "hasDetails": true}, {"id": "1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "name": "Update ODC status", "versionId": "1175767b-5bee-4b1a-9730-a32d090df2ed", "hasDetails": true}, {"id": "1.2d3ab562-82df-48a5-9de7-f5d964218191", "name": "Set document default properties", "versionId": "e1c75fd3-f8b2-4779-a4f1-4aecf972a0bc", "hasDetails": true}, {"id": "1.2d90a780-82bb-4941-b27e-d586607ce191", "name": "Audit Request History", "versionId": "0df5b9dd-9586-48b7-9813-b17c9e15df1d", "hasDetails": true}, {"id": "1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd", "name": "Authorize FileNet", "versionId": "97315d56-b618-4e50-85da-f470f8f8aa63", "hasDetails": true}, {"id": "1.2f8e9d68-4adb-42dd-a9f6-634969db03dc", "name": "Check GL Account", "versionId": "b6b36f0f-af11-4144-aa46-d39750902231", "hasDetails": true}, {"id": "1.2f93c4b5-368e-4a13-aff6-b12926260bb3", "name": "retrieve DB lookups", "versionId": "a98912a2-804a-4147-876d-4038ef0000fb", "hasDetails": true}, {"id": "1.302e4a54-16ab-47b9-9b70-d933329c72e7", "name": "Hub Filter service", "versionId": "be2a3126-ee11-4cb2-a3b8-708159a021f2", "hasDetails": true}, {"id": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "retrieve Search Criteria Result", "versionId": "b2ded4be-8e2d-4411-8c25-3346f14d22f2", "hasDetails": true}, {"id": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "Generate Remittance Letter", "versionId": "0f8b5d23-2ad9-4e92-a134-ff82524258aa", "hasDetails": true}, {"id": "1.40a7d34d-1192-436a-ba5a-0360265d3261", "name": "Calculate Change Amount", "versionId": "c9c58a63-fe98-45f3-a2d6-d2f0f3f3f3ff", "hasDetails": true}, {"id": "1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "name": "Audit ODC Update Request", "versionId": "53c70d0a-2e2e-4c55-9973-e0c4eacf2f52", "hasDetails": true}, {"id": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "Col02 - Review ODC Collection Request", "versionId": "e44dbf59-fb14-40c0-9374-7689ef0a2c4d", "hasDetails": true}, {"id": "1.46b984a3-b4ad-405a-abd3-8631f907efe4", "name": "Create FileNet Folder", "versionId": "f8e30919-05e5-410e-b324-f077ea1b0472", "hasDetails": true}, {"id": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "name": "Validate Required Documents 2", "versionId": "f8eed9d0-81b2-4e47-8fa4-d6d5eebd1940", "hasDetails": true}, {"id": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "Get Charges Completed", "versionId": "57b9519b-c7b3-4dd4-9f16-ff374ea487f8", "hasDetails": true}, {"id": "1.4855448a-1c86-4167-afa5-d79d3273d48b", "name": "get RequestID", "versionId": "6e3bc9bd-3726-456b-932c-ef8ab05bbde2", "hasDetails": true}, {"id": "1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f", "name": "Get BIC Codes", "versionId": "25d936f9-6075-44a7-8da2-d1e5f68e9901", "hasDetails": true}, {"id": "1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8", "name": "ClosureACT03 - Review ODC Closure Request by Trade FO", "versionId": "e01b683e-1c97-4ca9-99a7-3b87a9627907", "hasDetails": true}, {"id": "1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd", "name": "Calculate Default Amount Complete", "versionId": "44347ee5-e5d4-4e52-902a-0c75ba8f74fb", "hasDetails": true}, {"id": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "Retrieve Request Number", "versionId": "f95d146a-6c23-4ba7-85b6-bf3d0ee3d552", "hasDetails": true}, {"id": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Check Parent Request", "versionId": "c1d19b2a-90ea-4231-9cca-065d226aa8e1", "hasDetails": true}, {"id": "1.55f0e416-bc0f-4110-9fce-c26af87b8915", "name": "ODC Search Screen", "versionId": "296402b8-231b-4455-8fa0-710cab39e086", "hasDetails": true}, {"id": "1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f", "name": "Ajax get customer accounts", "versionId": "d14a310e-5b6f-4028-93bd-25d5318f176b", "hasDetails": true}, {"id": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "Query BC Contract", "versionId": "12a25f86-b68a-4af4-958b-1a646d73db0c", "hasDetails": true}, {"id": "1.58febcb0-50a3-4963-8368-32c96c00d116", "name": "Get Request and Customer Data", "versionId": "74c4c993-b191-4db5-b2ce-3a3a004e5f54", "hasDetails": true}, {"id": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "name": "cancel request", "versionId": "eb358d90-4d43-42df-a4a7-cd88da2d55ed", "hasDetails": true}, {"id": "1.5d77055c-98a8-4191-9b74-c7120a5823be", "name": "Create", "versionId": "78d80004-b3e8-4015-8160-b3ae3b3ad3ea", "hasDetails": true}, {"id": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "retrieve Odc request Data", "versionId": "55b7163e-f619-4530-b569-bf80238dd204", "hasDetails": true}, {"id": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "name": "Get Actions By ScreenName 2", "versionId": "d3e91fab-eaa6-42c0-bc2b-1993a9797adb", "hasDetails": true}, {"id": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Col01 - Create ODC Collection Request", "versionId": "2d44f93c-7507-483a-9a42-c82155bd0166", "hasDetails": true}, {"id": "1.672c16cb-04d7-4e67-a904-779d9009e1ce", "name": "Get Debited Nostro Vostro Account", "versionId": "36e03776-e05b-452d-b6de-63a7ffa7e26f", "hasDetails": true}, {"id": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "name": "ODC BO Initialization", "versionId": "9ee21618-4ac4-44bd-9e2a-77dc15c7a31c", "hasDetails": true}, {"id": "1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871", "name": "Ajax Get ODC Product codes", "versionId": "62f7d590-81c1-43b2-8adf-64124adb1f3e", "hasDetails": true}, {"id": "1.69ebf680-a301-4a98-ab97-9803a829bb25", "name": "Get Required Documents", "versionId": "9ee7bba9-5dd2-4974-a177-471c44906f41", "hasDetails": true}, {"id": "1.6a8c5669-195a-4ca0-80cc-606bbf87f9d0", "name": "Service Flow_1", "versionId": "feb0af45-0b25-44df-bad6-f8288abfe0c6", "hasDetails": true}, {"id": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "Act05 - ODC Execution Hub – Initiation Review", "versionId": "70fbc4e8-bbe0-438f-a2d2-24bceb75bd61", "hasDetails": true}, {"id": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "name": "Print barcode", "versionId": "8ed1b310-c0a7-4a1b-9389-c19ee528707b", "hasDetails": true}, {"id": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Start ODC Request", "versionId": "bc883da2-9235-4dad-85f4-25817ed4b654", "hasDetails": true}, {"id": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "name": "Trade Compliance", "versionId": "86e4fd32-c430-438d-a46b-870d97e311a2", "hasDetails": true}, {"id": "1.769a8281-bf53-46c6-a4c8-571fc192f312", "name": "Get charge amount", "versionId": "ce4d2ec4-0272-4163-b263-5eeb0cf5d121", "hasDetails": true}, {"id": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "Get ODC Initiators", "versionId": "f4a258c2-bb9f-42ca-ada7-e28ce1a91934", "hasDetails": true}, {"id": "1.77e38ca7-0df5-4ad2-98b9-25547cc61e1e", "name": "Retrieve Country List From FC", "versionId": "057ce9c7-5f9c-4321-89b4-ce0ffdbfbf78", "hasDetails": true}, {"id": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Audit Create Data", "versionId": "1382d1e5-4d3e-48a1-9c56-e2c1342710c9", "hasDetails": true}, {"id": "1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "name": "Document Generation", "versionId": "f6012b23-364c-4dcc-9b2f-62a0cad72961", "hasDetails": true}, {"id": "1.812db3ff-6589-474c-bcc5-21fde39e4d25", "name": "Cancel and Delete Transactions", "versionId": "772b74e8-d59f-4526-a7c0-52728951475d", "hasDetails": true}, {"id": "1.81656d33-5348-479b-a7af-5631356d9476", "name": "Set Status And Sub Status", "versionId": "0bd7cd63-4a1c-4d29-b8ec-8b4dc6aebb2d", "hasDetails": true}, {"id": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Create Folder FileNet", "versionId": "6415877a-8b28-461c-8678-da20a8bd7fad", "hasDetails": true}, {"id": "1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9", "name": "Create CIF Folder", "versionId": "f98ead3b-39b5-422a-b96a-f066cc63e6a5", "hasDetails": true}, {"id": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "Get Parent Request 2", "versionId": "f58cde10-b5fc-458d-8b18-10ee2601f718", "hasDetails": true}, {"id": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "name": "Init ODC Request", "versionId": "8856132e-d9e1-48a0-99c8-999f2e996e33", "hasDetails": true}, {"id": "1.8e583b1e-1719-4e19-a6ce-6f41202527d4", "name": "Generate BPM Request Number", "versionId": "726a60e9-20d1-464f-be10-cac0f6e5b358", "hasDetails": true}, {"id": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "name": "ACT04 - ODC Execution Hub Initiation", "versionId": "69ae4197-2b05-43e5-b85f-c7987517a34b", "hasDetails": true}, {"id": "1.9395be71-805c-4443-85ee-48c92de214a3", "name": "Compare Validations", "versionId": "388fedc6-f078-4b14-a057-d0465cc60849", "hasDetails": true}, {"id": "1.93bd8272-8f96-42db-92a3-1bdb463c2c28", "name": "ClosureACT05 - ODC Closure Execution", "versionId": "54a260cb-e48b-4db7-8b45-708bb909eb01", "hasDetails": true}, {"id": "1.9862410e-bba9-43a0-b511-69ae076e3b64", "name": "test", "versionId": "06f9ad28-019c-42f1-82d6-ef124c4d347e", "hasDetails": true}, {"id": "1.9db9271e-48cd-403e-accb-5dcc0e326e99", "name": "ACT04 - ODC Execution Hub Initiation 3", "versionId": "aabe6006-a38e-4f86-b9a0-8898fe3408db", "hasDetails": true}, {"id": "1.9e31ab59-ee74-4591-92f7-292855228c1b", "name": "Get Request Type", "versionId": "7d2185a5-4430-4985-8522-ec1a6488d07d", "hasDetails": true}, {"id": "1.a1d1f1de-87c1-424f-8115-086a8221db8b", "name": "Audit Reversal Data", "versionId": "12879b4c-7ebd-4500-9f7c-a58760291453", "hasDetails": true}, {"id": "1.a28b2951-6ed0-484f-81e5-3d5d147c88f9", "name": "Init Create ODC Request Service", "versionId": "917a87a1-d033-43a3-a2dd-ce56114e8edc", "hasDetails": true}, {"id": "1.a3badb88-0400-49ae-ac1d-0e0ace050eb7", "name": "ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير De_1", "versionId": "5bba26f0-66b8-470e-a1f1-91cace51e36c", "hasDetails": true}, {"id": "1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "name": "BoxUtils", "versionId": "82bbc204-9432-4081-b8a0-104b2219474c", "hasDetails": true}, {"id": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "retrieve charges and commision data", "versionId": "863323c5-c9f1-4ebb-8bb6-835644e37b23", "hasDetails": true}, {"id": "1.a6205b87-57cc-47bb-abfc-edff0743b08e", "name": "Send Mail to CAD Team", "versionId": "35c7c37e-292f-400c-bfe4-ad2eaa23ecc9", "hasDetails": true}, {"id": "1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc", "name": "getEPV", "versionId": "2c9659be-2ace-42c5-a494-3e8842519db1", "hasDetails": true}, {"id": "1.a9e6ba43-572e-4e44-b72a-285af67ec153", "name": "Get ODC charges", "versionId": "59c62612-504b-4989-a138-67fa4172d4bf", "hasDetails": true}, {"id": "1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5", "name": "retrieve invoice data", "versionId": "5534fa7b-3667-4287-83ca-3125449f7b02", "hasDetails": true}, {"id": "1.b1a705db-e502-470d-ad30-b08231994382", "name": "Get Party Details", "versionId": "685b456d-8331-4e15-8435-507759e74816", "hasDetails": true}, {"id": "1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1", "name": "Act01 - Create or Amend ODC Request", "versionId": "1bd76b05-5153-462d-993b-a53d8e782b07", "hasDetails": true}, {"id": "1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f", "name": "Get Exchange Rate", "versionId": "b9d3a129-d9ba-4e97-91d9-e9e67aa01b6d", "hasDetails": true}, {"id": "1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e", "name": "ACT04 - ODC Execution Hub Initiation 2", "versionId": "94c77e27-2519-45af-b545-d09e381df821", "hasDetails": true}, {"id": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "name": "Execution HUB Filter Service", "versionId": "43837a43-23fe-4fde-9d48-cd45e4400de0", "hasDetails": true}, {"id": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "name": "Set document default properties 2", "versionId": "db4be141-e3c9-4dd3-97a2-997aa022479e", "hasDetails": true}, {"id": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "Get Customer and Party Account List", "versionId": "93427c98-30b1-4e9b-afd1-dd78819d8b88", "hasDetails": true}, {"id": "1.b9bf1811-6e1e-41a3-828b-39059fa168cb", "name": "RACT01 - Create ODC Reversal Request", "versionId": "964de40b-0522-459f-a4fb-b63a9b125c1e", "hasDetails": true}, {"id": "1.badb27e5-ab0e-4227-bebf-eb6d54984f36", "name": "Audit Closure Data", "versionId": "18a28f54-72aa-4d3c-9b1c-26a3f9605bed", "hasDetails": true}, {"id": "1.bd03b303-b465-40a1-a4c5-f08887b0cc2e", "name": "ODC Position Screen", "versionId": "20231a0b-478b-48ec-99b6-aa3843bb0658", "hasDetails": true}, {"id": "1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9", "name": "ACT06 -Print Remittance Letter", "versionId": "d2ff0bc2-3f2f-40de-8897-6c929dcae36d", "hasDetails": true}, {"id": "1.be113c16-c0f5-4037-b134-f403be3fe56f", "name": "Service Flow", "versionId": "779fb191-e982-44b2-b736-4f3fa7eb9f53", "hasDetails": true}, {"id": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "Act03 -  Review ODC Request by Trade Front Office", "versionId": "b5c8a00c-4a4c-420f-95bd-6203311f9b9a", "hasDetails": true}, {"id": "1.c71c7233-23ac-4e5f-a241-09cd96502615", "name": "RACT02 - Review ODC Reversal Request", "versionId": "5114311e-2333-45ef-981f-94070d569a0c", "hasDetails": true}, {"id": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "Create amend audit service", "versionId": "2f282072-cc7e-4f91-a680-b115235ae194", "hasDetails": true}, {"id": "1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285", "name": "Ajax Get Customer Account Details", "versionId": "bc13d0da-10ab-416c-b52c-f7897f3c5bf3", "hasDetails": true}, {"id": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "name": "Get terms and conditions", "versionId": "3f6df8cc-626f-4428-a80d-46cc65930974", "hasDetails": true}, {"id": "1.d034d01a-267a-423b-a582-7444da255e09", "name": "Get Request Nature", "versionId": "cf53053e-c203-4386-b5cb-2dd80b75ae52", "hasDetails": true}, {"id": "1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5", "name": "Get Exchange Rate 2", "versionId": "b140fdd6-57d9-4bee-95fe-e4736776d96d", "hasDetails": true}, {"id": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "name": "get search Criteria List", "versionId": "e3330927-d39f-472a-8e13-00fffb123dde", "hasDetails": true}, {"id": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Col01 - test", "versionId": "b6e7910f-ef9c-4691-89d3-f6b311230cba", "hasDetails": true}, {"id": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "Send Escalation Mail", "versionId": "2adb820e-e4d2-4b13-812b-a734fd7d3c1e", "hasDetails": true}, {"id": "1.da205dca-ccab-4203-9f89-719cb8957cc5", "name": "ClosureACT04 - Review ODC Closure Request by Trade FO Checker", "versionId": "f7a78373-5e71-41d8-8e11-d6c9c7e9ef57", "hasDetails": true}, {"id": "1.e0a673da-2e76-4a12-8896-d868bab63e83", "name": "Deployment Service Flow", "versionId": "6246d9e2-970f-4112-a5ec-ca82a319deaf", "hasDetails": true}, {"id": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "name": "Get Required Documents 2", "versionId": "2867c960-8837-4a75-8b94-f1c61e5f5356", "hasDetails": true}, {"id": "1.e2ffa7a5-7c4f-4625-90e6-1d620f946497", "name": "Create Folder Structure", "versionId": "c7dc9099-18ab-4d7b-adff-38c22761dc34", "hasDetails": true}, {"id": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "ACT02 Review ODC Request By Compliance Rep", "versionId": "99d1d41f-38c8-4a04-b60c-56bdd6a0e721", "hasDetails": true}, {"id": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "Log FC Transactions", "versionId": "11b29981-5b4f-4708-b14b-a10a0c95da8b", "hasDetails": true}, {"id": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "Get account transactions", "versionId": "2ab775de-cfd8-4ef4-84c3-78d836c0426f", "hasDetails": true}, {"id": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "retrieve Odc request Data oneToMany Relation", "versionId": "998f25b5-9684-4c61-abb0-a378b8c9bec3", "hasDetails": true}, {"id": "1.e6908115-4586-455f-bf73-b96b60019972", "name": "Send Rem Letter Mail", "versionId": "a2a3e3ed-6c41-41b9-ba99-4e21b4092909", "hasDetails": true}, {"id": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "name": "History", "versionId": "60722f6e-3f02-4e69-af40-bc9baf2ba628", "hasDetails": true}, {"id": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "name": "Get Customer Information", "versionId": "d7b6d4fd-c803-4126-b7ce-66b725ee7fe1", "hasDetails": true}, {"id": "1.f18b1174-01f4-419d-80de-4b239479a7f1", "name": "get Reversal Reason id", "versionId": "0cd7d184-dc72-475b-a146-f1570fd0f490", "hasDetails": true}, {"id": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "retrieve bills data", "versionId": "2cfea046-37c5-434e-9f08-99940c2849ff", "hasDetails": true}, {"id": "1.f3735333-84b5-461b-9811-0817300dfb0b", "name": "Get bank BIC codes", "versionId": "51adad51-fef8-40a1-bfc8-cdc9146187c5", "hasDetails": true}, {"id": "1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6", "name": "UT TF", "versionId": "********-0758-4d6b-a6c7-e9ec794dddf0", "hasDetails": true}, {"id": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "name": "Branch Hub filter service", "versionId": "1812600f-233f-4424-8b78-d0d82862e944", "hasDetails": true}, {"id": "1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e", "name": "Get Exchange Rate New", "versionId": "99590f81-1b9d-486b-ac13-6c3e800fdae6", "hasDetails": true}]}, {"typeName": "Business Object", "count": 49, "objects": [{"id": "12.004a1efa-0a17-40a6-a5b9-125042216ff4", "name": "ActionConditions", "versionId": "d245357c-55f6-4b91-a732-7c4043636e80", "hasDetails": false}, {"id": "12.06a9548f-e79a-4754-a93e-956a150c9a91", "name": "Approvals", "versionId": "e9208e24-6700-4c0c-9ed4-d845f8935f5b", "hasDetails": false}, {"id": "12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "name": "Attachment", "versionId": "8dd42bf7-8ad9-4b16-a259-f0d503cb3f7c", "hasDetails": false}, {"id": "12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8", "name": "ResponseTemplatePojo", "versionId": "f0053244-cb35-4537-a419-e0e17f8ad5f9", "hasDetails": false}, {"id": "12.1974aaf6-f654-42f2-b5ef-93386ef5d51e", "name": "GeneratedDocumentInfo", "versionId": "45f165d0-e514-49a7-8498-5b500a69e2c0", "hasDetails": false}, {"id": "12.253b0033-bc46-490c-bac0-6d6c794d403e", "name": "ItmDef_1914247323_253b0033_bc46_490c_bac0_6d6c794d403e", "versionId": "ba658780-2273-442e-9e79-8705dc1dfb57", "hasDetails": false}, {"id": "12.29905e05-3ca0-49e0-96a6-a1035efdf052", "name": "ContractLiquidation", "versionId": "f3cd86fa-d1cf-4499-a838-b5e1d5caf82b", "hasDetails": false}, {"id": "12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "name": "DocumentGenerationRequest", "versionId": "9621acdb-733d-40ec-8438-385f61755704", "hasDetails": false}, {"id": "12.2d7d54a5-0d98-443e-ad21-49ff93c6dc08", "name": "Bills", "versionId": "18eabf5b-4435-4046-a557-0c264e1a1f41", "hasDetails": false}, {"id": "12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d", "name": "ItmDef_1412542389_2e5f0222_20a7_4c21_bdaa_5284bb68cf3d", "versionId": "fadb4a5e-db8e-42be-9c01-28ab590158e1", "hasDetails": false}, {"id": "12.3e75c63e-2df3-4510-8895-56d6203d0609", "name": "PartyDetails", "versionId": "4b759312-31eb-4636-9a26-32bd405f2792", "hasDetails": false}, {"id": "12.4e08a264-0cfe-41ef-b4b7-74959345f193", "name": "searchCriteria", "versionId": "975a8d34-9f4e-483f-928e-101e92da51d7", "hasDetails": false}, {"id": "12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134", "name": "FCTransactions", "versionId": "e8a18f78-609f-4ebd-b237-0c1e584d8df0", "hasDetails": false}, {"id": "12.53a1b1c3-7342-40f0-9f89-24bf1d5ea7cf", "name": "ImporterDetails", "versionId": "e701223e-814e-44ed-ba04-9e85dc33fa56", "hasDetails": false}, {"id": "12.55bb335a-d3b3-4749-a082-859e2a48ace9", "name": "attachmentDetails", "versionId": "3f34233e-558f-47d5-a5b8-eedb1dfd6349", "hasDetails": false}, {"id": "12.57fea321-b2d0-479e-93d1-735d7a7ae033", "name": "EcmDocumentInformation", "versionId": "f88aadb3-fa4f-4599-8b1b-0c50210bc7b0", "hasDetails": false}, {"id": "12.58617e47-17c6-4d9a-8fb0-1edafe8cd663", "name": "MultiTenorDates", "versionId": "890399b8-9a6e-45d8-ab02-6276e368589b", "hasDetails": false}, {"id": "12.5ab4dfaa-bb18-4ceb-b653-8f1a20041d49", "name": "Msg_1108305804_5ab4dfaa_bb18_4ceb_b653_8f1a20041d49", "versionId": "70baabff-bfe0-4de3-a967-e99d5ef6c292", "hasDetails": false}, {"id": "12.5f8547c1-515a-481c-b74a-f20ec40faff9", "name": "ODCRequest", "versionId": "c23098df-71e9-4add-9e4a-203c60e154f7", "hasDetails": false}, {"id": "12.65f0e3ad-27ed-4e8c-9713-56c6ccbffff5", "name": "FinancialDetailsFO", "versionId": "40b3e2b9-50f1-4880-8304-98491d4b3feb", "hasDetails": false}, {"id": "12.6b7da6cc-ded4-44e5-a5ab-864b19299f2f", "name": "Invoice", "versionId": "29561c58-f88c-4499-b4d2-17d0185a8e32", "hasDetails": false}, {"id": "12.6caeaaf8-3886-4b02-bf7a-25371ece2294", "name": "ODCCollection", "versionId": "89cc14fe-8485-44b2-bf63-e7a586e37aef", "hasDetails": false}, {"id": "12.74193c98-6d23-4451-b526-7bb6b010b116", "name": "Drawee", "versionId": "a4efaae0-414d-4b4e-ae10-f7870c294d95", "hasDetails": false}, {"id": "12.76e102f0-ce5e-45ff-b7b0-32b0993a6588", "name": "searchResult", "versionId": "7671f21b-6118-4ba6-8474-e5b8a87d8e99", "hasDetails": false}, {"id": "12.796a077f-9dd5-47e3-88a9-4908b204fdb0", "name": "RequestState", "versionId": "e24d0f34-aa04-4e0d-8685-404bed09f7b6", "hasDetails": false}, {"id": "12.80dacdbe-03aa-474f-9fb6-3a43c32fef58", "name": "ContractCreation", "versionId": "deef7a36-1140-49d5-8ac2-d93b55f1d213", "hasDetails": false}, {"id": "12.8566ac97-e9e5-481e-9605-42b0dc11da47", "name": "sequence", "versionId": "841603be-bbfc-45e7-a27a-72b8b1fc5b25", "hasDetails": false}, {"id": "12.88fbc33e-4ee3-4815-9620-f04917f7a3a2", "name": "FCCollections", "versionId": "0a4a3615-6604-4076-b7d3-fe5b6311cd0e", "hasDetails": false}, {"id": "12.8cf8193c-ddf5-44ec-8584-1443729<PERSON>ce", "name": "RequestTemplatePojo", "versionId": "0367ab96-f04e-4f66-8ff2-a386f18afe43", "hasDetails": false}, {"id": "12.90d4772d-4081-4a73-a8c2-e7f904511cd6", "name": "AccountDetails", "versionId": "9a9a9242-0b9c-4928-83e5-7caba133c00d", "hasDetails": false}, {"id": "12.9ad7c20d-998e-4629-84b9-f365c85a6733", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "versionId": "229827b8-d6db-4c57-a151-c007e05fff6c", "hasDetails": false}, {"id": "12.a0722085-9246-4af1-b86e-8a21be7e94cc", "name": "DocumentGenerationResponse", "versionId": "7c7bae4a-dc4f-49bb-950c-4ad1a8315b92", "hasDetails": false}, {"id": "12.a07f8f54-e398-452e-8542-5bef5fc7fbd7", "name": "userConditions", "versionId": "e4ccea8c-6fec-408d-9bc7-ab9c175cd386", "hasDetails": false}, {"id": "12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "name": "CustomerInfo", "versionId": "8a39674b-485d-4bf1-be4d-e4b844ec41f4", "hasDetails": false}, {"id": "12.a894d8aa-2563-4e74-8c8a-8b87c2aba05b", "name": "Msg_913804518_a894d8aa_2563_4e74_8c8a_8b87c2aba05b", "versionId": "9ee35003-7589-40b0-9cc6-22e4f584c2b8", "hasDetails": false}, {"id": "12.aad95560-c6ad-4add-bae6-8a1b432b6545", "name": "ProductShipmentDetails", "versionId": "e69b336a-65bb-4607-abc2-c5ba51d705a8", "hasDetails": false}, {"id": "12.b698dbfb-84da-40a5-9db3-676815055e65", "name": "ECMproperties", "versionId": "75acba00-60e4-4027-b3cc-265d0523c0f3", "hasDetails": false}, {"id": "12.b80<PERSON>ace-d932-480c-ab0a-f072d7a8a5db", "name": "CollectingBank", "versionId": "fec6a28e-99a6-4568-aaf2-4d8095e9e6d3", "hasDetails": false}, {"id": "12.ba753452-90b9-4866-9471-8425f722be03", "name": "BasicDetails", "versionId": "4a3d8dfb-993f-4c3d-bf4b-0c157fe190b3", "hasDetails": false}, {"id": "12.c923d7fa-ce48-4122-8c3e-afbd6e8e5917", "name": "Parties", "versionId": "7897d0b4-94c4-4df5-a5b7-7907a1cfb653", "hasDetails": false}, {"id": "12.cbf7e7d2-cb5c-4667-889f-9f05b7c947ce", "name": "odcParties", "versionId": "ab9beade-75aa-4dbf-9ed3-cb01b656bffd", "hasDetails": false}, {"id": "12.cd8189aa-0888-4e27-9937-95dfb001a822", "name": "ReversalReason", "versionId": "8c909dae-62e2-4f8e-8063-6101e9ab2c34", "hasDetails": false}, {"id": "12.cdbb2c22-8516-45ab-925a-2933e6e1bed5", "name": "Drawer", "versionId": "6c1d2559-c84c-44cb-810c-04104de10045", "hasDetails": false}, {"id": "12.d269ee9a-c6ac-4c71-908a-48eb92d1c60f", "name": "partyTypes", "versionId": "3f5bdf4b-05f7-4c9d-bd70-08490f422497", "hasDetails": false}, {"id": "12.d2a537b9-92c2-48c5-a18c-72e499761454", "name": "FinancialDetailsBranch", "versionId": "8c07c51c-ddf3-41f9-906d-f80bea261b03", "hasDetails": false}, {"id": "12.e3018bad-b453-4bf5-96fd-09a5141cd061", "name": "ChargesAndCommissions", "versionId": "941edc2c-cd76-47dc-9f9d-2c425890710b", "hasDetails": false}, {"id": "12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "name": "StepLog", "versionId": "4e50d24e-0965-4f32-8eaf-42e04df85414", "hasDetails": false}, {"id": "12.ed32bb17-02ab-4015-9dea-e9d2bc787f0d", "name": "AmountDetails", "versionId": "a2d3b6a3-9f6a-44a5-9e05-ba9546a1fe10", "hasDetails": false}, {"id": "12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "name": "odcRoutingDetails", "versionId": "3793d607-c255-4971-80fe-9dd69b16f667", "hasDetails": false}]}, {"typeName": "Coach <PERSON>", "count": 40, "objects": [{"id": "64.041a4da1-f77a-4f68-8837-a712d1a38d59", "name": "ODC Collections CV", "versionId": "dc0256a7-8e64-4c99-9b51-54d7514a45ec", "hasDetails": true}, {"id": "64.04664cbf-b838-4a5b-bf47-c4160069d58d", "name": "ODC Parties CV 2", "versionId": "30bb0b7d-c031-4cfa-8bf1-cb139dccd90b", "hasDetails": true}, {"id": "64.0f40f56d-733f-4bd5-916c-92ae7dccbb10", "name": "Contract Creation CV", "versionId": "af64a198-833a-4315-bc6b-a2056724e2f5", "hasDetails": true}, {"id": "64.0ff96fd8-0740-4d17-887e-a56c8ef7921b", "name": "Importer Details CV", "versionId": "f2f1658d-ae7b-45ee-a60e-36b46b282024", "hasDetails": true}, {"id": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "name": "Contract Liquidation", "versionId": "64571bed-66b5-41fa-87ad-9a9c8819f9f3", "hasDetails": true}, {"id": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "name": "Charges And Commissions CV 2", "versionId": "a72c1fc9-ee22-4196-9904-eb4eb8bfe774", "hasDetails": true}, {"id": "64.1d99aba8-195f-4eee-ba8c-a47926bc21e2", "name": "Document Generation CV", "versionId": "1a1713a3-de37-49e2-bdcb-d84680269095", "hasDetails": true}, {"id": "64.1da5da8a-d92b-4788-add6-533189d60ad0", "name": "layout", "versionId": "8c515d6c-be9c-40f0-b3da-415911a71f4b", "hasDetails": false}, {"id": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "name": "test view 2", "versionId": "2991e37c-d215-49ba-a874-3dbff01d5c0a", "hasDetails": true}, {"id": "64.24e33a8b-e481-4548-81a5-6647b0c96320", "name": "Commissions And Charges", "versionId": "9bd3cc2d-089b-489c-9a22-9c78560bf5ce", "hasDetails": true}, {"id": "64.331e6374-b546-4447-9923-316c70693dd1", "name": "temp", "versionId": "0db3b06a-9f16-4991-8ced-d67558109b4c", "hasDetails": true}, {"id": "64.3361b968-662d-491d-8e02-666b6b3648ec", "name": "Attachment 2", "versionId": "5bc4218b-087f-424c-9ba1-0eb7aced65ca", "hasDetails": true}, {"id": "64.3d608948-c1fa-4ac3-8d4b-6a42f90a5132", "name": "ODC Parties CV", "versionId": "5d16a9a4-8300-453d-869f-94ee95145d51", "hasDetails": true}, {"id": "64.3e998f87-f87f-443f-b820-c2f7d465fa68", "name": "Layout 3P CV", "versionId": "7ea0a2ea-391b-4ffd-b4eb-f2fd890c4773", "hasDetails": false}, {"id": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "name": "DC Templete", "versionId": "a4863016-bca6-415f-9407-7cd45040cbde", "hasDetails": true}, {"id": "64.436a7747-a90a-4b7e-8d02-ff4803d46ce0", "name": "testTable", "versionId": "15d228f6-0bd1-4af6-88b3-1af012b927a5", "hasDetails": true}, {"id": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "name": "Financial Details Branch", "versionId": "51456dd2-66ca-4a40-8805-14a62712aefd", "hasDetails": true}, {"id": "64.5510de3d-75a5-4353-8d6d-82f540b6d556", "name": "Print Barcode", "versionId": "8e1130c2-4503-4eb0-a825-dd9c0bdb85c5", "hasDetails": true}, {"id": "64.5c068fcf-4364-4084-8825-6bb898b38609", "name": "Products and Shipment Details CV", "versionId": "7db23117-87d3-4297-98bc-73fd5c7cf4e9", "hasDetails": true}, {"id": "64.5df8245e-3f18-41b6-8394-548397e4652f", "name": "DC History", "versionId": "385262a6-4cc6-44c7-aa6d-6db9baf128aa", "hasDetails": true}, {"id": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "name": "DC start request template", "versionId": "3f5595f6-740a-432d-af7f-278d37e4845c", "hasDetails": true}, {"id": "64.63a48491-564d-4a28-972c-df08820c76ff", "name": "functionSearch", "versionId": "9038759a-d909-4706-88ae-ae7a4ed18868", "hasDetails": true}, {"id": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "name": "FC Collections CV", "versionId": "f28116da-b2e5-4a00-a7a7-1562a7ca4ae3", "hasDetails": true}, {"id": "64.701ac070-61ee-4489-9c65-6cc48db9510a", "name": "Layout 4P CV", "versionId": "9eeb1345-7563-4417-88a6-845d0a059643", "hasDetails": false}, {"id": "64.72428c7b-aa19-4400-bea7-59743c5442cc", "name": "Validation Helper", "versionId": "c79eb96d-5560-467c-84c7-7135acc836e4", "hasDetails": true}, {"id": "64.74c6fa59-aa38-49a4-89bc-f00692e89191", "name": "Charges And Commissions CV", "versionId": "83f0eba5-cd01-40ef-b913-38fd871d516c", "hasDetails": true}, {"id": "64.77f06102-fddf-42a1-9b6b-6580b729871c", "name": "DC Templete 2", "versionId": "2c6b6d88-a3e8-4b28-9aa5-e87623af52eb", "hasDetails": true}, {"id": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "name": "Attachment", "versionId": "2de00cf6-d27b-4cc6-81a2-d1b6bba2edef", "hasDetails": true}, {"id": "64.7c4993bc-f7d5-4689-96c6-89c5b908583f", "name": "Start New Request CV", "versionId": "3e728fac-930b-4161-9fb2-c7fcac0ddd33", "hasDetails": true}, {"id": "64.7efffff5-e4b6-4afc-a9a2-4e9b478d26a5", "name": "Multi Tenor Dates CV", "versionId": "e146c342-750c-4f66-b669-01213bf55fe1", "hasDetails": true}, {"id": "64.98459c6f-cb8f-462d-9fae-63d331db4606", "name": "test view", "versionId": "9088f2b0-71c5-4a5b-991b-658253716d2c", "hasDetails": true}, {"id": "64.9d678f8f-ae90-4e9d-bfb5-10fbbf775731", "name": "Parties CV", "versionId": "dbb77c8b-14e2-4925-aafe-7b469c794b54", "hasDetails": true}, {"id": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "name": "Customer Information cv", "versionId": "b2affbad-b447-40bf-9336-acb174aa96ad", "hasDetails": true}, {"id": "64.af339c2a-1b07-4416-a616-ec3121b73970", "name": "Financial Details Trade FO CV 2", "versionId": "ee583e61-8bd1-4df8-a72c-7697f5b2d063", "hasDetails": true}, {"id": "64.b4f7785b-f352-434f-9dc2-a68e51beac4e", "name": "testView", "versionId": "4c168860-9eae-4b1d-a4e0-c42078b4ed87", "hasDetails": true}, {"id": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "name": "IDC Party", "versionId": "50d524c6-1b7b-4b28-a77e-5a644b96a625", "hasDetails": true}, {"id": "64.e2910043-5f79-408a-81b4-3af68cb1deac", "name": "Search Templete", "versionId": "35e0a560-da5a-41c8-815e-07c4f6458377", "hasDetails": true}, {"id": "64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1", "name": "Financial Details Trade FO CV", "versionId": "3afd7001-91c6-496d-bb5a-cb784ede3ad6", "hasDetails": true}, {"id": "64.f0c268ac-0772-4735-af5b-5fc6caec30a1", "name": "Reversal Closure CV", "versionId": "20ae7397-1248-4d0c-853a-6467781ec4ed", "hasDetails": true}, {"id": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "name": "Basic Details CV", "versionId": "122c2152-ac38-426a-a5d0-7bd7f51c5c49", "hasDetails": true}]}, {"typeName": "Participant", "count": 32, "objects": [{"id": "24.13887169-b900-4899-a81f-01e15c00c91f", "name": "DC control Unit Supervisor", "versionId": "39b6c253-3b30-4161-89a1-f2a5c0518b15", "hasDetails": false}, {"id": "24.219ec5c0-0c40-4f2d-ab50-d2c3b503ff14", "name": "HUB Execution Makers", "versionId": "d6309310-aec6-4786-b1ba-2d18c888bf92", "hasDetails": false}, {"id": "24.33723a74-9421-41a6-9d94-5464d6689d7d", "name": "Trade Compliance Checker", "versionId": "36fffbb3-e86f-4cfc-ab00-2075df9ab424", "hasDetails": false}, {"id": "24.3bf11e60-c652-49be-a22b-e25b3ef79931", "name": "Branch compliance Representative Manager", "versionId": "44277c59-ff55-499a-87f6-b1e01b622097", "hasDetails": false}, {"id": "24.47d810bd-7938-44a3-9e7f-3bdddc0d4dff", "name": "Reversal Managers", "versionId": "d7821a39-b4ab-4795-9239-ff57f7d12c54", "hasDetails": false}, {"id": "24.4e372b2d-494e-42ad-a5af-cb2e77259a38", "name": "ODC Managers", "versionId": "71db332b-8285-4518-adb4-1262cdabf102", "hasDetails": false}, {"id": "24.51364718-ca09-4922-bed7-2eab4ed3c5a9", "name": "HUB Execution Manager", "versionId": "cb4e9364-0539-4cd1-b67c-cd29b1e9f073", "hasDetails": false}, {"id": "24.55c1fe56-ce36-4b4b-87bd-380022cde382", "name": "Trade compliance Maker", "versionId": "8b8cf17f-db58-4284-86c7-f2ad044d9901", "hasDetails": false}, {"id": "24.5a62da37-5a68-4735-b00d-12829a30ea97", "name": "HUB compliance Representative  Manager", "versionId": "049f8938-ee40-402d-b8aa-97ea9195d697", "hasDetails": false}, {"id": "24.5f9278b1-621c-429c-9e91-b2b6d2850a95", "name": "Closure Owners", "versionId": "2d67cf60-8ab0-4d15-9453-ab28e7269ea5", "hasDetails": false}, {"id": "24.69e88363-c139-41f2-9dbf-64274c11a0dc", "name": "Trade finance Control Unit", "versionId": "aa8d6d8c-46b0-4bab-92ba-5b71ff9093f4", "hasDetails": false}, {"id": "24.7858d46a-5678-49eb-bfb0-758ef8ec2464", "name": "Hub CU Checkers", "versionId": "b76dffa3-b85d-4b2b-852c-1384bef8b49a", "hasDetails": false}, {"id": "24.84c9f3c0-b39d-42f8-9437-500732ca636c", "name": "HUB Execution Checkers", "versionId": "f09ea9f3-ae16-4e92-84d9-ea35683ec9c6", "hasDetails": false}, {"id": "24.87350f86-1636-41a7-80ca-60c055fb71f2", "name": "Reversal Owners", "versionId": "5915172e-fe34-4eed-9307-1f0fdde38d45", "hasDetails": false}, {"id": "24.8ab3fca1-29f3-4ddd-892d-55f78a0f69bc", "name": "ODC Collection Owners", "versionId": "80363967-8022-49dd-a6e3-7b57464c83df", "hasDetails": false}, {"id": "24.940ca5e1-5f71-40c0-8748-89f4b53c2525", "name": "Closure Managers", "versionId": "8d91fc81-a61e-4f31-aeca-f2fd791bd1db", "hasDetails": false}, {"id": "24.97bc604d-23d4-4c32-82d9-7c314516642f", "name": "DC Central Unit Supervisor", "versionId": "6a689023-cea9-4d61-9d13-dbee72e450a7", "hasDetails": false}, {"id": "24.a93a495e-ebfc-42ac-87e6-951e0ec1a877", "name": "Branch Manager", "versionId": "b9faf003-3246-48ad-8bf7-3ccc7444d8b3", "hasDetails": false}, {"id": "24.ab30711e-0674-4175-bb8c-e87b2f03fa98", "name": "ODC Collection Managers", "versionId": "4ddad2c1-bdac-420c-92ac-358710b1be2b", "hasDetails": false}, {"id": "24.abae0138-0c20-4914-b2ec-523594f4a93d", "name": "Trade compliance Manager", "versionId": "352e390c-1ac6-426d-b0e2-6ddda4a012c0", "hasDetails": false}, {"id": "24.b469f9ac-8dba-4742-bc4d-38c0f4f28ce3", "name": "ODC Creation Owners", "versionId": "55b18b45-ec0f-4bb9-bfa5-bd6d9526e9a4", "hasDetails": false}, {"id": "24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89", "name": "Trade FO Makers", "versionId": "f8d25f3b-0b03-40df-beee-9a37fd159eaa", "hasDetails": false}, {"id": "24.c1950331-89e8-4fb3-825b-52bc99292612", "name": "ODC compliance Representative checkers", "versionId": "6733a439-ecaf-42c2-af7c-0927c7d22049", "hasDetails": false}, {"id": "24.c1a3c19e-010a-4618-9c2f-16ff28c0245b", "name": "HUB CU Manager", "versionId": "ba85583f-3103-4dd5-ad65-b85eb880fb93", "hasDetails": false}, {"id": "24.cda98858-50ea-46de-89cf-0293c9ae9a00", "name": "Hub CU Makers", "versionId": "999c0d4a-c7a3-44d9-a496-197f86ca5c13", "hasDetails": false}, {"id": "24.d01bf37a-ec31-4585-aa1e-627d4ea6d162", "name": "Branch Makers", "versionId": "c1d23dd7-28f1-44b1-81b1-1c4572f16307", "hasDetails": false}, {"id": "24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec", "name": "HUB compliance Representative Checker", "versionId": "0b943e44-4716-4f4d-8001-267362d691f6", "hasDetails": false}, {"id": "24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4", "name": "Branch compliance Representative Checkers", "versionId": "810f835e-ee93-4052-91c7-1db6459ebc54", "hasDetails": false}, {"id": "24.e7977935-dba2-437e-9671-4ec41d29e437", "name": "ODC initators", "versionId": "dfd1d9ac-aebb-430b-b7ec-0e4d1f390b1c", "hasDetails": false}, {"id": "24.ec65db21-fa8b-430c-9116-176de416229a", "name": "Trade FO Managers", "versionId": "af559561-b69f-477c-ab2b-4b4c3fde2004", "hasDetails": false}, {"id": "24.f542e8cc-d0f3-4179-8fd0-1db0628a8b36", "name": "ODC compliance Representative Managers", "versionId": "64c78fc6-021c-4feb-8d9d-b6262eb12285", "hasDetails": false}, {"id": "24.f66dc7c7-4ada-40ba-ad62-7deff8804a27", "name": "ODC Position Screen Initiators", "versionId": "fce69022-293b-488f-b27c-e533a5c1dae9", "hasDetails": false}]}, {"typeName": "Environment Property Variable", "count": 18, "objects": [{"id": "21.00a114f4-2b01-4c48-aad9-bd62580da24b", "name": "Col_SLA", "versionId": "0e192c1e-ff10-4383-9bee-a91299a88299", "hasDetails": false}, {"id": "21.062854b5-6513-4da8-84ab-0126f90e550d", "name": "RequestState", "versionId": "521b2922-4fa2-4948-81ee-3ba2f3a22265", "hasDetails": false}, {"id": "21.0ac53667-a1c4-4086-b17a-f78b72a6299a", "name": "searchCriteria", "versionId": "2d2eb361-0624-4a07-917c-4c64a464ec28", "hasDetails": false}, {"id": "21.340b122c-2fdf-400c-822c-b0c52fb7b022", "name": "Status", "versionId": "8cb54f7c-a2db-4b51-ac76-1ecf1f515376", "hasDetails": false}, {"id": "21.51a88928-60d9-48d1-b133-fa98f61b49a6", "name": "ECMProperties", "versionId": "8c547f72-0dc0-4296-affb-4a2160dcb625", "hasDetails": false}, {"id": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "name": "RequestNature", "versionId": "97ba8feb-0017-4384-9f96-8ff8181f41a1", "hasDetails": false}, {"id": "21.679fe48b-845c-41d8-b41a-884a27c2acf3", "name": "ODCProcessName", "versionId": "26c951c2-4455-49dc-bf21-96405071c50c", "hasDetails": false}, {"id": "21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c", "name": "Mails", "versionId": "63932d06-9062-443e-a513-8892148fffdc", "hasDetails": false}, {"id": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "name": "TermsAndConditions", "versionId": "79c6e100-e701-463e-892d-2c931d6ffaa9", "hasDetails": false}, {"id": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "name": "userRole", "versionId": "128acb22-0680-4190-9f73-22129718aeea", "hasDetails": false}, {"id": "21.93c5c002-7ac4-4283-83ee-63b8662f9223", "name": "ProcessDetails", "versionId": "787291f6-5ebc-4162-813c-706d5f3f669b", "hasDetails": false}, {"id": "21.96f93187-360b-430a-9042-84a97749fff7", "name": "<PERSON><PERSON><PERSON>", "versionId": "59ff4d9e-9a2e-4f01-b647-e22f399c32ce", "hasDetails": false}, {"id": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "name": "Col_ScreenNames", "versionId": "4fb40a86-6ebe-4a8f-a67f-97e8a1ad9f79", "hasDetails": false}, {"id": "21.bed40437-f5de-4b1c-a063-7040de4075df", "name": "ScreenNames", "versionId": "006e834d-7c18-4dd6-853e-b16ae35ab834", "hasDetails": false}, {"id": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "name": "CreationActions", "versionId": "a2e0a4f1-9a12-4fe6-a90e-8f94ea5c21d9", "hasDetails": false}, {"id": "21.dbbaa047-8f02-4397-b1b5-41f11b0256b3", "name": "ODCCreationSLA", "versionId": "5f8db4c6-2265-4cad-aa68-531075abe6b6", "hasDetails": false}, {"id": "21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "name": "Col_Actions", "versionId": "f0c29a38-66c0-4f01-99e3-95891f4aa4cd", "hasDetails": false}, {"id": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "name": "RequestType", "versionId": "fd3df966-ad36-481d-b66d-1a0b9f1f32fb", "hasDetails": false}]}, {"typeName": "Managed Asset", "count": 8, "objects": [{"id": "61.03c776ac-d2b4-4bbe-b4df-c302f6d1d325", "name": "itextpdf-5.1.0.jar", "versionId": "bc487627-2396-420f-9377-503efe4e787a", "hasDetails": false}, {"id": "61.0ad6c2e7-6560-4811-9852-ad20a5b14c90", "name": "avalon-framework-4.1.4.jar", "versionId": "13e37789-efcf-4540-b4cf-495ebc139a05", "hasDetails": false}, {"id": "61.34ee4297-a5d2-4df0-882b-3f89c31112df", "name": "common.js", "versionId": "9264cef6-707a-4ace-925e-8eed6b8f8eef", "hasDetails": false}, {"id": "61.44c2943b-f742-4b94-aced-a678f2523ee7", "name": "barcode.jar", "versionId": "e0810ca2-f978-42ad-bd21-deaa1157966e", "hasDetails": false}, {"id": "61.68f611c2-cc89-4e7e-ba1f-44a3b929e0c9", "name": "0aacb363-83b1-4d0a-959d-7ab705b08e5c.zip", "versionId": "f2e117a2-64f3-4029-9e4f-9e50dfb1eef5", "hasDetails": false}, {"id": "61.6c107f22-ba5c-47d3-aacc-e3561380a3a2", "name": "header1.png", "versionId": "12cb2496-e762-4144-879f-48ceeb827cd0", "hasDetails": false}, {"id": "61.6c9a4cc1-6c0c-4b3f-a0dd-1e296f3e3f0c", "name": "print.min.js", "versionId": "c65271fd-d543-4b1f-b436-81f1cd859e31", "hasDetails": false}, {"id": "61.6f022f05-805c-446f-a5a8-152a05e9016a", "name": "barcode4j-2.0.jar", "versionId": "02b62376-9121-4348-9951-ebb6c6ca760d", "hasDetails": false}]}, {"typeName": "Business Process Definition", "count": 5, "objects": [{"id": "25.0b754ec1-9413-443e-9edc-f7cc6429b0d6", "name": "ODC Collection تسجيل حصائل على مستند تحصيل تصدير", "versionId": "7af263f8-ec28-4d86-9715-bda0c6db59f3", "hasDetails": false}, {"id": "25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9", "name": "ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير", "versionId": "8af1b4b7-9041-46d7-a78e-959b105f2457", "hasDetails": false}, {"id": "25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964", "name": "ODC Closure اقفال تحصيل مستندى تصدير", "versionId": "8085dbb1-53f3-4f16-b3ba-7ce403f4f24e", "hasDetails": false}, {"id": "25.933ae173-470f-4c1f-8a2b-6521da495a38", "name": "ODC Reversal اعادة قيد تحصيل مستندى تصدير", "versionId": "06482096-b951-4eed-b5a7-b9c8ff257a6c", "hasDetails": false}, {"id": "25.96c4a0c7-8055-4af4-8adb-e0ff90566b97", "name": "", "versionId": "3252646c-337c-4ca2-a04a-6f0942cdc552", "hasDetails": false}]}, {"typeName": "Resource Bundle", "count": 3, "objects": [{"id": "50.41101508-d2e4-4682-b3ef-b9b22266bb5a", "name": "odcLookupsTable", "versionId": "d90377d9-6e71-42cf-8f33-976bbcbd5ad6", "hasDetails": false}, {"id": "50.411567ee-9528-4af5-acce-fa1c1b418a1e", "name": "Lookups", "versionId": "f4111048-9307-42de-b236-301ab1956b77", "hasDetails": false}, {"id": "50.72059ba3-20f9-4926-b151-02b418301dd4", "name": "ValidationMessages", "versionId": "0105edf4-66a1-4613-b304-d4cc62c2595e", "hasDetails": false}]}, {"typeName": "Environment Variables", "count": 2, "objects": [{"id": "62.baa265b6-9a89-4025-a3ea-79bc6384c47d", "name": "$$REST$BINDING$1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "versionId": "79e9ee66-4b14-4f6f-bd2a-36577d5a88b3", "hasDetails": false}, {"id": "62.e6994ce9-1cd9-4d76-8fe5-0c7bd4f3ca1f", "name": "Environment Variables", "versionId": "efc2b9cd-22ab-4ea0-b881-464eea5824a6", "hasDetails": false}]}, {"typeName": "Esartifact", "count": 2, "objects": [{"id": "6023.3c0ee9b7-a4a7-4812-9e58-f2cc9dd2fd92", "name": "http://localhost:8080/v2/api-docs", "versionId": "a654415a-11c2-4992-b1c6-cccd57da22ed", "hasDetails": false}, {"id": "6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4", "name": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice.xsd", "versionId": "9f3f1b2e-9f4c-48a6-89e7-6c790cee5c1e", "hasDetails": false}]}, {"typeName": "Project Settings", "count": 1, "objects": [{"id": "63.96e0a0d5-7f81-467c-a91a-62a59ec268de", "name": "Process App Settings", "versionId": "f4faf856-5956-404b-8385-98726f806be9", "hasDetails": false}]}], "toolkits": []}