<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.63a48491-564d-4a28-972c-df08820c76ff" name="functionSearch">
        <lastModified>1748106880183</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.63a48491-564d-4a28-972c-df08820c76ff</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;e353ad9e-91fa-4c7f-88c6-b3d4ba816967&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2fc2b01b-5e65-4996-8179-0bc15609ab32&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;364ba5e2-e3cf-4d07-8f26-e315ed5632a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8bc5c073-d698-4d60-84b9-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;c72fc52b-8733-408a-8672-a1ac2c9e367b&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;84802a72-ea5e-44b2-806a-2cdd843954dc&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;searchFor&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81bd2e0b-98df-4d3a-8c3c-928ac394f20e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Search For&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;615a5bfd-7dac-4bbe-83dd-493ea74856b2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4fd440d6-d65b-427d-8bc9-5d2403248dbd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;91b975e8-092a-42cb-8930-38c3bba61697&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"33%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56ef8ccb-da1d-4aca-84c9-57b0c1081c9f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;L&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;19636b43-b21e-41a9-8969-1a64a2d97113&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"all","value":"All Functions"},{"name":"notUsed","value":"Not Used Functions"},{"name":"funName","value":"Function By Name"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;558850f5-171b-429a-8afe-b4700e2d04c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6918e865-3573-4e1e-87a9-cd17a524008f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;viewName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7de1eca6-0c8e-4af3-8084-652ab0b0c924&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3d96fe9e-f782-44de-8136-eaf60110648f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;View Name&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1ec9c23-8d97-465a-8eee-09a5f1af4305&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75b65af5-b3b8-4a6f-8c89-00f542ddc49b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c3325ce8-**************-63be05b15833&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"33%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4c36a3c1-070b-4447-8fef-b7383604c59d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;functionName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d2e987b2-4b48-481a-8b88-a5d78787e3fa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1dc35ba8-f00e-43b7-8f2a-0a652abfae0b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Function Name&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fb53141d-5811-4d13-8071-8426a345a496&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;167ec80a-f763-4684-8582-c429f9b449f6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;e9a06057-cba8-4ebc-84b6-a20dcb514e48&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;searchBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;01c45bdc-49fe-4906-8037-6b10152b1f39&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Search&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b9a31dd-fe77-45a1-85ad-08007b964c80&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fde0f087-961b-4620-8984-b444735bbb59&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4edd7221-1a10-40db-8387-6cae83c48134&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.executeSearch();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;ad0d88e8-d952-46ae-89ae-76269f15487f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;result&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;900fd2ed-6e41-4ec2-87be-42333e2d7f6f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Result&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92be0e96-3d86-4b22-82e1-253b8a329f0a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e1c3eab2-c5a4-495c-8597-00234783436f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;be88fcb8-045e-4b99-8858-0825ca103ecc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowHTML&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>e69bf87c-ec2e-4527-b98e-465bdda7f625</guid>
        <versionId>9038759a-d909-4706-88ae-ae7a4ed18868</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.7758c51c-f0fc-438a-ab32-360a0d72d706</coachViewInlineScriptId>
            <coachViewId>64.63a48491-564d-4a28-972c-df08820c76ff</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>// Constants&#xD;
&#xD;
const EVENT_TYPES = [&#xD;
	"eventON_LOAD",&#xD;
	"eventON_CHANGE",&#xD;
	"eventON_INPUT",&#xD;
	"eventON_BLUR",&#xD;
	"eventON_FOCUS",&#xD;
	"eventON_SVCERROR",&#xD;
	"eventON_SVCITEMS",&#xD;
	"eventON_BEFORE_SHOW_DAY"&#xD;
];&#xD;
const SEARCH_OPTIONS = {&#xD;
	ALL: "all",&#xD;
	FUNCTION_NAME: "funName",&#xD;
	NOT_USED: "notUsed"&#xD;
};&#xD;
const SYSTEM_PROPERTIES = ['constructor', 'prototype', '__proto__'];&#xD;
&#xD;
/**&#xD;
 * Builds a map of control labels to their bound object values for specified events&#xD;
 * @param {string} viewName - The name of the view to search&#xD;
 * @param {Array&lt;string&gt;} eventTypes - Array of event types to check for&#xD;
 * @returns {Map&lt;string, Array&lt;{eventType: string, value: string}&gt;} Map of control labels to arrays of event type and bound object value pairs&#xD;
 */&#xD;
function buildBoundObjectMap(viewName, eventTypes) {&#xD;
	const view = bpmext.ui.getView(viewName);&#xD;
	const boundObjectMap = new Map();&#xD;
	console.dir(view);&#xD;
	if (!view || !view._bpmextViewNode || !view._bpmextViewNode._children) {&#xD;
		console.error("View or view children not found");&#xD;
		return boundObjectMap;&#xD;
	}&#xD;
&#xD;
	const children = view._bpmextViewNode._children;&#xD;
	if (!children || Object.keys(children).length === 0) {&#xD;
		console.error("No children found in the view");&#xD;
		return boundObjectMap;&#xD;
	}&#xD;
	// Iterate through all child controls&#xD;
	for (const controlType in children) {&#xD;
		if (Object.prototype.hasOwnProperty.call(children, controlType)) {&#xD;
			const controls = children[controlType];&#xD;
&#xD;
			// Process each control of this type&#xD;
			for (const control of controls) {&#xD;
				if (!control._data) continue;&#xD;
&#xD;
				const label = control._data.getLabel ? control._data.getLabel() : controlType;&#xD;
&#xD;
				// Check for each event type&#xD;
				for (const eventType of eventTypes) {&#xD;
					if (control._data.context &amp;&amp; control._data.context.options &amp;&amp; control._data.context.options[eventType] &amp;&amp; control._data.context.options[eventType].boundObject &amp;&amp; control._data.context.options[eventType].boundObject.value) {&#xD;
						// Get the bound object value&#xD;
						const value = control._data.context.options[eventType].boundObject.value;&#xD;
&#xD;
						// Initialize array for this label if it doesn't exist&#xD;
						if (!boundObjectMap.has(label)) {&#xD;
							boundObjectMap.set(label, []);&#xD;
						}&#xD;
&#xD;
						// Add event type and value to the array for this label&#xD;
						boundObjectMap.get(label).push({&#xD;
							eventType: eventType,&#xD;
							value: value,&#xD;
						});&#xD;
					}&#xD;
				}&#xD;
			}&#xD;
		}&#xD;
	}&#xD;
	return boundObjectMap;&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Searches for a string in the bound object values and returns matching control labels&#xD;
 * @param {Map&lt;string, Array&lt;{eventType: string, value: string}&gt;} boundObjectMap - Map of control labels to arrays of event type and bound object value pairs&#xD;
 * @param {string} searchString - String to search for in bound object values&#xD;
 * @returns {Array&lt;{label: string, events: Array&lt;{eventType: string, value: string}&gt;}&gt;} Array of objects containing matching control labels and their events&#xD;
 */&#xD;
function searchBoundObjects(boundObjectMap, searchString) {&#xD;
	if (!searchString || typeof searchString !== "string") {&#xD;
		return [];&#xD;
	}&#xD;
&#xD;
	const matchingResults = [];&#xD;
&#xD;
	// Convert search string to lowercase for case-insensitive search&#xD;
	const searchLower = searchString.toLowerCase();&#xD;
&#xD;
	// Search through all values in the map&#xD;
	boundObjectMap.forEach((eventArray, label) =&gt; {&#xD;
		// Find matching events for this control&#xD;
		const matchingEvents = eventArray.filter((event) =&gt; typeof event.value === "string" &amp;&amp; event.value.toLowerCase().includes(searchLower));&#xD;
&#xD;
		// If we found any matches, add this control to the results&#xD;
		if (matchingEvents.length &gt; 0) {&#xD;
			matchingResults.push({&#xD;
				label: label,&#xD;
				events: matchingEvents,&#xD;
			});&#xD;
		}&#xD;
	});&#xD;
&#xD;
	return matchingResults;&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Main function to search for bound objects in a view&#xD;
 * @param {string} viewName - The name of the view to search&#xD;
 * @param {string} searchString - String to search for in bound object values&#xD;
 * @returns {Array&lt;{label: string, events: Array&lt;{eventType: string, value: string}&gt;}&gt;} Array of objects containing matching control labels and their events&#xD;
 */&#xD;
function searchFunctionsInView(viewName, searchString) {&#xD;
	const boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);&#xD;
	return searchBoundObjects(boundObjectMap, searchString);&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Gets just the labels of controls that match the search string&#xD;
 * @param {string} viewName - The name of the view to search&#xD;
 * @param {string} searchString - String to search for in bound object values&#xD;
 * @returns {Array&lt;string&gt;} Array of distinct control labels that match the search&#xD;
 */&#xD;
function getMatchingControlLabels(viewName, searchString) {&#xD;
	const results = searchFunctionsInView(viewName, searchString);&#xD;
	return results.map((result) =&gt; result.label);&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Formats search results as an HTML table&#xD;
 * @param {Array&lt;{label: string, events: Array&lt;{eventType: string, value: string}&gt;}&gt;} results - Search results from searchFunctionsInView&#xD;
 * @param {string} searchString - The search string used (for highlighting)&#xD;
 * @returns {string} HTML table representation of the search results&#xD;
 */&#xD;
function formatResultsAsHtml(results, searchString) {&#xD;
	if (!results || results.length === 0) {&#xD;
		return "&lt;div class='no-results'&gt;No matching controls found&lt;/div&gt;";&#xD;
	}&#xD;
&#xD;
	// Escape HTML special characters to prevent XSS&#xD;
	function escapeHtml(text) {&#xD;
		return text.replace(/&amp;/g, "&amp;amp;").replace(/&lt;/g, "&amp;lt;").replace(/&gt;/g, "&amp;gt;").replace(/"/g, "&amp;quot;").replace(/'/g, "&amp;#039;");&#xD;
	}&#xD;
&#xD;
	// Highlight the search string in the value&#xD;
	function highlightSearchString(text, search) {&#xD;
		if (!search || !text.includes(search)) {&#xD;
			return escapeHtml(text);&#xD;
		}&#xD;
&#xD;
		const escapedText = escapeHtml(text);&#xD;
		const regex = new RegExp(escapeHtml(search), "gi");&#xD;
		return escapedText.replace(regex, (match) =&gt; `&lt;span class="highlight"&gt;${match}&lt;/span&gt;`);&#xD;
	}&#xD;
&#xD;
	// Build the HTML table with unified design&#xD;
	let html = `&#xD;
    &lt;style&gt;&#xD;
        .search-results-table {&#xD;
            border-collapse: collapse;&#xD;
            width: 100%;&#xD;
            font-family: Arial, sans-serif;&#xD;
            margin-bottom: 20px;&#xD;
            table-layout: fixed;&#xD;
        }&#xD;
        .search-results-table th, .search-results-table td {&#xD;
            border: 1px solid #ddd;&#xD;
            padding: 8px;&#xD;
            text-align: left;&#xD;
        }&#xD;
        .search-results-table th {&#xD;
            background-color: #f2f2f2;&#xD;
            font-weight: bold;&#xD;
        }&#xD;
        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {&#xD;
            width: 20%;&#xD;
            white-space: nowrap;&#xD;
            overflow: hidden;&#xD;
            text-overflow: ellipsis;&#xD;
        }&#xD;
        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {&#xD;
            width: 20%;&#xD;
            white-space: nowrap;&#xD;
            overflow: hidden;&#xD;
            text-overflow: ellipsis;&#xD;
        }&#xD;
        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {&#xD;
            width: 15%;&#xD;
            white-space: nowrap;&#xD;
            overflow: hidden;&#xD;
            text-overflow: ellipsis;&#xD;
        }&#xD;
        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {&#xD;
            width: 45%;&#xD;
            word-wrap: break-word;&#xD;
            white-space: normal;&#xD;
        }&#xD;
        .search-results-table tr:nth-child(even) {&#xD;
            background-color: #f9f9f9;&#xD;
        }&#xD;
        .search-results-table tr:hover {&#xD;
            background-color: #f5f5f5;&#xD;
        }&#xD;
        .highlight {&#xD;
            background-color: yellow;&#xD;
            font-weight: bold;&#xD;
        }&#xD;
        .event-type {&#xD;
            font-style: italic;&#xD;
            color: #666;&#xD;
        }&#xD;
        .no-results {&#xD;
            padding: 20px;&#xD;
            text-align: center;&#xD;
            font-style: italic;&#xD;
            color: #666;&#xD;
        }&#xD;
        .function-group {&#xD;
            border-top: 2px solid #0066cc;&#xD;
        }&#xD;
        .controller-group {&#xD;
            border-left: 3px solid #f0f0f0;&#xD;
        }&#xD;
    &lt;/style&gt;&#xD;
    &lt;table class="search-results-table"&gt;&#xD;
        &lt;thead&gt;&#xD;
            &lt;tr&gt;&#xD;
                &lt;th&gt;Function Name&lt;/th&gt;&#xD;
                &lt;th&gt;Used by Control&lt;/th&gt;&#xD;
                &lt;th&gt;Event Type&lt;/th&gt;&#xD;
                &lt;th&gt;Context&lt;/th&gt;&#xD;
            &lt;/tr&gt;&#xD;
        &lt;/thead&gt;&#xD;
        &lt;tbody&gt;&#xD;
    `;&#xD;
&#xD;
	// Add rows for each result&#xD;
	results.forEach((result) =&gt; {&#xD;
		result.events.forEach((event, index) =&gt; {&#xD;
			html += `&#xD;
            &lt;tr&gt;&#xD;
                &lt;td&gt;${escapeHtml(searchString)}&lt;/td&gt;&#xD;
                &lt;td&gt;${index === 0 ? escapeHtml(result.label) : ""}&lt;/td&gt;&#xD;
                &lt;td class="event-type"&gt;${escapeHtml(event.eventType.replace("event", ""))}&lt;/td&gt;&#xD;
                &lt;td&gt;${highlightSearchString(event.value, searchString)}&lt;/td&gt;&#xD;
            &lt;/tr&gt;&#xD;
            `;&#xD;
		});&#xD;
	});&#xD;
&#xD;
	html += `&#xD;
        &lt;/tbody&gt;&#xD;
    &lt;/table&gt;&#xD;
    `;&#xD;
&#xD;
	return html;&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Checks if a function name is a system function&#xD;
 * @param {string} functionName - The function name to check&#xD;
 * @returns {boolean} True if it's a system function&#xD;
 */&#xD;
function isSystemFunction(functionName) {&#xD;
	return functionName.startsWith('event') ||&#xD;
		   functionName.includes('ON_') ||&#xD;
		   functionName.startsWith('get') ||&#xD;
		   functionName.startsWith('set') ||&#xD;
		   functionName.startsWith('_');&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Gets all user-defined functions from a view&#xD;
 * @param {Object} view - The view object&#xD;
 * @returns {Array&lt;string&gt;} Array of user function names&#xD;
 */&#xD;
function getUserFunctions(view) {&#xD;
	const userFunctions = [];&#xD;
	const ownPropertyNames = Object.getOwnPropertyNames(view);&#xD;
&#xD;
	for (const key of ownPropertyNames) {&#xD;
		try {&#xD;
			// Skip system properties&#xD;
			if (SYSTEM_PROPERTIES.includes(key)) {&#xD;
				continue;&#xD;
			}&#xD;
&#xD;
			const value = view[key];&#xD;
&#xD;
			// Check if it's a user-defined function&#xD;
			if (typeof value === 'function' &amp;&amp; !isSystemFunction(key)) {&#xD;
				userFunctions.push(key);&#xD;
			}&#xD;
		} catch (e) {&#xD;
			console.log(`Error accessing property ${key}: ${e.message}`);&#xD;
		}&#xD;
	}&#xD;
&#xD;
	return userFunctions;&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Extracts all user-defined function names from a view and finds their usage in controls&#xD;
 * @param {string} viewName - The name of the view to extract functions from&#xD;
 * @returns {Array&lt;{name: string, controller: string, eventType: string, context: string}&gt;} Array of function details found in the view&#xD;
 */&#xD;
function extractFunctionNamesFromView(viewName) {&#xD;
	const view = bpmext.ui.getView(viewName);&#xD;
	const functionDetails = [];&#xD;
&#xD;
	if (!view) {&#xD;
		console.error("View not found");&#xD;
		return functionDetails;&#xD;
	}&#xD;
&#xD;
	console.dir(view); // Debug logging&#xD;
&#xD;
	// Get all user-defined functions&#xD;
	const userFunctions = getUserFunctions(view);&#xD;
	const boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);&#xD;
&#xD;
	// Find usage for each function&#xD;
	userFunctions.forEach(functionName =&gt; {&#xD;
		const usageResults = searchBoundObjects(boundObjectMap, functionName);&#xD;
&#xD;
		if (usageResults.length &gt; 0) {&#xD;
			// Function is used in controls&#xD;
			usageResults.forEach(result =&gt; {&#xD;
				result.events.forEach(event =&gt; {&#xD;
					functionDetails.push({&#xD;
						name: functionName,&#xD;
						controller: result.label,&#xD;
						eventType: event.eventType.replace("event", ""),&#xD;
						context: event.value || functionName&#xD;
					});&#xD;
				});&#xD;
			});&#xD;
		} else {&#xD;
			// Function exists but not used&#xD;
			functionDetails.push({&#xD;
				name: functionName,&#xD;
				controller: 'Not Used',&#xD;
				eventType: 'Available',&#xD;
				context: 'Function not bound to any control'&#xD;
			});&#xD;
		}&#xD;
	});&#xD;
&#xD;
	return functionDetails;&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Filters function details to show only unused functions&#xD;
 * @param {Array&lt;{name: string, controller: string, eventType: string}&gt;} functionDetails - All function details&#xD;
 * @returns {Array&lt;{name: string, controller: string, eventType: string}&gt;} Filtered function details&#xD;
 */&#xD;
function getNotUsedFunctions(functionDetails) {&#xD;
	return functionDetails.filter(detail =&gt; detail.controller === 'Not Used');&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Formats function details as an HTML table with grouping&#xD;
 * @param {Array&lt;{name: string, controller: string, eventType: string, context: string}&gt;} functionDetails - Array of function details&#xD;
 * @returns {string} HTML table representation of the function details&#xD;
 */&#xD;
function formatFunctionNamesAsHtml(functionDetails) {&#xD;
	if (!functionDetails || functionDetails.length === 0) {&#xD;
		return "&lt;div class='no-results'&gt;No functions found in the view&lt;/div&gt;";&#xD;
	}&#xD;
&#xD;
	// Escape HTML special characters to prevent XSS&#xD;
	function escapeHtml(text) {&#xD;
		return String(text).replace(/&amp;/g, "&amp;amp;").replace(/&lt;/g, "&amp;lt;").replace(/&gt;/g, "&amp;gt;").replace(/"/g, "&amp;quot;").replace(/'/g, "&amp;#039;");&#xD;
	}&#xD;
&#xD;
	// Group by function name first, then by controller&#xD;
	const groupedByFunction = {};&#xD;
	functionDetails.forEach(detail =&gt; {&#xD;
		if (!groupedByFunction[detail.name]) {&#xD;
			groupedByFunction[detail.name] = {};&#xD;
		}&#xD;
		if (!groupedByFunction[detail.name][detail.controller]) {&#xD;
			groupedByFunction[detail.name][detail.controller] = [];&#xD;
		}&#xD;
		groupedByFunction[detail.name][detail.controller].push({&#xD;
			eventType: detail.eventType,&#xD;
			context: detail.context&#xD;
		});&#xD;
	});&#xD;
&#xD;
	// Sort function names alphabetically&#xD;
	const sortedFunctionNames = Object.keys(groupedByFunction).sort((a, b) =&gt;&#xD;
		a.toLowerCase().localeCompare(b.toLowerCase())&#xD;
	);&#xD;
&#xD;
	// Highlight function name in context&#xD;
	function highlightFunctionInContext(text, functionName) {&#xD;
		if (!functionName || !text.includes(functionName)) {&#xD;
			return escapeHtml(text);&#xD;
		}&#xD;
&#xD;
		const escapedText = escapeHtml(text);&#xD;
		const regex = new RegExp(escapeHtml(functionName), "gi");&#xD;
		return escapedText.replace(regex, (match) =&gt; `&lt;span class="highlight"&gt;${match}&lt;/span&gt;`);&#xD;
	}&#xD;
&#xD;
	// Build the HTML table with simple, clean design&#xD;
	let html = `&#xD;
    &lt;style&gt;&#xD;
        .search-results-table {&#xD;
            border-collapse: collapse;&#xD;
            width: 100%;&#xD;
            font-family: Arial, sans-serif;&#xD;
            margin-bottom: 20px;&#xD;
            table-layout: fixed;&#xD;
        }&#xD;
        .search-results-table th, .search-results-table td {&#xD;
            border: 1px solid #ddd;&#xD;
            padding: 8px;&#xD;
            text-align: left;&#xD;
        }&#xD;
        .search-results-table th {&#xD;
            background-color: #f2f2f2;&#xD;
            font-weight: bold;&#xD;
        }&#xD;
        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {&#xD;
            width: 20%;&#xD;
            white-space: nowrap;&#xD;
            overflow: hidden;&#xD;
            text-overflow: ellipsis;&#xD;
        }&#xD;
        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {&#xD;
            width: 20%;&#xD;
            white-space: nowrap;&#xD;
            overflow: hidden;&#xD;
            text-overflow: ellipsis;&#xD;
        }&#xD;
        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {&#xD;
            width: 15%;&#xD;
            white-space: nowrap;&#xD;
            overflow: hidden;&#xD;
            text-overflow: ellipsis;&#xD;
        }&#xD;
        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {&#xD;
            width: 45%;&#xD;
            word-wrap: break-word;&#xD;
            white-space: normal;&#xD;
        }&#xD;
        .search-results-table tr:nth-child(even) {&#xD;
            background-color: #f9f9f9;&#xD;
        }&#xD;
        .search-results-table tr:hover {&#xD;
            background-color: #f5f5f5;&#xD;
        }&#xD;
        .highlight {&#xD;
            background-color: yellow;&#xD;
            font-weight: bold;&#xD;
        }&#xD;
        .event-type {&#xD;
            font-style: italic;&#xD;
            color: #666;&#xD;
        }&#xD;
        .no-results {&#xD;
            padding: 20px;&#xD;
            text-align: center;&#xD;
            font-style: italic;&#xD;
            color: #666;&#xD;
        }&#xD;
        .function-group {&#xD;
            border-top: 2px solid #0066cc;&#xD;
        }&#xD;
        .controller-group {&#xD;
            border-left: 3px solid #f0f0f0;&#xD;
        }&#xD;
    &lt;/style&gt;&#xD;
    &lt;table class="search-results-table"&gt;&#xD;
        &lt;thead&gt;&#xD;
            &lt;tr&gt;&#xD;
                &lt;th&gt;Function Name&lt;/th&gt;&#xD;
                &lt;th&gt;Used by Control&lt;/th&gt;&#xD;
                &lt;th&gt;Event Type&lt;/th&gt;&#xD;
                &lt;th&gt;Context&lt;/th&gt;&#xD;
            &lt;/tr&gt;&#xD;
        &lt;/thead&gt;&#xD;
        &lt;tbody&gt;&#xD;
    `;&#xD;
&#xD;
	// Add rows for each function group&#xD;
	sortedFunctionNames.forEach((functionName) =&gt; {&#xD;
		const controllers = groupedByFunction[functionName];&#xD;
		const sortedControllers = Object.keys(controllers).sort((a, b) =&gt;&#xD;
			a.toLowerCase().localeCompare(b.toLowerCase())&#xD;
		);&#xD;
&#xD;
		let isFirstRowForFunction = true;&#xD;
&#xD;
		sortedControllers.forEach((controller) =&gt; {&#xD;
			const eventDetails = controllers[controller];&#xD;
			let isFirstRowForController = true;&#xD;
&#xD;
			eventDetails.forEach((detail) =&gt; {&#xD;
				const functionGroupClass = isFirstRowForFunction ? 'function-group' : '';&#xD;
				const controllerGroupClass = isFirstRowForController ? 'controller-group' : '';&#xD;
&#xD;
				html += `&#xD;
                &lt;tr class="${functionGroupClass}"&gt;&#xD;
                    &lt;td class="${controllerGroupClass}"&gt;&#xD;
                        ${isFirstRowForFunction ? escapeHtml(functionName) : ''}&#xD;
                    &lt;/td&gt;&#xD;
                    &lt;td class="${controllerGroupClass}"&gt;&#xD;
                        ${isFirstRowForController ? escapeHtml(controller) : ''}&#xD;
                    &lt;/td&gt;&#xD;
                    &lt;td class="event-type"&gt;${escapeHtml(detail.eventType)}&lt;/td&gt;&#xD;
                    &lt;td&gt;${highlightFunctionInContext(detail.context, functionName)}&lt;/td&gt;&#xD;
                &lt;/tr&gt;&#xD;
                `;&#xD;
&#xD;
				isFirstRowForFunction = false;&#xD;
				isFirstRowForController = false;&#xD;
			});&#xD;
		});&#xD;
	});&#xD;
&#xD;
	html += `&#xD;
        &lt;/tbody&gt;&#xD;
    &lt;/table&gt;&#xD;
    `;&#xD;
&#xD;
	return html;&#xD;
}&#xD;
&#xD;
/**&#xD;
 * Searches for functions in a view and returns the results as HTML&#xD;
 * @param {string} viewName - The name of the view to search&#xD;
 * @param {string} searchFor - Search option (all, funName, notUsed)&#xD;
 * @param {string} searchString - String to search for in bound object values (used when searchFor is funName)&#xD;
 * @returns {string} HTML representation of the search results&#xD;
 */&#xD;
function searchFunctionsAndFormatAsHtml(viewName, searchFor, searchString) {&#xD;
	switch (searchFor) {&#xD;
		case SEARCH_OPTIONS.ALL:&#xD;
			// Show all user-defined functions&#xD;
			const allFunctionDetails = extractFunctionNamesFromView(viewName);&#xD;
			return formatFunctionNamesAsHtml(allFunctionDetails);&#xD;
&#xD;
		case SEARCH_OPTIONS.FUNCTION_NAME:&#xD;
			// Search for specific function name&#xD;
			if (!searchString) {&#xD;
				return "&lt;div class='no-results'&gt;Please enter a function name to search for&lt;/div&gt;";&#xD;
			}&#xD;
			const results = searchFunctionsInView(viewName, searchString);&#xD;
			return formatResultsAsHtml(results, searchString);&#xD;
&#xD;
		case SEARCH_OPTIONS.NOT_USED:&#xD;
			// Show only unused functions&#xD;
			const allFunctions = extractFunctionNamesFromView(viewName);&#xD;
			const notUsedFunctions = getNotUsedFunctions(allFunctions);&#xD;
			return formatFunctionNamesAsHtml(notUsedFunctions);&#xD;
&#xD;
		default:&#xD;
			// Default to showing all functions&#xD;
			const defaultFunctionDetails = extractFunctionNamesFromView(viewName);&#xD;
			return formatFunctionNamesAsHtml(defaultFunctionDetails);&#xD;
	}&#xD;
}&#xD;
&#xD;
this.executeSearch = function () {&#xD;
	var viewName = this.ui.get("viewName").getData();&#xD;
	if (!viewName) {&#xD;
		console.error("View name is empty");&#xD;
		return;&#xD;
	}&#xD;
&#xD;
	var searchForVal = this.ui.get("searchFor").getData();&#xD;
	var funName = this.ui.get("functionName").getData();&#xD;
&#xD;
	console.log(`Search type: ${searchForVal}, Function name: ${funName || 'N/A'}, View: ${viewName}`);&#xD;
&#xD;
	var htmlResults = searchFunctionsAndFormatAsHtml(viewName, searchForVal, funName);&#xD;
	if (!!htmlResults) {&#xD;
		this.ui.get("result").setData(htmlResults);&#xD;
	}&#xD;
};&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>04a536d7-3d61-48b4-b446-9cf3cac51632</guid>
            <versionId>15340370-7a36-43b8-99fa-d6b1a4c04fac</versionId>
        </inlineScript>
    </coachView>
</teamworks>

