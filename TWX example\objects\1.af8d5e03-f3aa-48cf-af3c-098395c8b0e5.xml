<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5" name="retrieve invoice data">
        <lastModified>1696416292494</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.58782546-8ded-4f9e-88f5-9cf3ee439b25</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>6e928831-d562-45ab-860b-be65504248ec</guid>
        <versionId>5534fa7b-3667-4287-83ca-3125449f7b02</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:cdc758d910e20d2d:-607ed7:18af686ab01:44e5" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.37e89de5-046f-46f8-83d4-4d74348a9fbc"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b3d59642-1748-42a4-a474-477bff7ddcf1"},{"incoming":["a428a238-aed1-4b46-b74e-52f9be5715e7","94444e1d-4f09-4b16-8b01-940036c0d545"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":824,"y":79,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6449"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"0abbb544-e2ae-4a8e-9904-89eb75e02c2d"},{"targetRef":"58782546-8ded-4f9e-88f5-9cf3ee439b25","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.37e89de5-046f-46f8-83d4-4d74348a9fbc","sourceRef":"b3d59642-1748-42a4-a474-477bff7ddcf1"},{"startQuantity":1,"outgoing":["1d7d6e4e-f688-471a-86ca-9b40bc08abeb"],"incoming":["2027.37e89de5-046f-46f8-83d4-4d74348a9fbc"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":107,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"58782546-8ded-4f9e-88f5-9cf3ee439b25","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sql = \"select * from odc_invoice where requesrid = '\"+tw.local.requestId+\"' \";"]}},{"startQuantity":1,"outgoing":["a428a238-aed1-4b46-b74e-52f9be5715e7"],"incoming":["c5fadafd-2892-4d7c-8a60-acdd2cc37bad"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":477,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Mapping output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e","scriptFormat":"text\/x-javascript","script":{"content":["\/\/tw.local.odcRequest = {};\r\n\/\/tw.local.odcRequest.BasicDetails ={};\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\n\r\nif(tw.local.sqlResults.listLength &gt; 0)\r\n{\r\n\tfor(var i =0; i&lt;tw.local.sqlResults[0].rows.listLength ; i++)\r\n\t{\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i]    \t\t\t = new tw.object.Invoice();\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate      = tw.local.sqlResults[0].rows[i].data[1];\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo        = tw.local.sqlResults[0].rows[i].data[0];\r\n\t}\r\n\r\n}"]}},{"startQuantity":1,"outgoing":["c5fadafd-2892-4d7c-8a60-acdd2cc37bad"],"incoming":["1d7d6e4e-f688-471a-86ca-9b40bc08abeb"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":291,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Linked Service Flow","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"06f0d9ae-be0e-4404-9141-4625cf95ab3e","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.sqlResults"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"06f0d9ae-be0e-4404-9141-4625cf95ab3e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Linked Service Flow","declaredType":"sequenceFlow","id":"1d7d6e4e-f688-471a-86ca-9b40bc08abeb","sourceRef":"58782546-8ded-4f9e-88f5-9cf3ee439b25"},{"targetRef":"ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e","extensionElements":{"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Mapping output","declaredType":"sequenceFlow","id":"c5fadafd-2892-4d7c-8a60-acdd2cc37bad","sourceRef":"06f0d9ae-be0e-4404-9141-4625cf95ab3e"},{"targetRef":"0abbb544-e2ae-4a8e-9904-89eb75e02c2d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"a428a238-aed1-4b46-b74e-52f9be5715e7","sourceRef":"ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.1b24506c-d5b5-4371-b75f-10da3bae659d"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"sqlParameters","isCollection":true,"declaredType":"dataObject","id":"2056.fac6381e-3d03-435f-979b-becd4cc0d094"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"sqlResults","isCollection":true,"declaredType":"dataObject","id":"2056.7b78184e-ae33-4edb-94ed-90906651efa1"},{"startQuantity":1,"outgoing":["94444e1d-4f09-4b16-8b01-940036c0d545"],"incoming":["238e771d-5d93-4c88-87c2-5a93afe7a78b","1b049706-bb8f-441e-882f-73d15aff1d52"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":476,"y":210,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Retrieve Invoice data\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"752c47ad-98a5-4611-865b-f6170c9ca4e4","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"parallelMultiple":false,"outgoing":["1b049706-bb8f-441e-882f-73d15aff1d52"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"6efacbff-de93-4c93-8b16-bff66df6d3f0"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"75f1752b-66e3-49d1-800d-3a26bbc6cfba","otherAttributes":{"eventImplId":"b9cae404-7b74-40f2-8342-4843c52efeba"}}],"attachedToRef":"ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":512,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"06bdf241-c4d7-420e-88f1-9355f9964cab","outputSet":{}},{"parallelMultiple":false,"outgoing":["238e771d-5d93-4c88-87c2-5a93afe7a78b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"854ba56f-de62-42c6-8cc3-d62fa328f863"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8361ff9e-fb7e-4b67-87b3-a97f5fd3b0c6","otherAttributes":{"eventImplId":"136be916-00b0-4098-85fc-690c5e9acd97"}}],"attachedToRef":"06f0d9ae-be0e-4404-9141-4625cf95ab3e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":326,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"9e96b0ab-72ae-4365-8ad8-01cd38244080","outputSet":{}},{"targetRef":"752c47ad-98a5-4611-865b-f6170c9ca4e4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception handling","declaredType":"sequenceFlow","id":"238e771d-5d93-4c88-87c2-5a93afe7a78b","sourceRef":"9e96b0ab-72ae-4365-8ad8-01cd38244080"},{"targetRef":"752c47ad-98a5-4611-865b-f6170c9ca4e4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception handling","declaredType":"sequenceFlow","id":"1b049706-bb8f-441e-882f-73d15aff1d52","sourceRef":"06bdf241-c4d7-420e-88f1-9355f9964cab"},{"targetRef":"0abbb544-e2ae-4a8e-9904-89eb75e02c2d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"94444e1d-4f09-4b16-8b01-940036c0d545","sourceRef":"752c47ad-98a5-4611-865b-f6170c9ca4e4"}],"laneSet":[{"id":"59e87db5-3355-444d-aa88-b6941052f972","lane":[{"flowNodeRef":["b3d59642-1748-42a4-a474-477bff7ddcf1","0abbb544-e2ae-4a8e-9904-89eb75e02c2d","58782546-8ded-4f9e-88f5-9cf3ee439b25","ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e","06f0d9ae-be0e-4404-9141-4625cf95ab3e","752c47ad-98a5-4611-865b-f6170c9ca4e4","06bdf241-c4d7-420e-88f1-9355f9964cab","9e96b0ab-72ae-4365-8ad8-01cd38244080"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"108aecce-ed51-484d-a06a-490fb4f1cec9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"retrieve invoice data","declaredType":"process","id":"1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.9d46896d-4f98-437e-8770-a36bbe647f2d"}],"inputSet":[{"dataInputRefs":["2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a"]}],"outputSet":[{"dataOutputRefs":["2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd","2055.9d46896d-4f98-437e-8770-a36bbe647f2d"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"0"}]},"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"requestId","isCollection":false,"id":"2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="requestId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a</processParameterId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>0</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7e16c991-3319-4c83-a0aa-ed61946fdf2b</guid>
            <versionId>414a4177-4b44-4504-bce1-80659a0d853b</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd</processParameterId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6076e7a7-8fed-4d92-85bc-787f161dc739</guid>
            <versionId>e12c2947-f112-41ed-a84d-ad50c7f0ef10</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9d46896d-4f98-437e-8770-a36bbe647f2d</processParameterId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c377effe-d8f9-4d7f-ab45-6a18cbb21d8f</guid>
            <versionId>c126d737-3736-4583-8ed8-77b441f63145</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1b24506c-d5b5-4371-b75f-10da3bae659d</processVariableId>
            <description isNull="true" />
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>723d9e9b-f70b-403e-a14e-f59d8761444d</guid>
            <versionId>73906dda-59cb-4bd8-be9e-fa32ce12dad2</versionId>
        </processVariable>
        <processVariable name="sqlParameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fac6381e-3d03-435f-979b-becd4cc0d094</processVariableId>
            <description isNull="true" />
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f5b14909-58f1-41ef-bb81-78a5aec88fe4</guid>
            <versionId>064f530a-ec77-4e8a-933e-99120d317316</versionId>
        </processVariable>
        <processVariable name="sqlResults">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7b78184e-ae33-4edb-94ed-90906651efa1</processVariableId>
            <description isNull="true" />
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b5f363d2-9751-4f44-9b58-c042ce7e1ebc</guid>
            <versionId>485a9b42-b37c-4917-b054-419f02a12370</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.752c47ad-98a5-4611-865b-f6170c9ca4e4</processItemId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <name>Exception handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d77602a8-844a-4f4d-870f-ba308c3dc236</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f57c3a34823ee126:-91461cc:18aef7c4778:3de6</guid>
            <versionId>0bf00cec-c2c7-44f4-b759-c6d9919d3ff2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="476" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d77602a8-844a-4f4d-870f-ba308c3dc236</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>68dbff79-c2bb-4d1c-9dce-0c6febf0cb7d</guid>
                <versionId>b1d3b426-eda2-4e98-ae1e-558262054d13</versionId>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bf9dfd37-fd98-42d6-b53e-2a7fa0f5f9d9</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.d77602a8-844a-4f4d-870f-ba308c3dc236</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>dc9228fa-385b-47d2-9605-de205bff5cc9</guid>
                    <versionId>8a283bf6-4016-49ad-a5cb-de3a24e5e2b7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.89b90dee-2e16-42ad-b346-3aba3d5b095a</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.d77602a8-844a-4f4d-870f-ba308c3dc236</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Retrieve Invoice data"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>97df0064-cbde-4a62-9b83-7df3d3780e0f</guid>
                    <versionId>e46a7d99-5d24-4495-8631-7486060b69d5</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e</processItemId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <name>Mapping output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6aac60c4-5967-4f12-adfe-9d76de0bceb4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.752c47ad-98a5-4611-865b-f6170c9ca4e4</errorHandlerItemId>
            <guid>guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6448</guid>
            <versionId>3eba9e6e-b572-47dc-a984-1e7d3a4b7cfb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="477" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:f57c3a34823ee126:-91461cc:18aef7c4778:3de6</errorHandlerItem>
                <errorHandlerItemId>2025.752c47ad-98a5-4611-865b-f6170c9ca4e4</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6aac60c4-5967-4f12-adfe-9d76de0bceb4</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//tw.local.odcRequest = {};&#xD;
//tw.local.odcRequest.BasicDetails ={};&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
&#xD;
if(tw.local.sqlResults.listLength &gt; 0)&#xD;
{&#xD;
	for(var i =0; i&lt;tw.local.sqlResults[0].rows.listLength ; i++)&#xD;
	{&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i]    			 = new tw.object.Invoice();&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate      = tw.local.sqlResults[0].rows[i].data[1];&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo        = tw.local.sqlResults[0].rows[i].data[0];&#xD;
	}&#xD;
&#xD;
}</script>
                <isRule>false</isRule>
                <guid>4ae6b1d9-748c-48fd-ba41-5353e790c72d</guid>
                <versionId>e70be29d-6672-41d1-8fcf-d69a3ba25a9e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.06f0d9ae-be0e-4404-9141-4625cf95ab3e</processItemId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <name>Linked Service Flow</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.fad2dc18-1a99-4c33-9fe7-08061964cc34</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.752c47ad-98a5-4611-865b-f6170c9ca4e4</errorHandlerItemId>
            <guid>guid:f57c3a34823ee126:-91461cc:18aef7c4778:-644a</guid>
            <versionId>5ac54e2f-f3e0-4f7c-b6d2-414a844c35c9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="291" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:f57c3a34823ee126:-91461cc:18aef7c4778:3de6</errorHandlerItem>
                <errorHandlerItemId>2025.752c47ad-98a5-4611-865b-f6170c9ca4e4</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.fad2dc18-1a99-4c33-9fe7-08061964cc34</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>9ab2e5eb-19ca-4983-b227-2f4a63bfb174</guid>
                <versionId>323229e3-f131-48a1-b0ca-c60f7a353971</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.250443ce-40fd-406c-b3f8-c32cac74b4e8</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.fad2dc18-1a99-4c33-9fe7-08061964cc34</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4872afe5-f414-41e0-86c7-c3ed3217e6c0</guid>
                    <versionId>11752408-75da-4ef0-b5e6-2e212c22dada</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.80409630-0d4b-4540-9b69-05f79d3f6168</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.fad2dc18-1a99-4c33-9fe7-08061964cc34</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlResults</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>d1bf2bb4-75ce-49df-b8a5-ff9f6363da7b</guid>
                    <versionId>2653bc2b-29c8-47b3-9b13-ba49532b79c6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.200b49a9-9f94-4db5-a15c-d36c753ee42e</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.fad2dc18-1a99-4c33-9fe7-08061964cc34</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>0d7c6801-949e-44b4-8842-2795fead8a4f</guid>
                    <versionId>5bb57e9c-ec4f-4e10-b8a0-72e1073fb186</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b875326f-5fd8-4480-b9d7-755f833368c3</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.fad2dc18-1a99-4c33-9fe7-08061964cc34</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ac5a3fec-98fb-40c9-ab79-350422be155f</guid>
                    <versionId>75b66b67-e176-4f91-aa4a-6dcbafb27b3f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.270ba893-696b-42da-9885-f8340188b114</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.fad2dc18-1a99-4c33-9fe7-08061964cc34</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>30398e46-ef77-4497-bc5c-eb24d9029085</guid>
                    <versionId>c9eabb29-8155-4aee-a14b-fd8a36dfa725</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0abbb544-e2ae-4a8e-9904-89eb75e02c2d</processItemId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.b4c643a3-e44b-487a-a79d-9f6df520f39d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6449</guid>
            <versionId>68627e87-35ef-4771-b15c-a277cf824d72</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="824" y="79">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.b4c643a3-e44b-487a-a79d-9f6df520f39d</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>10625d68-14b5-42b8-ad48-4ef0773ad115</guid>
                <versionId>9fc9a8c6-fc37-4b11-94fb-f43ea6f354aa</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.58782546-8ded-4f9e-88f5-9cf3ee439b25</processItemId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2b3ca7cc-936d-4040-a84c-be6279b701a2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6447</guid>
            <versionId>8679e0db-180e-4cce-a079-f14a3d371d4e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="107" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2b3ca7cc-936d-4040-a84c-be6279b701a2</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql = "select * from odc_invoice where requesrid = '"+tw.local.requestId+"' ";</script>
                <isRule>false</isRule>
                <guid>531e7e13-830c-4518-bf20-3649d6f4a20a</guid>
                <versionId>c1443a50-26ab-41f1-a377-dbdcb1c70c5f</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.58782546-8ded-4f9e-88f5-9cf3ee439b25</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="retrieve invoice data" id="1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="requestId" itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" id="2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">0</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.9d46896d-4f98-437e-8770-a36bbe647f2d" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.9d46896d-4f98-437e-8770-a36bbe647f2d</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="59e87db5-3355-444d-aa88-b6941052f972">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="108aecce-ed51-484d-a06a-490fb4f1cec9" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b3d59642-1748-42a4-a474-477bff7ddcf1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0abbb544-e2ae-4a8e-9904-89eb75e02c2d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>58782546-8ded-4f9e-88f5-9cf3ee439b25</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>06f0d9ae-be0e-4404-9141-4625cf95ab3e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>752c47ad-98a5-4611-865b-f6170c9ca4e4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>06bdf241-c4d7-420e-88f1-9355f9964cab</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9e96b0ab-72ae-4365-8ad8-01cd38244080</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b3d59642-1748-42a4-a474-477bff7ddcf1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.37e89de5-046f-46f8-83d4-4d74348a9fbc</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="0abbb544-e2ae-4a8e-9904-89eb75e02c2d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="824" y="79" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6449</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a428a238-aed1-4b46-b74e-52f9be5715e7</ns16:incoming>
                        
                        
                        <ns16:incoming>94444e1d-4f09-4b16-8b01-940036c0d545</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b3d59642-1748-42a4-a474-477bff7ddcf1" targetRef="58782546-8ded-4f9e-88f5-9cf3ee439b25" name="To End" id="2027.37e89de5-046f-46f8-83d4-4d74348a9fbc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="58782546-8ded-4f9e-88f5-9cf3ee439b25">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="107" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.37e89de5-046f-46f8-83d4-4d74348a9fbc</ns16:incoming>
                        
                        
                        <ns16:outgoing>1d7d6e4e-f688-471a-86ca-9b40bc08abeb</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql = "select * from odc_invoice where requesrid = '"+tw.local.requestId+"' ";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Mapping output" id="ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="477" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c5fadafd-2892-4d7c-8a60-acdd2cc37bad</ns16:incoming>
                        
                        
                        <ns16:outgoing>a428a238-aed1-4b46-b74e-52f9be5715e7</ns16:outgoing>
                        
                        
                        <ns16:script>//tw.local.odcRequest = {};&#xD;
//tw.local.odcRequest.BasicDetails ={};&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
&#xD;
if(tw.local.sqlResults.listLength &gt; 0)&#xD;
{&#xD;
	for(var i =0; i&lt;tw.local.sqlResults[0].rows.listLength ; i++)&#xD;
	{&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i]    			 = new tw.object.Invoice();&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate      = tw.local.sqlResults[0].rows[i].data[1];&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo        = tw.local.sqlResults[0].rows[i].data[0];&#xD;
	}&#xD;
&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" name="Linked Service Flow" id="06f0d9ae-be0e-4404-9141-4625cf95ab3e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="291" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1d7d6e4e-f688-471a-86ca-9b40bc08abeb</ns16:incoming>
                        
                        
                        <ns16:outgoing>c5fadafd-2892-4d7c-8a60-acdd2cc37bad</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.sqlResults</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="58782546-8ded-4f9e-88f5-9cf3ee439b25" targetRef="06f0d9ae-be0e-4404-9141-4625cf95ab3e" name="To Linked Service Flow" id="1d7d6e4e-f688-471a-86ca-9b40bc08abeb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="06f0d9ae-be0e-4404-9141-4625cf95ab3e" targetRef="ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e" name="To Mapping output" id="c5fadafd-2892-4d7c-8a60-acdd2cc37bad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e" targetRef="0abbb544-e2ae-4a8e-9904-89eb75e02c2d" name="To End" id="a428a238-aed1-4b46-b74e-52f9be5715e7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.1b24506c-d5b5-4371-b75f-10da3bae659d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="sqlParameters" id="2056.fac6381e-3d03-435f-979b-becd4cc0d094" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="sqlResults" id="2056.7b78184e-ae33-4edb-94ed-90906651efa1" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exception handling" id="752c47ad-98a5-4611-865b-f6170c9ca4e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="476" y="210" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>238e771d-5d93-4c88-87c2-5a93afe7a78b</ns16:incoming>
                        
                        
                        <ns16:incoming>1b049706-bb8f-441e-882f-73d15aff1d52</ns16:incoming>
                        
                        
                        <ns16:outgoing>94444e1d-4f09-4b16-8b01-940036c0d545</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Retrieve Invoice data"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e" parallelMultiple="false" name="Error" id="06bdf241-c4d7-420e-88f1-9355f9964cab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="512" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1b049706-bb8f-441e-882f-73d15aff1d52</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="6efacbff-de93-4c93-8b16-bff66df6d3f0" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="75f1752b-66e3-49d1-800d-3a26bbc6cfba" eventImplId="b9cae404-7b74-40f2-8342-4843c52efeba">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="06f0d9ae-be0e-4404-9141-4625cf95ab3e" parallelMultiple="false" name="Error1" id="9e96b0ab-72ae-4365-8ad8-01cd38244080">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="326" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>238e771d-5d93-4c88-87c2-5a93afe7a78b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="854ba56f-de62-42c6-8cc3-d62fa328f863" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8361ff9e-fb7e-4b67-87b3-a97f5fd3b0c6" eventImplId="136be916-00b0-4098-85fc-690c5e9acd97">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9e96b0ab-72ae-4365-8ad8-01cd38244080" targetRef="752c47ad-98a5-4611-865b-f6170c9ca4e4" name="To Exception handling" id="238e771d-5d93-4c88-87c2-5a93afe7a78b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="06bdf241-c4d7-420e-88f1-9355f9964cab" targetRef="752c47ad-98a5-4611-865b-f6170c9ca4e4" name="To Exception handling" id="1b049706-bb8f-441e-882f-73d15aff1d52">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="752c47ad-98a5-4611-865b-f6170c9ca4e4" targetRef="0abbb544-e2ae-4a8e-9904-89eb75e02c2d" name="To End" id="94444e1d-4f09-4b16-8b01-940036c0d545">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightBottom</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Mapping output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c5fadafd-2892-4d7c-8a60-acdd2cc37bad</processLinkId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.06f0d9ae-be0e-4404-9141-4625cf95ab3e</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e</toProcessItemId>
            <guid>484217e3-8cff-4d73-898f-8b8eda7bfea2</guid>
            <versionId>92f960f5-2b36-4c0b-899b-6f9b2a32cd75</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.06f0d9ae-be0e-4404-9141-4625cf95ab3e</fromProcessItemId>
            <toProcessItemId>2025.ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e</toProcessItemId>
        </link>
        <link name="To Linked Service Flow">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1d7d6e4e-f688-471a-86ca-9b40bc08abeb</processLinkId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.58782546-8ded-4f9e-88f5-9cf3ee439b25</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.06f0d9ae-be0e-4404-9141-4625cf95ab3e</toProcessItemId>
            <guid>08fb284c-47bb-44d3-97bc-88752bec19f8</guid>
            <versionId>b6ce8efc-d9f6-4d82-a393-33707a902bab</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.58782546-8ded-4f9e-88f5-9cf3ee439b25</fromProcessItemId>
            <toProcessItemId>2025.06f0d9ae-be0e-4404-9141-4625cf95ab3e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.94444e1d-4f09-4b16-8b01-940036c0d545</processLinkId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.752c47ad-98a5-4611-865b-f6170c9ca4e4</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.0abbb544-e2ae-4a8e-9904-89eb75e02c2d</toProcessItemId>
            <guid>0335d5fb-5cbd-457a-bb6a-104c61351f04</guid>
            <versionId>f878f2f3-9ddb-4066-850d-78d64ce95e53</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightBottom" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.752c47ad-98a5-4611-865b-f6170c9ca4e4</fromProcessItemId>
            <toProcessItemId>2025.0abbb544-e2ae-4a8e-9904-89eb75e02c2d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a428a238-aed1-4b46-b74e-52f9be5715e7</processLinkId>
            <processId>1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.0abbb544-e2ae-4a8e-9904-89eb75e02c2d</toProcessItemId>
            <guid>69095295-1d52-4e07-8da0-4109bba97d8e</guid>
            <versionId>fdad5e05-0757-440d-a41d-fe4ecf787f40</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ff517dff-ac82-4e5c-9ba5-95cbb5b1a33e</fromProcessItemId>
            <toProcessItemId>2025.0abbb544-e2ae-4a8e-9904-89eb75e02c2d</toProcessItemId>
        </link>
    </process>
</teamworks>

