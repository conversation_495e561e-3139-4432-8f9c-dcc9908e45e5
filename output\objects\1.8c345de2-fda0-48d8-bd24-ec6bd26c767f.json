{"id": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "versionId": "f58cde10-b5fc-458d-8b18-10ee2601f718", "name": "Get Parent Request 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "Get Parent Request 2", "lastModified": "1698558539965", "lastModifiedBy": "<PERSON><PERSON>", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "2025.92275c3d-a15d-4b6c-bffc-0a7bb4a2943c"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "8f527cd4-d094-4f1f-8044-0e361d84bc77", "versionId": "f58cde10-b5fc-458d-8b18-10ee2601f718", "dependencySummary": "<dependencySummary id=\"37e7ddd8-27e3-491b-95ba-2d595875219b\" />", "jsonData": {"isNull": "true"}, "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "cif", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0f7d1cfb-8702-4df0-8644-697e79b3f609", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "\"06316421\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "84e71412-9183-41a6-a45f-a89ba887e364", "versionId": "221579d7-dde9-436e-9faa-59c6d78892d9"}, {"name": "requestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0cf38a4f-9c47-4a73-b458-11e5f57b4b1e", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"00102230001092\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "c0a57596-e27e-48fe-8fb4-106e22f1802e", "versionId": "fff35520-f039-47bb-b33e-91681c714a10"}, {"name": "parentIDCRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.bd0b918c-9e61-486c-8c6b-693ec45ba6e4", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "1", "isArrayOf": "false", "classId": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1a769a64-7a34-4180-aee9-f3e3768c1a6f", "versionId": "719b595b-1805-4158-8418-7869a0eac0c8"}, {"name": "message", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a6bce394-8d1d-45a9-99b5-b0425e1b18d5", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "544df333-**************-06d5d8cc3723", "versionId": "5bf6dd58-60d6-4871-a5b5-a20856b88a42"}, {"name": "isFound", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.59c91571-b2e5-4723-a942-1f7cae91b2ae", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4a2ec464-10df-4f92-ae68-f1a9f5121700", "versionId": "c86b07bb-b836-4990-aed9-c1f1108b9d4a"}, {"name": "parentIDCRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.3ffa3f80-0b60-4c91-bb4a-6f98f6a45314", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "2", "isArrayOf": "false", "classId": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3d568ef5-27ab-415f-a68b-************", "versionId": "b79ab9a5-267c-4f45-b03a-eb81bb19590b"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ba16966b-40cc-4adb-8812-eb868ed6c2d5", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "2", "isArrayOf": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "seq": "7", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6097c4b6-b582-4304-a757-39b44cc37244", "versionId": "11887336-195e-4177-9de8-fca9583f85a2"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e43c8f5f-9b0a-47a8-a9ec-129a4b663b3d", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "parameterType": "3", "isArrayOf": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "78cce108-8d6b-46c7-9737-f9010b2e0247", "versionId": "72e79878-e199-466f-939d-b3357ed5f34c"}], "processVariable": [{"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8016fb98-bc55-4958-ba55-b215e17ca401", "description": {"isNull": "true"}, "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "42eda11e-5ba3-4cb0-8dbf-36c1140af261", "versionId": "2d9dd390-0ffc-4a75-8b90-d82ed9e490e6"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5aeb7335-d06b-42d4-ac71-3dc8d2b9202c", "description": {"isNull": "true"}, "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "372664f2-8d35-479e-8899-68662bd90afb", "versionId": "fba5c9ce-00e9-4e18-8867-0212de2c49f6"}, {"name": "subResult", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.86926535-8e9f-48c3-9df8-6a9b2be3080e", "description": {"isNull": "true"}, "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a2b5c8bb-**************-01a35dc403c0", "versionId": "bc6a7484-f302-4a3b-a0cd-daf75912ae92"}, {"name": "errorMSG", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b689a13f-1e96-4996-b241-141c30ff972d", "description": {"isNull": "true"}, "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "56a36810-7456-48ac-96da-4fe6694586a0", "versionId": "bbf1b841-2cd9-4486-abd3-4f36ed67088a"}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2f573e78-dd3e-43bf-b64e-ba2c26666e94", "description": {"isNull": "true"}, "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "17fb5393-0ae8-43e0-a425-cd6feb60a3d4", "versionId": "4dec03a2-9a5c-4ffa-bace-079ea9e14e34"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.5207ac9e-0782-41c6-a330-928a00201e6b", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.08821fe2-1eb5-4bb4-a14c-ccad5cec2145", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:447", "versionId": "06cd2fdd-898f-42da-b5e4-a77d25663eff", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1140", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.08821fe2-1eb5-4bb4-a14c-ccad5cec2145", "haltProcess": "false", "guid": "9a5194a3-1639-401f-8f26-92f2f9dfdcd3", "versionId": "bf8ecbf5-723f-46c6-b6c1-c81bc8fa694d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "Catch Errors", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.d25eeaaf-8d3b-40f9-8982-ff1e2f942fdd", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:448", "versionId": "46bc18f7-9fca-48f0-b68c-f8952bd77ab4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "1107", "y": "254", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.d25eeaaf-8d3b-40f9-8982-ff1e2f942fdd", "scriptTypeId": "2", "isActive": "true", "script": "if (tw.local.error != undefined && !!tw.local.error.errorText) {\r\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\r\n}else if (tw.system.error != undefined && tw.system.error != null) {\r\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}else{\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}\r\r\n\r\r\ntw.local.error.errorText = \"Error Occured<br> Service Name : \"+tw.system.serviceFlow.name+\"<br> Error Message : \"+tw.local.errorMSG;", "isRule": "false", "guid": "7ae0423a-2cc4-440f-9614-24cc732fc1ce", "versionId": "5a783caa-9b66-43fd-8274-80b067958fd4"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b4fc285f-c557-4898-a65b-7f77c5c2dccc", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "Exclusive Gateway", "tWComponentName": "Switch", "tWComponentId": "3013.94deddb8-cf60-4223-a221-091003119ab6", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:446", "versionId": "4fc0a5c4-9f79-439c-8069-13398a50514b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "545", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.94deddb8-cf60-4223-a221-091003119ab6", "guid": "6ad38ea6-b543-4254-a140-f760f4ae55cb", "versionId": "3b1fcd6a-45dc-4ca9-8647-cdeab06085b7", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.9a0d1c77-ed4a-40e4-b838-e6fec200f592", "switchId": "3013.94deddb8-cf60-4223-a221-091003119ab6", "seq": "1", "endStateId": "guid:aff098473ecd546d:1d42df0a:18b37664be6:-6ffc", "condition": "tw.local.isFound\t  ==\t  false", "guid": "5e0aa95d-cd54-4fc5-91cc-52be4b412f9b", "versionId": "167004b1-3915-4af1-9d3f-d5514a8b1ddf"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f6e2fe58-82f2-441f-8cc2-47b228d2129f", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "Init SQL for sub", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.e63a6bc4-3523-46dc-8302-9282e3f8dc38", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:44a", "versionId": "8d5a1608-b997-42f4-abec-2c1368ada93b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "675", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error3", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:448", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.e63a6bc4-3523-46dc-8302-9282e3f8dc38", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar j = 0;\r\r\nvar i = 0;\r\r\nfunction paramInit (value) {\r\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\r\n\ti++;\r\r\n}\r\r\n//------------------------------------- Get invoice --------------------------------------------\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT  INVOICE_NUMBER, INVOICE_DATE FROM BPM.IDC_REQUEST_INVOICES WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get bills --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT BILL_OF_LADING_REF, BILL_OF_LADING_DATE FROM BPM.IDC_REQUEST_BILLS WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get customer --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT IMPORT_CARD_NUMBER FROM BPM.IDC_CUSTOMER_INFORMATION WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get multi tenor --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT INSTALLMENT_DATE, INSTALLMENT_AMOUNT FROM BPM.IDC_REQUEST_MULTI_TENOR WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get advance payment --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT d.ID,d.REQUEST_NUMBER,BENEFICIARY_NAME, CURRENCY, DOCUMENT_AMOUNT, AMOUNT_PAYABLE_BY_NBE, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT,i.INVOICE_NUMBER,a.AMOUNT_ALLOCATED_FOR_REQUEST, a.ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY FROM BPM.IDC_REQUEST_DETAILS d join BPM.IDC_REQUEST_INVOICES i on i.IDC_REQUEST_ID = d.ID  join BPM.IDC_ADVANCED_PAYMENT_USED a on a.IDC_REQUEST_ID = d.ID where a.REFERRAL_REQUEST_NUMBER= ? ORDER BY d.ID;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.requestNumber);", "isRule": "false", "guid": "c0db1d0e-8a92-4c37-8dcc-516d9bc50aa4", "versionId": "837d2ab6-e02e-4a49-9306-d5ee3533b059"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "Init SQL", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.53616716-388a-4157-b108-fa4f16ac28a1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:449", "versionId": "9969ca44-2c0b-4e30-929d-24bd58de3857", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "138", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:448", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.53616716-388a-4157-b108-fa4f16ac28a1", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar j = 0;\r\r\nvar i = 0;\r\r\nfunction paramInit (value) {\r\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\r\n\ti++;\r\r\n}\r\r\n//------------------------------------- Get Parent IDC -------------------------------------------- \r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT * FROM BPM.IDC_REQUEST_DETAILS d join BPM.IDC_CUSTOMER_INFORMATION c on d.ID = c.IDC_REQUEST_ID WHERE (d.REQUEST_STATE = 'Initial' or d.REQUEST_STATE = 'Final') AND d.REQUEST_STATUS = 'Completed' AND c.CIF = ? AND d.ID = (SELECT max(ID) FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER like ? AND REQUEST_STATUS != 'Terminated' AND REQUEST_STATUS != 'Canceled' ); \"\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.cif);\r\r\nparamInit(tw.local.requestNumber+\"%\");\r\r\n//------------------------------------- Check Type Of IDC -----------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT d.ID FROM BPM.IDC_REQUEST_DETAILS d join BPM.IDC_REQUEST_TYPE t on d.REQUEST_TYPE_ID = t.ID WHERE d.REQUEST_NUMBER = ? AND (t.ENGLISH_DESCRIPTION = 'IDC Execution' OR t.ENGLISH_DESCRIPTION = 'IDC Acknowledgement');\"\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.requestNumber);\r\r\n\r\r\n", "isRule": "false", "guid": "ed5cbb28-48d3-4f16-a32e-17d45c108cee", "versionId": "99becbe6-b8cc-4340-b706-b7f5d3765b7f"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b1e91750-9d6e-4693-a7ac-21e3560c8a19", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "Mapping", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.054fc345-4cb9-422a-ab46-20bf84a9d3c3", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:442", "versionId": "9bc187ca-b18c-4ebb-824b-ffa690574812", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "997", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error5", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:448", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.054fc345-4cb9-422a-ab46-20bf84a9d3c3", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar i=0-1;\r\r\nfunction getnext () {\r\r\n\ti++;\r\r\n\treturn tw.local.results[0].rows[0].data[i];\r\r\n}\r\r\n\r\r\ntw.local.parentIDCRequest.DBID = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.appInfo.instanceID = getnext ();\r\r\ntw.local.parentIDCRequest.ParentIDCRequestNumber =getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.appID = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.appInfo.initiator = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.appInfo.branch.name = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.IDCRequestNature.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.IDCRequestType.id = getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.requestDate = getnext().format(\"dd/MM/yyyy\"); \r\r\ntw.local.parentIDCRequest.IDCRequestState = getnext ();\r\r\ntw.local.parentIDCRequest.IDCRequestStage = getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.status = getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.subStatus = getnext ();\r\r\ntw.local.parentIDCRequest.FCContractNumber = getnext ();\r\r\ntw.local.parentIDCRequest.isIDCWithdrawn = Boolean(getnext ());\r\r\n\r\r\ntw.local.parentIDCRequest.importPurpose.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.paymentTerms.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.documentsSource.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productCategory.id = getnext ();\r\r\ntw.local.parentIDCRequest.commodityDescription = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.amtPayableByNBE = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.documentAmount= getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.documentCurrency.code= getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.chargesAccount = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfForeignCurrency.id= getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfFunds.id = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.paymentAccount = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.cashAmtInDocCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.CashAmtWithNoCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.facilityAmtInDocCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.facilityAmtWithNoCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.discountAmt = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amountAdvanced = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtPaidbyOtherBanks = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtSight = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtDeferredNoAvalized = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtDeferredAvalized = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtPayableByNBE = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.firstInstallementMaturityDate = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.daysTillMaturity = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.name = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.bank = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.account = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.country.code = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.tradeFOReferenceNumber = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.tradeFinanceApprovalNumber = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.executionHub.code = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.executionHub.arabicdescription = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.shippingDate = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.incoterms.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.shipmentMethod.id = getnext ();\r\r\ntw.local.parentIDCRequest.productsDetails.destinationPort = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.CBECommodityClassification.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.HSProduct.code = getnext ();\r\r\ntw.local.parentIDCRequest.productsDetails.HSProduct.englishdescription = getnext ();\r\r\ntw.local.parentIDCRequest.productsDetails.ACID = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.countryOfOrigin.code = getnext ();\r\r\ngetnext ();\r\r\ngetnext ();\r\r\ngetnext ();\r\r\ntw.local.parentIDCRequest.releaseAmount = getnext ();\r\r\n//------------------------------------- invoice ---------------------------------------\r\r\ntw.local.parentIDCRequest.invoices = new tw.object.listOf.Invoice();\r\r\nfor (var i=0; i<tw.local.subResult[0].rows.listLength; i++) {\r\r\n\ttw.local.parentIDCRequest.invoices[i] = new tw.object.Invoice();\r\r\n\ttw.local.parentIDCRequest.invoices[i].number = tw.local.subResult[0].rows[i].data[0];\r\r\n\ttw.local.parentIDCRequest.invoices[i].date = tw.local.subResult[0].rows[i].data[1];\r\r\n}\r\r\n//------------------------------------- bill ---------------------------------------\r\r\ntw.local.parentIDCRequest.billOfLading = new tw.object.listOf.Invoice();\r\r\nfor (var i=0; i<tw.local.subResult[1].rows.listLength; i++) {\r\r\n\ttw.local.parentIDCRequest.billOfLading[i] = new tw.object.Invoice();\r\r\n\ttw.local.parentIDCRequest.billOfLading[i].number = tw.local.subResult[1].rows[i].data[0];\r\r\n\ttw.local.parentIDCRequest.billOfLading[i].date = tw.local.subResult[1].rows[i].data[1];\r\r\n}\r\r\n//---------------------------------------- customer ----------------------------\r\r\ntw.local.parentIDCRequest.customerInformation = new tw.object.CustomerInformation();\r\r\ntw.local.parentIDCRequest.customerInformation.importCardNumber =  tw.local.subResult[2].rows[0].data[0];\r\r\n//--------------------------------------- multi tenor ------------------------------\r\r\ntw.local.parentIDCRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\r\nfor (var i=0; i<tw.local.subResult[3].rows.listLength; i++) {\r\r\n\ttw.local.parentIDCRequest.financialDetails.paymentTerms[i] = new tw.object.PaymentTerm();\r\r\n\ttw.local.parentIDCRequest.financialDetails.paymentTerms[i].installmentDate = tw.local.subResult[3].rows[i].data[0];\r\r\n\ttw.local.parentIDCRequest.financialDetails.paymentTerms[i].installmentAmount = tw.local.subResult[3].rows[i].data[1];\r\r\n}\r\r\n//---------------------------------------advancce payment ------------------------------------------------\r\r\ntw.local.parentIDCRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\r\nvar apID = 0;\r\r\nvar n = 0;\r\r\nif (tw.local.subResult[4].rows.listLength == 0) {\r\r\n\ttw.local.parentIDCRequest.financialDetails.isAdvancePaymentsUsed = false;\r\r\n}else{\r\r\n\tvar apID = tw.local.subResult[4].rows[0].data[0];\r\r\n\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\r\r\n\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[0].invoiceNumber =\"\";\r\r\n\ttw.local.parentIDCRequest.financialDetails.isAdvancePaymentsUsed = true;\r\r\n\tfor (var i=0; i<tw.local.subResult[4].rows.listLength; i++) {\r\r\n\t\tif (apID!= tw.local.subResult[4].rows[i].data[0]) {\r\r\n\t\t\t\r\r\n\t\t\tapID = tw.local.subResult[4].rows[i].data[0];\r\r\n\t\t\tn++;\r\r\n\t\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n] = new tw.object.UsedAdvancePayment();\r\r\n\t\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceNumber =\"\";\r\r\n\t\t}\r\r\n\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].DBID = tw.local.subResult[4].rows[i].data[0];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].advancePaymentRequestNumber = tw.local.subResult[4].rows[i].data[1];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].beneficiaryName = tw.local.subResult[4].rows[i].data[2];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceCurrency  = new tw.object.DBLookup();\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceCurrency.code = tw.local.subResult[4].rows[i].data[3];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].documentAmount = tw.local.subResult[4].rows[i].data[4];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].paidAmount = tw.local.subResult[4].rows[i].data[5];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].outstandingAmount = tw.local.subResult[4].rows[i].data[6];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceNumber += tw.local.subResult[4].rows[i].data[7]+\"\\n\";\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].AmountAllocated = tw.local.subResult[4].rows[i].data[8];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].AllocatedAmountinRequestCurrency = tw.local.subResult[4].rows[i].data[9];\r\r\n\r\r\n\t}\r\r\n}", "isRule": "false", "guid": "bc7faaa4-6dce-455a-b39b-852f8e08ffe0", "versionId": "131e62f3-2677-4759-9ba8-d170b47f635f"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8991738b-47d4-4337-9a34-0460d876d069", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "check is found", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.e1bc55c7-8b85-4c5c-b761-d8717264cf7c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:444", "versionId": "b0d3cacc-724c-4176-8efc-88d7b1303c22", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "402", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomLeft", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:448", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.e1bc55c7-8b85-4c5c-b761-d8717264cf7c", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.parentIDCRequest = new tw.object.IDCRequest();\r\r\ntw.local.parentIDCRequest.appInfo = new tw.object.AppInfo();\r\r\ntw.local.parentIDCRequest.appInfo.branch = new tw.object.NameValuePair();\r\r\ntw.local.parentIDCRequest.IDCRequestNature =new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.IDCRequestType = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.importPurpose = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.paymentTerms = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.documentsSource = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productCategory = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails = new tw.object.FinancialDetails();\r\r\ntw.local.parentIDCRequest.financialDetails.documentCurrency =new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup(); \r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails.executionHub = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails = new tw.object.ProductsDetails();\r\r\ntw.local.parentIDCRequest.productsDetails.incoterms = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails.HSProduct = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.countryOfOrigin = new tw.object.DBLookup();\r\r\n\r\r\nif (tw.local.results[0].rows.listLength == 0) {\r\r\n\ttw.local.message = \"This request number is not exist or another request editing on it\";\r\r\n\ttw.local.isFound = false;\r\r\n}else if(tw.local.results[1].rows.listLength == 0){\r\r\n\ttw.local.isFound = false;\r\r\n\ttw.local.message = \"This request number is not related to IDC Acknowledgement or IDC Execution request\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.isFound = true;\r\r\n}", "isRule": "false", "guid": "a61cb95f-5301-4f08-bc8f-5f57820c1082", "versionId": "8e7b1a28-353c-422d-9e63-d0843ee8fe1b"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.443fea9d-1c61-4752-aa58-2ea557669e1e", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "sub SQL Execute Multiple Statements (SQLResult)", "tWComponentName": "SubProcess", "tWComponentId": "3012.bb045b31-5ffd-44e8-b005-a4cafd8809d9", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:441", "versionId": "b8818cbe-6156-4470-a74f-d4247878f21e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "846", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error4", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:448", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.bb045b31-5ffd-44e8-b005-a4cafd8809d9", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7", "guid": "c3bc4e97-10b6-4700-b52f-65c2ca46bdc5", "versionId": "d7899eba-c129-4524-a43e-b49c2730ab97", "parameterMapping": [{"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e695c19d-6ac9-47ca-93ac-087cb887bc97", "processParameterId": "2055.a634f531-c979-476c-91db-a17bf5e55c90", "parameterMappingParentId": "3012.bb045b31-5ffd-44e8-b005-a4cafd8809d9", "useDefault": "false", "value": "tw.env.DATASOURCE", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "d4fd881c-2743-4df9-aa30-0732075d3cbb", "versionId": "6aee7f47-7bc9-4c62-978a-039a34eb7591", "description": {"isNull": "true"}}, {"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ab5c179e-06c8-4ec5-a57b-361ffcf0563b", "processParameterId": "2055.9695b715-db44-410b-8a74-65cf1d31d8ab", "parameterMappingParentId": "3012.bb045b31-5ffd-44e8-b005-a4cafd8809d9", "useDefault": "false", "value": "tw.local.sqlStatements", "classRef": "/12.3990d8b4-983d-4250-bd06-3600f527fed0", "isList": "true", "isInput": "true", "guid": "0e3cfe96-f920-4c2d-b9b4-47108b4e2d77", "versionId": "a0d9b774-5203-491d-86cd-206dd32b1a6c", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.254af8aa-6e29-45eb-ba5c-25fff290a7ff", "processParameterId": "2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e", "parameterMappingParentId": "3012.bb045b31-5ffd-44e8-b005-a4cafd8809d9", "useDefault": "false", "value": "tw.local.subResult", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "e771091f-4fcc-4ef2-9e0b-203bde0f018e", "versionId": "afa0129a-9a46-4c91-9b38-44f4243bc3e1", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "SQL Execute Multiple Statements (SQLResult)", "tWComponentName": "SubProcess", "tWComponentId": "3012.d41f1070-ab37-4bd4-b1ab-6efaaa935bcd", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:443", "versionId": "c3747eb9-eb22-419e-9090-22115227dd0d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "265", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:448", "errorHandlerItemId": "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.d41f1070-ab37-4bd4-b1ab-6efaaa935bcd", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7", "guid": "73ac47ac-ee0b-4671-a041-ac578ff61694", "versionId": "68ffdb21-4423-4391-a96b-e692fd831c52", "parameterMapping": [{"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0ead445b-c47e-44c5-956f-74f914a370b6", "processParameterId": "2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e", "parameterMappingParentId": "3012.d41f1070-ab37-4bd4-b1ab-6efaaa935bcd", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "6f297f7d-32fb-4d55-857e-c8d4b91e5923", "versionId": "0da34ea4-6746-4e72-bcb8-b20ef459f9db", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.bb3ebb59-7ea4-424a-a001-7ae9abb3b6ce", "processParameterId": "2055.a634f531-c979-476c-91db-a17bf5e55c90", "parameterMappingParentId": "3012.d41f1070-ab37-4bd4-b1ab-6efaaa935bcd", "useDefault": "false", "value": "tw.env.DATASOURCE", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "84d3d781-8832-49f8-a199-409bd273e073", "versionId": "34ebd9e2-d3a8-4cd2-9910-5050afafb515", "description": {"isNull": "true"}}, {"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.164a0896-acc2-42a7-b772-1b82e6d726cd", "processParameterId": "2055.9695b715-db44-410b-8a74-65cf1d31d8ab", "parameterMappingParentId": "3012.d41f1070-ab37-4bd4-b1ab-6efaaa935bcd", "useDefault": "false", "value": "tw.local.sqlStatements", "classRef": "/12.3990d8b4-983d-4250-bd06-3600f527fed0", "isList": "true", "isInput": "true", "guid": "2d63945c-c8a4-4968-b381-9e0044ae4e69", "versionId": "86c2149d-c44f-4c58-805c-f74f1e941b16", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.afb2adbf-a486-47b4-92ee-ea1e90a319a2", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.cae27f3a-4695-4a7a-af88-ae46437630ef", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:445", "versionId": "cddb0791-981b-4167-a339-81d995cb6385", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1215", "y": "188", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.cae27f3a-4695-4a7a-af88-ae46437630ef", "message": "", "faultStyle": "1", "guid": "69c29321-1dab-4f8c-81ef-b25d1559b55c", "versionId": "c211434e-eafc-4a4c-bae4-028d8379bd2e", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.dbe7793f-677a-4f7c-8d85-f92512d0f7ef", "processParameterId": "2055.e43c8f5f-9b0a-47a8-a9ec-129a4b663b3d", "parameterMappingParentId": "3007.cae27f3a-4695-4a7a-af88-ae46437630ef", "useDefault": "false", "value": "tw.local.error", "classRef": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isList": "false", "isInput": "false", "guid": "096bb7f2-9261-465c-bf4c-0b6b4de73976", "versionId": "931323a6-589f-4bcc-8f98-9e2afbf8431a", "description": {"isNull": "true"}}}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Parent Request 2", "id": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "cif", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.0f7d1cfb-8702-4df0-8644-697e79b3f609", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"06316421\"", "useDefault": "true"}}}, {"name": "requestNumber", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.0cf38a4f-9c47-4a73-b458-11e5f57b4b1e", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"00102230001092\"", "useDefault": "true"}}}, {"name": "parentIDCRequest", "itemSubjectRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isCollection": "false", "id": "2055.bd0b918c-9e61-486c-8c6b-693ec45ba6e4"}], "ns16:dataOutput": [{"name": "message", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a6bce394-8d1d-45a9-99b5-b0425e1b18d5"}, {"name": "isFound", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.59c91571-b2e5-4723-a942-1f7cae91b2ae"}, {"name": "parentIDCRequest", "itemSubjectRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isCollection": "false", "id": "2055.3ffa3f80-0b60-4c91-bb4a-6f98f6a45314"}, {"name": "error", "itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "id": "2055.ba16966b-40cc-4adb-8812-eb868ed6c2d5"}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.0f7d1cfb-8702-4df0-8644-697e79b3f609", "2055.0cf38a4f-9c47-4a73-b458-11e5f57b4b1e", "2055.bd0b918c-9e61-486c-8c6b-693ec45ba6e4"]}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.a6bce394-8d1d-45a9-99b5-b0425e1b18d5", "2055.59c91571-b2e5-4723-a942-1f7cae91b2ae", "2055.3ffa3f80-0b60-4c91-bb4a-6f98f6a45314", "2055.ba16966b-40cc-4adb-8812-eb868ed6c2d5"]}}, "ns16:laneSet": {"id": "32030609-c737-49ad-8466-63f03eefc2d4", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "c16f16bd-97c2-4830-8bae-7c239d365b46", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["edd9b058-5c4b-48ee-a346-e7f942be748d", "5207ac9e-0782-41c6-a330-928a00201e6b", "92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "8991738b-47d4-4337-9a34-0460d876d069", "b4fc285f-c557-4898-a65b-7f77c5c2dccc", "f6e2fe58-82f2-441f-8cc2-47b228d2129f", "443fea9d-1c61-4752-aa58-2ea557669e1e", "b1e91750-9d6e-4693-a7ac-21e3560c8a19", "0f6dbb10-94e7-4a99-9e6f-5daa82747e66", "53d9daa3-35f6-4f80-ad97-9a079deeb54a", "bc37b8b0-7e4f-40b6-ba7d-8689461524ab", "eafcf950-de5a-4d1b-b6c3-b5572bef7a06", "1fc22125-6456-43ee-8e25-27cdf80b1867", "8bbb867a-b9bf-48e4-adf0-3760811d4c5c", "8077e4c5-9212-45a9-a192-84c1c4834cf3", "afb2adbf-a486-47b4-92ee-ea1e90a319a2"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "edd9b058-5c4b-48ee-a346-e7f942be748d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.a9d4b515-ea87-44d2-bbf2-40285261984d"}, "ns16:endEvent": [{"name": "End", "id": "5207ac9e-0782-41c6-a330-928a00201e6b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1140", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:056f27db08707381:36b20ea0:18b6c1db471:447"}, "ns16:incoming": ["58602a5e-19e9-4ee1-b1ca-a9ee906b66de", "997ac1ac-bfc1-4f66-b051-dbf199d5c12d"]}, {"name": "End Event", "id": "afb2adbf-a486-47b4-92ee-ea1e90a319a2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1215", "y": "188", "width": "24", "height": "24"}}, "ns16:incoming": "a943ea9f-36c2-430e-b2c8-75e7af50597b", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, "ns16:errorEventDefinition": {"id": "807ccafc-bfd5-418b-99fb-9dc03b4a16b3", "eventImplId": "151a65b1-5476-423f-81ce-9d2144256b83", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "edd9b058-5c4b-48ee-a346-e7f942be748d", "targetRef": "92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "name": "To Init SQL", "id": "2027.a9d4b515-ea87-44d2-bbf2-40285261984d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "targetRef": "c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "name": "To SQL Execute Multiple Statements (SQLResult)", "id": "66e1ae39-7140-4649-8c18-18356c274ca5", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "targetRef": "8991738b-47d4-4337-9a34-0460d876d069", "name": "To check is found", "id": "98cdbfdb-ffe2-4bc7-8645-3b0cf8759465", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "8991738b-47d4-4337-9a34-0460d876d069", "targetRef": "b4fc285f-c557-4898-a65b-7f77c5c2dccc", "name": "To Exclusive Gateway", "id": "dcfc04dd-5b08-4301-bca4-52f72830ea59", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "b4fc285f-c557-4898-a65b-7f77c5c2dccc", "targetRef": "f6e2fe58-82f2-441f-8cc2-47b228d2129f", "name": "To Init SQL for sub", "id": "44f62ac2-c813-4bc5-9226-bc5c6ddd0063", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "f6e2fe58-82f2-441f-8cc2-47b228d2129f", "targetRef": "443fea9d-1c61-4752-aa58-2ea557669e1e", "name": "To End", "id": "2dbe567f-7a99-4fcc-862e-331fcdc57171", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "b4fc285f-c557-4898-a65b-7f77c5c2dccc", "targetRef": "5207ac9e-0782-41c6-a330-928a00201e6b", "name": "To End", "id": "58602a5e-19e9-4ee1-b1ca-a9ee906b66de", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "735", "y": "15"}, "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.isFound\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "443fea9d-1c61-4752-aa58-2ea557669e1e", "targetRef": "b1e91750-9d6e-4693-a7ac-21e3560c8a19", "name": "To Mapping", "id": "f691923e-3e78-4ac7-aef0-51de20038dbf", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "b1e91750-9d6e-4693-a7ac-21e3560c8a19", "targetRef": "5207ac9e-0782-41c6-a330-928a00201e6b", "name": "To End", "id": "997ac1ac-bfc1-4f66-b051-dbf199d5c12d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "0f6dbb10-94e7-4a99-9e6f-5daa82747e66", "targetRef": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "name": "To Catch Errors", "id": "18331fd6-20f0-4280-ba46-6d9c89fc624e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "53d9daa3-35f6-4f80-ad97-9a079deeb54a", "targetRef": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "name": "To Catch Errors", "id": "8b9f2095-917a-453b-b01e-9c4c3250ea4b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "bc37b8b0-7e4f-40b6-ba7d-8689461524ab", "targetRef": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "name": "To Catch Errors", "id": "5e0632ad-c4e1-401c-8622-06ec87eec235", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "eafcf950-de5a-4d1b-b6c3-b5572bef7a06", "targetRef": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "name": "To Catch Errors", "id": "53a96670-fb7d-4667-9df2-7a0ecdc67963", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "1fc22125-6456-43ee-8e25-27cdf80b1867", "targetRef": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "name": "To Catch Errors", "id": "2bdd73f4-71b2-424d-841c-14f06a0beac1", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "8bbb867a-b9bf-48e4-adf0-3760811d4c5c", "targetRef": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "name": "To Catch Errors", "id": "dfab9c0f-a4f3-4f97-b203-38e9dd3fa48d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "targetRef": "afb2adbf-a486-47b4-92ee-ea1e90a319a2", "name": "To End", "id": "a943ea9f-36c2-430e-b2c8-75e7af50597b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Init SQL", "id": "92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "138", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.a9d4b515-ea87-44d2-bbf2-40285261984d", "ns16:outgoing": "66e1ae39-7140-4649-8c18-18356c274ca5", "ns16:script": "tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar j = 0;\r\r\nvar i = 0;\r\r\nfunction paramInit (value) {\r\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\r\n\ti++;\r\r\n}\r\r\n//------------------------------------- Get Parent IDC -------------------------------------------- \r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT * FROM BPM.IDC_REQUEST_DETAILS d join BPM.IDC_CUSTOMER_INFORMATION c on d.ID = c.IDC_REQUEST_ID WHERE (d.REQUEST_STATE = 'Initial' or d.REQUEST_STATE = 'Final') AND d.REQUEST_STATUS = 'Completed' AND c.CIF = ? AND d.ID = (SELECT max(ID) FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER like ? AND REQUEST_STATUS != 'Terminated' AND REQUEST_STATUS != 'Canceled' ); \"\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.cif);\r\r\nparamInit(tw.local.requestNumber+\"%\");\r\r\n//------------------------------------- Check Type Of IDC -----------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT d.ID FROM BPM.IDC_REQUEST_DETAILS d join BPM.IDC_REQUEST_TYPE t on d.REQUEST_TYPE_ID = t.ID WHERE d.REQUEST_NUMBER = ? AND (t.ENGLISH_DESCRIPTION = 'IDC Execution' OR t.ENGLISH_DESCRIPTION = 'IDC Acknowledgement');\"\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.requestNumber);\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "check is found", "id": "8991738b-47d4-4337-9a34-0460d876d069", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "402", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "98cdbfdb-ffe2-4bc7-8645-3b0cf8759465", "ns16:outgoing": "dcfc04dd-5b08-4301-bca4-52f72830ea59", "ns16:script": "tw.local.parentIDCRequest = new tw.object.IDCRequest();\r\r\ntw.local.parentIDCRequest.appInfo = new tw.object.AppInfo();\r\r\ntw.local.parentIDCRequest.appInfo.branch = new tw.object.NameValuePair();\r\r\ntw.local.parentIDCRequest.IDCRequestNature =new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.IDCRequestType = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.importPurpose = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.paymentTerms = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.documentsSource = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productCategory = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails = new tw.object.FinancialDetails();\r\r\ntw.local.parentIDCRequest.financialDetails.documentCurrency =new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfFunds = new tw.object.DBLookup(); \r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.financialDetails.executionHub = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails = new tw.object.ProductsDetails();\r\r\ntw.local.parentIDCRequest.productsDetails.incoterms = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.productsDetails.HSProduct = new tw.object.DBLookup();\r\r\ntw.local.parentIDCRequest.countryOfOrigin = new tw.object.DBLookup();\r\r\n\r\r\nif (tw.local.results[0].rows.listLength == 0) {\r\r\n\ttw.local.message = \"This request number is not exist or another request editing on it\";\r\r\n\ttw.local.isFound = false;\r\r\n}else if(tw.local.results[1].rows.listLength == 0){\r\r\n\ttw.local.isFound = false;\r\r\n\ttw.local.message = \"This request number is not related to IDC Acknowledgement or IDC Execution request\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.isFound = true;\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "Init SQL for sub", "id": "f6e2fe58-82f2-441f-8cc2-47b228d2129f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "675", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "44f62ac2-c813-4bc5-9226-bc5c6ddd0063", "ns16:outgoing": "2dbe567f-7a99-4fcc-862e-331fcdc57171", "ns16:script": "tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar j = 0;\r\r\nvar i = 0;\r\r\nfunction paramInit (value) {\r\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\r\n\ti++;\r\r\n}\r\r\n//------------------------------------- Get invoice --------------------------------------------\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT  INVOICE_NUMBER, INVOICE_DATE FROM BPM.IDC_REQUEST_INVOICES WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get bills --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT BILL_OF_LADING_REF, BILL_OF_LADING_DATE FROM BPM.IDC_REQUEST_BILLS WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get customer --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT IMPORT_CARD_NUMBER FROM BPM.IDC_CUSTOMER_INFORMATION WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get multi tenor --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT INSTALLMENT_DATE, INSTALLMENT_AMOUNT FROM BPM.IDC_REQUEST_MULTI_TENOR WHERE IDC_REQUEST_ID = ?;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.results[0].rows[0].data[0]);\r\r\n//------------------------------------- Get advance payment --------------------------------------------\r\r\nj++;\r\r\ni=0;\r\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\r\ntw.local.sqlStatements[j].sql=\"SELECT d.ID,d.REQUEST_NUMBER,BENEFICIARY_NAME, CURRENCY, DOCUMENT_AMOUNT, AMOUNT_PAYABLE_BY_NBE, ADVANCE_PAYMENT_OUTSTANDING_AMOUNT,i.INVOICE_NUMBER,a.AMOUNT_ALLOCATED_FOR_REQUEST, a.ALLOCATED_AMOUNT_IN_REQUEST_CURRENCY FROM BPM.IDC_REQUEST_DETAILS d join BPM.IDC_REQUEST_INVOICES i on i.IDC_REQUEST_ID = d.ID  join BPM.IDC_ADVANCED_PAYMENT_USED a on a.IDC_REQUEST_ID = d.ID where a.REFERRAL_REQUEST_NUMBER= ? ORDER BY d.ID;\";\r\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\r\nparamInit(tw.local.requestNumber);"}, {"scriptFormat": "text/x-javascript", "name": "Mapping", "id": "b1e91750-9d6e-4693-a7ac-21e3560c8a19", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "997", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "f691923e-3e78-4ac7-aef0-51de20038dbf", "ns16:outgoing": "997ac1ac-bfc1-4f66-b051-dbf199d5c12d", "ns16:script": "\r\r\nvar i=0-1;\r\r\nfunction getnext () {\r\r\n\ti++;\r\r\n\treturn tw.local.results[0].rows[0].data[i];\r\r\n}\r\r\n\r\r\ntw.local.parentIDCRequest.DBID = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.appInfo.instanceID = getnext ();\r\r\ntw.local.parentIDCRequest.ParentIDCRequestNumber =getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.appID = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.appInfo.initiator = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.appInfo.branch.name = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.IDCRequestNature.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.IDCRequestType.id = getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.requestDate = getnext().format(\"dd/MM/yyyy\"); \r\r\ntw.local.parentIDCRequest.IDCRequestState = getnext ();\r\r\ntw.local.parentIDCRequest.IDCRequestStage = getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.status = getnext ();\r\r\ntw.local.parentIDCRequest.appInfo.subStatus = getnext ();\r\r\ntw.local.parentIDCRequest.FCContractNumber = getnext ();\r\r\ntw.local.parentIDCRequest.isIDCWithdrawn = Boolean(getnext ());\r\r\n\r\r\ntw.local.parentIDCRequest.importPurpose.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.paymentTerms.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.documentsSource.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productCategory.id = getnext ();\r\r\ntw.local.parentIDCRequest.commodityDescription = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.amtPayableByNBE = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.documentAmount= getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.documentCurrency.code= getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.chargesAccount = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfForeignCurrency.id= getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.sourceOfFunds.id = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.paymentAccount = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.cashAmtInDocCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.CashAmtWithNoCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.facilityAmtInDocCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.facilityAmtWithNoCurrency = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.discountAmt = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amountAdvanced = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtPaidbyOtherBanks = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtSight = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtDeferredNoAvalized = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtDeferredAvalized = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.amtPayableByNBE = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.firstInstallementMaturityDate = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.daysTillMaturity = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.name = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.bank = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.account = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.country.code = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.beneficiaryDetails.correspondentRefNum = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.tradeFOReferenceNumber = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.tradeFinanceApprovalNumber = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.financialDetails.executionHub.code = getnext ();\r\r\ntw.local.parentIDCRequest.financialDetails.executionHub.arabicdescription = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.shippingDate = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.incoterms.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.shipmentMethod.id = getnext ();\r\r\ntw.local.parentIDCRequest.productsDetails.destinationPort = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.CBECommodityClassification.id = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.productsDetails.HSProduct.code = getnext ();\r\r\ntw.local.parentIDCRequest.productsDetails.HSProduct.englishdescription = getnext ();\r\r\ntw.local.parentIDCRequest.productsDetails.ACID = getnext ();\r\r\n\r\r\ntw.local.parentIDCRequest.countryOfOrigin.code = getnext ();\r\r\ngetnext ();\r\r\ngetnext ();\r\r\ngetnext ();\r\r\ntw.local.parentIDCRequest.releaseAmount = getnext ();\r\r\n//------------------------------------- invoice ---------------------------------------\r\r\ntw.local.parentIDCRequest.invoices = new tw.object.listOf.Invoice();\r\r\nfor (var i=0; i<tw.local.subResult[0].rows.listLength; i++) {\r\r\n\ttw.local.parentIDCRequest.invoices[i] = new tw.object.Invoice();\r\r\n\ttw.local.parentIDCRequest.invoices[i].number = tw.local.subResult[0].rows[i].data[0];\r\r\n\ttw.local.parentIDCRequest.invoices[i].date = tw.local.subResult[0].rows[i].data[1];\r\r\n}\r\r\n//------------------------------------- bill ---------------------------------------\r\r\ntw.local.parentIDCRequest.billOfLading = new tw.object.listOf.Invoice();\r\r\nfor (var i=0; i<tw.local.subResult[1].rows.listLength; i++) {\r\r\n\ttw.local.parentIDCRequest.billOfLading[i] = new tw.object.Invoice();\r\r\n\ttw.local.parentIDCRequest.billOfLading[i].number = tw.local.subResult[1].rows[i].data[0];\r\r\n\ttw.local.parentIDCRequest.billOfLading[i].date = tw.local.subResult[1].rows[i].data[1];\r\r\n}\r\r\n//---------------------------------------- customer ----------------------------\r\r\ntw.local.parentIDCRequest.customerInformation = new tw.object.CustomerInformation();\r\r\ntw.local.parentIDCRequest.customerInformation.importCardNumber =  tw.local.subResult[2].rows[0].data[0];\r\r\n//--------------------------------------- multi tenor ------------------------------\r\r\ntw.local.parentIDCRequest.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\r\nfor (var i=0; i<tw.local.subResult[3].rows.listLength; i++) {\r\r\n\ttw.local.parentIDCRequest.financialDetails.paymentTerms[i] = new tw.object.PaymentTerm();\r\r\n\ttw.local.parentIDCRequest.financialDetails.paymentTerms[i].installmentDate = tw.local.subResult[3].rows[i].data[0];\r\r\n\ttw.local.parentIDCRequest.financialDetails.paymentTerms[i].installmentAmount = tw.local.subResult[3].rows[i].data[1];\r\r\n}\r\r\n//---------------------------------------advancce payment ------------------------------------------------\r\r\ntw.local.parentIDCRequest.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\r\nvar apID = 0;\r\r\nvar n = 0;\r\r\nif (tw.local.subResult[4].rows.listLength == 0) {\r\r\n\ttw.local.parentIDCRequest.financialDetails.isAdvancePaymentsUsed = false;\r\r\n}else{\r\r\n\tvar apID = tw.local.subResult[4].rows[0].data[0];\r\r\n\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\r\r\n\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[0].invoiceNumber =\"\";\r\r\n\ttw.local.parentIDCRequest.financialDetails.isAdvancePaymentsUsed = true;\r\r\n\tfor (var i=0; i<tw.local.subResult[4].rows.listLength; i++) {\r\r\n\t\tif (apID!= tw.local.subResult[4].rows[i].data[0]) {\r\r\n\t\t\t\r\r\n\t\t\tapID = tw.local.subResult[4].rows[i].data[0];\r\r\n\t\t\tn++;\r\r\n\t\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n] = new tw.object.UsedAdvancePayment();\r\r\n\t\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceNumber =\"\";\r\r\n\t\t}\r\r\n\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].DBID = tw.local.subResult[4].rows[i].data[0];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].advancePaymentRequestNumber = tw.local.subResult[4].rows[i].data[1];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].beneficiaryName = tw.local.subResult[4].rows[i].data[2];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceCurrency  = new tw.object.DBLookup();\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceCurrency.code = tw.local.subResult[4].rows[i].data[3];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].documentAmount = tw.local.subResult[4].rows[i].data[4];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].paidAmount = tw.local.subResult[4].rows[i].data[5];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].outstandingAmount = tw.local.subResult[4].rows[i].data[6];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].invoiceNumber += tw.local.subResult[4].rows[i].data[7]+\"\\n\";\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].AmountAllocated = tw.local.subResult[4].rows[i].data[8];\r\r\n\t\ttw.local.parentIDCRequest.financialDetails.usedAdvancePayment[n].AllocatedAmountinRequestCurrency = tw.local.subResult[4].rows[i].data[9];\r\r\n\r\r\n\t}\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "Catch Errors", "id": "8077e4c5-9212-45a9-a192-84c1c4834cf3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1107", "y": "254", "width": "95", "height": "70", "color": "#FF7782"}}, "ns16:incoming": ["18331fd6-20f0-4280-ba46-6d9c89fc624e", "8b9f2095-917a-453b-b01e-9c4c3250ea4b", "5e0632ad-c4e1-401c-8622-06ec87eec235", "53a96670-fb7d-4667-9df2-7a0ecdc67963", "2bdd73f4-71b2-424d-841c-14f06a0beac1", "dfab9c0f-a4f3-4f97-b203-38e9dd3fa48d"], "ns16:outgoing": "a943ea9f-36c2-430e-b2c8-75e7af50597b", "ns16:script": "if (tw.local.error != undefined && !!tw.local.error.errorText) {\r\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\r\n}else if (tw.system.error != undefined && tw.system.error != null) {\r\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}else{\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}\r\r\n\r\r\ntw.local.error.errorText = \"Error Occured<br> Service Name : \"+tw.system.serviceFlow.name+\"<br> Error Message : \"+tw.local.errorMSG;"}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0", "isCollection": "true", "name": "sqlStatements", "id": "2056.8016fb98-bc55-4958-ba55-b215e17ca401"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.5aeb7335-d06b-42d4-ac71-3dc8d2b9202c", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "subResult", "id": "2056.86926535-8e9f-48c3-9df8-6a9b2be3080e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMSG", "id": "2056.b689a13f-1e96-4996-b241-141c30ff972d"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorCode", "id": "2056.2f573e78-dd3e-43bf-b64e-ba2c26666e94"}], "ns16:callActivity": [{"calledElement": "1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7", "name": "SQL Execute Multiple Statements (SQLResult)", "id": "c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "265", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "66e1ae39-7140-4649-8c18-18356c274ca5", "ns16:outgoing": "98cdbfdb-ffe2-4bc7-8645-3b0cf8759465", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.9695b715-db44-410b-8a74-65cf1d31d8ab", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlStatements", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0"}}}, {"ns16:targetRef": "2055.a634f531-c979-476c-91db-a17bf5e55c90", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7", "name": "sub SQL Execute Multiple Statements (SQLResult)", "id": "443fea9d-1c61-4752-aa58-2ea557669e1e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "846", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "2dbe567f-7a99-4fcc-862e-331fcdc57171", "ns16:outgoing": "f691923e-3e78-4ac7-aef0-51de20038dbf", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.9695b715-db44-410b-8a74-65cf1d31d8ab", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlStatements", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0"}}}, {"ns16:targetRef": "2055.a634f531-c979-476c-91db-a17bf5e55c90", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e", "ns16:assignment": {"ns16:to": {"_": "tw.local.subResult", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}], "ns16:exclusiveGateway": {"default": "44f62ac2-c813-4bc5-9226-bc5c6ddd0063", "name": "Exclusive Gateway", "id": "b4fc285f-c557-4898-a65b-7f77c5c2dccc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "545", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "dcfc04dd-5b08-4301-bca4-52f72830ea59", "ns16:outgoing": ["44f62ac2-c813-4bc5-9226-bc5c6ddd0063", "58602a5e-19e9-4ee1-b1ca-a9ee906b66de"]}, "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "parallelMultiple": "false", "name": "Error", "id": "0f6dbb10-94e7-4a99-9e6f-5daa82747e66", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "173", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "18331fd6-20f0-4280-ba46-6d9c89fc624e", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "26c7741d-529c-450e-868a-faf6cc5803fe"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "19dcd6b9-c84a-4778-8c22-4caa4cbc0a5b", "eventImplId": "0f8a3263-1078-4116-8157-3dbdb45d12ca", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "parallelMultiple": "false", "name": "Error1", "id": "53d9daa3-35f6-4f80-ad97-9a079deeb54a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "300", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "8b9f2095-917a-453b-b01e-9c4c3250ea4b", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "2e84b0e4-da76-4fdb-8792-4f4e6326233d"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "5e3b5c02-66fc-4df8-b240-1140d6815980", "eventImplId": "c0bb68d5-e449-4221-8780-3e06edd5c85c", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "8991738b-47d4-4337-9a34-0460d876d069", "parallelMultiple": "false", "name": "Error2", "id": "bc37b8b0-7e4f-40b6-ba7d-8689461524ab", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "411", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "5e0632ad-c4e1-401c-8622-06ec87eec235", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "13b75b1c-5188-4cc7-bab0-6f9924f6d140"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "1f5e5c75-5b73-4f63-bc43-b4e315395645", "eventImplId": "b8b15213-ab4d-429d-8154-f3a5bab2efa1", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "f6e2fe58-82f2-441f-8cc2-47b228d2129f", "parallelMultiple": "false", "name": "Error3", "id": "eafcf950-de5a-4d1b-b6c3-b5572bef7a06", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "710", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "53a96670-fb7d-4667-9df2-7a0ecdc67963", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "ab5df7dc-4291-450a-991a-56cd57cb1b89"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "2d8a93be-0890-478c-a744-6e2156a94920", "eventImplId": "6ef42686-6b6b-4830-888b-0c5a17a536a8", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "443fea9d-1c61-4752-aa58-2ea557669e1e", "parallelMultiple": "false", "name": "Error4", "id": "1fc22125-6456-43ee-8e25-27cdf80b1867", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "881", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "2bdd73f4-71b2-424d-841c-14f06a0beac1", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "544485ef-4e7a-40a7-b1e8-71a056c32bed"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "23eb9030-9583-4e31-929a-81efc694ae10", "eventImplId": "e9eb9290-cde8-4789-8e89-087599177494", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "b1e91750-9d6e-4693-a7ac-21e3560c8a19", "parallelMultiple": "false", "name": "Error5", "id": "8bbb867a-b9bf-48e4-adf0-3760811d4c5c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1032", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "dfab9c0f-a4f3-4f97-b203-38e9dd3fa48d", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "45ceb9a2-be8a-4c61-a028-4ac0525ae50d"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "31bbd7a7-c2bb-45f1-90df-421105d9793d", "eventImplId": "0329761b-1151-4a51-8a48-65d0161e73fa", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To check is found", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.52e0b77a-c4a8-471b-b489-2cbfa7bdd872", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "2025.c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e"], "endStateId": "guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8", "toProcessItemId": ["2025.8991738b-47d4-4337-9a34-0460d876d069", "2025.8991738b-47d4-4337-9a34-0460d876d069"], "guid": "d309baca-7cbb-4b01-b803-3794f7b2ab2e", "versionId": "11bd095e-e2aa-4d32-8687-51a117a1215b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.5b2ba9d4-1139-4271-b315-2ba03b906870", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8077e4c5-9212-45a9-a192-84c1c4834cf3", "2025.8077e4c5-9212-45a9-a192-84c1c4834cf3"], "endStateId": "Out", "toProcessItemId": ["2025.afb2adbf-a486-47b4-92ee-ea1e90a319a2", "2025.afb2adbf-a486-47b4-92ee-ea1e90a319a2"], "guid": "0ad25478-d2ac-41cf-b9ca-0d33d84fe8a3", "versionId": "325b9bc2-7d94-4e3b-b2ce-926ffbe81541", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ff1fdbcd-a85b-4d44-99de-f1eaaba1ecde", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b4fc285f-c557-4898-a65b-7f77c5c2dccc", "2025.b4fc285f-c557-4898-a65b-7f77c5c2dccc"], "endStateId": "guid:aff098473ecd546d:1d42df0a:18b37664be6:-6ffc", "toProcessItemId": ["2025.5207ac9e-0782-41c6-a330-928a00201e6b", "2025.5207ac9e-0782-41c6-a330-928a00201e6b"], "guid": "5c7ae85a-7bf3-4d29-a57a-972e850aca76", "versionId": "3f287e4d-2f3b-40c3-ab74-a80c0b071a33", "layoutData": {"controlPoints": {"controlPoint": {"x": "735", "y": "15"}}, "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To Init SQL for sub", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.32a14fef-40d1-43b0-8928-c185746b032c", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b4fc285f-c557-4898-a65b-7f77c5c2dccc", "2025.b4fc285f-c557-4898-a65b-7f77c5c2dccc"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.f6e2fe58-82f2-441f-8cc2-47b228d2129f", "2025.f6e2fe58-82f2-441f-8cc2-47b228d2129f"], "guid": "f121eb5a-ce70-4fc0-be71-7a0bc80d4c93", "versionId": "5f5a3a1d-c74f-4fe3-9b8c-244d23d438fb", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7c4567a7-6430-420e-959e-fe3c8376110a", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f6e2fe58-82f2-441f-8cc2-47b228d2129f", "2025.f6e2fe58-82f2-441f-8cc2-47b228d2129f"], "endStateId": "Out", "toProcessItemId": ["2025.443fea9d-1c61-4752-aa58-2ea557669e1e", "2025.443fea9d-1c61-4752-aa58-2ea557669e1e"], "guid": "6a888535-5306-4b5d-bf7a-197cfb0a3dc1", "versionId": "7c34fa7c-89db-4c42-8a0a-39fb88d9f980", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1a1c8325-9097-406f-9493-28bebe02509d", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b1e91750-9d6e-4693-a7ac-21e3560c8a19", "2025.b1e91750-9d6e-4693-a7ac-21e3560c8a19"], "endStateId": "Out", "toProcessItemId": ["2025.5207ac9e-0782-41c6-a330-928a00201e6b", "2025.5207ac9e-0782-41c6-a330-928a00201e6b"], "guid": "8b38b30d-eba7-443f-a585-28da8cbffb71", "versionId": "a14ea376-9c5f-42cd-8022-45b19db4f611", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To SQL Execute Multiple Statements (SQLResult)", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.2a2fac16-24ca-43d8-9f5a-e431419f017c", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.92275c3d-a15d-4b6c-bffc-0a7bb4a2943c", "2025.92275c3d-a15d-4b6c-bffc-0a7bb4a2943c"], "endStateId": "Out", "toProcessItemId": ["2025.c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e", "2025.c8dadda1-ecf0-4d19-8fc6-ec44d0a0bd7e"], "guid": "80f63181-7fff-43ac-b666-b627cb1b328b", "versionId": "b08f493b-b255-42a4-92af-6bdcc4802dad", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Mapping", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.827cce7e-adc5-4652-9fea-31bfcb4a9cec", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.443fea9d-1c61-4752-aa58-2ea557669e1e", "2025.443fea9d-1c61-4752-aa58-2ea557669e1e"], "endStateId": "guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8", "toProcessItemId": ["2025.b1e91750-9d6e-4693-a7ac-21e3560c8a19", "2025.b1e91750-9d6e-4693-a7ac-21e3560c8a19"], "guid": "34240c98-d34d-4a84-9649-9bfd7f5136ce", "versionId": "e6b89e4b-b6d1-4d2e-82bb-b85b32982a7e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Exclusive Gateway", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.916df9e0-cb83-466b-84ac-f1345839a1f1", "processId": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8991738b-47d4-4337-9a34-0460d876d069", "2025.8991738b-47d4-4337-9a34-0460d876d069"], "endStateId": "Out", "toProcessItemId": ["2025.b4fc285f-c557-4898-a65b-7f77c5c2dccc", "2025.b4fc285f-c557-4898-a65b-7f77c5c2dccc"], "guid": "dd408c81-44c4-4d55-b303-a73a40ef72c6", "versionId": "ebe08eb5-293a-4a08-96ed-5757855c9fc8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}