{"id": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "versionId": "51456dd2-66ca-4a40-8805-14a62712aefd", "name": "Financial Details Branch", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetails", "configOptions": ["financialDetailsCVVis", "currencyDocAmountVIS", "today"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.FCVIS= function()\r\r\n{\r\r\n\tif(this.context.binding.get(\"value\").get(\"amountAdvanced\") > 0)\r\r\n\t\tthis.ui.get(\"fcPanel\").setVisible(true);\r\r\n\telse\r\r\n\t\tthis.ui.get(\"fcPanel\").setVisible(false,true);\r\r\n\r\r\n}"}]}, "hasDetails": true}