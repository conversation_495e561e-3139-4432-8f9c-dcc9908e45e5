<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871" name="Ajax Get ODC Product codes">
        <lastModified>1692010854187</lastModified>
        <lastModifiedBy>abdel<PERSON>man.saleh</lastModifiedBy>
        <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.fe42a9aa-d5e3-4c16-8b5d-196183d8479a</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-7372</guid>
        <versionId>62f7d590-81c1-43b2-8adf-64124adb1f3e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-703f" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.9621d480-1397-4f97-839d-134ccd41edfa"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"263d7105-a4e8-4c90-8ccd-8e1ca26ff914"},{"incoming":["75b81a0c-ea9a-4770-8aa7-b8ab2fef541f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-7370"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"175a5e73-7277-498e-8829-0dfa4e142348"},{"targetRef":"fe42a9aa-d5e3-4c16-8b5d-196183d8479a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get product codes","declaredType":"sequenceFlow","id":"2027.9621d480-1397-4f97-839d-134ccd41edfa","sourceRef":"263d7105-a4e8-4c90-8ccd-8e1ca26ff914"},{"startQuantity":1,"outgoing":["5695209c-772d-4344-8c0d-fbdb170d0358"],"incoming":["2027.9621d480-1397-4f97-839d-134ccd41edfa"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":152,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get product codes","dataInputAssociation":[{"targetRef":"2055.d98c668e-db67-401b-8e84-cc98608569ea","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.658c7ba0-e16a-4a1e-8bc6-18be5f3d12cb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BECO\""]}}]},{"targetRef":"2055.a1145555-7f2d-469e-89ad-87ef52093009","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.33fcb7f5-8fa1-4418-8211-679c99edc4c8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.97f65893-b0d8-4cd1-8557-a3e44250b84f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.1019293e-e09f-4c51-84c4-e3e8aa04849c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.409793fb-5b0c-4dc8-8d73-474de2f1d809","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC Creation \/ Amendment Process Details\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"fe42a9aa-d5e3-4c16-8b5d-196183d8479a","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.16a42fb6-68f4-443b-836a-521a420d5cb3"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.b78f8c49-b985-497c-8134-903ee8801680"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.4ce44c6e-a0df-4c3c-8dbf-0363fd871401"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.033d5d03-1e28-4166-8adf-83166aef740a","declaredType":"TFormalExpression","content":["tw.local.productList"]}}],"sourceRef":["2055.7cbc47bc-3460-4dab-84af-9838e8e29b10"]}],"calledElement":"1.757041b2-81d0-44c8-9d96-c2bbdf22a853"},{"targetRef":"da11a9b9-28cf-4189-84e9-868a271cdf7e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6199"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Map output","declaredType":"sequenceFlow","id":"5695209c-772d-4344-8c0d-fbdb170d0358","sourceRef":"fe42a9aa-d5e3-4c16-8b5d-196183d8479a"},{"startQuantity":1,"outgoing":["75b81a0c-ea9a-4770-8aa7-b8ab2fef541f"],"incoming":["5695209c-772d-4344-8c0d-fbdb170d0358"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":398,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"da11a9b9-28cf-4189-84e9-868a271cdf7e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = new tw.object.listOf.NameValuePair();\r\nif(tw.local.productList != null)\r\n{\r\n\tfor(var i=0;i&lt;tw.local.productList.listLength;i++)\r\n\t{\r\n\t\ttw.local.results[i] = new tw.object.NameValuePair();\r\n\t\ttw.local.results[i].name = tw.local.productList[i].arabicName;\r\n\t\ttw.local.results[i].value = tw.local.productList[i].productCode;\r\n\t}\r\n\t\r\n}"]}},{"targetRef":"175a5e73-7277-498e-8829-0dfa4e142348","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"75b81a0c-ea9a-4770-8aa7-b8ab2fef541f","sourceRef":"da11a9b9-28cf-4189-84e9-868a271cdf7e"},{"itemSubjectRef":"itm.12.033d5d03-1e28-4166-8adf-83166aef740a","name":"productList","isCollection":true,"declaredType":"dataObject","id":"2056.2c388dda-5822-4f75-8a22-e99c380353b4"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.2375cae6-2048-4ac3-826c-c1e2aaf058f1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.ca88cac7-dde4-48f5-8296-cc4ba466b696"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.7693206c-e4e7-48b3-8420-26cc10761201"},{"parallelMultiple":false,"outgoing":["b934c08d-fecc-4f06-8d87-71545777a088"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5df69263-d912-41b9-86ae-c8a4f0776fff"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0613ba04-9330-48b3-8a09-f3fe48848163","otherAttributes":{"eventImplId":"4d91ccde-1533-4620-806a-14f148139cfa"}}],"attachedToRef":"fe42a9aa-d5e3-4c16-8b5d-196183d8479a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":187,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"64fea019-b4d9-4530-846e-0fc85803adb1","outputSet":{}},{"incoming":["b934c08d-fecc-4f06-8d87-71545777a088","fdd22a9b-d4e4-4bd7-8088-d8d38bad023f"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c1ab06ea-a63e-4da4-811a-348d4496d787","otherAttributes":{"eventImplId":"df801d74-deec-468b-853f-5548ac8e2bae"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":281,"y":194,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}]}],"declaredType":"endEvent","id":"c43f319b-c907-4143-84be-39532540a091"},{"targetRef":"c43f319b-c907-4143-84be-39532540a091","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"b934c08d-fecc-4f06-8d87-71545777a088","sourceRef":"64fea019-b4d9-4530-846e-0fc85803adb1"},{"parallelMultiple":false,"outgoing":["fdd22a9b-d4e4-4bd7-8088-d8d38bad023f"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"9e419490-741d-4992-8807-97649d88e163"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e54d1ebf-1121-4546-8da5-c5f399613d0b","otherAttributes":{"eventImplId":"72802dcc-8f51-46c9-8290-643ec258b15f"}}],"attachedToRef":"da11a9b9-28cf-4189-84e9-868a271cdf7e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":433,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"475f4acf-af54-4a54-852d-bc3a23634cf2","outputSet":{}},{"targetRef":"c43f319b-c907-4143-84be-39532540a091","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"fdd22a9b-d4e4-4bd7-8088-d8d38bad023f","sourceRef":"475f4acf-af54-4a54-852d-bc3a23634cf2"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.b709cd85-1811-4c7c-8089-075f5abb4084"}],"laneSet":[{"id":"55a179cd-4368-4a2f-8416-4e2588356f9e","lane":[{"flowNodeRef":["263d7105-a4e8-4c90-8ccd-8e1ca26ff914","175a5e73-7277-498e-8829-0dfa4e142348","fe42a9aa-d5e3-4c16-8b5d-196183d8479a","da11a9b9-28cf-4189-84e9-868a271cdf7e","64fea019-b4d9-4530-846e-0fc85803adb1","c43f319b-c907-4143-84be-39532540a091","475f4acf-af54-4a54-852d-bc3a23634cf2"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"ef84d6c7-bbfb-47b8-8e66-2a74cac6a83e","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Ajax Get ODC Product codes","declaredType":"process","id":"1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.6f08b649-1759-4389-830f-3d1675047322"}],"inputSet":[{"dataInputRefs":["2055.9f353756-4a20-402b-87ae-15459d892d40"]}],"outputSet":[{"dataOutputRefs":["2055.6f08b649-1759-4389-830f-3d1675047322"]}],"dataInput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.9f353756-4a20-402b-87ae-15459d892d40"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9f353756-4a20-402b-87ae-15459d892d40</processParameterId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f226d9cb-e6d9-4075-b220-8de4a3c3ed89</guid>
            <versionId>b16f9983-f8e1-49d6-911c-86cd7ec1e119</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6f08b649-1759-4389-830f-3d1675047322</processParameterId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7d3814c8-d079-4439-98d9-4a3f2e1fa13c</guid>
            <versionId>f14493e4-ad49-42d1-8fcb-480919a2085b</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.354474d0-97f5-4d12-8d7b-15bba04456e0</processParameterId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bb888a91-a446-4f79-8d0d-0b564d01ed1b</guid>
            <versionId>d1aa324b-011f-4124-8ae7-f063b49f30c6</versionId>
        </processParameter>
        <processVariable name="productList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2c388dda-5822-4f75-8a22-e99c380353b4</processVariableId>
            <description isNull="true" />
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.033d5d03-1e28-4166-8adf-83166aef740a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>582c1047-2038-4436-ad4e-1b100bb85f84</guid>
            <versionId>b90a5dd8-17b9-442a-acd2-17ad924ce1be</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2375cae6-2048-4ac3-826c-c1e2aaf058f1</processVariableId>
            <description isNull="true" />
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>101a6c36-7808-46e3-bec7-839e319f9c36</guid>
            <versionId>f07502e9-56e0-4087-957e-de114c9f2053</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ca88cac7-dde4-48f5-8296-cc4ba466b696</processVariableId>
            <description isNull="true" />
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>69cfdec9-9e3b-470a-88b7-f85a1abafa62</guid>
            <versionId>2d8614c6-4311-4733-90b7-89dcdab2ee2f</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7693206c-e4e7-48b3-8420-26cc10761201</processVariableId>
            <description isNull="true" />
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d8829e73-86bb-485f-9103-706cba160263</guid>
            <versionId>f68dca00-03ac-4d57-9461-0d10c98e8bfb</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b709cd85-1811-4c7c-8089-075f5abb4084</processVariableId>
            <description isNull="true" />
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>66c62307-8202-42e4-9b87-4af0b4bb411f</guid>
            <versionId>927bb980-78d3-4a68-aa5f-6277f5da885c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.175a5e73-7277-498e-8829-0dfa4e142348</processItemId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c9172e21-447d-4019-8ab6-6b3b5e7a76ae</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-7370</guid>
            <versionId>20f24d43-a2e1-4dbb-878b-e72c630737bc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c9172e21-447d-4019-8ab6-6b3b5e7a76ae</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>464a6190-e192-4317-9017-28b0666409a6</guid>
                <versionId>31799409-d69f-4464-9d2e-30700f8152f9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fe42a9aa-d5e3-4c16-8b5d-196183d8479a</processItemId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <name>Get product codes</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.c43f319b-c907-4143-84be-39532540a091</errorHandlerItemId>
            <guid>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-735b</guid>
            <versionId>40625e09-630b-4cf8-9d41-30dc3c96e7b5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="152" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-72ca</errorHandlerItem>
                <errorHandlerItemId>2025.c43f319b-c907-4143-84be-39532540a091</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.757041b2-81d0-44c8-9d96-c2bbdf22a853</attachedProcessRef>
                <guid>e31c03ed-4674-42a1-9dcd-0ed341f35bc7</guid>
                <versionId>1956c500-586c-4147-873c-066bd7a4f1a9</versionId>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.528f2124-f56d-4ccb-a2bb-bd7ec019400d</parameterMappingId>
                    <processParameterId>2055.33fcb7f5-8fa1-4418-8211-679c99edc4c8</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>50d04ee1-780c-40b9-a799-e93249cd6799</guid>
                    <versionId>1c0487a5-3c62-480f-8d29-aa3d82eab661</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.27915728-09cc-426c-b439-f4b250de8a11</parameterMappingId>
                    <processParameterId>2055.7cbc47bc-3460-4dab-84af-9838e8e29b10</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.productList</value>
                    <classRef>/12.033d5d03-1e28-4166-8adf-83166aef740a</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>2710bdf0-0999-48dc-90c2-6adc76bad773</guid>
                    <versionId>5b382d91-0197-400f-b21b-840c450a80b7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.eb0b0926-4a96-4e2d-8c32-45fc8a3a504f</parameterMappingId>
                    <processParameterId>2055.409793fb-5b0c-4dc8-8d73-474de2f1d809</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC Creation / Amendment Process Details"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d1b55909-c854-487d-b4e0-0dff3893a70e</guid>
                    <versionId>6c7ff111-d5e6-42fd-97c6-10ecd234e46d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f166a46a-3cf5-4e58-973c-f41fc0235b9e</parameterMappingId>
                    <processParameterId>2055.a1145555-7f2d-469e-89ad-87ef52093009</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5a743248-d6a4-4b03-81a1-75c63d868f67</guid>
                    <versionId>78e2aafe-3af2-496b-94f4-21019e168b08</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productGroup">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.90c263a1-055e-4a2c-86a7-99f4a29e0c3a</parameterMappingId>
                    <processParameterId>2055.658c7ba0-e16a-4a1e-8bc6-18be5f3d12cb</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"BECO"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a5d4f925-0551-477b-9c6f-e17526846619</guid>
                    <versionId>87aea1f2-12ab-4da3-8e5f-1a6db89ea2b0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fe82c3e3-7aac-4d7b-a913-9033a9be600d</parameterMappingId>
                    <processParameterId>2055.97f65893-b0d8-4cd1-8557-a3e44250b84f</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>47df8b30-3e58-44f8-bee1-d41d898b924a</guid>
                    <versionId>8c2841f4-dfa1-403f-a838-2d575fbe5d04</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d2028fb3-4197-4297-ab56-5bf32b48a4f0</parameterMappingId>
                    <processParameterId>2055.b78f8c49-b985-497c-8134-903ee8801680</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>14bcf56b-cea0-4c33-b4d2-2648f3f25de2</guid>
                    <versionId>a38348c8-3ed2-4a93-9409-7f5b670e2740</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.af04268d-c66d-4dd4-b745-f5f0947ab01f</parameterMappingId>
                    <processParameterId>2055.d98c668e-db67-401b-8e84-cc98608569ea</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>eb0739bf-3e17-4f9c-af35-0c89e1387c63</guid>
                    <versionId>a6e2969c-5f0a-40a9-8c76-80ab3a0f9607</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8411f185-5b87-4f77-a830-eb1a0454126c</parameterMappingId>
                    <processParameterId>2055.1019293e-e09f-4c51-84c4-e3e8aa04849c</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>894c2e0f-4094-40ff-9f46-b9dcfd205f02</guid>
                    <versionId>bdcd7f63-105e-4f5d-a999-4556f5997114</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2c305cc7-dd90-4f77-99df-f7bc2d84723e</parameterMappingId>
                    <processParameterId>2055.16a42fb6-68f4-443b-836a-521a420d5cb3</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>78bff052-809e-4d39-ab97-01e98815bbc1</guid>
                    <versionId>cd31ba0a-359e-4e45-9706-ac3b28f3e23a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7f96b60e-eeb8-4e42-ad7e-8e12ba0cfae6</parameterMappingId>
                    <processParameterId>2055.4ce44c6e-a0df-4c3c-8dbf-0363fd871401</processParameterId>
                    <parameterMappingParentId>3012.00c8728b-bd80-4c7a-841a-8c5089ebfd9c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c117a061-265a-41cb-ad56-adec9960af9a</guid>
                    <versionId>de01432c-dbe0-4819-9793-50bdf2724f9e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c43f319b-c907-4143-84be-39532540a091</processItemId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.8bf89162-dc43-4f00-a0b5-91c9d9573089</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-72ca</guid>
            <versionId>73a41034-d3cd-4cbb-b43e-b6ec339034e7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="281" y="194">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.8bf89162-dc43-4f00-a0b5-91c9d9573089</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>5a641403-b7b6-427c-9d25-e38f8624d69d</guid>
                <versionId>29a3af6c-d45c-4fcc-a698-8693b9028377</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3ffe5dc4-7fc5-4143-889f-977ed59ae9af</parameterMappingId>
                    <processParameterId>2055.354474d0-97f5-4d12-8d7b-15bba04456e0</processParameterId>
                    <parameterMappingParentId>3007.8bf89162-dc43-4f00-a0b5-91c9d9573089</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5f59847c-cda8-4403-8d3f-c36e8bfe2afa</guid>
                    <versionId>24b2a382-df4f-45cc-918d-c369adf0747b</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.da11a9b9-28cf-4189-84e9-868a271cdf7e</processItemId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.816b6a8b-86e9-4b8a-a02c-0d315c6507a7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.c43f319b-c907-4143-84be-39532540a091</errorHandlerItemId>
            <guid>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-735a</guid>
            <versionId>de0fc602-7ef3-4b75-9263-e5a0b8142c65</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="398" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-72ca</errorHandlerItem>
                <errorHandlerItemId>2025.c43f319b-c907-4143-84be-39532540a091</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.816b6a8b-86e9-4b8a-a02c-0d315c6507a7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(tw.local.productList != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.productList.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = new tw.object.NameValuePair();&#xD;
		tw.local.results[i].name = tw.local.productList[i].arabicName;&#xD;
		tw.local.results[i].value = tw.local.productList[i].productCode;&#xD;
	}&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>8f7a6839-f9b0-40e0-802c-2f378e2a56a3</guid>
                <versionId>afa1e824-7ead-4fe0-bd69-63eefb97ade6</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.fe42a9aa-d5e3-4c16-8b5d-196183d8479a</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Ajax Get ODC Product codes" id="1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.9f353756-4a20-402b-87ae-15459d892d40" />
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.6f08b649-1759-4389-830f-3d1675047322" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.9f353756-4a20-402b-87ae-15459d892d40</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.6f08b649-1759-4389-830f-3d1675047322</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="55a179cd-4368-4a2f-8416-4e2588356f9e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="ef84d6c7-bbfb-47b8-8e66-2a74cac6a83e" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>263d7105-a4e8-4c90-8ccd-8e1ca26ff914</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>175a5e73-7277-498e-8829-0dfa4e142348</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fe42a9aa-d5e3-4c16-8b5d-196183d8479a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>da11a9b9-28cf-4189-84e9-868a271cdf7e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>64fea019-b4d9-4530-846e-0fc85803adb1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c43f319b-c907-4143-84be-39532540a091</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>475f4acf-af54-4a54-852d-bc3a23634cf2</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="263d7105-a4e8-4c90-8ccd-8e1ca26ff914">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.9621d480-1397-4f97-839d-134ccd41edfa</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="175a5e73-7277-498e-8829-0dfa4e142348">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-7370</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>75b81a0c-ea9a-4770-8aa7-b8ab2fef541f</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="263d7105-a4e8-4c90-8ccd-8e1ca26ff914" targetRef="fe42a9aa-d5e3-4c16-8b5d-196183d8479a" name="To Get product codes" id="2027.9621d480-1397-4f97-839d-134ccd41edfa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.757041b2-81d0-44c8-9d96-c2bbdf22a853" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get product codes" id="fe42a9aa-d5e3-4c16-8b5d-196183d8479a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="152" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.9621d480-1397-4f97-839d-134ccd41edfa</ns16:incoming>
                        
                        
                        <ns16:outgoing>5695209c-772d-4344-8c0d-fbdb170d0358</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d98c668e-db67-401b-8e84-cc98608569ea</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.658c7ba0-e16a-4a1e-8bc6-18be5f3d12cb</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"BECO"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a1145555-7f2d-469e-89ad-87ef52093009</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.33fcb7f5-8fa1-4418-8211-679c99edc4c8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.97f65893-b0d8-4cd1-8557-a3e44250b84f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1019293e-e09f-4c51-84c4-e3e8aa04849c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.409793fb-5b0c-4dc8-8d73-474de2f1d809</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC Creation / Amendment Process Details"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.16a42fb6-68f4-443b-836a-521a420d5cb3</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.b78f8c49-b985-497c-8134-903ee8801680</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4ce44c6e-a0df-4c3c-8dbf-0363fd871401</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.7cbc47bc-3460-4dab-84af-9838e8e29b10</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.033d5d03-1e28-4166-8adf-83166aef740a">tw.local.productList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="fe42a9aa-d5e3-4c16-8b5d-196183d8479a" targetRef="da11a9b9-28cf-4189-84e9-868a271cdf7e" name="To Map output" id="5695209c-772d-4344-8c0d-fbdb170d0358">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6199</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="da11a9b9-28cf-4189-84e9-868a271cdf7e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="398" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5695209c-772d-4344-8c0d-fbdb170d0358</ns16:incoming>
                        
                        
                        <ns16:outgoing>75b81a0c-ea9a-4770-8aa7-b8ab2fef541f</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(tw.local.productList != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.productList.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = new tw.object.NameValuePair();&#xD;
		tw.local.results[i].name = tw.local.productList[i].arabicName;&#xD;
		tw.local.results[i].value = tw.local.productList[i].productCode;&#xD;
	}&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="da11a9b9-28cf-4189-84e9-868a271cdf7e" targetRef="175a5e73-7277-498e-8829-0dfa4e142348" name="To End" id="75b81a0c-ea9a-4770-8aa7-b8ab2fef541f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.033d5d03-1e28-4166-8adf-83166aef740a" isCollection="true" name="productList" id="2056.2c388dda-5822-4f75-8a22-e99c380353b4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.2375cae6-2048-4ac3-826c-c1e2aaf058f1" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.ca88cac7-dde4-48f5-8296-cc4ba466b696" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.7693206c-e4e7-48b3-8420-26cc10761201" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="fe42a9aa-d5e3-4c16-8b5d-196183d8479a" parallelMultiple="false" name="Error" id="64fea019-b4d9-4530-846e-0fc85803adb1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="187" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b934c08d-fecc-4f06-8d87-71545777a088</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5df69263-d912-41b9-86ae-c8a4f0776fff" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="0613ba04-9330-48b3-8a09-f3fe48848163" eventImplId="4d91ccde-1533-4620-806a-14f148139cfa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="c43f319b-c907-4143-84be-39532540a091">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="281" y="194" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b934c08d-fecc-4f06-8d87-71545777a088</ns16:incoming>
                        
                        
                        <ns16:incoming>fdd22a9b-d4e4-4bd7-8088-d8d38bad023f</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="c1ab06ea-a63e-4da4-811a-348d4496d787" eventImplId="df801d74-deec-468b-853f-5548ac8e2bae">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="64fea019-b4d9-4530-846e-0fc85803adb1" targetRef="c43f319b-c907-4143-84be-39532540a091" name="To End Event" id="b934c08d-fecc-4f06-8d87-71545777a088">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="da11a9b9-28cf-4189-84e9-868a271cdf7e" parallelMultiple="false" name="Error1" id="475f4acf-af54-4a54-852d-bc3a23634cf2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="433" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>fdd22a9b-d4e4-4bd7-8088-d8d38bad023f</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="9e419490-741d-4992-8807-97649d88e163" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="e54d1ebf-1121-4546-8da5-c5f399613d0b" eventImplId="72802dcc-8f51-46c9-8290-643ec258b15f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="475f4acf-af54-4a54-852d-bc3a23634cf2" targetRef="c43f319b-c907-4143-84be-39532540a091" name="To End Event" id="fdd22a9b-d4e4-4bd7-8088-d8d38bad023f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.b709cd85-1811-4c7c-8089-075f5abb4084" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.75b81a0c-ea9a-4770-8aa7-b8ab2fef541f</processLinkId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.da11a9b9-28cf-4189-84e9-868a271cdf7e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.175a5e73-7277-498e-8829-0dfa4e142348</toProcessItemId>
            <guid>8c414236-6c56-4fdd-a483-5d131cf8a884</guid>
            <versionId>359590b5-5b43-48cb-a755-15a28862ab78</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.da11a9b9-28cf-4189-84e9-868a271cdf7e</fromProcessItemId>
            <toProcessItemId>2025.175a5e73-7277-498e-8829-0dfa4e142348</toProcessItemId>
        </link>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5695209c-772d-4344-8c0d-fbdb170d0358</processLinkId>
            <processId>1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fe42a9aa-d5e3-4c16-8b5d-196183d8479a</fromProcessItemId>
            <endStateId>guid:502f0309b7e9a7eb:-5dac1a55:188bbb8ea24:-6199</endStateId>
            <toProcessItemId>2025.da11a9b9-28cf-4189-84e9-868a271cdf7e</toProcessItemId>
            <guid>0a71d844-57f6-4089-986b-4adf5bff5bff</guid>
            <versionId>9890044d-a840-4f6f-aaf8-0c138c0df651</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fe42a9aa-d5e3-4c16-8b5d-196183d8479a</fromProcessItemId>
            <toProcessItemId>2025.da11a9b9-28cf-4189-84e9-868a271cdf7e</toProcessItemId>
        </link>
    </process>
</teamworks>

