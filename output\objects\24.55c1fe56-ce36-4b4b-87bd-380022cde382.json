{"id": "24.55c1fe56-ce36-4b4b-87bd-380022cde382", "versionId": "8b8cf17f-db58-4284-86c7-f2ad044d9901", "name": "Trade compliance Maker", "type": "participant", "typeName": "Participant", "details": {}, "_fullObjectData": {"teamworks": {"participant": {"id": "24.55c1fe56-ce36-4b4b-87bd-380022cde382", "name": "Trade compliance Maker", "lastModified": "1691144885665", "lastModifiedBy": "heba", "participantId": "24.55c1fe56-ce36-4b4b-87bd-380022cde382", "participantDefinition": {"isNull": "true"}, "simulationGroupSize": "2", "capacityType": "1", "definitionType": "3", "percentAvailable": {"isNull": "true"}, "percentEfficiency": {"isNull": "true"}, "cost": "10.00", "currencyCode": {"isNull": "true"}, "image": {"isNull": "true"}, "serviceMembersRef": {"isNull": "true"}, "managersRef": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"team\":[{\"members\":{\"Users\":[{\"name\":\"heba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"somaia\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"abdelrahman.saleh\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}],\"UserGroups\":[{\"name\":\"BPM_ODC_Trade_Compliance_MKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails\",\"type\":\"StandardMembers\"}]},\"documentation\":[{\"content\":[],\"textFormat\":\"text\\/plain\"}],\"name\":\"Trade compliance Maker\",\"declaredType\":\"resource\",\"id\":\"24.55c1fe56-ce36-4b4b-87bd-380022cde382\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.w3.org\\/1999\\/XPath\",\"id\":\"24.55c1fe56-ce36-4b4b-87bd-380022cde382\"}", "externalId": {"isNull": "true"}, "description": "", "guid": "guid:d694a63221635d5b:6baf87c4:1896d4865c2:74f1", "versionId": "8b8cf17f-db58-4284-86c7-f2ad044d9901", "standardMembers": {"standardMember": [{"type": "Group", "name": "BPM_ODC_Trade_Compliance_MKR"}, {"type": "User", "name": "heba"}, {"type": "User", "name": "so<PERSON>ia"}, {"type": "User", "name": "abdelrahman.saleh"}]}, "teamAssignments": ""}}}}