{"id": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "versionId": "43837a43-23fe-4fde-9d48-cd45e4400de0", "name": "Execution HUB Filter Service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "name": "Execution HUB Filter Service", "lastModified": "1693992867476", "lastModifiedBy": "abdelrahman.saleh", "processId": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.92e7eabd-57ba-422f-8da7-3f3fe5919852", "2025.92e7eabd-57ba-422f-8da7-3f3fe5919852"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "65ceffc9-7695-4242-8cac-8c7f4d17e717", "versionId": "43837a43-23fe-4fde-9d48-cd45e4400de0", "dependencySummary": "<dependencySummary id=\"bpdid:fc53d6da47fd17d0:63ae7219:18a68bd8c93:7172\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.4e06d43f-6adf-48d9-880e-e562a530b04b\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"d2814379-e4ae-4c6b-b818-a21d4101db5b\"},{\"incoming\":[\"bdd71da2-33b7-4474-8d19-fcfa17f4eab9\",\"ba7e71b0-a45b-4325-842b-b8740bc4e517\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-6f0\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"f4bf22da-a97b-4a4a-8f87-8000efbf5b57\"},{\"targetRef\":\"92e7eabd-57ba-422f-8da7-3f3fe5919852\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get filtered team\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4e06d43f-6adf-48d9-880e-e562a530b04b\",\"sourceRef\":\"d2814379-e4ae-4c6b-b818-a21d4101db5b\"},{\"startQuantity\":1,\"outgoing\":[\"bdd71da2-33b7-4474-8d19-fcfa17f4eab9\"],\"incoming\":[\"2027.4e06d43f-6adf-48d9-880e-e562a530b04b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":299,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Get filtered team\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"92e7eabd-57ba-422f-8da7-3f3fe5919852\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.originalTeam = new tw.object.Team();\\r\\ntw.local.originalTeam.members = new tw.object.listOf.String();\\r\\n\\/\\/tw.local.originalTeam.members[0] = \\\"odchubexemkr07\\\";\\r\\n\\/\\/tw.local.originalTeam.members[1] = \\\"odchubexemkr10\\\";\\r\\n\\/\\/tw.local.originalTeam.members[2] = \\\"odchubexemkr20\\\";\\r\\n\\/\\/tw.local.originalTeam.members[3] = \\\"odchubexemkr31\\\";\\r\\n\\/\\/tw.local.originalTeam.members[4] = \\\"odchubexemkr38\\\";\\r\\n\\/\\/tw.local.originalTeam.members[5] = \\\"odchubexemkr59\\\";\\r\\n\\/\\/tw.local.originalTeam.managerTeam = \\\"Managers\\\";\\r\\n\\/\\/tw.local.originalTeam.name = \\\"ODC EXE HUB\\\";\\r\\n\\r\\ntw.local.filteredTeam = new tw.object.Team();\\r\\ntw.local.filteredTeam.members = new tw.object.listOf.String();\\r\\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\\r\\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\\r\\n\\r\\nvar users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;\\r\\n\\t\\r\\nlog.info(\\\"users::: \\\"+ users);\\r\\n\\tfor (var i = 0; i < users.listLength ; i++)\\r\\n\\t{\\r\\n\\t\\ttw.local.filteredTeam.members[i] = users[i].name;\\r\\n\\t}\"]}},{\"targetRef\":\"f4bf22da-a97b-4a4a-8f87-8000efbf5b57\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"bdd71da2-33b7-4474-8d19-fcfa17f4eab9\",\"sourceRef\":\"92e7eabd-57ba-422f-8da7-3f3fe5919852\"},{\"parallelMultiple\":false,\"outgoing\":[\"ba7e71b0-a45b-4325-842b-b8740bc4e517\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"5a9e4b34-86d9-4a81-8951-6b8a01d1273c\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"d1902755-df9e-4aa2-8370-ad5a180706c9\",\"otherAttributes\":{\"eventImplId\":\"63e505b1-32e2-4692-804b-c9e2a46c366d\"}}],\"attachedToRef\":\"92e7eabd-57ba-422f-8da7-3f3fe5919852\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":334,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"1143842a-fe6d-4496-8c07-d1669c8a307d\",\"outputSet\":{}},{\"targetRef\":\"f4bf22da-a97b-4a4a-8f87-8000efbf5b57\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"ba7e71b0-a45b-4325-842b-b8740bc4e517\",\"sourceRef\":\"1143842a-fe6d-4496-8c07-d1669c8a307d\"}],\"laneSet\":[{\"id\":\"c0e9d626-f46b-4851-bc72-cd90c9c5c79c\",\"lane\":[{\"flowNodeRef\":[\"d2814379-e4ae-4c6b-b818-a21d4101db5b\",\"f4bf22da-a97b-4a4a-8f87-8000efbf5b57\",\"92e7eabd-57ba-422f-8da7-3f3fe5919852\",\"1143842a-fe6d-4496-8c07-d1669c8a307d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"12257839-569d-4997-add6-a5ec7ef6b539\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Execution HUB Filter Service\",\"declaredType\":\"process\",\"id\":\"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0\",\"name\":\"filteredTeam\",\"isCollection\":false,\"id\":\"2055.169bd327-93b4-4642-9fae-8c641f0cdf88\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}readOnly\":\"true\"}}],\"inputSet\":[{\"dataInputRefs\":[\"2055.5257e111-aedc-4a58-a1fc-f4e3ad1de61d\",\"2055.b95f0910-f183-4dab-9065-28297a14ceef\"]}],\"outputSet\":[{}],\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}readOnlyOutputs\":\"true\"},\"dataInput\":[{\"itemSubjectRef\":\"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0\",\"name\":\"originalTeam\",\"isCollection\":false,\"id\":\"2055.5257e111-aedc-4a58-a1fc-f4e3ad1de61d\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}readOnly\":\"true\"}},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"BPM_IDC_HUB_0599_EXE_MKR\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"groupName\",\"isCollection\":false,\"id\":\"2055.b95f0910-f183-4dab-9065-28297a14ceef\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "originalTeam", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5257e111-aedc-4a58-a1fc-f4e3ad1de61d", "processId": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "58c46915-581e-4642-b47b-a3ef5d398acc", "versionId": "33f44076-c13f-4edc-b27d-ad559163dfdf"}, {"name": "groupName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b95f0910-f183-4dab-9065-28297a14ceef", "processId": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"BPM_IDC_HUB_0599_EXE_MKR\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "d687bf6b-7a71-4af5-9776-63d16f5fce44", "versionId": "3a36cd08-d7b7-4055-8fb4-fbe8394faa52"}, {"name": "filteredTeam", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.169bd327-93b4-4642-9fae-8c641f0cdf88", "processId": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "676177cc-fe59-4bf5-acf3-48df8011b941", "versionId": "6c39c161-479c-4208-bc92-3ac2443189a3"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.92e7eabd-57ba-422f-8da7-3f3fe5919852", "processId": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "name": "Get filtered team", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.872066d2-d012-403a-9dc8-2ec3db6cef4b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-6ef", "versionId": "a61869de-ecce-4e78-bf95-8350859872f1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "299", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-6f0", "errorHandlerItemId": "2025.f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "bottomCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.872066d2-d012-403a-9dc8-2ec3db6cef4b", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.originalTeam = new tw.object.Team();\r\r\ntw.local.originalTeam.members = new tw.object.listOf.String();\r\r\n//tw.local.originalTeam.members[0] = \"odchubexemkr07\";\r\r\n//tw.local.originalTeam.members[1] = \"odchubexemkr10\";\r\r\n//tw.local.originalTeam.members[2] = \"odchubexemkr20\";\r\r\n//tw.local.originalTeam.members[3] = \"odchubexemkr31\";\r\r\n//tw.local.originalTeam.members[4] = \"odchubexemkr38\";\r\r\n//tw.local.originalTeam.members[5] = \"odchubexemkr59\";\r\r\n//tw.local.originalTeam.managerTeam = \"Managers\";\r\r\n//tw.local.originalTeam.name = \"ODC EXE HUB\";\r\r\n\r\r\ntw.local.filteredTeam = new tw.object.Team();\r\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\r\n\r\r\nvar users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;\r\r\n\t\r\r\nlog.info(\"users::: \"+ users);\r\r\n\tfor (var i = 0; i < users.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.filteredTeam.members[i] = users[i].name;\r\r\n\t}", "isRule": "false", "guid": "44475302-c688-4a12-9ea4-249c659486cc", "versionId": "b20deb9f-bedd-4466-82b9-cad58d1aa8d7"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "processId": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.07476d83-f79c-4825-bec8-b63b4d76b405", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-6f0", "versionId": "d239bc5d-ae3f-4556-a733-d38428268683", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.07476d83-f79c-4825-bec8-b63b4d76b405", "haltProcess": "false", "guid": "0214592b-1a82-435d-af67-441b8a961289", "versionId": "6ed57c5d-98ce-4dc0-86f4-41c80a53177a"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Execution HUB Filter Service", "id": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns3:readOnlyOutputs": "true", "ns16:dataInput": [{"name": "originalTeam", "itemSubjectRef": "itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "isCollection": "false", "id": "2055.5257e111-aedc-4a58-a1fc-f4e3ad1de61d", "ns3:readOnly": "true"}, {"name": "groupName", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.b95f0910-f183-4dab-9065-28297a14ceef", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"BPM_IDC_HUB_0599_EXE_MKR\"", "useDefault": "true"}}}], "ns16:dataOutput": {"name": "filteredTeam", "itemSubjectRef": "itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "isCollection": "false", "id": "2055.169bd327-93b4-4642-9fae-8c641f0cdf88", "ns3:readOnly": "true"}, "ns16:inputSet": {"ns16:dataInputRefs": ["2055.5257e111-aedc-4a58-a1fc-f4e3ad1de61d", "2055.b95f0910-f183-4dab-9065-28297a14ceef"]}, "ns16:outputSet": ""}, "ns16:laneSet": {"id": "c0e9d626-f46b-4851-bc72-cd90c9c5c79c", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "12257839-569d-4997-add6-a5ec7ef6b539", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["d2814379-e4ae-4c6b-b818-a21d4101db5b", "f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "92e7eabd-57ba-422f-8da7-3f3fe5919852", "1143842a-fe6d-4496-8c07-d1669c8a307d"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "d2814379-e4ae-4c6b-b818-a21d4101db5b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.4e06d43f-6adf-48d9-880e-e562a530b04b"}, "ns16:endEvent": {"name": "End", "id": "f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-6f0"}, "ns16:incoming": ["bdd71da2-33b7-4474-8d19-fcfa17f4eab9", "ba7e71b0-a45b-4325-842b-b8740bc4e517"]}, "ns16:sequenceFlow": [{"sourceRef": "d2814379-e4ae-4c6b-b818-a21d4101db5b", "targetRef": "92e7eabd-57ba-422f-8da7-3f3fe5919852", "name": "To Get filtered team", "id": "2027.4e06d43f-6adf-48d9-880e-e562a530b04b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "92e7eabd-57ba-422f-8da7-3f3fe5919852", "targetRef": "f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "name": "To End", "id": "bdd71da2-33b7-4474-8d19-fcfa17f4eab9", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "1143842a-fe6d-4496-8c07-d1669c8a307d", "targetRef": "f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "name": "To End", "id": "ba7e71b0-a45b-4325-842b-b8740bc4e517", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Get filtered team", "id": "92e7eabd-57ba-422f-8da7-3f3fe5919852", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "299", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.4e06d43f-6adf-48d9-880e-e562a530b04b", "ns16:outgoing": "bdd71da2-33b7-4474-8d19-fcfa17f4eab9", "ns16:script": "tw.local.originalTeam = new tw.object.Team();\r\r\ntw.local.originalTeam.members = new tw.object.listOf.String();\r\r\n//tw.local.originalTeam.members[0] = \"odchubexemkr07\";\r\r\n//tw.local.originalTeam.members[1] = \"odchubexemkr10\";\r\r\n//tw.local.originalTeam.members[2] = \"odchubexemkr20\";\r\r\n//tw.local.originalTeam.members[3] = \"odchubexemkr31\";\r\r\n//tw.local.originalTeam.members[4] = \"odchubexemkr38\";\r\r\n//tw.local.originalTeam.members[5] = \"odchubexemkr59\";\r\r\n//tw.local.originalTeam.managerTeam = \"Managers\";\r\r\n//tw.local.originalTeam.name = \"ODC EXE HUB\";\r\r\n\r\r\ntw.local.filteredTeam = new tw.object.Team();\r\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\r\n\r\r\nvar users = tw.system.org.findRoleByName(tw.local.groupName).allUsers;\r\r\n\t\r\r\nlog.info(\"users::: \"+ users);\r\r\n\tfor (var i = 0; i < users.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.filteredTeam.members[i] = users[i].name;\r\r\n\t}"}, "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "92e7eabd-57ba-422f-8da7-3f3fe5919852", "parallelMultiple": "false", "name": "Error", "id": "1143842a-fe6d-4496-8c07-d1669c8a307d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "334", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "ba7e71b0-a45b-4325-842b-b8740bc4e517", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "5a9e4b34-86d9-4a81-8951-6b8a01d1273c"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "d1902755-df9e-4aa2-8370-ad5a180706c9", "eventImplId": "63e505b1-32e2-4692-804b-c9e2a46c366d", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.bdd71da2-33b7-4474-8d19-fcfa17f4eab9", "processId": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.92e7eabd-57ba-422f-8da7-3f3fe5919852", "2025.92e7eabd-57ba-422f-8da7-3f3fe5919852"], "endStateId": "Out", "toProcessItemId": ["2025.f4bf22da-a97b-4a4a-8f87-8000efbf5b57", "2025.f4bf22da-a97b-4a4a-8f87-8000efbf5b57"], "guid": "526a9383-f788-4f9f-b218-159377142975", "versionId": "47dadfda-8b62-469f-b5a0-d1125a481adb", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}}}}, "subType": "12", "hasDetails": false}