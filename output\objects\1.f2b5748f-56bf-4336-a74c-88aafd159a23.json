{"id": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "versionId": "2cfea046-37c5-434e-9f08-99940c2849ff", "name": "retrieve bills data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "retrieve bills data", "lastModified": "1696416365520", "lastModifiedBy": "heba", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.df927a44-7dfc-4559-80b6-4b45cf736964", "2025.df927a44-7dfc-4559-80b6-4b45cf736964"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49aa", "versionId": "2cfea046-37c5-434e-9f08-99940c2849ff", "dependencySummary": "<dependencySummary id=\"bpdid:cdc758d910e20d2d:-607ed7:18af686ab01:450e\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.d6ce474b-ca8c-4eb3-851a-35927c232ac7\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"ae8fd174-2e9e-468b-8959-5952bc65cb77\"},{\"incoming\":[\"4d03b375-6b2b-474a-886f-5ddcb6554c2a\",\"d7945606-5ca4-4d92-8b73-c5201d8fa0b4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":802,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aeac88b51:49ac\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71\"},{\"targetRef\":\"df927a44-7dfc-4559-80b6-4b45cf736964\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d6ce474b-ca8c-4eb3-851a-35927c232ac7\",\"sourceRef\":\"ae8fd174-2e9e-468b-8959-5952bc65cb77\"},{\"startQuantity\":1,\"outgoing\":[\"f0016647-ac85-4059-8cd9-904015aaaf8b\"],\"incoming\":[\"2027.d6ce474b-ca8c-4eb3-851a-35927c232ac7\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":107,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Script Task\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"df927a44-7dfc-4559-80b6-4b45cf736964\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sql = \\\"select * from odc_bills where requesrid = '\\\"+tw.local.requestId+\\\"' \\\";\"]}},{\"startQuantity\":1,\"outgoing\":[\"4d03b375-6b2b-474a-886f-5ddcb6554c2a\"],\"incoming\":[\"171bccfc-8572-410f-8495-8fcd830254e5\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":477,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Mapping output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"9f4d31aa-d83a-4598-8616-d3823b4192ca\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/tw.local.odcRequest = {};\\r\\n\\/\\/tw.local.odcRequest.BasicDetails ={};\\r\\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\\r\\n\\r\\nif(tw.local.sqlResults.listLength > 0)\\r\\n{\\r\\n\\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\\r\\n\\t{\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Bills[i]    \\t\\t\\t = new tw.object.Bills();\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate   = tw.local.sqlResults[0].rows[i].data[1];\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef    = tw.local.sqlResults[0].rows[i].data[0];\\r\\n\\t}\\r\\n\\r\\n}\"]}},{\"startQuantity\":1,\"outgoing\":[\"171bccfc-8572-410f-8495-8fcd830254e5\"],\"incoming\":[\"f0016647-ac85-4059-8cd9-904015aaaf8b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":291,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Linked Service Flow\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"431577d6-1e81-40a8-8074-893b3183d1de\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlResults\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"431577d6-1e81-40a8-8074-893b3183d1de\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Linked Service Flow\",\"declaredType\":\"sequenceFlow\",\"id\":\"f0016647-ac85-4059-8cd9-904015aaaf8b\",\"sourceRef\":\"df927a44-7dfc-4559-80b6-4b45cf736964\"},{\"targetRef\":\"9f4d31aa-d83a-4598-8616-d3823b4192ca\",\"extensionElements\":{\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Mapping output\",\"declaredType\":\"sequenceFlow\",\"id\":\"171bccfc-8572-410f-8495-8fcd830254e5\",\"sourceRef\":\"431577d6-1e81-40a8-8074-893b3183d1de\"},{\"targetRef\":\"08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"4d03b375-6b2b-474a-886f-5ddcb6554c2a\",\"sourceRef\":\"9f4d31aa-d83a-4598-8616-d3823b4192ca\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2f744c7f-7a6b-4dde-8d49-913078cf199f\"},{\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"sqlParameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.b37ad3e6-12b3-4bc8-8713-6fed29423cd9\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"sqlResults\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.e5bb3f41-7195-4ec9-8cd1-b371ee838767\"},{\"parallelMultiple\":false,\"outgoing\":[\"4c5128a7-c894-4972-8bff-367a1d98fb90\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"cfa3df0b-a2dd-40f0-820c-e43e3e2a33dd\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"c135ec7e-566c-4899-8340-d04873f7052d\",\"otherAttributes\":{\"eventImplId\":\"7f33cd7f-a3ca-4b5e-80a9-0f782abe75d3\"}}],\"attachedToRef\":\"9f4d31aa-d83a-4598-8616-d3823b4192ca\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":512,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"29c3ccc7-103a-4a26-8f09-f6cd155972b4\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"8d66540e-cba3-45de-8210-e2c93de71ba8\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"3d6bd4af-8299-47e0-81e7-5d5c6b3c6df0\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"cdbed30f-d3db-4bd9-8053-193c1d15649d\",\"otherAttributes\":{\"eventImplId\":\"4f038fa4-0467-48ac-8508-4d5ce98a3611\"}}],\"attachedToRef\":\"431577d6-1e81-40a8-8074-893b3183d1de\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":326,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error2\",\"declaredType\":\"boundaryEvent\",\"id\":\"7b28daef-9f39-461b-853c-57434fafef44\",\"outputSet\":{}},{\"startQuantity\":1,\"outgoing\":[\"d7945606-5ca4-4d92-8b73-c5201d8fa0b4\"],\"incoming\":[\"8d66540e-cba3-45de-8210-e2c93de71ba8\",\"4c5128a7-c894-4972-8bff-367a1d98fb90\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":500,\"y\":173,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Retrieve bills data\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"cd110212-**************-8ddc39a45c35\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"cd110212-**************-8ddc39a45c35\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"8d66540e-cba3-45de-8210-e2c93de71ba8\",\"sourceRef\":\"7b28daef-9f39-461b-853c-57434fafef44\"},{\"targetRef\":\"cd110212-**************-8ddc39a45c35\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"4c5128a7-c894-4972-8bff-367a1d98fb90\",\"sourceRef\":\"29c3ccc7-103a-4a26-8f09-f6cd155972b4\"},{\"targetRef\":\"08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"d7945606-5ca4-4d92-8b73-c5201d8fa0b4\",\"sourceRef\":\"cd110212-**************-8ddc39a45c35\"}],\"laneSet\":[{\"id\":\"192ab1ae-3fb8-4e8a-8ef3-f330d5baf8a9\",\"lane\":[{\"flowNodeRef\":[\"ae8fd174-2e9e-468b-8959-5952bc65cb77\",\"08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71\",\"df927a44-7dfc-4559-80b6-4b45cf736964\",\"9f4d31aa-d83a-4598-8616-d3823b4192ca\",\"431577d6-1e81-40a8-8074-893b3183d1de\",\"29c3ccc7-103a-4a26-8f09-f6cd155972b4\",\"7b28daef-9f39-461b-853c-57434fafef44\",\"cd110212-**************-8ddc39a45c35\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"9a1fb9e2-58f8-4c2e-8142-1227c5b94c98\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"retrieve bills data\",\"declaredType\":\"process\",\"id\":\"1.f2b5748f-56bf-4336-a74c-88aafd159a23\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.b6ac116b-6326-4d97-8b93-e99cfc87b708\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.6ff52d30-eb38-49af-8675-b5853cb88523\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.b6ac116b-6326-4d97-8b93-e99cfc87b708\",\"2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"0\"}]},\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"requestId\",\"isCollection\":false,\"id\":\"2055.6ff52d30-eb38-49af-8675-b5853cb88523\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6ff52d30-eb38-49af-8675-b5853cb88523", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "seq": "1", "hasDefault": "true", "defaultValue": "0", "isLocked": "false", "description": {"isNull": "true"}, "guid": "0aa72b16-1363-4e68-a758-de7a445c47bf", "versionId": "6b69ca26-6796-4c4e-a831-efd01480c4be"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b6ac116b-6326-4d97-8b93-e99cfc87b708", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "42c2984e-cea5-4d17-b29e-5e19cdd1e169", "versionId": "ae389dae-5e10-4ea4-a1d7-f8cdb5fb51bd"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "26177706-3ae7-47c1-bf1c-7492db1c8e20", "versionId": "3e5de291-5a80-4dee-a802-2fc457be698e"}], "processVariable": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2f744c7f-7a6b-4dde-8d49-913078cf199f", "description": {"isNull": "true"}, "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7ac34e70-4ce5-490a-83c5-a74de07adbfc", "versionId": "f35e2bf1-7209-4afe-955b-b8e0247624a9"}, {"name": "sqlParameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b37ad3e6-12b3-4bc8-8713-6fed29423cd9", "description": {"isNull": "true"}, "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c8e77215-29bc-41f2-aef7-3e574c659c05", "versionId": "308045df-f68a-4fb3-8626-29e3bb8c2f8b"}, {"name": "sqlResults", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.e5bb3f41-7195-4ec9-8cd1-b371ee838767", "description": {"isNull": "true"}, "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4e73deff-0a32-4e51-8ffc-f0c08338e481", "versionId": "5f812d53-a859-4e5a-8a33-14fe0fda0e5d"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.696609e6-c5e1-47e9-bde9-5fe5a3ef976a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49ac", "versionId": "50f762fb-8f7f-42b1-8939-36fc2c2fd230", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "802", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.696609e6-c5e1-47e9-bde9-5fe5a3ef976a", "haltProcess": "false", "guid": "9843d785-db7d-436d-87ba-4de17097911a", "versionId": "2b881ef6-6ea7-4ec9-8c0f-c46db17fef92"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.431577d6-1e81-40a8-8074-893b3183d1de", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "Linked Service Flow", "tWComponentName": "SubProcess", "tWComponentId": "3012.e8760454-aac9-4f17-b534-004383789b68", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.cd110212-**************-8ddc39a45c35", "guid": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49b9", "versionId": "81561420-d066-4430-bd36-eb8bcbc443a0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "291", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:3a89", "errorHandlerItemId": "2025.cd110212-**************-8ddc39a45c35", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.e8760454-aac9-4f17-b534-004383789b68", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "e5bd3226-16f0-4e88-9148-d6bc1a0f7b32", "versionId": "c8130d96-0599-45b5-8cd5-4174e62e6c46", "parameterMapping": [{"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4e774722-b8a7-419c-b733-c4040f0ed816", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.e8760454-aac9-4f17-b534-004383789b68", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "160e8d3e-ed42-4dd1-abdb-5c32aee78e43", "versionId": "1a853757-9d74-4d6d-8fe0-0b2e836dafcd", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.803faf2b-fe81-42db-aebf-84a1a1842545", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.e8760454-aac9-4f17-b534-004383789b68", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "dc3ff1c4-4dc3-4b0d-bf94-7bb1e243aa4a", "versionId": "3aba4bdd-e206-4271-9edd-91eb2e06d736", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.cd969f2a-9b52-45f0-a1c0-e8078bde524c", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.e8760454-aac9-4f17-b534-004383789b68", "useDefault": "false", "value": "tw.local.sqlResults", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "a9da56dc-4181-4e2d-908c-fb7e5523c45b", "versionId": "91730415-41d7-469b-a7c9-6bb8d74fe02d", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ca8f9c3d-bd58-46f6-a0da-1be78dcdfed8", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.e8760454-aac9-4f17-b534-004383789b68", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "3d2065a7-a20a-453a-822b-7eda33527f53", "versionId": "9fe503da-cdec-4cbc-a678-30f81ed1b837", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4151b68e-754a-445f-aea2-e597d25d0ca9", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.e8760454-aac9-4f17-b534-004383789b68", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ceb2cbd0-42b6-4cf5-acec-ce68bac882b0", "versionId": "e8c8b3ae-63a9-4f6d-9c64-918f97e8d9ce", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.cd110212-**************-8ddc39a45c35", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.402e15d6-48da-4cee-b6c8-e1db12d97519", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:3a89", "versionId": "895bf783-a187-4b72-9532-9934f1ef48dd", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "500", "y": "173", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.402e15d6-48da-4cee-b6c8-e1db12d97519", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "f81b8067-5d23-4524-8d2e-dffa0831814a", "versionId": "0237a7bd-09cc-40f1-82f2-b1cc53f293c1", "parameterMapping": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1efdc2c7-268e-434c-a356-71c89d4d1198", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.402e15d6-48da-4cee-b6c8-e1db12d97519", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "7e93aba6-a0e2-4d3f-93e4-8ecd6248c972", "versionId": "3201399e-850c-4a4b-abdf-2e4699736b5c", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3de344ae-3373-4d92-adbe-9ccefe11a965", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.402e15d6-48da-4cee-b6c8-e1db12d97519", "useDefault": "false", "value": "\"Retrieve bills data\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "b3c6f0a2-c494-4469-a755-4e7c8ed3f0d4", "versionId": "6cc985e2-adb0-4236-8174-6cff764fa4e6", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.df927a44-7dfc-4559-80b6-4b45cf736964", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "Script Task", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.6ba10409-1bed-4ee9-b2ac-64f343d7ddc9", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49b7", "versionId": "8f5c7f3f-3ead-4651-ae87-32f357f9b55e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "107", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.6ba10409-1bed-4ee9-b2ac-64f343d7ddc9", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sql = \"select * from odc_bills where requesrid = '\"+tw.local.requestId+\"' \";", "isRule": "false", "guid": "b60ab253-c41a-4370-83a2-f52a2d97ad8d", "versionId": "b64cdb2d-f930-4f30-808c-e008369543b5"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9f4d31aa-d83a-4598-8616-d3823b4192ca", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "Mapping output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.d46b1cd9-12af-4478-9df5-64358977ef48", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.cd110212-**************-8ddc39a45c35", "guid": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49b8", "versionId": "bfa6b8d1-7c58-4d8e-953b-41a0a636c464", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "477", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:3a89", "errorHandlerItemId": "2025.cd110212-**************-8ddc39a45c35", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topLeft", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.d46b1cd9-12af-4478-9df5-64358977ef48", "scriptTypeId": "2", "isActive": "true", "script": "//tw.local.odcRequest = {};\r\r\n//tw.local.odcRequest.BasicDetails ={};\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\n\r\r\nif(tw.local.sqlResults.listLength > 0)\r\r\n{\r\r\n\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i]    \t\t\t = new tw.object.Bills();\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate   = tw.local.sqlResults[0].rows[i].data[1];\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef    = tw.local.sqlResults[0].rows[i].data[0];\r\r\n\t}\r\r\n\r\r\n}", "isRule": "false", "guid": "f7bfa7f3-fac3-4dfa-88ee-40536295b9e4", "versionId": "1687c8d9-824f-4ddd-a5c7-5979f3f42d7a"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "retrieve bills data", "id": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "requestId", "itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "id": "2055.6ff52d30-eb38-49af-8675-b5853cb88523", "ns16:extensionElements": {"ns3:defaultValue": {"_": "0", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.b6ac116b-6326-4d97-8b93-e99cfc87b708"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.6ff52d30-eb38-49af-8675-b5853cb88523"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.b6ac116b-6326-4d97-8b93-e99cfc87b708", "2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1"]}}, "ns16:laneSet": {"id": "192ab1ae-3fb8-4e8a-8ef3-f330d5baf8a9", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "9a1fb9e2-58f8-4c2e-8142-1227c5b94c98", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["ae8fd174-2e9e-468b-8959-5952bc65cb77", "08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71", "df927a44-7dfc-4559-80b6-4b45cf736964", "9f4d31aa-d83a-4598-8616-d3823b4192ca", "431577d6-1e81-40a8-8074-893b3183d1de", "29c3ccc7-103a-4a26-8f09-f6cd155972b4", "7b28daef-9f39-461b-853c-57434fafef44", "cd110212-**************-8ddc39a45c35"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "ae8fd174-2e9e-468b-8959-5952bc65cb77", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.d6ce474b-ca8c-4eb3-851a-35927c232ac7"}, "ns16:endEvent": {"name": "End", "id": "08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "802", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49ac"}, "ns16:incoming": ["4d03b375-6b2b-474a-886f-5ddcb6554c2a", "d7945606-5ca4-4d92-8b73-c5201d8fa0b4"]}, "ns16:sequenceFlow": [{"sourceRef": "ae8fd174-2e9e-468b-8959-5952bc65cb77", "targetRef": "df927a44-7dfc-4559-80b6-4b45cf736964", "name": "To End", "id": "2027.d6ce474b-ca8c-4eb3-851a-35927c232ac7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "df927a44-7dfc-4559-80b6-4b45cf736964", "targetRef": "431577d6-1e81-40a8-8074-893b3183d1de", "name": "To Linked Service Flow", "id": "f0016647-ac85-4059-8cd9-904015aaaf8b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "431577d6-1e81-40a8-8074-893b3183d1de", "targetRef": "9f4d31aa-d83a-4598-8616-d3823b4192ca", "name": "To Mapping output", "id": "171bccfc-8572-410f-8495-8fcd830254e5", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "9f4d31aa-d83a-4598-8616-d3823b4192ca", "targetRef": "08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71", "name": "To End", "id": "4d03b375-6b2b-474a-886f-5ddcb6554c2a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "7b28daef-9f39-461b-853c-57434fafef44", "targetRef": "cd110212-**************-8ddc39a45c35", "name": "To Exception Handling", "id": "8d66540e-cba3-45de-8210-e2c93de71ba8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "29c3ccc7-103a-4a26-8f09-f6cd155972b4", "targetRef": "cd110212-**************-8ddc39a45c35", "name": "To Exception Handling", "id": "4c5128a7-c894-4972-8bff-367a1d98fb90", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "cd110212-**************-8ddc39a45c35", "targetRef": "08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71", "name": "To End", "id": "d7945606-5ca4-4d92-8b73-c5201d8fa0b4", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Script Task", "id": "df927a44-7dfc-4559-80b6-4b45cf736964", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "107", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.d6ce474b-ca8c-4eb3-851a-35927c232ac7", "ns16:outgoing": "f0016647-ac85-4059-8cd9-904015aaaf8b", "ns16:script": "tw.local.sql = \"select * from odc_bills where requesrid = '\"+tw.local.requestId+\"' \";"}, {"scriptFormat": "text/x-javascript", "name": "Mapping output", "id": "9f4d31aa-d83a-4598-8616-d3823b4192ca", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "477", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "171bccfc-8572-410f-8495-8fcd830254e5", "ns16:outgoing": "4d03b375-6b2b-474a-886f-5ddcb6554c2a", "ns16:script": "//tw.local.odcRequest = {};\r\r\n//tw.local.odcRequest.BasicDetails ={};\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\n\r\r\nif(tw.local.sqlResults.listLength > 0)\r\r\n{\r\r\n\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i]    \t\t\t = new tw.object.Bills();\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate   = tw.local.sqlResults[0].rows[i].data[1];\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef    = tw.local.sqlResults[0].rows[i].data[0];\r\r\n\t}\r\r\n\r\r\n}"}], "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "Linked Service Flow", "id": "431577d6-1e81-40a8-8074-893b3183d1de", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "291", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "f0016647-ac85-4059-8cd9-904015aaaf8b", "ns16:outgoing": "171bccfc-8572-410f-8495-8fcd830254e5", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.sqlResults", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exception Handling", "id": "cd110212-**************-8ddc39a45c35", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "500", "y": "173", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["8d66540e-cba3-45de-8210-e2c93de71ba8", "4c5128a7-c894-4972-8bff-367a1d98fb90"], "ns16:outgoing": "d7945606-5ca4-4d92-8b73-c5201d8fa0b4", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Retrieve bills data\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.2f744c7f-7a6b-4dde-8d49-913078cf199f"}, {"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "sqlParameters", "id": "2056.b37ad3e6-12b3-4bc8-8713-6fed29423cd9"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "sqlResults", "id": "2056.e5bb3f41-7195-4ec9-8cd1-b371ee838767"}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "9f4d31aa-d83a-4598-8616-d3823b4192ca", "parallelMultiple": "false", "name": "Error1", "id": "29c3ccc7-103a-4a26-8f09-f6cd155972b4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "512", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "4c5128a7-c894-4972-8bff-367a1d98fb90", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "cfa3df0b-a2dd-40f0-820c-e43e3e2a33dd"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "c135ec7e-566c-4899-8340-d04873f7052d", "eventImplId": "7f33cd7f-a3ca-4b5e-80a9-0f782abe75d3", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "431577d6-1e81-40a8-8074-893b3183d1de", "parallelMultiple": "false", "name": "Error2", "id": "7b28daef-9f39-461b-853c-57434fafef44", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "326", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "8d66540e-cba3-45de-8210-e2c93de71ba8", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "3d6bd4af-8299-47e0-81e7-5d5c6b3c6df0"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "cdbed30f-d3db-4bd9-8053-193c1d15649d", "eventImplId": "4f038fa4-0467-48ac-8508-4d5ce98a3611", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To Mapping output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.171bccfc-8572-410f-8495-8fcd830254e5", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.431577d6-1e81-40a8-8074-893b3183d1de", "2025.431577d6-1e81-40a8-8074-893b3183d1de"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.9f4d31aa-d83a-4598-8616-d3823b4192ca", "2025.9f4d31aa-d83a-4598-8616-d3823b4192ca"], "guid": "c5db83e6-37f8-4054-a5fe-48ce8162e691", "versionId": "2c92c09f-dd43-43e2-bc75-0468d851d3f6", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Linked Service Flow", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f0016647-ac85-4059-8cd9-904015aaaf8b", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.df927a44-7dfc-4559-80b6-4b45cf736964", "2025.df927a44-7dfc-4559-80b6-4b45cf736964"], "endStateId": "Out", "toProcessItemId": ["2025.431577d6-1e81-40a8-8074-893b3183d1de", "2025.431577d6-1e81-40a8-8074-893b3183d1de"], "guid": "a9629b9d-a4e3-40ee-94ab-5aee5a4e1a10", "versionId": "30c50bb3-55d5-4f4f-b7ba-fbbbc8b8fb40", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d7945606-5ca4-4d92-8b73-c5201d8fa0b4", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.cd110212-**************-8ddc39a45c35", "2025.cd110212-**************-8ddc39a45c35"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71", "2025.08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71"], "guid": "1e9d76b9-826c-49cd-9fb8-303705e65588", "versionId": "4f00bc97-59cc-439c-af2b-8f4e8443739d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4d03b375-6b2b-474a-886f-5ddcb6554c2a", "processId": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.9f4d31aa-d83a-4598-8616-d3823b4192ca", "2025.9f4d31aa-d83a-4598-8616-d3823b4192ca"], "endStateId": "Out", "toProcessItemId": ["2025.08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71", "2025.08b5dd3b-cba0-4cfe-86c8-a5eb459a8d71"], "guid": "6cc7d1d1-2c67-44bc-a288-3bae66b5deef", "versionId": "b8c6ab47-e7ad-42aa-a4ed-0e747d93b5ee", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}