<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <participant id="24.940ca5e1-5f71-40c0-8748-89f4b53c2525" name="Closure Managers">
        <lastModified>1693726884594</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <participantId>24.940ca5e1-5f71-40c0-8748-89f4b53c2525</participantId>
        <participantDefinition isNull="true" />
        <simulationGroupSize>2</simulationGroupSize>
        <capacityType>1</capacityType>
        <definitionType>3</definitionType>
        <percentAvailable isNull="true" />
        <percentEfficiency isNull="true" />
        <cost>10.00</cost>
        <currencyCode isNull="true" />
        <image isNull="true" />
        <serviceMembersRef isNull="true" />
        <managersRef isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"team":[{"members":{"UserGroups":[{"name":"BPM_Trade_Finance_CU","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_Trade_FO_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_DC_CU_Supervisor","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_077_CU_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_100_CU_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_200_CU_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_310_CU_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_380_CU_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_599_CU_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_Trade_FO_MKR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_Branch_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails","type":"StandardMembers"}]},"documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"Closure Managers","declaredType":"resource","id":"24.940ca5e1-5f71-40c0-8748-89f4b53c2525"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.w3.org\/1999\/XPath","id":"24.940ca5e1-5f71-40c0-8748-89f4b53c2525"}</jsonData>
        <externalId isNull="true" />
        <description></description>
        <guid>6ddec7ee-276c-4a74-a8c7-b9ab0a5ffc56</guid>
        <versionId>8d91fc81-a61e-4f31-aeca-f2fd791bd1db</versionId>
        <standardMembers>
            <standardMember>
                <type>Group</type>
                <name>BPM_Trade_Finance_CU</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_Trade_FO_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_DC_CU_Supervisor</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_077_CU_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_100_CU_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_200_CU_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_310_CU_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_380_CU_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_599_CU_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_Trade_FO_MKR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_Branch_MNGR</name>
            </standardMember>
        </standardMembers>
        <teamAssignments />
    </participant>
</teamworks>

