{"typeName": "Participant", "count": 32, "objects": [{"id": "24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4", "versionId": "810f835e-ee93-4052-91c7-1db6459ebc54", "name": "Branch compliance Representative Checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.3bf11e60-c652-49be-a22b-e25b3ef79931", "versionId": "44277c59-ff55-499a-87f6-b1e01b622097", "name": "Branch compliance Representative Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.d01bf37a-ec31-4585-aa1e-627d4ea6d162", "versionId": "c1d23dd7-28f1-44b1-81b1-1c4572f16307", "name": "Branch Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.a93a495e-ebfc-42ac-87e6-951e0ec1a877", "versionId": "b9faf003-3246-48ad-8bf7-3ccc7444d8b3", "name": "Branch Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.940ca5e1-5f71-40c0-8748-89f4b53c2525", "versionId": "8d91fc81-a61e-4f31-aeca-f2fd791bd1db", "name": "Closure Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.5f9278b1-621c-429c-9e91-b2b6d2850a95", "versionId": "2d67cf60-8ab0-4d15-9453-ab28e7269ea5", "name": "Closure Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.97bc604d-23d4-4c32-82d9-7c314516642f", "versionId": "6a689023-cea9-4d61-9d13-dbee72e450a7", "name": "DC Central Unit Supervisor", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.13887169-b900-4899-a81f-01e15c00c91f", "versionId": "39b6c253-3b30-4161-89a1-f2a5c0518b15", "name": "DC control Unit Supervisor", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.5a62da37-5a68-4735-b00d-12829a30ea97", "versionId": "049f8938-ee40-402d-b8aa-97ea9195d697", "name": "HUB compliance Representative  Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec", "versionId": "0b943e44-4716-4f4d-8001-267362d691f6", "name": "HUB compliance Representative Checker", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.7858d46a-5678-49eb-bfb0-758ef8ec2464", "versionId": "b76dffa3-b85d-4b2b-852c-1384bef8b49a", "name": "Hub CU Checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.cda98858-50ea-46de-89cf-0293c9ae9a00", "versionId": "999c0d4a-c7a3-44d9-a496-197f86ca5c13", "name": "Hub CU Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.c1a3c19e-010a-4618-9c2f-16ff28c0245b", "versionId": "ba85583f-3103-4dd5-ad65-b85eb880fb93", "name": "HUB CU Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.84c9f3c0-b39d-42f8-9437-500732ca636c", "versionId": "f09ea9f3-ae16-4e92-84d9-ea35683ec9c6", "name": "HUB Execution Checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.219ec5c0-0c40-4f2d-ab50-d2c3b503ff14", "versionId": "d6309310-aec6-4786-b1ba-2d18c888bf92", "name": "HUB Execution Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.51364718-ca09-4922-bed7-2eab4ed3c5a9", "versionId": "cb4e9364-0539-4cd1-b67c-cd29b1e9f073", "name": "HUB Execution Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.ab30711e-0674-4175-bb8c-e87b2f03fa98", "versionId": "4ddad2c1-bdac-420c-92ac-358710b1be2b", "name": "ODC Collection Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.8ab3fca1-29f3-4ddd-892d-55f78a0f69bc", "versionId": "80363967-8022-49dd-a6e3-7b57464c83df", "name": "ODC Collection Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.c1950331-89e8-4fb3-825b-52bc99292612", "versionId": "6733a439-ecaf-42c2-af7c-0927c7d22049", "name": "ODC compliance Representative checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.f542e8cc-d0f3-4179-8fd0-1db0628a8b36", "versionId": "64c78fc6-021c-4feb-8d9d-b6262eb12285", "name": "ODC compliance Representative Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.b469f9ac-8dba-4742-bc4d-38c0f4f28ce3", "versionId": "55b18b45-ec0f-4bb9-bfa5-bd6d9526e9a4", "name": "ODC Creation Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.e7977935-dba2-437e-9671-4ec41d29e437", "versionId": "dfd1d9ac-aebb-430b-b7ec-0e4d1f390b1c", "name": "ODC initators", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.4e372b2d-494e-42ad-a5af-cb2e77259a38", "versionId": "71db332b-8285-4518-adb4-1262cdabf102", "name": "ODC Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.f66dc7c7-4ada-40ba-ad62-7deff8804a27", "versionId": "fce69022-293b-488f-b27c-e533a5c1dae9", "name": "ODC Position Screen Initiators", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.47d810bd-7938-44a3-9e7f-3bdddc0d4dff", "versionId": "d7821a39-b4ab-4795-9239-ff57f7d12c54", "name": "Reversal Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.87350f86-1636-41a7-80ca-60c055fb71f2", "versionId": "5915172e-fe34-4eed-9307-1f0fdde38d45", "name": "Reversal Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.33723a74-9421-41a6-9d94-5464d6689d7d", "versionId": "36fffbb3-e86f-4cfc-ab00-2075df9ab424", "name": "Trade Compliance Checker", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.55c1fe56-ce36-4b4b-87bd-380022cde382", "versionId": "8b8cf17f-db58-4284-86c7-f2ad044d9901", "name": "Trade compliance Maker", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.abae0138-0c20-4914-b2ec-523594f4a93d", "versionId": "352e390c-1ac6-426d-b0e2-6ddda4a012c0", "name": "Trade compliance Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.69e88363-c139-41f2-9dbf-64274c11a0dc", "versionId": "aa8d6d8c-46b0-4bab-92ba-5b71ff9093f4", "name": "Trade finance Control Unit", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89", "versionId": "f8d25f3b-0b03-40df-beee-9a37fd159eaa", "name": "Trade FO Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.ec65db21-fa8b-430c-9116-176de416229a", "versionId": "af559561-b69f-477c-ab2b-4b4c3fde2004", "name": "Trade FO Managers", "type": "participant", "typeName": "Participant", "details": {}}]}