<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b1a705db-e502-470d-ad30-b08231994382" name="Get Party Details">
        <lastModified>1700575302116</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.29967c03-ada7-4580-8450-6fad512d554e</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>71abbf2d-d971-4f5a-b554-a489d314ff3d</guid>
        <versionId>685b456d-8331-4e15-8435-507759e74816</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50f3" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.bd69d149-fee3-4278-9027-e71f93002131"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":110,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a0044a02-eaa7-4d70-8da1-21d8c7343c0a"},{"incoming":["6ea16192-b086-4e45-a438-307105db4483","58848525-ab10-4f69-8b0e-7fb14e25a39c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-4a7"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c6d3c549-3382-41cc-bd2f-3f274e2a0a76"},{"targetRef":"29967c03-ada7-4580-8450-6fad512d554e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Input","declaredType":"sequenceFlow","id":"2027.bd69d149-fee3-4278-9027-e71f93002131","sourceRef":"a0044a02-eaa7-4d70-8da1-21d8c7343c0a"},{"startQuantity":1,"outgoing":["60cfb855-0e40-4492-badb-8a007c07dbcb"],"incoming":["2027.bd69d149-fee3-4278-9027-e71f93002131"],"extensionElements":{"postAssignmentScript":["tw.local.results = new tw.object.CustomerFullDetails();\r\ntw.local.results.customerAddress = new tw.object.CustomerAddress();\r\nif (tw.local.isSuccessful) {\r\n\ttw.local.results = tw.local.customerFullDetails;\r\n}"],"nodeVisualInfo":[{"width":95,"x":303,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.customerCif = tw.local.data;"],"activityType":["CalledProcess"]},"name":"Party Details","dataInputAssociation":[{"targetRef":"2055.9e66331e-0f98-44e0-b836-7f39c9a6d317","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.4fcde37d-b029-4d8e-ae28-d26a621479e6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.40e09d4c-cff9-4d38-bd81-0995df08be6f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.customerCif"]}}]},{"targetRef":"2055.b540e2f5-2008-4705-b4e1-7edcf2a379df","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.instanceID"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"29967c03-ada7-4580-8450-6fad512d554e","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","declaredType":"TFormalExpression","content":["tw.local.customerFullDetails"]}}],"sourceRef":["2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.3d943bb5-aec9-4b29-862f-735a93741afa"]}],"calledElement":"1.fd9b955b-0237-4cbe-86d6-cd0d295550aa"},{"targetRef":"846582f3-919c-4b17-bd56-5f89d9c760df","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2a57"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To is Successful","declaredType":"sequenceFlow","id":"60cfb855-0e40-4492-badb-8a007c07dbcb","sourceRef":"29967c03-ada7-4580-8450-6fad512d554e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.toolkit.NBEINTT.CustomerFullDetails();\nautoObject.customerNo = \"\";\nautoObject.customerType = \"\";\nautoObject.customerName = \"\";\nautoObject.customerCategory = \"\";\nautoObject.customerBranch = \"\";\nautoObject.customerStatus = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.customerStatus.name = \"\";\nautoObject.customerStatus.value = \"\";\nautoObject.privateCustomer = \"\";\nautoObject.faciliyBranchCode = \"\";\nautoObject.frozen = false;\nautoObject.deceased = false;\nautoObject.sName = \"\";\nautoObject.customerFullName = \"\";\nautoObject.nationality = \"\";\nautoObject.country = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.country.name = \"\";\nautoObject.country.value = \"\";\nautoObject.customerAddress = new tw.object.toolkit.NBEINTT.CustomerAddress();\nautoObject.customerAddress.addressLine1 = \"\";\nautoObject.customerAddress.addressLine2 = \"\";\nautoObject.customerAddress.addressLine3 = \"\";\nautoObject.customerAddress.addressLine4 = \"\";\nautoObject.customerAddress.customerNO = \"\";\nautoObject.fullName = \"\";\nautoObject.customerArabicName = \"\";\nautoObject.customerEnglishName = \"\";\nautoObject.customerNationalID = \"\";\nautoObject.tradingTitle = \"\";\nautoObject.commercialRegisterNo = \"\";\nautoObject.cardTaxNo = \"\";\nautoObject.businessActivity = \"\";\nautoObject.arabicName = \"\";\nautoObject.EnglishName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","name":"customerFullDetails","isCollection":false,"declaredType":"dataObject","id":"2056.b317ce7e-fc20-4b8c-b3b5-a4855e708031"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.ebc94f00-2d4e-40e9-b6df-c0c3f8d66e96"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"prefix","isCollection":false,"declaredType":"dataObject","id":"2056.0d38c8d9-2b41-433f-8bdf-6caf2848d393"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.de87e848-0ce5-4b64-bd59-c6db78f306b0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestAppID","isCollection":false,"declaredType":"dataObject","id":"2056.1373a5b6-4a0c-4a62-9d5d-5a96e16eef95"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"snapshot","isCollection":false,"declaredType":"dataObject","id":"2056.bb1126c0-afe1-422d-bc13-0107cd93b791"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"declaredType":"dataObject","id":"2056.5f801e2a-6a23-4186-8740-45f0418b8a5f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.72487a51-f8c6-46fc-af35-afd9fef4f6d8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.b6950350-0033-431a-8525-7f7191cdf387"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.fc119eb8-f59c-4003-8bee-adce21fef5ae"},{"parallelMultiple":false,"outgoing":["1f9d947f-c71c-4453-8e65-1d6d2416b11a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"692f511d-9765-4169-94db-8deaf8ac2b6c"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"1070e9cd-980f-48bb-904f-f0743ba1e71b","otherAttributes":{"eventImplId":"af2a221a-6c75-4858-8a05-faf2d2e7be09"}}],"attachedToRef":"29967c03-ada7-4580-8450-6fad512d554e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":338,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"8e6243cc-db9c-4d7f-92c4-cdbabd18a6ed","outputSet":{}},{"outgoing":["6ea16192-b086-4e45-a438-307105db4483","db521322-ecbf-4227-8d7e-8d754754d24d"],"incoming":["60cfb855-0e40-4492-badb-8a007c07dbcb"],"default":"6ea16192-b086-4e45-a438-307105db4483","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":456,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Successful","declaredType":"exclusiveGateway","id":"846582f3-919c-4b17-bd56-5f89d9c760df"},{"targetRef":"c6d3c549-3382-41cc-bd2f-3f274e2a0a76","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"6ea16192-b086-4e45-a438-307105db4483","sourceRef":"846582f3-919c-4b17-bd56-5f89d9c760df"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerCif","isCollection":false,"declaredType":"dataObject","id":"2056.acbd57f9-9a51-4849-b02a-052db09152ff"},{"startQuantity":1,"outgoing":["58848525-ab10-4f69-8b0e-7fb14e25a39c"],"incoming":["1f9d947f-c71c-4453-8e65-1d6d2416b11a","db521322-ecbf-4227-8d7e-8d754754d24d"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":473,"y":202,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.serviceFlow.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"18bf2baf-a9b2-44ce-8c61-b45eee6b8201","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"18bf2baf-a9b2-44ce-8c61-b45eee6b8201","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"1f9d947f-c71c-4453-8e65-1d6d2416b11a","sourceRef":"8e6243cc-db9c-4d7f-92c4-cdbabd18a6ed"},{"targetRef":"18bf2baf-a9b2-44ce-8c61-b45eee6b8201","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"db521322-ecbf-4227-8d7e-8d754754d24d","sourceRef":"846582f3-919c-4b17-bd56-5f89d9c760df"},{"targetRef":"c6d3c549-3382-41cc-bd2f-3f274e2a0a76","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"58848525-ab10-4f69-8b0e-7fb14e25a39c","sourceRef":"18bf2baf-a9b2-44ce-8c61-b45eee6b8201"}],"laneSet":[{"id":"e006fc26-0481-40f8-9955-0fe15567be21","lane":[{"flowNodeRef":["a0044a02-eaa7-4d70-8da1-21d8c7343c0a","c6d3c549-3382-41cc-bd2f-3f274e2a0a76","29967c03-ada7-4580-8450-6fad512d554e","8e6243cc-db9c-4d7f-92c4-cdbabd18a6ed","846582f3-919c-4b17-bd56-5f89d9c760df","18bf2baf-a9b2-44ce-8c61-b45eee6b8201"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"1e688c3b-9083-4bd8-b6ff-17b234de0eae","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[false]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Party Details","declaredType":"process","id":"1.b1a705db-e502-470d-ad30-b08231994382","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.060ae176-de84-4fc8-8fcb-a0e3cdef220e"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.4f15661f-9658-409d-9bbb-8fbe130d3d8a"}],"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.060ae176-de84-4fc8-8fcb-a0e3cdef220e","2055.4f15661f-9658-409d-9bbb-8fbe130d3d8a"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"06316421\"\r\n\/\/\"06948425\"\r\n\/\/\"02366014\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.81eb497e-8a97-4ef4-9180-e95600b25604"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.81eb497e-8a97-4ef4-9180-e95600b25604</processParameterId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"06316421"&#xD;
//"06948425"&#xD;
//"02366014"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3542eb63-1364-4538-a554-d88ab729dfc9</guid>
            <versionId>803e3321-9713-4bdc-b1a5-cfb5d87737d9</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.060ae176-de84-4fc8-8fcb-a0e3cdef220e</processParameterId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8f0ef91a-a7ed-4f01-90e3-6333c3cf5f00</guid>
            <versionId>7a95cdb8-6c33-4052-b4ae-f56e17d0ee1a</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4f15661f-9658-409d-9bbb-8fbe130d3d8a</processParameterId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b50b2c25-d103-40ec-9271-7076b57abd51</guid>
            <versionId>797f5a19-c6ce-432e-926f-280553f19da4</versionId>
        </processParameter>
        <processVariable name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b317ce7e-fc20-4b8c-b3b5-a4855e708031</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.toolkit.NBEINTT.CustomerFullDetails();
autoObject.customerNo = "";
autoObject.customerType = "";
autoObject.customerName = "";
autoObject.customerCategory = "";
autoObject.customerBranch = "";
autoObject.customerStatus = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.customerStatus.name = "";
autoObject.customerStatus.value = "";
autoObject.privateCustomer = "";
autoObject.faciliyBranchCode = "";
autoObject.frozen = false;
autoObject.deceased = false;
autoObject.sName = "";
autoObject.customerFullName = "";
autoObject.nationality = "";
autoObject.country = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.country.name = "";
autoObject.country.value = "";
autoObject.customerAddress = new tw.object.toolkit.NBEINTT.CustomerAddress();
autoObject.customerAddress.addressLine1 = "";
autoObject.customerAddress.addressLine2 = "";
autoObject.customerAddress.addressLine3 = "";
autoObject.customerAddress.addressLine4 = "";
autoObject.customerAddress.customerNO = "";
autoObject.fullName = "";
autoObject.customerArabicName = "";
autoObject.customerEnglishName = "";
autoObject.customerNationalID = "";
autoObject.tradingTitle = "";
autoObject.commercialRegisterNo = "";
autoObject.cardTaxNo = "";
autoObject.businessActivity = "";
autoObject.arabicName = "";
autoObject.EnglishName = "";
autoObject</defaultValue>
            <guid>ec73211d-3211-425a-aa23-d2682fe3d9e0</guid>
            <versionId>d2df1110-9d81-4335-beed-19c72a7961c4</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ebc94f00-2d4e-40e9-b6df-c0c3f8d66e96</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7384f688-ccd0-4eb0-827b-3d6f8131740b</guid>
            <versionId>b78ba68b-0958-479a-b928-05db5224fe80</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0d38c8d9-2b41-433f-8bdf-6caf2848d393</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>24a1b7b3-38f8-4bc8-975c-6429d549b79b</guid>
            <versionId>22dc2ea2-4898-4ee8-9761-dc418d20741f</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.de87e848-0ce5-4b64-bd59-c6db78f306b0</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>44b96016-f6b3-4181-9ddd-295af9e95837</guid>
            <versionId>cdb76965-7480-4095-b9ff-554563ae905c</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1373a5b6-4a0c-4a62-9d5d-5a96e16eef95</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3f038dc0-7c1c-4f34-85f4-e64395c04660</guid>
            <versionId>d25a5fc3-5da9-4478-b9d0-da4d72bff5fb</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bb1126c0-afe1-422d-bc13-0107cd93b791</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a7240f4d-6054-464a-ad31-7e86adedc205</guid>
            <versionId>c02dae97-348d-47ca-831d-0b9d23877ce4</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5f801e2a-6a23-4186-8740-45f0418b8a5f</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a533f193-2275-4aa8-a840-cb5457462735</guid>
            <versionId>5e25133d-a487-4c8d-beb1-d46c3551ea17</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.72487a51-f8c6-46fc-af35-afd9fef4f6d8</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6e4a3a9a-b85b-4def-95fb-10be584610de</guid>
            <versionId>da539426-35cf-4568-81e2-979fec86b837</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b6950350-0033-431a-8525-7f7191cdf387</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>52d16092-f472-475b-bd06-423b0d1c5a36</guid>
            <versionId>3ca58231-aafd-47f6-bad6-7471b7bfb2ea</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fc119eb8-f59c-4003-8bee-adce21fef5ae</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e513ef32-2af4-4359-86b7-3a5b78d81bbf</guid>
            <versionId>4128a21a-0930-4a19-bae2-f17f5d1a5857</versionId>
        </processVariable>
        <processVariable name="customerCif">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.acbd57f9-9a51-4849-b02a-052db09152ff</processVariableId>
            <description isNull="true" />
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5d522a72-e83f-4017-9843-630cdab8e6af</guid>
            <versionId>ec7c29af-663f-4b91-b838-59249ee0def7</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c6d3c549-3382-41cc-bd2f-3f274e2a0a76</processItemId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2727eab5-650f-4ca4-aef3-04dcf6d0c28d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-4a7</guid>
            <versionId>1c648b23-e750-4619-9fc8-10d8b067ec85</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2727eab5-650f-4ca4-aef3-04dcf6d0c28d</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>d7a06681-b3ef-42b7-bee0-b634af24218f</guid>
                <versionId>93e85566-b097-4497-beb8-dfc51cf1d0a7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.18bf2baf-a9b2-44ce-8c61-b45eee6b8201</processItemId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.dcf31a61-6369-4bb4-a7d7-bf54b44e9de0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-5125</guid>
            <versionId>8743e4b0-caa3-4911-be6a-75c9390f1ce6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="473" y="202">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.dcf31a61-6369-4bb4-a7d7-bf54b44e9de0</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>d31325c9-6949-40c6-915b-352445a7a3fe</guid>
                <versionId>7d2cdf1e-6865-4221-b458-bce0ad2d445b</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.90825c30-5902-4cb3-b1cf-d775d6268588</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.dcf31a61-6369-4bb4-a7d7-bf54b44e9de0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6cdc57a1-eec5-4eea-9223-e3eec5a6b4a5</guid>
                    <versionId>282a3c73-21ca-4c9a-bfb0-b5bf1b07a486</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6f15f19c-beb8-4b3e-8283-22677ed11723</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.dcf31a61-6369-4bb4-a7d7-bf54b44e9de0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4e47e159-e364-410d-9b66-f2004cb32063</guid>
                    <versionId>889eddae-ef75-4a9d-8d2c-14961c4004c9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e08b5d82-c240-4018-a782-86e68dc8b3f4</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.dcf31a61-6369-4bb4-a7d7-bf54b44e9de0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.serviceFlow.name</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c672df45-f3e9-4b8c-941f-6116fd74f6f5</guid>
                    <versionId>d99811c5-4177-4c4c-a238-678960e527e8</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.846582f3-919c-4b17-bd56-5f89d9c760df</processItemId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.61973609-3c30-4717-bbf5-513b4ef4c545</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-4a9</guid>
            <versionId>cc47f492-d509-4960-9cae-a6ed1cf03171</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="456" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.61973609-3c30-4717-bbf5-513b4ef4c545</switchId>
                <guid>23d67d54-fd11-4134-85fa-49ec8e311edf</guid>
                <versionId>c135cf07-55b4-4e51-858a-219a4e34a1cd</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.cd3de5d6-735c-441c-b8f0-d96cdadb7528</switchConditionId>
                    <switchId>3013.61973609-3c30-4717-bbf5-513b4ef4c545</switchId>
                    <seq>1</seq>
                    <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50f4</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>9a053165-1fa7-456e-924e-ffd05dd01a37</guid>
                    <versionId>1b874513-78b2-41e8-87d4-6ee38588008b</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.29967c03-ada7-4580-8450-6fad512d554e</processItemId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <name>Party Details</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.18bf2baf-a9b2-44ce-8c61-b45eee6b8201</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-4aa</guid>
            <versionId>e50125a5-7562-4971-b691-c0933c3cc823</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.207d3186-77da-40bf-9586-b8e3c0427f7b</processItemPrePostId>
                <processItemId>2025.29967c03-ada7-4580-8450-6fad512d554e</processItemId>
                <location>2</location>
                <script>tw.local.results = new tw.object.CustomerFullDetails();&#xD;
tw.local.results.customerAddress = new tw.object.CustomerAddress();&#xD;
if (tw.local.isSuccessful) {&#xD;
	tw.local.results = tw.local.customerFullDetails;&#xD;
}</script>
                <guid>edb933e5-dfa2-4a4c-9648-80801fb1acdd</guid>
                <versionId>192dc14f-0245-48b6-bd34-a752702db188</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.590ed673-0567-43d8-96cc-d2a822e46acd</processItemPrePostId>
                <processItemId>2025.29967c03-ada7-4580-8450-6fad512d554e</processItemId>
                <location>1</location>
                <script>tw.local.customerCif = tw.local.data;</script>
                <guid>6e3190db-b4f5-4c73-9503-62d51b471187</guid>
                <versionId>263d09df-edd4-479a-8bbe-ffbbcf63431c</versionId>
            </processPrePosts>
            <layoutData x="303" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-5125</errorHandlerItem>
                <errorHandlerItemId>2025.18bf2baf-a9b2-44ce-8c61-b45eee6b8201</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.fd9b955b-0237-4cbe-86d6-cd0d295550aa</attachedProcessRef>
                <guid>2ace9346-a1e1-4f28-a0d6-1b8b648f6f3d</guid>
                <versionId>0cb3513a-dcf1-4c95-8abd-36ca2bc6df8f</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ad229546-b34b-4cea-8b37-e5a29bff0bf4</parameterMappingId>
                    <processParameterId>2055.40e09d4c-cff9-4d38-bd81-0995df08be6f</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a747f888-4cb8-479c-8b14-9845cad80b28</guid>
                    <versionId>3cd04083-b7eb-4630-9623-285fc2bf7963</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.953ee7d8-9742-490c-a874-0755f319839a</parameterMappingId>
                    <processParameterId>2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>81d87638-bdd6-4d02-afbd-6c315505a633</guid>
                    <versionId>4e14f100-1b4e-42a0-be32-7e1a5d50dfb2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6f195514-8115-4bf3-adc5-3c0f45b2b6a4</parameterMappingId>
                    <processParameterId>2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>7ad3a001-a560-4625-8dcd-1f6a811536e0</guid>
                    <versionId>6d2552fb-08f6-43d5-ad23-6208723ff104</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f0e8f969-70f9-4191-a408-dc332b4bbaf3</parameterMappingId>
                    <processParameterId>2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8d930d45-7865-4415-b519-f03bc2348e79</guid>
                    <versionId>725445a1-6bc6-48bf-8355-2c071ccfb745</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerCif">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.10ff7826-e4c4-47a0-9be8-8f32655c8e90</parameterMappingId>
                    <processParameterId>2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerCif</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>627fbcc4-e340-44cc-af32-595a7cd0f95b</guid>
                    <versionId>aa0d8cd9-277c-4b0c-859b-7c7274ed2d3e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f1296d83-7ae7-479f-8368-adcff4d3f81a</parameterMappingId>
                    <processParameterId>2055.4fcde37d-b029-4d8e-ae28-d26a621479e6</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f5b30764-68e1-48c9-94ed-c3a7bd7d1c92</guid>
                    <versionId>af51abe3-2e73-4b4e-8ada-d4b8d8152e0f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b29c3153-170a-4cec-930c-5b5214c1751b</parameterMappingId>
                    <processParameterId>2055.9e66331e-0f98-44e0-b836-7f39c9a6d317</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b699a4c9-855b-470e-9e71-b7d73b19e603</guid>
                    <versionId>b380a2a9-7a2f-4bc0-bcb7-e4654ea78ff1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b747bb34-ee0a-46f6-b8f7-fa6dd23503f9</parameterMappingId>
                    <processParameterId>2055.b540e2f5-2008-4705-b4e1-7edcf2a379df</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.instanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>062b9ecd-0abc-428a-807b-b3aa014f5e88</guid>
                    <versionId>b704aa70-4cf8-4266-8e24-bdcccaa9415c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.73af4a0a-2232-497d-a878-1f1e2ed58448</parameterMappingId>
                    <processParameterId>2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e0c821d2-623c-4943-98ea-431b821859dc</guid>
                    <versionId>bc048812-e9a0-4fcf-831b-699a1ecae333</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fabcef03-7904-4d3c-947a-6d097f8eea0a</parameterMappingId>
                    <processParameterId>2055.3d943bb5-aec9-4b29-862f-735a93741afa</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>41bed482-b103-48cc-b064-1ee8313e28af</guid>
                    <versionId>c5b7f870-e178-4359-b6df-bd682be80639</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerFullDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b72f6583-40e5-452b-8b90-baf23bead55c</parameterMappingId>
                    <processParameterId>2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac</processParameterId>
                    <parameterMappingParentId>3012.885553a5-f731-4bfe-b3ab-f21154a07324</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerFullDetails</value>
                    <classRef>/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>327a94b0-2ea8-4900-947c-1d7f509f3dcd</guid>
                    <versionId>fff39203-c0e2-4090-9bd1-11650dd3cdb4</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.29967c03-ada7-4580-8450-6fad512d554e</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="110" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Party Details" id="1.b1a705db-e502-470d-ad30-b08231994382" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>false</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.81eb497e-8a97-4ef4-9180-e95600b25604">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"06316421"&#xD;
//"06948425"&#xD;
//"02366014"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.060ae176-de84-4fc8-8fcb-a0e3cdef220e" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.4f15661f-9658-409d-9bbb-8fbe130d3d8a" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.060ae176-de84-4fc8-8fcb-a0e3cdef220e</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.4f15661f-9658-409d-9bbb-8fbe130d3d8a</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="e006fc26-0481-40f8-9955-0fe15567be21">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="1e688c3b-9083-4bd8-b6ff-17b234de0eae" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a0044a02-eaa7-4d70-8da1-21d8c7343c0a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c6d3c549-3382-41cc-bd2f-3f274e2a0a76</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>29967c03-ada7-4580-8450-6fad512d554e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8e6243cc-db9c-4d7f-92c4-cdbabd18a6ed</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>846582f3-919c-4b17-bd56-5f89d9c760df</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>18bf2baf-a9b2-44ce-8c61-b45eee6b8201</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a0044a02-eaa7-4d70-8da1-21d8c7343c0a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="110" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.bd69d149-fee3-4278-9027-e71f93002131</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c6d3c549-3382-41cc-bd2f-3f274e2a0a76">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-4a7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6ea16192-b086-4e45-a438-307105db4483</ns16:incoming>
                        
                        
                        <ns16:incoming>58848525-ab10-4f69-8b0e-7fb14e25a39c</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a0044a02-eaa7-4d70-8da1-21d8c7343c0a" targetRef="29967c03-ada7-4580-8450-6fad512d554e" name="To Set Input" id="2027.bd69d149-fee3-4278-9027-e71f93002131">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.fd9b955b-0237-4cbe-86d6-cd0d295550aa" name="Party Details" id="29967c03-ada7-4580-8450-6fad512d554e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="303" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>tw.local.results = new tw.object.CustomerFullDetails();&#xD;
tw.local.results.customerAddress = new tw.object.CustomerAddress();&#xD;
if (tw.local.isSuccessful) {&#xD;
	tw.local.results = tw.local.customerFullDetails;&#xD;
}</ns3:postAssignmentScript>
                            
                            
                            <ns3:preAssignmentScript>tw.local.customerCif = tw.local.data;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.bd69d149-fee3-4278-9027-e71f93002131</ns16:incoming>
                        
                        
                        <ns16:outgoing>60cfb855-0e40-4492-badb-8a007c07dbcb</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9e66331e-0f98-44e0-b836-7f39c9a6d317</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4fcde37d-b029-4d8e-ae28-d26a621479e6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.40e09d4c-cff9-4d38-bd81-0995df08be6f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.customerCif</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b540e2f5-2008-4705-b4e1-7edcf2a379df</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651">tw.local.customerFullDetails</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.3d943bb5-aec9-4b29-862f-735a93741afa</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="29967c03-ada7-4580-8450-6fad512d554e" targetRef="846582f3-919c-4b17-bd56-5f89d9c760df" name="To is Successful" id="60cfb855-0e40-4492-badb-8a007c07dbcb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a57</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651" isCollection="false" name="customerFullDetails" id="2056.b317ce7e-fc20-4b8c-b3b5-a4855e708031">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.toolkit.NBEINTT.CustomerFullDetails();
autoObject.customerNo = "";
autoObject.customerType = "";
autoObject.customerName = "";
autoObject.customerCategory = "";
autoObject.customerBranch = "";
autoObject.customerStatus = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.customerStatus.name = "";
autoObject.customerStatus.value = "";
autoObject.privateCustomer = "";
autoObject.faciliyBranchCode = "";
autoObject.frozen = false;
autoObject.deceased = false;
autoObject.sName = "";
autoObject.customerFullName = "";
autoObject.nationality = "";
autoObject.country = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.country.name = "";
autoObject.country.value = "";
autoObject.customerAddress = new tw.object.toolkit.NBEINTT.CustomerAddress();
autoObject.customerAddress.addressLine1 = "";
autoObject.customerAddress.addressLine2 = "";
autoObject.customerAddress.addressLine3 = "";
autoObject.customerAddress.addressLine4 = "";
autoObject.customerAddress.customerNO = "";
autoObject.fullName = "";
autoObject.customerArabicName = "";
autoObject.customerEnglishName = "";
autoObject.customerNationalID = "";
autoObject.tradingTitle = "";
autoObject.commercialRegisterNo = "";
autoObject.cardTaxNo = "";
autoObject.businessActivity = "";
autoObject.arabicName = "";
autoObject.EnglishName = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.ebc94f00-2d4e-40e9-b6df-c0c3f8d66e96" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.0d38c8d9-2b41-433f-8bdf-6caf2848d393" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.de87e848-0ce5-4b64-bd59-c6db78f306b0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.1373a5b6-4a0c-4a62-9d5d-5a96e16eef95" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.bb1126c0-afe1-422d-bc13-0107cd93b791" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.5f801e2a-6a23-4186-8740-45f0418b8a5f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.72487a51-f8c6-46fc-af35-afd9fef4f6d8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.b6950350-0033-431a-8525-7f7191cdf387" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.fc119eb8-f59c-4003-8bee-adce21fef5ae" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="29967c03-ada7-4580-8450-6fad512d554e" parallelMultiple="false" name="Error" id="8e6243cc-db9c-4d7f-92c4-cdbabd18a6ed">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="338" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1f9d947f-c71c-4453-8e65-1d6d2416b11a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="692f511d-9765-4169-94db-8deaf8ac2b6c" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="1070e9cd-980f-48bb-904f-f0743ba1e71b" eventImplId="af2a221a-6c75-4858-8a05-faf2d2e7be09">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:exclusiveGateway default="6ea16192-b086-4e45-a438-307105db4483" name="is Successful" id="846582f3-919c-4b17-bd56-5f89d9c760df">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="456" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>60cfb855-0e40-4492-badb-8a007c07dbcb</ns16:incoming>
                        
                        
                        <ns16:outgoing>6ea16192-b086-4e45-a438-307105db4483</ns16:outgoing>
                        
                        
                        <ns16:outgoing>db521322-ecbf-4227-8d7e-8d754754d24d</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="846582f3-919c-4b17-bd56-5f89d9c760df" targetRef="c6d3c549-3382-41cc-bd2f-3f274e2a0a76" name="To Script Task" id="6ea16192-b086-4e45-a438-307105db4483">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="customerCif" id="2056.acbd57f9-9a51-4849-b02a-052db09152ff" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exception Handling" id="18bf2baf-a9b2-44ce-8c61-b45eee6b8201">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="473" y="202" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1f9d947f-c71c-4453-8e65-1d6d2416b11a</ns16:incoming>
                        
                        
                        <ns16:incoming>db521322-ecbf-4227-8d7e-8d754754d24d</ns16:incoming>
                        
                        
                        <ns16:outgoing>58848525-ab10-4f69-8b0e-7fb14e25a39c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.serviceFlow.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8e6243cc-db9c-4d7f-92c4-cdbabd18a6ed" targetRef="18bf2baf-a9b2-44ce-8c61-b45eee6b8201" name="To Exception Handling" id="1f9d947f-c71c-4453-8e65-1d6d2416b11a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="846582f3-919c-4b17-bd56-5f89d9c760df" targetRef="18bf2baf-a9b2-44ce-8c61-b45eee6b8201" name="To Exception Handling" id="db521322-ecbf-4227-8d7e-8d754754d24d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="18bf2baf-a9b2-44ce-8c61-b45eee6b8201" targetRef="c6d3c549-3382-41cc-bd2f-3f274e2a0a76" name="To End" id="58848525-ab10-4f69-8b0e-7fb14e25a39c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.60cfb855-0e40-4492-badb-8a007c07dbcb</processLinkId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.29967c03-ada7-4580-8450-6fad512d554e</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a57</endStateId>
            <toProcessItemId>2025.846582f3-919c-4b17-bd56-5f89d9c760df</toProcessItemId>
            <guid>b4a0091e-fb8a-4894-84e3-17a6d2f465ab</guid>
            <versionId>22613b21-96bb-4cc9-b6d5-e198e1d36a2c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.29967c03-ada7-4580-8450-6fad512d554e</fromProcessItemId>
            <toProcessItemId>2025.846582f3-919c-4b17-bd56-5f89d9c760df</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6ea16192-b086-4e45-a438-307105db4483</processLinkId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.846582f3-919c-4b17-bd56-5f89d9c760df</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.c6d3c549-3382-41cc-bd2f-3f274e2a0a76</toProcessItemId>
            <guid>59a0c591-11a5-4bcb-8224-1a06b4bb63c9</guid>
            <versionId>57d8bb3d-a683-466a-8819-64192a6b7424</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.846582f3-919c-4b17-bd56-5f89d9c760df</fromProcessItemId>
            <toProcessItemId>2025.c6d3c549-3382-41cc-bd2f-3f274e2a0a76</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.58848525-ab10-4f69-8b0e-7fb14e25a39c</processLinkId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.18bf2baf-a9b2-44ce-8c61-b45eee6b8201</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.c6d3c549-3382-41cc-bd2f-3f274e2a0a76</toProcessItemId>
            <guid>ae1a8ce4-d5c2-4e2c-8c42-c0f6e258f22d</guid>
            <versionId>a3c79925-4058-43b9-8b66-851588fb63a3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.18bf2baf-a9b2-44ce-8c61-b45eee6b8201</fromProcessItemId>
            <toProcessItemId>2025.c6d3c549-3382-41cc-bd2f-3f274e2a0a76</toProcessItemId>
        </link>
        <link name="To Exception Handling">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.db521322-ecbf-4227-8d7e-8d754754d24d</processLinkId>
            <processId>1.b1a705db-e502-470d-ad30-b08231994382</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.846582f3-919c-4b17-bd56-5f89d9c760df</fromProcessItemId>
            <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50f4</endStateId>
            <toProcessItemId>2025.18bf2baf-a9b2-44ce-8c61-b45eee6b8201</toProcessItemId>
            <guid>65884752-a91d-4252-b5b9-0600b3bcbda5</guid>
            <versionId>e50a1d06-eae3-4b22-9c95-8380042a7020</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.846582f3-919c-4b17-bd56-5f89d9c760df</fromProcessItemId>
            <toProcessItemId>2025.18bf2baf-a9b2-44ce-8c61-b45eee6b8201</toProcessItemId>
        </link>
    </process>
</teamworks>

