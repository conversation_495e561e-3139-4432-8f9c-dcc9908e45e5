<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <participant id="24.3bf11e60-c652-49be-a22b-e25b3ef79931" name="Branch compliance Representative Manager">
        <lastModified>1691144180817</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <participantId>24.3bf11e60-c652-49be-a22b-e25b3ef79931</participantId>
        <participantDefinition isNull="true" />
        <simulationGroupSize>2</simulationGroupSize>
        <capacityType>1</capacityType>
        <definitionType>3</definitionType>
        <percentAvailable isNull="true" />
        <percentEfficiency isNull="true" />
        <cost>10.00</cost>
        <currencyCode isNull="true" />
        <image isNull="true" />
        <serviceMembersRef isNull="true" />
        <managersRef isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"team":[{"members":{"Users":[{"name":"heba","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"abdelrahman.saleh","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"somaia","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}],"UserGroups":[{"name":"BPM_ODC_BR_COMP_REP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails","type":"StandardMembers"}]},"documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"Branch compliance Representative Manager","declaredType":"resource","id":"24.3bf11e60-c652-49be-a22b-e25b3ef79931"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.w3.org\/1999\/XPath","id":"24.3bf11e60-c652-49be-a22b-e25b3ef79931"}</jsonData>
        <externalId isNull="true" />
        <description></description>
        <guid>guid:d694a63221635d5b:6baf87c4:18969a9a6e2:771e</guid>
        <versionId>44277c59-ff55-499a-87f6-b1e01b622097</versionId>
        <standardMembers>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_BR_COMP_REP_MNGR</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>heba</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>abdelrahman.saleh</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>somaia</name>
            </standardMember>
        </standardMembers>
        <teamAssignments />
    </participant>
</teamworks>

