<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <participant id="24.69e88363-c139-41f2-9dbf-64274c11a0dc" name="Trade finance Control Unit">
        <lastModified>1692265178796</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <participantId>24.69e88363-c139-41f2-9dbf-64274c11a0dc</participantId>
        <participantDefinition isNull="true" />
        <simulationGroupSize>2</simulationGroupSize>
        <capacityType>1</capacityType>
        <definitionType>3</definitionType>
        <percentAvailable isNull="true" />
        <percentEfficiency isNull="true" />
        <cost>10.00</cost>
        <currencyCode isNull="true" />
        <image isNull="true" />
        <serviceMembersRef isNull="true" />
        <managersRef isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"team":[{"members":{"Users":[{"name":"heba","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"abdelrahman.saleh","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"somaia","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}],"UserGroups":[{"name":"BPM_Trade_Finance_CU","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails","type":"StandardMembers"}]},"documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"Trade finance Control Unit","declaredType":"resource","id":"24.69e88363-c139-41f2-9dbf-64274c11a0dc"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.w3.org\/1999\/XPath","id":"24.69e88363-c139-41f2-9dbf-64274c11a0dc"}</jsonData>
        <externalId isNull="true" />
        <description></description>
        <guid>guid:d694a63221635d5b:6baf87c4:1896e72cf4d:-7c8e</guid>
        <versionId>aa8d6d8c-46b0-4bab-92ba-5b71ff9093f4</versionId>
        <standardMembers>
            <standardMember>
                <type>Group</type>
                <name>BPM_Trade_Finance_CU</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>heba</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>abdelrahman.saleh</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>somaia</name>
            </standardMember>
        </standardMembers>
        <teamAssignments />
    </participant>
</teamworks>

