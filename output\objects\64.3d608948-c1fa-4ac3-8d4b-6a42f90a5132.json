{"id": "64.3d608948-c1fa-4ac3-8d4b-6a42f90a5132", "versionId": "5d16a9a4-8300-453d-869f-94ee95145d51", "name": "ODC Parties CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (!this.context.options.isReturned.get(\"value\")) {\t\r\r\n\tthis.ui.get(\"GetCustomerAndPartyAccountListBtn\").click();\r\r\n}", "bindingType": "Parties", "configOptions": ["odcPartiesCVVIS", "odcPartiesCVBTNVIS", "partyTypeName", "customerFullDetails", "addressBICList", "isReturned", "<PERSON><PERSON><PERSON><PERSON>"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//Note : old script is below commented!!\r\r\n\r\r\n//On retrieve cif info btn - service call to retrieve party info\r\r\nthis.getPartyDetails = function (cif,name) {\r\r\n\t\r\r\n    this.context.options.partyTypeName.set(\"value\", name);\r\r\n    \r\r\n    var customerCIF = this.ui.get(cif).getData();\r\r\n    //validate cif before using it\r\r\n    if (isNaN(Number(customerCIF)) || customerCIF.length < 8) {\r\r\n        this.ui.get(cif).setValid(false, \"CIF must be 8 Digits\");\r\r\n\r\r\n    } else {\r\r\n        this.ui.get(cif).setValid(true);\r\r\n        this.ui.get(\"GetPartyDetails\").execute(customerCIF);\r\r\n    }\r\r\n}\r\r\n\r\r\n//on GetPartyDetails results - map the retrieved data\r\r\nthis.mapPartyData = function (name) {\r\r\n\r\r\n    if (name == \"collectingBank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\") {\r\r\n        var cif = \"collectingBankCIF\";\r\r\n        this.ui.get(cif).setValid(false, \"This CIF is not corresponding to a Bank\");\r\r\n\r\r\n    } else if (name == \"collectingBank\") {\r\r\n\t  var bankCIF = this.context.options.customerFullDetails.get(\"value\").get(\"customerNo\")\r\r\n        this.context.binding.get(\"value\").get(name).set(\"id\", bankCIF);\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"EG\");\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"reference\", \"NO REF\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"media\", \"SWIFT\");\r\r\n\t  \r\r\n\t  //Service call to get Address code list\r\r\n        this.ui.get(\"GetAddressBIC\").execute(bankCIF);\r\r\n\r\r\n    } else {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyId\", this.context.options.customerFullDetails.get(\"value\").get(\"customerNo\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyName\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"EG\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"ENG\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"refrence\", \"NO REF\");\r\r\n        \r\r\n        //Hidden btn to get Customer And Party Account List for charges - also its called on view load!!!\r\r\n        this.ui.get(\"GetCustomerAndPartyAccountListBtn\").click();\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of cif field - reset party info\r\r\nthis.resetPartyOnCifChange = function (name){\r\r\n    if (name == \"collectingBank\") {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"id\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"name\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"reference\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"media\", \"\");\r\r\n        this.context.options.addressBICList.set(\"value\", [])\r\r\n\r\r\n    } else {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyId\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyName\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"refrence\", \"\");\r\r\n    }\r\r\n}\r\r\n\r\r\n\r\r\n// this.showCollectigBankRefSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifInfo.get(\"value\") == true) {\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}\r\r\n// this.partyTypeSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifNOForPartyTypes.get(\"value\") == true){\r\r\n//\t console.log(this.context.options.retrieveCifInfo)\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}"}]}, "hasDetails": true}