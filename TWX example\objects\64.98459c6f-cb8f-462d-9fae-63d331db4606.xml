<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.98459c6f-cb8f-462d-9fae-63d331db4606" name="test view">
        <lastModified>1734341797459</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.98459c6f-cb8f-462d-9fae-63d331db4606</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;642f44c8-1a69-4cf0-82ca-1a197748c03f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d863e971-b8ee-473a-bf56-332c79a120f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fac7f1fe-cb6d-4c8d-8555-778929df0a2f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Plain text&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a8edf4e8-fcaf-44fb-8b0b-2a7e44c1e9a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6facde80-cade-4dc3-86a4-5387291dc70a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f4361288-945c-42e0-8038-c43598948d78&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81be124b-ae34-45f4-855d-38f5954c14dc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.changeIt(me)&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.newtest&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>false</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-7d4e</guid>
        <versionId>9088f2b0-71c5-4a5b-991b-658253716d2c</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="newtest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.798bdc8b-a448-430d-a1e8-592cb1d78228</coachViewBindingTypeId>
            <coachViewId>64.98459c6f-cb8f-462d-9fae-63d331db4606</coachViewId>
            <isList>false</isList>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>59f2f124-fa89-4836-a675-3f21d0590f13</guid>
            <versionId>207394a5-3e80-44f4-a0aa-7ae14eb93528</versionId>
        </bindingType>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.787f7f98-2dea-4c1c-82eb-82d80e6735f7</coachViewInlineScriptId>
            <coachViewId>64.98459c6f-cb8f-462d-9fae-63d331db4606</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.changeIt = function(me){&#xD;
	var v1 = me.getData();&#xD;
	var test2 = bpmext.ui.getView("/test_view_21");&#xD;
	test2.setData(v1);&#xD;
	&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
//---------------VALIDATION FUNCTIONS---------------------------------------------------------&#xD;
&#xD;
/* &#xD;
How to use:&#xD;
    - put these functions at the inline JS of the template.&#xD;
	- in the CSHS script, add the following line:&#xD;
		var lib = bpmext.ui.getView('templatePathName');&#xD;
	- at te start of the script, add the following line, to clear the error list:&#xD;
		lib.setErrorList();&#xD;
	- use the functions as follows:&#xD;
		lib.addError(path, message);&#xD;
		lib.mandatory(value, path, message);&#xD;
	- at the end of the script, add the following line, to get the error list:&#xD;
		lib.getErrorList();&#xD;
*/&#xD;
&#xD;
var dateOptions = { day: "numeric", month: "long", year: "numeric" };&#xD;
var validationList = [];&#xD;
&#xD;
/* Add a coach validation error if the field is empty, message is OPTIONAL!!&#xD;
mandatory(tw.local.input, "tw.local.input", message : concat) : CSHS */&#xD;
this.mandatory = function (value, path, message) {&#xD;
&#xD;
	message = message || 'This field is Mandatory';&#xD;
&#xD;
	if (value == null || value == undefined) {&#xD;
		addError(path, message);&#xD;
		return false;&#xD;
	} else {&#xD;
		switch (typeof value) {&#xD;
			case "string":&#xD;
				if (value.trim().length == 0) {&#xD;
					addError(path, message);&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (value == 0.0 || value &lt; 0.0) {&#xD;
					addError(path, message);&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error 		&#xD;
EX: addError(tw.local.name , 'validation message!')*/&#xD;
function addError (path, message) {&#xD;
	&#xD;
	if (validationList == null) {&#xD;
		validationList = [];&#xD;
	}&#xD;
	validationList.push({&#xD;
		errorBOPath: path,&#xD;
		errorMessage: message,&#xD;
	});&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is in the past today.&#xD;
   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/&#xD;
this.notPastDate = function (value, path, exclude, message) {&#xD;
	if (!value) return;&#xD;
	message = message || "Please input a valid future date";&#xD;
&#xD;
	var checkDate = this.getMidNightDate();&#xD;
	//exclude today?&#xD;
	if (exclude &amp;&amp; value &lt; checkDate) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	if (!exclude &amp;&amp; value &lt;= new Date()) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is between two dates.&#xD;
   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/&#xD;
this.dateBetween = function (value, path, date1, date2, message) {&#xD;
	if (!value) return;&#xD;
&#xD;
	message =&#xD;
		message ||&#xD;
		`This date must be between ${date1.toLocaleDateString(&#xD;
			"en-GB",&#xD;
			dateOptions&#xD;
		)} and ${date2.toLocaleDateString("en-GB", dateOptions)}`;&#xD;
&#xD;
	if (value &amp;&amp; value &gt; date1 &amp;&amp; value &lt; date2) {&#xD;
		return true;&#xD;
	}&#xD;
&#xD;
	this.addError(path, message);&#xD;
	return false;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.&#xD;
  EX:	notFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/&#xD;
this.notFutureDate = function (value, path, exclude, message) {&#xD;
	if (!value) return;&#xD;
	&#xD;
	message = message || "Please input a valid date in the past";&#xD;
&#xD;
	var checkDate = this.getMidNightDate();&#xD;
	//exclude today?&#xD;
	if (exclude &amp;&amp; value &gt; checkDate) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	if (!exclude &amp;&amp; value &gt;= new Date()) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the STRING length is greater than given length (len).&#xD;
  EX:	maxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/&#xD;
this.maxLength = function (value, path, len, message) {&#xD;
	if (!value) return;&#xD;
	message = message || `This field can only contain up to ${len} characters.`;&#xD;
	if (value.length &gt; len) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the STRING length is less than given length (len).&#xD;
   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/&#xD;
this.minLength = function (value, path, len, message) {&#xD;
	if (!value) return;&#xD;
&#xD;
	message = message || `This field must contain at least ${len} characters.`;&#xD;
	if (value.length &lt; len) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the number is greater than given value (max).&#xD;
 * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/&#xD;
this.maxNumber = function (value, path, max, message) {&#xD;
	if (!value) return;&#xD;
	&#xD;
	message = message || `This value must be &lt; ${max}`;&#xD;
	if (!isNaN(value) &amp;&amp; value &gt; max) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the number is less than given value (min).&#xD;
   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/&#xD;
this.minNumber = function (value, path, min, message) {&#xD;
	if (!value) return;&#xD;
	&#xD;
	message = message || `This value must be &gt; ${min}`;&#xD;
	if (!isNaN(value) &amp;&amp; value &lt; min) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
// FOR VIEW&#xD;
//prevents input length exceeding len, only works for string and on input event&#xD;
this.maxLenOnInput = function (me, potential, len, message) {&#xD;
	if (potential.length &gt; len) {&#xD;
		var label = me.getLabel();&#xD;
		message = message || `${label} can only contain up to ${len} characters.`;&#xD;
		me.setValid(false, message);&#xD;
		return false;&#xD;
	}&#xD;
	me.setValid(true);&#xD;
	return true;&#xD;
};&#xD;
&#xD;
this.isChecker = function (fun, me, ...args) {&#xD;
	if (!me) return;&#xD;
	if (me.getVisibility() == "READONLY" || me.getVisibility() == "HIDDEN") {&#xD;
		return;&#xD;
	}&#xD;
	return fun(...args);&#xD;
};&#xD;
//-------------------------------------Private-----------------------------------------------------&#xD;
// returns 'new Date()' but reset it to '00:00:00'.&#xD;
this.getMidNightDate = function () {&#xD;
	var midNightDate = new Date();&#xD;
	// Set hours, minutes, seconds and milliseconds to 0&#xD;
	midNightDate.setHours(0);&#xD;
	midNightDate.setMinutes(0);&#xD;
	midNightDate.setSeconds(0);&#xD;
	midNightDate.setMilliseconds(0);&#xD;
&#xD;
	return midNightDate;&#xD;
};&#xD;
&#xD;
// getter for 'validationErrors', input is (tw.system.coachValidation).&#xD;
this.getErrorList = function (coachValidation) {&#xD;
	coachValidation.validationErrors = validationList;&#xD;
	// this.setErrorList();&#xD;
      validationList = [];&#xD;
}&#xD;
&#xD;
// setter for 'validationErrors'.&#xD;
this.setErrorList = function () {&#xD;
	validationList = [];&#xD;
};&#xD;
&#xD;
this.camelCaseToTitle = function (camelCase) {&#xD;
	var fieldName = camelCase.split(".").pop();&#xD;
	// Convert camelCase to Title Case&#xD;
	var titleCase = fieldName.replace(/([A-Z])/g, " $1");&#xD;
	// Uppercase the first character&#xD;
	titleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);&#xD;
	return titleCase;&#xD;
};&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>506a9627-bd47-4705-8ad1-e3e10406c26c</guid>
            <versionId>04c996e6-1ff0-4d59-8cde-e0016453ff36</versionId>
        </inlineScript>
    </coachView>
</teamworks>

