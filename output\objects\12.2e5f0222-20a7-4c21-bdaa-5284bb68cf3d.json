{"id": "12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d", "versionId": "fadb4a5e-db8e-42be-9c01-28ab590158e1", "name": "ItmDef_1412542389_2e5f0222_20a7_4c21_bdaa_5284bb68cf3d", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d", "name": "ItmDef_1412542389_2e5f0222_20a7_4c21_bdaa_5284bb68cf3d", "lastModified": "1693480732480", "lastModifiedBy": "abdelrahman.saleh", "classId": "12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": "3", "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d", "dependencySummary": "<dependencySummary id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173e\">\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173d\">\r\n    <refId>/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173c\">\r\n      <name>externalId</name>\r\n      <value>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173b\">\r\n      <name>mimeType</name>\r\n      <value>xsd</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n</dependencySummary>", "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{\"namespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"typeName\":\"generateDocumentUsingPOSTResponse\"}],\"shadow\":[false]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{\"content\":[\"Document Generated Successfully\"]}],\"appinfo\":[{\"propertyName\":[\"generateDocumentUsingPOST_200\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"_minOccurs\":0,\"typeName\":\"DocumentGenerationResponse\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":1}]}]},\"name\":\"generateDocumentUsingPOST_200\",\"type\":\"{http:\\/\\/NBEODCR}BrokenReference\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.a0722085-9246-4af1-b86e-8a21be7e94cc\"}}]},\"name\":\"ItmDef_1412542389_2e5f0222_20a7_4c21_bdaa_5284bb68cf3d\"}],\"id\":\"_12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d\"}", "description": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1767", "versionId": "fadb4a5e-db8e-42be-9c01-28ab590158e1", "definition": {"property": {"name": "generateDocumentUsingPOST_200", "description": "Document Generated Successfully", "classRef": "/12.a0722085-9246-4af1-b86e-8a21be7e94cc", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "DocumentGenerationResponse", "typeNamespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "1", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": "generateDocumentUsingPOSTResponse", "namespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}