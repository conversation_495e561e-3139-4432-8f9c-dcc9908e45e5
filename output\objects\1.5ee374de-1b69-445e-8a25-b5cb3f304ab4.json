{"id": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "versionId": "55b7163e-f619-4530-b569-bf80238dd204", "name": "retrieve Odc request Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "retrieve Odc request Data", "lastModified": "1697367092986", "lastModifiedBy": "heba", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.e453880a-a17c-44c0-8f65-cb94528924f9", "2025.e453880a-a17c-44c0-8f65-cb94528924f9"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-1f85", "versionId": "55b7163e-f619-4530-b569-bf80238dd204", "dependencySummary": "<dependencySummary id=\"bpdid:aff098473ecd546d:1d42df0a:18b291cb73a:b10\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"c690c7d1-5e44-44cc-8a17-30a5efab2cf7\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":100,\"y\":214,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"5b89eae5-f71d-42b5-86f1-a474d2bf7f66\"},{\"incoming\":[\"7dc5fb1c-3096-4a55-8cbe-33c357ceff97\",\"bd436f25-2a76-4d05-8e08-1f520694e225\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":990,\"y\":215,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-1f83\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"96d725a5-dcab-4d31-890f-3e065fc00594\"},{\"startQuantity\":1,\"outgoing\":[\"d6cdaa80-fd0d-4762-8beb-d9408f3cabf3\"],\"incoming\":[\"a15a62b6-9036-4a76-8a64-87f6ad1e0bee\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":405,\"y\":191,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Sql Execute statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"startQuantity\":1,\"outgoing\":[\"e55f589e-15a5-4e7f-8bf9-86962f844303\"],\"incoming\":[\"1a7ed6a4-07e9-4c1d-8c5a-5d2394e2023e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":695,\"y\":192,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Mapping Output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"dbfefe55-69f0-4717-8a38-c0d013025f41\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.BasicDetails ={};\\r\\ntw.local.odcRequest.BasicDetails.exportPurpose     = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.paymentTerms\\t   = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.productCategory   = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ContractCreation.productCode   = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsFO.executionHub= new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ProductShipmentDetails.shipmentMethod = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ImporterDetails.importerCountry = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ImporterDetails.bankCountry = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.FcCollections.accountNo     = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.FcCollections.currency      = new tw.object.NameValuePair();\\r\\n\\r\\n\\r\\nif(tw.local.results.listLength > 0 )\\r\\n{\\r\\n\\ttw.local.odcRequest.requestNo \\t\\t\\t= tw.local.results[0].rows[0].data[1];\\r\\n\\ttw.local.odcRequest.requestNature.value \\t\\t= tw.local.results[0].rows[0].data[2];\\r\\n\\ttw.local.odcRequest.requestType.value \\t\\t= tw.local.results[0].rows[0].data[3];\\r\\n\\ttw.local.odcRequest.requestDate\\t\\t      = tw.local.results[0].rows[0].data[4];\\r\\n\\t\\r\\n\\ttw.local.odcRequest.appInfo.status \\t\\t      = tw.local.results[0].rows[0].data[6];\\r\\n\\t\\r\\n\\t\\r\\n\\ttw.local.odcRequest.BasicDetails.requestType \\t\\t\\t= tw.local.results[0].rows[0].data[3];\\r\\n\\ttw.local.odcRequest.BasicDetails.requestNature  \\t\\t= tw.local.results[0].rows[0].data[2];\\r\\n\\ttw.local.odcRequest.BasicDetails.requestState \\t\\t\\t= tw.local.results[0].rows[0].data[5];\\r\\n\\ttw.local.odcRequest.BasicDetails.parentRequestNo \\t\\t= tw.local.results[0].rows[0].data[7];\\r\\n\\ttw.local.odcRequest.BasicDetails.flexCubeContractNo \\t\\t= tw.local.results[0].rows[0].data[8];\\r\\n\\ttw.local.odcRequest.BasicDetails.contractStage \\t\\t\\t= tw.local.results[0].rows[0].data[9];\\r\\n\\ttw.local.odcRequest.BasicDetails.exportPurpose.value \\t\\t= tw.local.results[0].rows[0].data[10];\\r\\n\\ttw.local.odcRequest.BasicDetails.paymentTerms.value \\t\\t= tw.local.results[0].rows[0].data[11];\\r\\n\\ttw.local.odcRequest.BasicDetails.productCategory.value \\t= tw.local.results[0].rows[0].data[12];\\r\\n\\ttw.local.odcRequest.BasicDetails.commodityDescription \\t= tw.local.results[0].rows[0].data[13];\\r\\n\\t\\r\\n\\ttw.local.odcRequest.cif \\t\\t\\t\\t\\t\\t= tw.local.results[0].rows[0].data[14];\\r\\n\\ttw.local.odcRequest.CustomerInfo.cif\\t\\t\\t\\t= tw.local.results[0].rows[0].data[14];\\r\\n\\ttw.local.odcRequest.CustomerInfo.customerName\\t\\t\\t= tw.local.results[0].rows[0].data[15];\\r\\n\\ttw.local.odcRequest.customerName\\t\\t\\t\\t\\t= tw.local.results[0].rows[0].data[15];\\r\\n\\t\\r\\n\\ttw.local.odcRequest.ContractCreation.baseDate     \\t\\t= tw.local.results[0].rows[0].data[16];\\r\\n\\ttw.local.odcRequest.ContractCreation.valueDate\\t\\t\\t= tw.local.results[0].rows[0].data[17];\\r\\n\\ttw.local.odcRequest.ContractCreation.tenorDays\\t\\t\\t= tw.local.results[0].rows[0].data[18];\\r\\n\\ttw.local.odcRequest.ContractCreation.transitDays\\t\\t= tw.local.results[0].rows[0].data[19];\\r\\n\\ttw.local.odcRequest.ContractCreation.maturityDate\\t\\t= tw.local.results[0].rows[0].data[20];\\r\\n\\t\\r\\n\\ttw.local.odcRequest.ContractCreation.userReference\\t\\t= tw.local.results[0].rows[0].data[21];\\r\\n\\ttw.local.odcRequest.ContractCreation.productCode.value\\t\\t= tw.local.results[0].rows[0].data[22];\\r\\n\\ttw.local.odcRequest.ContractCreation.productDescription\\t= tw.local.results[0].rows[0].data[23];\\r\\n\\ttw.local.odcRequest.ContractCreation.Stage\\t\\t\\t= tw.local.results[0].rows[0].data[24];\\r\\n\\ttw.local.odcRequest.ContractCreation.sourceReference\\t\\t= tw.local.results[0].rows[0].data[25];\\r\\n\\t\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.referenceNo\\t\\t= tw.local.results[0].rows[0].data[26];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.discount\\t\\t\\t= tw.local.results[0].rows[0].data[27];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.extraCharges\\t\\t\\t= tw.local.results[0].rows[0].data[28];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.ourCharges\\t\\t\\t= tw.local.results[0].rows[0].data[29];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountSight\\t\\t\\t= tw.local.results[0].rows[0].data[30];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization     = tw.local.results[0].rows[0].data[31];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\\t      = tw.local.results[0].rows[0].data[32];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount\\t\\t= tw.local.results[0].rows[0].data[33];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.outstandingAmount\\t\\t= tw.local.results[0].rows[0].data[34];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.maturityDate\\t\\t\\t= tw.local.results[0].rows[0].data[35];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\\t\\t= tw.local.results[0].rows[0].data[36];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.financeApprovalNo\\t\\t= tw.local.results[0].rows[0].data[37];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.executionHub.value\\t\\t= tw.local.results[0].rows[0].data[38];\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.executionHub.name\\t\\t= tw.local.results[0].rows[0].data[39];\\r\\n\\t\\r\\n\\r\\n\\ttw.local.odcRequest.OdcCollection.amount\\t\\t= tw.local.results[0].rows[0].data[40];\\r\\n\\ttw.local.odcRequest.OdcCollection.currency\\t= tw.local.results[0].rows[0].data[41];\\r\\n\\t\\r\\n\\t\\/\\/\\/\\/\\/need to ask about allocated amount and trans ref no\\r\\n\\/\\/\\ttw.local.odcRequest.FcCollections.standardExchangeRate\\t\\t= tw.local.results[0].rows[0].data[43];\\r\\n\\/\\/\\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate\\t\\t= tw.local.results[0].rows[0].data[44];\\r\\n\\/\\/\\t\\/\\/tw.local.odcRequest.FcCollections.usedAmount\\t\\t            = tw.local.results[0].rows[0].data[45];\\r\\n\\/\\/\\ttw.local.odcRequest.FcCollections.totalAllocatedAmount\\t\\t= tw.local.results[0].rows[0].data[46];\\r\\n\\/\\/\\t\\r\\n\\/\\/\\ttw.local.odcRequest.FinancialDetailsFO.referenceNo\\t\\t      = tw.local.results[0].rows[0].data[47];\\r\\n\\t\\r\\n\\t\\r\\n\\ttw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.value\\t\\t= tw.local.results[0].rows[0].data[48];\\r\\n\\ttw.local.odcRequest.ProductShipmentDetails.shipmentMethod.value\\t\\t\\t\\t= tw.local.results[0].rows[0].data[49];\\r\\n\\ttw.local.odcRequest.ProductShipmentDetails.shippingDate\\t\\t\\t\\t= tw.local.results[0].rows[0].data[50];\\r\\n\\t\\r\\n\\ttw.local.odcRequest.ReversalReason.reversalReason\\t\\t= tw.local.results[0].rows[0].data[51];\\r\\n\\ttw.local.odcRequest.ReversalReason.closureReason\\t\\t= tw.local.results[0].rows[0].data[52];\\r\\n\\t\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerName\\t\\t\\t= tw.local.results[0].rows[0].data[53];\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerCountry.value\\t\\t= tw.local.results[0].rows[0].data[54];\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerAddress\\t\\t\\t= tw.local.results[0].rows[0].data[55];\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerPhoneNo\\t\\t\\t= tw.local.results[0].rows[0].data[56];\\r\\n\\ttw.local.odcRequest.ImporterDetails.bank\\t\\t\\t\\t\\t= tw.local.results[0].rows[0].data[57];\\r\\n\\ttw.local.odcRequest.ImporterDetails.BICCode\\t\\t\\t\\t= tw.local.results[0].rows[0].data[58];\\r\\n\\ttw.local.odcRequest.ImporterDetails.ibanAccount\\t\\t\\t\\t= tw.local.results[0].rows[0].data[59];\\r\\n\\ttw.local.odcRequest.ImporterDetails.bankCountry.value\\t\\t\\t= tw.local.results[0].rows[0].data[60];\\r\\n\\ttw.local.odcRequest.ImporterDetails.bankAddress\\t\\t\\t\\t= tw.local.results[0].rows[0].data[61];\\r\\n\\ttw.local.odcRequest.ImporterDetails.bankPhoneNo\\t\\t\\t\\t= tw.local.results[0].rows[0].data[62];\\r\\n\\ttw.local.odcRequest.ImporterDetails.collectingBankReference\\t\\t= tw.local.results[0].rows[0].data[63];\\r\\n\\r\\n\\r\\n\\/\\/\\\"ODC_FCTRANSACTION.ACCOUNTNO, ODC_FCTRANSACTION.POSTINGDATE, ODC_FCTRANSACTION.VALUEDATE, ODC_FCTRANSACTION.TRANSACTIONAMOUNT, ODC_FCTRANSACTION.AMOUNTINBPM, \\\"\\r\\n\\/\\/+\\\" ODC_PARTIESINFO.PARTYID, ODC_PARTIESINFO.PARTYTYPE, ODC_PARTIESINFO.CIF\\\"\\r\\n\\ttw.local.odcRequest.FcCollections.accountNo \\t\\t= new tw.object.listOf.NameValuePair();\\r\\n\\ttw.local.odcRequest.FcCollections.accountNo.value     =  tw.local.results[0].rows[0].data[64];\\r\\n\\ttw.local.odcRequest.FcCollections.fromDate  \\t\\t=  tw.local.results[0].rows[0].data[65];\\/\\/postingDate\\r\\n\\ttw.local.odcRequest.FcCollections.ToDate  \\t\\t=  tw.local.results[0].rows[0].data[66];\\/\\/valueDate\\r\\n\\ttw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount  \\t\\t=  tw.local.results[0].rows[0].data[65];\\/\\/transactionamount\\r\\n\\t\\/\\/tw.local.odcRequest.FcCollections.  \\t\\t=  tw.local.results[0].rows[0].data[65];\\/\\/amount in bpm\\r\\n\\t\\/\\/tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount  =  tw.local.results[0].rows[0].data[65];\\/\\/transactionamount\\r\\n\\t\\r\\n\\t\\/\\/tw.local.odcRequest.Parties.partyTypes.partyId  \\t\\t=  tw.local.results[0].rows[0].data[66];\\/\\/valueDate\\r\\n\\t\\/\\/tw.local.odcRequest.Parties.partyTypes.partyType  \\t\\t=  tw.local.results[0].rows[0].data[66];\\/\\/valueDate\\r\\n\\t\\/\\/tw.local.odcRequest.Parties.partyTypes.partyCIF  \\t\\t=  tw.local.results[0].rows[0].data[66];\\/\\/valueDate\\r\\n}\"]}},{\"startQuantity\":1,\"outgoing\":[\"a15a62b6-9036-4a76-8a64-87f6ad1e0bee\"],\"incoming\":[\"c690c7d1-5e44-44cc-8a17-30a5efab2cf7\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":234,\"y\":191,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"sql script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"e453880a-a17c-44c0-8f65-cb94528924f9\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sql = \\\"SELECT ID, REQUESTNO, REQUESTNATURE, REQUESTTYPE, REQUESTDATE, REQUESTSTATE, REQUESTSTATUS, \\\"\\r\\n+\\\"PARENTREQUESTNO, FCCONTRACTNO, CONTRACTSTAGE, EXPORTPURPOSE, PAYMENTTERMS, PRODUCTCATEGORY, COMMODITYDESCRIPTION, \\\"\\r\\n+\\\"  ODC_REQUESTINFO.CIF, CUSTOMERNAME, BASEDATE, ODC_REQUESTINFO.VALUEDATE, TENORDAYS, TRANSITDAYS, \\\"\\r\\n+\\\"CONTRACTMATURITYDATE, USERREF, PRODUCTCODE, PRODUCTDESC, STAGE, SOURCEREF, TRADEFOREFNO,\\\"\\r\\n+\\\"DISCOUNT, EXTRACHARGES, OURCHARGES, AMOUNTSIGHT, AMOUNTDEFNOAVALIZATION, AMOUNTDEFAVALIZATION, COLLECTABLEAMOUNT, \\\"\\r\\n+\\\" OUTSTANDINGAMOUNT, MATURITYDATE, MATURITYDAYS, FINANCEAPPROVALNO, HUBCODE, HUBNAME, LIQAMOUNT,\\\"\\r\\n+\\\" LIQCURRENCY, COLLECTIONCURRENCY, STANDARDEXRATE, NEGOTIATEDEXRATE, ALLOCATEDAMOUNT, TOTALALLOCATEDAMOUNT, TRANSACTIONREFNO,\\\"\\r\\n+\\\" CBECLASSIFICATION, SHIPMENTMETHOD, SHIPMENTDATE, REVERSALREASON, CLOSUREREASON, IMPORTERNAME, IMPORTERCOUNTRY, \\\"\\r\\n+\\\" IMPORTERADDRESS, IMPORTERPHONENO, IMPORTERBANK, BICCODE, IBANACCOUNT, BANKCOUNTRY, BANKADDRESS, \\\"\\r\\n+\\\" BANKPHONENO, COLLECTINGBANKREF , \\\"\\/\\/64\\r\\n+\\\"ODC_FCTRANSACTION.ACCOUNTNO, ODC_FCTRANSACTION.POSTINGDATE, ODC_FCTRANSACTION.VALUEDATE, ODC_FCTRANSACTION.TRANSACTIONAMOUNT, ODC_FCTRANSACTION.AMOUNTINBPM, \\\"\\r\\n+\\\" ODC_PARTIESINFO.PARTYID, ODC_PARTIESINFO.PARTYTYPE, ODC_PARTIESINFO.CIF\\\"\\r\\n+\\\" FROM ODC_REQUESTINFO\\\"\\r\\n+\\\"    Inner join ODC_FCTRANSACTION   on ODC_REQUESTINFO.ID = ODC_FCTRANSACTION.REQUESRID       \\\" \\r\\n+\\\"    Inner join ODC_PARTIESINFO     on ODC_REQUESTINFO.ID = ODC_PARTIESINFO.REQUESRID         \\\"\\r\\n\\r\\n+\\\"    where ODC_REQUESTINFO.REQUESTNO like  '\\\"+tw.local.requestNumber+\\\"'  ; \\\"\\r\\n\\r\\n\\r\\n\\r\\n\\t               \"]}},{\"targetRef\":\"f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Sql Execute statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"a15a62b6-9036-4a76-8a64-87f6ad1e0bee\",\"sourceRef\":\"e453880a-a17c-44c0-8f65-cb94528924f9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9e1923a7-2a6d-43f2-8b40-ff0954a38475\"},{\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"sqlParameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.031e9385-697a-44c5-83d9-f853decf33e8\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"results\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.a9d6a9a7-2c30-4f9e-8272-40cf4b0ce90a\"},{\"parallelMultiple\":false,\"outgoing\":[\"bcfac1fa-5a0c-4ade-8321-1b8b09d1f8e1\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"613b55ba-dc6f-4d05-8d26-4940b8bba2f3\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"b9b3e802-a45f-4a13-8ee6-02e691b064b6\",\"otherAttributes\":{\"eventImplId\":\"214fc5bc-57dc-4bce-893d-571fa88d260c\"}}],\"attachedToRef\":\"f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":440,\"y\":249,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"21fa0ae0-b17c-4070-83be-c0263fc49e1a\",\"outputSet\":{}},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":225,\"y\":95,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"SQL\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"6252e11a-00cc-45ce-89a9-3cb606ea687f\",\"scriptFormat\":\"text\\/plain\",\"script\":{\"content\":[\"tw.local.sql\\nSELECT  REQUESTNO,  REQUESTNATURE, REQUESTTYPE, REQUESTDATE, REQUESTSTATE, REQUESTSTATUS, PARENTREQUESTNO,\\r\\nFCCONTRACTNO, CONTRACTSTAGE, EXPORTPURPOSE, PAYMENTTERMS, PRODUCTCATEGORY, COMMODITYDESCRIPTION, --RI.ISLIQUIDATED,\\r\\nRI.CIF, RI.CUSTOMERNAME, RI.BASEDATE, RI.VALUEDATE, RI.TENORDAYS, RI.TRANSITDAYS, RI.CONTRACTMATURITYDATE, \\r\\nRI.USERREF, RI.PRODUCTCODE, PRODUCTDESC, STAGE, SOURCEREF, TRADEFOREFNO, DISCOUNT, EXTRACHARGES, OURCHARGES,\\r\\nAMOUNTSIGHT, AMOUNTDEFNOAVALIZATION, AMOUNTDEFAVALIZATION, COLLECTABLEAMOUNT, OUTSTANDINGAMOUNT, MATURITYDATE,\\r\\nMATURITYDAYS, FINANCEAPPROVALNO, HUBCODE, HUBNAME, LIQAMOUNT, LIQCURRENCY, COLLECTIONCURRENCY, RI.STANDARDEXRATE, \\r\\nRI.NEGOTIATEDEXRATE, ALLOCATEDAMOUNT, TOTALALLOCATEDAMOUNT, TRANSACTIONREFNO, CBECLASSIFICATION, SHIPMENTMETHOD,\\r\\nSHIPMENTDATE, REVERSALREASON, CLOSUREREASON, IMPORTERNAME, IMPORTERCOUNTRY, IMPORTERADDRESS, IMPORTERPHONENO,\\r\\nIMPORTERBANK, BICCODE, IBANACCOUNT, BANKCOUNTRY, BANKADDRESS, BANKPHONENO, COLLECTINGBANKREF, CURRENTSTEPNAME, \\r\\nRI.BPMINSTANCENUMBER, \\r\\n\\r\\nINV.INVOICENO, INV.INVOICEDATE, \\r\\n\\r\\nB.BILLOFLADINGREF , B.BILLOFLADINGDATE, \\r\\n\\r\\nFCT.ACCOUNTNO,FCT.POSTINGDATE, FCT.VALUEDATE, FCT.TRANSACTIONAMOUNT, FCT.TRANSACTIONAMOUNT, FCT.AMOUNTINBPM,\\r\\n\\r\\nPI.PARTYID,PI.PARTYTYPE,PI.CIF,\\r\\n\\r\\nCC.COMPONENT, CC.DEFAULTCURRENCY, CC.DEFAULTAMOUNT, CC.CHARGEAMOUNT, --CC.WAIVER ,\\r\\nCC.ACCOUNTCLASS, CC.ACCOUNTNO, CC.BRANCHCODE, CC.CURRENCY, CC.BALANCE, CC.BALANCESIGN, CC.STANDARDEXRATE,\\r\\nCC.NEGOTIATEDEXRATE, CC.DEBITEDAMOUNT,\\r\\n\\r\\nMTD.INSTALLMENTDATE, MTD.INSTALLMENTAMOUNT\\r\\n\\r\\nFROM ODC_REQUESTINFO RI\\r\\nINNER JOIN ODC_INVOICE INV   \\t\\t ON  RI.ID = INV.REQUESRID\\r\\nINNER JOIN ODC_BILLS B       \\t \\t ON  RI.ID = B.REQUESRID\\r\\nINNER JOIN ODC_FCTRANSACTION FCT   \\t ON  RI.ID = FCT.REQUESRID\\r\\nINNER JOIN ODC_PARTIESINFO PI  \\t\\t ON  RI.ID = PI.REQUESRID\\r\\nINNER JOIN ODC_CHARGESCOMMISSIONS CC ON  RI.ID = CC.REQUESRID\\r\\nINNER JOIN ODC_MULTITENORDATES MTD   ON  RI.ID = MTD.REQUESRID\\r\\n\\r\\nWHERE RI.REQUESTNO = '<#= tw.local.requestNumber #>'\\r\\n\\r\\n         \"]}},{\"startQuantity\":1,\"outgoing\":[\"bd436f25-2a76-4d05-8e08-1f520694e225\"],\"incoming\":[\"bcfac1fa-5a0c-4ade-8321-1b8b09d1f8e1\",\"e0f91632-e0c3-46f7-88d3-96f1b98fc243\",\"8f98589d-e89e-45bd-883b-f6bedcc35e18\",\"7ea91734-4459-43cc-84cf-98808567d537\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":544,\"y\":316,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Retrieve Odc request data\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"bf318cac-19b9-4d69-8048-a77915d5572c\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"bf318cac-19b9-4d69-8048-a77915d5572c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"bcfac1fa-5a0c-4ade-8321-1b8b09d1f8e1\",\"sourceRef\":\"21fa0ae0-b17c-4070-83be-c0263fc49e1a\"},{\"startQuantity\":1,\"outgoing\":[\"1a7ed6a4-07e9-4c1d-8c5a-5d2394e2023e\"],\"incoming\":[\"d6cdaa80-fd0d-4762-8beb-d9408f3cabf3\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":545,\"y\":192,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"odc object init Service\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"faafa041-87b0-41de-8a80-fe8fb89bca0b\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.247d12a8-cf76-495c-8cd4-700f32286023\"]}],\"calledElement\":\"1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e\"},{\"targetRef\":\"dbfefe55-69f0-4717-8a38-c0d013025f41\",\"extensionElements\":{\"endStateId\":[\"guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Mapping Output\",\"declaredType\":\"sequenceFlow\",\"id\":\"1a7ed6a4-07e9-4c1d-8c5a-5d2394e2023e\",\"sourceRef\":\"faafa041-87b0-41de-8a80-fe8fb89bca0b\"},{\"startQuantity\":1,\"outgoing\":[\"7dc5fb1c-3096-4a55-8cbe-33c357ceff97\"],\"incoming\":[\"e55f589e-15a5-4e7f-8bf9-86962f844303\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":832,\"y\":192,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"retrieve rest of odc data 1:M relation\",\"dataInputAssociation\":[{\"targetRef\":\"2055.4c3318d7-370d-4d28-80de-b197f1a2aaec\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestNumber\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"4414718d-b38a-4fb5-809f-6e2ef44e794e\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.33b5dccf-**************-560ecde149f2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.6ff165ef-cd1e-4b27-8678-e809adfd458d\"]}],\"calledElement\":\"1.e62adcff-5053-4977-814b-9e511ca5d190\"},{\"targetRef\":\"faafa041-87b0-41de-8a80-fe8fb89bca0b\",\"extensionElements\":{\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To odc object init Service\",\"declaredType\":\"sequenceFlow\",\"id\":\"d6cdaa80-fd0d-4762-8beb-d9408f3cabf3\",\"sourceRef\":\"f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f\"},{\"targetRef\":\"4414718d-b38a-4fb5-809f-6e2ef44e794e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To retrieve rest of odc data 1:M relation\",\"declaredType\":\"sequenceFlow\",\"id\":\"e55f589e-15a5-4e7f-8bf9-86962f844303\",\"sourceRef\":\"dbfefe55-69f0-4717-8a38-c0d013025f41\"},{\"targetRef\":\"96d725a5-dcab-4d31-890f-3e065fc00594\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:cdc758d910e20d2d:-607ed7:18af686ab01:a22\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"7dc5fb1c-3096-4a55-8cbe-33c357ceff97\",\"sourceRef\":\"4414718d-b38a-4fb5-809f-6e2ef44e794e\"},{\"targetRef\":\"e453880a-a17c-44c0-8f65-cb94528924f9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To sql script\",\"declaredType\":\"sequenceFlow\",\"id\":\"c690c7d1-5e44-44cc-8a17-30a5efab2cf7\",\"sourceRef\":\"5b89eae5-f71d-42b5-86f1-a474d2bf7f66\"},{\"parallelMultiple\":false,\"outgoing\":[\"e0f91632-e0c3-46f7-88d3-96f1b98fc243\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"3b6241da-6d07-454d-8c80-b075dad38db6\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"77c4bb3f-6822-4958-84a5-7036f2079f3d\",\"otherAttributes\":{\"eventImplId\":\"2b36e3bb-836c-4eb4-816f-0f322738032a\"}}],\"attachedToRef\":\"faafa041-87b0-41de-8a80-fe8fb89bca0b\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":580,\"y\":250,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"3b47645a-bfb5-4d47-86df-4d44f0b958d3\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"8f98589d-e89e-45bd-883b-f6bedcc35e18\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"13fb13d2-db9c-4dad-8653-5b9cc314a83d\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"e08c8e23-d0a8-42d9-8f69-043e4d922d64\",\"otherAttributes\":{\"eventImplId\":\"71331427-39f6-4e0a-8318-dcf703555613\"}}],\"attachedToRef\":\"dbfefe55-69f0-4717-8a38-c0d013025f41\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":730,\"y\":250,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error2\",\"declaredType\":\"boundaryEvent\",\"id\":\"fbd86b97-5ea2-4b4c-8412-92821eeda748\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"7ea91734-4459-43cc-84cf-98808567d537\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"8434658d-4d60-469d-8d68-4208609b09c1\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"42ab8d64-c1e6-4d1e-8ce9-61bf31e3ff59\",\"otherAttributes\":{\"eventImplId\":\"c5167150-a0e2-4ea8-8681-aba6c77633a2\"}}],\"attachedToRef\":\"4414718d-b38a-4fb5-809f-6e2ef44e794e\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":867,\"y\":250,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error3\",\"declaredType\":\"boundaryEvent\",\"id\":\"6b2dae35-2c9b-4501-8544-5f0587d87adc\",\"outputSet\":{}},{\"targetRef\":\"bf318cac-19b9-4d69-8048-a77915d5572c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"e0f91632-e0c3-46f7-88d3-96f1b98fc243\",\"sourceRef\":\"3b47645a-bfb5-4d47-86df-4d44f0b958d3\"},{\"targetRef\":\"bf318cac-19b9-4d69-8048-a77915d5572c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"8f98589d-e89e-45bd-883b-f6bedcc35e18\",\"sourceRef\":\"fbd86b97-5ea2-4b4c-8412-92821eeda748\"},{\"targetRef\":\"bf318cac-19b9-4d69-8048-a77915d5572c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"7ea91734-4459-43cc-84cf-98808567d537\",\"sourceRef\":\"6b2dae35-2c9b-4501-8544-5f0587d87adc\"},{\"targetRef\":\"96d725a5-dcab-4d31-890f-3e065fc00594\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"bd436f25-2a76-4d05-8e08-1f520694e225\",\"sourceRef\":\"bf318cac-19b9-4d69-8048-a77915d5572c\"}],\"laneSet\":[{\"id\":\"fd3d6dac-321c-49ff-8c01-ce46aa3c68e8\",\"lane\":[{\"flowNodeRef\":[\"5b89eae5-f71d-42b5-86f1-a474d2bf7f66\",\"96d725a5-dcab-4d31-890f-3e065fc00594\",\"f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f\",\"dbfefe55-69f0-4717-8a38-c0d013025f41\",\"e453880a-a17c-44c0-8f65-cb94528924f9\",\"21fa0ae0-b17c-4070-83be-c0263fc49e1a\",\"6252e11a-00cc-45ce-89a9-3cb606ea687f\",\"bf318cac-19b9-4d69-8048-a77915d5572c\",\"faafa041-87b0-41de-8a80-fe8fb89bca0b\",\"4414718d-b38a-4fb5-809f-6e2ef44e794e\",\"3b47645a-bfb5-4d47-86df-4d44f0b958d3\",\"fbd86b97-5ea2-4b4c-8412-92821eeda748\",\"6b2dae35-2c9b-4501-8544-5f0587d87adc\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"bac5a3f9-f109-41c3-8f2e-ed29f6384db0\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"retrieve Odc request Data\",\"declaredType\":\"process\",\"id\":\"1.5ee374de-1b69-445e-8a25-b5cb3f304ab4\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.545510ce-ad3f-4e51-8848-e60223dc7eed\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.42c2aa04-75bd-4785-8a9e-dbfbf46cc496\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.c1f30445-c933-43d3-81d5-4bb22f76c46f\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.545510ce-ad3f-4e51-8848-e60223dc7eed\",\"2055.42c2aa04-75bd-4785-8a9e-dbfbf46cc496\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\/\\/\\\"11111111111111_023\\\"\\r\\n\\\"11223344556677\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"documentation\":[{\"content\":[\"\\/\\/tw.local.results[0].columnIndexes\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"requestNumber\",\"isCollection\":false,\"id\":\"2055.c1f30445-c933-43d3-81d5-4bb22f76c46f\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c1f30445-c933-43d3-81d5-4bb22f76c46f", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "//\"11111111111111_023\"\r\r\n\"11223344556677\"", "isLocked": "false", "description": "//tw.local.results[0].columnIndexes", "guid": "e79f50c6-804a-4b7a-a387-190a95569f1b", "versionId": "3ca39914-046d-4e44-b1d6-45b408c830a6"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.545510ce-ad3f-4e51-8848-e60223dc7eed", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "fda60131-ef54-4457-8e59-5727e6b5d8a1", "versionId": "215a4293-ac3f-45e4-8aa1-6754acda6c14"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.42c2aa04-75bd-4785-8a9e-dbfbf46cc496", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3d28a8f7-e5ca-427d-baa4-a12a7bb7aa2f", "versionId": "35c35501-e9db-4281-8d42-1b3d4fc60c8c"}], "processVariable": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9e1923a7-2a6d-43f2-8b40-ff0954a38475", "description": {"isNull": "true"}, "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "2d62b032-1bb9-4d65-aaf4-c0cb6a527a2d", "versionId": "e372bcd5-6d15-439a-a69e-8a36198cd1c2"}, {"name": "sqlParameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.031e9385-697a-44c5-83d9-f853decf33e8", "description": {"isNull": "true"}, "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7abd0508-2043-4eda-9a6c-39471fd9c33f", "versionId": "39792304-ae56-4f5a-9605-8463c68ac0c9"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a9d6a9a7-2c30-4f9e-8272-40cf4b0ce90a", "description": {"isNull": "true"}, "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bc660f77-22a3-4769-bbe9-5319c25892fc", "versionId": "6106bcbc-8b19-48dd-9003-382cb26bb939"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.96d725a5-dcab-4d31-890f-3e065fc00594", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.42aa27c2-4db6-4fa7-9b51-db6c33a1a1b5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-1f83", "versionId": "2fe29c5e-46b4-4d52-a0eb-ff23ff6ebc11", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "990", "y": "215", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.42aa27c2-4db6-4fa7-9b51-db6c33a1a1b5", "haltProcess": "false", "guid": "dfe54912-d941-43a3-a1db-daf134900676", "versionId": "ab74c3d8-fa91-4d40-8450-21d2060c3fa0"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4414718d-b38a-4fb5-809f-6e2ef44e794e", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "retrieve rest of odc data 1:M relation", "tWComponentName": "SubProcess", "tWComponentId": "3012.7a6174b4-4d21-4f81-8532-21e863a763e0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "guid": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:3d74", "versionId": "58793294-5f83-4848-b1df-32653d3e5dcf", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "832", "y": "192", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error3", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-61af", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "rightBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.7a6174b4-4d21-4f81-8532-21e863a763e0", "attachedProcessRef": "/1.e62adcff-5053-4977-814b-9e511ca5d190", "guid": "31952610-f614-4ccc-a294-7367b5e08be8", "versionId": "2ae01289-c1c0-445d-b155-4d3c6c90766a", "parameterMapping": [{"name": "requestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.655bc692-a2b4-4f1f-aa6e-f6bf47a06843", "processParameterId": "2055.4c3318d7-370d-4d28-80de-b197f1a2aaec", "parameterMappingParentId": "3012.7a6174b4-4d21-4f81-8532-21e863a763e0", "useDefault": "false", "value": "tw.local.requestNumber", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "32869061-1dbb-47fd-b682-f671f4fceaf1", "versionId": "1ea99b30-9816-44ae-acd6-fe4a090ba7c9", "description": {"isNull": "true"}}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.020f7ec8-0f40-421a-8508-0968e6bd423c", "processParameterId": "2055.33b5dccf-**************-560ecde149f2", "parameterMappingParentId": "3012.7a6174b4-4d21-4f81-8532-21e863a763e0", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "01137965-1415-43c6-8ea3-13d5322ff658", "versionId": "a2cc3ae4-0b8f-4a40-81df-ff4b4d4baec5", "description": {"isNull": "true"}}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.fbe46dc2-5f34-4ca0-844c-f5e80272dfe9", "processParameterId": "2055.6ff165ef-cd1e-4b27-8678-e809adfd458d", "parameterMappingParentId": "3012.7a6174b4-4d21-4f81-8532-21e863a763e0", "useDefault": "false", "value": "tw.local.odcRequest", "classRef": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isList": "false", "isInput": "false", "guid": "7f992df3-3d61-4d52-8d5a-a01578f7895d", "versionId": "ea283273-5bbe-43ac-bd61-5a10cfc95199", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6252e11a-00cc-45ce-89a9-3cb606ea687f", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "SQL", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.8c91948c-ff05-453b-9fe3-f92dc8c1b948", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-61bc", "versionId": "8d9a5ca3-3746-421c-8b93-0ea0e55a2ee1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "225", "y": "95", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.8c91948c-ff05-453b-9fe3-f92dc8c1b948", "scriptTypeId": "128", "isActive": "true", "script": "tw.local.sql\r\nSELECT  REQUESTNO,  R<PERSON><PERSON><PERSON><PERSON><PERSON>TU<PERSON>, REQ<PERSON><PERSON><PERSON><PERSON><PERSON>, REQUESTDATE, REQUESTSTATE, REQUESTSTATUS, PARENTREQ<PERSON><PERSON>N<PERSON>,\r\r\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CONTRACTSTAGE, <PERSON><PERSON><PERSON><PERSON><PERSON>URPO<PERSON>, <PERSON>Y<PERSON>NTTER<PERSON>, <PERSON><PERSON><PERSON><PERSON>AT<PERSON>ORY, COMM<PERSON><PERSON><PERSON>DESCRIPTION, --RI.<PERSON>LIQUIDATED,\r\r\nRI.CIF, RI.CUS<PERSON><PERSON><PERSON>ME, RI.BASEDATE, RI.VALUEDATE, RI.TENORDAYS, RI.TRANSITDAYS, RI.CONTRACTMATURITYDATE, \r\r\nRI.USERREF, RI.PRODUCTCODE, PRODUCTDESC, STAGE, SO<PERSON><PERSON>RE<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>EFN<PERSON>, <PERSON><PERSON><PERSON>UN<PERSON>, <PERSON><PERSON>RA<PERSON>ARGES, <PERSON><PERSON><PERSON>ARGES,\r\r\nAM<PERSON>UNTSIGHT, AMOUNTDEF<PERSON><PERSON>VA<PERSON>IZATION, AMOUNTDE<PERSON>VA<PERSON>IZATION, COLLEC<PERSON>BL<PERSON>MOUNT, OUTSTANDINGAMOUNT, MA<PERSON>RITYDA<PERSON>,\r\r\n<PERSON><PERSON>RITY<PERSON><PERSON>S, <PERSON><PERSON>ANCE<PERSON>PR<PERSON><PERSON>LNO, H<PERSON><PERSON>ODE, H<PERSON>BNAME, LIQAMOUN<PERSON>, LIQ<PERSON>RRENCY, COLLECTIONCURRENCY, RI.STANDARDEXRATE, \r\r\nRI.NEGOTIATEDEXRATE, ALLOCATEDAMOUNT, TOTALALLOCATEDAMOUNT, TRANSACTIONREFNO, CBECLASSIFICATION, SHIPMENTMETHOD,\r\r\nSHIPMENTDATE, REVERSALREASON, CLOSUREREASON, IMPORTERNAME, IMPORTERCOUNTRY, IMPORTERADDRESS, IMPORTERPHONENO,\r\r\nIMPORTERBANK, BICCODE, IBANACCOUNT, BANKCOUNTRY, BANKADDRESS, BANKPHONENO, COLLECTINGBANKREF, CURRENTSTEPNAME, \r\r\nRI.BPMINSTANCENUMBER, \r\r\n\r\r\nINV.INVOICENO, INV.INVOICEDATE, \r\r\n\r\r\nB.BILLOFLADINGREF , B.BILLOFLADINGDATE, \r\r\n\r\r\nFCT.ACCOUNTNO,FCT.POSTINGDATE, FCT.VALUEDATE, FCT.TRANSACTIONAMOUNT, FCT.TRANSACTIONAMOUNT, FCT.AMOUNTINBPM,\r\r\n\r\r\nPI.PARTYID,PI.PARTYTYPE,PI.CIF,\r\r\n\r\r\nCC.COMPONENT, CC.DEFAULTCURRENCY, CC.DEFAULTAMOUNT, CC.CHARGEAMOUNT, --CC.WAIVER ,\r\r\nCC.ACCOUNTCLASS, CC.ACCOUNTNO, CC.BRANCHCODE, CC.CURRENCY, CC.BALANCE, CC.BALANCESIGN, CC.STANDARDEXRATE,\r\r\nCC.NEGOTIATEDEXRATE, CC.DEBITEDAMOUNT,\r\r\n\r\r\nMTD.INSTALLMENTDATE, MTD.INSTALLMENTAMOUNT\r\r\n\r\r\nFROM ODC_REQUESTINFO RI\r\r\nINNER JOIN ODC_INVOICE INV   \t\t ON  RI.ID = INV.REQUESRID\r\r\nINNER JOIN ODC_BILLS B       \t \t ON  RI.ID = B.REQUESRID\r\r\nINNER JOIN ODC_FCTRANSACTION FCT   \t ON  RI.ID = FCT.REQUESRID\r\r\nINNER JOIN ODC_PARTIESINFO PI  \t\t ON  RI.ID = PI.REQUESRID\r\r\nINNER JOIN ODC_CHARGESCOMMISSIONS CC ON  RI.ID = CC.REQUESRID\r\r\nINNER JOIN ODC_MULTITENORDATES MTD   ON  RI.ID = MTD.REQUESRID\r\r\n\r\r\nWHERE RI.REQUESTNO = '<#= tw.local.requestNumber #>'\r\r\n\r\r\n         ", "isRule": "false", "guid": "31a04206-9b4d-476c-9ed0-b6447daafa0b", "versionId": "********-f91b-4fab-9149-df591192d622"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "Sql Execute statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.ac0e408f-13ea-4a8f-bb4e-1a29fe27c09b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "guid": "guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-1f7f", "versionId": "af8fc280-60a3-4fdd-b673-d52be29ccded", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "405", "y": "191", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-61af", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.ac0e408f-13ea-4a8f-bb4e-1a29fe27c09b", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "fa21300b-cd68-4699-a8c2-fdf73b78ff45", "versionId": "17aaa2df-1d0a-4ec8-af44-80ce042de078", "parameterMapping": [{"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.60badeef-88ea-45b9-a328-64ce792a9fc2", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.ac0e408f-13ea-4a8f-bb4e-1a29fe27c09b", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "b08f0b1d-046c-4aec-86e4-eabbf7fb5dc5", "versionId": "107bbd7d-af65-419e-8bea-eac165538b3e", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f5c99901-e1b2-4bff-89d3-7c9f660d8f7a", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.ac0e408f-13ea-4a8f-bb4e-1a29fe27c09b", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "c21d8812-8716-40a5-b493-3637f032c4ff", "versionId": "30b540eb-460d-4057-8f00-3baba9e0d195", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.35ceec1e-fa6a-4a68-b2f4-2f415ac9f94f", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.ac0e408f-13ea-4a8f-bb4e-1a29fe27c09b", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "34c7eefd-53aa-4192-bff0-b7d48a9a42db", "versionId": "484306cd-e745-4642-a9bf-a2721fcec99c", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7561f1dc-c9a5-4217-bf34-747e26f1bffa", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.ac0e408f-13ea-4a8f-bb4e-1a29fe27c09b", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "77cf8ebb-d14e-4526-9d7f-6cb494cac64f", "versionId": "70d773bf-61d9-4b8d-8ada-c5b3d8ea3e26", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d2a8a25f-7ce7-4c55-9403-ce20fb15d543", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.ac0e408f-13ea-4a8f-bb4e-1a29fe27c09b", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "18f8ed4e-4805-495c-9628-c7f7e54ddbe8", "versionId": "a3a340db-9c3e-4781-a646-430f76ec8435", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.8019d098-3e35-409f-9b6d-4381fb7512eb", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-61af", "versionId": "d0b91fce-156a-46e6-adcc-7164842a264b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "544", "y": "316", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.8019d098-3e35-409f-9b6d-4381fb7512eb", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "118948d7-e244-47ba-9146-3e3bb6bed890", "versionId": "1490e1a9-7292-4d06-a322-c4de8c3afd0d", "parameterMapping": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f22aa272-a6a6-4f57-a4c6-0720d0f30f21", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.8019d098-3e35-409f-9b6d-4381fb7512eb", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "64a13aaf-c7c3-452f-a294-2d3adcc30671", "versionId": "a9fc8d8f-771d-4743-9604-2056aa1e5f65", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.78a99376-4368-4cc0-afd1-492f69411219", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.8019d098-3e35-409f-9b6d-4381fb7512eb", "useDefault": "false", "value": "\"Retrieve Odc request data\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "feed548f-8323-4286-b439-14fb41b0a4d3", "versionId": "e7d41122-8202-4f71-93f1-90f050a996a1", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e453880a-a17c-44c0-8f65-cb94528924f9", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "sql script", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.879b1b2b-8174-450b-8f0e-83ec2e818f29", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-1f4f", "versionId": "de0c75ce-02a0-46be-85ac-d9f180abf22d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "234", "y": "191", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.879b1b2b-8174-450b-8f0e-83ec2e818f29", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sql = \"SELECT ID, REQ<PERSON><PERSON><PERSON><PERSON>, REQ<PERSON><PERSON>NATURE, REQUESTTYPE, REQUESTDATE, REQUESTSTATE, REQUESTSTATUS, \"\r\r\n+\"PARENTREQUESTN<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CONTRA<PERSON>STAG<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>PO<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>R<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ORY, CO<PERSON><PERSON><PERSON><PERSON><PERSON>SCRIPTION, \"\r\r\n+\"  ODC_REQUESTINFO.CIF, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BASEDATE, ODC_REQUESTINFO.VALUEDATE, TENORDAYS, TRANSITDAYS, \"\r\r\n+\"CONTRACTMATURITYDATE, USERREF, PRODUCTCODE, PRODUCT<PERSON>SC, STAGE, SOUR<PERSON>RE<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>EFN<PERSON>,\"\r\r\n+\"DISCOUNT, E<PERSON>RACHARGES, O<PERSON><PERSON>ARGES, AMOUNTSIGHT, AMOUNTDEF<PERSON>OAVALIZATION, AMOUNT<PERSON><PERSON>VALIZATION, COLLECTABLEAMOUNT, \"\r\r\n+\" OUTSTAN<PERSON>NGAMOUNT, MATURITYDA<PERSON>, <PERSON><PERSON><PERSON>TY<PERSON>Y<PERSON>, <PERSON><PERSON>ANCE<PERSON><PERSON><PERSON><PERSON><PERSON>N<PERSON>, <PERSON><PERSON><PERSON>OD<PERSON>, <PERSON><PERSON><PERSON>NA<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\"\r\r\n+\" <PERSON>IQCURRENCY, COLLECTIONCURRENCY, STANDARDEXRATE, NEGOTIATEDEXRATE, ALLOCATEDAMOUNT, TOTALALLOCATEDAMOUNT, TRANSACTIONREFNO,\"\r\r\n+\" CBECLASSIFICATION, SHIPMENTMETHOD, SHIPMENTDATE, REVERSALREASON, CLOSUREREASON, IMPORTERNAME, IMPORTERCOUNTRY, \"\r\r\n+\" IMPORTERADDRESS, IMPORTERPHONENO, IMPORTERBANK, BICCODE, IBANACCOUNT, BANKCOUNTRY, BANKADDRESS, \"\r\r\n+\" BANKPHONENO, COLLECTINGBANKREF , \"//64\r\r\n+\"ODC_FCTRANSACTION.ACCOUNTNO, ODC_FCTRANSACTION.POSTINGDATE, ODC_FCTRANSACTION.VALUEDATE, ODC_FCTRANSACTION.TRANSACTIONAMOUNT, ODC_FCTRANSACTION.AMOUNTINBPM, \"\r\r\n+\" ODC_PARTIESINFO.PARTYID, ODC_PARTIESINFO.PARTYTYPE, ODC_PARTIESINFO.CIF\"\r\r\n+\" FROM ODC_REQUESTINFO\"\r\r\n+\"    Inner join ODC_FCTRANSACTION   on ODC_REQUESTINFO.ID = ODC_FCTRANSACTION.REQUESRID       \" \r\r\n+\"    Inner join ODC_PARTIESINFO     on ODC_REQUESTINFO.ID = ODC_PARTIESINFO.REQUESRID         \"\r\r\n\r\r\n+\"    where ODC_REQUESTINFO.REQUESTNO like  '\"+tw.local.requestNumber+\"'  ; \"\r\r\n\r\r\n\r\r\n\r\r\n\t               ", "isRule": "false", "guid": "7730359b-903e-438d-bf00-3801b33cc942", "versionId": "1d29c986-507d-4c56-91a0-1afc7fb3ba41"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.faafa041-87b0-41de-8a80-fe8fb89bca0b", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "odc object init Service", "tWComponentName": "SubProcess", "tWComponentId": "3012.1c3828dd-f4cc-4bd6-be79-fbff4e980b36", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "guid": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:-17dc", "versionId": "df3c7950-4a5b-4cd7-8361-22131bec41e0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "545", "y": "192", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-61af", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.1c3828dd-f4cc-4bd6-be79-fbff4e980b36", "attachedProcessRef": "/1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "guid": "fc76e82b-8654-4923-b8b9-955cdff707d7", "versionId": "41feb2d4-7843-463c-b26a-b849147512ad", "parameterMapping": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.710f111e-2310-43b4-babd-c5d6acdb108a", "processParameterId": "2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5", "parameterMappingParentId": "3012.1c3828dd-f4cc-4bd6-be79-fbff4e980b36", "useDefault": "false", "value": "tw.local.odcRequest", "classRef": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isList": "false", "isInput": "true", "guid": "b21ce407-f8f4-47f2-bf7e-83753251ac6f", "versionId": "0cc6b992-d5af-4f55-aba6-f7ba5e812e19", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b0870359-8bd3-4542-ba0f-0c17876c244e", "processParameterId": "2055.247d12a8-cf76-495c-8cd4-700f32286023", "parameterMappingParentId": "3012.1c3828dd-f4cc-4bd6-be79-fbff4e980b36", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "e2d47363-e64e-4c5d-88e4-d44f15d4d4db", "versionId": "4b89ea9d-10cf-4e3b-b5fa-56ae6d40c3f3", "description": {"isNull": "true"}}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.41c9a273-fae9-4c3c-90b5-5f796deb5922", "processParameterId": "2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de", "parameterMappingParentId": "3012.1c3828dd-f4cc-4bd6-be79-fbff4e980b36", "useDefault": "false", "value": "tw.local.odcRequest", "classRef": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isList": "false", "isInput": "false", "guid": "f01c5285-82ad-48f1-ace5-7be6860407b9", "versionId": "f08ff50d-2183-4c95-8c2a-f614b0216486", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.dbfefe55-69f0-4717-8a38-c0d013025f41", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "name": "Mapping Output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.7b965f6b-d31c-494f-9dc0-f00582c1d42c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "guid": "guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-1f7e", "versionId": "fad78189-8378-47ea-890b-0d3f416e72db", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "695", "y": "192", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-61af", "errorHandlerItemId": "2025.bf318cac-19b9-4d69-8048-a77915d5572c", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.7b965f6b-d31c-494f-9dc0-f00582c1d42c", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.odcRequest.BasicDetails ={};\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose     = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms\t   = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.productCategory   = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ContractCreation.productCode   = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub= new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ProductShipmentDetails.shipmentMethod = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ImporterDetails.importerCountry = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ImporterDetails.bankCountry = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.accountNo     = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.currency      = new tw.object.NameValuePair();\r\r\n\r\r\n\r\r\nif(tw.local.results.listLength > 0 )\r\r\n{\r\r\n\ttw.local.odcRequest.requestNo \t\t\t= tw.local.results[0].rows[0].data[1];\r\r\n\ttw.local.odcRequest.requestNature.value \t\t= tw.local.results[0].rows[0].data[2];\r\r\n\ttw.local.odcRequest.requestType.value \t\t= tw.local.results[0].rows[0].data[3];\r\r\n\ttw.local.odcRequest.requestDate\t\t      = tw.local.results[0].rows[0].data[4];\r\r\n\t\r\r\n\ttw.local.odcRequest.appInfo.status \t\t      = tw.local.results[0].rows[0].data[6];\r\r\n\t\r\r\n\t\r\r\n\ttw.local.odcRequest.BasicDetails.requestType \t\t\t= tw.local.results[0].rows[0].data[3];\r\r\n\ttw.local.odcRequest.BasicDetails.requestNature  \t\t= tw.local.results[0].rows[0].data[2];\r\r\n\ttw.local.odcRequest.BasicDetails.requestState \t\t\t= tw.local.results[0].rows[0].data[5];\r\r\n\ttw.local.odcRequest.BasicDetails.parentRequestNo \t\t= tw.local.results[0].rows[0].data[7];\r\r\n\ttw.local.odcRequest.BasicDetails.flexCubeContractNo \t\t= tw.local.results[0].rows[0].data[8];\r\r\n\ttw.local.odcRequest.BasicDetails.contractStage \t\t\t= tw.local.results[0].rows[0].data[9];\r\r\n\ttw.local.odcRequest.BasicDetails.exportPurpose.value \t\t= tw.local.results[0].rows[0].data[10];\r\r\n\ttw.local.odcRequest.BasicDetails.paymentTerms.value \t\t= tw.local.results[0].rows[0].data[11];\r\r\n\ttw.local.odcRequest.BasicDetails.productCategory.value \t= tw.local.results[0].rows[0].data[12];\r\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription \t= tw.local.results[0].rows[0].data[13];\r\r\n\t\r\r\n\ttw.local.odcRequest.cif \t\t\t\t\t\t= tw.local.results[0].rows[0].data[14];\r\r\n\ttw.local.odcRequest.CustomerInfo.cif\t\t\t\t= tw.local.results[0].rows[0].data[14];\r\r\n\ttw.local.odcRequest.CustomerInfo.customerName\t\t\t= tw.local.results[0].rows[0].data[15];\r\r\n\ttw.local.odcRequest.customerName\t\t\t\t\t= tw.local.results[0].rows[0].data[15];\r\r\n\t\r\r\n\ttw.local.odcRequest.ContractCreation.baseDate     \t\t= tw.local.results[0].rows[0].data[16];\r\r\n\ttw.local.odcRequest.ContractCreation.valueDate\t\t\t= tw.local.results[0].rows[0].data[17];\r\r\n\ttw.local.odcRequest.ContractCreation.tenorDays\t\t\t= tw.local.results[0].rows[0].data[18];\r\r\n\ttw.local.odcRequest.ContractCreation.transitDays\t\t= tw.local.results[0].rows[0].data[19];\r\r\n\ttw.local.odcRequest.ContractCreation.maturityDate\t\t= tw.local.results[0].rows[0].data[20];\r\r\n\t\r\r\n\ttw.local.odcRequest.ContractCreation.userReference\t\t= tw.local.results[0].rows[0].data[21];\r\r\n\ttw.local.odcRequest.ContractCreation.productCode.value\t\t= tw.local.results[0].rows[0].data[22];\r\r\n\ttw.local.odcRequest.ContractCreation.productDescription\t= tw.local.results[0].rows[0].data[23];\r\r\n\ttw.local.odcRequest.ContractCreation.Stage\t\t\t= tw.local.results[0].rows[0].data[24];\r\r\n\ttw.local.odcRequest.ContractCreation.sourceReference\t\t= tw.local.results[0].rows[0].data[25];\r\r\n\t\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.referenceNo\t\t= tw.local.results[0].rows[0].data[26];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.discount\t\t\t= tw.local.results[0].rows[0].data[27];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.extraCharges\t\t\t= tw.local.results[0].rows[0].data[28];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.ourCharges\t\t\t= tw.local.results[0].rows[0].data[29];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight\t\t\t= tw.local.results[0].rows[0].data[30];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization     = tw.local.results[0].rows[0].data[31];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\t      = tw.local.results[0].rows[0].data[32];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount\t\t= tw.local.results[0].rows[0].data[33];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.outstandingAmount\t\t= tw.local.results[0].rows[0].data[34];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.maturityDate\t\t\t= tw.local.results[0].rows[0].data[35];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\t\t= tw.local.results[0].rows[0].data[36];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.financeApprovalNo\t\t= tw.local.results[0].rows[0].data[37];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.executionHub.value\t\t= tw.local.results[0].rows[0].data[38];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.executionHub.name\t\t= tw.local.results[0].rows[0].data[39];\r\r\n\t\r\r\n\r\r\n\ttw.local.odcRequest.OdcCollection.amount\t\t= tw.local.results[0].rows[0].data[40];\r\r\n\ttw.local.odcRequest.OdcCollection.currency\t= tw.local.results[0].rows[0].data[41];\r\r\n\t\r\r\n\t/////need to ask about allocated amount and trans ref no\r\r\n//\ttw.local.odcRequest.FcCollections.standardExchangeRate\t\t= tw.local.results[0].rows[0].data[43];\r\r\n//\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate\t\t= tw.local.results[0].rows[0].data[44];\r\r\n//\t//tw.local.odcRequest.FcCollections.usedAmount\t\t            = tw.local.results[0].rows[0].data[45];\r\r\n//\ttw.local.odcRequest.FcCollections.totalAllocatedAmount\t\t= tw.local.results[0].rows[0].data[46];\r\r\n//\t\r\r\n//\ttw.local.odcRequest.FinancialDetailsFO.referenceNo\t\t      = tw.local.results[0].rows[0].data[47];\r\r\n\t\r\r\n\t\r\r\n\ttw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.value\t\t= tw.local.results[0].rows[0].data[48];\r\r\n\ttw.local.odcRequest.ProductShipmentDetails.shipmentMethod.value\t\t\t\t= tw.local.results[0].rows[0].data[49];\r\r\n\ttw.local.odcRequest.ProductShipmentDetails.shippingDate\t\t\t\t= tw.local.results[0].rows[0].data[50];\r\r\n\t\r\r\n\ttw.local.odcRequest.ReversalReason.reversalReason\t\t= tw.local.results[0].rows[0].data[51];\r\r\n\ttw.local.odcRequest.ReversalReason.closureReason\t\t= tw.local.results[0].rows[0].data[52];\r\r\n\t\r\r\n\ttw.local.odcRequest.ImporterDetails.importerName\t\t\t= tw.local.results[0].rows[0].data[53];\r\r\n\ttw.local.odcRequest.ImporterDetails.importerCountry.value\t\t= tw.local.results[0].rows[0].data[54];\r\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress\t\t\t= tw.local.results[0].rows[0].data[55];\r\r\n\ttw.local.odcRequest.ImporterDetails.importerPhoneNo\t\t\t= tw.local.results[0].rows[0].data[56];\r\r\n\ttw.local.odcRequest.ImporterDetails.bank\t\t\t\t\t= tw.local.results[0].rows[0].data[57];\r\r\n\ttw.local.odcRequest.ImporterDetails.BICCode\t\t\t\t= tw.local.results[0].rows[0].data[58];\r\r\n\ttw.local.odcRequest.ImporterDetails.ibanAccount\t\t\t\t= tw.local.results[0].rows[0].data[59];\r\r\n\ttw.local.odcRequest.ImporterDetails.bankCountry.value\t\t\t= tw.local.results[0].rows[0].data[60];\r\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress\t\t\t\t= tw.local.results[0].rows[0].data[61];\r\r\n\ttw.local.odcRequest.ImporterDetails.bankPhoneNo\t\t\t\t= tw.local.results[0].rows[0].data[62];\r\r\n\ttw.local.odcRequest.ImporterDetails.collectingBankReference\t\t= tw.local.results[0].rows[0].data[63];\r\r\n\r\r\n\r\r\n//\"ODC_FCTRANSACTION.ACCOUNTNO, ODC_FCTRANSACTION.POSTINGDATE, ODC_FCTRANSACTION.VALUEDATE, ODC_FCTRANSACTION.TRANSACTIONAMOUNT, ODC_FCTRANSACTION.AMOUNTINBPM, \"\r\r\n//+\" ODC_PARTIESINFO.PARTYID, ODC_PARTIESINFO.PARTYTYPE, ODC_PARTIESINFO.CIF\"\r\r\n\ttw.local.odcRequest.FcCollections.accountNo \t\t= new tw.object.listOf.NameValuePair();\r\r\n\ttw.local.odcRequest.FcCollections.accountNo.value     =  tw.local.results[0].rows[0].data[64];\r\r\n\ttw.local.odcRequest.FcCollections.fromDate  \t\t=  tw.local.results[0].rows[0].data[65];//postingDate\r\r\n\ttw.local.odcRequest.FcCollections.ToDate  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n\ttw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount  \t\t=  tw.local.results[0].rows[0].data[65];//transactionamount\r\r\n\t//tw.local.odcRequest.FcCollections.  \t\t=  tw.local.results[0].rows[0].data[65];//amount in bpm\r\r\n\t//tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount  =  tw.local.results[0].rows[0].data[65];//transactionamount\r\r\n\t\r\r\n\t//tw.local.odcRequest.Parties.partyTypes.partyId  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n\t//tw.local.odcRequest.Parties.partyTypes.partyType  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n\t//tw.local.odcRequest.Parties.partyTypes.partyCIF  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n}", "isRule": "false", "guid": "bae3b9db-1638-4b9b-9072-2cdfed6fcfea", "versionId": "afd41b5b-5f1a-4d3d-86ac-31c6f6139f90"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "100", "y": "214", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "retrieve Odc request Data", "id": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "requestNumber", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.c1f30445-c933-43d3-81d5-4bb22f76c46f", "ns16:documentation": {"_": "//tw.local.results[0].columnIndexes", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "//\"11111111111111_023\"\r\r\n\"11223344556677\"", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.545510ce-ad3f-4e51-8848-e60223dc7eed"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.42c2aa04-75bd-4785-8a9e-dbfbf46cc496"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.c1f30445-c933-43d3-81d5-4bb22f76c46f"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.545510ce-ad3f-4e51-8848-e60223dc7eed", "2055.42c2aa04-75bd-4785-8a9e-dbfbf46cc496"]}}, "ns16:laneSet": {"id": "fd3d6dac-321c-49ff-8c01-ce46aa3c68e8", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "bac5a3f9-f109-41c3-8f2e-ed29f6384db0", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["5b89eae5-f71d-42b5-86f1-a474d2bf7f66", "96d725a5-dcab-4d31-890f-3e065fc00594", "f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "dbfefe55-69f0-4717-8a38-c0d013025f41", "e453880a-a17c-44c0-8f65-cb94528924f9", "21fa0ae0-b17c-4070-83be-c0263fc49e1a", "6252e11a-00cc-45ce-89a9-3cb606ea687f", "bf318cac-19b9-4d69-8048-a77915d5572c", "faafa041-87b0-41de-8a80-fe8fb89bca0b", "4414718d-b38a-4fb5-809f-6e2ef44e794e", "3b47645a-bfb5-4d47-86df-4d44f0b958d3", "fbd86b97-5ea2-4b4c-8412-92821eeda748", "6b2dae35-2c9b-4501-8544-5f0587d87adc"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "5b89eae5-f71d-42b5-86f1-a474d2bf7f66", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "100", "y": "214", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "c690c7d1-5e44-44cc-8a17-30a5efab2cf7"}, "ns16:endEvent": {"name": "End", "id": "96d725a5-dcab-4d31-890f-3e065fc00594", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "990", "y": "215", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-1f83"}, "ns16:incoming": ["7dc5fb1c-3096-4a55-8cbe-33c357ceff97", "bd436f25-2a76-4d05-8e08-1f520694e225"]}, "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Sql Execute statement", "id": "f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "405", "y": "191", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "a15a62b6-9036-4a76-8a64-87f6ad1e0bee", "ns16:outgoing": "d6cdaa80-fd0d-4762-8beb-d9408f3cabf3", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exception Handling", "id": "bf318cac-19b9-4d69-8048-a77915d5572c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "544", "y": "316", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["bcfac1fa-5a0c-4ade-8321-1b8b09d1f8e1", "e0f91632-e0c3-46f7-88d3-96f1b98fc243", "8f98589d-e89e-45bd-883b-f6bedcc35e18", "7ea91734-4459-43cc-84cf-98808567d537"], "ns16:outgoing": "bd436f25-2a76-4d05-8e08-1f520694e225", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Retrieve Odc request data\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "odc object init Service", "id": "faafa041-87b0-41de-8a80-fe8fb89bca0b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "545", "y": "192", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "d6cdaa80-fd0d-4762-8beb-d9408f3cabf3", "ns16:outgoing": "1a7ed6a4-07e9-4c1d-8c5a-5d2394e2023e", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.247d12a8-cf76-495c-8cd4-700f32286023", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "retrieve rest of odc data 1:M relation", "id": "4414718d-b38a-4fb5-809f-6e2ef44e794e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "832", "y": "192", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "e55f589e-15a5-4e7f-8bf9-86962f844303", "ns16:outgoing": "7dc5fb1c-3096-4a55-8cbe-33c357ceff97", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.4c3318d7-370d-4d28-80de-b197f1a2aaec", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestNumber", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.33b5dccf-**************-560ecde149f2", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.6ff165ef-cd1e-4b27-8678-e809adfd458d", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}]}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Mapping Output", "id": "dbfefe55-69f0-4717-8a38-c0d013025f41", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "695", "y": "192", "width": "95", "height": "70"}}, "ns16:incoming": "1a7ed6a4-07e9-4c1d-8c5a-5d2394e2023e", "ns16:outgoing": "e55f589e-15a5-4e7f-8bf9-86962f844303", "ns16:script": "tw.local.odcRequest.BasicDetails ={};\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose     = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms\t   = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.productCategory   = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ContractCreation.productCode   = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub= new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ProductShipmentDetails.shipmentMethod = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ImporterDetails.importerCountry = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ImporterDetails.bankCountry = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.accountNo     = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.currency      = new tw.object.NameValuePair();\r\r\n\r\r\n\r\r\nif(tw.local.results.listLength > 0 )\r\r\n{\r\r\n\ttw.local.odcRequest.requestNo \t\t\t= tw.local.results[0].rows[0].data[1];\r\r\n\ttw.local.odcRequest.requestNature.value \t\t= tw.local.results[0].rows[0].data[2];\r\r\n\ttw.local.odcRequest.requestType.value \t\t= tw.local.results[0].rows[0].data[3];\r\r\n\ttw.local.odcRequest.requestDate\t\t      = tw.local.results[0].rows[0].data[4];\r\r\n\t\r\r\n\ttw.local.odcRequest.appInfo.status \t\t      = tw.local.results[0].rows[0].data[6];\r\r\n\t\r\r\n\t\r\r\n\ttw.local.odcRequest.BasicDetails.requestType \t\t\t= tw.local.results[0].rows[0].data[3];\r\r\n\ttw.local.odcRequest.BasicDetails.requestNature  \t\t= tw.local.results[0].rows[0].data[2];\r\r\n\ttw.local.odcRequest.BasicDetails.requestState \t\t\t= tw.local.results[0].rows[0].data[5];\r\r\n\ttw.local.odcRequest.BasicDetails.parentRequestNo \t\t= tw.local.results[0].rows[0].data[7];\r\r\n\ttw.local.odcRequest.BasicDetails.flexCubeContractNo \t\t= tw.local.results[0].rows[0].data[8];\r\r\n\ttw.local.odcRequest.BasicDetails.contractStage \t\t\t= tw.local.results[0].rows[0].data[9];\r\r\n\ttw.local.odcRequest.BasicDetails.exportPurpose.value \t\t= tw.local.results[0].rows[0].data[10];\r\r\n\ttw.local.odcRequest.BasicDetails.paymentTerms.value \t\t= tw.local.results[0].rows[0].data[11];\r\r\n\ttw.local.odcRequest.BasicDetails.productCategory.value \t= tw.local.results[0].rows[0].data[12];\r\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription \t= tw.local.results[0].rows[0].data[13];\r\r\n\t\r\r\n\ttw.local.odcRequest.cif \t\t\t\t\t\t= tw.local.results[0].rows[0].data[14];\r\r\n\ttw.local.odcRequest.CustomerInfo.cif\t\t\t\t= tw.local.results[0].rows[0].data[14];\r\r\n\ttw.local.odcRequest.CustomerInfo.customerName\t\t\t= tw.local.results[0].rows[0].data[15];\r\r\n\ttw.local.odcRequest.customerName\t\t\t\t\t= tw.local.results[0].rows[0].data[15];\r\r\n\t\r\r\n\ttw.local.odcRequest.ContractCreation.baseDate     \t\t= tw.local.results[0].rows[0].data[16];\r\r\n\ttw.local.odcRequest.ContractCreation.valueDate\t\t\t= tw.local.results[0].rows[0].data[17];\r\r\n\ttw.local.odcRequest.ContractCreation.tenorDays\t\t\t= tw.local.results[0].rows[0].data[18];\r\r\n\ttw.local.odcRequest.ContractCreation.transitDays\t\t= tw.local.results[0].rows[0].data[19];\r\r\n\ttw.local.odcRequest.ContractCreation.maturityDate\t\t= tw.local.results[0].rows[0].data[20];\r\r\n\t\r\r\n\ttw.local.odcRequest.ContractCreation.userReference\t\t= tw.local.results[0].rows[0].data[21];\r\r\n\ttw.local.odcRequest.ContractCreation.productCode.value\t\t= tw.local.results[0].rows[0].data[22];\r\r\n\ttw.local.odcRequest.ContractCreation.productDescription\t= tw.local.results[0].rows[0].data[23];\r\r\n\ttw.local.odcRequest.ContractCreation.Stage\t\t\t= tw.local.results[0].rows[0].data[24];\r\r\n\ttw.local.odcRequest.ContractCreation.sourceReference\t\t= tw.local.results[0].rows[0].data[25];\r\r\n\t\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.referenceNo\t\t= tw.local.results[0].rows[0].data[26];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.discount\t\t\t= tw.local.results[0].rows[0].data[27];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.extraCharges\t\t\t= tw.local.results[0].rows[0].data[28];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.ourCharges\t\t\t= tw.local.results[0].rows[0].data[29];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight\t\t\t= tw.local.results[0].rows[0].data[30];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization     = tw.local.results[0].rows[0].data[31];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\t      = tw.local.results[0].rows[0].data[32];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount\t\t= tw.local.results[0].rows[0].data[33];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.outstandingAmount\t\t= tw.local.results[0].rows[0].data[34];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.maturityDate\t\t\t= tw.local.results[0].rows[0].data[35];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\t\t= tw.local.results[0].rows[0].data[36];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.financeApprovalNo\t\t= tw.local.results[0].rows[0].data[37];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.executionHub.value\t\t= tw.local.results[0].rows[0].data[38];\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.executionHub.name\t\t= tw.local.results[0].rows[0].data[39];\r\r\n\t\r\r\n\r\r\n\ttw.local.odcRequest.OdcCollection.amount\t\t= tw.local.results[0].rows[0].data[40];\r\r\n\ttw.local.odcRequest.OdcCollection.currency\t= tw.local.results[0].rows[0].data[41];\r\r\n\t\r\r\n\t/////need to ask about allocated amount and trans ref no\r\r\n//\ttw.local.odcRequest.FcCollections.standardExchangeRate\t\t= tw.local.results[0].rows[0].data[43];\r\r\n//\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate\t\t= tw.local.results[0].rows[0].data[44];\r\r\n//\t//tw.local.odcRequest.FcCollections.usedAmount\t\t            = tw.local.results[0].rows[0].data[45];\r\r\n//\ttw.local.odcRequest.FcCollections.totalAllocatedAmount\t\t= tw.local.results[0].rows[0].data[46];\r\r\n//\t\r\r\n//\ttw.local.odcRequest.FinancialDetailsFO.referenceNo\t\t      = tw.local.results[0].rows[0].data[47];\r\r\n\t\r\r\n\t\r\r\n\ttw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.value\t\t= tw.local.results[0].rows[0].data[48];\r\r\n\ttw.local.odcRequest.ProductShipmentDetails.shipmentMethod.value\t\t\t\t= tw.local.results[0].rows[0].data[49];\r\r\n\ttw.local.odcRequest.ProductShipmentDetails.shippingDate\t\t\t\t= tw.local.results[0].rows[0].data[50];\r\r\n\t\r\r\n\ttw.local.odcRequest.ReversalReason.reversalReason\t\t= tw.local.results[0].rows[0].data[51];\r\r\n\ttw.local.odcRequest.ReversalReason.closureReason\t\t= tw.local.results[0].rows[0].data[52];\r\r\n\t\r\r\n\ttw.local.odcRequest.ImporterDetails.importerName\t\t\t= tw.local.results[0].rows[0].data[53];\r\r\n\ttw.local.odcRequest.ImporterDetails.importerCountry.value\t\t= tw.local.results[0].rows[0].data[54];\r\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress\t\t\t= tw.local.results[0].rows[0].data[55];\r\r\n\ttw.local.odcRequest.ImporterDetails.importerPhoneNo\t\t\t= tw.local.results[0].rows[0].data[56];\r\r\n\ttw.local.odcRequest.ImporterDetails.bank\t\t\t\t\t= tw.local.results[0].rows[0].data[57];\r\r\n\ttw.local.odcRequest.ImporterDetails.BICCode\t\t\t\t= tw.local.results[0].rows[0].data[58];\r\r\n\ttw.local.odcRequest.ImporterDetails.ibanAccount\t\t\t\t= tw.local.results[0].rows[0].data[59];\r\r\n\ttw.local.odcRequest.ImporterDetails.bankCountry.value\t\t\t= tw.local.results[0].rows[0].data[60];\r\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress\t\t\t\t= tw.local.results[0].rows[0].data[61];\r\r\n\ttw.local.odcRequest.ImporterDetails.bankPhoneNo\t\t\t\t= tw.local.results[0].rows[0].data[62];\r\r\n\ttw.local.odcRequest.ImporterDetails.collectingBankReference\t\t= tw.local.results[0].rows[0].data[63];\r\r\n\r\r\n\r\r\n//\"ODC_FCTRANSACTION.ACCOUNTNO, ODC_FCTRANSACTION.POSTINGDATE, ODC_FCTRANSACTION.VALUEDATE, ODC_FCTRANSACTION.TRANSACTIONAMOUNT, ODC_FCTRANSACTION.AMOUNTINBPM, \"\r\r\n//+\" ODC_PARTIESINFO.PARTYID, ODC_PARTIESINFO.PARTYTYPE, ODC_PARTIESINFO.CIF\"\r\r\n\ttw.local.odcRequest.FcCollections.accountNo \t\t= new tw.object.listOf.NameValuePair();\r\r\n\ttw.local.odcRequest.FcCollections.accountNo.value     =  tw.local.results[0].rows[0].data[64];\r\r\n\ttw.local.odcRequest.FcCollections.fromDate  \t\t=  tw.local.results[0].rows[0].data[65];//postingDate\r\r\n\ttw.local.odcRequest.FcCollections.ToDate  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n\ttw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount  \t\t=  tw.local.results[0].rows[0].data[65];//transactionamount\r\r\n\t//tw.local.odcRequest.FcCollections.  \t\t=  tw.local.results[0].rows[0].data[65];//amount in bpm\r\r\n\t//tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount  =  tw.local.results[0].rows[0].data[65];//transactionamount\r\r\n\t\r\r\n\t//tw.local.odcRequest.Parties.partyTypes.partyId  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n\t//tw.local.odcRequest.Parties.partyTypes.partyType  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n\t//tw.local.odcRequest.Parties.partyTypes.partyCIF  \t\t=  tw.local.results[0].rows[0].data[66];//valueDate\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "sql script", "id": "e453880a-a17c-44c0-8f65-cb94528924f9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "234", "y": "191", "width": "95", "height": "70"}}, "ns16:incoming": "c690c7d1-5e44-44cc-8a17-30a5efab2cf7", "ns16:outgoing": "a15a62b6-9036-4a76-8a64-87f6ad1e0bee", "ns16:script": "tw.local.sql = \"SELECT ID, REQ<PERSON><PERSON><PERSON><PERSON>, REQ<PERSON><PERSON>NATURE, REQUESTTYPE, REQUESTDATE, REQUESTSTATE, REQUESTSTATUS, \"\r\r\n+\"PARENTREQUESTN<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CONTRA<PERSON>STAG<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>PO<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>R<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ORY, CO<PERSON><PERSON><PERSON><PERSON><PERSON>SCRIPTION, \"\r\r\n+\"  ODC_REQUESTINFO.CIF, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BASEDATE, ODC_REQUESTINFO.VALUEDATE, TENORDAYS, TRANSITDAYS, \"\r\r\n+\"CONTRACTMATURITYDATE, USERREF, PRODUCTCODE, PRODUCT<PERSON>SC, STAGE, SOUR<PERSON>RE<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>EFN<PERSON>,\"\r\r\n+\"DISCOUNT, E<PERSON>RACHARGES, O<PERSON><PERSON>ARGES, AMOUNTSIGHT, AMOUNTDEF<PERSON>OAVALIZATION, AMOUNT<PERSON><PERSON>VALIZATION, COLLECTABLEAMOUNT, \"\r\r\n+\" OUTSTAN<PERSON>NGAMOUNT, MATURITYDA<PERSON>, <PERSON><PERSON><PERSON>TY<PERSON>Y<PERSON>, <PERSON><PERSON>ANCE<PERSON><PERSON><PERSON><PERSON><PERSON>N<PERSON>, <PERSON><PERSON><PERSON>OD<PERSON>, <PERSON><PERSON><PERSON>NA<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\"\r\r\n+\" <PERSON>IQCURRENCY, COLLECTIONCURRENCY, STANDARDEXRATE, NEGOTIATEDEXRATE, ALLOCATEDAMOUNT, TOTALALLOCATEDAMOUNT, TRANSACTIONREFNO,\"\r\r\n+\" CBECLASSIFICATION, SHIPMENTMETHOD, SHIPMENTDATE, REVERSALREASON, CLOSUREREASON, IMPORTERNAME, IMPORTERCOUNTRY, \"\r\r\n+\" IMPORTERADDRESS, IMPORTERPHONENO, IMPORTERBANK, BICCODE, IBANACCOUNT, BANKCOUNTRY, BANKADDRESS, \"\r\r\n+\" BANKPHONENO, COLLECTINGBANKREF , \"//64\r\r\n+\"ODC_FCTRANSACTION.ACCOUNTNO, ODC_FCTRANSACTION.POSTINGDATE, ODC_FCTRANSACTION.VALUEDATE, ODC_FCTRANSACTION.TRANSACTIONAMOUNT, ODC_FCTRANSACTION.AMOUNTINBPM, \"\r\r\n+\" ODC_PARTIESINFO.PARTYID, ODC_PARTIESINFO.PARTYTYPE, ODC_PARTIESINFO.CIF\"\r\r\n+\" FROM ODC_REQUESTINFO\"\r\r\n+\"    Inner join ODC_FCTRANSACTION   on ODC_REQUESTINFO.ID = ODC_FCTRANSACTION.REQUESRID       \" \r\r\n+\"    Inner join ODC_PARTIESINFO     on ODC_REQUESTINFO.ID = ODC_PARTIESINFO.REQUESRID         \"\r\r\n\r\r\n+\"    where ODC_REQUESTINFO.REQUESTNO like  '\"+tw.local.requestNumber+\"'  ; \"\r\r\n\r\r\n\r\r\n\r\r\n\t               "}, {"scriptFormat": "text/plain", "name": "SQL", "id": "6252e11a-00cc-45ce-89a9-3cb606ea687f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "225", "y": "95", "width": "95", "height": "70"}}, "ns16:script": "tw.local.sql\r\nSELECT  REQUESTNO,  R<PERSON><PERSON><PERSON><PERSON><PERSON>TU<PERSON>, REQ<PERSON><PERSON><PERSON><PERSON><PERSON>, REQUESTDATE, REQUESTSTATE, REQUESTSTATUS, PARENTREQ<PERSON><PERSON>N<PERSON>,\r\r\n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CONTRACTSTAGE, <PERSON><PERSON><PERSON><PERSON><PERSON>URPO<PERSON>, <PERSON>Y<PERSON>NTTER<PERSON>, <PERSON><PERSON><PERSON><PERSON>AT<PERSON>ORY, COMM<PERSON><PERSON><PERSON>DESCRIPTION, --RI.<PERSON>LIQUIDATED,\r\r\nRI.CIF, RI.CUS<PERSON><PERSON><PERSON>ME, RI.BASEDATE, RI.VALUEDATE, RI.TENORDAYS, RI.TRANSITDAYS, RI.CONTRACTMATURITYDATE, \r\r\nRI.USERREF, RI.PRODUCTCODE, PRODUCTDESC, STAGE, SO<PERSON><PERSON>RE<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>EFN<PERSON>, <PERSON><PERSON><PERSON>UN<PERSON>, <PERSON><PERSON>RA<PERSON>ARGES, <PERSON><PERSON><PERSON>ARGES,\r\r\nAM<PERSON>UNTSIGHT, AMOUNTDEF<PERSON><PERSON>VA<PERSON>IZATION, AMOUNTDE<PERSON>VA<PERSON>IZATION, COLLEC<PERSON>BL<PERSON>MOUNT, OUTSTANDINGAMOUNT, MA<PERSON>RITYDA<PERSON>,\r\r\n<PERSON><PERSON>RITY<PERSON><PERSON>S, <PERSON><PERSON>ANCE<PERSON>PR<PERSON><PERSON>LNO, H<PERSON><PERSON>ODE, H<PERSON>BNAME, LIQAMOUN<PERSON>, LIQ<PERSON>RRENCY, COLLECTIONCURRENCY, RI.STANDARDEXRATE, \r\r\nRI.NEGOTIATEDEXRATE, ALLOCATEDAMOUNT, TOTALALLOCATEDAMOUNT, TRANSACTIONREFNO, CBECLASSIFICATION, SHIPMENTMETHOD,\r\r\nSHIPMENTDATE, REVERSALREASON, CLOSUREREASON, IMPORTERNAME, IMPORTERCOUNTRY, IMPORTERADDRESS, IMPORTERPHONENO,\r\r\nIMPORTERBANK, BICCODE, IBANACCOUNT, BANKCOUNTRY, BANKADDRESS, BANKPHONENO, COLLECTINGBANKREF, CURRENTSTEPNAME, \r\r\nRI.BPMINSTANCENUMBER, \r\r\n\r\r\nINV.INVOICENO, INV.INVOICEDATE, \r\r\n\r\r\nB.BILLOFLADINGREF , B.BILLOFLADINGDATE, \r\r\n\r\r\nFCT.ACCOUNTNO,FCT.POSTINGDATE, FCT.VALUEDATE, FCT.TRANSACTIONAMOUNT, FCT.TRANSACTIONAMOUNT, FCT.AMOUNTINBPM,\r\r\n\r\r\nPI.PARTYID,PI.PARTYTYPE,PI.CIF,\r\r\n\r\r\nCC.COMPONENT, CC.DEFAULTCURRENCY, CC.DEFAULTAMOUNT, CC.CHARGEAMOUNT, --CC.WAIVER ,\r\r\nCC.ACCOUNTCLASS, CC.ACCOUNTNO, CC.BRANCHCODE, CC.CURRENCY, CC.BALANCE, CC.BALANCESIGN, CC.STANDARDEXRATE,\r\r\nCC.NEGOTIATEDEXRATE, CC.DEBITEDAMOUNT,\r\r\n\r\r\nMTD.INSTALLMENTDATE, MTD.INSTALLMENTAMOUNT\r\r\n\r\r\nFROM ODC_REQUESTINFO RI\r\r\nINNER JOIN ODC_INVOICE INV   \t\t ON  RI.ID = INV.REQUESRID\r\r\nINNER JOIN ODC_BILLS B       \t \t ON  RI.ID = B.REQUESRID\r\r\nINNER JOIN ODC_FCTRANSACTION FCT   \t ON  RI.ID = FCT.REQUESRID\r\r\nINNER JOIN ODC_PARTIESINFO PI  \t\t ON  RI.ID = PI.REQUESRID\r\r\nINNER JOIN ODC_CHARGESCOMMISSIONS CC ON  RI.ID = CC.REQUESRID\r\r\nINNER JOIN ODC_MULTITENORDATES MTD   ON  RI.ID = MTD.REQUESRID\r\r\n\r\r\nWHERE RI.REQUESTNO = '<#= tw.local.requestNumber #>'\r\r\n\r\r\n         "}], "ns16:sequenceFlow": [{"sourceRef": "e453880a-a17c-44c0-8f65-cb94528924f9", "targetRef": "f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "name": "To Sql Execute statement", "id": "a15a62b6-9036-4a76-8a64-87f6ad1e0bee", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "21fa0ae0-b17c-4070-83be-c0263fc49e1a", "targetRef": "bf318cac-19b9-4d69-8048-a77915d5572c", "name": "To Exception Handling", "id": "bcfac1fa-5a0c-4ade-8321-1b8b09d1f8e1", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "faafa041-87b0-41de-8a80-fe8fb89bca0b", "targetRef": "dbfefe55-69f0-4717-8a38-c0d013025f41", "name": "To Mapping Output", "id": "1a7ed6a4-07e9-4c1d-8c5a-5d2394e2023e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940"}}, {"sourceRef": "f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "targetRef": "faafa041-87b0-41de-8a80-fe8fb89bca0b", "name": "To odc object init Service", "id": "d6cdaa80-fd0d-4762-8beb-d9408f3cabf3", "ns16:extensionElements": {"ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "dbfefe55-69f0-4717-8a38-c0d013025f41", "targetRef": "4414718d-b38a-4fb5-809f-6e2ef44e794e", "name": "To retrieve rest of odc data 1:M relation", "id": "e55f589e-15a5-4e7f-8bf9-86962f844303", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4414718d-b38a-4fb5-809f-6e2ef44e794e", "targetRef": "96d725a5-dcab-4d31-890f-3e065fc00594", "name": "To End", "id": "7dc5fb1c-3096-4a55-8cbe-33c357ceff97", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a22"}}, {"sourceRef": "5b89eae5-f71d-42b5-86f1-a474d2bf7f66", "targetRef": "e453880a-a17c-44c0-8f65-cb94528924f9", "name": "To sql script", "id": "c690c7d1-5e44-44cc-8a17-30a5efab2cf7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "3b47645a-bfb5-4d47-86df-4d44f0b958d3", "targetRef": "bf318cac-19b9-4d69-8048-a77915d5572c", "name": "To Exception Handling", "id": "e0f91632-e0c3-46f7-88d3-96f1b98fc243", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fbd86b97-5ea2-4b4c-8412-92821eeda748", "targetRef": "bf318cac-19b9-4d69-8048-a77915d5572c", "name": "To Exception Handling", "id": "8f98589d-e89e-45bd-883b-f6bedcc35e18", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "6b2dae35-2c9b-4501-8544-5f0587d87adc", "targetRef": "bf318cac-19b9-4d69-8048-a77915d5572c", "name": "To Exception Handling", "id": "7ea91734-4459-43cc-84cf-98808567d537", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "bf318cac-19b9-4d69-8048-a77915d5572c", "targetRef": "96d725a5-dcab-4d31-890f-3e065fc00594", "name": "To End", "id": "bd436f25-2a76-4d05-8e08-1f520694e225", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.9e1923a7-2a6d-43f2-8b40-ff0954a38475"}, {"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "sqlParameters", "id": "2056.031e9385-697a-44c5-83d9-f853decf33e8"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.a9d6a9a7-2c30-4f9e-8272-40cf4b0ce90a"}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "parallelMultiple": "false", "name": "Error", "id": "21fa0ae0-b17c-4070-83be-c0263fc49e1a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "440", "y": "249", "width": "24", "height": "24"}}, "ns16:outgoing": "bcfac1fa-5a0c-4ade-8321-1b8b09d1f8e1", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "613b55ba-dc6f-4d05-8d26-4940b8bba2f3"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "b9b3e802-a45f-4a13-8ee6-02e691b064b6", "eventImplId": "214fc5bc-57dc-4bce-893d-571fa88d260c", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "faafa041-87b0-41de-8a80-fe8fb89bca0b", "parallelMultiple": "false", "name": "Error1", "id": "3b47645a-bfb5-4d47-86df-4d44f0b958d3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "580", "y": "250", "width": "24", "height": "24"}}, "ns16:outgoing": "e0f91632-e0c3-46f7-88d3-96f1b98fc243", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "3b6241da-6d07-454d-8c80-b075dad38db6"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "77c4bb3f-6822-4958-84a5-7036f2079f3d", "eventImplId": "2b36e3bb-836c-4eb4-816f-0f322738032a", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "dbfefe55-69f0-4717-8a38-c0d013025f41", "parallelMultiple": "false", "name": "Error2", "id": "fbd86b97-5ea2-4b4c-8412-92821eeda748", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "730", "y": "250", "width": "24", "height": "24"}}, "ns16:outgoing": "8f98589d-e89e-45bd-883b-f6bedcc35e18", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "13fb13d2-db9c-4dad-8653-5b9cc314a83d"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "e08c8e23-d0a8-42d9-8f69-043e4d922d64", "eventImplId": "71331427-39f6-4e0a-8318-dcf703555613", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "4414718d-b38a-4fb5-809f-6e2ef44e794e", "parallelMultiple": "false", "name": "Error3", "id": "6b2dae35-2c9b-4501-8544-5f0587d87adc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "867", "y": "250", "width": "24", "height": "24"}}, "ns16:outgoing": "7ea91734-4459-43cc-84cf-98808567d537", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "8434658d-4d60-469d-8d68-4208609b09c1"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "42ab8d64-c1e6-4d1e-8ce9-61bf31e3ff59", "eventImplId": "c5167150-a0e2-4ea8-8681-aba6c77633a2", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To Sql Execute statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a15a62b6-9036-4a76-8a64-87f6ad1e0bee", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e453880a-a17c-44c0-8f65-cb94528924f9", "2025.e453880a-a17c-44c0-8f65-cb94528924f9"], "endStateId": "Out", "toProcessItemId": ["2025.f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "2025.f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f"], "guid": "24e31352-b384-4cd0-85b9-b59a7df0535e", "versionId": "46008c07-762d-470f-acbd-5091366d9d91", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7dc5fb1c-3096-4a55-8cbe-33c357ceff97", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4414718d-b38a-4fb5-809f-6e2ef44e794e", "2025.4414718d-b38a-4fb5-809f-6e2ef44e794e"], "endStateId": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a22", "toProcessItemId": ["2025.96d725a5-dcab-4d31-890f-3e065fc00594", "2025.96d725a5-dcab-4d31-890f-3e065fc00594"], "guid": "1647598b-c38e-4333-a8a1-9602a4c61364", "versionId": "518a354f-3e9a-44b7-80e7-24c144cec6eb", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To retrieve rest of odc data 1:M relation", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e55f589e-15a5-4e7f-8bf9-86962f844303", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.dbfefe55-69f0-4717-8a38-c0d013025f41", "2025.dbfefe55-69f0-4717-8a38-c0d013025f41"], "endStateId": "Out", "toProcessItemId": ["2025.4414718d-b38a-4fb5-809f-6e2ef44e794e", "2025.4414718d-b38a-4fb5-809f-6e2ef44e794e"], "guid": "af891540-0fee-4501-892a-66c4ea1a0030", "versionId": "78b33fd7-082d-49d2-b24b-c32c07bbde08", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.bd436f25-2a76-4d05-8e08-1f520694e225", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.bf318cac-19b9-4d69-8048-a77915d5572c", "2025.bf318cac-19b9-4d69-8048-a77915d5572c"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.96d725a5-dcab-4d31-890f-3e065fc00594", "2025.96d725a5-dcab-4d31-890f-3e065fc00594"], "guid": "899ba06a-aa92-4e25-b3cb-72da8804d7b0", "versionId": "7b8181a3-8eb7-46e5-a2de-e26a787673b5", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To odc object init Service", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d6cdaa80-fd0d-4762-8beb-d9408f3cabf3", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f", "2025.f9c31031-4c4c-4a29-8c9a-4432ff2ecc3f"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.faafa041-87b0-41de-8a80-fe8fb89bca0b", "2025.faafa041-87b0-41de-8a80-fe8fb89bca0b"], "guid": "9709c1b1-4346-4444-929e-fa2e554c4ca2", "versionId": "9df9b1cc-f059-4ce3-af92-96d566e57c5f", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Mapping Output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1a7ed6a4-07e9-4c1d-8c5a-5d2394e2023e", "processId": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.faafa041-87b0-41de-8a80-fe8fb89bca0b", "2025.faafa041-87b0-41de-8a80-fe8fb89bca0b"], "endStateId": "guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940", "toProcessItemId": ["2025.dbfefe55-69f0-4717-8a38-c0d013025f41", "2025.dbfefe55-69f0-4717-8a38-c0d013025f41"], "guid": "59810b69-efa6-4fbf-8ffc-a522f479b80e", "versionId": "af75515b-cd6f-47ba-9214-0ec9c2e13f78", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}