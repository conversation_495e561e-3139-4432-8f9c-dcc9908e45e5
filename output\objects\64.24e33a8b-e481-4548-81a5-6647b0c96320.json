{"id": "64.24e33a8b-e481-4548-81a5-6647b0c96320", "versionId": "9bd3cc2d-089b-489c-9a22-9c78560bf5ce", "name": "Commissions And Charges", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "CommissionsAndChargesDetails", "configOptions": ["CommAccountList", "customerCIF", "accounteeCIF", "caseCIF", "draweeCIF", "<PERSON><PERSON><PERSON><PERSON>", "exRate", "accountIndexC", "alertMessage", "errorVis"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.debitedAmountSum = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar changeAm = this.ui.get(\"ChangeAmount[\"+index+\"]\").getData();\r\r\n\tvar nRate = this.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").getData();\r\r\n\tvar sum = changeAm * nRate;\r\r\n\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setData(sum);\r\r\n}\r\r\n\r\r\nthis.executeService = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (value.getData() == \"Customer Account\"){\r\r\n       \tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setEnabled(false);\r\r\n       \tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setEnabled(false);\r\r\n        \tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setEnabled(false);\r\r\n       \tthis.ui.get(\"Owner1[\"+index+\"]\").setEnabled(true);\r\r\n    \t\tif (this.ui.get(\"Owner1[\"+index+\"]\").getData() != \"\") {\r\r\n   \t  \t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(true);\r\r\n    \t  \t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setData(\"\");\r\r\n    \t\t}\r\r\n\t}else if (value.getData() == \"GL Account\"){\r\r\n\t\tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setEnabled(true);\r\r\n       \tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Owner1[\"+index+\"]\").setEnabled(false);\r\r\n   \t  \tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"Decimal1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"Output_Text1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setData(\"\");\r\r\n    }\r\r\n}\r\r\n\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\tthis.context.options.accountIndexC.set(\"value\", value.ui.getIndex());\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"GLAccountNumber\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\nthis.setAccountInfo = function (value){\r\r\n\tfor (var i=0; i<this.context.options.CommAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t// this.context.options.commClassCode.set(\"value\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountClassCode\"));\r\r\n\t\t\t// var commClassCode =  this.context.options.commClassCode.get(\"value\");//SA04\r\r\n\t\t\tcommClassCode = this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0,1);\r\r\n\t\t\t\r\r\n\t\t\tif (code == \"O\" || code == \"D\"){\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountBranchCode\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountCurrency\",{});\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").get(\"accountCurrency\").set(\"code\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountBalance\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"debitedAccount\").get(\"isGLFoundC\")) {\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+record+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.setPartyCIF = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(true);\r\r\n\tif (value.getData() == \"Accountee\") {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.accounteeCIF.get(\"value\"));\r\r\n\t}else if (value.getData() == \"Case In Need\") {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.caseCIF.get(\"value\"));\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.draweeCIF.get(\"value\"));\r\r\n\t}\r\r\n\tthis.ui.get(\"GetCustomerAccount1[\"+index+\"]\").execute(this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"commCIF\"));\r\r\n}\r\r\n\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(value.getData() != \"\" && value.getData() != null && value.getData() != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == value.getData()) {\r\r\n\t\t\tthis.ui.get(\"StandardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setData(this.ui.get(\"StandardExchangeRate[\"+index+\"]\").getData());\r\r\n\t\t}else{\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = value.getData();\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"StandardExchangeRate[\"+index+\"]\").setData(rate.toFixed(6));\r\r\n\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setData(rate.toFixed(6));\r\r\n\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\nthis.setCommAccounts = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"commAccountList\" , []);\r\r\n\tfor (var i=0; i<this.context.options.CommAccountList.get(\"value\").length(); i++) {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"commAccountList\").add({name:this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\") , value:this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\")});\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setData(false);\r\r\n}\r\r\n\r\r\nthis.accountVis = function(value){\r\r\n\tvalue.setEnabled(false);\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(this.context.options.isChecker.get(\"value\") == true || this.ui.get(\"Owner1[\"+index+\"]\").getData() == \"\"){\r\r\n\t\tvalue.setEnabled(false);\r\r\n\t}else\r\r\n\t\tvalue.setEnabled(true);\r\r\n}\r\r\n\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n//--------------------------------------------------------------------------------Drop_2----------------------------------------------------------------------------------------\r\r\n//------------------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}"}]}, "hasDetails": true}