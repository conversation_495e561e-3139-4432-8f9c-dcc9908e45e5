{"id": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "versionId": "50d524c6-1b7b-4b28-a77e-5a644b96a625", "name": "IDC Party", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "", "bindingType": "input", "configOptions": ["partyTypeList", "warningMess", "BeneficiaryDetails", "partyIndex", "customerFullDetails", "selectedBIC", "customerCIF", "accounteeCIF", "caseCIF", "addressBICList", "selectedCIF", "alertMessage", "errorVis", "draweeCIF", "requestType", "<PERSON><PERSON><PERSON><PERSON>", "requestState", "columns", "addParty", "deleteParty", "deletedCIF", "appID"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "var isAmndFinal = false;\r\r\nvar length = 0;\r\r\nthis.setColumnsVis = function () {\r\r\n\tvar vis = \"V\";\r\r\n\tif ((this.context.options.requestType.get(\"value\") == \"IDC Amendment\" || this.context.options.requestType.get(\"value\") == \"IDC Completion\") && !this.context.options.isChecker.get(\"value\")) {\r\r\n        this.context.options.addParty.set(\"value\", false);\r\r\n        this.context.options.deleteParty.set(\"value\", false);\r\r\n        if (this.context.options.requestState.get(\"value\") == \"Initial\"){\r\r\n            vis = \"R\";\r\r\n            isAmndFinal = false;\r\r\n        }else if (this.context.options.requestState.get(\"value\") == \"Final\"){\r\r\n        \tvis = \"R\";\r\r\n            length = this.context.binding.get(\"value\").length();\r\r\n            if (length < 5) {\r\r\n                this.context.options.addParty.set(\"value\", true);\r\r\n                isAmndFinal = true;\r\r\n            }    \r\r\n        }\r\r\n    }else{\r\r\n        vis = \"V\";\r\r\n        this.context.options.addParty.set(\"value\", true);\r\r\n        this.context.options.deleteParty.set(\"value\", true);\r\r\n        isAmndFinal = false;\r\r\n    }\r\r\n\r\r\n    var columns = [];\r\r\n    columns = [\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"}\r\r\n    ];\r\r\n    this.context.options.columns.set(\"value\", []);\r\r\n    this.context.options.columns.set(\"value\", columns);\r\r\n}\r\r\n\r\r\nthis.addAccountee = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n   \tif (isAmndFinal && record == length) {\r\r\n\t    \tvalue.setEnabled(true);\r\r\n\t    \tthis.context.options.addParty.set(\"value\", false);\r\r\n\t    \tthis.context.options.deleteParty.set(\"value\", true);\r\r\n\t    \tthis.ui.get(\"PartyTable/partytype[\"+record+\"]\").setData({name:\"Accountee\",value:\"Accountee\"});\r\r\n    \t}\r\r\n}\r\r\n\r\r\nthis.setMediaVis = function (value) {\r\r\n\tvar r = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(r).get(\"partyType\").get(\"name\") == \"Remitting Bank\") {\r\r\n\t\tvalue.setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tvalue.hide();\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.onDeleteParty = function (value1,value2) {\r\r\n\tvar i = value1.getRecordIndex(value2);\r\r\n\tvar type = value2.partyType.name;\r\r\n\r\r\n\tif((i == 0 || i == 1) || (isAmndFinal && i != length)) {\r\r\n\t\talert(\"You can not delete this row\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n\t\tthis.context.options.addParty.set(\"value\", true);\r\r\n\r\r\n\t\tthis.context.options.deletedCIF.set(\"value\", value2.partyId);\r\r\n\t\tif (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \t\tthis.ui.get(\"update\").click();\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n//------------------------------------------------------------------\r\r\nthis.validateOneType = function (type,value) {\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar n = 0;\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == type){\r\r\n\t\t\tn+=1\r\r\n\t\t}\r\r\n\t}\r\r\n\tif(value.getData().name == type && n>1){\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"Only one  is allowed of same Type\");\r\r\n\t\tthis.context.binding.get(\"value\").remove(record);\r\r\n\t}else{\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"\");\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetPartyItem = function (record) {\r\r\n\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/CIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/partyname[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/country[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/language[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/reference[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address1[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address2[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address3[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n}\r\r\n\r\r\nthis.validateParty = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"country\", \"EG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\")\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(false);\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(false);\r\r\n\t//validateOneType\r\r\n\tthis.validateOneType(\"Accountee\",value);\r\r\n\tthis.validateOneType(\"Drawee\",value);\r\r\n\tthis.validateOneType(\"Case in Need\",value);\r\r\n\tthis.validateOneType(\"Drawer\",value);\r\r\n\tthis.validateOneType(\"Remitting Bank\",value);\r\r\n\t\r\r\n\t//Reset Data\r\r\n\tthis.resetPartyItem(record);\t\r\r\n    \t//Default values\r\r\n\r\r\n\tif (this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\") == \"Remitting Bank\"){\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"SWIFT\");\r\r\n\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address\", \"\");\r\r\n//\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n//\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t}\r\r\n\t\r\r\n\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n\r\r\n}\r\r\n\r\r\nthis.setOwnerCIF = function () {\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Accountee\"){\r\r\n\t\t\tthis.context.options.accounteeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Drawee\"){\r\r\n\t\t\tthis.context.options.draweeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Case in Need\"){\r\r\n\t\t\tthis.context.options.caseCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\nthis.setPartyId = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar type = this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"isRetrived\", false);\r\r\n\t\r\r\n\t//Set Owner CIF\r\r\n//\tthis.setOwnerCIF();\r\r\n\t//Validation\r\r\n\tif(isNaN(Number(value.getData())) || value.getData().length < 8){\r\r\n        value.setValid(false , \"must be 8 digits\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n        value.setValid(true);\r\r\n        if (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n        }\r\r\n\t\treturn true;\t\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.concatOnDelete = function (value){\r\r\n\tvar newString = \"\";\r\r\n\tfor (var i=0; i<this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif (i == value && i > 1) {\r\r\n\t\t\tcontinue;\r\r\n\t\t}\r\r\n\t\tnewString +=this.context.binding.get(\"value\").get(i).get(\"partyType\").name +\",\";\r\r\n\t}\r\r\n\tthis.context.options.concatString.set(\"value\", newString);\r\r\n}\r\r\n\r\r\nthis.drawerNotNBE = function (){\r\r\n    if (this.context.binding.get(\"value\").get(1).get(\"isNbeCustomer\") == false){\r\r\n        this.context.binding.get(\"value\").get(1).set(\"country\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"country\").get(\"englishdescription\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(1).set(\"name\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"name\"));\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.mapPartyData = function (){\r\r\n      record = this.context.options.partyIndex.get(\"value\");\r\r\n\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") == \"Remitting Bank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\"){\r\r\n\r\r\n\t\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setValid(false,\"This CIF is not corresponding to a Bank\");\r\r\n\t}else{\t\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",\"EG\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",this.context.options.customerFullDetails.get(\"value\").get(\"nationality\"));\r\r\n\t\t}\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"language\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\t\t}else{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", this.context.options.customerFullDetails.get(\"value\").get(\"language\"));\r\r\n\t\t}\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"branch\", {});\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"value\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") === \"Remitting Bank\"){\t\r\r\n\t\t\t\r\r\n\t\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\t\tvar input = cif+\"-\"+appID;\r\r\n\t\t\tthis.ui.get(\"GetAddressBIC\").execute(input);\r\r\n\t\t\tif (!!this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\")) {\t\r\r\n\t\t\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\"));\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\",\"NO REF\");\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(true);\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(true);\r\r\n\t\t}else{\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\");\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.clickEx = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tthis.context.options.partyIndex.set(\"value\", record);\r\r\n\tvalue.setData(true);\r\r\n\tif (!!this.context.binding.get(\"value\").get(record).get(\"partyCIF\")) {\r\r\n\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\tvar input = cif +\"-\"+appID;\r\r\n\t    \tthis.ui.get(\"GetPartyDetails1\").execute(input);\r\r\n//\t    \tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t}\t\r\r\n}\r\r\n\r\r\nthis.setBIC = function (value){\r\r\n    this.context.options.selectedBIC.set(\"value\", value.getData());\r\r\n}\r\r\n//---------------------------------------------------------------------------Drop_2--------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}\r\r\n\r\r\nthis.disableRetrieveBtn = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tif (record != 1){\r\r\n//\t\tvalue.setEnabled(false);\r\r\n\t\tvalue.hide();\r\r\n\t}else{\r\r\n\t\tvalue.setEnabled(true);\r\r\n\t}\r\r\n\tif(record == 1 && this.context.binding.get(\"value\").get(record).get(\"isNbeCustomer\") == true){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else if (record != 1){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else {\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}