{"id": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "versionId": "50d524c6-1b7b-4b28-a77e-5a644b96a625", "name": "IDC Party", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "", "bindingType": "input", "configOptions": ["partyTypeList", "warningMess", "BeneficiaryDetails", "partyIndex", "customerFullDetails", "selectedBIC", "customerCIF", "accounteeCIF", "caseCIF", "addressBICList", "selectedCIF", "alertMessage", "errorVis", "draweeCIF", "requestType", "<PERSON><PERSON><PERSON><PERSON>", "requestState", "columns", "addParty", "deleteParty", "deletedCIF", "appID"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "var isAmndFinal = false;\r\r\nvar length = 0;\r\r\nthis.setColumnsVis = function () {\r\r\n\tvar vis = \"V\";\r\r\n\tif ((this.context.options.requestType.get(\"value\") == \"IDC Amendment\" || this.context.options.requestType.get(\"value\") == \"IDC Completion\") && !this.context.options.isChecker.get(\"value\")) {\r\r\n        this.context.options.addParty.set(\"value\", false);\r\r\n        this.context.options.deleteParty.set(\"value\", false);\r\r\n        if (this.context.options.requestState.get(\"value\") == \"Initial\"){\r\r\n            vis = \"R\";\r\r\n            isAmndFinal = false;\r\r\n        }else if (this.context.options.requestState.get(\"value\") == \"Final\"){\r\r\n        \tvis = \"R\";\r\r\n            length = this.context.binding.get(\"value\").length();\r\r\n            if (length < 5) {\r\r\n                this.context.options.addParty.set(\"value\", true);\r\r\n                isAmndFinal = true;\r\r\n            }    \r\r\n        }\r\r\n    }else{\r\r\n        vis = \"V\";\r\r\n        this.context.options.addParty.set(\"value\", true);\r\r\n        this.context.options.deleteParty.set(\"value\", true);\r\r\n        isAmndFinal = false;\r\r\n    }\r\r\n\r\r\n    var columns = [];\r\r\n    columns = [\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"}\r\r\n    ];\r\r\n    this.context.options.columns.set(\"value\", []);\r\r\n    this.context.options.columns.set(\"value\", columns);\r\r\n}\r\r\n\r\r\nthis.addAccountee = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n   \tif (isAmndFinal && record == length) {\r\r\n\t    \tvalue.setEnabled(true);\r\r\n\t    \tthis.context.options.addParty.set(\"value\", false);\r\r\n\t    \tthis.context.options.deleteParty.set(\"value\", true);\r\r\n\t    \tthis.ui.get(\"PartyTable/partytype[\"+record+\"]\").setData({name:\"Accountee\",value:\"Accountee\"});\r\r\n    \t}\r\r\n}\r\r\n\r\r\nthis.setMediaVis = function (value) {\r\r\n\tvar r = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(r).get(\"partyType\").get(\"name\") == \"Remitting Bank\") {\r\r\n\t\tvalue.setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tvalue.hide();\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.onDeleteParty = function (value1,value2) {\r\r\n\tvar i = value1.getRecordIndex(value2);\r\r\n\tvar type = value2.partyType.name;\r\r\n\r\r\n\tif((i == 0 || i == 1) || (isAmndFinal && i != length)) {\r\r\n\t\talert(\"You can not delete this row\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n\t\tthis.context.options.addParty.set(\"value\", true);\r\r\n\r\r\n\t\tthis.context.options.deletedCIF.set(\"value\", value2.partyId);\r\r\n\t\tif (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \t\tthis.ui.get(\"update\").click();\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n//------------------------------------------------------------------\r\r\nthis.validateOneType = function (type,value) {\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar n = 0;\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == type){\r\r\n\t\t\tn+=1\r\r\n\t\t}\r\r\n\t}\r\r\n\tif(value.getData().name == type && n>1){\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"Only one  is allowed of same Type\");\r\r\n\t\tthis.context.binding.get(\"value\").remove(record);\r\r\n\t}else{\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"\");\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetPartyItem = function (record) {\r\r\n\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/CIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/partyname[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/country[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/language[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/reference[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address1[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address2[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address3[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n}\r\r\n\r\r\nthis.validateParty = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"country\", \"EG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\")\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(false);\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(false);\r\r\n\t//validateOneType\r\r\n\tthis.validateOneType(\"Accountee\",value);\r\r\n\tthis.validateOneType(\"Drawee\",value);\r\r\n\tthis.validateOneType(\"Case in Need\",value);\r\r\n\tthis.validateOneType(\"Drawer\",value);\r\r\n\tthis.validateOneType(\"Remitting Bank\",value);\r\r\n\t\r\r\n\t//Reset Data\r\r\n\tthis.resetPartyItem(record);\t\r\r\n    \t//Default values\r\r\n\r\r\n\tif (this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\") == \"Remitting Bank\"){\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"SWIFT\");\r\r\n\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address\", \"\");\r\r\n//\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n//\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t}\r\r\n\t\r\r\n\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n\r\r\n}\r\r\n\r\r\nthis.setOwnerCIF = function () {\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Accountee\"){\r\r\n\t\t\tthis.context.options.accounteeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Drawee\"){\r\r\n\t\t\tthis.context.options.draweeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Case in Need\"){\r\r\n\t\t\tthis.context.options.caseCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\nthis.setPartyId = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar type = this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"isRetrived\", false);\r\r\n\t\r\r\n\t//Set Owner CIF\r\r\n//\tthis.setOwnerCIF();\r\r\n\t//Validation\r\r\n\tif(isNaN(Number(value.getData())) || value.getData().length < 8){\r\r\n        value.setValid(false , \"must be 8 digits\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n        value.setValid(true);\r\r\n        if (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n        }\r\r\n\t\treturn true;\t\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.concatOnDelete = function (value){\r\r\n\tvar newString = \"\";\r\r\n\tfor (var i=0; i<this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif (i == value && i > 1) {\r\r\n\t\t\tcontinue;\r\r\n\t\t}\r\r\n\t\tnewString +=this.context.binding.get(\"value\").get(i).get(\"partyType\").name +\",\";\r\r\n\t}\r\r\n\tthis.context.options.concatString.set(\"value\", newString);\r\r\n}\r\r\n\r\r\nthis.drawerNotNBE = function (){\r\r\n    if (this.context.binding.get(\"value\").get(1).get(\"isNbeCustomer\") == false){\r\r\n        this.context.binding.get(\"value\").get(1).set(\"country\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"country\").get(\"englishdescription\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(1).set(\"name\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"name\"));\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.mapPartyData = function (){\r\r\n      record = this.context.options.partyIndex.get(\"value\");\r\r\n\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") == \"Remitting Bank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\"){\r\r\n\r\r\n\t\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setValid(false,\"This CIF is not corresponding to a Bank\");\r\r\n\t}else{\t\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",\"EG\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",this.context.options.customerFullDetails.get(\"value\").get(\"nationality\"));\r\r\n\t\t}\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"language\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\t\t}else{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", this.context.options.customerFullDetails.get(\"value\").get(\"language\"));\r\r\n\t\t}\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"branch\", {});\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"value\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") === \"Remitting Bank\"){\t\r\r\n\t\t\t\r\r\n\t\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\t\tvar input = cif+\"-\"+appID;\r\r\n\t\t\tthis.ui.get(\"GetAddressBIC\").execute(input);\r\r\n\t\t\tif (!!this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\")) {\t\r\r\n\t\t\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\"));\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\",\"NO REF\");\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(true);\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(true);\r\r\n\t\t}else{\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\");\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.clickEx = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tthis.context.options.partyIndex.set(\"value\", record);\r\r\n\tvalue.setData(true);\r\r\n\tif (!!this.context.binding.get(\"value\").get(record).get(\"partyCIF\")) {\r\r\n\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\tvar input = cif +\"-\"+appID;\r\r\n\t    \tthis.ui.get(\"GetPartyDetails1\").execute(input);\r\r\n//\t    \tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t}\t\r\r\n}\r\r\n\r\r\nthis.setBIC = function (value){\r\r\n    this.context.options.selectedBIC.set(\"value\", value.getData());\r\r\n}\r\r\n//---------------------------------------------------------------------------Drop_2--------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}\r\r\n\r\r\nthis.disableRetrieveBtn = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tif (record != 1){\r\r\n//\t\tvalue.setEnabled(false);\r\r\n\t\tvalue.hide();\r\r\n\t}else{\r\r\n\t\tvalue.setEnabled(true);\r\r\n\t}\r\r\n\tif(record == 1 && this.context.binding.get(\"value\").get(record).get(\"isNbeCustomer\") == true){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else if (record != 1){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else {\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "name": "IDC Party", "lastModified": "1735207941825", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isTemplate": "false", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>3fbac431-4890-42e3-8fc1-6291d6c19b43</ns2:id><ns2:layoutItemId>Collapsible_Panel1</ns2:layoutItemId><ns2:configData><ns2:id>a780789d-00c7-44e3-86fc-94630f607332</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.party</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>770e91dd-c524-4f7a-8d4f-877e5ddc912b</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>dc6a1319-0626-470f-8c69-9b4cd23c5b50</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>4fbcd04d-9fa5-4aa8-8d68-471aafc966da</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>dbe35c55-cdb4-40de-8566-e1e672110cb5</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>81d79244-faed-4734-8b9a-5961415f4ee0</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>var input = \"partyName\";\r\nview.onloadTest(\"partyName\");</ns2:value></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>325b69de-5c95-4a70-85cf-28b68b8929c5</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>e4745733-b0fd-4842-8d26-6fb32cb34622</ns2:id><ns2:layoutItemId>Text1</ns2:layoutItemId><ns2:configData><ns2:id>ded2244e-640a-4e7b-adb6-7b1d6f091e44</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>b399a024-990b-440c-8811-dd82fbb11fc9</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>ID</ns2:value></ns2:configData><ns2:configData><ns2:id>cee0d6fa-9ae7-455f-8fba-bcb7b38cd64d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>05f63d73-7bba-4b9b-8770-42b20c26eea1</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>897fea8e-9247-4c36-8b3d-92a0589a9103</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>alert( me.ui.getAbsoluteName() )</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.input.partyId</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>a6bb7c61-e79d-4dda-8566-6b48cbdcf992</ns2:id><ns2:layoutItemId>Text2</ns2:layoutItemId><ns2:configData><ns2:id>ec9f3b67-9c9d-473d-9ad6-688453d3725c</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>ccd8d3c4-2715-4240-8f99-980a3b32b29c</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Party Name</ns2:value></ns2:configData><ns2:configData><ns2:id>169bdce4-891f-4949-84f4-c7f6f63b0837</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>d22c6c81-bd7e-4332-87b9-d0b1c1cfba96</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.input.partyName</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>8f093819-ab55-4274-8238-f2cce5ad9c19</ns2:id><ns2:layoutItemId>Horizontal_Layout1</ns2:layoutItemId><ns2:configData><ns2:id>73e705a8-d2ce-4089-8f6b-ea56c6d53cbd</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout</ns2:value></ns2:configData><ns2:configData><ns2:id>3cd4dc4a-**************-c0eff665e17a</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>11f185e9-7621-4523-868a-88e2cbe7a156</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>49b22179-8ef2-4139-868f-4263c178d972</ns2:id><ns2:optionName>layoutFlow</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"H\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>4938cafd-4133-4d92-8914-47f6cb6993d0</ns2:id><ns2:optionName>hAlignment</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"L\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>679fb1ce-cc86-4c0e-816a-31540c937582</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>8802c138-7dab-4524-8d17-33511e03767d</ns2:id><ns2:layoutItemId>PartyTable</ns2:layoutItemId><ns2:configData><ns2:id>2c6557e8-9b95-415b-84b2-b67849fc3baa</ns2:id><ns2:optionName>showAddButton</ns2:optionName><ns2:value>tw.options.addParty</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>512b9878-2c23-4b2c-8548-2e4cc82a07b7</ns2:id><ns2:optionName>showDeleteButton</ns2:optionName><ns2:value>tw.options.deleteParty</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>a1e7bfc5-b51d-4215-8720-6cc7559d592f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Parties Table</ns2:value></ns2:configData><ns2:configData><ns2:id>5d302aff-0b0d-4e07-8cf1-7cf5dc63a5f7</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c9adea63-4097-47a9-8ecb-d56fab9063ab</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>2f153700-10c8-4bbe-8d86-8aa48df9b9e1</ns2:id><ns2:optionName>expandableRows</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>34718a41-c834-4610-8caf-c281a60ec9a5</ns2:id><ns2:optionName>allowColumnResize</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>693a3a6f-ea4d-40ec-809d-15e74826b7a5</ns2:id><ns2:optionName>tableStyle</ns2:optionName><ns2:value>H</ns2:value></ns2:configData><ns2:configData><ns2:id>ff39b7e1-4394-4276-8a0f-ec2dfd175029</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>5e95a2f9-b93f-46e8-8649-5ca3679bf2d1</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>db3660a3-c2ae-42fb-8ae4-5ae6608d0991</ns2:id><ns2:optionName>columnSpecs</ns2:optionName><ns2:value>tw.options.columns[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>2861dbfb-0584-44da-8853-728b33a02abc</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"200%\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>cb6c0a70-d4c3-4786-8846-adcfbdacdf22</ns2:id><ns2:optionName>eventON_ADDREC</ns2:optionName><ns2:value>//return view.addAccountee();</ns2:value></ns2:configData><ns2:configData><ns2:id>bd1baf9e-893f-4c48-8cf2-cd51015cf429</ns2:id><ns2:optionName>eventON_DELREC</ns2:optionName><ns2:value>return view.onDeleteParty(me,item);\r\n//if(item.partyType.name ==\"Drawer\" || item.partyType.name ==\"Drawee\"){\r\n//\talert(\"You can not delete this row\");\r\n//\treturn false;\r\n//}</ns2:value></ns2:configData><ns2:configData><ns2:id>dbb1067c-f504-4015-81da-affe9c695d71</ns2:id><ns2:optionName>eventON_ROWSLOADED</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>55e38c1f-25ae-4b39-8b24-be568892e1ec</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.f515b79f-fe61-4bd3-8e26-72f00155d139</ns2:viewUUID><ns2:binding>tw.businessData.Party[]</ns2:binding><ns2:contentBoxContrib><ns2:id>7cc7fd18-3e3a-473d-8e0a-5179501d8120</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>e0168522-1676-4bdb-8133-3a6eccfafa37</ns2:id><ns2:layoutItemId>partytype</ns2:layoutItemId><ns2:configData><ns2:id>92c77f30-1a44-48b4-b85a-a88cde39c380</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>f00315fb-9596-4352-8dc4-944abab5f7f8</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.partytype</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>eafe285b-b6a7-41cd-8796-10577640b56e</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>e8f16404-7953-416b-8498-532ae7a48b34</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ae34a733-0283-4daa-81d8-f5ba30198e86</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>B</ns2:value></ns2:configData><ns2:configData><ns2:id>19de7c7a-7643-4cc0-8c21-692795ac203f</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.resource.ListNames.PartyType</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>bd8f69a8-0e16-4e4c-8d66-669d4ab32373</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"value\",\"optionDisplayProperty\":\"name\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>0946557d-8346-4981-8ad5-6cc1b85ecba3</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"value\",\"optionDisplayProperty\":\"name\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>7eb25bb5-6c08-4a6b-8256-00a10ef89a41</ns2:id><ns2:optionName>itemList</ns2:optionName><ns2:value>tw.options.partyTypeList[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ce31854d-b4d2-4003-8629-051997986baa</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>@validateParty(me);\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>c5e4b595-4a54-450a-8b4e-88a96e3335fe</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>if(me.ui.getIndex() &lt;2){\r\n\tme.setEnabled(false);\r\n}\r\n\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>d6748914-94c2-4dd3-875e-508a3778acbb</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.4febbdb6-818a-4a1f-bbc5-2a880549ba9f</ns2:value></ns2:configData><ns2:configData><ns2:id>f5179e91-c386-4ccf-8b13-96fbfe64f2ed</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value>[{\"name\":\"new\",\"value\":\"new\"},{\"name\":\"new1\",\"value\":\"new1\"},{\"name\":\"new2\",\"value\":\"new2\"}]</ns2:value></ns2:configData><ns2:configData><ns2:id>7d91e1e0-6997-4742-8f8e-4d97faeb7e99</ns2:id><ns2:optionName>eventON_BLUR</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.partyType</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>153e2e06-cd03-410f-807b-2af91921fbd8</ns2:id><ns2:layoutItemId>nbeCustomer</ns2:layoutItemId><ns2:configData><ns2:id>63ff4e55-cb86-4f62-8c4a-b8e8ec7227f7</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.FiledsNames.nbeCustomer</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>e54afbaa-4407-4e9a-807d-1b6268e5e479</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>97ce49de-0dc7-4fe5-88f6-7685afd76ef5</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>28519375-2a40-4ad6-89e8-50578a437f13</ns2:id><ns2:optionName>showValidationMarker</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":false}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>92a47bbd-a249-45d8-8a12-be35e383d8d7</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>bca7de24-3fba-46ee-847b-1faec1753555</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>@disableRetrieveBtn(me);\r\n\r\n\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>a338ee0a-1085-4bc8-838a-6680d95c3674</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>@disableRetrieveBtn(me);\r\n</ns2:value></ns2:configData><ns2:viewUUID>64.fffd1628-baee-44e8-b7ca-5ae48644b0be</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.isNbeCustomer</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>c90046c1-a32c-4a68-8bc5-2c43c26eee55</ns2:id><ns2:layoutItemId>partyCIF</ns2:layoutItemId><ns2:configData><ns2:id>c4c91c68-dff7-4ee7-ad69-971806360b45</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>83abce14-f255-4d67-8a0c-2937cea873ad</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Party CIF</ns2:value></ns2:configData><ns2:configData><ns2:id>a49eb100-44d6-445d-8a7f-83f4c4c0a334</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>56567d11-3dc9-4f5e-8e08-386732de766e</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>ec00c580-05d3-4544-8949-e14b86319e34</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>if(isNaN(Number(me.getData())) || me.getData().length &lt; 8){\r\n\tme.setValid(false , \"must be 8 digits\");\r\n//\tme.setData(\"\");\r\n\treturn false;\r\n}else\r\n{\r\n\tme.setValid(true);\r\n\treturn true;\t\r\n}\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>9999b0e9-852c-44b2-81f5-629aad3290f3</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value>if(potential.length &gt; 8){\r\n\tme.setValid(false , \"must be 8 digits\");\r\n\tme.setData(\"\");\r\n\treturn false;\r\n}else{\r\n\tme.setValid(true);\r\n\treturn true;\r\n}\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>ba367efe-c593-4acf-825d-35db1c551c97</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>@addAccountee(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>41d8d86a-3011-4021-8ff0-7d1c042f8d97</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>46710c75-fe55-49dc-80ce-1c311f6dbc83</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.partyCIF</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>c390859a-d31d-4138-82d4-b46426ae09ae</ns2:id><ns2:layoutItemId>RetrieveBtn</ns2:layoutItemId><ns2:configData><ns2:id>e5a6fdc3-ec08-485e-897b-eba1955a9f40</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Retrieve</ns2:value></ns2:configData><ns2:configData><ns2:id>22d8c8dd-8ebe-4c72-8d85-60c301dc6485</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>1b9f2faf-6902-44e6-8682-0cc83247609b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>210954a5-5ddf-47e8-8d53-b803e3051987</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>a7c64ee4-d36e-4c56-8ada-361ca688901e</ns2:id><ns2:optionName>eventON_CLICK</ns2:optionName><ns2:value>@clickEx(me);\r\n//${GetPartyDetails1}.execute();</ns2:value></ns2:configData><ns2:configData><ns2:id>99ad1cd8-17ce-417c-8fb3-5dc32dcfd2ab</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>4b25f612-155c-44e7-8ac2-f3296d150765</ns2:id><ns2:optionName>shapeStyle</ns2:optionName><ns2:value>R</ns2:value></ns2:configData><ns2:configData><ns2:id>0efd7cb0-5cee-4dc4-8bd5-ea13632890c8</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>//me.setData(false);\r\n//if(me.ui.getIndex() == 1){\r\n//\tme.setEnabled(false);\r\n//}else{\r\n//\tme.setEnabled(true);\r\n//}</ns2:value></ns2:configData><ns2:configData><ns2:id>9eaafb57-e3a1-4703-87de-f0d5905e30c8</ns2:id><ns2:optionName>preventMultipleClicks</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.isRetrived</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>88d4ccfa-4fde-4543-82a5-c80b63a2b9dd</ns2:id><ns2:layoutItemId>CIF</ns2:layoutItemId><ns2:configData><ns2:id>5a87191e-ecc6-4b16-b924-4861fdd7d7e6</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>36a8f52f-4931-4038-84b4-b2965e0b9c47</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Party ID</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>77d5663a-262d-4c1f-86f2-804e357e9ccb</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2a9f5260-b84b-428f-8d6f-46ffd983ff19</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>23711435-d7ab-4188-8246-690d3976e339</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>@setPartyId(me);\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>4eeeea28-8607-43c1-8945-2603cc215106</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value>if(potential.length &gt; 8)\r\n{\r\n\tme.setValid(false , \"max lenght is 8 digits\");\r\n\treturn false;\r\n}\r\nelse\r\n{\r\n\tme.setValid(true);\r\n\treturn true;\r\n}</ns2:value></ns2:configData><ns2:configData><ns2:id>4d935a5d-dc75-4406-86d2-dd0e5b33ff23</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>//@setOwnerCIF();</ns2:value></ns2:configData><ns2:configData><ns2:id>8e7fb271-6d01-493a-8a31-11b28d9135b5</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.partyId</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d1c48724-c648-4a45-8de7-b1d46ca1caae</ns2:id><ns2:layoutItemId>partyname</ns2:layoutItemId><ns2:configData><ns2:id>4af6b9bb-01b6-4da9-953b-60cbc58bbbf3</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>4f45a1bc-6b9e-4c90-86b2-8e24dc05e102</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.partyname</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>b74b48bb-7189-4635-8cdd-af1460290cba</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>56f9cab0-42ba-485c-8184-e12b306ac9e6</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.name</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>9c0e8436-0a67-4a2a-85ea-b3c78e45c9e3</ns2:id><ns2:layoutItemId>country</ns2:layoutItemId><ns2:configData><ns2:id>ca6165c9-6862-4412-9ac6-2f02d2895e0c</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>0112f3f5-34ab-4777-863c-5687af43c1db</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.country</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>e5fe3879-6ea1-4848-8a1c-bae2fe0e46d5</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>49f667c2-92e7-4f5c-89e3-9945ba68b0a2</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>5bff1cbc-e735-43dd-88fc-19684913c14d</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>60361e13-bba2-4fbf-8da9-c68cf55e23a6</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.4febbdb6-818a-4a1f-bbc5-2a880549ba9f</ns2:value></ns2:configData><ns2:configData><ns2:id>974197d1-9604-4725-8c93-023b722395f1</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.resource.ListNames.Country</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>40cdb65c-dd45-4ddf-894e-2d29ffd0fc57</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"value\",\"optionDisplayProperty\":\"name\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>981c98dd-2620-4283-85d4-0fa9e2714520</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"value\",\"optionDisplayProperty\":\"name\"}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.country</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>16ae3cb7-413a-47dc-8c99-a3f684fcf4d2</ns2:id><ns2:layoutItemId>language</ns2:layoutItemId><ns2:configData><ns2:id>102b9238-3e1e-424e-85c0-ee8c8683a38b</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>bb368526-879f-4170-83ab-1f9e9824ffd8</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.language</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ebae7d00-1100-4ec5-837c-ab93524188e0</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>ebf10515-b989-4e00-8c05-8ddc9eb73d36</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.language</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>aa4fbb8e-1eff-409f-8faf-3136edfe37b9</ns2:id><ns2:layoutItemId>reference</ns2:layoutItemId><ns2:configData><ns2:id>873af441-0129-47c5-a3cd-cd4d7b28a7a7</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>878b088b-15eb-40ff-87fb-d4eb243241ac</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.reference</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>4d48ef1d-e32d-4714-88f5-87ade3c67af0</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>3cc70c73-e82d-4612-8b9d-109eee7fdb3b</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.reference</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>460d0fba-**************-984bb38823a0</ns2:id><ns2:layoutItemId>address1</ns2:layoutItemId><ns2:configData><ns2:id>91aa628e-5e98-4678-95e3-40a18aa5ece7</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>7df0e730-8beb-468b-8303-486acdba7d0a</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.address1</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>9d5efb27-903f-4181-80a6-7c7b37ec332e</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>8ff5445c-58da-4807-8b83-2379237d7220</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.address1</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>92c4eb2b-4c1e-43d2-8704-5a93172ec198</ns2:id><ns2:layoutItemId>address2</ns2:layoutItemId><ns2:configData><ns2:id>fcff24f4-5187-447c-aa34-8954b3b7ad5c</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>48adad91-cbda-4328-831d-17009e5f68ad</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.address2</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>8455f632-335c-4c52-8698-12a3f1925048</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>7555b621-d0f7-40b8-863d-07c795f8ba06</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.address2</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>7789c0c1-8a7c-4018-86f9-a0760911988b</ns2:id><ns2:layoutItemId>address3</ns2:layoutItemId><ns2:configData><ns2:id>ee92456e-3142-41f8-95c3-1e05cdc026af</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>d9026e2d-8800-4ab0-8328-f1c43a835203</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.address3</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>3711837d-e1dc-49d8-84bf-7804c5ce75e1</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>ff0076e4-3b99-4918-8d0a-c999b894747a</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.address3</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>a2a5e47f-5f7d-434d-87f3-cdb4d40e7359</ns2:id><ns2:layoutItemId>media</ns2:layoutItemId><ns2:configData><ns2:id>1e66e1fd-7d8e-4e30-aedd-fe0bfdeaf191</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>4a5c1573-a457-4c74-8bc7-f66b597b1e4d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.media</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ef8f92da-bb0d-401b-8e95-dd54d4d74922</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>f11d80af-d487-4de1-819a-7728f03ff32a</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ff8ad4ee-710b-43a0-8bf2-028a3081a514</ns2:id><ns2:optionName>expression</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>bfe63508-9518-4c8c-8bda-704ea9637721</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>@setMediaVis(me);</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.media</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>7a69b036-e2a2-4711-85bd-cf1aef36de30</ns2:id><ns2:layoutItemId>address</ns2:layoutItemId><ns2:configData><ns2:id>43edfb72-3468-4931-8c43-4697be11f96c</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.resource.CommonLacale.address</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>2f82835b-d033-4083-8ee7-8ce25d4b0a07</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>8b9a1eb0-3fdb-4bd2-8e50-e38c4f7ec672</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>3ea99a68-f819-4072-8b1c-32bb081a8ed6</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>B</ns2:value></ns2:configData><ns2:configData><ns2:id>aef02cd9-6d3a-411f-87d4-17681c97480c</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</ns2:value></ns2:configData><ns2:configData><ns2:id>b85e7dbe-31bb-4932-822c-f1d75a19e613</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>@setBIC(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>904f9e06-db7d-4804-8bba-f521375da86f</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"\",\"optionDisplayProperty\":\"\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>373481b7-1eaf-4e3d-8c08-23cbcde91a3e</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"\",\"optionDisplayProperty\":\"\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>3cf95d0f-f366-40d1-8815-71ef0e87138b</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.businessData.Party.currentItem.partyCIF</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>f212838d-9b0a-457a-8350-a0b4c6e264dd</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:configData><ns2:id>9799d472-9fb7-4123-8927-4ce26bfb7f1a</ns2:id><ns2:optionName>itemList</ns2:optionName><ns2:value>tw.options.addressBICList[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>8e2af431-985d-4580-8795-b383785b186f</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>@setMediaVis(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>24d9f0fb-8eb5-4abd-8a58-3ac022ccbccc</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value>tw.options.addressBICList[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.Party.currentItem.address</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>131cc910-c2ed-4e36-8215-2cc7c1064f63</ns2:id><ns2:layoutItemId>update</ns2:layoutItemId><ns2:configData><ns2:id>4577426c-31eb-436b-8ffd-92764971586e</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>update</ns2:value></ns2:configData><ns2:configData><ns2:id>0c97ae14-2ee6-42d6-8ce1-b6fdd2b0d941</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>fdc744e7-325d-423a-8c7b-be2732c885b9</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>7040fd64-84c0-4b38-8d8b-5bf2f28566f7</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"HIDDEN\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>f9440858-1b77-4e4c-8070-81248f283fc8</ns2:id><ns2:layoutItemId>GetPartyDetails1</ns2:layoutItemId><ns2:configData><ns2:id>547ced56-317e-43d4-8120-c453aef0edbc</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Get Party Details</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>df46e941-8d67-41f9-877b-17ea9816f764</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>15e0c455-4b66-418d-8ac5-1bc8cb240a75</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>cc69f1ac-bff8-43b4-88c6-e6f68d3de474</ns2:id><ns2:optionName>attachedService</ns2:optionName><ns2:value>1.c3124e3b-0cdd-4710-a44b-6071a04e76dc</ns2:value></ns2:configData><ns2:configData><ns2:id>3b991536-7c8b-482e-84ef-9187fda0440b</ns2:id><ns2:optionName>eventON_SVCRESULT</ns2:optionName><ns2:value>view.mapPartyData();</ns2:value></ns2:configData><ns2:configData><ns2:id>5779ce9e-afde-4738-8148-567315e86af6</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value></ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>6442679f-5dc6-449c-87c7-ef1c6912b906</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value>view.AjaxErrorHandling(error.errorText);</ns2:value></ns2:configData><ns2:viewUUID>64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9</ns2:viewUUID><ns2:binding>tw.options.customerFullDetails</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>0ddfe2fe-e6a5-4145-8e6e-260ad2bafdb4</ns2:id><ns2:layoutItemId>GetAddressBIC</ns2:layoutItemId><ns2:configData><ns2:id>5f29ccc7-fb4a-42ed-8908-b95ab2986081</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Get Address BIC</ns2:value></ns2:configData><ns2:configData><ns2:id>e99180a4-bd54-4dcc-8470-f5016fef2a0d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>4e68a5eb-8ba3-4754-8e89-1ff6a6b81827</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>58a11cfe-5597-4790-80ec-449b467b68a6</ns2:id><ns2:optionName>attachedService</ns2:optionName><ns2:value>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</ns2:value></ns2:configData><ns2:configData><ns2:id>5d6b394c-c514-4c5c-8219-36944bd336b9</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value>view.AjaxErrorHandling(error.errorText);</ns2:value></ns2:configData><ns2:viewUUID>64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9</ns2:viewUUID><ns2:binding>tw.options.addressBICList[]</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "true", "loadJsFunction": "\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n", "unloadJsFunction": "", "viewJsFunction": "", "changeJsFunction": {"isNull": "true"}, "collaborationJsFunction": "", "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "a58354e2-8a4c-4344-aab7-98d6b0518e60", "versionId": "50d524c6-1b7b-4b28-a77e-5a644b96a625", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "bindingType": {"name": "input", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewBindingTypeId": "65.4bb5bd5f-5755-43dd-9012-f8e6de008e2e", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "classId": "/12.cdbb2c22-8516-45ab-925a-2933e6e1bed5", "seq": "0", "description": {"isNull": "true"}, "guid": "fa9336a8-be82-470d-bc8b-3aab37780a71", "versionId": "0e15342a-8f35-493c-9d70-e2bf4c91e4fc"}, "configOption": [{"name": "partyTypeList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.ac4e91e2-c2c2-497e-a7d6-6c533d93b0de", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "0", "description": "", "groupName": "", "guid": "9d89e10a-4d5c-4987-b752-55f27ec01748", "versionId": "c71ee089-88f0-4462-ae70-a0775b1b7857"}, {"name": "warningMess", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.eef77a75-c136-4bfb-a49a-a67b8200b8a3", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "1", "description": "", "groupName": "", "guid": "417b8d79-a049-4ecb-9091-cf8a0c7ce8b9", "versionId": "2e29a97d-7680-4a46-ae47-bc605dd9cc98"}, {"name": "BeneficiaryDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.a8cced89-d62d-498a-8ac4-26ac30dddf0a", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "/12.a025468c-38bc-4809-b337-57da9e95dacb", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "2", "description": "", "groupName": "", "guid": "1cf59857-8609-42d0-a8c7-db32a4d373f2", "versionId": "ca66e698-a892-4e29-9ad3-5f8a165cb6d2"}, {"name": "partyIndex", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.91cb6bdf-5b82-43b2-b50f-2a3f69c78c3e", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "3", "description": "", "groupName": "", "guid": "08288aef-4b9c-4116-a0ad-37df9df343a1", "versionId": "8d91ad62-2c83-411c-9b11-0cd7ab2e632d"}, {"name": "customerFullDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.d6c10bf7-7ad4-4ed3-9a07-900a241d82c2", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "4", "description": "", "groupName": "", "guid": "b5fe91f4-2827-49e2-8221-db09920cf038", "versionId": "75a230ce-230b-447d-9e2f-47f0b514a84a"}, {"name": "selectedBIC", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.24b24d84-ef24-4616-ab87-eb4b94eb95a0", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "5", "description": "", "groupName": "", "guid": "25d8dd28-65b5-4d01-9b2f-622c72d5d339", "versionId": "97545c3c-f9f2-4689-ba4b-eba0e86d890b"}, {"name": "customerCIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.3289fde9-c029-49bc-96b3-5d3c396f4e06", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "6", "description": "", "groupName": "", "guid": "c371a561-7efc-4fa3-9a62-aa02df425cc2", "versionId": "6b7fbb82-357f-4e00-8cd0-4458cfde1e39"}, {"name": "accounteeCIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.39aa05e8-c342-4fea-b1c0-bc7cfe7b90b5", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "7", "description": "", "groupName": "", "guid": "84ee59bd-8be4-4136-a630-889d358a2b84", "versionId": "d8a5b711-098f-4308-8b0a-9d6a750abc99"}, {"name": "caseCIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.********-8689-419a-a59e-29d0f145c613", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "8", "description": "", "groupName": "", "guid": "b6681d92-2ce6-4408-911b-113fa7881c7d", "versionId": "c9abe58c-7b03-4241-9161-150652afa979"}, {"name": "addressBICList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.a45145ad-beb7-4836-b0d9-2e2f17164b64", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "9", "description": "", "groupName": "", "guid": "6a19931f-a40d-4efa-8933-038e5d302da0", "versionId": "2ccd92c9-e7e3-47f0-a90f-47cf3ef37c66"}, {"name": "selectedCIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.f1e2d8dd-f99a-40a4-9e4a-eb4f74abe5c0", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "10", "description": "", "groupName": "", "guid": "d90c1074-9520-4312-a1db-d75bd63772d9", "versionId": "24eb3f5f-2521-4068-8eed-739ea8def657"}, {"name": "alertMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.0be88f91-1b79-4b76-8fcb-c53066b635b2", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "11", "description": "", "groupName": "", "guid": "c78e67e7-b94f-4146-9ac2-094f2431517b", "versionId": "40eb45ad-58f5-4bf3-b348-b4a8872e3b2a"}, {"name": "errorVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.06b74dd0-4d12-4e5e-b166-61ba535cbf29", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "12", "description": "", "groupName": "", "guid": "515fd8d0-ebfe-4c08-a9b4-3613c17dc4af", "versionId": "132c5bf7-1d5d-4bc3-9528-5e9be50dcb60"}, {"name": "draweeCIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.237c11da-d07c-42f8-a24f-6f1fc76b22e8", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "13", "description": "", "groupName": "", "guid": "f9dd791d-2773-4e5b-aee7-5007b064dd9d", "versionId": "0ef547de-0042-4ca0-b138-51ac77148d62"}, {"name": "requestType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.9b0d1a2f-9955-41a3-b3fd-b7d01ed51914", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "14", "description": "", "groupName": "", "guid": "6373316a-7443-4efd-866e-de9eb9728ab1", "versionId": "9abdd14d-0be1-4e71-a1c5-46003d317ff3"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.2aa7f3d1-a239-40b3-97c4-338a70dc05b1", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "15", "description": "", "groupName": "", "guid": "a35fac42-c07e-40e1-aeaf-cf39d280b3a8", "versionId": "667411a2-51f5-47a5-a96d-55a860ca7dc2"}, {"name": "requestState", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.e14610e2-aa2f-4f74-a980-f59b7e872a84", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "16", "description": "", "groupName": "", "guid": "e68b0b41-9b93-48a4-a61d-0dfd2e68e1fc", "versionId": "70b2a4cd-c606-4d8d-ab93-d181dafd2df2"}, {"name": "columns", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.07e19288-958c-498f-98b4-4b31a396f446", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.55625d09-2e7f-4b8b-82aa-e01b1ba5bb57", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "17", "description": "", "groupName": "", "guid": "84fb8430-845b-45b9-9954-89cc8ba1755c", "versionId": "f9d787f8-7f81-461f-a031-7079e27ac6ad"}, {"name": "addParty", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.7f0b7e69-e793-49ae-a607-22bee5db8e7e", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "18", "description": "", "groupName": "", "guid": "f7838e6d-07ca-46ea-b274-3411140c1add", "versionId": "bf3a9ecd-1e12-462e-895b-4e7565e71f00"}, {"name": "deleteParty", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.165fe315-4bf9-4097-9a5b-3a7e56f4feb8", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "19", "description": "", "groupName": "", "guid": "4621fc7d-511d-4fc9-8daa-3e2e8a6ed214", "versionId": "8cbc485a-9b6b-4459-ba6e-cd14201b285d"}, {"name": "deletedCIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.915cc37e-d8cf-424d-a00d-2ab2a7c878bc", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "20", "description": "", "groupName": "", "guid": "5210530c-a377-4fc9-8cbb-e9710ecac46f", "versionId": "3d5bd137-9c68-425c-abb5-ed9ce8467f60"}, {"name": "appID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.fb3296af-e7ce-463d-9c07-062acbeae704", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "21", "description": "", "groupName": "", "guid": "225dcf11-7af3-428b-8da5-9906910dc3a0", "versionId": "82133490-5bb4-4d77-a748-4b1acd631e6e"}], "inlineScript": {"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.5fce2ce4-ca63-44fb-b112-4645c0c7d121", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "scriptType": "JS", "scriptBlock": "\r\r\n\r\r\n\r\r\n\r\r\n\r\r\nvar isAmndFinal = false;\r\r\nvar length = 0;\r\r\nthis.setColumnsVis = function () {\r\r\n\tvar vis = \"V\";\r\r\n\tif ((this.context.options.requestType.get(\"value\") == \"IDC Amendment\" || this.context.options.requestType.get(\"value\") == \"IDC Completion\") && !this.context.options.isChecker.get(\"value\")) {\r\r\n        this.context.options.addParty.set(\"value\", false);\r\r\n        this.context.options.deleteParty.set(\"value\", false);\r\r\n        if (this.context.options.requestState.get(\"value\") == \"Initial\"){\r\r\n            vis = \"R\";\r\r\n            isAmndFinal = false;\r\r\n        }else if (this.context.options.requestState.get(\"value\") == \"Final\"){\r\r\n        \tvis = \"R\";\r\r\n            length = this.context.binding.get(\"value\").length();\r\r\n            if (length < 5) {\r\r\n                this.context.options.addParty.set(\"value\", true);\r\r\n                isAmndFinal = true;\r\r\n            }    \r\r\n        }\r\r\n    }else{\r\r\n        vis = \"V\";\r\r\n        this.context.options.addParty.set(\"value\", true);\r\r\n        this.context.options.deleteParty.set(\"value\", true);\r\r\n        isAmndFinal = false;\r\r\n    }\r\r\n\r\r\n    var columns = [];\r\r\n    columns = [\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"}\r\r\n    ];\r\r\n    this.context.options.columns.set(\"value\", []);\r\r\n    this.context.options.columns.set(\"value\", columns);\r\r\n}\r\r\n\r\r\nthis.addAccountee = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n   \tif (isAmndFinal && record == length) {\r\r\n\t    \tvalue.setEnabled(true);\r\r\n\t    \tthis.context.options.addParty.set(\"value\", false);\r\r\n\t    \tthis.context.options.deleteParty.set(\"value\", true);\r\r\n\t    \tthis.ui.get(\"PartyTable/partytype[\"+record+\"]\").setData({name:\"Accountee\",value:\"Accountee\"});\r\r\n    \t}\r\r\n}\r\r\n\r\r\nthis.setMediaVis = function (value) {\r\r\n\tvar r = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(r).get(\"partyType\").get(\"name\") == \"Remitting Bank\") {\r\r\n\t\tvalue.setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tvalue.hide();\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.onDeleteParty = function (value1,value2) {\r\r\n\tvar i = value1.getRecordIndex(value2);\r\r\n\tvar type = value2.partyType.name;\r\r\n\r\r\n\tif((i == 0 || i == 1) || (isAmndFinal && i != length)) {\r\r\n\t\talert(\"You can not delete this row\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n\t\tthis.context.options.addParty.set(\"value\", true);\r\r\n\r\r\n\t\tthis.context.options.deletedCIF.set(\"value\", value2.partyId);\r\r\n\t\tif (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \t\tthis.ui.get(\"update\").click();\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n//------------------------------------------------------------------\r\r\nthis.validateOneType = function (type,value) {\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar n = 0;\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == type){\r\r\n\t\t\tn+=1\r\r\n\t\t}\r\r\n\t}\r\r\n\tif(value.getData().name == type && n>1){\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"Only one  is allowed of same Type\");\r\r\n\t\tthis.context.binding.get(\"value\").remove(record);\r\r\n\t}else{\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"\");\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetPartyItem = function (record) {\r\r\n\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/CIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/partyname[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/country[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/language[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/reference[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address1[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address2[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address3[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n}\r\r\n\r\r\nthis.validateParty = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"country\", \"EG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\")\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(false);\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(false);\r\r\n\t//validateOneType\r\r\n\tthis.validateOneType(\"Accountee\",value);\r\r\n\tthis.validateOneType(\"Drawee\",value);\r\r\n\tthis.validateOneType(\"Case in Need\",value);\r\r\n\tthis.validateOneType(\"Drawer\",value);\r\r\n\tthis.validateOneType(\"Remitting Bank\",value);\r\r\n\t\r\r\n\t//Reset Data\r\r\n\tthis.resetPartyItem(record);\t\r\r\n    \t//Default values\r\r\n\r\r\n\tif (this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\") == \"Remitting Bank\"){\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"SWIFT\");\r\r\n\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address\", \"\");\r\r\n//\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n//\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t}\r\r\n\t\r\r\n\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n\r\r\n}\r\r\n\r\r\nthis.setOwnerCIF = function () {\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Accountee\"){\r\r\n\t\t\tthis.context.options.accounteeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Drawee\"){\r\r\n\t\t\tthis.context.options.draweeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Case in Need\"){\r\r\n\t\t\tthis.context.options.caseCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\nthis.setPartyId = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar type = this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"isRetrived\", false);\r\r\n\t\r\r\n\t//Set Owner CIF\r\r\n//\tthis.setOwnerCIF();\r\r\n\t//Validation\r\r\n\tif(isNaN(Number(value.getData())) || value.getData().length < 8){\r\r\n        value.setValid(false , \"must be 8 digits\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n        value.setValid(true);\r\r\n        if (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n        }\r\r\n\t\treturn true;\t\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.concatOnDelete = function (value){\r\r\n\tvar newString = \"\";\r\r\n\tfor (var i=0; i<this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif (i == value && i > 1) {\r\r\n\t\t\tcontinue;\r\r\n\t\t}\r\r\n\t\tnewString +=this.context.binding.get(\"value\").get(i).get(\"partyType\").name +\",\";\r\r\n\t}\r\r\n\tthis.context.options.concatString.set(\"value\", newString);\r\r\n}\r\r\n\r\r\nthis.drawerNotNBE = function (){\r\r\n    if (this.context.binding.get(\"value\").get(1).get(\"isNbeCustomer\") == false){\r\r\n        this.context.binding.get(\"value\").get(1).set(\"country\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"country\").get(\"englishdescription\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(1).set(\"name\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"name\"));\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.mapPartyData = function (){\r\r\n      record = this.context.options.partyIndex.get(\"value\");\r\r\n\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") == \"Remitting Bank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\"){\r\r\n\r\r\n\t\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setValid(false,\"This CIF is not corresponding to a Bank\");\r\r\n\t}else{\t\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",\"EG\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",this.context.options.customerFullDetails.get(\"value\").get(\"nationality\"));\r\r\n\t\t}\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"language\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\t\t}else{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", this.context.options.customerFullDetails.get(\"value\").get(\"language\"));\r\r\n\t\t}\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"branch\", {});\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"value\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") === \"Remitting Bank\"){\t\r\r\n\t\t\t\r\r\n\t\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\t\tvar input = cif+\"-\"+appID;\r\r\n\t\t\tthis.ui.get(\"GetAddressBIC\").execute(input);\r\r\n\t\t\tif (!!this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\")) {\t\r\r\n\t\t\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\"));\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\",\"NO REF\");\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(true);\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(true);\r\r\n\t\t}else{\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\");\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.clickEx = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tthis.context.options.partyIndex.set(\"value\", record);\r\r\n\tvalue.setData(true);\r\r\n\tif (!!this.context.binding.get(\"value\").get(record).get(\"partyCIF\")) {\r\r\n\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\tvar input = cif +\"-\"+appID;\r\r\n\t    \tthis.ui.get(\"GetPartyDetails1\").execute(input);\r\r\n//\t    \tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t}\t\r\r\n}\r\r\n\r\r\nthis.setBIC = function (value){\r\r\n    this.context.options.selectedBIC.set(\"value\", value.getData());\r\r\n}\r\r\n//---------------------------------------------------------------------------Drop_2--------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}\r\r\n\r\r\nthis.disableRetrieveBtn = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tif (record != 1){\r\r\n//\t\tvalue.setEnabled(false);\r\r\n\t\tvalue.hide();\r\r\n\t}else{\r\r\n\t\tvalue.setEnabled(true);\r\r\n\t}\r\r\n\tif(record == 1 && this.context.binding.get(\"value\").get(record).get(\"isNbeCustomer\") == true){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else if (record != 1){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else {\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}\r\r\n", "seq": "0", "description": "", "guid": "5990bcc7-0aab-4db5-9ef0-cf0798f6fde1", "versionId": "9b8b2597-2459-4d00-b9bd-135da5d53957"}, "localization": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewLocalResId": "69.57aa66aa-753f-4cd3-9c72-b2be7b1e7c1d", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "resourceBundleGroupId": "341e5684-f12d-4cf6-aac8-d2dc77d648ab/50.babe04b6-56bb-4ff0-9723-638bba7612f2", "seq": "0", "guid": "e80c9779-918f-4f8a-a7a7-d408ed4db7f5", "versionId": "28e7802d-26d5-4c41-99e2-832ef9ea1bf4"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewLocalResId": "69.17105772-5ce3-471d-bce7-2e382660c462", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "resourceBundleGroupId": "341e5684-f12d-4cf6-aac8-d2dc77d648ab/50.49a69c8c-61e1-426f-907a-e8ed80310ea5", "seq": "1", "guid": "7387fa18-e3fb-4d7c-bf1c-b12f861e226c", "versionId": "8a96d00d-2b85-427c-a60a-a4ee3836b1fb"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewLocalResId": "69.af07db09-d19d-444a-815f-785756962f20", "coachViewId": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "resourceBundleGroupId": "/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c", "seq": "2", "guid": "f4e1f839-4722-473b-a5f3-cf427d102451", "versionId": "8d01affe-b0ed-4a4f-b9dc-2ca3f6c73b8b"}]}}}, "hasDetails": true}