<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.a0722085-9246-4af1-b86e-8a21be7e94cc" name="DocumentGenerationResponse">
        <lastModified>1693480732486</lastModified>
        <lastModifiedBy>abdelrahman.saleh</lastModifiedBy>
        <classId>12.a0722085-9246-4af1-b86e-8a21be7e94cc</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>true</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc</externalId>
        <dependencySummary>&lt;dependencySummary id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173a"&gt;
  &lt;artifactReference id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1739"&gt;
    &lt;refId&gt;/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4&lt;/refId&gt;
    &lt;refType&gt;1&lt;/refType&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1738"&gt;
      &lt;name&gt;externalId&lt;/name&gt;
      &lt;value&gt;http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1737"&gt;
      &lt;name&gt;mimeType&lt;/name&gt;
      &lt;value&gt;xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
  &lt;/artifactReference&gt;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","complexType":[{"annotation":{"documentation":[{"content":["Document Generation Response"]}],"appinfo":[{"shared":[false],"advancedProperties":[{"namespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","typeName":"DocumentGenerationResponse"}],"shadow":[true]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["status"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":1,"typeName":"boolean","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":1}]}]},"name":"status","type":"{http:\/\/lombardi.ibm.com\/schema\/}Boolean","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["templatesResponse"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","_minOccurs":0,"typeName":"ResponseTemplatePojo","_nillable":false,"nodeType":1,"_maxOccurs":2147483647,"order":2}]}]},"name":"templatesResponse","maxOccurs":"unbounded","type":"{http:\/\/NBEODCR}BrokenReference","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8"}}]},"name":"DocumentGenerationResponse"}],"id":"_12.a0722085-9246-4af1-b86e-8a21be7e94cc"}</jsonData>
        <description>Document Generation Response</description>
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1766</guid>
        <versionId>7c7bae4a-dc4f-49bb-950c-4ad1a8315b92</versionId>
        <definition>
            <property>
                <name>status</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>boolean</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>1</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>1</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>templatesResponse</name>
                <description isNull="true" />
                <classRef>/12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8</classRef>
                <arrayProperty>true</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>ResponseTemplatePojo</typeName>
                    <typeNamespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>unbounded</maxOccurs>
                    <nillable>false</nillable>
                    <order>2</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="DocumentGenerationResponse">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name>DocumentGenerationResponse</name>
                <namespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</namespace>
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

