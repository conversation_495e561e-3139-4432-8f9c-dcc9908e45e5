<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.40a7d34d-1192-436a-ba5a-0360265d3261" name="Calculate Change Amount">
        <lastModified>1700212584375</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.995d3178-8aaa-4d17-a459-bd51f4a7845b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>41d5efce-5dac-438d-b0f6-88fbc70dac96</guid>
        <versionId>c9c58a63-fe98-45f3-a2d6-d2f0f3f3f3ff</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3e01" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.4176c021-edfa-4547-b2d2-e03d99dea481"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"5ec02d17-bf63-43c5-8a1b-bca0c154001c"},{"incoming":["2c78841a-f5f8-4f01-aa10-d8c83f35f9a7","48615fdb-611b-4430-a7ba-7d0518062657"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":540,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2e"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"ff4cc3c5-467b-423f-825f-bb17139da505"},{"targetRef":"995d3178-8aaa-4d17-a459-bd51f4a7845b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Map Input","declaredType":"sequenceFlow","id":"2027.4176c021-edfa-4547-b2d2-e03d99dea481","sourceRef":"5ec02d17-bf63-43c5-8a1b-bca0c154001c"},{"startQuantity":1,"outgoing":["2c78841a-f5f8-4f01-aa10-d8c83f35f9a7"],"incoming":["850d4a4a-9e91-4d4a-9295-f4ab21b506a2"],"extensionElements":{"postAssignmentScript":["tw.local.results = Number(tw.local.changeAmount);\r\n\r\n"],"nodeVisualInfo":[{"width":95,"x":277,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Calculate Change Amount","dataInputAssociation":[{"targetRef":"2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.defaultPercentage"]}}]},{"targetRef":"2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.aa2d49db-103b-4ad9-8e57-95487013a0fc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.234755dd-aa6d-47c3-8a5d-f6860d21d2da","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"INWARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.8420386c-2f0f-431a-8b39-de0080607008","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.d08ff4c0-0f8d-47b1-8bd2-ff091c64a490","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.8c593a04-d1d3-40d2-8288-991e270ebc48","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.instanceID"]}}]},{"targetRef":"2055.94ff52b2-0d25-41a6-810b-8c71f86202a8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.collateralAmnt"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"50d54e1d-a8a8-475a-99d4-c20f87e3a34d","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.9cf99306-b011-40e1-852b-7c1366b243e7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.03bb44c7-2ddc-40f7-8cf9-25e5eacb2c37"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.ec819699-1ccd-4c1b-8a08-e574e43b9765"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.changeAmount"]}}],"sourceRef":["2055.45c20ab6-7f17-473e-891c-c8352d52a7c0"]}],"calledElement":"1.c63132b4-26e8-4922-945e-0c52767485a5"},{"targetRef":"ff4cc3c5-467b-423f-825f-bb17139da505","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2c78841a-f5f8-4f01-aa10-d8c83f35f9a7","sourceRef":"50d54e1d-a8a8-475a-99d4-c20f87e3a34d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"collateralAmnt","isCollection":false,"declaredType":"dataObject","id":"2056.10340b98-d283-404e-a913-efe2773041b8"},{"startQuantity":1,"outgoing":["850d4a4a-9e91-4d4a-9295-f4ab21b506a2"],"incoming":["2027.4176c021-edfa-4547-b2d2-e03d99dea481"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":109,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"995d3178-8aaa-4d17-a459-bd51f4a7845b","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tvar inputList = new tw.object.listOf.String();\r\n\tinputList = tw.local.data.split(\",\");\r\n\ttw.local.collateralAmnt = inputList[0];\r\n\ttw.local.defaultPercentage = inputList[1];\r\n}catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\r\n"]}},{"targetRef":"50d54e1d-a8a8-475a-99d4-c20f87e3a34d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Calculate Change Amount","declaredType":"sequenceFlow","id":"850d4a4a-9e91-4d4a-9295-f4ab21b506a2","sourceRef":"995d3178-8aaa-4d17-a459-bd51f4a7845b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"defaultPercentage","isCollection":false,"declaredType":"dataObject","id":"2056.2e4ca3f4-e22d-47c9-8dec-db6791b81b16"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"changeAmount","isCollection":false,"declaredType":"dataObject","id":"2056.85a8974d-9569-4c2d-99a6-0b763c6fb2f4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.56e17071-0bc3-479f-b2ce-308ee69ec5d7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.20bb7903-c8bf-4610-a66c-9b045e6a3b5d"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.46bbf8af-a56e-4321-800d-5ea722960beb"},{"startQuantity":1,"outgoing":["48615fdb-611b-4430-a7ba-7d0518062657"],"incoming":["eb8bda8b-8fc0-4462-aa55-a7242278ef46","a98dc870-9a94-41a5-abc1-e1db58f32c7f"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":278,"y":188,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"650d424d-c0f4-455f-845f-64b7b79ca1fa","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.error != null &amp;&amp; !!tw.local.error.errorText) {\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\n}else if ( tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n\ttw.local.error = new tw.object.AjaxError();\r\n}else{\r\n\ttw.local.error = new tw.object.AjaxError();\r\n}\r\n\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG;\r\nlog.error(\"IDC PROCESS with ID \"+tw.local.instanceID+\" An Error Occured in Service Name : \"+tw.system.serviceFlow.name+\" with Error Message : \"+tw.local.errorMSG);"]}},{"parallelMultiple":false,"outgoing":["eb8bda8b-8fc0-4462-aa55-a7242278ef46"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"13d8a720-e47c-4905-a99c-040686103add"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"3915226b-8e7f-4cfc-a14d-7d2ff19ae826","otherAttributes":{"eventImplId":"45cde78b-a982-4aab-8b06-384eb5c8edae"}}],"attachedToRef":"995d3178-8aaa-4d17-a459-bd51f4a7845b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":144,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"a0788a44-b0be-4bbd-a627-2d41fff17bf7","outputSet":{}},{"parallelMultiple":false,"outgoing":["a98dc870-9a94-41a5-abc1-e1db58f32c7f"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"93a1aa4e-c268-4f59-b927-872c453c7b26"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"84e6b13e-c0b3-4fbb-bc8c-926d5494c506","otherAttributes":{"eventImplId":"9738eff8-4e3f-47c7-83b8-36f5842986d3"}}],"attachedToRef":"50d54e1d-a8a8-475a-99d4-c20f87e3a34d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":312,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"e2611070-8fae-4ca6-9469-758b5ab52bd4","outputSet":{}},{"targetRef":"650d424d-c0f4-455f-845f-64b7b79ca1fa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"eb8bda8b-8fc0-4462-aa55-a7242278ef46","sourceRef":"a0788a44-b0be-4bbd-a627-2d41fff17bf7"},{"targetRef":"650d424d-c0f4-455f-845f-64b7b79ca1fa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"a98dc870-9a94-41a5-abc1-e1db58f32c7f","sourceRef":"e2611070-8fae-4ca6-9469-758b5ab52bd4"},{"targetRef":"ff4cc3c5-467b-423f-825f-bb17139da505","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"48615fdb-611b-4430-a7ba-7d0518062657","sourceRef":"650d424d-c0f4-455f-845f-64b7b79ca1fa"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"changePercentage","isCollection":false,"declaredType":"dataObject","id":"2056.32f40e12-c4ba-45d2-81b1-9e8cb07c5bb2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"defaultAmount","isCollection":false,"declaredType":"dataObject","id":"2056.1a23012d-7492-4b4d-adba-eb8f5bf8adcf"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.4cb0264d-57ab-47e5-b199-d8ae5d957665"}],"laneSet":[{"id":"1ba94ed9-dc26-42ff-993d-e1bed3c7391e","lane":[{"flowNodeRef":["5ec02d17-bf63-43c5-8a1b-bca0c154001c","ff4cc3c5-467b-423f-825f-bb17139da505","50d54e1d-a8a8-475a-99d4-c20f87e3a34d","995d3178-8aaa-4d17-a459-bd51f4a7845b","650d424d-c0f4-455f-845f-64b7b79ca1fa","a0788a44-b0be-4bbd-a627-2d41fff17bf7","e2611070-8fae-4ca6-9469-758b5ab52bd4"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5875e161-488a-47f8-945f-d29d769e55e0","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Calculate Change Amount","declaredType":"process","id":"1.40a7d34d-1192-436a-ba5a-0360265d3261","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.ff9a3dd1-fc0d-4504-afbc-dcc535ae5090"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.7859d155-5d55-4fa7-bd13-fc76eb5deb84"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"500,22.4\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.8edfccfc-7a82-4cbf-a36c-40465550425c"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8edfccfc-7a82-4cbf-a36c-40465550425c</processParameterId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"500,22.4"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a07754e5-ff89-4420-8c24-85d8d410e501</guid>
            <versionId>3a4d7794-ccbe-43e2-be1e-fc0d911ab849</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ff9a3dd1-fc0d-4504-afbc-dcc535ae5090</processParameterId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fe67d83a-c7c4-4eef-9ec3-c4c2c046a126</guid>
            <versionId>b2b8b6d0-3bcb-4e27-a897-14d099888c6f</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7859d155-5d55-4fa7-bd13-fc76eb5deb84</processParameterId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4853238a-4455-470b-94a6-f57d1a3e29a3</guid>
            <versionId>*************-4fe6-b5fd-65ecd85baf52</versionId>
        </processParameter>
        <processVariable name="collateralAmnt">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.10340b98-d283-404e-a913-efe2773041b8</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>381f9406-a5b4-4059-921b-863fc52d74e3</guid>
            <versionId>ba4efcd9-e457-42e5-a263-4a88e927f423</versionId>
        </processVariable>
        <processVariable name="defaultPercentage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2e4ca3f4-e22d-47c9-8dec-db6791b81b16</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f792a719-26af-4a74-8669-abc37411185a</guid>
            <versionId>e8dcd611-b3b6-4812-9c27-90c22f8435e3</versionId>
        </processVariable>
        <processVariable name="changeAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.85a8974d-9569-4c2d-99a6-0b763c6fb2f4</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b137c703-db93-4e80-804a-971c06a448c0</guid>
            <versionId>11e5ec49-516b-437a-82b2-cbd0123ddfa4</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.56e17071-0bc3-479f-b2ce-308ee69ec5d7</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>23f4d119-db89-481a-be8a-9365a5bbbc77</guid>
            <versionId>18f39707-8933-4b6b-bfaa-d97049eb40b3</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.20bb7903-c8bf-4610-a66c-9b045e6a3b5d</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ccc52b14-e188-4738-8e45-8d70597134bf</guid>
            <versionId>5080a20f-092e-4fab-9ec1-0f4166a4f9a6</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.46bbf8af-a56e-4321-800d-5ea722960beb</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>61c9823e-45a1-42ee-a1b6-def4a1fbf028</guid>
            <versionId>2864dfb2-c839-45e8-b91c-086077fbcec1</versionId>
        </processVariable>
        <processVariable name="changePercentage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.32f40e12-c4ba-45d2-81b1-9e8cb07c5bb2</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3952dfd5-07f2-4266-a992-683877e7261a</guid>
            <versionId>725765d1-4c0b-44ea-b81c-8689f58dc6fd</versionId>
        </processVariable>
        <processVariable name="defaultAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1a23012d-7492-4b4d-adba-eb8f5bf8adcf</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f44d2fd3-eb31-483a-928d-9a077e268bf3</guid>
            <versionId>a60ffc2f-f581-474d-b836-b965912c2de9</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4cb0264d-57ab-47e5-b199-d8ae5d957665</processVariableId>
            <description isNull="true" />
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d708a126-49bd-48a6-9038-c9ee856f297a</guid>
            <versionId>5b7455b9-c99d-4fe9-81b1-a82e80545e68</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.995d3178-8aaa-4d17-a459-bd51f4a7845b</processItemId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <name>Map Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.91759cd3-ad95-4bb9-b9ea-f377bfba0ede</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.650d424d-c0f4-455f-845f-64b7b79ca1fa</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2f</guid>
            <versionId>5ab7660c-8797-4747-837f-d5a8f1e18270</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="109" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2c</errorHandlerItem>
                <errorHandlerItemId>2025.650d424d-c0f4-455f-845f-64b7b79ca1fa</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.91759cd3-ad95-4bb9-b9ea-f377bfba0ede</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	var inputList = new tw.object.listOf.String();&#xD;
	inputList = tw.local.data.split(",");&#xD;
	tw.local.collateralAmnt = inputList[0];&#xD;
	tw.local.defaultPercentage = inputList[1];&#xD;
}catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>fc0f7152-4df4-4611-bc94-201c0b34291c</guid>
                <versionId>eaa2e7e4-345f-4080-b67f-fda453b7661b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.650d424d-c0f4-455f-845f-64b7b79ca1fa</processItemId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8c7ccda3-cbbe-43be-863d-f52a47d5f63d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2c</guid>
            <versionId>7fbc7396-7b27-41b9-a0d6-275f4b193312</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="278" y="188">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8c7ccda3-cbbe-43be-863d-f52a47d5f63d</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.error != null &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if ( tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;&#xD;
log.error("IDC PROCESS with ID "+tw.local.instanceID+" An Error Occured in Service Name : "+tw.system.serviceFlow.name+" with Error Message : "+tw.local.errorMSG);</script>
                <isRule>false</isRule>
                <guid>29b8fda0-9604-440d-a008-f68b0ba59e77</guid>
                <versionId>fc88e847-d1f0-4338-97b6-5ee65e6743a6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.50d54e1d-a8a8-475a-99d4-c20f87e3a34d</processItemId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <name>Calculate Change Amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.650d424d-c0f4-455f-845f-64b7b79ca1fa</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2d</guid>
            <versionId>85546182-7cd3-42d4-be57-4094330894a5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.8722f041-8bae-4051-8c15-920954fa6472</processItemPrePostId>
                <processItemId>2025.50d54e1d-a8a8-475a-99d4-c20f87e3a34d</processItemId>
                <location>2</location>
                <script>tw.local.results = Number(tw.local.changeAmount);&#xD;
&#xD;
</script>
                <guid>ad94f667-0f2e-431b-8f78-f486a42de767</guid>
                <versionId>5175bb8b-ab90-4394-9361-4fee07670914</versionId>
            </processPrePosts>
            <layoutData x="277" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2c</errorHandlerItem>
                <errorHandlerItemId>2025.650d424d-c0f4-455f-845f-64b7b79ca1fa</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.c63132b4-26e8-4922-945e-0c52767485a5</attachedProcessRef>
                <guid>75f87eae-b66c-4ec2-95ee-9150606e26b6</guid>
                <versionId>724284e3-f9d6-4aed-a9f9-873f067da08c</versionId>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.59a186d1-676c-4030-abf9-802109f7ce31</parameterMappingId>
                    <processParameterId>2055.03bb44c7-2ddc-40f7-8cf9-25e5eacb2c37</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c0ff6e62-25cc-4927-97c2-7c236823d7f7</guid>
                    <versionId>002e7d3c-d752-45eb-98d6-2dceac27e3d4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f88a99df-816d-468c-b76f-1ee61852fcee</parameterMappingId>
                    <processParameterId>2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ef9b7ff4-4b7c-4f5e-8e77-66d1a838426b</guid>
                    <versionId>1e0abe40-953d-4c11-a9dc-8566fa4dfb25</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.51bc31cc-690e-4626-9ac6-40ffaf08867f</parameterMappingId>
                    <processParameterId>2055.8c593a04-d1d3-40d2-8288-991e270ebc48</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.instanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e57f930b-a90c-4096-a4fd-50f7e0938240</guid>
                    <versionId>1e5743d0-88d0-4c75-a393-c8917ab81ca4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="Percentage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a3fe59a7-a506-4b64-bd14-16af3929e278</parameterMappingId>
                    <processParameterId>2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.defaultPercentage</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3886d632-e8d6-454b-ad22-1510bb0f5971</guid>
                    <versionId>36899bd5-fda7-4b8c-bded-1dc7583c029f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ce0b3696-5943-425a-a051-c87ad8dcbb14</parameterMappingId>
                    <processParameterId>2055.9cf99306-b011-40e1-852b-7c1366b243e7</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3a4c2c2d-b66d-4040-89f5-e66b901697d8</guid>
                    <versionId>4916226c-9a38-464d-9fea-c22083e4ebcd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0a4942c4-dd42-4ce4-a9e8-712bcd7659f4</parameterMappingId>
                    <processParameterId>2055.94ff52b2-0d25-41a6-810b-8c71f86202a8</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.collateralAmnt</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7d7416b2-8a99-4a56-b104-176ade2e870f</guid>
                    <versionId>4dbfd4fd-e4d4-40fb-97c3-9f3e7ea00077</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9ca0bd34-9c31-48cc-b89f-52ff7fb1c188</parameterMappingId>
                    <processParameterId>2055.8420386c-2f0f-431a-8b39-de0080607008</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4a8469d8-889e-4dab-ad71-626d42128704</guid>
                    <versionId>67db8226-ae5a-42f8-9124-9f52d021bd34</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="chargeAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d3b0e155-c5a3-43fd-9096-ffae2129f660</parameterMappingId>
                    <processParameterId>2055.45c20ab6-7f17-473e-891c-c8352d52a7c0</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.changeAmount</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>644cdc29-08b0-4878-91ff-a26521f70e2f</guid>
                    <versionId>7b391c8e-6575-4a0a-aae6-1de664a46372</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6ad3043b-6f60-41cf-9c86-d2ea457040c1</parameterMappingId>
                    <processParameterId>2055.234755dd-aa6d-47c3-8a5d-f6860d21d2da</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a0b50907-bc39-4057-9238-52b4a59f7dba</guid>
                    <versionId>81c343fe-458c-4702-b694-7207f932b36a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.20e259b6-0e7d-4a9d-a784-38c2c83b8162</parameterMappingId>
                    <processParameterId>2055.ec819699-1ccd-4c1b-8a08-e574e43b9765</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>166ae48f-0533-44f2-a4ad-d5d40020c289</guid>
                    <versionId>a56c13a4-f07c-4965-ab50-bb903ec06612</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ef0d33ca-9eed-4cf7-ae9c-e270bb6c0b28</parameterMappingId>
                    <processParameterId>2055.aa2d49db-103b-4ad9-8e57-95487013a0fc</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2324200a-3cc0-4cd9-bf62-e5e573625568</guid>
                    <versionId>ecc27ec1-dd28-40fa-a8fd-2934bf4f0cb5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8f432a11-22ea-44e5-b54a-7f2b72fb345b</parameterMappingId>
                    <processParameterId>2055.d08ff4c0-0f8d-47b1-8bd2-ff091c64a490</processParameterId>
                    <parameterMappingParentId>3012.f7f684b1-0ec1-4fc0-bb2d-7b4ccfcc4036</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2924b3be-cbcb-4da0-9708-300d6026176c</guid>
                    <versionId>ff02ebc8-e1bb-43a9-8295-89b87ec72ba1</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ff4cc3c5-467b-423f-825f-bb17139da505</processItemId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.f2cc8f97-255e-4ce2-9aa5-b45d5adf2081</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2e</guid>
            <versionId>b973a798-0eac-4455-b5d1-4137b5ccf3b4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="540" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.f2cc8f97-255e-4ce2-9aa5-b45d5adf2081</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b3221354-7de3-4234-b903-b479459ac771</guid>
                <versionId>e0864ac7-c7a5-46eb-861c-05b3ae0f5fa5</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.995d3178-8aaa-4d17-a459-bd51f4a7845b</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Calculate Change Amount" id="1.40a7d34d-1192-436a-ba5a-0360265d3261" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.8edfccfc-7a82-4cbf-a36c-40465550425c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"500,22.4"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.ff9a3dd1-fc0d-4504-afbc-dcc535ae5090" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.7859d155-5d55-4fa7-bd13-fc76eb5deb84" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="1ba94ed9-dc26-42ff-993d-e1bed3c7391e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5875e161-488a-47f8-945f-d29d769e55e0" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>5ec02d17-bf63-43c5-8a1b-bca0c154001c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ff4cc3c5-467b-423f-825f-bb17139da505</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>50d54e1d-a8a8-475a-99d4-c20f87e3a34d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>995d3178-8aaa-4d17-a459-bd51f4a7845b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>650d424d-c0f4-455f-845f-64b7b79ca1fa</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a0788a44-b0be-4bbd-a627-2d41fff17bf7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e2611070-8fae-4ca6-9469-758b5ab52bd4</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="5ec02d17-bf63-43c5-8a1b-bca0c154001c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.4176c021-edfa-4547-b2d2-e03d99dea481</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="ff4cc3c5-467b-423f-825f-bb17139da505">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="540" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3b2e</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2c78841a-f5f8-4f01-aa10-d8c83f35f9a7</ns16:incoming>
                        
                        
                        <ns16:incoming>48615fdb-611b-4430-a7ba-7d0518062657</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="5ec02d17-bf63-43c5-8a1b-bca0c154001c" targetRef="995d3178-8aaa-4d17-a459-bd51f4a7845b" name="To Map Input" id="2027.4176c021-edfa-4547-b2d2-e03d99dea481">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c63132b4-26e8-4922-945e-0c52767485a5" name="Calculate Change Amount" id="50d54e1d-a8a8-475a-99d4-c20f87e3a34d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="277" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>tw.local.results = Number(tw.local.changeAmount);&#xD;
&#xD;
</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>850d4a4a-9e91-4d4a-9295-f4ab21b506a2</ns16:incoming>
                        
                        
                        <ns16:outgoing>2c78841a-f5f8-4f01-aa10-d8c83f35f9a7</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.defaultPercentage</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.aa2d49db-103b-4ad9-8e57-95487013a0fc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.234755dd-aa6d-47c3-8a5d-f6860d21d2da</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8420386c-2f0f-431a-8b39-de0080607008</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d08ff4c0-0f8d-47b1-8bd2-ff091c64a490</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8c593a04-d1d3-40d2-8288-991e270ebc48</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94ff52b2-0d25-41a6-810b-8c71f86202a8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.collateralAmnt</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9cf99306-b011-40e1-852b-7c1366b243e7</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.03bb44c7-2ddc-40f7-8cf9-25e5eacb2c37</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ec819699-1ccd-4c1b-8a08-e574e43b9765</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.45c20ab6-7f17-473e-891c-c8352d52a7c0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.changeAmount</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="50d54e1d-a8a8-475a-99d4-c20f87e3a34d" targetRef="ff4cc3c5-467b-423f-825f-bb17139da505" name="To End" id="2c78841a-f5f8-4f01-aa10-d8c83f35f9a7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="collateralAmnt" id="2056.10340b98-d283-404e-a913-efe2773041b8" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map Input" id="995d3178-8aaa-4d17-a459-bd51f4a7845b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="109" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.4176c021-edfa-4547-b2d2-e03d99dea481</ns16:incoming>
                        
                        
                        <ns16:outgoing>850d4a4a-9e91-4d4a-9295-f4ab21b506a2</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	var inputList = new tw.object.listOf.String();&#xD;
	inputList = tw.local.data.split(",");&#xD;
	tw.local.collateralAmnt = inputList[0];&#xD;
	tw.local.defaultPercentage = inputList[1];&#xD;
}catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="995d3178-8aaa-4d17-a459-bd51f4a7845b" targetRef="50d54e1d-a8a8-475a-99d4-c20f87e3a34d" name="To Calculate Change Amount" id="850d4a4a-9e91-4d4a-9295-f4ab21b506a2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="defaultPercentage" id="2056.2e4ca3f4-e22d-47c9-8dec-db6791b81b16" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="changeAmount" id="2056.85a8974d-9569-4c2d-99a6-0b763c6fb2f4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.56e17071-0bc3-479f-b2ce-308ee69ec5d7" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.20bb7903-c8bf-4610-a66c-9b045e6a3b5d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.46bbf8af-a56e-4321-800d-5ea722960beb" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="650d424d-c0f4-455f-845f-64b7b79ca1fa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="278" y="188" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>eb8bda8b-8fc0-4462-aa55-a7242278ef46</ns16:incoming>
                        
                        
                        <ns16:incoming>a98dc870-9a94-41a5-abc1-e1db58f32c7f</ns16:incoming>
                        
                        
                        <ns16:outgoing>48615fdb-611b-4430-a7ba-7d0518062657</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.error != null &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if ( tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;&#xD;
log.error("IDC PROCESS with ID "+tw.local.instanceID+" An Error Occured in Service Name : "+tw.system.serviceFlow.name+" with Error Message : "+tw.local.errorMSG);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="995d3178-8aaa-4d17-a459-bd51f4a7845b" parallelMultiple="false" name="Error" id="a0788a44-b0be-4bbd-a627-2d41fff17bf7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="144" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>eb8bda8b-8fc0-4462-aa55-a7242278ef46</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="13d8a720-e47c-4905-a99c-040686103add" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="3915226b-8e7f-4cfc-a14d-7d2ff19ae826" eventImplId="45cde78b-a982-4aab-8b06-384eb5c8edae">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="50d54e1d-a8a8-475a-99d4-c20f87e3a34d" parallelMultiple="false" name="Error1" id="e2611070-8fae-4ca6-9469-758b5ab52bd4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="312" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a98dc870-9a94-41a5-abc1-e1db58f32c7f</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="93a1aa4e-c268-4f59-b927-872c453c7b26" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="84e6b13e-c0b3-4fbb-bc8c-926d5494c506" eventImplId="9738eff8-4e3f-47c7-83b8-36f5842986d3">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a0788a44-b0be-4bbd-a627-2d41fff17bf7" targetRef="650d424d-c0f4-455f-845f-64b7b79ca1fa" name="To Catch Errors" id="eb8bda8b-8fc0-4462-aa55-a7242278ef46">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e2611070-8fae-4ca6-9469-758b5ab52bd4" targetRef="650d424d-c0f4-455f-845f-64b7b79ca1fa" name="To Catch Errors" id="a98dc870-9a94-41a5-abc1-e1db58f32c7f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="650d424d-c0f4-455f-845f-64b7b79ca1fa" targetRef="ff4cc3c5-467b-423f-825f-bb17139da505" name="To End" id="48615fdb-611b-4430-a7ba-7d0518062657">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="changePercentage" id="2056.32f40e12-c4ba-45d2-81b1-9e8cb07c5bb2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="defaultAmount" id="2056.1a23012d-7492-4b4d-adba-eb8f5bf8adcf" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.4cb0264d-57ab-47e5-b199-d8ae5d957665" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.48615fdb-611b-4430-a7ba-7d0518062657</processLinkId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.650d424d-c0f4-455f-845f-64b7b79ca1fa</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ff4cc3c5-467b-423f-825f-bb17139da505</toProcessItemId>
            <guid>b6f0ee99-ef2c-49c3-8537-616d55423d5d</guid>
            <versionId>10064a56-e0e6-45aa-83ce-a65c22bd424b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.650d424d-c0f4-455f-845f-64b7b79ca1fa</fromProcessItemId>
            <toProcessItemId>2025.ff4cc3c5-467b-423f-825f-bb17139da505</toProcessItemId>
        </link>
        <link name="To Calculate Change Amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.850d4a4a-9e91-4d4a-9295-f4ab21b506a2</processLinkId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.995d3178-8aaa-4d17-a459-bd51f4a7845b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.50d54e1d-a8a8-475a-99d4-c20f87e3a34d</toProcessItemId>
            <guid>af95122f-14a0-4e73-b456-23cf2b316ee8</guid>
            <versionId>8ac7af65-1d19-43a8-97c6-45c49bec812b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.995d3178-8aaa-4d17-a459-bd51f4a7845b</fromProcessItemId>
            <toProcessItemId>2025.50d54e1d-a8a8-475a-99d4-c20f87e3a34d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2c78841a-f5f8-4f01-aa10-d8c83f35f9a7</processLinkId>
            <processId>1.40a7d34d-1192-436a-ba5a-0360265d3261</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.50d54e1d-a8a8-475a-99d4-c20f87e3a34d</fromProcessItemId>
            <endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603</endStateId>
            <toProcessItemId>2025.ff4cc3c5-467b-423f-825f-bb17139da505</toProcessItemId>
            <guid>74c46569-ac5b-4766-bfe6-cb4325e93044</guid>
            <versionId>fd9a7a19-f791-4211-b522-c6da70bdf4fe</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.50d54e1d-a8a8-475a-99d4-c20f87e3a34d</fromProcessItemId>
            <toProcessItemId>2025.ff4cc3c5-467b-423f-825f-bb17139da505</toProcessItemId>
        </link>
    </process>
</teamworks>

