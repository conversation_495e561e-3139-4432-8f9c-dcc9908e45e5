<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.8a1c5f67-55b2-4785-97cb-9ec3e4441186" name="Create Folder FileNet">
        <lastModified>1692517102531</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>32bfad83-f0e3-4c6b-a54e-df98ab349f21</guid>
        <versionId>6415877a-8b28-461c-8678-da20a8bd7fad</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:aa4c986259b1691d:-23a6d209:18a11a6235c:-55a9" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["a3278b3b-72d5-40ad-8f33-59aa3954be74"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":180,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"363595f9-2e1f-41c0-a7ea-6c290347c1b1"},{"incoming":["6ffca438-aeea-4957-9031-860407594c35","5b198226-e3ad-461d-9677-43d822ef24a4"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":940,"y":180,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:-15c3"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b"},{"outgoing":["eb5c74a2-1cfd-4704-b15b-ebf54d8ac408"],"incoming":["f4164704-3364-405a-a4cf-cf8dccea0061","ae9a7470-a9c1-45b6-bb27-812131c23770"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":504,"y":157,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_CREATE_FOLDER","implementation":"##WebService","serverName":"useMappingServer","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Create Folder","dataInputAssociation":[{"targetRef":"NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.folderName"]}}]},{"targetRef":"PARENT_FOLDER_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.parentFolderID"]}}]},{"targetRef":"PROPERTIES","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}]},{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"FileNet\""]}}]},{"targetRef":"OBJECT_TYPE_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"FolderTemp\""]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"f21cb207-a190-41b9-b10d-7d39f96ac7c5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}],"sourceRef":["FOLDER_ID"]}],"orderOverride":false},{"startQuantity":1,"outgoing":["6ffca438-aeea-4957-9031-860407594c35"],"incoming":["eb5c74a2-1cfd-4704-b15b-ebf54d8ac408"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":737,"y":157,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67","scriptFormat":"text\/x-javascript","script":{"content":["var fID = tw.local.folderID.toString();\r\nfID = fID.substring(4);\r\ntw.local.folderID = \"{\" + fID + \"}\" ;"]}},{"targetRef":"9ce3886e-02c6-4e5c-84c5-ce4a01f51542","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Parent Folder Exist","declaredType":"sequenceFlow","id":"a3278b3b-72d5-40ad-8f33-59aa3954be74","sourceRef":"363595f9-2e1f-41c0-a7ea-6c290347c1b1"},{"targetRef":"c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get ID","declaredType":"sequenceFlow","id":"eb5c74a2-1cfd-4704-b15b-ebf54d8ac408","sourceRef":"f21cb207-a190-41b9-b10d-7d39f96ac7c5"},{"targetRef":"b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"6ffca438-aeea-4957-9031-860407594c35","sourceRef":"c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67"},{"outgoing":["569b6b1e-f44d-4094-bc5d-f54da7e8bd11"],"incoming":["647df52e-aafe-46a3-966b-c43d492d82bb"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":503,"y":279,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_FOLDER_BY_PATH","implementation":"##WebService","serverName":"useMappingServer","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Folder Path","dataInputAssociation":[{"targetRef":"PATH","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.parentFolderPath+\"\/\"+tw.local.folderName"]}}]},{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"FileNet\""]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","declaredType":"TFormalExpression","content":["tw.local.folder"]}}],"sourceRef":["FOLDER"]}],"orderOverride":false},{"itemSubjectRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","name":"folder","isCollection":false,"declaredType":"dataObject","id":"2056.a86b02be-147b-402a-bce5-de80edea8b4a"},{"startQuantity":1,"outgoing":["5b198226-e3ad-461d-9677-43d822ef24a4"],"incoming":["569b6b1e-f44d-4094-bc5d-f54da7e8bd11"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":729,"y":279,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get Folder ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"9e3296e0-52ba-45ce-b809-ee2920ccd61a","scriptFormat":"text\/x-javascript","script":{"content":["var fID = tw.local.folder.objectId.toString();\r\nfID = fID.substring(4);\r\ntw.local.folderID = \"{\" + fID + \"}\" ;"]}},{"targetRef":"9e3296e0-52ba-45ce-b809-ee2920ccd61a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Folder ID","declaredType":"sequenceFlow","id":"569b6b1e-f44d-4094-bc5d-f54da7e8bd11","sourceRef":"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec"},{"targetRef":"b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"5b198226-e3ad-461d-9677-43d822ef24a4","sourceRef":"9e3296e0-52ba-45ce-b809-ee2920ccd61a"},{"incoming":["fab2c56e-2536-4117-931e-3f1f6a920cf1"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"086ee1d1-050a-421c-9ef0-1fc36b0aef0e","otherAttributes":{"eventImplId":"0dadf3e3-cca2-4a36-8b95-15d5ddf44c75"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":661,"y":374,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}]}],"declaredType":"endEvent","id":"91958c60-6f9d-465a-9bf8-b3f7098d293b"},{"outgoing":["806448af-5398-4733-90d4-27ad3548411c"],"incoming":["1684a75b-107f-4f05-a39f-8e1cf8969ea9"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":238,"y":157,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_FOLDER_BY_PATH","implementation":"##WebService","serverName":"useMappingServer","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Parent Folder By Path","dataInputAssociation":[{"targetRef":"PATH","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentFolderPath"]}}]},{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"FileNet\""]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","declaredType":"TFormalExpression","content":["tw.local.parent"]}}],"sourceRef":["FOLDER"]}],"orderOverride":false},{"targetRef":"03092d42-57f8-40ea-8811-5bb47f0f3b4e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Parent Folder ID","declaredType":"sequenceFlow","id":"806448af-5398-4733-90d4-27ad3548411c","sourceRef":"7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60"},{"itemSubjectRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","name":"parent","isCollection":false,"declaredType":"dataObject","id":"2056.2f56b88c-c347-4340-af59-ae2e991a8012"},{"parallelMultiple":false,"outgoing":["647df52e-aafe-46a3-966b-c43d492d82bb"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"9bcf3ccd-cc70-4b0e-8fa6-5ae680069b92"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"cba5ace3-7091-4dac-9cb5-5628f653c133","otherAttributes":{"eventImplId":"800f7367-f5b4-4eb6-8171-1be5da45ce6d"}}],"attachedToRef":"f21cb207-a190-41b9-b10d-7d39f96ac7c5","extensionElements":{"nodeVisualInfo":[{"width":24,"x":539,"y":215,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5","outputSet":{}},{"targetRef":"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Folder Path","declaredType":"sequenceFlow","id":"647df52e-aafe-46a3-966b-c43d492d82bb","sourceRef":"dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5"},{"parallelMultiple":false,"outgoing":["fab2c56e-2536-4117-931e-3f1f6a920cf1"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"7091cfbb-a0c2-4753-864e-c442982f66ed"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ca0577d8-9f93-44ec-bb36-97e884c4e1ec","otherAttributes":{"eventImplId":"738a5e9b-9337-43d6-8f6b-583f20b18a99"}}],"attachedToRef":"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec","extensionElements":{"nodeVisualInfo":[{"width":24,"x":538,"y":337,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"92d3f8b7-7247-4ead-a7b3-650e8a6ce87b","outputSet":{}},{"targetRef":"91958c60-6f9d-465a-9bf8-b3f7098d293b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Error","declaredType":"sequenceFlow","id":"fab2c56e-2536-4117-931e-3f1f6a920cf1","sourceRef":"92d3f8b7-7247-4ead-a7b3-650e8a6ce87b"},{"outgoing":["1684a75b-107f-4f05-a39f-8e1cf8969ea9","f4164704-3364-405a-a4cf-cf8dccea0061"],"incoming":["a3278b3b-72d5-40ad-8f33-59aa3954be74"],"default":"1684a75b-107f-4f05-a39f-8e1cf8969ea9","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":123,"y":176,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Parent Folder Exist","declaredType":"exclusiveGateway","id":"9ce3886e-02c6-4e5c-84c5-ce4a01f51542"},{"targetRef":"7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.parentFolderID\t  ==\t  "]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"not exist","declaredType":"sequenceFlow","id":"1684a75b-107f-4f05-a39f-8e1cf8969ea9","sourceRef":"9ce3886e-02c6-4e5c-84c5-ce4a01f51542"},{"targetRef":"f21cb207-a190-41b9-b10d-7d39f96ac7c5","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.parentFolderID\t  !=\t  null"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"does not exist","declaredType":"sequenceFlow","id":"f4164704-3364-405a-a4cf-cf8dccea0061","sourceRef":"9ce3886e-02c6-4e5c-84c5-ce4a01f51542"},{"startQuantity":1,"outgoing":["ae9a7470-a9c1-45b6-bb27-812131c23770"],"incoming":["806448af-5398-4733-90d4-27ad3548411c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":364,"y":157,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Parent Folder ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"03092d42-57f8-40ea-8811-5bb47f0f3b4e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.parentFolderID= tw.local.parent.objectId;"]}},{"targetRef":"f21cb207-a190-41b9-b10d-7d39f96ac7c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Folder","declaredType":"sequenceFlow","id":"ae9a7470-a9c1-45b6-bb27-812131c23770","sourceRef":"03092d42-57f8-40ea-8811-5bb47f0f3b4e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.2a8d058c-2114-451f-8b41-9ee1afc0f5eb"}],"laneSet":[{"id":"1c395cd6-4af0-4d2b-9068-ae13a29eba7f","lane":[{"flowNodeRef":["363595f9-2e1f-41c0-a7ea-6c290347c1b1","b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b","f21cb207-a190-41b9-b10d-7d39f96ac7c5","c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67","91958c60-6f9d-465a-9bf8-b3f7098d293b","2d599c7d-5ed0-4edb-a4e6-b527a56b8fec","9e3296e0-52ba-45ce-b809-ee2920ccd61a","7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60","dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5","92d3f8b7-7247-4ead-a7b3-650e8a6ce87b","9ce3886e-02c6-4e5c-84c5-ce4a01f51542","03092d42-57f8-40ea-8811-5bb47f0f3b4e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5bbe3ea9-b8cb-43be-9892-860764be0829","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create Folder FileNet","declaredType":"process","id":"1.8a1c5f67-55b2-4785-97cb-9ec3e4441186","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"id":"2055.21385f08-1be8-41a4-b758-c8dc9b8bb509"}],"inputSet":[{"dataInputRefs":["2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e","2055.98c175d8-7e63-4e2d-ac3f-************","2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63"]}],"outputSet":[{"dataOutputRefs":["2055.21385f08-1be8-41a4-b758-c8dc9b8bb509"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentFolderPath","isCollection":false,"id":"2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"folderName","isCollection":false,"id":"2055.98c175d8-7e63-4e2d-ac3f-************"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"parentFolderID","isCollection":false,"id":"2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="parentFolderPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>14fa973f-4230-4f92-9d6d-72a7e66a3530</guid>
            <versionId>6e0cb88c-d167-4ff5-9d52-ef310a5d1d3c</versionId>
        </processParameter>
        <processParameter name="folderName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.98c175d8-7e63-4e2d-ac3f-************</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6829457d-35bc-4556-9ad7-6c8679a05229</guid>
            <versionId>7dde878f-4fd0-472b-8d8c-3d6143a6771b</versionId>
        </processParameter>
        <processParameter name="parentFolderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f6b27258-5475-4699-acd3-7d61c5ca5726</guid>
            <versionId>77088655-fe63-4e83-b442-31c0c42042d2</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.21385f08-1be8-41a4-b758-c8dc9b8bb509</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>30c66d73-f8d3-464f-b559-3675d08e46e7</guid>
            <versionId>1b601800-ed3f-469e-9d97-a2e3e2a7ce65</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e37e2691-8697-4ec5-8371-2cc7c3a976ea</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>56</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5ae2e703-0434-461e-8a7e-2c2cf0d3e700</guid>
            <versionId>df0df81d-ce66-497a-94ae-d44d402332ee</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.caee7282-11cb-478f-96e5-4cf6ab11acc6</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>122</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0a320314-8b7c-4955-b17f-f21c608dbb05</guid>
            <versionId>ae5d319d-66f4-402a-a90d-92991d30039f</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.94074e6b-b956-4746-b409-e66e410cf818</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>123</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>eaddbefa-08c5-4da7-8173-2b2fa5b0fea1</guid>
            <versionId>ccc9ef05-acf3-4785-88e5-9e29ee94e818</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d61d9230-7663-4bb4-858c-7467edbece0c</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>124</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6efb6b3c-a658-4187-9f0b-98dcdb3bbe4e</guid>
            <versionId>5ed1d054-be62-4339-a16b-baa0713f3b0f</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9faa5b20-b8b4-4cce-997a-a4bd6de08a61</processParameterId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>125</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3b9bc890-35a7-421b-8fa9-96639c2998d9</guid>
            <versionId>c790592c-1b97-4bce-ae18-fc8b17884855</versionId>
        </processParameter>
        <processVariable name="folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a86b02be-147b-402a-bce5-de80edea8b4a</processVariableId>
            <description isNull="true" />
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>371ff49f-fef8-46a7-8ca4-09272fd63b24</guid>
            <versionId>90db085e-301f-4ce3-982e-282f3e2b5840</versionId>
        </processVariable>
        <processVariable name="parent">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2f56b88c-c347-4340-af59-ae2e991a8012</processVariableId>
            <description isNull="true" />
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>60bc553b-aea3-417e-a97c-8e81aa770005</guid>
            <versionId>f4bc0ef0-184b-41b5-ad19-3b91d44575f6</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2a8d058c-2114-451f-8b41-9ee1afc0f5eb</processVariableId>
            <description isNull="true" />
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bb1023ab-39ff-4537-a4b5-126aa0aaa084</guid>
            <versionId>5c62f03a-115a-40d0-b077-f442358590d9</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Parent Folder Exist</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.6ace1fdc-afcb-47bb-936d-f0a3ead13033</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15be</guid>
            <versionId>09ebe71d-bf53-42e5-807a-983240c22f5d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="123" y="176">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.6ace1fdc-afcb-47bb-936d-f0a3ead13033</switchId>
                <guid>a6bf16c1-1d97-4309-a1e8-0d58fbdf867e</guid>
                <versionId>883ffd22-7f8c-426b-8981-f553ef23f763</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.b4da1864-cf7f-41d8-aed9-954e1ec88c94</switchConditionId>
                    <switchId>3013.6ace1fdc-afcb-47bb-936d-f0a3ead13033</switchId>
                    <seq>1</seq>
                    <endStateId>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:-55aa</endStateId>
                    <condition>tw.local.parentFolderID	  !=	  null</condition>
                    <guid>66e97bae-7ea7-4e2a-ba30-c6ec1c244368</guid>
                    <versionId>a237abf5-e987-4342-8f24-d8c8dc77ac62</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Get ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0e00adad-e3df-4abf-87a0-02f3fa7186eb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15c0</guid>
            <versionId>1832621f-5953-46b1-b56d-a5a23e8aad1c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="737" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0e00adad-e3df-4abf-87a0-02f3fa7186eb</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var fID = tw.local.folderID.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</script>
                <isRule>false</isRule>
                <guid>319d0fc3-8ca0-4709-b28b-c6ace78fea4a</guid>
                <versionId>a57fc192-5c4d-47b3-a718-667ea9921c37</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Set Parent Folder ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.f1131d45-0f8e-4664-86ca-84c2757d88b0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15c6</guid>
            <versionId>1b78d474-f83e-4244-bd26-943e9356ab70</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="364" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.f1131d45-0f8e-4664-86ca-84c2757d88b0</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.parentFolderID= tw.local.parent.objectId;</script>
                <isRule>false</isRule>
                <guid>4cd1263c-95a7-4052-b557-998fffd1c3a3</guid>
                <versionId>*************-4603-8d8b-85b05a7fb7af</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Get Folder Path</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.b53173c5-0d8c-443d-9841-91007a43a41d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.91958c60-6f9d-465a-9bf8-b3f7098d293b</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15c5</guid>
            <versionId>38266acc-9c4c-460d-bf37-288e53b86795</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.ade002f3-9d73-4adc-a67e-6dee21933fdf</processItemPrePostId>
                <processItemId>2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>24625521-5ed5-4ffe-a45f-0dcebb0573e0</guid>
                <versionId>26ce713c-3759-48eb-9be4-30a480c84b37</versionId>
            </processPrePosts>
            <layoutData x="503" y="279">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:fef170a08f25d496:5466e087:189df5f8551:-15c2</errorHandlerItem>
                <errorHandlerItemId>2025.91958c60-6f9d-465a-9bf8-b3f7098d293b</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.b53173c5-0d8c-443d-9841-91007a43a41d</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;path&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parentFolderPath+"/"+tw.local.folderName&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"FileNet"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folder&lt;/name&gt;&#xD;
      &lt;type&gt;ECMFolder&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folder&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_FOLDER_BY_PATH&lt;/operationType&gt;&#xD;
  &lt;server&gt;useMappingServer&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.9faa5b20-b8b4-4cce-997a-a4bd6de08a61&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>91806f92-0abd-40e4-bff5-0343dc27702e</guid>
                <versionId>81d60e21-f492-43f5-aa5e-355dc1f74ec1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Create Folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.0f289cae-63c5-4cfe-a4dd-fc0724489ee3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15bf</guid>
            <versionId>535520fe-c337-434a-8092-c6f47977132a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.d511c3f0-784c-45a1-bb94-a75bcb0f90e4</processItemPrePostId>
                <processItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>0faba6db-0ca8-4076-b5d8-e170f8626c5b</guid>
                <versionId>f455d1d7-1283-4dde-a41a-5024d6686c76</versionId>
            </processPrePosts>
            <layoutData x="504" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:fef170a08f25d496:5466e087:189df5f8551:-15c5</errorHandlerItem>
                <errorHandlerItemId>2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.0f289cae-63c5-4cfe-a4dd-fc0724489ee3</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;objectTypeId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"FolderTemp"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;parentFolderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parentFolderID&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;name&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderName&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;properties&lt;/name&gt;&#xD;
      &lt;type&gt;ECMProperty&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;true&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"FileNet"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderID&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_CREATE_FOLDER&lt;/operationType&gt;&#xD;
  &lt;server&gt;useMappingServer&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.d61d9230-7663-4bb4-858c-7467edbece0c&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>b92eaa89-c8e6-405e-85d2-ccf4751c4f2e</guid>
                <versionId>55e8707b-fac8-42ae-af6c-823f277646ec</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Get Parent Folder By Path</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.b77e0b47-c650-45dc-8d6f-6dca6c78f1b2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15c1</guid>
            <versionId>64f49358-7495-4a1e-955b-8bcbcdd024e5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="238" y="157">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.b77e0b47-c650-45dc-8d6f-6dca6c78f1b2</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;path&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parentFolderPath&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"FileNet"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folder&lt;/name&gt;&#xD;
      &lt;type&gt;ECMFolder&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.parent&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_FOLDER_BY_PATH&lt;/operationType&gt;&#xD;
  &lt;server&gt;useMappingServer&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.94074e6b-b956-4746-b409-e66e410cf818&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>df5a9f15-ec0e-4c92-ac9b-3da413854624</guid>
                <versionId>68b9e9c7-1f34-4397-a232-141f57a3f07e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.91958c60-6f9d-465a-9bf8-b3f7098d293b</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Error</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.28f7282a-889f-4db3-8f1d-decc5e3f3ee1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15c2</guid>
            <versionId>6a5de735-72e2-4de8-8e43-83fc360a2380</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="661" y="374">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.28f7282a-889f-4db3-8f1d-decc5e3f3ee1</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>7fe3ac2f-8aab-4982-b57b-f2ad360cc05e</guid>
                <versionId>b08bd756-ac1d-441f-9b4c-4d1104d2f19d</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5a22d340-a620-47ec-b4ac-705eec869255</parameterMappingId>
                    <processParameterId>2055.e37e2691-8697-4ec5-8371-2cc7c3a976ea</processParameterId>
                    <parameterMappingParentId>3007.28f7282a-889f-4db3-8f1d-decc5e3f3ee1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>cdb6d660-ddea-4380-bd73-26ba1af2c5fc</guid>
                    <versionId>be75c203-44e0-4441-9021-838fe6c0169d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.11d60de9-899d-4f97-9c40-414a901739e9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15c3</guid>
            <versionId>9dc83b75-a30d-4436-bb28-6dc4d43fbaf6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="940" y="180">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.11d60de9-899d-4f97-9c40-414a901739e9</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b1392e5f-b1a3-4543-9516-eb123a37fbd9</guid>
                <versionId>be5ce4d4-fd52-459e-b6e3-8cc021424e79</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a</processItemId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <name>Get Folder ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e8cf361d-48b9-457c-aa62-c4da22478c0b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-15c4</guid>
            <versionId>a216f418-7567-4703-8a38-41c8fd089186</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="729" y="279">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e8cf361d-48b9-457c-aa62-c4da22478c0b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var fID = tw.local.folder.objectId.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</script>
                <isRule>false</isRule>
                <guid>931dfef3-c427-4b06-8dea-ec5080310fcd</guid>
                <versionId>38ab8292-e8f2-4fd7-9b9b-e83980b7b1cf</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="180">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Create Folder FileNet" id="1.8a1c5f67-55b2-4785-97cb-9ec3e4441186" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="parentFolderPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="folderName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.98c175d8-7e63-4e2d-ac3f-************">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="parentFolderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.21385f08-1be8-41a4-b758-c8dc9b8bb509" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.98c175d8-7e63-4e2d-ac3f-************</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.21385f08-1be8-41a4-b758-c8dc9b8bb509</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="1c395cd6-4af0-4d2b-9068-ae13a29eba7f">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5bbe3ea9-b8cb-43be-9892-860764be0829" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>363595f9-2e1f-41c0-a7ea-6c290347c1b1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f21cb207-a190-41b9-b10d-7d39f96ac7c5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>91958c60-6f9d-465a-9bf8-b3f7098d293b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2d599c7d-5ed0-4edb-a4e6-b527a56b8fec</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9e3296e0-52ba-45ce-b809-ee2920ccd61a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>92d3f8b7-7247-4ead-a7b3-650e8a6ce87b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9ce3886e-02c6-4e5c-84c5-ce4a01f51542</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>03092d42-57f8-40ea-8811-5bb47f0f3b4e</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="363595f9-2e1f-41c0-a7ea-6c290347c1b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="180" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a3278b3b-72d5-40ad-8f33-59aa3954be74</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="940" y="180" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-15c3</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6ffca438-aeea-4957-9031-860407594c35</ns16:incoming>
                        
                        
                        <ns16:incoming>5b198226-e3ad-461d-9677-43d822ef24a4</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns4:contentTask serverName="useMappingServer" operationRef="FOLDER_OP_CREATE_FOLDER" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Create Folder" id="f21cb207-a190-41b9-b10d-7d39f96ac7c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="504" y="157" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f4164704-3364-405a-a4cf-cf8dccea0061</ns16:incoming>
                        
                        
                        <ns16:incoming>ae9a7470-a9c1-45b6-bb27-812131c23770</ns16:incoming>
                        
                        
                        <ns16:outgoing>eb5c74a2-1cfd-4704-b15b-ebf54d8ac408</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.folderName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PARENT_FOLDER_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.parentFolderID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PROPERTIES</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"FileNet"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>OBJECT_TYPE_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"FolderTemp"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER_ID</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get ID" id="c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="737" y="157" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>eb5c74a2-1cfd-4704-b15b-ebf54d8ac408</ns16:incoming>
                        
                        
                        <ns16:outgoing>6ffca438-aeea-4957-9031-860407594c35</ns16:outgoing>
                        
                        
                        <ns16:script>var fID = tw.local.folderID.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="363595f9-2e1f-41c0-a7ea-6c290347c1b1" targetRef="9ce3886e-02c6-4e5c-84c5-ce4a01f51542" name="To Parent Folder Exist" id="a3278b3b-72d5-40ad-8f33-59aa3954be74">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f21cb207-a190-41b9-b10d-7d39f96ac7c5" targetRef="c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67" name="To Get ID" id="eb5c74a2-1cfd-4704-b15b-ebf54d8ac408">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67" targetRef="b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b" name="To End" id="6ffca438-aeea-4957-9031-860407594c35">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns4:contentTask serverName="useMappingServer" operationRef="FOLDER_OP_GET_FOLDER_BY_PATH" name="Get Folder Path" id="2d599c7d-5ed0-4edb-a4e6-b527a56b8fec">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="503" y="279" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>647df52e-aafe-46a3-966b-c43d492d82bb</ns16:incoming>
                        
                        
                        <ns16:outgoing>569b6b1e-f44d-4094-bc5d-f54da7e8bd11</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PATH</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.parentFolderPath+"/"+tw.local.folderName</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"FileNet"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183">tw.local.folder</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183" isCollection="false" name="folder" id="2056.a86b02be-147b-402a-bce5-de80edea8b4a" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get Folder ID" id="9e3296e0-52ba-45ce-b809-ee2920ccd61a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="729" y="279" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>569b6b1e-f44d-4094-bc5d-f54da7e8bd11</ns16:incoming>
                        
                        
                        <ns16:outgoing>5b198226-e3ad-461d-9677-43d822ef24a4</ns16:outgoing>
                        
                        
                        <ns16:script>var fID = tw.local.folder.objectId.toString();&#xD;
fID = fID.substring(4);&#xD;
tw.local.folderID = "{" + fID + "}" ;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2d599c7d-5ed0-4edb-a4e6-b527a56b8fec" targetRef="9e3296e0-52ba-45ce-b809-ee2920ccd61a" name="To Get Folder ID" id="569b6b1e-f44d-4094-bc5d-f54da7e8bd11">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9e3296e0-52ba-45ce-b809-ee2920ccd61a" targetRef="b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b" name="To End" id="5b198226-e3ad-461d-9677-43d822ef24a4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="Error" id="91958c60-6f9d-465a-9bf8-b3f7098d293b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="661" y="374" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fab2c56e-2536-4117-931e-3f1f6a920cf1</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="086ee1d1-050a-421c-9ef0-1fc36b0aef0e" eventImplId="0dadf3e3-cca2-4a36-8b95-15d5ddf44c75">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns4:contentTask serverName="useMappingServer" operationRef="FOLDER_OP_GET_FOLDER_BY_PATH" name="Get Parent Folder By Path" id="7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="238" y="157" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1684a75b-107f-4f05-a39f-8e1cf8969ea9</ns16:incoming>
                        
                        
                        <ns16:outgoing>806448af-5398-4733-90d4-27ad3548411c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PATH</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentFolderPath</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"FileNet"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183">tw.local.parent</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60" targetRef="03092d42-57f8-40ea-8811-5bb47f0f3b4e" name="To Set Parent Folder ID" id="806448af-5398-4733-90d4-27ad3548411c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183" isCollection="false" name="parent" id="2056.2f56b88c-c347-4340-af59-ae2e991a8012" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="f21cb207-a190-41b9-b10d-7d39f96ac7c5" parallelMultiple="false" name="Error" id="dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="539" y="215" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>647df52e-aafe-46a3-966b-c43d492d82bb</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="9bcf3ccd-cc70-4b0e-8fa6-5ae680069b92" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="cba5ace3-7091-4dac-9cb5-5628f653c133" eventImplId="800f7367-f5b4-4eb6-8171-1be5da45ce6d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5" targetRef="2d599c7d-5ed0-4edb-a4e6-b527a56b8fec" name="To Get Folder Path" id="647df52e-aafe-46a3-966b-c43d492d82bb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="2d599c7d-5ed0-4edb-a4e6-b527a56b8fec" parallelMultiple="false" name="Error1" id="92d3f8b7-7247-4ead-a7b3-650e8a6ce87b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="538" y="337" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>fab2c56e-2536-4117-931e-3f1f6a920cf1</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="7091cfbb-a0c2-4753-864e-c442982f66ed" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ca0577d8-9f93-44ec-bb36-97e884c4e1ec" eventImplId="738a5e9b-9337-43d6-8f6b-583f20b18a99">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="92d3f8b7-7247-4ead-a7b3-650e8a6ce87b" targetRef="91958c60-6f9d-465a-9bf8-b3f7098d293b" name="To Error" id="fab2c56e-2536-4117-931e-3f1f6a920cf1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="1684a75b-107f-4f05-a39f-8e1cf8969ea9" name="Parent Folder Exist" id="9ce3886e-02c6-4e5c-84c5-ce4a01f51542">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="123" y="176" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a3278b3b-72d5-40ad-8f33-59aa3954be74</ns16:incoming>
                        
                        
                        <ns16:outgoing>1684a75b-107f-4f05-a39f-8e1cf8969ea9</ns16:outgoing>
                        
                        
                        <ns16:outgoing>f4164704-3364-405a-a4cf-cf8dccea0061</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="9ce3886e-02c6-4e5c-84c5-ce4a01f51542" targetRef="7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60" name="not exist" id="1684a75b-107f-4f05-a39f-8e1cf8969ea9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.parentFolderID	  ==	  </ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9ce3886e-02c6-4e5c-84c5-ce4a01f51542" targetRef="f21cb207-a190-41b9-b10d-7d39f96ac7c5" name="does not exist" id="f4164704-3364-405a-a4cf-cf8dccea0061">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.parentFolderID	  !=	  null</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Parent Folder ID" id="03092d42-57f8-40ea-8811-5bb47f0f3b4e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="364" y="157" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>806448af-5398-4733-90d4-27ad3548411c</ns16:incoming>
                        
                        
                        <ns16:outgoing>ae9a7470-a9c1-45b6-bb27-812131c23770</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.parentFolderID= tw.local.parent.objectId;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="03092d42-57f8-40ea-8811-5bb47f0f3b4e" targetRef="f21cb207-a190-41b9-b10d-7d39f96ac7c5" name="To Create Folder" id="ae9a7470-a9c1-45b6-bb27-812131c23770">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.2a8d058c-2114-451f-8b41-9ee1afc0f5eb" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Get ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.eb5c74a2-1cfd-4704-b15b-ebf54d8ac408</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67</toProcessItemId>
            <guid>f9c5761a-c02f-4e90-9d8f-beda95826128</guid>
            <versionId>0ca16998-4748-41b8-b356-d46e42cd2dc4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</fromProcessItemId>
            <toProcessItemId>2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67</toProcessItemId>
        </link>
        <link name="To Set Parent Folder ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.806448af-5398-4733-90d4-27ad3548411c</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e</toProcessItemId>
            <guid>2447edf9-0f72-46b6-a306-86472010962f</guid>
            <versionId>42a3eeec-f63d-4468-b50b-bdce7ea1b51d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60</fromProcessItemId>
            <toProcessItemId>2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5b198226-e3ad-461d-9677-43d822ef24a4</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b</toProcessItemId>
            <guid>709c376a-c252-4f8b-9a39-e172c717eb93</guid>
            <versionId>59e92cad-23d9-4ba2-adbc-d4300368dead</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a</fromProcessItemId>
            <toProcessItemId>2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b</toProcessItemId>
        </link>
        <link name="does not exist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f4164704-3364-405a-a4cf-cf8dccea0061</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542</fromProcessItemId>
            <endStateId>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:-55aa</endStateId>
            <toProcessItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</toProcessItemId>
            <guid>6411c176-25e6-4f49-9671-c6aa64f90396</guid>
            <versionId>6e560c23-4b61-4601-9947-99695a6cc292</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542</fromProcessItemId>
            <toProcessItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</toProcessItemId>
        </link>
        <link name="To Get Folder ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.569b6b1e-f44d-4094-bc5d-f54da7e8bd11</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a</toProcessItemId>
            <guid>f68aabc9-3d56-43ad-bb81-27f983a50c99</guid>
            <versionId>9c843449-8afd-492d-aead-dc7f817d3134</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec</fromProcessItemId>
            <toProcessItemId>2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6ffca438-aeea-4957-9031-860407594c35</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b</toProcessItemId>
            <guid>a5356230-3eac-4a14-aa6c-63d825bcb440</guid>
            <versionId>ba75f181-79f1-4288-874f-dded92e46fa8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67</fromProcessItemId>
            <toProcessItemId>2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b</toProcessItemId>
        </link>
        <link name="To Create Folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ae9a7470-a9c1-45b6-bb27-812131c23770</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</toProcessItemId>
            <guid>e9b24b9e-5727-4dfc-92ae-0536cf5b0493</guid>
            <versionId>bb3f3598-ae93-4ef4-95b6-6ac3488419d7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e</fromProcessItemId>
            <toProcessItemId>2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5</toProcessItemId>
        </link>
        <link name="not exist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1684a75b-107f-4f05-a39f-8e1cf8969ea9</processLinkId>
            <processId>1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60</toProcessItemId>
            <guid>3a239312-72b7-4b87-bd1f-c177d22b02aa</guid>
            <versionId>d6bf8d4b-e043-4792-a8fb-7aab6cfc4b5e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542</fromProcessItemId>
            <toProcessItemId>2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60</toProcessItemId>
        </link>
    </process>
</teamworks>

