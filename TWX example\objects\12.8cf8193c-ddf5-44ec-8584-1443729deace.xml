<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.8cf8193c-ddf5-44ec-8584-1443729deace" name="RequestTemplatePojo">
        <lastModified>1693480732445</lastModified>
        <lastModifiedBy>abdel<PERSON>man.saleh</lastModifiedBy>
        <classId>12.8cf8193c-ddf5-44ec-8584-1443729deace</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>true</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.8cf8193c-ddf5-44ec-8584-1443729deace</externalId>
        <dependencySummary>&lt;dependencySummary id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-174d"&gt;
  &lt;artifactReference id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-174c"&gt;
    &lt;refId&gt;/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4&lt;/refId&gt;
    &lt;refType&gt;1&lt;/refType&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-174b"&gt;
      &lt;name&gt;externalId&lt;/name&gt;
      &lt;value&gt;http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-174a"&gt;
      &lt;name&gt;mimeType&lt;/name&gt;
      &lt;value&gt;xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
  &lt;/artifactReference&gt;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","complexType":[{"annotation":{"documentation":[{"content":["RequestTemplatePojo"]}],"appinfo":[{"shared":[false],"advancedProperties":[{"namespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","typeName":"RequestTemplatePojo"}],"shadow":[true]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["ecmDocInfo"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","_minOccurs":0,"typeName":"EcmDocumentInformation","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":1}]}]},"name":"ecmDocInfo","type":"{http:\/\/NBEODCR}BrokenReference","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.57fea321-b2d0-479e-93d1-735d7a7ae033"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["generatedDocName"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"string","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":2}]}]},"name":"generatedDocName","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["noOfCopies"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"int","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":3}]}]},"name":"noOfCopies","type":"{http:\/\/lombardi.ibm.com\/schema\/}Integer","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["paramsValues"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"anyType","_nillable":false,"nodeType":1,"_maxOccurs":2147483647,"order":4}]}]},"name":"paramsValues","maxOccurs":"unbounded","type":"{http:\/\/lombardi.ibm.com\/schema\/}ANY","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.c09c9b6e-aabd-4897-bef2-ed61db106297"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["secure"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"boolean","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":5}]}]},"name":"secure","type":"{http:\/\/lombardi.ibm.com\/schema\/}Boolean","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["templateCode"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"string","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":6}]}]},"name":"templateCode","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}]},"name":"RequestTemplatePojo"}],"id":"_12.8cf8193c-ddf5-44ec-8584-1443729deace"}</jsonData>
        <description>RequestTemplatePojo</description>
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-176a</guid>
        <versionId>0367ab96-f04e-4f66-8ff2-a386f18afe43</versionId>
        <definition>
            <property>
                <name>ecmDocInfo</name>
                <description isNull="true" />
                <classRef>/12.57fea321-b2d0-479e-93d1-735d7a7ae033</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>EcmDocumentInformation</typeName>
                    <typeNamespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>1</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>generatedDocName</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>string</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>2</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>noOfCopies</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>int</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>3</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>paramsValues</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                <arrayProperty>true</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>anyType</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>unbounded</maxOccurs>
                    <nillable>false</nillable>
                    <order>4</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>secure</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>boolean</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>5</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>templateCode</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>string</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>6</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="RequestTemplatePojo">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name>RequestTemplatePojo</name>
                <namespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</namespace>
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

