{"id": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "versionId": "6415877a-8b28-461c-8678-da20a8bd7fad", "name": "Create Folder FileNet", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Create Folder FileNet", "lastModified": "1692517102531", "lastModifiedBy": "so<PERSON>ia", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "32bfad83-f0e3-4c6b-a54e-df98ab349f21", "versionId": "6415877a-8b28-461c-8678-da20a8bd7fad", "dependencySummary": "<dependencySummary id=\"bpdid:aa4c986259b1691d:-23a6d209:18a11a6235c:-55a9\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"a3278b3b-72d5-40ad-8f33-59aa3954be74\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":180,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"363595f9-2e1f-41c0-a7ea-6c290347c1b1\"},{\"incoming\":[\"6ffca438-aeea-4957-9031-860407594c35\",\"5b198226-e3ad-461d-9677-43d822ef24a4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":940,\"y\":180,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:fef170a08f25d496:5466e087:189df5f8551:-15c3\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b\"},{\"outgoing\":[\"eb5c74a2-1cfd-4704-b15b-ebf54d8ac408\"],\"incoming\":[\"f4164704-3364-405a-a4cf-cf8dccea0061\",\"ae9a7470-a9c1-45b6-bb27-812131c23770\"],\"matchAllSearchCriteria\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":504,\"y\":157,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[],\"activityType\":[\"ContentTask\"]},\"operationRef\":\"FOLDER_OP_CREATE_FOLDER\",\"implementation\":\"##WebService\",\"serverName\":\"useMappingServer\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask\",\"startQuantity\":1,\"name\":\"Create Folder\",\"dataInputAssociation\":[{\"targetRef\":\"NAME\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.folderName\"]}}]},{\"targetRef\":\"PARENT_FOLDER_ID\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentFolderID\"]}}]},{\"targetRef\":\"PROPERTIES\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\"}}]},{\"targetRef\":\"SERVER_NAME\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"FileNet\\\"\"]}}]},{\"targetRef\":\"OBJECT_TYPE_ID\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"FolderTemp\\\"\"]}}]}],\"isForCompensation\":false,\"useMappedVariable\":true,\"completionQuantity\":1,\"id\":\"f21cb207-a190-41b9-b10d-7d39f96ac7c5\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.folderID\"]}}],\"sourceRef\":[\"FOLDER_ID\"]}],\"orderOverride\":false},{\"startQuantity\":1,\"outgoing\":[\"6ffca438-aeea-4957-9031-860407594c35\"],\"incoming\":[\"eb5c74a2-1cfd-4704-b15b-ebf54d8ac408\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":737,\"y\":157,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Get ID\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"var fID = tw.local.folderID.toString();\\r\\nfID = fID.substring(4);\\r\\ntw.local.folderID = \\\"{\\\" + fID + \\\"}\\\" ;\"]}},{\"targetRef\":\"9ce3886e-02c6-4e5c-84c5-ce4a01f51542\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Parent Folder Exist\",\"declaredType\":\"sequenceFlow\",\"id\":\"a3278b3b-72d5-40ad-8f33-59aa3954be74\",\"sourceRef\":\"363595f9-2e1f-41c0-a7ea-6c290347c1b1\"},{\"targetRef\":\"c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get ID\",\"declaredType\":\"sequenceFlow\",\"id\":\"eb5c74a2-1cfd-4704-b15b-ebf54d8ac408\",\"sourceRef\":\"f21cb207-a190-41b9-b10d-7d39f96ac7c5\"},{\"targetRef\":\"b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"6ffca438-aeea-4957-9031-860407594c35\",\"sourceRef\":\"c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67\"},{\"outgoing\":[\"569b6b1e-f44d-4094-bc5d-f54da7e8bd11\"],\"incoming\":[\"647df52e-aafe-46a3-966b-c43d492d82bb\"],\"matchAllSearchCriteria\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":503,\"y\":279,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[],\"activityType\":[\"ContentTask\"]},\"operationRef\":\"FOLDER_OP_GET_FOLDER_BY_PATH\",\"implementation\":\"##WebService\",\"serverName\":\"useMappingServer\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask\",\"startQuantity\":1,\"name\":\"Get Folder Path\",\"dataInputAssociation\":[{\"targetRef\":\"PATH\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentFolderPath+\\\"\\/\\\"+tw.local.folderName\"]}}]},{\"targetRef\":\"SERVER_NAME\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"FileNet\\\"\"]}}]}],\"isForCompensation\":false,\"useMappedVariable\":true,\"completionQuantity\":1,\"id\":\"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.folder\"]}}],\"sourceRef\":[\"FOLDER\"]}],\"orderOverride\":false},{\"itemSubjectRef\":\"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183\",\"name\":\"folder\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a86b02be-147b-402a-bce5-de80edea8b4a\"},{\"startQuantity\":1,\"outgoing\":[\"5b198226-e3ad-461d-9677-43d822ef24a4\"],\"incoming\":[\"569b6b1e-f44d-4094-bc5d-f54da7e8bd11\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":729,\"y\":279,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Get Folder ID\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"9e3296e0-52ba-45ce-b809-ee2920ccd61a\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"var fID = tw.local.folder.objectId.toString();\\r\\nfID = fID.substring(4);\\r\\ntw.local.folderID = \\\"{\\\" + fID + \\\"}\\\" ;\"]}},{\"targetRef\":\"9e3296e0-52ba-45ce-b809-ee2920ccd61a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Folder ID\",\"declaredType\":\"sequenceFlow\",\"id\":\"569b6b1e-f44d-4094-bc5d-f54da7e8bd11\",\"sourceRef\":\"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec\"},{\"targetRef\":\"b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"5b198226-e3ad-461d-9677-43d822ef24a4\",\"sourceRef\":\"9e3296e0-52ba-45ce-b809-ee2920ccd61a\"},{\"incoming\":[\"fab2c56e-2536-4117-931e-3f1f6a920cf1\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"086ee1d1-050a-421c-9ef0-1fc36b0aef0e\",\"otherAttributes\":{\"eventImplId\":\"0dadf3e3-cca2-4a36-8b95-15d5ddf44c75\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":661,\"y\":374,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error\",\"dataInputAssociation\":[{\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}]}],\"declaredType\":\"endEvent\",\"id\":\"91958c60-6f9d-465a-9bf8-b3f7098d293b\"},{\"outgoing\":[\"806448af-5398-4733-90d4-27ad3548411c\"],\"incoming\":[\"1684a75b-107f-4f05-a39f-8e1cf8969ea9\"],\"matchAllSearchCriteria\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":238,\"y\":157,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"ContentTask\"]},\"operationRef\":\"FOLDER_OP_GET_FOLDER_BY_PATH\",\"implementation\":\"##WebService\",\"serverName\":\"useMappingServer\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask\",\"startQuantity\":1,\"name\":\"Get Parent Folder By Path\",\"dataInputAssociation\":[{\"targetRef\":\"PATH\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentFolderPath\"]}}]},{\"targetRef\":\"SERVER_NAME\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"FileNet\\\"\"]}}]}],\"isForCompensation\":false,\"useMappedVariable\":true,\"completionQuantity\":1,\"id\":\"7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parent\"]}}],\"sourceRef\":[\"FOLDER\"]}],\"orderOverride\":false},{\"targetRef\":\"03092d42-57f8-40ea-8811-5bb47f0f3b4e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set Parent Folder ID\",\"declaredType\":\"sequenceFlow\",\"id\":\"806448af-5398-4733-90d4-27ad3548411c\",\"sourceRef\":\"7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60\"},{\"itemSubjectRef\":\"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183\",\"name\":\"parent\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2f56b88c-c347-4340-af59-ae2e991a8012\"},{\"parallelMultiple\":false,\"outgoing\":[\"647df52e-aafe-46a3-966b-c43d492d82bb\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"9bcf3ccd-cc70-4b0e-8fa6-5ae680069b92\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"cba5ace3-7091-4dac-9cb5-5628f653c133\",\"otherAttributes\":{\"eventImplId\":\"800f7367-f5b4-4eb6-8171-1be5da45ce6d\"}}],\"attachedToRef\":\"f21cb207-a190-41b9-b10d-7d39f96ac7c5\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":539,\"y\":215,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5\",\"outputSet\":{}},{\"targetRef\":\"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Folder Path\",\"declaredType\":\"sequenceFlow\",\"id\":\"647df52e-aafe-46a3-966b-c43d492d82bb\",\"sourceRef\":\"dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5\"},{\"parallelMultiple\":false,\"outgoing\":[\"fab2c56e-2536-4117-931e-3f1f6a920cf1\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"7091cfbb-a0c2-4753-864e-c442982f66ed\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"ca0577d8-9f93-44ec-bb36-97e884c4e1ec\",\"otherAttributes\":{\"eventImplId\":\"738a5e9b-9337-43d6-8f6b-583f20b18a99\"}}],\"attachedToRef\":\"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":538,\"y\":337,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"92d3f8b7-7247-4ead-a7b3-650e8a6ce87b\",\"outputSet\":{}},{\"targetRef\":\"91958c60-6f9d-465a-9bf8-b3f7098d293b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Error\",\"declaredType\":\"sequenceFlow\",\"id\":\"fab2c56e-2536-4117-931e-3f1f6a920cf1\",\"sourceRef\":\"92d3f8b7-7247-4ead-a7b3-650e8a6ce87b\"},{\"outgoing\":[\"1684a75b-107f-4f05-a39f-8e1cf8969ea9\",\"f4164704-3364-405a-a4cf-cf8dccea0061\"],\"incoming\":[\"a3278b3b-72d5-40ad-8f33-59aa3954be74\"],\"default\":\"1684a75b-107f-4f05-a39f-8e1cf8969ea9\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":123,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Parent Folder Exist\",\"declaredType\":\"exclusiveGateway\",\"id\":\"9ce3886e-02c6-4e5c-84c5-ce4a01f51542\"},{\"targetRef\":\"7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentFolderID\\t  ==\\t  \"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"not exist\",\"declaredType\":\"sequenceFlow\",\"id\":\"1684a75b-107f-4f05-a39f-8e1cf8969ea9\",\"sourceRef\":\"9ce3886e-02c6-4e5c-84c5-ce4a01f51542\"},{\"targetRef\":\"f21cb207-a190-41b9-b10d-7d39f96ac7c5\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentFolderID\\t  !=\\t  null\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"does not exist\",\"declaredType\":\"sequenceFlow\",\"id\":\"f4164704-3364-405a-a4cf-cf8dccea0061\",\"sourceRef\":\"9ce3886e-02c6-4e5c-84c5-ce4a01f51542\"},{\"startQuantity\":1,\"outgoing\":[\"ae9a7470-a9c1-45b6-bb27-812131c23770\"],\"incoming\":[\"806448af-5398-4733-90d4-27ad3548411c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":364,\"y\":157,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Parent Folder ID\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"03092d42-57f8-40ea-8811-5bb47f0f3b4e\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.parentFolderID= tw.local.parent.objectId;\"]}},{\"targetRef\":\"f21cb207-a190-41b9-b10d-7d39f96ac7c5\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Create Folder\",\"declaredType\":\"sequenceFlow\",\"id\":\"ae9a7470-a9c1-45b6-bb27-812131c23770\",\"sourceRef\":\"03092d42-57f8-40ea-8811-5bb47f0f3b4e\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2a8d058c-2114-451f-8b41-9ee1afc0f5eb\"}],\"laneSet\":[{\"id\":\"1c395cd6-4af0-4d2b-9068-ae13a29eba7f\",\"lane\":[{\"flowNodeRef\":[\"363595f9-2e1f-41c0-a7ea-6c290347c1b1\",\"b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b\",\"f21cb207-a190-41b9-b10d-7d39f96ac7c5\",\"c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67\",\"91958c60-6f9d-465a-9bf8-b3f7098d293b\",\"2d599c7d-5ed0-4edb-a4e6-b527a56b8fec\",\"9e3296e0-52ba-45ce-b809-ee2920ccd61a\",\"7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60\",\"dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5\",\"92d3f8b7-7247-4ead-a7b3-650e8a6ce87b\",\"9ce3886e-02c6-4e5c-84c5-ce4a01f51542\",\"03092d42-57f8-40ea-8811-5bb47f0f3b4e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"5bbe3ea9-b8cb-43be-9892-860764be0829\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Create Folder FileNet\",\"declaredType\":\"process\",\"id\":\"1.8a1c5f67-55b2-4785-97cb-9ec3e4441186\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"name\":\"folderID\",\"isCollection\":false,\"id\":\"2055.21385f08-1be8-41a4-b758-c8dc9b8bb509\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e\",\"2055.98c175d8-7e63-4e2d-ac3f-************\",\"2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.21385f08-1be8-41a4-b758-c8dc9b8bb509\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentFolderPath\",\"isCollection\":false,\"id\":\"2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"folderName\",\"isCollection\":false,\"id\":\"2055.98c175d8-7e63-4e2d-ac3f-************\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\"}]},\"itemSubjectRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"name\":\"parentFolderID\",\"isCollection\":false,\"id\":\"2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "parentFolderPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "14fa973f-4230-4f92-9d6d-72a7e66a3530", "versionId": "6e0cb88c-d167-4ff5-9d52-ef310a5d1d3c"}, {"name": "folderName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.98c175d8-7e63-4e2d-ac3f-************", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6829457d-35bc-4556-9ad7-6c8679a05229", "versionId": "7dde878f-4fd0-472b-8d8c-3d6143a6771b"}, {"name": "parentFolderID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "1", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f6b27258-5475-4699-acd3-7d61c5ca5726", "versionId": "77088655-fe63-4e83-b442-31c0c42042d2"}, {"name": "folderID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.21385f08-1be8-41a4-b758-c8dc9b8bb509", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "2", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "30c66d73-f8d3-464f-b559-3675d08e46e7", "versionId": "1b601800-ed3f-469e-9d97-a2e3e2a7ce65"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e37e2691-8697-4ec5-8371-2cc7c3a976ea", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "3", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "56", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "5ae2e703-0434-461e-8a7e-2c2cf0d3e700", "versionId": "df0df81d-ce66-497a-94ae-d44d402332ee"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.caee7282-11cb-478f-96e5-4cf6ab11acc6", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "122", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "0a320314-8b7c-4955-b17f-f21c608dbb05", "versionId": "ae5d319d-66f4-402a-a90d-92991d30039f"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.94074e6b-b956-4746-b409-e66e410cf818", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "123", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "eaddbefa-08c5-4da7-8173-2b2fa5b0fea1", "versionId": "ccc9ef05-acf3-4785-88e5-9e29ee94e818"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d61d9230-7663-4bb4-858c-7467edbece0c", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "124", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "6efb6b3c-a658-4187-9f0b-98dcdb3bbe4e", "versionId": "5ed1d054-be62-4339-a16b-baa0713f3b0f"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.9faa5b20-b8b4-4cce-997a-a4bd6de08a61", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "125", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "3b9bc890-35a7-421b-8fa9-96639c2998d9", "versionId": "c790592c-1b97-4bce-ae18-fc8b17884855"}], "processVariable": [{"name": "folder", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a86b02be-147b-402a-bce5-de80edea8b4a", "description": {"isNull": "true"}, "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "371ff49f-fef8-46a7-8ca4-09272fd63b24", "versionId": "90db085e-301f-4ce3-982e-282f3e2b5840"}, {"name": "parent", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2f56b88c-c347-4340-af59-ae2e991a8012", "description": {"isNull": "true"}, "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "60bc553b-aea3-417e-a97c-8e81aa770005", "versionId": "f4bc0ef0-184b-41b5-ad19-3b91d44575f6"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2a8d058c-2114-451f-8b41-9ee1afc0f5eb", "description": {"isNull": "true"}, "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bb1023ab-39ff-4537-a4b5-126aa0aaa084", "versionId": "5c62f03a-115a-40d0-b077-f442358590d9"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "<PERSON><PERSON>er Exist", "tWComponentName": "Switch", "tWComponentId": "3013.6ace1fdc-afcb-47bb-936d-f0a3ead13033", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15be", "versionId": "09ebe71d-bf53-42e5-807a-983240c22f5d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "123", "y": "176", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.6ace1fdc-afcb-47bb-936d-f0a3ead13033", "guid": "a6bf16c1-1d97-4309-a1e8-0d58fbdf867e", "versionId": "883ffd22-7f8c-426b-8981-f553ef23f763", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.b4da1864-cf7f-41d8-aed9-954e1ec88c94", "switchId": "3013.6ace1fdc-afcb-47bb-936d-f0a3ead13033", "seq": "1", "endStateId": "guid:aa4c986259b1691d:-23a6d209:18a11a6235c:-55aa", "condition": "tw.local.parentFolderID\t  !=\t  null", "guid": "66e97bae-7ea7-4e2a-ba30-c6ec1c244368", "versionId": "a237abf5-e987-4342-8f24-d8c8dc77ac62"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Get ID", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.0e00adad-e3df-4abf-87a0-02f3fa7186eb", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c0", "versionId": "1832621f-5953-46b1-b56d-a5a23e8aad1c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "737", "y": "157", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.0e00adad-e3df-4abf-87a0-02f3fa7186eb", "scriptTypeId": "2", "isActive": "true", "script": "var fID = tw.local.folderID.toString();\r\r\nfID = fID.substring(4);\r\r\ntw.local.folderID = \"{\" + fID + \"}\" ;", "isRule": "false", "guid": "319d0fc3-8ca0-4709-b28b-c6ace78fea4a", "versionId": "a57fc192-5c4d-47b3-a718-667ea9921c37"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Set Parent Folder ID", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.f1131d45-0f8e-4664-86ca-84c2757d88b0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c6", "versionId": "1b78d474-f83e-4244-bd26-943e9356ab70", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "364", "y": "157", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.f1131d45-0f8e-4664-86ca-84c2757d88b0", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.parentFolderID= tw.local.parent.objectId;", "isRule": "false", "guid": "4cd1263c-95a7-4052-b557-998fffd1c3a3", "versionId": "*************-4603-8d8b-85b05a7fb7af"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Get Folder Path", "tWComponentName": "ECMConnector", "tWComponentId": "3030.b53173c5-0d8c-443d-9841-91007a43a41d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.91958c60-6f9d-465a-9bf8-b3f7098d293b", "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c5", "versionId": "38266acc-9c4c-460d-bf37-288e53b86795", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.ade002f3-9d73-4adc-a67e-6dee21933fdf", "processItemId": "2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "location": "1", "script": {"isNull": "true"}, "guid": "24625521-5ed5-4ffe-a45f-0dcebb0573e0", "versionId": "26ce713c-3759-48eb-9be4-30a480c84b37"}, "layoutData": {"x": "503", "y": "279", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c2", "errorHandlerItemId": "2025.91958c60-6f9d-465a-9bf8-b3f7098d293b", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "ecmConnectorId": "3030.b53173c5-0d8c-443d-9841-91007a43a41d", "definition": "<config type=\"com.lombardisoftware.client.persistence.ECMConnectorConfiguration\">\r\r\n  <inputParameters>\r\r\n    <parameter>\r\r\n      <name>path</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.parentFolderPath+\"/\"+tw.local.folderName</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>serverName</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>\"FileNet\"</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </inputParameters>\r\r\n  <outputParameters>\r\r\n    <parameter>\r\r\n      <name>folder</name>\r\r\n      <type>ECMFolder</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.folder</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </outputParameters>\r\r\n  <matchAllSearchCriteria>true</matchAllSearchCriteria>\r\r\n  <searchParameters />\r\r\n  <selectParameters />\r\r\n  <orderOverride>false</orderOverride>\r\r\n  <orderOverrideValue></orderOverrideValue>\r\r\n  <operationType>FOLDER_OP_GET_FOLDER_BY_PATH</operationType>\r\r\n  <server>useMappingServer</server>\r\r\n  <objectTypeSelection>DOCUMENT</objectTypeSelection>\r\r\n  <useMappedVariable>true</useMappedVariable>\r\r\n  <faultParameterId>2055.9faa5b20-b8b4-4cce-997a-a4bd6de08a61</faultParameterId>\r\r\n</config>", "guid": "91806f92-0abd-40e4-bff5-0343dc27702e", "versionId": "81d60e21-f492-43f5-aa5e-355dc1f74ec1"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Create Folder", "tWComponentName": "ECMConnector", "tWComponentId": "3030.0f289cae-63c5-4cfe-a4dd-fc0724489ee3", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15bf", "versionId": "535520fe-c337-434a-8092-c6f47977132a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.d511c3f0-784c-45a1-bb94-a75bcb0f90e4", "processItemId": "2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5", "location": "1", "script": {"isNull": "true"}, "guid": "0faba6db-0ca8-4076-b5d8-e170f8626c5b", "versionId": "f455d1d7-1283-4dde-a41a-5024d6686c76"}, "layoutData": {"x": "504", "y": "157", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c5", "errorHandlerItemId": "2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "ecmConnectorId": "3030.0f289cae-63c5-4cfe-a4dd-fc0724489ee3", "definition": "<config type=\"com.lombardisoftware.client.persistence.ECMConnectorConfiguration\">\r\r\n  <inputParameters>\r\r\n    <parameter>\r\r\n      <name>objectTypeId</name>\r\r\n      <type>ECMID</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>\"FolderTemp\"</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>parentFolderId</name>\r\r\n      <type>ECMID</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.parentFolderID</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>name</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.folderName</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>properties</name>\r\r\n      <type>ECMProperty</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable></argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>true</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>serverName</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>\"FileNet\"</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </inputParameters>\r\r\n  <outputParameters>\r\r\n    <parameter>\r\r\n      <name>folderId</name>\r\r\n      <type>ECMID</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.folderID</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </outputParameters>\r\r\n  <matchAllSearchCriteria>true</matchAllSearchCriteria>\r\r\n  <searchParameters />\r\r\n  <selectParameters />\r\r\n  <orderOverride>false</orderOverride>\r\r\n  <orderOverrideValue></orderOverrideValue>\r\r\n  <operationType>FOLDER_OP_CREATE_FOLDER</operationType>\r\r\n  <server>useMappingServer</server>\r\r\n  <objectTypeSelection>DOCUMENT</objectTypeSelection>\r\r\n  <useMappedVariable>true</useMappedVariable>\r\r\n  <faultParameterId>2055.d61d9230-7663-4bb4-858c-7467edbece0c</faultParameterId>\r\r\n</config>", "guid": "b92eaa89-c8e6-405e-85d2-ccf4751c4f2e", "versionId": "55e8707b-fac8-42ae-af6c-823f277646ec"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Get Parent Folder By Path", "tWComponentName": "ECMConnector", "tWComponentId": "3030.b77e0b47-c650-45dc-8d6f-6dca6c78f1b2", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c1", "versionId": "64f49358-7495-4a1e-955b-8bcbcdd024e5", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "238", "y": "157", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "ecmConnectorId": "3030.b77e0b47-c650-45dc-8d6f-6dca6c78f1b2", "definition": "<config type=\"com.lombardisoftware.client.persistence.ECMConnectorConfiguration\">\r\r\n  <inputParameters>\r\r\n    <parameter>\r\r\n      <name>path</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.parentFolderPath</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>serverName</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>\"FileNet\"</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </inputParameters>\r\r\n  <outputParameters>\r\r\n    <parameter>\r\r\n      <name>folder</name>\r\r\n      <type>ECMFolder</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.parent</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </outputParameters>\r\r\n  <matchAllSearchCriteria>true</matchAllSearchCriteria>\r\r\n  <searchParameters />\r\r\n  <selectParameters />\r\r\n  <orderOverride>false</orderOverride>\r\r\n  <orderOverrideValue></orderOverrideValue>\r\r\n  <operationType>FOLDER_OP_GET_FOLDER_BY_PATH</operationType>\r\r\n  <server>useMappingServer</server>\r\r\n  <objectTypeSelection>DOCUMENT</objectTypeSelection>\r\r\n  <useMappedVariable>true</useMappedVariable>\r\r\n  <faultParameterId>2055.94074e6b-b956-4746-b409-e66e410cf818</faultParameterId>\r\r\n</config>", "guid": "df5a9f15-ec0e-4c92-ac9b-3da413854624", "versionId": "68b9e9c7-1f34-4397-a232-141f57a3f07e"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.91958c60-6f9d-465a-9bf8-b3f7098d293b", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Error", "tWComponentName": "Exception", "tWComponentId": "3007.28f7282a-889f-4db3-8f1d-decc5e3f3ee1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c2", "versionId": "6a5de735-72e2-4de8-8e43-83fc360a2380", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "661", "y": "374", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.28f7282a-889f-4db3-8f1d-decc5e3f3ee1", "message": "", "faultStyle": "1", "guid": "7fe3ac2f-8aab-4982-b57b-f2ad360cc05e", "versionId": "b08bd756-ac1d-441f-9b4c-4d1104d2f19d", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5a22d340-a620-47ec-b4ac-705eec869255", "processParameterId": "2055.e37e2691-8697-4ec5-8371-2cc7c3a976ea", "parameterMappingParentId": "3007.28f7282a-889f-4db3-8f1d-decc5e3f3ee1", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "cdb6d660-ddea-4380-bd73-26ba1af2c5fc", "versionId": "be75c203-44e0-4441-9021-838fe6c0169d", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.11d60de9-899d-4f97-9c40-414a901739e9", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c3", "versionId": "9dc83b75-a30d-4436-bb28-6dc4d43fbaf6", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "940", "y": "180", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.11d60de9-899d-4f97-9c40-414a901739e9", "haltProcess": "false", "guid": "b1392e5f-b1a3-4543-9516-eb123a37fbd9", "versionId": "be5ce4d4-fd52-459e-b6e3-8cc021424e79"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "name": "Get Folder ID", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.e8cf361d-48b9-457c-aa62-c4da22478c0b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c4", "versionId": "a216f418-7567-4703-8a38-41c8fd089186", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "729", "y": "279", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.e8cf361d-48b9-457c-aa62-c4da22478c0b", "scriptTypeId": "2", "isActive": "true", "script": "var fID = tw.local.folder.objectId.toString();\r\r\nfID = fID.substring(4);\r\r\ntw.local.folderID = \"{\" + fID + \"}\" ;", "isRule": "false", "guid": "931dfef3-c427-4b06-8dea-ec5080310fcd", "versionId": "38ab8292-e8f2-4fd7-9b9b-e83980b7b1cf"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "180", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Create Folder FileNet", "id": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isAjaxExposed": "true", "ns3:isSecured": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "parentFolderPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, {"name": "folderName", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.98c175d8-7e63-4e2d-ac3f-************", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, {"name": "parentFolderID", "itemSubjectRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01", "isCollection": "false", "id": "2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}], "ns16:dataOutput": {"name": "folderID", "itemSubjectRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01", "isCollection": "false", "id": "2055.21385f08-1be8-41a4-b758-c8dc9b8bb509"}, "ns16:inputSet": {"ns16:dataInputRefs": ["2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e", "2055.98c175d8-7e63-4e2d-ac3f-************", "2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63"]}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.21385f08-1be8-41a4-b758-c8dc9b8bb509"}}, "ns16:laneSet": {"id": "1c395cd6-4af0-4d2b-9068-ae13a29eba7f", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "5bbe3ea9-b8cb-43be-9892-860764be0829", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["363595f9-2e1f-41c0-a7ea-6c290347c1b1", "b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b", "f21cb207-a190-41b9-b10d-7d39f96ac7c5", "c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67", "91958c60-6f9d-465a-9bf8-b3f7098d293b", "2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "9e3296e0-52ba-45ce-b809-ee2920ccd61a", "7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60", "dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5", "92d3f8b7-7247-4ead-a7b3-650e8a6ce87b", "9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "03092d42-57f8-40ea-8811-5bb47f0f3b4e"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "363595f9-2e1f-41c0-a7ea-6c290347c1b1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "180", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "a3278b3b-72d5-40ad-8f33-59aa3954be74"}, "ns16:endEvent": [{"name": "End", "id": "b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "940", "y": "180", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:fef170a08f25d496:5466e087:189df5f8551:-15c3"}, "ns16:incoming": ["6ffca438-aeea-4957-9031-860407594c35", "5b198226-e3ad-461d-9677-43d822ef24a4"]}, {"name": "Error", "id": "91958c60-6f9d-465a-9bf8-b3f7098d293b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "661", "y": "374", "width": "24", "height": "24"}}, "ns16:incoming": "fab2c56e-2536-4117-931e-3f1f6a920cf1", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:errorEventDefinition": {"id": "086ee1d1-050a-421c-9ef0-1fc36b0aef0e", "eventImplId": "0dadf3e3-cca2-4a36-8b95-15d5ddf44c75", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns4:contentTask": [{"serverName": "useMappingServer", "operationRef": "FOLDER_OP_CREATE_FOLDER", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Create Folder", "id": "f21cb207-a190-41b9-b10d-7d39f96ac7c5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "504", "y": "157", "width": "95", "height": "70"}, "ns4:activityType": "ContentTask", "ns3:preAssignmentScript": ""}, "ns16:incoming": ["f4164704-3364-405a-a4cf-cf8dccea0061", "ae9a7470-a9c1-45b6-bb27-812131c23770"], "ns16:outgoing": "eb5c74a2-1cfd-4704-b15b-ebf54d8ac408", "ns16:dataInputAssociation": [{"ns16:targetRef": "NAME", "ns16:assignment": {"ns16:from": {"_": "tw.local.folderName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "PARENT_FOLDER_ID", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentFolderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:targetRef": "PROPERTIES", "ns16:assignment": {"ns16:from": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "SERVER_NAME", "ns16:assignment": {"ns16:from": {"_": "\"FileNet\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "OBJECT_TYPE_ID", "ns16:assignment": {"ns16:from": {"_": "\"FolderTemp\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "FOLDER_ID", "ns16:assignment": {"ns16:to": {"_": "tw.local.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}}, {"serverName": "useMappingServer", "operationRef": "FOLDER_OP_GET_FOLDER_BY_PATH", "name": "Get Folder Path", "id": "2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "503", "y": "279", "width": "95", "height": "70"}, "ns4:activityType": "ContentTask", "ns3:preAssignmentScript": ""}, "ns16:incoming": "647df52e-aafe-46a3-966b-c43d492d82bb", "ns16:outgoing": "569b6b1e-f44d-4094-bc5d-f54da7e8bd11", "ns16:dataInputAssociation": [{"ns16:targetRef": "PATH", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentFolderPath+\"/\"+tw.local.folderName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "SERVER_NAME", "ns16:assignment": {"ns16:from": {"_": "\"FileNet\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "FOLDER", "ns16:assignment": {"ns16:to": {"_": "tw.local.folder", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183"}}}}, {"serverName": "useMappingServer", "operationRef": "FOLDER_OP_GET_FOLDER_BY_PATH", "name": "Get Parent Folder By Path", "id": "7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "238", "y": "157", "width": "95", "height": "70"}, "ns4:activityType": "ContentTask"}, "ns16:incoming": "1684a75b-107f-4f05-a39f-8e1cf8969ea9", "ns16:outgoing": "806448af-5398-4733-90d4-27ad3548411c", "ns16:dataInputAssociation": [{"ns16:targetRef": "PATH", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentFolderPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "SERVER_NAME", "ns16:assignment": {"ns16:from": {"_": "\"FileNet\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "FOLDER", "ns16:assignment": {"ns16:to": {"_": "tw.local.parent", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183"}}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Get ID", "id": "c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "737", "y": "157", "width": "95", "height": "70"}}, "ns16:incoming": "eb5c74a2-1cfd-4704-b15b-ebf54d8ac408", "ns16:outgoing": "6ffca438-aeea-4957-9031-860407594c35", "ns16:script": "var fID = tw.local.folderID.toString();\r\r\nfID = fID.substring(4);\r\r\ntw.local.folderID = \"{\" + fID + \"}\" ;"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Get Folder ID", "id": "9e3296e0-52ba-45ce-b809-ee2920ccd61a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "729", "y": "279", "width": "95", "height": "70"}}, "ns16:incoming": "569b6b1e-f44d-4094-bc5d-f54da7e8bd11", "ns16:outgoing": "5b198226-e3ad-461d-9677-43d822ef24a4", "ns16:script": "var fID = tw.local.folder.objectId.toString();\r\r\nfID = fID.substring(4);\r\r\ntw.local.folderID = \"{\" + fID + \"}\" ;"}, {"scriptFormat": "text/x-javascript", "name": "Set Parent Folder ID", "id": "03092d42-57f8-40ea-8811-5bb47f0f3b4e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "364", "y": "157", "width": "95", "height": "70"}}, "ns16:incoming": "806448af-5398-4733-90d4-27ad3548411c", "ns16:outgoing": "ae9a7470-a9c1-45b6-bb27-812131c23770", "ns16:script": "tw.local.parentFolderID= tw.local.parent.objectId;"}], "ns16:sequenceFlow": [{"sourceRef": "363595f9-2e1f-41c0-a7ea-6c290347c1b1", "targetRef": "9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "name": "To Parent Folder Exist", "id": "a3278b3b-72d5-40ad-8f33-59aa3954be74", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "f21cb207-a190-41b9-b10d-7d39f96ac7c5", "targetRef": "c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67", "name": "To Get ID", "id": "eb5c74a2-1cfd-4704-b15b-ebf54d8ac408", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67", "targetRef": "b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b", "name": "To End", "id": "6ffca438-aeea-4957-9031-860407594c35", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "targetRef": "9e3296e0-52ba-45ce-b809-ee2920ccd61a", "name": "To Get Folder ID", "id": "569b6b1e-f44d-4094-bc5d-f54da7e8bd11", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "9e3296e0-52ba-45ce-b809-ee2920ccd61a", "targetRef": "b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b", "name": "To End", "id": "5b198226-e3ad-461d-9677-43d822ef24a4", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60", "targetRef": "03092d42-57f8-40ea-8811-5bb47f0f3b4e", "name": "To Set Parent Folder ID", "id": "806448af-5398-4733-90d4-27ad3548411c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5", "targetRef": "2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "name": "To Get Folder Path", "id": "647df52e-aafe-46a3-966b-c43d492d82bb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "92d3f8b7-7247-4ead-a7b3-650e8a6ce87b", "targetRef": "91958c60-6f9d-465a-9bf8-b3f7098d293b", "name": "To Error", "id": "fab2c56e-2536-4117-931e-3f1f6a920cf1", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "targetRef": "7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60", "name": "not exist", "id": "1684a75b-107f-4f05-a39f-8e1cf8969ea9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.parentFolderID\t  ==\t  ", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "targetRef": "f21cb207-a190-41b9-b10d-7d39f96ac7c5", "name": "does not exist", "id": "f4164704-3364-405a-a4cf-cf8dccea0061", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.parentFolderID\t  !=\t  null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "03092d42-57f8-40ea-8811-5bb47f0f3b4e", "targetRef": "f21cb207-a190-41b9-b10d-7d39f96ac7c5", "name": "To Create Folder", "id": "ae9a7470-a9c1-45b6-bb27-812131c23770", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183", "isCollection": "false", "name": "folder", "id": "2056.a86b02be-147b-402a-bce5-de80edea8b4a"}, {"itemSubjectRef": "itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183", "isCollection": "false", "name": "parent", "id": "2056.2f56b88c-c347-4340-af59-ae2e991a8012"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.2a8d058c-2114-451f-8b41-9ee1afc0f5eb"}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "f21cb207-a190-41b9-b10d-7d39f96ac7c5", "parallelMultiple": "false", "name": "Error", "id": "dd0a61ba-057f-4ea5-8cc9-a64d30ce5bc5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "539", "y": "215", "width": "24", "height": "24"}}, "ns16:outgoing": "647df52e-aafe-46a3-966b-c43d492d82bb", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "9bcf3ccd-cc70-4b0e-8fa6-5ae680069b92"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "cba5ace3-7091-4dac-9cb5-5628f653c133", "eventImplId": "800f7367-f5b4-4eb6-8171-1be5da45ce6d", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "parallelMultiple": "false", "name": "Error1", "id": "92d3f8b7-7247-4ead-a7b3-650e8a6ce87b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "538", "y": "337", "width": "24", "height": "24"}}, "ns16:outgoing": "fab2c56e-2536-4117-931e-3f1f6a920cf1", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "7091cfbb-a0c2-4753-864e-c442982f66ed"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "ca0577d8-9f93-44ec-bb36-97e884c4e1ec", "eventImplId": "738a5e9b-9337-43d6-8f6b-583f20b18a99", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}], "ns16:exclusiveGateway": {"default": "1684a75b-107f-4f05-a39f-8e1cf8969ea9", "name": "<PERSON><PERSON>er Exist", "id": "9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "123", "y": "176", "width": "32", "height": "32"}}, "ns16:incoming": "a3278b3b-72d5-40ad-8f33-59aa3954be74", "ns16:outgoing": ["1684a75b-107f-4f05-a39f-8e1cf8969ea9", "f4164704-3364-405a-a4cf-cf8dccea0061"]}}}}, "link": [{"name": "To Get ID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.eb5c74a2-1cfd-4704-b15b-ebf54d8ac408", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5", "2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5"], "endStateId": "Out", "toProcessItemId": ["2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67", "2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67"], "guid": "f9c5761a-c02f-4e90-9d8f-beda95826128", "versionId": "0ca16998-4748-41b8-b356-d46e42cd2dc4", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Set Parent Folder ID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.806448af-5398-4733-90d4-27ad3548411c", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60", "2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60"], "endStateId": "Out", "toProcessItemId": ["2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e", "2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e"], "guid": "2447edf9-0f72-46b6-a306-86472010962f", "versionId": "42a3eeec-f63d-4468-b50b-bdce7ea1b51d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.5b198226-e3ad-461d-9677-43d822ef24a4", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a", "2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a"], "endStateId": "Out", "toProcessItemId": ["2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b", "2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b"], "guid": "709c376a-c252-4f8b-9a39-e172c717eb93", "versionId": "59e92cad-23d9-4ba2-adbc-d4300368dead", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "does not exist", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f4164704-3364-405a-a4cf-cf8dccea0061", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542"], "endStateId": "guid:aa4c986259b1691d:-23a6d209:18a11a6235c:-55aa", "toProcessItemId": ["2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5", "2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5"], "guid": "6411c176-25e6-4f49-9671-c6aa64f90396", "versionId": "6e560c23-4b61-4601-9947-99695a6cc292", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To Get Folder ID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.569b6b1e-f44d-4094-bc5d-f54da7e8bd11", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec", "2025.2d599c7d-5ed0-4edb-a4e6-b527a56b8fec"], "endStateId": "Out", "toProcessItemId": ["2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a", "2025.9e3296e0-52ba-45ce-b809-ee2920ccd61a"], "guid": "f68aabc9-3d56-43ad-bb81-27f983a50c99", "versionId": "9c843449-8afd-492d-aead-dc7f817d3134", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6ffca438-aeea-4957-9031-860407594c35", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67", "2025.c2f4b8e0-7e25-4a0c-8c6d-d629c8361f67"], "endStateId": "Out", "toProcessItemId": ["2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b", "2025.b6b07089-5ab8-4f51-80bd-b9e2ca4d2c9b"], "guid": "a5356230-3eac-4a14-aa6c-63d825bcb440", "versionId": "ba75f181-79f1-4288-874f-dded92e46fa8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Create Folder", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ae9a7470-a9c1-45b6-bb27-812131c23770", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e", "2025.03092d42-57f8-40ea-8811-5bb47f0f3b4e"], "endStateId": "Out", "toProcessItemId": ["2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5", "2025.f21cb207-a190-41b9-b10d-7d39f96ac7c5"], "guid": "e9b24b9e-5727-4dfc-92ae-0536cf5b0493", "versionId": "bb3f3598-ae93-4ef4-95b6-6ac3488419d7", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "not exist", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1684a75b-107f-4f05-a39f-8e1cf8969ea9", "processId": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542", "2025.9ce3886e-02c6-4e5c-84c5-ce4a01f51542"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60", "2025.7f1719e7-0e5f-4ada-a9fe-d5bc22d61c60"], "guid": "3a239312-72b7-4b87-bd1f-c177d22b02aa", "versionId": "d6bf8d4b-e043-4792-a8fb-7aab6cfc4b5e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}