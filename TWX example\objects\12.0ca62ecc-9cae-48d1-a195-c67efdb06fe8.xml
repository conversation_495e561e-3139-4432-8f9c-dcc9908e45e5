<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8" name="ResponseTemplatePojo">
        <lastModified>1693480732491</lastModified>
        <lastModifiedBy>abdelrahman.saleh</lastModifiedBy>
        <classId>12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>true</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8</externalId>
        <dependencySummary>&lt;dependencySummary id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1736"&gt;
  &lt;artifactReference id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1735"&gt;
    &lt;refId&gt;/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4&lt;/refId&gt;
    &lt;refType&gt;1&lt;/refType&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1734"&gt;
      &lt;name&gt;externalId&lt;/name&gt;
      &lt;value&gt;http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1733"&gt;
      &lt;name&gt;mimeType&lt;/name&gt;
      &lt;value&gt;xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
  &lt;/artifactReference&gt;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","complexType":[{"annotation":{"documentation":[{"content":["ResponseTemplatePojo"]}],"appinfo":[{"shared":[false],"advancedProperties":[{"namespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","typeName":"ResponseTemplatePojo"}],"shadow":[true]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["docBase64"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"string","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":1}]}]},"name":"docBase64","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["ecmDocName"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"string","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":2}]}]},"name":"ecmDocName","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["error"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","_minOccurs":0,"typeName":"ErrorPojo","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":3}]}]},"name":"error","type":"{http:\/\/NBEODCR}BrokenReference","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.9ad7c20d-998e-4629-84b9-f365c85a6733"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["status"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"boolean","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":4}]}]},"name":"status","type":"{http:\/\/lombardi.ibm.com\/schema\/}Boolean","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["templateCode"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"string","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":5}]}]},"name":"templateCode","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}]},"name":"ResponseTemplatePojo"}],"id":"_12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8"}</jsonData>
        <description>ResponseTemplatePojo</description>
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1765</guid>
        <versionId>f0053244-cb35-4537-a419-e0e17f8ad5f9</versionId>
        <definition>
            <property>
                <name>docBase64</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>string</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>1</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>ecmDocName</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>string</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>2</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>error</name>
                <description isNull="true" />
                <classRef>/12.9ad7c20d-998e-4629-84b9-f365c85a6733</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>ErrorPojo</typeName>
                    <typeNamespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>3</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>status</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>boolean</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>4</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>templateCode</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>string</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>5</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="ResponseTemplatePojo">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name>ResponseTemplatePojo</name>
                <namespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</namespace>
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

