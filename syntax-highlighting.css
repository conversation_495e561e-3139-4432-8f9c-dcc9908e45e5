/* Shared JavaScript Syntax Highlighting Styles */

/* Code block container */
.script-content {
    margin-top: 12px;
    border-top: 1px solid #e9ecef;
    padding-top: 12px;
}

.script-content strong {
    color: #495057;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    display: block;
    margin-bottom: 8px;
}

.script-content pre {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    border: 1px solid #333;
    max-height: 400px;
    overflow-y: auto;
}

.script-content code {
    background: none;
    padding: 0;
    border: none;
    color: inherit;
    font-family: inherit;
}

/* JavaScript syntax highlighting colors */
.js-keyword {
    color: #569cd6;
    font-weight: bold;
}

.js-string {
    color: #ce9178;
}

.js-comment {
    color: #6a9955;
    font-style: italic;
}

.js-number {
    color: #b5cea8;
}

.js-function {
    color: #dcdcaa;
}

.js-property {
    color: #9cdcfe;
}

/* Collapsible sections */
.collapsible-section {
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.collapsible-header {
    background: #f8f9fa;
    padding: 12px 15px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.2s ease;
    user-select: none;
}

.collapsible-header:hover {
    background: #e9ecef;
}

.collapsible-title {
    font-weight: 600;
    color: #495057;
    display: flex;
    align-items: center;
    gap: 8px;
}

.collapsible-title::before {
    content: '📁';
    font-size: 1em;
}

.collapsible-toggle {
    font-size: 14px;
    color: #6c757d;
    transition: transform 0.2s ease;
}

.collapsible-section.expanded .collapsible-toggle {
    transform: rotate(90deg);
}

.collapsible-content {
    padding: 15px;
    background: white;
    display: none;
}

.collapsible-section.expanded .collapsible-content {
    display: block;
}

/* Expandable elements */
.expandable-element {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 10px;
    overflow: hidden;
}

.element-header {
    background: #f8f9fa;
    padding: 10px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s ease;
    user-select: none;
}

.element-header:hover {
    background: #e9ecef;
}

.element-name {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
    flex: 1;
}

.element-toggle {
    font-size: 12px;
    color: #6c757d;
    transition: transform 0.2s ease;
}

.expandable-element.expanded .element-toggle {
    transform: rotate(90deg);
}

.element-content {
    padding: 12px;
    background: white;
    display: none;
    border-top: 1px solid #dee2e6;
}

.expandable-element.expanded .element-content {
    display: block;
}

/* Script indicators */
.script-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    font-size: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-left: 4px;
}

.script-indicator.pre-script {
    background: #28a745;
}

.script-indicator.post-script {
    background: #dc3545;
}

.script-indicator.main-script {
    background: #007bff;
}

/* Inline expansion styles */
.cshs-inline-details {
    margin-top: 10px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.cshs-inline-details .collapsible-section {
    margin-bottom: 10px;
}

.cshs-inline-details .collapsible-section:last-child {
    margin-bottom: 0;
}

/* Variable items in collapsible sections */
.variable-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.variable-item:last-child {
    margin-bottom: 0;
}

.variable-name {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.variable-default {
    display: flex;
    align-items: center;
}

.variable-default label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85em;
    color: #6c757d;
    cursor: default;
}

.variable-default input[type="checkbox"] {
    margin: 0;
}

/* No items message */
.no-items {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background: white;
    border: 1px dashed #dee2e6;
    border-radius: 6px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .script-content pre {
        font-size: 12px;
        padding: 12px;
    }
    
    .collapsible-header,
    .element-header {
        padding: 10px 12px;
    }
    
    .collapsible-content,
    .element-content {
        padding: 12px;
    }
}
