<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.8e583b1e-1719-4e19-a6ce-6f41202527d4" name="Generate BPM Request Number">
        <lastModified>1699439358899</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.17c8f710-6673-4337-968e-a1dd3169362e</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>c3d8ccaf-5511-491e-a664-24eb512744be</guid>
        <versionId>726a60e9-20d1-464f-be10-cac0f6e5b358</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:cde91268c0d1a44e:-10e36a59:18badede6df:-41ca" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.454d406a-6436-46ba-90a5-27b28cc4013f"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b100e10a-aa95-4baa-aaf5-aa4a42c3b2aa"},{"incoming":["9fbedd09-94ac-4ce9-80b1-e1f712df61bc","24ab6713-7de4-4595-8910-e5e0ec1a4271"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:-1572"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd"},{"targetRef":"17c8f710-6673-4337-968e-a1dd3169362e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Query","declaredType":"sequenceFlow","id":"2027.454d406a-6436-46ba-90a5-27b28cc4013f","sourceRef":"b100e10a-aa95-4baa-aaf5-aa4a42c3b2aa"},{"startQuantity":1,"outgoing":["d1523a1e-c532-4298-a365-de8a9958bdaa"],"incoming":["921d6883-b423-41d4-9326-5df367bd7b30"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":238,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.ec350cc0-a909-411a-b0c2-96e08b779c85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_APP"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"sequence\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"3b2a27dd-bb0d-42df-aff0-699cf0e3c861","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["tw.local.sequence"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"b830c26b-c414-4848-a011-f032c4b7dd02","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set BPM Request Number","declaredType":"sequenceFlow","id":"d1523a1e-c532-4298-a365-de8a9958bdaa","sourceRef":"3b2a27dd-bb0d-42df-aff0-699cf0e3c861"},{"itemSubjectRef":"itm.12.8566ac97-e9e5-481e-9605-42b0dc11da47","name":"sequence","isCollection":true,"declaredType":"dataObject","id":"2056.53ceb906-6fc9-456e-a2db-069fb6b4de84"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.75dbb317-9def-4aa0-9e3e-dd9226bc2f78"},{"startQuantity":1,"outgoing":["921d6883-b423-41d4-9326-5df367bd7b30"],"incoming":["2027.454d406a-6436-46ba-90a5-27b28cc4013f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":94,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"17c8f710-6673-4337-968e-a1dd3169362e","scriptFormat":"text\/plain","script":{"content":["tw.local.sql\nselect next value for ODC_Req_No_SEQ as Request_Number from ( values 1 );"]}},{"targetRef":"3b2a27dd-bb0d-42df-aff0-699cf0e3c861","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"921d6883-b423-41d4-9326-5df367bd7b30","sourceRef":"17c8f710-6673-4337-968e-a1dd3169362e"},{"startQuantity":1,"outgoing":["9fbedd09-94ac-4ce9-80b1-e1f712df61bc"],"incoming":["d1523a1e-c532-4298-a365-de8a9958bdaa"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":406,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set BPM Request Number","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b830c26b-c414-4848-a011-f032c4b7dd02","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.tempSequence = \"\"+tw.local.sequence[0].Request_Number;\r\ntw.local.BPM_Request_Number = \"\";\r\ntw.local.date = new tw.object.Date();\r\ntw.local.year = tw.local.date.getFullYear()+\"\";\r\n\r\ntw.local.BPM_Request_Number += tw.local.branchCode+\"04\"+tw.local.year.substring(2,4);\r\nfor (var i=0; i&lt;(7-tw.local.tempSequence.length); i++) {\r\n\ttw.local.BPM_Request_Number += \"0\";\r\n}\r\ntw.local.BPM_Request_Number += tw.local.tempSequence;"]}},{"targetRef":"1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"9fbedd09-94ac-4ce9-80b1-e1f712df61bc","sourceRef":"b830c26b-c414-4848-a011-f032c4b7dd02"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"tempSequence","isCollection":false,"declaredType":"dataObject","id":"2056.2a1b8670-c53d-4735-aa93-91c396b6cbf3"},{"parallelMultiple":false,"outgoing":["ce62d8f7-d497-40c7-a1e0-d12d1ad36b69"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"047c7944-3561-4826-a568-4db4151e40d5"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0990e815-d4b3-4504-a0a5-ee5c95eb3304","otherAttributes":{"eventImplId":"170aaabb-67e1-4503-8fa9-51de1520bfda"}}],"attachedToRef":"17c8f710-6673-4337-968e-a1dd3169362e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":129,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"fbff765d-5c3d-4b04-ae00-6dd8b61d2f45","outputSet":{}},{"parallelMultiple":false,"outgoing":["2d33c128-2cb2-4fa8-82bc-8ac6f0288b7c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ff2892c2-420c-41a8-817f-9b9714c782db"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"1502ffbd-4c45-4474-b8da-fc6937b43a22","otherAttributes":{"eventImplId":"e35941d2-27ed-4a06-8d1d-7d73751f365f"}}],"attachedToRef":"3b2a27dd-bb0d-42df-aff0-699cf0e3c861","extensionElements":{"nodeVisualInfo":[{"width":24,"x":273,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"427d0d0a-b434-4b46-bb08-3e411f972c32","outputSet":{}},{"parallelMultiple":false,"outgoing":["a42cb662-40b5-47e1-86f2-d8645af04199"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"aa7ae487-d3cb-4e6b-a4ac-1567956dadb5"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"308e0478-fb06-4600-8a6f-dfa9e9fd540f","otherAttributes":{"eventImplId":"6d949b86-f4f2-4597-8ea7-ef0f1753e3fa"}}],"attachedToRef":"b830c26b-c414-4848-a011-f032c4b7dd02","extensionElements":{"nodeVisualInfo":[{"width":24,"x":441,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"c4542e4d-c2be-4159-a2a8-6428db1b467a","outputSet":{}},{"targetRef":"9a60bc53-f7cb-4bf6-8531-43d172713401","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"ce62d8f7-d497-40c7-a1e0-d12d1ad36b69","sourceRef":"fbff765d-5c3d-4b04-ae00-6dd8b61d2f45"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"year","isCollection":false,"declaredType":"dataObject","id":"2056.c864b876-2cf4-459b-8a60-6e1dfe771c71"},{"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"date","isCollection":false,"declaredType":"dataObject","id":"2056.2f7c0f64-bcf7-4472-8415-afef04ea5bb8"},{"startQuantity":1,"outgoing":["24ab6713-7de4-4595-8910-e5e0ec1a4271"],"incoming":["ce62d8f7-d497-40c7-a1e0-d12d1ad36b69","2d33c128-2cb2-4fa8-82bc-8ac6f0288b7c","a42cb662-40b5-47e1-86f2-d8645af04199"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":337,"y":237,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Generate BPM Request Number\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"9a60bc53-f7cb-4bf6-8531-43d172713401","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"9a60bc53-f7cb-4bf6-8531-43d172713401","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"2d33c128-2cb2-4fa8-82bc-8ac6f0288b7c","sourceRef":"427d0d0a-b434-4b46-bb08-3e411f972c32"},{"targetRef":"9a60bc53-f7cb-4bf6-8531-43d172713401","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"a42cb662-40b5-47e1-86f2-d8645af04199","sourceRef":"c4542e4d-c2be-4159-a2a8-6428db1b467a"},{"targetRef":"1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"24ab6713-7de4-4595-8910-e5e0ec1a4271","sourceRef":"9a60bc53-f7cb-4bf6-8531-43d172713401"}],"laneSet":[{"id":"d235a6f6-f723-4f79-a7ec-824353555cab","lane":[{"flowNodeRef":["b100e10a-aa95-4baa-aaf5-aa4a42c3b2aa","1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd","3b2a27dd-bb0d-42df-aff0-699cf0e3c861","17c8f710-6673-4337-968e-a1dd3169362e","b830c26b-c414-4848-a011-f032c4b7dd02","fbff765d-5c3d-4b04-ae00-6dd8b61d2f45","427d0d0a-b434-4b46-bb08-3e411f972c32","c4542e4d-c2be-4159-a2a8-6428db1b467a","9a60bc53-f7cb-4bf6-8531-43d172713401"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5dbcd8fd-8866-4b6a-b22a-8adf69d25d96","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Generate BPM Request Number","declaredType":"process","id":"1.8e583b1e-1719-4e19-a6ce-6f41202527d4","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"BPM_Request_Number","isCollection":false,"id":"2055.3bdd8527-404f-4ac0-bd04-07931e8d167e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.9a8f8a58-0e47-413f-b430-fb905024fce6"}],"inputSet":[{"dataInputRefs":["2055.d063d99f-aaa6-427d-ac13-2ac9946acbda"]}],"outputSet":[{"dataOutputRefs":["2055.3bdd8527-404f-4ac0-bd04-07931e8d167e","2055.9a8f8a58-0e47-413f-b430-fb905024fce6"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"077\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.d063d99f-aaa6-427d-ac13-2ac9946acbda"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d063d99f-aaa6-427d-ac13-2ac9946acbda</processParameterId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d5ae210c-ae8b-4165-8e46-7aac035fc86f</guid>
            <versionId>bf4ca6a4-41c7-4351-90d5-faae999283fb</versionId>
        </processParameter>
        <processParameter name="BPM_Request_Number">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3bdd8527-404f-4ac0-bd04-07931e8d167e</processParameterId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fca9fc0e-5164-4b96-a01d-217f0600e7e7</guid>
            <versionId>6b48784a-3966-47d8-ad61-463dd8e77da8</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9a8f8a58-0e47-413f-b430-fb905024fce6</processParameterId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0b163319-72f7-4f0b-97cd-4fdb35f95977</guid>
            <versionId>eda0ea23-9b9a-4f7f-91a3-be1dfde4d510</versionId>
        </processParameter>
        <processVariable name="sequence">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.53ceb906-6fc9-456e-a2db-069fb6b4de84</processVariableId>
            <description isNull="true" />
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.8566ac97-e9e5-481e-9605-42b0dc11da47</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>53f7a283-5e91-4cae-9503-4472e9ed52f4</guid>
            <versionId>c27effd4-db83-4a34-bbf2-e966d9bbbd55</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.75dbb317-9def-4aa0-9e3e-dd9226bc2f78</processVariableId>
            <description isNull="true" />
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1199916a-e780-4378-9850-999df5d46c85</guid>
            <versionId>9b9c7b3d-c713-4d6f-86b6-874cdbc830f5</versionId>
        </processVariable>
        <processVariable name="tempSequence">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2a1b8670-c53d-4735-aa93-91c396b6cbf3</processVariableId>
            <description isNull="true" />
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4a8904f1-96a3-46ab-acd6-3bfcc6268384</guid>
            <versionId>5b4f9bb0-d769-4911-9297-9a4866fd42b8</versionId>
        </processVariable>
        <processVariable name="year">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c864b876-2cf4-459b-8a60-6e1dfe771c71</processVariableId>
            <description isNull="true" />
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f471d039-bccb-4e4a-979a-09f7a5ebf66a</guid>
            <versionId>1bd076c0-cb03-42a5-8429-d8fc239bf194</versionId>
        </processVariable>
        <processVariable name="date">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2f7c0f64-bcf7-4472-8415-afef04ea5bb8</processVariableId>
            <description isNull="true" />
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0cc23f8d-72b7-4ec6-8d97-05f66636fc10</guid>
            <versionId>d0b412b1-e468-420e-a7ed-e1ffd8730dfa</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3b2a27dd-bb0d-42df-aff0-699cf0e3c861</processItemId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1576</guid>
            <versionId>8dccda06-4226-4db9-922e-0dc981351ccc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="238" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:cde91268c0d1a44e:-10e36a59:18badede6df:-41d2</errorHandlerItem>
                <errorHandlerItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>04964dad-9dc3-4c88-be9e-b34b82d105c7</guid>
                <versionId>45389ac0-958a-4a4b-909b-4e62aa720889</versionId>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ed8cd81b-b03e-4708-9ee4-b160e29dc657</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"sequence"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ab2c710e-eb33-4ac1-8d1e-985cc9c982f5</guid>
                    <versionId>30539936-c47f-4b70-8116-8dbf4c2cf55e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7fbd117f-b19d-4af8-bcc9-50a5be677b0c</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_APP</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>15592d9d-4ea8-4cae-903d-197dbc13c82f</guid>
                    <versionId>50472116-6d0a-468f-b5df-c4605bbc4c0e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f93fd072-8875-49ec-9d67-358e2ea12314</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7d9266eb-9eb3-4eed-bba3-8e71f72cec13</guid>
                    <versionId>5fd441a6-61bc-4078-9b6c-2c9fe3434f26</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.502161b3-5be8-4ad2-ae95-0b8d157fc060</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>ed11be2a-2eca-4513-911e-490c2a82ebf5</guid>
                    <versionId>96fc8614-ebb2-420f-b9f7-14e8cd0008d1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9a3be2e4-02a1-4bfe-b657-b78fe810b16b</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a59b7ebe-d335-494e-9023-005fa69f991f</guid>
                    <versionId>9c28ff60-49b1-43fa-86f9-596767d969de</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1ea27c5a-ed5e-4ec6-aa79-d5dd4e1d8bcb</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.6d5f5978-b79b-49a1-8015-77a3d9e77946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sequence</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f94b1cf4-0eab-446c-b99b-061793b8b361</guid>
                    <versionId>e0b4031a-26cc-419f-b6a1-88b3cb896655</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd</processItemId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.e521c61a-dfa7-4942-bd85-5ffc406e860a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1572</guid>
            <versionId>9f1ca93e-55d5-4bd5-974f-c614b31ff4db</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.e521c61a-dfa7-4942-bd85-5ffc406e860a</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>063ae2c9-1e6c-48c4-91cb-d87541395099</guid>
                <versionId>069f3922-2adb-443d-8db3-f5ded054a191</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</processItemId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.5ee16f68-eaec-4b5e-9ac0-deae8940e32a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cde91268c0d1a44e:-10e36a59:18badede6df:-41d2</guid>
            <versionId>b22fe95a-91d3-406c-94e0-0b205af0e737</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="337" y="237">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.5ee16f68-eaec-4b5e-9ac0-deae8940e32a</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>e1b0318c-e774-4558-b4c6-d3b3a7ef7c08</guid>
                <versionId>5b63077c-b5a3-4c01-b720-be2d62a06904</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d57e2f12-f07b-4912-9e52-f5d332ce55dd</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.5ee16f68-eaec-4b5e-9ac0-deae8940e32a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ad2291db-fbfc-4da8-bf29-ae1eba3c171e</guid>
                    <versionId>5dfe7f1f-a167-45dd-8914-b2ef93c03365</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2ea1649d-6d8a-46fb-af40-f52d5c84a273</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.5ee16f68-eaec-4b5e-9ac0-deae8940e32a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Generate BPM Request Number"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>de0d24b8-025a-449b-bc68-5d1c663c4381</guid>
                    <versionId>eda6f8e5-54ea-4f77-b7c3-6a076bcea33d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7b369d6b-e19d-4e86-8330-f0dc2ce7a71f</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.5ee16f68-eaec-4b5e-9ac0-deae8940e32a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b5e4c519-33c0-4f8c-be85-2384710b864a</guid>
                    <versionId>f16253e3-7d9a-413f-83f5-3bb91567b3c7</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b830c26b-c414-4848-a011-f032c4b7dd02</processItemId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <name>Set BPM Request Number</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.a7a95364-550f-4b1b-ade4-7bf3280660c3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1575</guid>
            <versionId>bcb91c73-3e2a-4e41-af88-98391e30ccd6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="406" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:cde91268c0d1a44e:-10e36a59:18badede6df:-41d2</errorHandlerItem>
                <errorHandlerItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.a7a95364-550f-4b1b-ade4-7bf3280660c3</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.tempSequence = ""+tw.local.sequence[0].Request_Number;&#xD;
tw.local.BPM_Request_Number = "";&#xD;
tw.local.date = new tw.object.Date();&#xD;
tw.local.year = tw.local.date.getFullYear()+"";&#xD;
&#xD;
tw.local.BPM_Request_Number += tw.local.branchCode+"04"+tw.local.year.substring(2,4);&#xD;
for (var i=0; i&lt;(7-tw.local.tempSequence.length); i++) {&#xD;
	tw.local.BPM_Request_Number += "0";&#xD;
}&#xD;
tw.local.BPM_Request_Number += tw.local.tempSequence;</script>
                <isRule>false</isRule>
                <guid>18a0dc7f-fc05-45d1-88f1-f28a15edef62</guid>
                <versionId>7bb32b39-71e9-48c6-8d63-96586eebe7e1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.17c8f710-6673-4337-968e-a1dd3169362e</processItemId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <name>Set Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.daff2191-c0ff-4c6a-bddc-9a409b01b95a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1574</guid>
            <versionId>f2536bf4-f73f-49e3-af68-615a3a16e2b0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="94" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:cde91268c0d1a44e:-10e36a59:18badede6df:-41d2</errorHandlerItem>
                <errorHandlerItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.daff2191-c0ff-4c6a-bddc-9a409b01b95a</scriptId>
                <scriptTypeId>128</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql
select next value for ODC_Req_No_SEQ as Request_Number from ( values 1 );</script>
                <isRule>false</isRule>
                <guid>d40c934d-8119-4c2e-80ac-200b716556d4</guid>
                <versionId>76f1350c-bd20-41d0-9777-2e5f4de9f682</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.17c8f710-6673-4337-968e-a1dd3169362e</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Generate BPM Request Number" id="1.8e583b1e-1719-4e19-a6ce-6f41202527d4" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d063d99f-aaa6-427d-ac13-2ac9946acbda">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"077"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="BPM_Request_Number" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.3bdd8527-404f-4ac0-bd04-07931e8d167e" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.9a8f8a58-0e47-413f-b430-fb905024fce6" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.d063d99f-aaa6-427d-ac13-2ac9946acbda</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.3bdd8527-404f-4ac0-bd04-07931e8d167e</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.9a8f8a58-0e47-413f-b430-fb905024fce6</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="d235a6f6-f723-4f79-a7ec-824353555cab">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5dbcd8fd-8866-4b6a-b22a-8adf69d25d96" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b100e10a-aa95-4baa-aaf5-aa4a42c3b2aa</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3b2a27dd-bb0d-42df-aff0-699cf0e3c861</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>17c8f710-6673-4337-968e-a1dd3169362e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b830c26b-c414-4848-a011-f032c4b7dd02</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fbff765d-5c3d-4b04-ae00-6dd8b61d2f45</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>427d0d0a-b434-4b46-bb08-3e411f972c32</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c4542e4d-c2be-4159-a2a8-6428db1b467a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9a60bc53-f7cb-4bf6-8531-43d172713401</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b100e10a-aa95-4baa-aaf5-aa4a42c3b2aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.454d406a-6436-46ba-90a5-27b28cc4013f</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-1572</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9fbedd09-94ac-4ce9-80b1-e1f712df61bc</ns16:incoming>
                        
                        
                        <ns16:incoming>24ab6713-7de4-4595-8910-e5e0ec1a4271</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b100e10a-aa95-4baa-aaf5-aa4a42c3b2aa" targetRef="17c8f710-6673-4337-968e-a1dd3169362e" name="To Set Query" id="2027.454d406a-6436-46ba-90a5-27b28cc4013f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" name="SQL Execute Statement" id="3b2a27dd-bb0d-42df-aff0-699cf0e3c861">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="238" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>921d6883-b423-41d4-9326-5df367bd7b30</ns16:incoming>
                        
                        
                        <ns16:outgoing>d1523a1e-c532-4298-a365-de8a9958bdaa</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_APP</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"sequence"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">tw.local.sequence</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="3b2a27dd-bb0d-42df-aff0-699cf0e3c861" targetRef="b830c26b-c414-4848-a011-f032c4b7dd02" name="To Set BPM Request Number" id="d1523a1e-c532-4298-a365-de8a9958bdaa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.8566ac97-e9e5-481e-9605-42b0dc11da47" isCollection="true" name="sequence" id="2056.53ceb906-6fc9-456e-a2db-069fb6b4de84" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.75dbb317-9def-4aa0-9e3e-dd9226bc2f78" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/plain" name="Set Query" id="17c8f710-6673-4337-968e-a1dd3169362e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="94" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.454d406a-6436-46ba-90a5-27b28cc4013f</ns16:incoming>
                        
                        
                        <ns16:outgoing>921d6883-b423-41d4-9326-5df367bd7b30</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql
select next value for ODC_Req_No_SEQ as Request_Number from ( values 1 );</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="17c8f710-6673-4337-968e-a1dd3169362e" targetRef="3b2a27dd-bb0d-42df-aff0-699cf0e3c861" name="To SQL Execute Statement" id="921d6883-b423-41d4-9326-5df367bd7b30">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set BPM Request Number" id="b830c26b-c414-4848-a011-f032c4b7dd02">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="406" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d1523a1e-c532-4298-a365-de8a9958bdaa</ns16:incoming>
                        
                        
                        <ns16:outgoing>9fbedd09-94ac-4ce9-80b1-e1f712df61bc</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.tempSequence = ""+tw.local.sequence[0].Request_Number;&#xD;
tw.local.BPM_Request_Number = "";&#xD;
tw.local.date = new tw.object.Date();&#xD;
tw.local.year = tw.local.date.getFullYear()+"";&#xD;
&#xD;
tw.local.BPM_Request_Number += tw.local.branchCode+"04"+tw.local.year.substring(2,4);&#xD;
for (var i=0; i&lt;(7-tw.local.tempSequence.length); i++) {&#xD;
	tw.local.BPM_Request_Number += "0";&#xD;
}&#xD;
tw.local.BPM_Request_Number += tw.local.tempSequence;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b830c26b-c414-4848-a011-f032c4b7dd02" targetRef="1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd" name="To End" id="9fbedd09-94ac-4ce9-80b1-e1f712df61bc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="tempSequence" id="2056.2a1b8670-c53d-4735-aa93-91c396b6cbf3" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="17c8f710-6673-4337-968e-a1dd3169362e" parallelMultiple="false" name="Error" id="fbff765d-5c3d-4b04-ae00-6dd8b61d2f45">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="129" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ce62d8f7-d497-40c7-a1e0-d12d1ad36b69</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="047c7944-3561-4826-a568-4db4151e40d5" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="0990e815-d4b3-4504-a0a5-ee5c95eb3304" eventImplId="170aaabb-67e1-4503-8fa9-51de1520bfda">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="3b2a27dd-bb0d-42df-aff0-699cf0e3c861" parallelMultiple="false" name="Error1" id="427d0d0a-b434-4b46-bb08-3e411f972c32">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="273" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2d33c128-2cb2-4fa8-82bc-8ac6f0288b7c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ff2892c2-420c-41a8-817f-9b9714c782db" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="1502ffbd-4c45-4474-b8da-fc6937b43a22" eventImplId="e35941d2-27ed-4a06-8d1d-7d73751f365f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b830c26b-c414-4848-a011-f032c4b7dd02" parallelMultiple="false" name="Error2" id="c4542e4d-c2be-4159-a2a8-6428db1b467a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="441" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a42cb662-40b5-47e1-86f2-d8645af04199</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="aa7ae487-d3cb-4e6b-a4ac-1567956dadb5" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="308e0478-fb06-4600-8a6f-dfa9e9fd540f" eventImplId="6d949b86-f4f2-4597-8ea7-ef0f1753e3fa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="fbff765d-5c3d-4b04-ae00-6dd8b61d2f45" targetRef="9a60bc53-f7cb-4bf6-8531-43d172713401" name="To End Event" id="ce62d8f7-d497-40c7-a1e0-d12d1ad36b69">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="year" id="2056.c864b876-2cf4-459b-8a60-6e1dfe771c71" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="date" id="2056.2f7c0f64-bcf7-4472-8415-afef04ea5bb8" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exception Handling" id="9a60bc53-f7cb-4bf6-8531-43d172713401">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="337" y="237" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ce62d8f7-d497-40c7-a1e0-d12d1ad36b69</ns16:incoming>
                        
                        
                        <ns16:incoming>2d33c128-2cb2-4fa8-82bc-8ac6f0288b7c</ns16:incoming>
                        
                        
                        <ns16:incoming>a42cb662-40b5-47e1-86f2-d8645af04199</ns16:incoming>
                        
                        
                        <ns16:outgoing>24ab6713-7de4-4595-8910-e5e0ec1a4271</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Generate BPM Request Number"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="427d0d0a-b434-4b46-bb08-3e411f972c32" targetRef="9a60bc53-f7cb-4bf6-8531-43d172713401" name="To Exception Handling" id="2d33c128-2cb2-4fa8-82bc-8ac6f0288b7c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c4542e4d-c2be-4159-a2a8-6428db1b467a" targetRef="9a60bc53-f7cb-4bf6-8531-43d172713401" name="To Exception Handling" id="a42cb662-40b5-47e1-86f2-d8645af04199">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9a60bc53-f7cb-4bf6-8531-43d172713401" targetRef="1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd" name="To End" id="24ab6713-7de4-4595-8910-e5e0ec1a4271">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightBottom</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set BPM Request Number">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d1523a1e-c532-4298-a365-de8a9958bdaa</processLinkId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3b2a27dd-bb0d-42df-aff0-699cf0e3c861</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.b830c26b-c414-4848-a011-f032c4b7dd02</toProcessItemId>
            <guid>49e1fb64-fd72-4f73-bb49-449e7a1afff6</guid>
            <versionId>362cc557-871c-4983-bac0-f1521216a93e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3b2a27dd-bb0d-42df-aff0-699cf0e3c861</fromProcessItemId>
            <toProcessItemId>2025.b830c26b-c414-4848-a011-f032c4b7dd02</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.24ab6713-7de4-4595-8910-e5e0ec1a4271</processLinkId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd</toProcessItemId>
            <guid>a577cefe-7081-48d9-bd66-2d62b2f7a9d5</guid>
            <versionId>3ff2abad-ded3-4dbc-a12e-7ccb99880c66</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightBottom" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.9a60bc53-f7cb-4bf6-8531-43d172713401</fromProcessItemId>
            <toProcessItemId>2025.1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.921d6883-b423-41d4-9326-5df367bd7b30</processLinkId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.17c8f710-6673-4337-968e-a1dd3169362e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3b2a27dd-bb0d-42df-aff0-699cf0e3c861</toProcessItemId>
            <guid>8068b033-3451-4ccf-bfbd-2e826f8eec88</guid>
            <versionId>55347555-c710-4b9a-b154-f3dc72f71af8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.17c8f710-6673-4337-968e-a1dd3169362e</fromProcessItemId>
            <toProcessItemId>2025.3b2a27dd-bb0d-42df-aff0-699cf0e3c861</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9fbedd09-94ac-4ce9-80b1-e1f712df61bc</processLinkId>
            <processId>1.8e583b1e-1719-4e19-a6ce-6f41202527d4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b830c26b-c414-4848-a011-f032c4b7dd02</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd</toProcessItemId>
            <guid>52124b10-08b2-49e5-b910-e7d9cc7c7544</guid>
            <versionId>e2761aad-7c5d-4ed8-8597-353a500afe6f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b830c26b-c414-4848-a011-f032c4b7dd02</fromProcessItemId>
            <toProcessItemId>2025.1585fa3f-2dd0-49ad-ad7f-7fc031c35ecd</toProcessItemId>
        </link>
    </process>
</teamworks>

