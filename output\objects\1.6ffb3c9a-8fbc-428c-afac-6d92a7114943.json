{"id": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "versionId": "8ed1b310-c0a7-4a1b-9389-c19ee528707b", "name": "Print barcode", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "name": "Print barcode", "lastModified": "1707765723528", "lastModifiedBy": "mohamed.reda", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.0b38ba63-35a3-462b-8908-a56f6392b5be", "2025.0b38ba63-35a3-462b-8908-a56f6392b5be"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4b7d", "versionId": "8ed1b310-c0a7-4a1b-9389-c19ee528707b", "dependencySummary": "<dependencySummary id=\"bpdid:709ea623f6099cbd:-68e5c70f:18d7fd15bbe:2389\">\r\r\n  <artifactReference id=\"bpdid:709ea623f6099cbd:-68e5c70f:18d7fd15bbe:238a\">\r\r\n    <refId>1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d</refId>\r\r\n    <refType>1</refType>\r\r\n    <nameValuePair id=\"bpdid:709ea623f6099cbd:-68e5c70f:18d7fd15bbe:238b\">\r\r\n      <name>operationId</name>\r\r\n      <value>_d9bb1876-6c68-4dd3-911c-d2550bb89434</value>\r\r\n    </nameValuePair>\r\r\n  </artifactReference>\r\r\n</dependencySummary>", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.3cc93a40-8b00-4b60-84ad-7946d62d9093\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"8c8a9dbb-d8fc-4157-84f4-2941cfb1d9c7\"},{\"incoming\":[\"96135cf9-56ef-4146-865f-916def19c79f\",\"59914810-e6fc-48ed-8090-f9ff47abdbc3\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4b7f\"],\"preAssignmentScript\":[\"writeLog(\\\"End Service\\\");\\r\\n\\r\\n\\r\\nfunction writeLog(msg , variable){  \\/\\/ vairable is optional\\r\\n\\tvar instanceID = \\\"\\\";\\r\\n\\ttry {instanceID = \\\"Instance ID : \\\"+tw.system.currentProcessInstance.id  +\\\"  ::  \\\";} catch (err) {}\\r\\n\\tvariable == undefined ? variable =\\\" \\\" : variable = \\\" : \\\"+ variable ;\\r\\n\\tvar message = instanceID + tw.system.model.processApp.name  +\\\" :: \\\" + tw.system.serviceFlow.type +\\\" :: \\\"+ tw.system.serviceFlow.name +\\\" :: \\\"  + msg+\\\" : \\\" + variable;\\r\\n\\tlog.info( message);\\r\\n}\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"e52eabbc-1d89-406e-8b67-2fc707b52002\"},{\"targetRef\":\"0b38ba63-35a3-462b-8908-a56f6392b5be\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Generate Barcode\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3cc93a40-8b00-4b60-84ad-7946d62d9093\",\"sourceRef\":\"8c8a9dbb-d8fc-4157-84f4-2941cfb1d9c7\"},{\"outgoing\":[\"96135cf9-56ef-4146-865f-916def19c79f\"],\"incoming\":[\"2027.3cc93a40-8b00-4b60-84ad-7946d62d9093\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":270,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"writeLog(\\\"Start Service\\\");\\r\\n\\r\\nfunction writeLog(msg , variable){  \\/\\/ vairable is optional\\r\\n\\tvar instanceID = \\\"\\\";\\r\\n\\ttry {instanceID = \\\"Instance ID : \\\"+tw.system.currentProcessInstance.id  +\\\"  ::  \\\";} catch (err) {}\\r\\n\\tvariable == undefined ? variable =\\\" \\\" : variable = \\\" : \\\"+ variable ;\\r\\n\\tvar message = instanceID + tw.system.model.processApp.name  +\\\" :: \\\" + tw.system.serviceFlow.type +\\\" :: \\\"+ tw.system.serviceFlow.name +\\\" :: \\\"  + msg+\\\" : \\\" + variable;\\r\\n\\tlog.info( message);\\r\\n}\\r\\n\"],\"activityType\":[\"ServiceTask\"],\"externalServiceRefPO\":[\"1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d\"],\"externalServiceRef\":[\"_406b38c7-7d45-41e9-97b0-619e439685fb\"]},\"operationRef\":\"_d9bb1876-6c68-4dd3-911c-d2550bb89434\",\"implementation\":\"##WebService\",\"declaredType\":\"serviceTask\",\"startQuantity\":1,\"name\":\"Generate Barcode\",\"dataInputAssociation\":[{\"targetRef\":\"_a181d5f4-514a-4499-93d8-0d824eea2d5f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.refrenceNo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"0b38ba63-35a3-462b-8908-a56f6392b5be\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.pdf\"]}}],\"sourceRef\":[\"_1ad9bd31-bbe2-48ec-9d09-d250b18c8f7b\"]}]},{\"targetRef\":\"e52eabbc-1d89-406e-8b67-2fc707b52002\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Script Task\",\"declaredType\":\"sequenceFlow\",\"id\":\"96135cf9-56ef-4146-865f-916def19c79f\",\"sourceRef\":\"0b38ba63-35a3-462b-8908-a56f6392b5be\"},{\"parallelMultiple\":false,\"outgoing\":[\"81fa9e4c-872c-445f-8836-cd3dc9bd35ad\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"d29e4faa-5446-4e7a-837a-61cb6f626cb0\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"67c1753b-1517-492c-80b9-358457d349e4\",\"otherAttributes\":{\"eventImplId\":\"26af8183-ef05-4d3c-85b0-bc3868f598a4\"}}],\"attachedToRef\":\"0b38ba63-35a3-462b-8908-a56f6392b5be\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":305,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"f083209b-bf06-404e-8c16-73e7bbb31f71\",\"outputSet\":{}},{\"startQuantity\":1,\"outgoing\":[\"59914810-e6fc-48ed-8090-f9ff47abdbc3\"],\"incoming\":[\"81fa9e4c-872c-445f-8836-cd3dc9bd35ad\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":458,\"y\":142,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Error Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Print barcode\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"964fda4d-0c71-4009-8f93-666b98c2c476\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"964fda4d-0c71-4009-8f93-666b98c2c476\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"81fa9e4c-872c-445f-8836-cd3dc9bd35ad\",\"sourceRef\":\"f083209b-bf06-404e-8c16-73e7bbb31f71\"},{\"targetRef\":\"e52eabbc-1d89-406e-8b67-2fc707b52002\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"59914810-e6fc-48ed-8090-f9ff47abdbc3\",\"sourceRef\":\"964fda4d-0c71-4009-8f93-666b98c2c476\"}],\"laneSet\":[{\"id\":\"1ae89fc0-3f38-4889-8f9c-f854a68bff2c\",\"lane\":[{\"flowNodeRef\":[\"8c8a9dbb-d8fc-4157-84f4-2941cfb1d9c7\",\"e52eabbc-1d89-406e-8b67-2fc707b52002\",\"0b38ba63-35a3-462b-8908-a56f6392b5be\",\"f083209b-bf06-404e-8c16-73e7bbb31f71\",\"964fda4d-0c71-4009-8f93-666b98c2c476\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"c23af0d6-b46b-43a1-8ee2-25b1ffd903ca\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Print barcode\",\"declaredType\":\"process\",\"id\":\"1.6ffb3c9a-8fbc-428c-afac-6d92a7114943\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"pdf\",\"isCollection\":false,\"id\":\"2055.a08fbc3a-70cd-4554-8ce3-d959ff6131e6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.6b30683f-a0a2-441b-8723-c99633bfaa01\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.48afe099-7302-4c77-80f1-61f62b685731\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.a08fbc3a-70cd-4554-8ce3-d959ff6131e6\",\"2055.6b30683f-a0a2-441b-8723-c99633bfaa01\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"7765403220002777\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"refrenceNo\",\"isCollection\":false,\"id\":\"2055.48afe099-7302-4c77-80f1-61f62b685731\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "refrenceNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.48afe099-7302-4c77-80f1-61f62b685731", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "\"7765403220002777\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "2a9d7b99-c09f-4ed1-bd05-b8be062dc2d7", "versionId": "7f16763b-7aa5-4cd0-9739-4fac38eea6fe"}, {"name": "pdf", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a08fbc3a-70cd-4554-8ce3-d959ff6131e6", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9f1ff817-d584-4c60-900a-60fcfd1341ec", "versionId": "b97bfe19-a419-4635-b89d-5f1a2f6c86cb"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6b30683f-a0a2-441b-8723-c99633bfaa01", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "59749427-d395-4f4b-8b88-15de135d2df0", "versionId": "c17fc3be-3f74-4ffb-8018-5cb9cad717ca"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.964fda4d-0c71-4009-8f93-666b98c2c476", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "name": "Erro<PERSON>", "tWComponentName": "SubProcess", "tWComponentId": "3012.b8674084-0e6b-4cba-a013-d3d6368e9204", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4d8a", "versionId": "40bfd28e-27cd-4274-a372-900eb2829710", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "458", "y": "142", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.b8674084-0e6b-4cba-a013-d3d6368e9204", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "491490db-068a-4522-bdb3-8ccf5c2b10d2", "versionId": "090d9c73-fbb9-4d7d-a085-c7341136eb95", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a4faa437-4406-4d30-a475-6fa5d4f87d60", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.b8674084-0e6b-4cba-a013-d3d6368e9204", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "57807af9-cfdf-4bea-adf3-0c330cb95c4e", "versionId": "26093944-de6b-4564-9671-8a128c861415", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ed493c9b-c8f1-4eb8-934d-a7be93e92bd1", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.b8674084-0e6b-4cba-a013-d3d6368e9204", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "492c89af-a01b-436c-8fff-79f2db61f299", "versionId": "8b888668-8790-4006-b286-08443ef1aa4d", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.408bcae5-c316-4fc2-9793-ad12d90940dc", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.b8674084-0e6b-4cba-a013-d3d6368e9204", "useDefault": "false", "value": "\"Print barcode\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "1ce40c14-b0eb-4e91-98bb-daedeae8314e", "versionId": "983e2a87-df7b-4438-91e1-2816859941e4", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0b38ba63-35a3-462b-8908-a56f6392b5be", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "name": "Generate Barcode", "tWComponentName": "SKELConnector", "tWComponentId": "3034.77ddc2d1-20c1-40ca-8248-6886a40f9e8e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.964fda4d-0c71-4009-8f93-666b98c2c476", "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4d7c", "versionId": "70d76d17-aeca-4e3e-b110-a7fc7887d37f", "externalServiceRef": "/1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "externalServiceOp": "_d9bb1876-6c68-4dd3-911c-d2550bb89434", "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.597d531b-6936-480b-876a-9b27c6bcb991", "processItemId": "2025.0b38ba63-35a3-462b-8908-a56f6392b5be", "location": "1", "script": "writeLog(\"Start Service\");\r\r\n\r\r\nfunction writeLog(msg , variable){  // vairable is optional\r\r\n\tvar instanceID = \"\";\r\r\n\ttry {instanceID = \"Instance ID : \"+tw.system.currentProcessInstance.id  +\"  ::  \";} catch (err) {}\r\r\n\tvariable == undefined ? variable =\" \" : variable = \" : \"+ variable ;\r\r\n\tvar message = instanceID + tw.system.model.processApp.name  +\" :: \" + tw.system.serviceFlow.type +\" :: \"+ tw.system.serviceFlow.name +\" :: \"  + msg+\" : \" + variable;\r\r\n\tlog.info( message);\r\r\n}\r\r\n", "guid": "adf2c298-3c66-4788-bdc9-ce0395dbffe3", "versionId": "b65283c4-4ae0-4e4b-9835-2cd75874e4c3"}, "layoutData": {"x": "270", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4d8a", "errorHandlerItemId": "2025.964fda4d-0c71-4009-8f93-666b98c2c476", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "skelConnectorId": "3034.77ddc2d1-20c1-40ca-8248-6886a40f9e8e", "definition": "<config type=\"com.lombardisoftware.client.persistence.SKELConnectorConfiguration\">\r\r\n  <inputParameters>\r\r\n    <parameter>\r\r\n      <name>_a181d5f4-514a-4499-93d8-0d824eea2d5f</name>\r\r\n      <type>itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022</type>\r\r\n      <description />\r\r\n      <defaultValue />\r\r\n      <argumentVariable>tw.local.refrenceNo</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n    </parameter>\r\r\n  </inputParameters>\r\r\n  <outputParameters>\r\r\n    <parameter>\r\r\n      <name>_1ad9bd31-bbe2-48ec-9d09-d250b18c8f7b</name>\r\r\n      <type>itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022</type>\r\r\n      <description />\r\r\n      <defaultValue />\r\r\n      <argumentVariable>tw.local.pdf</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n    </parameter>\r\r\n  </outputParameters>\r\r\n  <extendedProperties />\r\r\n</config>", "guid": "b6c9daec-723b-4bb1-a7eb-1c347f79fb77", "versionId": "62f36c2a-81cf-4f99-a1ba-7f0dccd0cd42"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e52eabbc-1d89-406e-8b67-2fc707b52002", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.0e51a0b8-5e1f-44f2-9d61-e135251462ad", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4b7f", "versionId": "e931e4e2-51f9-4168-b164-24821b8779af", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.bf30b5e4-1df8-4642-8946-6a58a6c68107", "processItemId": "2025.e52eabbc-1d89-406e-8b67-2fc707b52002", "location": "1", "script": "writeLog(\"End Service\");\r\r\n\r\r\n\r\r\nfunction writeLog(msg , variable){  // vairable is optional\r\r\n\tvar instanceID = \"\";\r\r\n\ttry {instanceID = \"Instance ID : \"+tw.system.currentProcessInstance.id  +\"  ::  \";} catch (err) {}\r\r\n\tvariable == undefined ? variable =\" \" : variable = \" : \"+ variable ;\r\r\n\tvar message = instanceID + tw.system.model.processApp.name  +\" :: \" + tw.system.serviceFlow.type +\" :: \"+ tw.system.serviceFlow.name +\" :: \"  + msg+\" : \" + variable;\r\r\n\tlog.info( message);\r\r\n}", "guid": "30ad361e-3a4e-4cd0-8762-22799125e545", "versionId": "121e9bb2-91de-4505-9632-87338b24172a"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.0e51a0b8-5e1f-44f2-9d61-e135251462ad", "haltProcess": "false", "guid": "a2280fc5-51ec-41a4-8c88-836b575ebb82", "versionId": "2f681fd9-6820-4eef-9023-b314a2d24ff1"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Print barcode", "id": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "refrenceNo", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.48afe099-7302-4c77-80f1-61f62b685731", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"7765403220002777\"", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "pdf", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a08fbc3a-70cd-4554-8ce3-d959ff6131e6"}, {"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.6b30683f-a0a2-441b-8723-c99633bfaa01"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.48afe099-7302-4c77-80f1-61f62b685731"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.a08fbc3a-70cd-4554-8ce3-d959ff6131e6", "2055.6b30683f-a0a2-441b-8723-c99633bfaa01"]}}, "ns16:laneSet": {"id": "1ae89fc0-3f38-4889-8f9c-f854a68bff2c", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "c23af0d6-b46b-43a1-8ee2-25b1ffd903ca", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["8c8a9dbb-d8fc-4157-84f4-2941cfb1d9c7", "e52eabbc-1d89-406e-8b67-2fc707b52002", "0b38ba63-35a3-462b-8908-a56f6392b5be", "f083209b-bf06-404e-8c16-73e7bbb31f71", "964fda4d-0c71-4009-8f93-666b98c2c476"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "8c8a9dbb-d8fc-4157-84f4-2941cfb1d9c7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.3cc93a40-8b00-4b60-84ad-7946d62d9093"}, "ns16:endEvent": {"name": "End", "id": "e52eabbc-1d89-406e-8b67-2fc707b52002", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4b7f", "ns3:preAssignmentScript": "writeLog(\"End Service\");\r\r\n\r\r\n\r\r\nfunction writeLog(msg , variable){  // vairable is optional\r\r\n\tvar instanceID = \"\";\r\r\n\ttry {instanceID = \"Instance ID : \"+tw.system.currentProcessInstance.id  +\"  ::  \";} catch (err) {}\r\r\n\tvariable == undefined ? variable =\" \" : variable = \" : \"+ variable ;\r\r\n\tvar message = instanceID + tw.system.model.processApp.name  +\" :: \" + tw.system.serviceFlow.type +\" :: \"+ tw.system.serviceFlow.name +\" :: \"  + msg+\" : \" + variable;\r\r\n\tlog.info( message);\r\r\n}"}, "ns16:incoming": ["96135cf9-56ef-4146-865f-916def19c79f", "59914810-e6fc-48ed-8090-f9ff47abdbc3"]}, "ns16:sequenceFlow": [{"sourceRef": "8c8a9dbb-d8fc-4157-84f4-2941cfb1d9c7", "targetRef": "0b38ba63-35a3-462b-8908-a56f6392b5be", "name": "To Generate Barcode", "id": "2027.3cc93a40-8b00-4b60-84ad-7946d62d9093", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "0b38ba63-35a3-462b-8908-a56f6392b5be", "targetRef": "e52eabbc-1d89-406e-8b67-2fc707b52002", "name": "To <PERSON>ript Task", "id": "96135cf9-56ef-4146-865f-916def19c79f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "f083209b-bf06-404e-8c16-73e7bbb31f71", "targetRef": "964fda4d-0c71-4009-8f93-666b98c2c476", "name": "To <PERSON><PERSON>r <PERSON>", "id": "81fa9e4c-872c-445f-8836-cd3dc9bd35ad", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "964fda4d-0c71-4009-8f93-666b98c2c476", "targetRef": "e52eabbc-1d89-406e-8b67-2fc707b52002", "name": "To End", "id": "59914810-e6fc-48ed-8090-f9ff47abdbc3", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}], "ns16:serviceTask": {"operationRef": "_d9bb1876-6c68-4dd3-911c-d2550bb89434", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Generate Barcode", "id": "0b38ba63-35a3-462b-8908-a56f6392b5be", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "270", "y": "57", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "writeLog(\"Start Service\");\r\r\n\r\r\nfunction writeLog(msg , variable){  // vairable is optional\r\r\n\tvar instanceID = \"\";\r\r\n\ttry {instanceID = \"Instance ID : \"+tw.system.currentProcessInstance.id  +\"  ::  \";} catch (err) {}\r\r\n\tvariable == undefined ? variable =\" \" : variable = \" : \"+ variable ;\r\r\n\tvar message = instanceID + tw.system.model.processApp.name  +\" :: \" + tw.system.serviceFlow.type +\" :: \"+ tw.system.serviceFlow.name +\" :: \"  + msg+\" : \" + variable;\r\r\n\tlog.info( message);\r\r\n}\r\r\n", "ns4:activityType": "ServiceTask", "ns3:externalServiceRefPO": "1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "ns3:externalServiceRef": "_406b38c7-7d45-41e9-97b0-619e439685fb"}, "ns16:incoming": "2027.3cc93a40-8b00-4b60-84ad-7946d62d9093", "ns16:outgoing": "96135cf9-56ef-4146-865f-916def19c79f", "ns16:dataInputAssociation": {"ns16:targetRef": "_a181d5f4-514a-4499-93d8-0d824eea2d5f", "ns16:assignment": {"ns16:from": {"_": "tw.local.refrenceNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "_1ad9bd31-bbe2-48ec-9d09-d250b18c8f7b", "ns16:assignment": {"ns16:to": {"_": "tw.local.pdf", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "0b38ba63-35a3-462b-8908-a56f6392b5be", "parallelMultiple": "false", "name": "Error", "id": "f083209b-bf06-404e-8c16-73e7bbb31f71", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "305", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "81fa9e4c-872c-445f-8836-cd3dc9bd35ad", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "d29e4faa-5446-4e7a-837a-61cb6f626cb0"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "67c1753b-1517-492c-80b9-358457d349e4", "eventImplId": "26af8183-ef05-4d3c-85b0-bc3868f598a4", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:callActivity": {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Erro<PERSON>", "id": "964fda4d-0c71-4009-8f93-666b98c2c476", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "458", "y": "142", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "81fa9e4c-872c-445f-8836-cd3dc9bd35ad", "ns16:outgoing": "59914810-e6fc-48ed-8090-f9ff47abdbc3", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Print barcode\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}}}}, "link": [{"name": "To <PERSON>ript Task", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.96135cf9-56ef-4146-865f-916def19c79f", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.0b38ba63-35a3-462b-8908-a56f6392b5be", "2025.0b38ba63-35a3-462b-8908-a56f6392b5be"], "endStateId": "Out", "toProcessItemId": ["2025.e52eabbc-1d89-406e-8b67-2fc707b52002", "2025.e52eabbc-1d89-406e-8b67-2fc707b52002"], "guid": "4292f5b6-95f3-4681-bef9-c0f896d73681", "versionId": "271bc8e9-82b3-4647-8c9d-c22f44d364e3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.59914810-e6fc-48ed-8090-f9ff47abdbc3", "processId": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.964fda4d-0c71-4009-8f93-666b98c2c476", "2025.964fda4d-0c71-4009-8f93-666b98c2c476"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.e52eabbc-1d89-406e-8b67-2fc707b52002", "2025.e52eabbc-1d89-406e-8b67-2fc707b52002"], "guid": "d1410ebe-65a4-49da-8cce-5d8b774cb6e7", "versionId": "a9a6acca-68f5-4ee8-8c27-14329ddf62e1", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}