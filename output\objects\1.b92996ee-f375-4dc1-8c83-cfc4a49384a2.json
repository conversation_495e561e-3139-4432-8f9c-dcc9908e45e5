{"id": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "versionId": "93427c98-30b1-4e9b-afd1-dd78819d8b88", "name": "Get Customer and Party Account List", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "Get Customer and Party Account List", "lastModified": "*************", "lastModifiedBy": "mohamed.reda", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.170f5ed2-bbef-4227-8a71-b9cd58a4d952", "2025.170f5ed2-bbef-4227-8a71-b9cd58a4d952"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "1d184576-7117-432d-82a7-0afea6f800dc", "versionId": "93427c98-30b1-4e9b-afd1-dd78819d8b88", "dependencySummary": "<dependencySummary id=\"bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50f8\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.4d3cabb9-26b7-4993-9849-dbb9c742ecc8\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"bc802a08-1359-490c-bec3-c5f021c6bdaf\"},{\"incoming\":[\"786d372c-b0af-460a-a533-074993a009ff\",\"e3cdf416-2aee-4e1e-b984-e01876c6ad6b\",\"af7be5a2-02e1-4151-8dec-65e0f7382d50\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":690,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-61f0\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"16260fa1-d1b7-4d54-a5a0-20d024d9397b\"},{\"targetRef\":\"170f5ed2-bbef-4227-8a71-b9cd58a4d952\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Initialization\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4d3cabb9-26b7-4993-9849-dbb9c742ecc8\",\"sourceRef\":\"bc802a08-1359-490c-bec3-c5f021c6bdaf\"},{\"startQuantity\":1,\"outgoing\":[\"021c2ee6-6896-4169-8ae6-cce05470244c\"],\"incoming\":[\"bee2e63c-6df3-415b-a9d4-27c03a83ae82\",\"f63bc299-953b-4aca-957a-269ba0b1cd60\"],\"extensionElements\":{\"postAssignmentScript\":[\"for (var i=0; i<tw.local.accountList.listLength; i++) {\\r\\n\\ttw.local.totalAccountList.insertIntoList(tw.local.totalAccountList.listLength, tw.local.accountList[i]);\\r\\n}\\r\\n\\r\\ntw.local.index = tw.local.index +1;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":364,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Get accounts\",\"dataInputAssociation\":[{\"targetRef\":\"2055.1a3552fa-7acb-43dc-9fd8-af0b031152e8\",\"assignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.cifs[tw.local.index]\"]}}]},{\"targetRef\":\"2055.ef963c07-a0e0-47de-a99a-995cd5146f82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.prefix\"]}}]},{\"targetRef\":\"2055.ddff36e5-3880-4ef4-a940-387a22f5e0b8\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user_id\"]}}]},{\"targetRef\":\"2055.246bbadd-892b-4d12-ad68-7d03cbc463dd\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.currentProcessInstanceID\"]}}]},{\"targetRef\":\"2055.ab1eb755-2de3-44ae-893e-3b078b4b594d\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"ODC Creation \\/ Amendment Process Details\\\"\"]}}]},{\"targetRef\":\"2055.72073a72-e4d4-4c67-af56-6dcd28abac8d\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.ee2392cb-9715-4e86-afa1-8376111abb77\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"0af23bd3-3aa4-45cc-9d94-4ad6af1baecd\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.accountList\"]}}],\"sourceRef\":[\"2055.081712aa-eb0f-473f-9a8c-8b128642a67b\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.144a896d-105b-40b0-bb38-944b8f8b858b\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerNo\"]}}],\"sourceRef\":[\"2055.40cbdc9b-1d23-40e9-a38b-c6801fc3982e\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.97292ff8-1040-4e8e-b19c-5cda00cb4c6a\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorCode\"]}}],\"sourceRef\":[\"2055.c40dc139-a328-4752-90f6-254db0bdb266\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.status\"]}}],\"sourceRef\":[\"2055.c62c3bc0-46ea-4519-9703-3a27676d6d87\"]}],\"calledElement\":\"1.bd127e8f-b948-40ef-a529-898ff4290d2a\"},{\"outgoing\":[\"786d372c-b0af-460a-a533-074993a009ff\",\"bee2e63c-6df3-415b-a9d4-27c03a83ae82\"],\"incoming\":[\"021c2ee6-6896-4169-8ae6-cce05470244c\"],\"default\":\"786d372c-b0af-460a-a533-074993a009ff\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":522,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Get accounts?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"d4a68312-3de8-4b0d-8773-7eb90b4d0cdd\"},{\"targetRef\":\"16260fa1-d1b7-4d54-a5a0-20d024d9397b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"786d372c-b0af-460a-a533-074993a009ff\",\"sourceRef\":\"d4a68312-3de8-4b0d-8773-7eb90b4d0cdd\"},{\"targetRef\":\"0af23bd3-3aa4-45cc-9d94-4ad6af1baecd\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.index\\t  <\\t  tw.local.cifs.listLength\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"bee2e63c-6df3-415b-a9d4-27c03a83ae82\",\"sourceRef\":\"d4a68312-3de8-4b0d-8773-7eb90b4d0cdd\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"0\"}]},\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"index\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.5cdc4e98-1f9f-430b-bf60-db6ef4a6c083\"},{\"outgoing\":[\"f63bc299-953b-4aca-957a-269ba0b1cd60\",\"e3cdf416-2aee-4e1e-b984-e01876c6ad6b\"],\"incoming\":[\"8ee6a338-1a92-4f99-803f-5c087bc221e2\"],\"default\":\"f63bc299-953b-4aca-957a-269ba0b1cd60\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"postAssignmentScript\":[\"tw.local.accountList = [];\"],\"nodeVisualInfo\":[{\"width\":32,\"x\":225,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"cifs exist?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2c46ded5-c36d-4ea8-ab55-aa51ced9526a\"},{\"targetRef\":\"0af23bd3-3aa4-45cc-9d94-4ad6af1baecd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Get accounts\",\"declaredType\":\"sequenceFlow\",\"id\":\"f63bc299-953b-4aca-957a-269ba0b1cd60\",\"sourceRef\":\"2c46ded5-c36d-4ea8-ab55-aa51ced9526a\"},{\"targetRef\":\"16260fa1-d1b7-4d54-a5a0-20d024d9397b\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.cifs.listLength\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true,\"customBendPoint\":[{\"x\":474,\"y\":14}]}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e3cdf416-2aee-4e1e-b984-e01876c6ad6b\",\"sourceRef\":\"2c46ded5-c36d-4ea8-ab55-aa51ced9526a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"CustomerCIF\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.be89bab1-477e-4542-8f7d-d3331587ae5f\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();\\nautoObject[0] = new tw.object.toolkit.NBEINTT.Account();\\nautoObject[0].accountNO = \\\"\\\";\\nautoObject[0].currencyCode = \\\"\\\";\\nautoObject[0].branchCode = \\\"\\\";\\nautoObject[0].balance = 0.0;\\nautoObject[0].typeCode = \\\"\\\";\\nautoObject[0].customerName = \\\"\\\";\\nautoObject[0].customerNo = \\\"\\\";\\nautoObject[0].frozen = false;\\nautoObject[0].dormant = false;\\nautoObject[0].noDebit = false;\\nautoObject[0].noCredit = false;\\nautoObject[0].postingAllowed = false;\\nautoObject[0].ibanAccountNumber = \\\"\\\";\\nautoObject[0].accountClassCode = \\\"\\\";\\nautoObject[0].balanceType = \\\"\\\";\\nautoObject[0].accountStatus = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"accountList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.0a429eaf-8eda-4830-8669-64c5d6d7df7c\"},{\"startQuantity\":1,\"outgoing\":[\"8ee6a338-1a92-4f99-803f-5c087bc221e2\"],\"incoming\":[\"2027.4d3cabb9-26b7-4993-9849-dbb9c742ecc8\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":93,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Initialization\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"170f5ed2-bbef-4227-8a71-b9cd58a4d952\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.totalAccountList = new tw.object.listOf.Account();\\r\\n\\r\\nfor (var i=tw.local.cifs.listLength-1; i>=0; i--) {\\r\\n\\tif (tw.local.cifs[i] == \\\"\\\" || tw.local.cifs[i] == null) {\\r\\n\\t\\ttw.local.cifs.removeIndex(i);\\r\\n\\t}\\r\\n}\\r\\n\\r\\n\"]}},{\"targetRef\":\"2c46ded5-c36d-4ea8-ab55-aa51ced9526a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To cifs exist?\",\"declaredType\":\"sequenceFlow\",\"id\":\"8ee6a338-1a92-4f99-803f-5c087bc221e2\",\"sourceRef\":\"170f5ed2-bbef-4227-8a71-b9cd58a4d952\"},{\"targetRef\":\"d4a68312-3de8-4b0d-8773-7eb90b4d0cdd\",\"extensionElements\":{\"endStateId\":[\"guid:45353b16cdfc415c:-1fa23e4a:188af536685:-15cd\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get accounts?\",\"declaredType\":\"sequenceFlow\",\"id\":\"021c2ee6-6896-4169-8ae6-cce05470244c\",\"sourceRef\":\"0af23bd3-3aa4-45cc-9d94-4ad6af1baecd\"},{\"startQuantity\":1,\"outgoing\":[\"af7be5a2-02e1-4151-8dec-65e0f7382d50\"],\"incoming\":[\"e3418699-0734-4456-8b41-173e0547a4f2\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":363,\"y\":205,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.serviceFlow.name\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"06c7d1f5-d191-441c-8196-5b4d46d409cd\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"parallelMultiple\":false,\"outgoing\":[\"e3418699-0734-4456-8b41-173e0547a4f2\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"f71bcb57-3337-4a5d-8d57-01e96a11ce0b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"2d0ed83b-1115-46d7-8cd1-4a01277f6415\",\"otherAttributes\":{\"eventImplId\":\"9f50c909-5cc4-47c9-8b69-f491fb226059\"}}],\"attachedToRef\":\"0af23bd3-3aa4-45cc-9d94-4ad6af1baecd\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":399,\"y\":114,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"df98375f-d44d-4be1-8601-c37beaaf2111\",\"outputSet\":{}},{\"targetRef\":\"06c7d1f5-d191-441c-8196-5b4d46d409cd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"e3418699-0734-4456-8b41-173e0547a4f2\",\"sourceRef\":\"df98375f-d44d-4be1-8601-c37beaaf2111\"},{\"targetRef\":\"16260fa1-d1b7-4d54-a5a0-20d024d9397b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"af7be5a2-02e1-4151-8dec-65e0f7382d50\",\"sourceRef\":\"06c7d1f5-d191-441c-8196-5b4d46d409cd\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.3e6321c1-d728-42d9-8112-239095ec2cc7\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"customerNo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.268af980-7c57-40f7-8943-6307de451ef6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorCode\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.312c546c-b56a-4ab4-817a-21aa411f774a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"status\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ce6653bc-b561-4474-869c-ef3bcf2faf39\"}],\"laneSet\":[{\"id\":\"9b0a8309-fd18-406a-8296-fd3fac3f01d0\",\"lane\":[{\"flowNodeRef\":[\"bc802a08-1359-490c-bec3-c5f021c6bdaf\",\"16260fa1-d1b7-4d54-a5a0-20d024d9397b\",\"0af23bd3-3aa4-45cc-9d94-4ad6af1baecd\",\"d4a68312-3de8-4b0d-8773-7eb90b4d0cdd\",\"2c46ded5-c36d-4ea8-ab55-aa51ced9526a\",\"170f5ed2-bbef-4227-8a71-b9cd58a4d952\",\"06c7d1f5-d191-441c-8196-5b4d46d409cd\",\"df98375f-d44d-4be1-8601-c37beaaf2111\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"b947dc08-a7b9-4131-a81b-ea8ad8037b69\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get Customer and Party Account List\",\"declaredType\":\"process\",\"id\":\"1.b92996ee-f375-4dc1-8c83-cfc4a49384a2\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"totalAccountList\",\"isCollection\":true,\"id\":\"2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe\",\"2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject[0] = \\\"********\\\";\\r\\nautoObject[1] = \\\"********\\\";\\r\\nautoObject[2] = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"cifs\",\"isCollection\":true,\"id\":\"2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "cifs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "parameterType": "1", "isArrayOf": "true", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6f49b211-76b4-4f29-84db-aa0dbedb79aa", "versionId": "d137a6d9-3813-4521-b026-ef711e848055"}, {"name": "totalAccountList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "parameterType": "2", "isArrayOf": "true", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "2c12c8b0-6383-446e-90ce-20fa5a83f0ca", "versionId": "2534ecc5-bba9-4d2b-913d-a8d9013d4c8a"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7644ff37-446d-4d6d-9dd3-41b234068214", "versionId": "b3768014-8982-422c-93bd-6e4d78c9062f"}], "processVariable": [{"name": "index", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5cdc4e98-1f9f-430b-bf60-db6ef4a6c083", "description": {"isNull": "true"}, "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "true", "defaultValue": "0", "guid": "849232a6-fc23-4f8b-98a0-1db621236f2b", "versionId": "35751b0d-e046-4a79-b4b9-6ccf73f98581"}, {"name": "CustomerCIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.be89bab1-477e-4542-8f7d-d3331587ae5f", "description": {"isNull": "true"}, "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5f4a8968-678d-4e5e-9ac6-1d124cff9d69", "versionId": "2ee04fc0-df4a-4790-8726-15c31bdaa6ae"}, {"name": "accountList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0a429eaf-8eda-4830-8669-64c5d6d7df7c", "description": {"isNull": "true"}, "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();\r\nautoObject[0] = new tw.object.toolkit.NBEINTT.Account();\r\nautoObject[0].accountNO = \"\";\r\nautoObject[0].currencyCode = \"\";\r\nautoObject[0].branchCode = \"\";\r\nautoObject[0].balance = 0.0;\r\nautoObject[0].typeCode = \"\";\r\nautoObject[0].customerName = \"\";\r\nautoObject[0].customerNo = \"\";\r\nautoObject[0].frozen = false;\r\nautoObject[0].dormant = false;\r\nautoObject[0].noDebit = false;\r\nautoObject[0].noCredit = false;\r\nautoObject[0].postingAllowed = false;\r\nautoObject[0].ibanAccountNumber = \"\";\r\nautoObject[0].accountClassCode = \"\";\r\nautoObject[0].balanceType = \"\";\r\nautoObject[0].accountStatus = \"\";\r\nautoObject", "guid": "12287e0e-f6c2-4da5-a901-d4cb55424bb6", "versionId": "4cf886c5-f018-4ec5-b7a1-94080415f3fb"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3e6321c1-d728-42d9-8112-239095ec2cc7", "description": {"isNull": "true"}, "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "03a7e0e5-0ba8-490b-9688-37820b092b7a", "versionId": "dacaae36-1ade-47a4-8479-02b97e94df04"}, {"name": "customerNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.268af980-7c57-40f7-8943-6307de451ef6", "description": {"isNull": "true"}, "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "26233dd6-6e1e-4252-a74b-1a56a7d2e765", "versionId": "cdb396ee-1f9e-4a9c-a94c-b7b07468c6d6"}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.312c546c-b56a-4ab4-817a-21aa411f774a", "description": {"isNull": "true"}, "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fbfcb9f0-85cf-4466-a2bf-27e87b624e61", "versionId": "48154358-22ee-474f-80db-5fbdc27b56e9"}, {"name": "status", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ce6653bc-b561-4474-869c-ef3bcf2faf39", "description": {"isNull": "true"}, "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d3578b56-0193-4c2e-af51-b2d1411883eb", "versionId": "123a0da7-0797-45be-9b3f-951f6797a456"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "cifs exist?", "tWComponentName": "Switch", "tWComponentId": "3013.0f9c75a3-3c46-47b3-a7a4-e5b29c408cc2", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-61f2", "versionId": "0794284f-6fc3-4b91-bb2d-729480695f5e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.cdb1a369-a5db-45c9-a234-cc7dfe3639d0", "processItemId": "2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "location": "2", "script": "tw.local.accountList = [];", "guid": "2aaa121d-00c3-43c2-8bbb-97202b1c6abf", "versionId": "b0414990-c179-4f9f-8185-84fc0b16c16c"}, "layoutData": {"x": "225", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.0f9c75a3-3c46-47b3-a7a4-e5b29c408cc2", "guid": "8b673164-f5d6-496b-b0d1-4260ae599a95", "versionId": "0a014efe-4c31-4599-8d8f-1e7acb920288", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.7b46f6b4-5e52-4bd4-ae9b-3f36a362beba", "switchId": "3013.0f9c75a3-3c46-47b3-a7a4-e5b29c408cc2", "seq": "1", "endStateId": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50f9", "condition": "tw.local.cifs.listLength\t  ==\t  0", "guid": "e2fc343f-6aed-4856-a631-a361e2bbf177", "versionId": "2e0fdc2b-9bf1-43f1-9dcc-47ced7e95961"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "Get accounts", "tWComponentName": "SubProcess", "tWComponentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.06c7d1f5-d191-441c-8196-5b4d46d409cd", "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-61f1", "versionId": "54e2bca2-55a8-4a7f-be7f-259e7b5610c9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.dbe1d812-9662-4548-a23e-739038b26a01", "processItemId": "2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "location": "2", "script": "for (var i=0; i<tw.local.accountList.listLength; i++) {\r\r\n\ttw.local.totalAccountList.insertIntoList(tw.local.totalAccountList.listLength, tw.local.accountList[i]);\r\r\n}\r\r\n\r\r\ntw.local.index = tw.local.index +1;", "guid": "0c013d91-d90d-48e1-99c9-f3ef754a7550", "versionId": "b8a05cfd-b44f-4bcb-8d47-5ae662afbadf"}, "layoutData": {"x": "364", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-6140", "errorHandlerItemId": "2025.06c7d1f5-d191-441c-8196-5b4d46d409cd", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.acabd684-cff9-4509-b556-93038da292b5", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.bd127e8f-b948-40ef-a529-898ff4290d2a", "guid": "ea9c1ebc-638c-4b63-9697-2569e04c0fa3", "versionId": "f38e3a7c-e715-4f7e-a1fb-2c37e821f84a", "parameterMapping": [{"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1207e3bb-8271-427a-825e-b0ce9ea0ea11", "processParameterId": "2055.ab1eb755-2de3-44ae-893e-3b078b4b594d", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "\"ODC Creation / Amendment Process Details\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "9c79f8e8-a039-45d7-855f-92db74a589ab", "versionId": "0bb13210-62ab-4ddd-8a14-b03b6f7afb8a", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.74c8478a-dc9f-413d-89de-5c52a0963a1c", "processParameterId": "2055.246bbadd-892b-4d12-ad68-7d03cbc463dd", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.system.currentProcessInstanceID", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "9c816e94-5ee7-4e4c-8e2a-8ee36448567d", "versionId": "0cde5994-ce8d-4c7e-83f0-8d18f009bd13", "description": {"isNull": "true"}}, {"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.879d0041-d1dd-4f7f-885a-3d218af522e6", "processParameterId": "2055.ddff36e5-3880-4ef4-a940-387a22f5e0b8", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.system.user_id", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ebaf5ec1-9935-4d26-a9b1-0e2aaac8c06f", "versionId": "11d49561-ab79-4ac0-9072-74e5fafa3501", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ba84a26d-1f7c-484a-8c06-84776cb2c983", "processParameterId": "2055.c40dc139-a328-4752-90f6-254db0bdb266", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.local.errorCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "dcaca974-4ef2-4822-bc23-281963b33152", "versionId": "161449e9-d66a-4011-a542-37fe36aff4d4", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.74a90dff-5bad-4e19-9404-de37701a3ed0", "processParameterId": "2055.ef963c07-a0e0-47de-a99a-995cd5146f82", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "4181199e-7be2-49ef-8cae-e080581041a6", "versionId": "21267bdf-28be-4862-81a0-c6c536935ca1", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a0f42d09-b757-4997-98e5-4c04c2a40893", "processParameterId": "2055.97292ff8-1040-4e8e-b19c-5cda00cb4c6a", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "f1d26b29-333b-49b3-9582-c9330da052d6", "versionId": "72beb9b0-0b31-4afc-b25d-21b90d4e468d", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d39fc47c-586f-4ff7-bc81-923ff51d78b4", "processParameterId": "2055.72073a72-e4d4-4c67-af56-6dcd28abac8d", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "3ddd7334-cb05-4335-9a69-cd191f2c4e21", "versionId": "7bbb0f33-ae92-476f-905d-ad4e208b8b83", "description": {"isNull": "true"}}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6f5db2f8-8944-4c39-a79b-6b829060ac54", "processParameterId": "2055.144a896d-105b-40b0-bb38-944b8f8b858b", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "bace1a1f-5bc4-4eb8-bcc8-03ed082ef72d", "versionId": "897b0eee-a4f5-4c82-80a6-66a52ec9c48a", "description": {"isNull": "true"}}, {"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4f9ce3ea-1e87-42b6-a1d1-f165e40fb7ac", "processParameterId": "2055.ee2392cb-9715-4e86-afa1-8376111abb77", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "bc353104-8b6c-4da6-abb5-b6c547b49921", "versionId": "981f18a0-cfaa-4b92-8bcc-973987b47ebe", "description": {"isNull": "true"}}, {"name": "status", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.663da0eb-eea8-4657-956e-98ad5224957b", "processParameterId": "2055.c62c3bc0-46ea-4519-9703-3a27676d6d87", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.local.status", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "5feaee44-da79-4832-a191-9a7ec9ae137e", "versionId": "9f257b61-15a8-408d-ac2d-82f2820f8d6e", "description": {"isNull": "true"}}, {"name": "customerNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5c4c64cf-95b7-4c3d-accc-09d328e9c511", "processParameterId": "2055.40cbdc9b-1d23-40e9-a38b-c6801fc3982e", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.local.customerNo", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "fd6c83fc-3c7b-40c2-bb8e-a6380b467d2b", "versionId": "b5850a2f-1676-4729-a9b6-78445ecc17b9", "description": {"isNull": "true"}}, {"name": "accountsList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.340f9d98-f957-4d3d-ac91-f18c77dd989b", "processParameterId": "2055.081712aa-eb0f-473f-9a8c-8b128642a67b", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.local.accountList", "classRef": "/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isList": "true", "isInput": "false", "guid": "e409ffbd-a27f-4a7b-8c6a-1b22543f954c", "versionId": "cd59e04e-9bc0-4a7f-b2ff-c5ef7d734994", "description": {"isNull": "true"}}, {"name": "customerNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b8b47f2b-3ce7-4149-8d33-8caffec50d10", "processParameterId": "2055.1a3552fa-7acb-43dc-9fd8-af0b031152e8", "parameterMappingParentId": "3012.acabd684-cff9-4509-b556-93038da292b5", "useDefault": "false", "value": "tw.local.cifs[tw.local.index]", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "5025c421-b1ea-44e7-b075-3f689b1804d2", "versionId": "e9d3a733-af27-46d2-929b-cfd6a45b25de", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.16260fa1-d1b7-4d54-a5a0-20d024d9397b", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.4690b479-6e62-4af0-840b-486ba5ab61e6", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-61f0", "versionId": "6c528248-8ffd-43ff-8da0-26f9cc9088d3", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "690", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.4690b479-6e62-4af0-840b-486ba5ab61e6", "haltProcess": "false", "guid": "56d57427-405c-4e50-9600-0ff9baf155a4", "versionId": "a197ee43-a9e4-4fdc-b37f-4e8db2f28dfd"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.170f5ed2-bbef-4227-8a71-b9cd58a4d952", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "Initialization", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.ac532012-2a5e-4b4e-8df4-c5db0526de8a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-6183", "versionId": "8c35c140-894e-4846-b50b-991e2d7ce5fc", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "93", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.ac532012-2a5e-4b4e-8df4-c5db0526de8a", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.totalAccountList = new tw.object.listOf.Account();\r\r\n\r\r\nfor (var i=tw.local.cifs.listLength-1; i>=0; i--) {\r\r\n\tif (tw.local.cifs[i] == \"\" || tw.local.cifs[i] == null) {\r\r\n\t\ttw.local.cifs.removeIndex(i);\r\r\n\t}\r\r\n}\r\r\n\r\r\n", "isRule": "false", "guid": "d96976ca-eab7-4677-b5c3-660bf7f638d2", "versionId": "777475bb-c40e-4e3c-9a03-9d0349cb5f55"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.06c7d1f5-d191-441c-8196-5b4d46d409cd", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.239bdafd-d6b7-4fa1-8044-50b0e92035e0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-6140", "versionId": "8d25b0d6-aacd-41cd-b52f-2c1b05a1504a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "363", "y": "205", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.239bdafd-d6b7-4fa1-8044-50b0e92035e0", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "1efe348f-4c1f-4cba-8c69-aaf2f59e7834", "versionId": "4f4f7028-0e3d-4427-b84e-d3724130cc61", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7efbbc24-f60b-4188-a200-1966fb7bb3dd", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.239bdafd-d6b7-4fa1-8044-50b0e92035e0", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "418fccd7-5c17-4b7c-a0d8-b05de129563d", "versionId": "8c765f55-f009-4db8-b4ce-32154c976cfc", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.86e0f6d6-f8af-48bb-8b73-bedc13a011f2", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.239bdafd-d6b7-4fa1-8044-50b0e92035e0", "useDefault": "false", "value": "tw.system.serviceFlow.name", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "caf68e69-a6ce-497d-b9cb-2da5d446e04f", "versionId": "d70bacef-32b3-4ce7-a11d-b180dddc6591", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.336fe227-7b51-4ac1-bea3-ca8abc483afc", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.239bdafd-d6b7-4fa1-8044-50b0e92035e0", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "3ec8ed68-b9e3-4e02-a3fa-8a53195cb844", "versionId": "f31cf465-b224-4159-8493-74cce4fe61fe", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "name": "Get accounts?", "tWComponentName": "Switch", "tWComponentId": "3013.b3b6cbbf-2ad3-4956-8f1e-63bef4d47649", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-61f3", "versionId": "d3554680-2d0a-4231-ad29-7451c5b12d2d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "522", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.b3b6cbbf-2ad3-4956-8f1e-63bef4d47649", "guid": "0ac45b23-0163-41bf-b73f-cdc91e24090d", "versionId": "66dc34a1-9e5a-49c7-b982-ee3deeb18b00", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.8718e23e-7b96-49d2-bb36-b7904b497eca", "switchId": "3013.b3b6cbbf-2ad3-4956-8f1e-63bef4d47649", "seq": "1", "endStateId": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50fa", "condition": "tw.local.index\t  <\t  tw.local.cifs.listLength", "guid": "5fb656de-b735-4ab3-ab99-95d4decb7fcf", "versionId": "d3068104-4e18-4f77-8fd0-7bac40714de6"}}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Customer and Party Account List", "id": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "cifs", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "id": "2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject[0] = \"********\";\r\r\nautoObject[1] = \"********\";\r\r\nautoObject[2] = \"\";\r\nautoObject", "useDefault": "false"}}}, "ns16:dataOutput": [{"name": "totalAccountList", "itemSubjectRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isCollection": "true", "id": "2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe", "2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5"]}}, "ns16:laneSet": {"id": "9b0a8309-fd18-406a-8296-fd3fac3f01d0", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "b947dc08-a7b9-4131-a81b-ea8ad8037b69", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["bc802a08-1359-490c-bec3-c5f021c6bdaf", "16260fa1-d1b7-4d54-a5a0-20d024d9397b", "0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "170f5ed2-bbef-4227-8a71-b9cd58a4d952", "06c7d1f5-d191-441c-8196-5b4d46d409cd", "df98375f-d44d-4be1-8601-c37beaaf2111"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "bc802a08-1359-490c-bec3-c5f021c6bdaf", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.4d3cabb9-26b7-4993-9849-dbb9c742ecc8"}, "ns16:endEvent": {"name": "End", "id": "16260fa1-d1b7-4d54-a5a0-20d024d9397b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "690", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-61f0"}, "ns16:incoming": ["786d372c-b0af-460a-a533-074993a009ff", "e3cdf416-2aee-4e1e-b984-e01876c6ad6b", "af7be5a2-02e1-4151-8dec-65e0f7382d50"]}, "ns16:sequenceFlow": [{"sourceRef": "bc802a08-1359-490c-bec3-c5f021c6bdaf", "targetRef": "170f5ed2-bbef-4227-8a71-b9cd58a4d952", "name": "To Initialization", "id": "2027.4d3cabb9-26b7-4993-9849-dbb9c742ecc8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "targetRef": "16260fa1-d1b7-4d54-a5a0-20d024d9397b", "name": "To End", "id": "786d372c-b0af-460a-a533-074993a009ff", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "targetRef": "0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "name": "Yes", "id": "bee2e63c-6df3-415b-a9d4-27c03a83ae82", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.index\t  <\t  tw.local.cifs.listLength", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "targetRef": "0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "name": "To Get accounts", "id": "f63bc299-953b-4aca-957a-269ba0b1cd60", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "targetRef": "16260fa1-d1b7-4d54-a5a0-20d024d9397b", "name": "To End", "id": "e3cdf416-2aee-4e1e-b984-e01876c6ad6b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "474", "y": "14"}, "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.cifs.listLength\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "170f5ed2-bbef-4227-8a71-b9cd58a4d952", "targetRef": "2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "name": "To cifs exist?", "id": "8ee6a338-1a92-4f99-803f-5c087bc221e2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "targetRef": "d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "name": "To Get accounts?", "id": "021c2ee6-6896-4169-8ae6-cce05470244c", "ns16:extensionElements": {"ns3:endStateId": "guid:45353b16cdfc415c:-1fa23e4a:188af536685:-15cd", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "df98375f-d44d-4be1-8601-c37beaaf2111", "targetRef": "06c7d1f5-d191-441c-8196-5b4d46d409cd", "name": "To Exception Handling", "id": "e3418699-0734-4456-8b41-173e0547a4f2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "06c7d1f5-d191-441c-8196-5b4d46d409cd", "targetRef": "16260fa1-d1b7-4d54-a5a0-20d024d9397b", "name": "To End", "id": "af7be5a2-02e1-4151-8dec-65e0f7382d50", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:callActivity": [{"calledElement": "1.bd127e8f-b948-40ef-a529-898ff4290d2a", "name": "Get accounts", "id": "0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "364", "y": "56", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:postAssignmentScript": "for (var i=0; i<tw.local.accountList.listLength; i++) {\r\r\n\ttw.local.totalAccountList.insertIntoList(tw.local.totalAccountList.listLength, tw.local.accountList[i]);\r\r\n}\r\r\n\r\r\ntw.local.index = tw.local.index +1;"}, "ns16:incoming": ["bee2e63c-6df3-415b-a9d4-27c03a83ae82", "f63bc299-953b-4aca-957a-269ba0b1cd60"], "ns16:outgoing": "021c2ee6-6896-4169-8ae6-cce05470244c", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.1a3552fa-7acb-43dc-9fd8-af0b031152e8", "ns16:assignment": {"ns16:from": {"_": "tw.local.cifs[tw.local.index]", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}}, {"ns16:targetRef": "2055.ef963c07-a0e0-47de-a99a-995cd5146f82", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.ddff36e5-3880-4ef4-a940-387a22f5e0b8", "ns16:assignment": {"ns16:from": {"_": "tw.system.user_id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.246bbadd-892b-4d12-ad68-7d03cbc463dd", "ns16:assignment": {"ns16:from": {"_": "tw.system.currentProcessInstanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.ab1eb755-2de3-44ae-893e-3b078b4b594d", "ns16:assignment": {"ns16:from": {"_": "\"ODC Creation / Amendment Process Details\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.72073a72-e4d4-4c67-af56-6dcd28abac8d", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.ee2392cb-9715-4e86-afa1-8376111abb77", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.081712aa-eb0f-473f-9a8c-8b128642a67b", "ns16:assignment": {"ns16:to": {"_": "tw.local.accountList", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"}}}, {"ns16:sourceRef": "2055.144a896d-105b-40b0-bb38-944b8f8b858b", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.40cbdc9b-1d23-40e9-a38b-c6801fc3982e", "ns16:assignment": {"ns16:to": {"_": "tw.local.customerNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.97292ff8-1040-4e8e-b19c-5cda00cb4c6a", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.c40dc139-a328-4752-90f6-254db0bdb266", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.c62c3bc0-46ea-4519-9703-3a27676d6d87", "ns16:assignment": {"ns16:to": {"_": "tw.local.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Exception Handling", "id": "06c7d1f5-d191-441c-8196-5b4d46d409cd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "363", "y": "205", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "e3418699-0734-4456-8b41-173e0547a4f2", "ns16:outgoing": "af7be5a2-02e1-4151-8dec-65e0f7382d50", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "tw.system.serviceFlow.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:exclusiveGateway": [{"default": "786d372c-b0af-460a-a533-074993a009ff", "name": "Get accounts?", "id": "d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "522", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "021c2ee6-6896-4169-8ae6-cce05470244c", "ns16:outgoing": ["786d372c-b0af-460a-a533-074993a009ff", "bee2e63c-6df3-415b-a9d4-27c03a83ae82"]}, {"default": "f63bc299-953b-4aca-957a-269ba0b1cd60", "name": "cifs exist?", "id": "2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "225", "y": "76", "width": "32", "height": "32"}, "ns3:postAssignmentScript": "tw.local.accountList = [];"}, "ns16:incoming": "8ee6a338-1a92-4f99-803f-5c087bc221e2", "ns16:outgoing": ["f63bc299-953b-4aca-957a-269ba0b1cd60", "e3cdf416-2aee-4e1e-b984-e01876c6ad6b"]}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "index", "id": "2056.5cdc4e98-1f9f-430b-bf60-db6ef4a6c083", "ns16:extensionElements": {"ns3:defaultValue": {"_": "0", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "CustomerCIF", "id": "2056.be89bab1-477e-4542-8f7d-d3331587ae5f"}, {"itemSubjectRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isCollection": "true", "name": "accountList", "id": "2056.0a429eaf-8eda-4830-8669-64c5d6d7df7c", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.NBEINTT.Account();\r\nautoObject[0] = new tw.object.toolkit.NBEINTT.Account();\r\nautoObject[0].accountNO = \"\";\r\nautoObject[0].currencyCode = \"\";\r\nautoObject[0].branchCode = \"\";\r\nautoObject[0].balance = 0.0;\r\nautoObject[0].typeCode = \"\";\r\nautoObject[0].customerName = \"\";\r\nautoObject[0].customerNo = \"\";\r\nautoObject[0].frozen = false;\r\nautoObject[0].dormant = false;\r\nautoObject[0].noDebit = false;\r\nautoObject[0].noCredit = false;\r\nautoObject[0].postingAllowed = false;\r\nautoObject[0].ibanAccountNumber = \"\";\r\nautoObject[0].accountClassCode = \"\";\r\nautoObject[0].balanceType = \"\";\r\nautoObject[0].accountStatus = \"\";\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.3e6321c1-d728-42d9-8112-239095ec2cc7"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "customerNo", "id": "2056.268af980-7c57-40f7-8943-6307de451ef6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorCode", "id": "2056.312c546c-b56a-4ab4-817a-21aa411f774a"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "status", "id": "2056.ce6653bc-b561-4474-869c-ef3bcf2faf39"}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Initialization", "id": "170f5ed2-bbef-4227-8a71-b9cd58a4d952", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "93", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.4d3cabb9-26b7-4993-9849-dbb9c742ecc8", "ns16:outgoing": "8ee6a338-1a92-4f99-803f-5c087bc221e2", "ns16:script": "tw.local.totalAccountList = new tw.object.listOf.Account();\r\r\n\r\r\nfor (var i=tw.local.cifs.listLength-1; i>=0; i--) {\r\r\n\tif (tw.local.cifs[i] == \"\" || tw.local.cifs[i] == null) {\r\r\n\t\ttw.local.cifs.removeIndex(i);\r\r\n\t}\r\r\n}\r\r\n\r\r\n"}, "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "parallelMultiple": "false", "name": "Error", "id": "df98375f-d44d-4be1-8601-c37beaaf2111", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "399", "y": "114", "width": "24", "height": "24"}}, "ns16:outgoing": "e3418699-0734-4456-8b41-173e0547a4f2", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "f71bcb57-3337-4a5d-8d57-01e96a11ce0b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "2d0ed83b-1115-46d7-8cd1-4a01277f6415", "eventImplId": "9f50c909-5cc4-47c9-8b69-f491fb226059", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": [{"name": "To Get accounts?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.021c2ee6-6896-4169-8ae6-cce05470244c", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd"], "endStateId": "guid:45353b16cdfc415c:-1fa23e4a:188af536685:-15cd", "toProcessItemId": ["2025.d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "2025.d4a68312-3de8-4b0d-8773-7eb90b4d0cdd"], "guid": "79da679c-aa79-4753-9faa-b7e88c3c53da", "versionId": "023360fd-0e59-4fb7-a6da-d65d722e9ac3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get accounts", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f63bc299-953b-4aca-957a-269ba0b1cd60", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd"], "guid": "dfbabe2e-9278-4d59-a7a4-97fc2298a19b", "versionId": "56835efe-341a-49e8-9a58-66f114ae0c7b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.bee2e63c-6df3-415b-a9d4-27c03a83ae82", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "2025.d4a68312-3de8-4b0d-8773-7eb90b4d0cdd"], "endStateId": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50fa", "toProcessItemId": ["2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd", "2025.0af23bd3-3aa4-45cc-9d94-4ad6af1baecd"], "guid": "425d5663-8130-4acd-9f02-5ec740795d02", "versionId": "69349160-ad0b-4edd-8273-dcd2f6e3b984", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.af7be5a2-02e1-4151-8dec-65e0f7382d50", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.06c7d1f5-d191-441c-8196-5b4d46d409cd", "2025.06c7d1f5-d191-441c-8196-5b4d46d409cd"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.16260fa1-d1b7-4d54-a5a0-20d024d9397b", "2025.16260fa1-d1b7-4d54-a5a0-20d024d9397b"], "guid": "d3ea175b-d77c-45be-9765-93c28feb4b71", "versionId": "6d451be0-fde9-4337-95b7-161fbdb16967", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.786d372c-b0af-460a-a533-074993a009ff", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.d4a68312-3de8-4b0d-8773-7eb90b4d0cdd", "2025.d4a68312-3de8-4b0d-8773-7eb90b4d0cdd"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.16260fa1-d1b7-4d54-a5a0-20d024d9397b", "2025.16260fa1-d1b7-4d54-a5a0-20d024d9397b"], "guid": "443d9d5d-9b96-4dbf-8e47-448e7b830e24", "versionId": "98a58039-78c6-4a76-8ff8-41e8df204589", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e3cdf416-2aee-4e1e-b984-e01876c6ad6b", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a"], "endStateId": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-50f9", "toProcessItemId": ["2025.16260fa1-d1b7-4d54-a5a0-20d024d9397b", "2025.16260fa1-d1b7-4d54-a5a0-20d024d9397b"], "guid": "204f6725-4c39-40e3-a892-4411542a15bd", "versionId": "e4570366-9338-41c7-8481-fdcbfb9eca3a", "layoutData": {"controlPoints": {"controlPoint": {"x": "474", "y": "14"}}, "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To cifs exist?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.8ee6a338-1a92-4f99-803f-5c087bc221e2", "processId": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.170f5ed2-bbef-4227-8a71-b9cd58a4d952", "2025.170f5ed2-bbef-4227-8a71-b9cd58a4d952"], "endStateId": "Out", "toProcessItemId": ["2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a", "2025.2c46ded5-c36d-4ea8-ab55-aa51ced9526a"], "guid": "2522356a-f1ce-47a1-a03a-94cb3aa493aa", "versionId": "f6b1ebec-e2e6-47b4-8f39-90a02b3f824d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}