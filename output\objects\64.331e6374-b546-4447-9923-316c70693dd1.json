{"id": "64.331e6374-b546-4447-9923-316c70693dd1", "versionId": "0db3b06a-9f16-4991-8ced-d67558109b4c", "name": "temp", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "//alert(this.context.viewid)", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.mandatory2 = function (me){\r\r\n//\tme.setEnabled(false,true)\r\r\n\tme.setValid(false,\"shit\");\r\r\n//\tif(me.getData() == null || me.getData() == undefined){\r\r\n//\t\tme.setValid(false,\"shit\");\r\r\n//\t\treturn false\r\r\n//\t}else{\r\r\n//\t\tme.setValid(true);\r\r\n//\t\treturn true\r\r\n//\t} \r\r\n}\r\r\n\r\r\n//---------------------------------------Validation Library------------------------------------\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nvar validationList = [];\r\r\nthis.addError = function (path, message) {\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n\t// return validationList;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the field is null 'Mandatory', \r\r\n   message is OPTIONAL!! , DEFAULT is 'This Field Is Mandatory',\r\r\n   EX: mandatory(tw.local.name , 'tw.local.name', 'validation message') */\r\r\n\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\tif (!message) {\r\r\n\t\tmessage = \"This Field Is Mandatory\";\r\r\n\t}\r\r\n\r\r\n\tif (value == null) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.mandatoryList = function (mandatoryList) {\r\r\n\tmandatoryList.forEach((element) => {\r\r\n\t\tthis.mandatory(element[0], element[1], element[2]);\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past and the last variable is OPTIONAL to exclude today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude) {\r\r\n\t\tif (value != null && value < checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value <= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude && exclude === true) {\r\r\n\t\tif (value != null && value > checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value >= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (value && value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (value && value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.exactLength = function (value, path, len, message) {\r\r\n\tif (value && value.length != len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (value && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (value && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Validates the before and after decimal length (even if string).\r\r\n   message is OPTIONAL , DEFAULT `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it` \r\r\n   Ex: validateDecimal (tw.local.decimal, 'tw.local.decimal', 10, 6, 'validation message')*/\r\r\nthis.validateDecimal = function (value, path, beforeDecimal, afterDecimal, message) {\r\r\n\tif (!value) return;\r\r\n\tif (!message || message == \"\") {\r\r\n\t\tmessage = `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it`;\r\r\n\t}\r\r\n\t// Handle string or number input\r\r\n\tif (typeof value === \"number\") {\r\r\n\t\tvalue = value.toString();\r\r\n\t}\r\r\n\t// Regex to check overall format\r\r\n\tconst regex = new RegExp(\"^\\\\d{1,\" + beforeDecimal + \"}\\\\.?\\\\d{0,\" + afterDecimal + \"}$\");\r\r\n\r\r\n\tif (regex.test(value) == false) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\tthis.setErrorList();\r\r\n};\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.331e6374-b546-4447-9923-316c70693dd1", "name": "temp", "lastModified": "1709052338850", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.331e6374-b546-4447-9923-316c70693dd1", "isTemplate": "false", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ContentBox\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>9777338d-dfca-4c72-8791-79de42ed3e75</ns2:id><ns2:layoutItemId>ContentBox1</ns2:layoutItemId></ns2:layoutItem><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>65152c0b-ec93-4812-8eb3-dd4e2a4745d0</ns2:id><ns2:layoutItemId>Validation_Helper1</ns2:layoutItemId><ns2:configData><ns2:id>fd8a05f2-484e-4b3f-8caa-e5f2189dca38</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Validation Helper</ns2:value></ns2:configData><ns2:configData><ns2:id>409e9e29-0886-46a1-832a-ea963998b464</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>cdd1b7ee-0ff2-41f7-858e-0bef37f6281b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>32c13548-605b-45be-869b-60c821e940ee</ns2:id><ns2:optionName>disableSubmit</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>fad8d1db-ba50-46c2-80de-a36016370cb9</ns2:id><ns2:optionName>stop</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>577b0f63-e572-4748-855e-33aebd048e7a</ns2:id><ns2:optionName>runTimeValid</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:viewUUID>64.72428c7b-aa19-4400-bea7-59743c5442cc</ns2:viewUUID></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "false", "loadJsFunction": "//alert(this.context.viewid)", "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": {"isNull": "true"}, "changeJsFunction": {"isNull": "true"}, "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "guid:709ea623f6099cbd:-68e5c70f:18db33126a5:7ba8", "versionId": "0db3b06a-9f16-4991-8ced-d67558109b4c", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "inlineScript": {"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.49798dac-8d44-4bbf-ac79-37b64041cdce", "coachViewId": "64.331e6374-b546-4447-9923-316c70693dd1", "scriptType": "JS", "scriptBlock": "this.mandatory2 = function (me){\r\r\n//\tme.setEnabled(false,true)\r\r\n\tme.setValid(false,\"shit\");\r\r\n//\tif(me.getData() == null || me.getData() == undefined){\r\r\n//\t\tme.setValid(false,\"shit\");\r\r\n//\t\treturn false\r\r\n//\t}else{\r\r\n//\t\tme.setValid(true);\r\r\n//\t\treturn true\r\r\n//\t} \r\r\n}\r\r\n\r\r\n//---------------------------------------Validation Library------------------------------------\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nvar validationList = [];\r\r\nthis.addError = function (path, message) {\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n\t// return validationList;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the field is null 'Mandatory', \r\r\n   message is OPTIONAL!! , DEFAULT is 'This Field Is Mandatory',\r\r\n   EX: mandatory(tw.local.name , 'tw.local.name', 'validation message') */\r\r\n\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\tif (!message) {\r\r\n\t\tmessage = \"This Field Is Mandatory\";\r\r\n\t}\r\r\n\r\r\n\tif (value == null) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.mandatoryList = function (mandatoryList) {\r\r\n\tmandatoryList.forEach((element) => {\r\r\n\t\tthis.mandatory(element[0], element[1], element[2]);\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past and the last variable is OPTIONAL to exclude today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude) {\r\r\n\t\tif (value != null && value < checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value <= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude && exclude === true) {\r\r\n\t\tif (value != null && value > checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value >= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (value && value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (value && value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.exactLength = function (value, path, len, message) {\r\r\n\tif (value && value.length != len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (value && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (value && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Validates the before and after decimal length (even if string).\r\r\n   message is OPTIONAL , DEFAULT `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it` \r\r\n   Ex: validateDecimal (tw.local.decimal, 'tw.local.decimal', 10, 6, 'validation message')*/\r\r\nthis.validateDecimal = function (value, path, beforeDecimal, afterDecimal, message) {\r\r\n\tif (!value) return;\r\r\n\tif (!message || message == \"\") {\r\r\n\t\tmessage = `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it`;\r\r\n\t}\r\r\n\t// Handle string or number input\r\r\n\tif (typeof value === \"number\") {\r\r\n\t\tvalue = value.toString();\r\r\n\t}\r\r\n\t// Regex to check overall format\r\r\n\tconst regex = new RegExp(\"^\\\\d{1,\" + beforeDecimal + \"}\\\\.?\\\\d{0,\" + afterDecimal + \"}$\");\r\r\n\r\r\n\tif (regex.test(value) == false) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\tthis.setErrorList();\r\r\n};\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n", "seq": "0", "description": "", "guid": "ff780c24-d51d-4dc7-8a4b-52f30b868094", "versionId": "85e06e8b-82c4-440a-af98-24fec9092c09"}}}}, "hasDetails": true}