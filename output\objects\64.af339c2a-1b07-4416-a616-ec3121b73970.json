{"id": "64.af339c2a-1b07-4416-a616-ec3121b73970", "versionId": "ee583e61-8bd1-4df8-a72c-7697f5b2d063", "name": "Financial Details Trade FO CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetailsFO", "configOptions": ["financialDetailsFOVis", "requestType", "act3VIS", "multiTenorDatesVIS", "todayDate", "documentAmount", "amountAdvanced", "columns"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.seColumnVis = function(me) {\r\r\n\tvar tableColumn=[\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"N\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false}\r\r\n\t];\r\r\n\t\r\r\nvar columnss2 = [\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 0,\r\r\n        type: \"Date_Time_Picker\",\r\r\n        label: \"Installment Date التاريخ\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 1,\r\r\n        type: \"Date_Time_Picker\",\r\r\n        label: \"Mat Date\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 2,\r\r\n        type: \"Integer\",\r\r\n        label: \"Tenor Days\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 3,\r\r\n        type: \"Decimal\",\r\r\n        label: \"Installment Amount المبلغ\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 4,\r\r\n        type: \"Decimal\",\r\r\n        label: \"Rebate\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    }\r\r\n]\r\r\n\t\r\r\n//\tthis.context.options.columns.set(\"value\", tableColumn);\r\r\n\tme.setColumns(columnss2);\r\r\n//\tconsole.log( JSON.stringify(me.getColumns()));\r\r\n//\tme.refresh();\r\r\n\t\r\r\n}"}]}, "hasDetails": true}