<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a6205b87-57cc-47bb-abfc-edff0743b08e" name="Send Mail to CAD Team">
        <lastModified>1700585612194</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.3aeec71b-454b-49b8-890b-2d3f025af4b0</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
        <errorHandlerItemId>2025.*************-467d-8819-583abef229ab</errorHandlerItemId>
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</description>
        <guid>9d0f0185-744b-4f55-9b9c-1926cfe8c208</guid>
        <versionId>35c7c37e-292f-400c-bfe4-ad2eaa23ecc9</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-29bd" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.24f7c3c4-2ff4-47ca-944f-3c373b24cbd7"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":40,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"e3e18d94-e83e-417b-aafe-17a15ee3e748"},{"incoming":["42fbf9a2-b85d-409c-8200-c4ad45e7b52f","b1adb7f1-c762-45ec-8695-5da03834a8b3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":470,"y":40,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:221a"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"cadaa4ff-**************-cf85c3ee89c1"},{"targetRef":"3aeec71b-454b-49b8-890b-2d3f025af4b0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init Data","declaredType":"sequenceFlow","id":"2027.24f7c3c4-2ff4-47ca-944f-3c373b24cbd7","sourceRef":"e3e18d94-e83e-417b-aafe-17a15ee3e748"},{"startQuantity":1,"outgoing":["949fe970-849e-48dd-ac9f-6b663f623034"],"incoming":["2027.24f7c3c4-2ff4-47ca-944f-3c373b24cbd7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":106,"y":17,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"3aeec71b-454b-49b8-890b-2d3f025af4b0","scriptFormat":"text\/x-javascript","script":{"content":["\/\/Set Mail Subject\r\ntw.local.subject = \"ODC Request No. \"+tw.local.odcRequest.appInfo.instanceID\r\n\t\t\t+\" for Customer \"+tw.local.odcRequest.CustomerInfo.customerName\r\n\t\t\t+\" is collected \"\r\n\t\t\t+\"\u0637\u0644\u0628 \u0627\u0644\u062a\u062d\u0635\u064a\u0644 \u0627\u0644\u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631 \u0631\u0642\u0645 \"+tw.local.odcRequest.appInfo.instanceID\r\n\t\t\t+ \" \u0644\u0644\u0639\u0645\u064a\u0644 \"+tw.local.odcRequest.CustomerInfo.customerName\r\n\t\t\t+\"\u062a\u0645 \u062a\u062d\u0635\u064a\u0644\u0647\";\r\n\r\n\r\n"]}},{"startQuantity":1,"outgoing":["42fbf9a2-b85d-409c-8200-c4ad45e7b52f"],"incoming":["67089f7c-a6e3-43d0-81d9-c3f7e67044ab"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":341,"y":17,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Send Mail","dataInputAssociation":[{"targetRef":"2055.a7c74b41-811f-4581-94ef-69a84c74eb84","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]},{"targetRef":"2055.ae81d59d-2fde-4526-9476-d0598d6e8472","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.subject"]}}]},{"targetRef":"2055.6d9bd911-88b8-4ea3-8823-421a4f690290","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.msgBody"]}}]},{"targetRef":"2055.1da05789-2131-46bc-aacf-34d84ca37def","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailDebugMode"]}}]},{"targetRef":"2055.20348cf5-023e-4d3a-826e-5b92143ec224","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]},{"targetRef":"2055.268afc2e-a651-49ef-8704-9a6ff22065c6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"3b2bf338-1a51-49f5-8e47-26a6c7cca3cf","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.ec26c46c-d70b-4881-98d8-40e694dd7362"]}],"calledElement":"1.338e9f4d-8538-4ceb-a155-c288604435d4"},{"targetRef":"cadaa4ff-**************-cf85c3ee89c1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2970"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"42fbf9a2-b85d-409c-8200-c4ad45e7b52f","sourceRef":"3b2bf338-1a51-49f5-8e47-26a6c7cca3cf"},{"targetRef":"1f6160a2-a666-45ca-8f72-21901d59e49e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Mail Body","declaredType":"sequenceFlow","id":"949fe970-849e-48dd-ac9f-6b663f623034","sourceRef":"3aeec71b-454b-49b8-890b-2d3f025af4b0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailDebugMode","isCollection":false,"declaredType":"dataObject","id":"2056.647a4741-ee6e-4a1d-a8d7-057074aa7167"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"msgBody","isCollection":false,"declaredType":"dataObject","id":"2056.a9254194-82fe-47fd-ae4d-caf13a59dc74"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"subject","isCollection":false,"declaredType":"dataObject","id":"2056.00c5395a-8eda-4395-a3c4-c278fcd25b02"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"declaredType":"dataObject","id":"2056.3d07a861-fafd-4e49-8584-4d2e3df87a49"},{"startQuantity":1,"outgoing":["67089f7c-a6e3-43d0-81d9-c3f7e67044ab"],"incoming":["949fe970-849e-48dd-ac9f-6b663f623034"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":221,"y":17,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["\/\/tw.local.odcRequest.BasicDetails.Invoice[0]."]},"name":"Set Mail Body","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1f6160a2-a666-45ca-8f72-21901d59e49e","scriptFormat":"text\/plain","script":{"content":["tw.local.msgBody\n&lt;html&gt;\r\n\t&lt;p dir=\"rtl\" lang=\"ar\"&gt;\t\r\n\t\u0627\u0644\u0633\u064a\u062f \/ \u0627\u0644\u0633\u064a\u062f\u0629 &lt;\/br&gt;&lt;\/br&gt;\r\n\t\u0631\u062c\u0627\u0621 \u0627\u0644\u0639\u0644\u0645 \u0628\u0623\u0646\u0647 \u062a\u0645 \u0627\u0636\u0627\u0641\u0629 \u0645\u0628\u0644\u063a  &lt;#=tw.local.odcRequest.FinancialDetailsBR.currency.value #&gt; &lt;#=tw.local.odcRequest.FinancialDetailsBR.amountAdvanced #&gt; \u0631\u0642\u0645   &lt;#=tw.local.odcRequest.cif #&gt;  \u0644\u0644\u0639\u0645\u064a\u0644   &lt;#=tw.local.odcRequest.CustomerInfo.customerName #&gt; \r\n\t\u060c \u062d\u0635\u064a\u0644\u0629 \u0648\u0627\u0631\u062f\u0629 \u0639\u0646 \u0645\u0633\u062a\u0646\u062f \u0634\u062d\u0646 \u062a\u0635\u062f\u064a\u0631   &lt;#=tw.local.odcRequest.BasicDetails.flexCubeContractNo #&gt;\r\n\t&lt;ul  dir=\"rtl\" lang=\"ar\"&gt;\r\n\t\t&lt;li&gt;&lt;b&gt;\u062a\u0627\u0631\u064a\u062e \u0627\u0644\u062d\u0642: &lt;\/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.ContractLiquidation.creditValueDate#&gt;&lt;\/em&gt;&lt;\/li&gt;\r\n\t\t&lt;li&gt;&lt;b&gt;\u0631\u0642\u0645 \u0627\u0644\u0641\u0627\u062a\u0648\u0631\u0629: &lt;\/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo#&gt;&lt;\/em&gt;&lt;\/li&gt;\r\n\t\t&lt;li&gt;&lt;b&gt;\u0631\u0642\u0645 \u0628\u0648\u0644\u064a\u0635\u0629 \u0627\u0644\u0634\u062d\u0646: &lt;\/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef #&gt;&lt;\/em&gt;&lt;\/li&gt;\r\n\t&lt;\/ul&gt; \r\n\t&lt;\/p&gt;\r\n\t&lt;b&gt; &lt;p dir=\"rtl\"; lang=\"ar\"; style=\"color:red;\"&gt;\t \u0627\u0644\u0631\u062c\u0627\u0621 \u0639\u062f\u0645 \u0627\u0644\u0631\u062f \u0639\u0644\u0649 \u0647\u0630\u0647 \u0627\u0644\u0631\u0633\u0627\u0644\u0629. \u0647\u0630\u0627 \u0628\u0631\u064a\u062f \u0625\u0644\u0643\u062a\u0631\u0648\u0646\u064a \u062a\u0645 \u0625\u0646\u0634\u0627\u0624\u0647 \u062a\u0644\u0642\u0627\u0626\u064a\u064b\u0627.  &lt;\/p&gt;&lt;\/b&gt;\r\n&lt;\/html&gt; \r\n\t \r\n\t \r\n "]}},{"targetRef":"3b2bf338-1a51-49f5-8e47-26a6c7cca3cf","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Service","declaredType":"sequenceFlow","id":"67089f7c-a6e3-43d0-81d9-c3f7e67044ab","sourceRef":"1f6160a2-a666-45ca-8f72-21901d59e49e"},{"startQuantity":1,"outgoing":["b1adb7f1-c762-45ec-8695-5da03834a8b3"],"incoming":["656f5a27-1a50-40b3-8b59-29a88e473e7e"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":343,"y":108,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exp. Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Send Mail to CAD Team\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"*************-467d-8819-583abef229ab","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"parallelMultiple":false,"outgoing":["656f5a27-1a50-40b3-8b59-29a88e473e7e"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"02325e1d-fdbd-47bb-83c7-97d868ca490d","otherAttributes":{"eventImplId":"81dd27f5-ad20-4088-8976-b74bae3c4d14"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":260,"y":131,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error Event","declaredType":"intermediateCatchEvent","id":"676aba80-f575-4678-8d55-bdbea4af9c39"},{"targetRef":"*************-467d-8819-583abef229ab","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exp. Handling","declaredType":"sequenceFlow","id":"656f5a27-1a50-40b3-8b59-29a88e473e7e","sourceRef":"676aba80-f575-4678-8d55-bdbea4af9c39"},{"targetRef":"cadaa4ff-**************-cf85c3ee89c1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"b1adb7f1-c762-45ec-8695-5da03834a8b3","sourceRef":"*************-467d-8819-583abef229ab"}],"laneSet":[{"id":"1ce98c9e-b211-4aac-ad97-11b5c00168af","lane":[{"flowNodeRef":["e3e18d94-e83e-417b-aafe-17a15ee3e748","cadaa4ff-**************-cf85c3ee89c1","3aeec71b-454b-49b8-890b-2d3f025af4b0","3b2bf338-1a51-49f5-8e47-26a6c7cca3cf","1f6160a2-a666-45ca-8f72-21901d59e49e","*************-467d-8819-583abef229ab","676aba80-f575-4678-8d55-bdbea4af9c39"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":413}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"dcaa0884-526a-428d-9966-007ed504c14f","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"content":["Escalation mail service:\u00a0&lt;div&gt;\u00a0 \u00a0this service is running when the task has delay to be done\u00a0&lt;\/div&gt;"],"textFormat":"text\/plain"}],"name":"Send Mail to CAD Team","declaredType":"process","id":"1.a6205b87-57cc-47bb-abfc-edff0743b08e","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"inputSet":[{"dataInputRefs":["2055.8510e459-4375-44f2-afb0-c0b98b03ee89","2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1"]}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.8510e459-4375-44f2-afb0-c0b98b03ee89"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"id":"2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8510e459-4375-44f2-afb0-c0b98b03ee89</processParameterId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>050aec05-16d9-4e3a-8862-1bd5a1e5856d</guid>
            <versionId>86af8ae0-0639-4d2c-977a-2382e64788dd</versionId>
        </processParameter>
        <processParameter name="mailTo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1</processParameterId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>538a1662-6a81-4299-9708-6824bcf45828</guid>
            <versionId>94caa402-1eb5-499c-adab-64f92d42a95c</versionId>
        </processParameter>
        <processVariable name="mailDebugMode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.647a4741-ee6e-4a1d-a8d7-057074aa7167</processVariableId>
            <description isNull="true" />
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dde64f13-4dca-4cb1-b7aa-09c75911edd4</guid>
            <versionId>7e335302-2da8-4c31-869d-179bd9f286a5</versionId>
        </processVariable>
        <processVariable name="msgBody">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a9254194-82fe-47fd-ae4d-caf13a59dc74</processVariableId>
            <description isNull="true" />
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e8587c8e-3d20-48d9-b69b-646a4e0ac409</guid>
            <versionId>70a2b00f-e8f5-437f-a3b3-239bc839c3ca</versionId>
        </processVariable>
        <processVariable name="subject">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.00c5395a-8eda-4395-a3c4-c278fcd25b02</processVariableId>
            <description isNull="true" />
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>83ea2069-c4f7-4f0e-8fb2-08fee4a09d2d</guid>
            <versionId>5c7a8e44-f119-45de-9dcb-09052d4b83d1</versionId>
        </processVariable>
        <processVariable name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3d07a861-fafd-4e49-8584-4d2e3df87a49</processVariableId>
            <description isNull="true" />
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4db947a9-6440-4581-bdd0-626b0930bfae</guid>
            <versionId>7f5146d8-8b65-492c-8b0d-ca57e4b4deaf</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cadaa4ff-**************-cf85c3ee89c1</processItemId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.84d9b986-2c23-489d-a79b-412e97569145</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:221a</guid>
            <versionId>149d7dda-f675-4b31-9c7c-18442dbd0ec9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="470" y="40">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.84d9b986-2c23-489d-a79b-412e97569145</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>cbcd071a-80d0-47fd-bebd-81a916bfad59</guid>
                <versionId>0f76c7e5-7630-45c7-9f6b-5614a1e2bfde</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1f6160a2-a666-45ca-8f72-21901d59e49e</processItemId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <name>Set Mail Body</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.42728982-73f0-472b-8b3f-4f3d64387a4d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:221c</guid>
            <versionId>398f1bed-03ed-4bdc-a201-e20eb91d184e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.57254c70-94ef-44a0-a401-47d9fc2a61c5</processItemPrePostId>
                <processItemId>2025.1f6160a2-a666-45ca-8f72-21901d59e49e</processItemId>
                <location>1</location>
                <script>//tw.local.odcRequest.BasicDetails.Invoice[0].</script>
                <guid>6c10108f-e080-42ba-baa2-cccaa26e95f0</guid>
                <versionId>33fc8d0f-6113-40ce-a419-a836867d4af3</versionId>
            </processPrePosts>
            <layoutData x="221" y="17">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.42728982-73f0-472b-8b3f-4f3d64387a4d</scriptId>
                <scriptTypeId>128</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.msgBody
&lt;html&gt;&#xD;
	&lt;p dir="rtl" lang="ar"&gt;	&#xD;
	السيد / السيدة &lt;/br&gt;&lt;/br&gt;&#xD;
	رجاء العلم بأنه تم اضافة مبلغ  &lt;#=tw.local.odcRequest.FinancialDetailsBR.currency.value #&gt; &lt;#=tw.local.odcRequest.FinancialDetailsBR.amountAdvanced #&gt; رقم   &lt;#=tw.local.odcRequest.cif #&gt;  للعميل   &lt;#=tw.local.odcRequest.CustomerInfo.customerName #&gt; &#xD;
	، حصيلة واردة عن مستند شحن تصدير   &lt;#=tw.local.odcRequest.BasicDetails.flexCubeContractNo #&gt;&#xD;
	&lt;ul  dir="rtl" lang="ar"&gt;&#xD;
		&lt;li&gt;&lt;b&gt;تاريخ الحق: &lt;/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.ContractLiquidation.creditValueDate#&gt;&lt;/em&gt;&lt;/li&gt;&#xD;
		&lt;li&gt;&lt;b&gt;رقم الفاتورة: &lt;/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo#&gt;&lt;/em&gt;&lt;/li&gt;&#xD;
		&lt;li&gt;&lt;b&gt;رقم بوليصة الشحن: &lt;/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef #&gt;&lt;/em&gt;&lt;/li&gt;&#xD;
	&lt;/ul&gt; &#xD;
	&lt;/p&gt;&#xD;
	&lt;b&gt; &lt;p dir="rtl"; lang="ar"; style="color:red;"&gt;	 الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.  &lt;/p&gt;&lt;/b&gt;&#xD;
&lt;/html&gt; &#xD;
	 &#xD;
	 &#xD;
 </script>
                <isRule>false</isRule>
                <guid>3b3e7e1f-9a1f-4fdb-b07f-0f01121de932</guid>
                <versionId>bb0cb214-2147-46db-b792-0351ca8cf981</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3aeec71b-454b-49b8-890b-2d3f025af4b0</processItemId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <name>Init Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.68d539dd-3d44-42aa-83b9-459d5a38a7d7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:221d</guid>
            <versionId>5d17cdf2-a2cb-4c77-8b30-4d67fafd860a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="106" y="17">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.68d539dd-3d44-42aa-83b9-459d5a38a7d7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//Set Mail Subject&#xD;
tw.local.subject = "ODC Request No. "+tw.local.odcRequest.appInfo.instanceID&#xD;
			+" for Customer "+tw.local.odcRequest.CustomerInfo.customerName&#xD;
			+" is collected "&#xD;
			+"طلب التحصيل المستندى تصدير رقم "+tw.local.odcRequest.appInfo.instanceID&#xD;
			+ " للعميل "+tw.local.odcRequest.CustomerInfo.customerName&#xD;
			+"تم تحصيله";&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>a7de5043-4815-4f7d-90b4-3d928c729779</guid>
                <versionId>edc4bdf8-f372-467e-8ea3-e0ccb4809e05</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.*************-467d-8819-583abef229ab</processItemId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <name>Exp. Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.df6c6682-9277-452c-b91f-d56dc388d542</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-6788</guid>
            <versionId>9ee00c0f-d893-471e-a988-e2d8a95f9e4a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="343" y="108">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.df6c6682-9277-452c-b91f-d56dc388d542</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>d9b46d48-8882-47c9-801d-c90704f6bb85</guid>
                <versionId>*************-4cb3-b81d-5e4e001150d8</versionId>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1fbc0e0b-e7e4-4c83-9475-8b625df32133</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.df6c6682-9277-452c-b91f-d56dc388d542</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>37dbbfd0-95f6-4683-9c8a-d9bed3e67b22</guid>
                    <versionId>14260024-8b15-4e6b-9d21-671ab0ab5fe4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b760d046-3f20-4e34-8496-cdbc10d7150b</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.df6c6682-9277-452c-b91f-d56dc388d542</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Send Mail to CAD Team"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>60bcefdb-ad7b-413f-a702-a475feae0ac3</guid>
                    <versionId>2b130758-4e4f-4e54-8c49-bc55c3dc03a6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.89896385-04dd-4b25-a023-812ac7d97181</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.df6c6682-9277-452c-b91f-d56dc388d542</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d344437c-7345-4dc6-bdec-26faa6955498</guid>
                    <versionId>3fcf1106-96c0-4895-94dd-faf15409ff80</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3b2bf338-1a51-49f5-8e47-26a6c7cca3cf</processItemId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <name>Send Mail</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:2219</guid>
            <versionId>e1f8708c-e514-41e0-9682-9de79233a4cc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="341" y="17">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.338e9f4d-8538-4ceb-a155-c288604435d4</attachedProcessRef>
                <guid>92f2765a-f6a0-4ef4-a8c9-5381eff8ac4c</guid>
                <versionId>f2d3b99c-7e0e-4fce-8d14-5d016a71a5cd</versionId>
                <parameterMapping name="mailDebugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8b05bde9-1f53-4023-9eae-91a0f640d4ee</parameterMappingId>
                    <processParameterId>2055.1da05789-2131-46bc-aacf-34d84ca37def</processParameterId>
                    <parameterMappingParentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailDebugMode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f4c4eddb-3f2a-42b5-a734-db38678b9585</guid>
                    <versionId>04b8c3bd-32bd-4bad-a840-50f3240afbdc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.836b5129-7fb2-44b6-be8d-b58a05452507</parameterMappingId>
                    <processParameterId>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</processParameterId>
                    <parameterMappingParentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b3a22fae-e1bc-4452-9b39-0c465061a8ac</guid>
                    <versionId>0a25536b-bdd8-45ff-a8bb-9e1d846584f3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5bb3ca45-ccd7-43eb-aef9-db46a51499d5</parameterMappingId>
                    <processParameterId>2055.20348cf5-023e-4d3a-826e-5b92143ec224</processParameterId>
                    <parameterMappingParentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cb473a27-29ff-4c08-b82b-2428f1c2ec14</guid>
                    <versionId>0abaddf5-6c60-4e10-9d10-fb514eda681d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="mailTo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.aa7681d6-4b0f-4a64-b6e4-ac5305760f35</parameterMappingId>
                    <processParameterId>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</processParameterId>
                    <parameterMappingParentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.mailTo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2aaca519-a752-4e6f-a801-3d83a0dc38ac</guid>
                    <versionId>76ed2bcc-6e95-4750-8299-3f13cb863696</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="attachments">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.199e9ff2-c900-4a65-93b7-bc4c75d320dd</parameterMappingId>
                    <processParameterId>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</processParameterId>
                    <parameterMappingParentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.8a11240b-682f-4caf-9f03-5ce6a64d720b</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>bf3f15ef-b468-44f5-a753-e127f7a0fbfb</guid>
                    <versionId>a10382cc-f50b-43ae-90c7-f6e32e69bb9c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="msgBody">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dc143b1e-7a14-4e4b-adf7-d04d93a8b2e8</parameterMappingId>
                    <processParameterId>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</processParameterId>
                    <parameterMappingParentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.msgBody</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cd0b7830-2446-4d2d-a65f-b39b460871c6</guid>
                    <versionId>a998f703-e2f8-4d78-87d8-94847ad2294b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="subject">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cd4dbe33-2833-477e-8b48-b3a58df0d5ca</parameterMappingId>
                    <processParameterId>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</processParameterId>
                    <parameterMappingParentId>3012.b47b016b-3a42-4d72-bf78-d0d542480f5a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.subject</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>078d9027-155d-45eb-b88e-************</guid>
                    <versionId>f3954e67-6964-4e25-97e0-795af0a0c26f</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.3aeec71b-454b-49b8-890b-2d3f025af4b0</startingProcessItemId>
        <errorHandlerItemId>2025.*************-467d-8819-583abef229ab</errorHandlerItemId>
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="40">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <globalExceptionHandler>
            <layoutData x="260" y="131">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </globalExceptionHandler>
        <globalExceptionLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </globalExceptionLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Send Mail to CAD Team" id="1.a6205b87-57cc-47bb-abfc-edff0743b08e" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain">Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</ns16:documentation>
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.8510e459-4375-44f2-afb0-c0b98b03ee89" />
                        
                        
                        <ns16:dataInput name="mailTo" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.8510e459-4375-44f2-afb0-c0b98b03ee89</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="1ce98c9e-b211-4aac-ad97-11b5c00168af">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="dcaa0884-526a-428d-9966-007ed504c14f" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="413" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>e3e18d94-e83e-417b-aafe-17a15ee3e748</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cadaa4ff-**************-cf85c3ee89c1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3aeec71b-454b-49b8-890b-2d3f025af4b0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3b2bf338-1a51-49f5-8e47-26a6c7cca3cf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1f6160a2-a666-45ca-8f72-21901d59e49e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>*************-467d-8819-583abef229ab</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>676aba80-f575-4678-8d55-bdbea4af9c39</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="e3e18d94-e83e-417b-aafe-17a15ee3e748">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="40" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.24f7c3c4-2ff4-47ca-944f-3c373b24cbd7</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="cadaa4ff-**************-cf85c3ee89c1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="470" y="40" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:221a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>42fbf9a2-b85d-409c-8200-c4ad45e7b52f</ns16:incoming>
                        
                        
                        <ns16:incoming>b1adb7f1-c762-45ec-8695-5da03834a8b3</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="e3e18d94-e83e-417b-aafe-17a15ee3e748" targetRef="3aeec71b-454b-49b8-890b-2d3f025af4b0" name="To Init Data" id="2027.24f7c3c4-2ff4-47ca-944f-3c373b24cbd7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init Data" id="3aeec71b-454b-49b8-890b-2d3f025af4b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="106" y="17" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.24f7c3c4-2ff4-47ca-944f-3c373b24cbd7</ns16:incoming>
                        
                        
                        <ns16:outgoing>949fe970-849e-48dd-ac9f-6b663f623034</ns16:outgoing>
                        
                        
                        <ns16:script>//Set Mail Subject&#xD;
tw.local.subject = "ODC Request No. "+tw.local.odcRequest.appInfo.instanceID&#xD;
			+" for Customer "+tw.local.odcRequest.CustomerInfo.customerName&#xD;
			+" is collected "&#xD;
			+"طلب التحصيل المستندى تصدير رقم "+tw.local.odcRequest.appInfo.instanceID&#xD;
			+ " للعميل "+tw.local.odcRequest.CustomerInfo.customerName&#xD;
			+"تم تحصيله";&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.338e9f4d-8538-4ceb-a155-c288604435d4" name="Send Mail" id="3b2bf338-1a51-49f5-8e47-26a6c7cca3cf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="341" y="17" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>67089f7c-a6e3-43d0-81d9-c3f7e67044ab</ns16:incoming>
                        
                        
                        <ns16:outgoing>42fbf9a2-b85d-409c-8200-c4ad45e7b52f</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a7c74b41-811f-4581-94ef-69a84c74eb84</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailTo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ae81d59d-2fde-4526-9476-d0598d6e8472</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.subject</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6d9bd911-88b8-4ea3-8823-421a4f690290</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.msgBody</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1da05789-2131-46bc-aacf-34d84ca37def</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.mailDebugMode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.20348cf5-023e-4d3a-826e-5b92143ec224</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.268afc2e-a651-49ef-8704-9a6ff22065c6</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ec26c46c-d70b-4881-98d8-40e694dd7362</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="3b2bf338-1a51-49f5-8e47-26a6c7cca3cf" targetRef="cadaa4ff-**************-cf85c3ee89c1" name="To End" id="42fbf9a2-b85d-409c-8200-c4ad45e7b52f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3aeec71b-454b-49b8-890b-2d3f025af4b0" targetRef="1f6160a2-a666-45ca-8f72-21901d59e49e" name="To Set Mail Body" id="949fe970-849e-48dd-ac9f-6b663f623034">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailDebugMode" id="2056.647a4741-ee6e-4a1d-a8d7-057074aa7167" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="msgBody" id="2056.a9254194-82fe-47fd-ae4d-caf13a59dc74" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="subject" id="2056.00c5395a-8eda-4395-a3c4-c278fcd25b02" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMsg" id="2056.3d07a861-fafd-4e49-8584-4d2e3df87a49" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/plain" name="Set Mail Body" id="1f6160a2-a666-45ca-8f72-21901d59e49e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="221" y="17" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>//tw.local.odcRequest.BasicDetails.Invoice[0].</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>949fe970-849e-48dd-ac9f-6b663f623034</ns16:incoming>
                        
                        
                        <ns16:outgoing>67089f7c-a6e3-43d0-81d9-c3f7e67044ab</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.msgBody
&lt;html&gt;&#xD;
	&lt;p dir="rtl" lang="ar"&gt;	&#xD;
	السيد / السيدة &lt;/br&gt;&lt;/br&gt;&#xD;
	رجاء العلم بأنه تم اضافة مبلغ  &lt;#=tw.local.odcRequest.FinancialDetailsBR.currency.value #&gt; &lt;#=tw.local.odcRequest.FinancialDetailsBR.amountAdvanced #&gt; رقم   &lt;#=tw.local.odcRequest.cif #&gt;  للعميل   &lt;#=tw.local.odcRequest.CustomerInfo.customerName #&gt; &#xD;
	، حصيلة واردة عن مستند شحن تصدير   &lt;#=tw.local.odcRequest.BasicDetails.flexCubeContractNo #&gt;&#xD;
	&lt;ul  dir="rtl" lang="ar"&gt;&#xD;
		&lt;li&gt;&lt;b&gt;تاريخ الحق: &lt;/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.ContractLiquidation.creditValueDate#&gt;&lt;/em&gt;&lt;/li&gt;&#xD;
		&lt;li&gt;&lt;b&gt;رقم الفاتورة: &lt;/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo#&gt;&lt;/em&gt;&lt;/li&gt;&#xD;
		&lt;li&gt;&lt;b&gt;رقم بوليصة الشحن: &lt;/b&gt;&lt;em&gt;&lt;#=tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef #&gt;&lt;/em&gt;&lt;/li&gt;&#xD;
	&lt;/ul&gt; &#xD;
	&lt;/p&gt;&#xD;
	&lt;b&gt; &lt;p dir="rtl"; lang="ar"; style="color:red;"&gt;	 الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.  &lt;/p&gt;&lt;/b&gt;&#xD;
&lt;/html&gt; &#xD;
	 &#xD;
	 &#xD;
 </ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1f6160a2-a666-45ca-8f72-21901d59e49e" targetRef="3b2bf338-1a51-49f5-8e47-26a6c7cca3cf" name="To Service" id="67089f7c-a6e3-43d0-81d9-c3f7e67044ab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exp. Handling" id="*************-467d-8819-583abef229ab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="343" y="108" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>656f5a27-1a50-40b3-8b59-29a88e473e7e</ns16:incoming>
                        
                        
                        <ns16:outgoing>b1adb7f1-c762-45ec-8695-5da03834a8b3</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Send Mail to CAD Team"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:intermediateCatchEvent name="Error Event" id="676aba80-f575-4678-8d55-bdbea4af9c39">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="260" y="131" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>656f5a27-1a50-40b3-8b59-29a88e473e7e</ns16:outgoing>
                        
                        
                        <ns16:errorEventDefinition id="02325e1d-fdbd-47bb-83c7-97d868ca490d" eventImplId="81dd27f5-ad20-4088-8976-b74bae3c4d14">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:intermediateCatchEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="676aba80-f575-4678-8d55-bdbea4af9c39" targetRef="*************-467d-8819-583abef229ab" name="To Exp. Handling" id="656f5a27-1a50-40b3-8b59-29a88e473e7e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="*************-467d-8819-583abef229ab" targetRef="cadaa4ff-**************-cf85c3ee89c1" name="To End Event" id="b1adb7f1-c762-45ec-8695-5da03834a8b3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b1adb7f1-c762-45ec-8695-5da03834a8b3</processLinkId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.*************-467d-8819-583abef229ab</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.cadaa4ff-**************-cf85c3ee89c1</toProcessItemId>
            <guid>5fb48c6b-4b69-43ec-a091-18ae0b855510</guid>
            <versionId>5ee210e5-b5d8-4b40-a176-08ef838cf889</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.*************-467d-8819-583abef229ab</fromProcessItemId>
            <toProcessItemId>2025.cadaa4ff-**************-cf85c3ee89c1</toProcessItemId>
        </link>
        <link name="To Set Mail Body">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.949fe970-849e-48dd-ac9f-6b663f623034</processLinkId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3aeec71b-454b-49b8-890b-2d3f025af4b0</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1f6160a2-a666-45ca-8f72-21901d59e49e</toProcessItemId>
            <guid>14b0a7e4-dea6-492b-bc90-ae36a028c528</guid>
            <versionId>7fe27188-39e2-4f7e-b7bd-7a67234097b5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3aeec71b-454b-49b8-890b-2d3f025af4b0</fromProcessItemId>
            <toProcessItemId>2025.1f6160a2-a666-45ca-8f72-21901d59e49e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.42fbf9a2-b85d-409c-8200-c4ad45e7b52f</processLinkId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3b2bf338-1a51-49f5-8e47-26a6c7cca3cf</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2970</endStateId>
            <toProcessItemId>2025.cadaa4ff-**************-cf85c3ee89c1</toProcessItemId>
            <guid>40708f48-ac91-4b86-a627-f19fbcbee022</guid>
            <versionId>8777ae12-1394-46be-9763-e118ef1fc330</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3b2bf338-1a51-49f5-8e47-26a6c7cca3cf</fromProcessItemId>
            <toProcessItemId>2025.cadaa4ff-**************-cf85c3ee89c1</toProcessItemId>
        </link>
        <link name="To Service">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.67089f7c-a6e3-43d0-81d9-c3f7e67044ab</processLinkId>
            <processId>1.a6205b87-57cc-47bb-abfc-edff0743b08e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1f6160a2-a666-45ca-8f72-21901d59e49e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3b2bf338-1a51-49f5-8e47-26a6c7cca3cf</toProcessItemId>
            <guid>d3bd0100-78dc-4957-bea3-a7ef7931d96f</guid>
            <versionId>bdebf4f4-7814-4c09-929f-f8c5bfd05926</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1f6160a2-a666-45ca-8f72-21901d59e49e</fromProcessItemId>
            <toProcessItemId>2025.3b2bf338-1a51-49f5-8e47-26a6c7cca3cf</toProcessItemId>
        </link>
    </process>
</teamworks>

