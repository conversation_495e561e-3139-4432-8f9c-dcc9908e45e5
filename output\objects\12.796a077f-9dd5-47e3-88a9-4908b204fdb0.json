{"id": "12.796a077f-9dd5-47e3-88a9-4908b204fdb0", "versionId": "e24d0f34-aa04-4e0d-8685-404bed09f7b6", "name": "RequestState", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.796a077f-9dd5-47e3-88a9-4908b204fdb0", "name": "RequestState", "lastModified": "1692013448492", "lastModifiedBy": "heba", "classId": "12.796a077f-9dd5-47e3-88a9-4908b204fdb0", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.796a077f-9dd5-47e3-88a9-4908b204fdb0", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/NBEODCR\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{}],\"shadow\":[false]}]},\"sequence\":{},\"name\":\"RequestState\"}],\"id\":\"_12.796a077f-9dd5-47e3-88a9-4908b204fdb0\"}", "description": {"isNull": "true"}, "guid": "guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-3aa0", "versionId": "e24d0f34-aa04-4e0d-8685-404bed09f7b6", "definition": {"validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "RequestState", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}