<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2ab87399-906f-4f13-8778-10a62c41cfa4" name="Get Actions for screens">
        <lastModified>1691583686643</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b2</guid>
        <versionId>481d6826-1a12-4925-b771-84e41e86c71b</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4d86" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"f72e8ef9-4258-4573-843a-fb62026cd288"},{"incoming":["e55fd0e4-29a9-46ce-8133-76db774847ff"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b4"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"9a566538-58e3-4581-88e6-edf4be6e49bc"},{"targetRef":"a0025a1a-cc7b-49bf-8c92-08a1abb366b6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4","sourceRef":"f72e8ef9-4258-4573-843a-fb62026cd288"},{"startQuantity":1,"outgoing":["e55fd0e4-29a9-46ce-8133-76db774847ff"],"incoming":["2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":323,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"a0025a1a-cc7b-49bf-8c92-08a1abb366b6","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.conditions = tw.local.data;\r\n\r\ntw.local.actions =[];\r\ntw.local.screenName = tw.epv.ScreenNames.CACT01;\r\n\/\/Set Actions for Act01\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\n\ttw.local.action = tw.epv.CreationActions.submitRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n\ttw.local.action = tw.epv.CreationActions.submitRequestToHubDirectory;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n\ttw.local.action = tw.epv.CreationActions.cancelRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n}\r\n\r\ntw.local.screenName = tw.epv.ScreenNames.CACT02;\r\n\/\/Set Actions for Act02\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\n \r\n \ttw.local.action = tw.epv.CreationActions.submitRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n\ttw.local.action = tw.epv.CreationActions.cancelRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n \r\n}\r\ntw.local.screenName = tw.epv.ScreenNames.CACT03;\r\n\/\/Set Actions for Act03\r\nif(tw.local.conditions.screenName == tw.local.screenName) \r\n{\r\n\tif(tw.local.conditions.complianceApproval){\r\n \t\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\t}\r\n\ttw.local.action = tw.epv.CreationActions.approveRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action; \r\n\r\n}\r\n\r\ntw.local.screenName = tw.epv.ScreenNames.CACT04;\r\n\/\/Set Actions for Act04\r\n\r\nif( (tw.local.conditions.screenName == tw.local.screenName) &amp;&amp; tw.local.conditions.complianceApproval)\r\n{\r\n\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\t\r\n\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\t\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n}\r\nelse{\r\n\ttw.local.action = tw.epv.CreationActions.createContract;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\n\ttw.local.action = tw.epv.CreationActions.amendContract;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\t\r\n\ttw.local.action = tw.epv.CreationActions.recreateContract;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\t\r\n\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\t\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n}\r\n\r\ntw.local.screenName = tw.epv.ScreenNames.CACT05;\r\n\/\/Set Actions for Act05\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\n tw.local.action = tw.epv.CreationActions.authorize;\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\ntw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\ntw.local.action = tw.epv.CreationActions.obtainApprovals;\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n\r\ntw.local.action = tw.epv.CreationActions.terminateRequest;\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action; \r\n\r\n}\r\ntw.local.screenName = tw.epv.ScreenNames.CACT06;\r\n\/\/Set Actions for Act06\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\n\r\n\ttw.local.action = tw.epv.CreationActions.completeRequest;\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\n}\r\n\r\ntw.local.screenName = tw.epv.ScreenNames.CACT07;\r\nif(tw.local.conditions.screenName == tw.local.screenName)\r\n{\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\r\n\t\r\n\ttw.local.action = tw.epv.CreationActions.approveRequest;\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\r\n}\r\n\r\n\r\n\/\/--------------------------\r\ntw.local.results= tw.local.actions;\r\n\r\n\r\n\r\n\r\n"]}},{"targetRef":"9a566538-58e3-4581-88e6-edf4be6e49bc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e55fd0e4-29a9-46ce-8133-76db774847ff","sourceRef":"a0025a1a-cc7b-49bf-8c92-08a1abb366b6"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"conditions","isCollection":false,"declaredType":"dataObject","id":"2056.318dc4e9-4eb6-4e8e-8b56-dc88c4ad0962"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"actions","isCollection":true,"declaredType":"dataObject","id":"2056.838388be-bd17-434d-8b8a-ad6aac77c935"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"screenName","isCollection":false,"declaredType":"dataObject","id":"2056.98462802-fd55-40c9-8636-b1864fbce829"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":false,"declaredType":"dataObject","id":"2056.30286102-8bb2-4bb3-829e-4c92c14e6370"}],"laneSet":[{"id":"be36812a-be1b-4767-8eef-427db2088665","lane":[{"flowNodeRef":["f72e8ef9-4258-4573-843a-fb62026cd288","9a566538-58e3-4581-88e6-edf4be6e49bc","a0025a1a-cc7b-49bf-8c92-08a1abb366b6"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"af7fc447-d364-4d9c-8711-cdf60cb994b1","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Actions for screens","declaredType":"process","id":"1.2ab87399-906f-4f13-8778-10a62c41cfa4","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.a693188f-ec7f-494e-8da9-9c0e2c42eb1b"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.5760666d-a1fd-4fb5-8a8f-8418b12c05d4"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"7f2b8603-7a3d-4b2f-8d10-b450158b2485","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"4b794af4-5543-4b55-89b4-8b4fd549be73","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Review ODC Request by Trade FO,userRole,true,lastAction\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.662bc25b-05e7-4912-8c9c-28deeda4dfc9"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.662bc25b-05e7-4912-8c9c-28deeda4dfc9</processParameterId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Review ODC Request by Trade FO,userRole,true,lastAction"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c4f3673e-3a6d-4ff3-b843-1d42bc2eddc8</guid>
            <versionId>5e056444-0e42-4137-b85e-0e354be2b5ed</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a693188f-ec7f-494e-8da9-9c0e2c42eb1b</processParameterId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5ae94a7d-ef68-4b2c-bdf8-a243bd9a23e2</guid>
            <versionId>e7d54e71-1dc8-4d3f-97b2-0b3e7675eb32</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5760666d-a1fd-4fb5-8a8f-8418b12c05d4</processParameterId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>085ee811-cbf4-465b-a564-bfbc48b7b21a</guid>
            <versionId>77901c83-4b6a-4f70-bdc0-1b56440f3ee0</versionId>
        </processParameter>
        <processVariable name="conditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.318dc4e9-4eb6-4e8e-8b56-dc88c4ad0962</processVariableId>
            <description isNull="true" />
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f4dd0f71-0423-4648-8e60-e030d4c06cc3</guid>
            <versionId>5ecdb192-73cb-4be2-8656-11a3e9471c9e</versionId>
        </processVariable>
        <processVariable name="actions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.838388be-bd17-434d-8b8a-ad6aac77c935</processVariableId>
            <description isNull="true" />
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ca127a98-1a0f-490a-8acb-37db5712c8e1</guid>
            <versionId>b0bb1003-b9f9-4c4c-9cd4-9f4765c00adb</versionId>
        </processVariable>
        <processVariable name="screenName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.98462802-fd55-40c9-8636-b1864fbce829</processVariableId>
            <description isNull="true" />
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9423a3f7-3678-4a81-9336-cac873bd257f</guid>
            <versionId>218ba86f-ef53-4cb0-b910-4a1906e0590c</versionId>
        </processVariable>
        <processVariable name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.30286102-8bb2-4bb3-829e-4c92c14e6370</processVariableId>
            <description isNull="true" />
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c1a37bbb-1a37-4ea4-aedc-944a5d72a918</guid>
            <versionId>02533462-9eb0-49d6-9354-e123f613039f</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9a566538-58e3-4581-88e6-edf4be6e49bc</processItemId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.94cb2aa3-41ad-438a-9975-487f666d2338</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b4</guid>
            <versionId>2573c3cc-9c53-4dfd-984e-ea6cc415a32c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.94cb2aa3-41ad-438a-9975-487f666d2338</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>83cfcc7c-6f59-4a14-8717-213b7d9bf284</guid>
                <versionId>e7469c6c-9bf1-424b-9363-aef7939f5e7e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6</processItemId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.5dc52de5-61b2-4d73-a78d-31ecd4f71d5b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4468</guid>
            <versionId>c1c82b19-2a93-4f20-8fb5-783c905d1624</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="323" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.5dc52de5-61b2-4d73-a78d-31ecd4f71d5b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.conditions = tw.local.data;&#xD;
&#xD;
tw.local.actions =[];&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT01;&#xD;
//Set Actions for Act01&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
	tw.local.action = tw.epv.CreationActions.submitRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.submitRequestToHubDirectory;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.cancelRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT02;&#xD;
//Set Actions for Act02&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
 &#xD;
 	tw.local.action = tw.epv.CreationActions.submitRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.cancelRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.returnToInitiator;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
 &#xD;
}&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT03;&#xD;
//Set Actions for Act03&#xD;
if(tw.local.conditions.screenName == tw.local.screenName) &#xD;
{&#xD;
	if(tw.local.conditions.complianceApproval){&#xD;
 		tw.local.action = tw.epv.CreationActions.obtainApprovals;&#xD;
		tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	}&#xD;
	tw.local.action = tw.epv.CreationActions.approveRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.returnToInitiator;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action; &#xD;
&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT04;&#xD;
//Set Actions for Act04&#xD;
&#xD;
if( (tw.local.conditions.screenName == tw.local.screenName) &amp;&amp; tw.local.conditions.complianceApproval)&#xD;
{&#xD;
	tw.local.action = tw.epv.CreationActions.obtainApprovals;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.returnToTradeFo;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
}&#xD;
else{&#xD;
	tw.local.action = tw.epv.CreationActions.createContract;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.amendContract;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.recreateContract;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.returnToTradeFo;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT05;&#xD;
//Set Actions for Act05&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
 tw.local.action = tw.epv.CreationActions.authorize;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
tw.local.action = tw.epv.CreationActions.returnToTradeFo;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
tw.local.action = tw.epv.CreationActions.obtainApprovals;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action; &#xD;
&#xD;
}&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT06;&#xD;
//Set Actions for Act06&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.completeRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT07;&#xD;
if(tw.local.conditions.screenName == tw.local.screenName)&#xD;
{&#xD;
	tw.local.action = tw.epv.CreationActions.returnToInitiator;&#xD;
	tw.local.actions[tw.local.actions.listLength] = tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.approveRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength] = tw.local.action;&#xD;
}&#xD;
&#xD;
&#xD;
//--------------------------&#xD;
tw.local.results= tw.local.actions;&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>627da6ad-f5d3-4ac4-b938-3916aece2a8a</guid>
                <versionId>21a0e545-8644-4932-8edb-80d8ae6e7396</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.9c4c8352-d4f4-4815-8221-c91f51e5acf8</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <guid>9cbdc4f0-9a9e-4289-810a-f1d44eb25b31</guid>
            <versionId>2ddd8ea1-28ad-4d0b-a0fd-551bb60db9d0</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.9d6b18f2-f65b-484e-8a9e-87e35bcb2097</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <guid>0bcebb04-77f9-4b93-9049-d89e631b0184</guid>
            <versionId>94326003-fdee-4ed4-96aa-7a8f6899ec02</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Actions for screens" id="1.2ab87399-906f-4f13-8778-10a62c41cfa4" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="7f2b8603-7a3d-4b2f-8d10-b450158b2485" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="4b794af4-5543-4b55-89b4-8b4fd549be73" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.662bc25b-05e7-4912-8c9c-28deeda4dfc9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Review ODC Request by Trade FO,userRole,true,lastAction"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.a693188f-ec7f-494e-8da9-9c0e2c42eb1b" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.5760666d-a1fd-4fb5-8a8f-8418b12c05d4" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="be36812a-be1b-4767-8eef-427db2088665">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="af7fc447-d364-4d9c-8711-cdf60cb994b1" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>f72e8ef9-4258-4573-843a-fb62026cd288</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9a566538-58e3-4581-88e6-edf4be6e49bc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a0025a1a-cc7b-49bf-8c92-08a1abb366b6</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="f72e8ef9-4258-4573-843a-fb62026cd288">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="9a566538-58e3-4581-88e6-edf4be6e49bc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b4</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e55fd0e4-29a9-46ce-8133-76db774847ff</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f72e8ef9-4258-4573-843a-fb62026cd288" targetRef="a0025a1a-cc7b-49bf-8c92-08a1abb366b6" name="To Script Task" id="2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="a0025a1a-cc7b-49bf-8c92-08a1abb366b6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="323" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4</ns16:incoming>
                        
                        
                        <ns16:outgoing>e55fd0e4-29a9-46ce-8133-76db774847ff</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.conditions = tw.local.data;&#xD;
&#xD;
tw.local.actions =[];&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT01;&#xD;
//Set Actions for Act01&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
	tw.local.action = tw.epv.CreationActions.submitRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.submitRequestToHubDirectory;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.cancelRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT02;&#xD;
//Set Actions for Act02&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
 &#xD;
 	tw.local.action = tw.epv.CreationActions.submitRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.cancelRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.returnToInitiator;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
 &#xD;
}&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT03;&#xD;
//Set Actions for Act03&#xD;
if(tw.local.conditions.screenName == tw.local.screenName) &#xD;
{&#xD;
	if(tw.local.conditions.complianceApproval){&#xD;
 		tw.local.action = tw.epv.CreationActions.obtainApprovals;&#xD;
		tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	}&#xD;
	tw.local.action = tw.epv.CreationActions.approveRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.returnToInitiator;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action; &#xD;
&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT04;&#xD;
//Set Actions for Act04&#xD;
&#xD;
if( (tw.local.conditions.screenName == tw.local.screenName) &amp;&amp; tw.local.conditions.complianceApproval)&#xD;
{&#xD;
	tw.local.action = tw.epv.CreationActions.obtainApprovals;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.returnToTradeFo;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
}&#xD;
else{&#xD;
	tw.local.action = tw.epv.CreationActions.createContract;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.amendContract;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.recreateContract;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.returnToTradeFo;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT05;&#xD;
//Set Actions for Act05&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
 tw.local.action = tw.epv.CreationActions.authorize;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
tw.local.action = tw.epv.CreationActions.returnToTradeFo;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
tw.local.action = tw.epv.CreationActions.obtainApprovals;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
&#xD;
tw.local.action = tw.epv.CreationActions.terminateRequest;&#xD;
tw.local.actions[tw.local.actions.listLength]= tw.local.action; &#xD;
&#xD;
}&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT06;&#xD;
//Set Actions for Act06&#xD;
if(tw.local.conditions.screenName == tw.local.screenName){&#xD;
&#xD;
	tw.local.action = tw.epv.CreationActions.completeRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength]= tw.local.action;&#xD;
}&#xD;
&#xD;
tw.local.screenName = tw.epv.ScreenNames.CACT07;&#xD;
if(tw.local.conditions.screenName == tw.local.screenName)&#xD;
{&#xD;
	tw.local.action = tw.epv.CreationActions.returnToInitiator;&#xD;
	tw.local.actions[tw.local.actions.listLength] = tw.local.action;&#xD;
	&#xD;
	tw.local.action = tw.epv.CreationActions.approveRequest;&#xD;
	tw.local.actions[tw.local.actions.listLength] = tw.local.action;&#xD;
}&#xD;
&#xD;
&#xD;
//--------------------------&#xD;
tw.local.results= tw.local.actions;&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="a0025a1a-cc7b-49bf-8c92-08a1abb366b6" targetRef="9a566538-58e3-4581-88e6-edf4be6e49bc" name="To End" id="e55fd0e4-29a9-46ce-8133-76db774847ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="conditions" id="2056.318dc4e9-4eb6-4e8e-8b56-dc88c4ad0962" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="actions" id="2056.838388be-bd17-434d-8b8a-ad6aac77c935" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="screenName" id="2056.98462802-fd55-40c9-8636-b1864fbce829" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="action" id="2056.30286102-8bb2-4bb3-829e-4c92c14e6370" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e55fd0e4-29a9-46ce-8133-76db774847ff</processLinkId>
            <processId>1.2ab87399-906f-4f13-8778-10a62c41cfa4</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9a566538-58e3-4581-88e6-edf4be6e49bc</toProcessItemId>
            <guid>54b99463-c790-43dc-bcee-8fc8c750aa64</guid>
            <versionId>c4a4bc6c-d0c3-4a22-9338-7b8eda5c15f6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6</fromProcessItemId>
            <toProcessItemId>2025.9a566538-58e3-4581-88e6-edf4be6e49bc</toProcessItemId>
        </link>
    </process>
</teamworks>

