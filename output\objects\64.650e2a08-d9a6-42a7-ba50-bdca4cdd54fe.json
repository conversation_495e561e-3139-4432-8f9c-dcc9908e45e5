{"id": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "versionId": "f28116da-b2e5-4a00-a7a7-1562a7ca4ae3", "name": "FC Collections CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "fcCollections", "configOptions": ["FCVIS", "retrieveBtnVis", "customerCif", "collectionCurrencyVIS", "negotiatedExchangeRateVIS", "requestCurrency", "addBtnVIS", "activityType"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//amountAllocatedForCurrentRequest * negotiatedExchangeRate\r\r\n //allocatedAmountInRequestCurrency\r\r\n //Function to calculate allocated amount in request currency\r\r\n// this.calculateAllocatedAmount = function(){\r\r\n//\tvar rate = this.context.binding.get(\"value\").get(\"negotiatedExchangeRate\");\r\r\n//\tconsole.log(\"Negotiated Exchange Rate\");\r\r\n//\tvar amount ;\r\r\n//\t\t\r\r\n//\tvar result = rate * amount;\r\r\n//}\r\r\n\r\r\n\r\r\nthis.negotiatedVIS = function  (me) {\r\r\n\tif (me.getData() == 1) {\r\r\n\t\tthis.context.options.negotiatedExchangeRateVIS.set(\"value\", \"READONLY\");\r\r\n\t}else{\r\r\n\t\tthis.context.options.negotiatedExchangeRateVIS.set(\"value\", \"DEFAULT\");\r\r\n\t}\r\r\n} \r\r\n\r\r\n\r\r\nthis.showRetrieveTransactionBtn = function(){\r\r\n//console.log(\"///////////////////\"+this.context.options.activityType.get(\"value\"));\r\r\nif(this.context.options.activityType.get(\"value\") == \"write\")\r\r\n{\r\r\n\ttry {\r\r\n\t\t\r\r\n\t\r\r\n\t\tif(this.context.binding.get(\"value\").get(\"currency\").get(\"name\") == \"\" || this.context.binding.get(\"value\").get(\"fromDate\") == null ||\r\r\n\t\tthis.context.binding.get(\"value\").get(\"ToDate\") == null || this.context.binding.get(\"value\").get(\"accountNo\").get(\"value\") == \"\") \r\r\n\t\t{\r\r\n\t\t \tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t\t \tconsole.log(\"not visible\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get(\"Button1\").setVisible(true,true);\t\r\r\n\t\t\tconsole.log(\" visible\");\r\r\n\t\t}\r\r\n\t} catch (err) {\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n\telse{\r\r\n\t\t\tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}