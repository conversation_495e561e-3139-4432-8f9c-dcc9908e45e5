<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7" name="Get Required Documents 2">
        <lastModified>1699524074689</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.ee42c9e4-14dd-461d-8072-1376f66937ca</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>dbcd6042-3abb-4774-ad38-731a1ea0befe</guid>
        <versionId>2867c960-8837-4a75-8b94-f1c61e5f5356</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:651a1a6abf396537:64776e00:18baeba64af:300e" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["9f341f37-845b-4376-8132-c7b451771a28"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":265,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"d230b937-036c-4eea-9f13-b124395e692e"},{"incoming":["62eeda52-f7c5-455b-ae03-d1f1dd120195"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":740,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bf"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"b869dae4-a686-4c35-b2d2-e5c96ec03d17"},{"outgoing":["603f88b9-4f72-4af4-95ff-7d04e50dc660"],"incoming":["9f341f37-845b-4376-8132-c7b451771a28"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":400,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_DOCS_IN_FOLDER","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Documents In Folder","dataInputAssociation":[{"targetRef":"FOLDER_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.FileNet"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"ee42c9e4-14dd-461d-8072-1376f66937ca","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a","declaredType":"TFormalExpression","content":["tw.local.documents"]}}],"sourceRef":["DOCUMENTS"]}],"orderOverride":false},{"targetRef":"886f3c4c-8291-4e97-8f45-fe20f41fe905","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Missing Documents","declaredType":"sequenceFlow","id":"603f88b9-4f72-4af4-95ff-7d04e50dc660","sourceRef":"ee42c9e4-14dd-461d-8072-1376f66937ca"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMDocument();\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMDocument();\nautoObject[0].objectId = \"\";\nautoObject[0].serverName = \"\";\nautoObject[0].repositoryId = \"\";\nautoObject[0].objectTypeId = \"\";\nautoObject[0].name = \"\";\nautoObject[0].contentURL = \"\";\nautoObject[0].creationDate = new TWDate();\nautoObject[0].createdBy = \"\";\nautoObject[0].lastModificationDate = new TWDate();\nautoObject[0].lastModifiedBy = \"\";\nautoObject[0].versionLabel = \"\";\nautoObject[0].isLatestVersion = false;\nautoObject[0].isMajorVersion = false;\nautoObject[0].isLatestMajorVersion = false;\nautoObject[0].checkinComment = \"\";\nautoObject[0].properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject[0].properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject[0].properties[0].objectTypeId = \"\";\nautoObject[0].properties[0].value = null;\nautoObject"}]},"itemSubjectRef":"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a","name":"documents","isCollection":true,"declaredType":"dataObject","id":"2056.b089d7c5-8abe-4c6e-85dc-de497dea0598"},{"startQuantity":1,"outgoing":["62eeda52-f7c5-455b-ae03-d1f1dd120195"],"incoming":["603f88b9-4f72-4af4-95ff-7d04e50dc660"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":547,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Missing Documents","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"886f3c4c-8291-4e97-8f45-fe20f41fe905","scriptFormat":"text\/x-javascript","script":{"content":["var docExist= false;\r\ntw.local.errorMessage=\"\";\r\n\r\nif (!!tw.local.documents &amp;&amp; tw.local.documents.listLength &gt; 0) {\r\n\tfor (var j=0; j&lt;tw.local.documents.listLength; j++) {\r\n\r\n\t\tif ( tw.local.documents[j].objectTypeId == \"ODCDocuments\") {  \r\n\t\t\tdocExist= true\r\n\t\t\tbreak;\t\t\t\t\r\n\t\t}\r\n\t}\/\/enf of for loop\r\n}\r\n\r\n\r\nif (docExist==false)\r\n\ttw.local.errorMessage=  \"&lt;li&gt;\"+ \"'Please upload 'Customer Request' Document\" +\"&lt;\/li&gt;\";\r\n\r\n\r\n"]}},{"targetRef":"b869dae4-a686-4c35-b2d2-e5c96ec03d17","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Validate Required Documents","declaredType":"sequenceFlow","id":"62eeda52-f7c5-455b-ae03-d1f1dd120195","sourceRef":"886f3c4c-8291-4e97-8f45-fe20f41fe905"},{"parallelMultiple":false,"outgoing":["4610b27a-8a16-4a05-8f87-c990678fbd49"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"4eeb05a5-dc1a-498d-adf7-c0791003d21f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"01c4868b-b445-44ec-8942-aebb2eb07d93","otherAttributes":{"eventImplId":"332bc231-23bd-4cb6-85c6-4e6a676d92c2"}}],"attachedToRef":"ee42c9e4-14dd-461d-8072-1376f66937ca","extensionElements":{"nodeVisualInfo":[{"width":24,"x":435,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"aa99c25a-00df-42eb-bc9e-bbfb204715ab","outputSet":{}},{"parallelMultiple":false,"outgoing":["afa728c5-f024-4357-8c31-7387ed4a6e03"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"6d33b726-33ff-47b7-a2bc-bb0fc855f0b8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c1b284cf-bafc-4daa-8aa2-29b46fcc5b0d","otherAttributes":{"eventImplId":"c634a25f-d714-4b8d-8155-c3a041c5e0af"}}],"attachedToRef":"886f3c4c-8291-4e97-8f45-fe20f41fe905","extensionElements":{"nodeVisualInfo":[{"width":24,"x":582,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"af930738-d36f-4e57-b8e6-f5d94314787a","outputSet":{}},{"startQuantity":1,"outgoing":["ae61db14-9c26-40ca-ab1f-e157a0e8a5e1"],"incoming":["4610b27a-8a16-4a05-8f87-c990678fbd49","afa728c5-f024-4357-8c31-7387ed4a6e03"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":547,"y":167,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Error Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Get Required Documents 2\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"53e7135a-0e89-45c0-9c92-da1f1c53c967","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"53e7135a-0e89-45c0-9c92-da1f1c53c967","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Error Handling","declaredType":"sequenceFlow","id":"4610b27a-8a16-4a05-8f87-c990678fbd49","sourceRef":"aa99c25a-00df-42eb-bc9e-bbfb204715ab"},{"targetRef":"53e7135a-0e89-45c0-9c92-da1f1c53c967","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Error Handling","declaredType":"sequenceFlow","id":"afa728c5-f024-4357-8c31-7387ed4a6e03","sourceRef":"af930738-d36f-4e57-b8e6-f5d94314787a"},{"targetRef":"27d806a4-e4b3-4c61-bb80-149825d59a5c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"ae61db14-9c26-40ca-ab1f-e157a0e8a5e1","sourceRef":"53e7135a-0e89-45c0-9c92-da1f1c53c967"},{"incoming":["ae61db14-9c26-40ca-ab1f-e157a0e8a5e1"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"398d5181-6f76-4594-882c-a192a21cc100","otherAttributes":{"eventImplId":"6a53d0e9-5805-45db-8c90-8f5da7292cc7"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":752,"y":190,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}]}],"declaredType":"endEvent","id":"27d806a4-e4b3-4c61-bb80-149825d59a5c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.6d83ac2f-7a03-4af3-8ec1-93d2a246984d"},{"targetRef":"ee42c9e4-14dd-461d-8072-1376f66937ca","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Documents In Folder","declaredType":"sequenceFlow","id":"9f341f37-845b-4376-8132-c7b451771a28","sourceRef":"d230b937-036c-4eea-9f13-b124395e692e"}],"laneSet":[{"id":"2b271906-eef7-4a77-bfbd-1a54490e89db","lane":[{"flowNodeRef":["d230b937-036c-4eea-9f13-b124395e692e","b869dae4-a686-4c35-b2d2-e5c96ec03d17","ee42c9e4-14dd-461d-8072-1376f66937ca","886f3c4c-8291-4e97-8f45-fe20f41fe905","aa99c25a-00df-42eb-bc9e-bbfb204715ab","af930738-d36f-4e57-b8e6-f5d94314787a","53e7135a-0e89-45c0-9c92-da1f1c53c967","27d806a4-e4b3-4c61-bb80-149825d59a5c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"3294eae9-f954-453c-ab46-eb921f3f9ea6","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Required Documents 2","declaredType":"process","id":"1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"49c3a2b6-10bf-4d4e-8cfb-b2cb91e8037b","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.f43a0726-0192-4d4c-942e-83e973ee5015"]}],"outputSet":[{"dataOutputRefs":["2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"{D01E4D8A-0000-CA47-A825-DBDAE9D5B1EA}\""}]},"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.f43a0726-0192-4d4c-942e-83e973ee5015"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f43a0726-0192-4d4c-942e-83e973ee5015</processParameterId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"{D01E4D8A-0000-CA47-A825-DBDAE9D5B1EA}"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ed5d01b7-50f0-4777-b960-079d3859af87</guid>
            <versionId>8ff3b62c-13df-4e34-b201-80eeefb59e95</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b</processParameterId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9864425e-b387-4e66-beb1-eb699cd1143c</guid>
            <versionId>6b70533c-2dcc-493c-88ff-00e807b80051</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.92feb460-d2b0-4146-ad2f-5119229ca290</processParameterId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>84</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>abf4db22-3339-4950-a140-b4454bd28537</guid>
            <versionId>f767f6fe-3628-4be1-816b-05a0c0b06419</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d669e07f-84fd-4ec6-8658-c277f3e3633a</processParameterId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>146</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>83e63222-684f-492a-aa2a-a1dc5edd3374</guid>
            <versionId>b83d8f10-69cb-4382-9e96-b5c23dbaa23c</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2f9d58fe-75a7-457b-82cb-fa048aa891a8</processParameterId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>147</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c0582e65-dd8a-4ee4-ae67-8daa483662fb</guid>
            <versionId>3a5bb757-bc05-4c81-966d-5b62cd523bc1</versionId>
        </processParameter>
        <processVariable name="documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b089d7c5-8abe-4c6e-85dc-de497dea0598</processVariableId>
            <description isNull="true" />
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.490f939c-3c6d-4ef7-9707-33b5b618877a</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMDocument();
autoObject[0] = new tw.object.toolkit.SYSCM.ECMDocument();
autoObject[0].objectId = "";
autoObject[0].serverName = "";
autoObject[0].repositoryId = "";
autoObject[0].objectTypeId = "";
autoObject[0].name = "";
autoObject[0].contentURL = "";
autoObject[0].creationDate = new TWDate();
autoObject[0].createdBy = "";
autoObject[0].lastModificationDate = new TWDate();
autoObject[0].lastModifiedBy = "";
autoObject[0].versionLabel = "";
autoObject[0].isLatestVersion = false;
autoObject[0].isMajorVersion = false;
autoObject[0].isLatestMajorVersion = false;
autoObject[0].checkinComment = "";
autoObject[0].properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject[0].properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject[0].properties[0].objectTypeId = "";
autoObject[0].properties[0].value = null;
autoObject</defaultValue>
            <guid>64e60a46-9a95-4cb1-a355-e8949b4e9f52</guid>
            <versionId>d51e650e-a115-4c4f-b349-9978c33fcda3</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6d83ac2f-7a03-4af3-8ec1-93d2a246984d</processVariableId>
            <description isNull="true" />
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>47a0bbec-b6fd-47df-95b7-0b241dc95a5e</guid>
            <versionId>9381d72c-b1da-4994-b6f2-490e4adb4753</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.27d806a4-e4b3-4c61-bb80-149825d59a5c</processItemId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.1826b540-ac6c-406d-943f-8d4107f85ae5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15c0</guid>
            <versionId>4b2d030d-d4f0-443d-9734-8e2fcb98a4ec</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="752" y="190">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.1826b540-ac6c-406d-943f-8d4107f85ae5</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>31294865-22da-4ef8-a846-2ca45f5232d3</guid>
                <versionId>db1171c1-88e3-495e-a004-261c5c62aaa6</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.50de3c73-6cb3-46be-8ceb-747c310cfd4b</parameterMappingId>
                    <processParameterId>2055.92feb460-d2b0-4146-ad2f-5119229ca290</processParameterId>
                    <parameterMappingParentId>3007.1826b540-ac6c-406d-943f-8d4107f85ae5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d7555ab2-9301-4e3e-88b4-eec8d244f049</guid>
                    <versionId>d058d263-c424-4acd-9341-201804d0222c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ee42c9e4-14dd-461d-8072-1376f66937ca</processItemId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <name>Get Documents In Folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.dfe1dd92-3921-46ca-9749-71235080c403</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.53e7135a-0e89-45c0-9c92-da1f1c53c967</errorHandlerItemId>
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bb</guid>
            <versionId>882a4762-9a4e-4fe4-819c-a615efdb4be0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.b034d4ce-1c0d-496a-9b60-b7f3f920b0fa</processItemPrePostId>
                <processItemId>2025.ee42c9e4-14dd-461d-8072-1376f66937ca</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>b0ec5808-cb67-4572-ac72-afdaa4fd6b93</guid>
                <versionId>c506b191-3c8f-4aed-b840-76c70b2e3991</versionId>
            </processPrePosts>
            <layoutData x="400" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bd</errorHandlerItem>
                <errorHandlerItemId>2025.53e7135a-0e89-45c0-9c92-da1f1c53c967</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.dfe1dd92-3921-46ca-9749-71235080c403</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderId&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.env.FileNet&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;documents&lt;/name&gt;&#xD;
      &lt;type&gt;ECMDocument&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.documents&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;true&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_DOCS_IN_FOLDER&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.2f9d58fe-75a7-457b-82cb-fa048aa891a8&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>a0ba5120-ffa2-4ac6-b0ee-8d241b198e6f</guid>
                <versionId>4b1b9e6c-9780-4874-ae63-f2d978149691</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.886f3c4c-8291-4e97-8f45-fe20f41fe905</processItemId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <name>Set Missing Documents</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2b71e3fa-cf27-4bbd-a256-c843d66735b6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.53e7135a-0e89-45c0-9c92-da1f1c53c967</errorHandlerItemId>
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15c1</guid>
            <versionId>9f95bbd3-755f-4fe2-a31f-799ed9f8a68d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="547" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bd</errorHandlerItem>
                <errorHandlerItemId>2025.53e7135a-0e89-45c0-9c92-da1f1c53c967</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2b71e3fa-cf27-4bbd-a256-c843d66735b6</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var docExist= false;&#xD;
tw.local.errorMessage="";&#xD;
&#xD;
if (!!tw.local.documents &amp;&amp; tw.local.documents.listLength &gt; 0) {&#xD;
	for (var j=0; j&lt;tw.local.documents.listLength; j++) {&#xD;
&#xD;
		if ( tw.local.documents[j].objectTypeId == "ODCDocuments") {  &#xD;
			docExist= true&#xD;
			break;				&#xD;
		}&#xD;
	}//enf of for loop&#xD;
}&#xD;
&#xD;
&#xD;
if (docExist==false)&#xD;
	tw.local.errorMessage=  "&lt;li&gt;"+ "'Please upload 'Customer Request' Document" +"&lt;/li&gt;";&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>d642883d-1ce9-4b1d-867f-ccef66a909d2</guid>
                <versionId>eeed9cba-2690-49c7-8cc7-b2ab9b1ffc96</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.53e7135a-0e89-45c0-9c92-da1f1c53c967</processItemId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <name>Error Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.118b4b87-c75f-4c98-858c-d84f432a308f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bd</guid>
            <versionId>c2495188-1822-4bbd-aaf9-3e04bb827354</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="547" y="167">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.118b4b87-c75f-4c98-858c-d84f432a308f</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>b9c6611c-7881-4c41-8760-5f6cf373a3d6</guid>
                <versionId>af12380e-8aae-4eb1-893f-39b720ed3377</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e269e728-c8bf-4fec-ba16-c50777f9e428</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.118b4b87-c75f-4c98-858c-d84f432a308f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b53ee722-e845-42f0-b8ed-8ffed7e70d5b</guid>
                    <versionId>6d744249-f396-4c79-b550-43e766b55319</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ad55bc2e-f2b6-45b4-a67f-e3669e58d24e</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.118b4b87-c75f-4c98-858c-d84f432a308f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f5b92c90-a23f-4817-a5a3-413a2193f919</guid>
                    <versionId>7a9daaa2-a239-40cf-8ce7-2e7b432e47de</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bdd39a55-0cb2-44df-be83-418afb6d9ee7</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.118b4b87-c75f-4c98-858c-d84f432a308f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Get Required Documents 2"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>24f0b301-d939-40ea-b382-ab8b1484efd3</guid>
                    <versionId>8ae1b8ed-4787-4f90-83ea-79d0a2f3b526</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b869dae4-a686-4c35-b2d2-e5c96ec03d17</processItemId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.47c91513-31db-47f4-ae4d-38e85e4753db</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bf</guid>
            <versionId>ec4b32a0-e0b0-4899-9a9f-f2c124964e67</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="740" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.47c91513-31db-47f4-ae4d-38e85e4753db</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a40b46c3-a3a4-4f85-845a-95de83adb24e</guid>
                <versionId>5a42bcd0-5369-4daa-b513-7c1b56377f1e</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.749a06f9-6105-4df6-be0c-8172b4a882f5</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <guid>f8fe307f-7578-4f5f-9fa1-cc2a7eff3310</guid>
            <versionId>2f529315-2a93-4f5c-8a3b-d4e453cb8080</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.ee42c9e4-14dd-461d-8072-1376f66937ca</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="265" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Required Documents 2" id="1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="49c3a2b6-10bf-4d4e-8cfb-b2cb91e8037b" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.f43a0726-0192-4d4c-942e-83e973ee5015">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"{D01E4D8A-0000-CA47-A825-DBDAE9D5B1EA}"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.f43a0726-0192-4d4c-942e-83e973ee5015</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="2b271906-eef7-4a77-bfbd-1a54490e89db">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="3294eae9-f954-453c-ab46-eb921f3f9ea6" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>d230b937-036c-4eea-9f13-b124395e692e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b869dae4-a686-4c35-b2d2-e5c96ec03d17</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ee42c9e4-14dd-461d-8072-1376f66937ca</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>886f3c4c-8291-4e97-8f45-fe20f41fe905</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>aa99c25a-00df-42eb-bc9e-bbfb204715ab</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>af930738-d36f-4e57-b8e6-f5d94314787a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>53e7135a-0e89-45c0-9c92-da1f1c53c967</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>27d806a4-e4b3-4c61-bb80-149825d59a5c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="d230b937-036c-4eea-9f13-b124395e692e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="265" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>9f341f37-845b-4376-8132-c7b451771a28</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="b869dae4-a686-4c35-b2d2-e5c96ec03d17">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="740" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bf</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>62eeda52-f7c5-455b-ae03-d1f1dd120195</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns4:contentTask serverName="FileNet" operationRef="FOLDER_OP_GET_DOCS_IN_FOLDER" name="Get Documents In Folder" id="ee42c9e4-14dd-461d-8072-1376f66937ca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="400" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9f341f37-845b-4376-8132-c7b451771a28</ns16:incoming>
                        
                        
                        <ns16:outgoing>603f88b9-4f72-4af4-95ff-7d04e50dc660</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>FOLDER_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.FileNet</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>DOCUMENTS</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a">tw.local.documents</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ee42c9e4-14dd-461d-8072-1376f66937ca" targetRef="886f3c4c-8291-4e97-8f45-fe20f41fe905" name="To Set Missing Documents" id="603f88b9-4f72-4af4-95ff-7d04e50dc660">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a" isCollection="true" name="documents" id="2056.b089d7c5-8abe-4c6e-85dc-de497dea0598">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMDocument();
autoObject[0] = new tw.object.toolkit.SYSCM.ECMDocument();
autoObject[0].objectId = "";
autoObject[0].serverName = "";
autoObject[0].repositoryId = "";
autoObject[0].objectTypeId = "";
autoObject[0].name = "";
autoObject[0].contentURL = "";
autoObject[0].creationDate = new TWDate();
autoObject[0].createdBy = "";
autoObject[0].lastModificationDate = new TWDate();
autoObject[0].lastModifiedBy = "";
autoObject[0].versionLabel = "";
autoObject[0].isLatestVersion = false;
autoObject[0].isMajorVersion = false;
autoObject[0].isLatestMajorVersion = false;
autoObject[0].checkinComment = "";
autoObject[0].properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject[0].properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject[0].properties[0].objectTypeId = "";
autoObject[0].properties[0].value = null;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Missing Documents" id="886f3c4c-8291-4e97-8f45-fe20f41fe905">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="547" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>603f88b9-4f72-4af4-95ff-7d04e50dc660</ns16:incoming>
                        
                        
                        <ns16:outgoing>62eeda52-f7c5-455b-ae03-d1f1dd120195</ns16:outgoing>
                        
                        
                        <ns16:script>var docExist= false;&#xD;
tw.local.errorMessage="";&#xD;
&#xD;
if (!!tw.local.documents &amp;&amp; tw.local.documents.listLength &gt; 0) {&#xD;
	for (var j=0; j&lt;tw.local.documents.listLength; j++) {&#xD;
&#xD;
		if ( tw.local.documents[j].objectTypeId == "ODCDocuments") {  &#xD;
			docExist= true&#xD;
			break;				&#xD;
		}&#xD;
	}//enf of for loop&#xD;
}&#xD;
&#xD;
&#xD;
if (docExist==false)&#xD;
	tw.local.errorMessage=  "&lt;li&gt;"+ "'Please upload 'Customer Request' Document" +"&lt;/li&gt;";&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="886f3c4c-8291-4e97-8f45-fe20f41fe905" targetRef="b869dae4-a686-4c35-b2d2-e5c96ec03d17" name="To Validate Required Documents" id="62eeda52-f7c5-455b-ae03-d1f1dd120195">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ee42c9e4-14dd-461d-8072-1376f66937ca" parallelMultiple="false" name="Error1" id="aa99c25a-00df-42eb-bc9e-bbfb204715ab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="435" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4610b27a-8a16-4a05-8f87-c990678fbd49</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="4eeb05a5-dc1a-498d-adf7-c0791003d21f" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="01c4868b-b445-44ec-8942-aebb2eb07d93" eventImplId="332bc231-23bd-4cb6-85c6-4e6a676d92c2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="886f3c4c-8291-4e97-8f45-fe20f41fe905" parallelMultiple="false" name="Error2" id="af930738-d36f-4e57-b8e6-f5d94314787a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="582" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>afa728c5-f024-4357-8c31-7387ed4a6e03</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="6d33b726-33ff-47b7-a2bc-bb0fc855f0b8" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c1b284cf-bafc-4daa-8aa2-29b46fcc5b0d" eventImplId="c634a25f-d714-4b8d-8155-c3a041c5e0af">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Error Handling" id="53e7135a-0e89-45c0-9c92-da1f1c53c967">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="547" y="167" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4610b27a-8a16-4a05-8f87-c990678fbd49</ns16:incoming>
                        
                        
                        <ns16:incoming>afa728c5-f024-4357-8c31-7387ed4a6e03</ns16:incoming>
                        
                        
                        <ns16:outgoing>ae61db14-9c26-40ca-ab1f-e157a0e8a5e1</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Get Required Documents 2"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="aa99c25a-00df-42eb-bc9e-bbfb204715ab" targetRef="53e7135a-0e89-45c0-9c92-da1f1c53c967" name="To Error Handling" id="4610b27a-8a16-4a05-8f87-c990678fbd49">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="af930738-d36f-4e57-b8e6-f5d94314787a" targetRef="53e7135a-0e89-45c0-9c92-da1f1c53c967" name="To Error Handling" id="afa728c5-f024-4357-8c31-7387ed4a6e03">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="53e7135a-0e89-45c0-9c92-da1f1c53c967" targetRef="27d806a4-e4b3-4c61-bb80-149825d59a5c" name="To End" id="ae61db14-9c26-40ca-ab1f-e157a0e8a5e1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="27d806a4-e4b3-4c61-bb80-149825d59a5c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="752" y="190" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ae61db14-9c26-40ca-ab1f-e157a0e8a5e1</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="398d5181-6f76-4594-882c-a192a21cc100" eventImplId="6a53d0e9-5805-45db-8c90-8f5da7292cc7">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.6d83ac2f-7a03-4af3-8ec1-93d2a246984d" />
                    
                    
                    <ns16:sequenceFlow sourceRef="d230b937-036c-4eea-9f13-b124395e692e" targetRef="ee42c9e4-14dd-461d-8072-1376f66937ca" name="To Get Documents In Folder" id="9f341f37-845b-4376-8132-c7b451771a28">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set Missing Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.603f88b9-4f72-4af4-95ff-7d04e50dc660</processLinkId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ee42c9e4-14dd-461d-8072-1376f66937ca</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.886f3c4c-8291-4e97-8f45-fe20f41fe905</toProcessItemId>
            <guid>45b161db-**************-97c4b3ca2c70</guid>
            <versionId>1db96a4a-00e0-4792-aaa5-43f47740337c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ee42c9e4-14dd-461d-8072-1376f66937ca</fromProcessItemId>
            <toProcessItemId>2025.886f3c4c-8291-4e97-8f45-fe20f41fe905</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ae61db14-9c26-40ca-ab1f-e157a0e8a5e1</processLinkId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.53e7135a-0e89-45c0-9c92-da1f1c53c967</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.27d806a4-e4b3-4c61-bb80-149825d59a5c</toProcessItemId>
            <guid>d34ef7b8-e8da-4825-bf7f-17ca28d5d29b</guid>
            <versionId>20c512b5-38e2-4534-a3f5-3f396fbb52e3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.53e7135a-0e89-45c0-9c92-da1f1c53c967</fromProcessItemId>
            <toProcessItemId>2025.27d806a4-e4b3-4c61-bb80-149825d59a5c</toProcessItemId>
        </link>
        <link name="To Validate Required Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.62eeda52-f7c5-455b-ae03-d1f1dd120195</processLinkId>
            <processId>1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.886f3c4c-8291-4e97-8f45-fe20f41fe905</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b869dae4-a686-4c35-b2d2-e5c96ec03d17</toProcessItemId>
            <guid>3ba60f1b-2e93-44ec-bb4c-794697520fdf</guid>
            <versionId>a11b0381-14fc-4a7d-96e0-35a758b2498a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.886f3c4c-8291-4e97-8f45-fe20f41fe905</fromProcessItemId>
            <toProcessItemId>2025.b869dae4-a686-4c35-b2d2-e5c96ec03d17</toProcessItemId>
        </link>
    </process>
</teamworks>

