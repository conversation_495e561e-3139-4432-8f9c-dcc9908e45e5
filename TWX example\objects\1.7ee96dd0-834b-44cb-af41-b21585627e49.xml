<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.7ee96dd0-834b-44cb-af41-b21585627e49" name="Audit Create Data">
        <lastModified>1700199190493</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
        <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de3</guid>
        <versionId>1382d1e5-4d3e-48a1-9c56-e2c1342710c9</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:33eb" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["1004085d-935b-4f0b-8b7d-00f0e577a11a"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":99,"y":126,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"081c9314-33e1-4935-8aa2-fd628f60e06c"},{"incoming":["4a775d57-f5a3-421f-89c6-8f0cb17b3efa","cdaf90c4-7974-40d9-8c80-2cd09e52dc77"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1555,"y":79,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de5"],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID +\" - ServiceName : AUDIT CREATE DATA : END\");\r\n\r\ntw.local.reqID= tw.local.requestID;"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"646302c3-7d88-4f2d-8840-dec30757da05"},{"startQuantity":1,"outgoing":["b3af8797-0b4e-411f-804d-fc11e6ac8cbb"],"incoming":["c27dcbbc-8a95-4f89-8f2f-45efe37c2fad"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":838,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START--Insert Path\");\r\n\r\n\r\n\r\n"]},"name":"Insert Request_info &amp; Retrieve Request ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1988ef7c-d47c-41ca-8f40-ab05373c7958","scriptFormat":"text\/x-javascript","script":{"content":["\r\n\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\n\/***********************************************************************************\/\r\n\ttw.local.sql=\" SELECT ID FROM FINAL TABLE ( \"\r\n\t\t\t+ \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_RequestInfo ( \"\r\n               \t+ \" requestNo, requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,               \"  \r\n\t\t\t+ \" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \t\t\t \"  \r\n\t\t\t+ \" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \"  \r\n\t\t\t+ \" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \"  \r\n\t\t\t+ \" amountDefAvalization, collectableAmount, outstandingAmount, maturityDate, maturityDays, financeApprovalNo, hubCode,  \"  \r\n\t\t\t+ \" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \t\t\t\t\t  \t\t\t \"  \r\n\t\t\t+ \"   CBEClassification, shipmentMethod, shipmentDate, \t\t\t\t\t\t \t\t\t\t\t \"  \r\n\t\t\t+ \" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \t\t\t \"  \r\n\t\t\t+ \" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \t,ISLIQUIDATED,\t \" \r\n\t\t\t+\"  BPMInstanceNumber, currentStepName, substatus,\t\t\t\t\t\t\t\t\t\t\t \"\r\n\t\t\t+\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \"\r\n\t\t\t+ \" ) VALUES\" \r\n\t\t\t+ \" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\"\r\n\t\t\t+\",?,?,?,?,?,?,?)\"\r\n\t\t\t+ \" )\";\r\n\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\"0\")); \r\n \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \r\n  \r\n\/\/\/\r\n\r\n\/\/CHARGESACCOUNT\r\n\/\/TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \r\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \r\n\r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n"]}},{"targetRef":"6b290809-0a1a-487e-8fcf-899da748dc05","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To SQL Execute Statement","declaredType":"sequenceFlow","id":"b3af8797-0b4e-411f-804d-fc11e6ac8cbb","sourceRef":"1988ef7c-d47c-41ca-8f40-ab05373c7958"},{"startQuantity":1,"outgoing":["4deb808a-8acb-44c0-818d-8f2dbea1d4cb"],"incoming":["b3af8797-0b4e-411f-804d-fc11e6ac8cbb"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":955,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["1"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6b290809-0a1a-487e-8fcf-899da748dc05","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"29ef6559-8a76-4830-84f5-e12eee5f40a1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Request ID","declaredType":"sequenceFlow","id":"4deb808a-8acb-44c0-818d-8f2dbea1d4cb","sourceRef":"6b290809-0a1a-487e-8fcf-899da748dc05"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLParameter();\nautoObject[0].value = null;\nautoObject[0].type = \"\";\nautoObject[0].mode = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"sqlParameters","isCollection":true,"declaredType":"dataObject","id":"2056.c7095e57-5f5f-49e1-89d1-7aa304a64524"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.116007eb-077a-4059-8f3b-1ff2f6837c3d"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.cb80769f-3387-4e8b-8b5e-82a6dfd69a8e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"0"}]},"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"requestID","isCollection":false,"declaredType":"dataObject","id":"2056.63cfedee-d6e5-4a3c-8e47-d9d6176aaf3f"},{"startQuantity":1,"outgoing":["4a775d57-f5a3-421f-89c6-8f0cb17b3efa"],"incoming":["aeebf34e-e506-4186-8d36-a9ad0378f4f6"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1427,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Insert ODC Request Execute SQL","dataInputAssociation":[{"targetRef":"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.628ceac6-aa42-426b-97c7-540674f12f38","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"SQLResult\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.24961b69-3b9b-4311-8707-6b6dfffaf207"]}],"calledElement":"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();\nautoObject[0].sql = \"\";\nautoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\nautoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\nautoObject[0].parameters[0].value = null;\nautoObject[0].parameters[0].type = \"\";\nautoObject[0].parameters[0].mode = \"\";\nautoObject[0].maxRows = 0;\nautoObject"}]},"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.ce409362-d245-4476-8bac-bdafde3da38f"},{"targetRef":"646302c3-7d88-4f2d-8840-dec30757da05","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"4a775d57-f5a3-421f-89c6-8f0cb17b3efa","sourceRef":"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81"},{"startQuantity":1,"outgoing":["aeebf34e-e506-4186-8d36-a9ad0378f4f6"],"incoming":["f0ce710d-dacd-48c1-87a4-b97b7d2ef860","3f77b7cb-8b99-48b5-844b-6fdfa82c5b42"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":1313,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Insert ODC Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4f8c5959-0089-4977-8ac8-802b96f20d99","scriptFormat":"text\/x-javascript","script":{"content":["\r\ntw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\n\r\n\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\n\/***********************************************************************************\/\r\n\r\n\/**-----------------------------------------------------------------------**\/\r\n\/**-------------------------- Log All ODC Data ---------------------------**\/\r\n\/**-----------------------------------------------------------------------**\/\r\n\r\nvar i = 0;\r\nvar sqlStatementLen= 0;\r\n\r\n\/**-------------------------- Log Invoice Data --------------------------**\/\r\n\r\nwhile (i &lt; tw.local.odcRequest.BasicDetails.Invoice.listLength) {\r\n\t\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\n\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_Invoice ( \" \r\n\t\t\t\t\t\t\t\t  + \" requesrID, invoiceNo , invoiceDate\" \r\n\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\n\t\t\t\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\t\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.requestID));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate));\r\n\t\r\n    i++;\r\n}\r\n \/**-------------------------- Log Bills Data --------------------------**\/\r\ni = 0;\r\n\r\nwhile (i &lt; tw.local.odcRequest.BasicDetails.Bills.listLength) {\r\n\t\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\n\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_Bills ( \" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, billOfLadingRef, billOfLadingDate\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\n\t\t\t\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\t\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate));\r\n\r\n    i++;\r\n}\r\n \/**-------------------------- Log Parties Data --------------------------**\/\r\n\r\n\/**-------------------------- Set SQL query to add party record --------------------------**\/\r\nfunction AuditParty(requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address){\r\n\r\n\tif(partyId != null || partyId != \"\"){\r\n\t\t\r\n\t\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\n\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\n\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_PartiesInfo ( \" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, partyId, partyType, cif, partyName,Country,Language,\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  +\"  Reference, Address1, Address2, Address3, Media, Address\" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?,?,?,?,?,?,?) \";\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(requesrID));\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyId));\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyType));\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(cif));\t\t\r\n\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyName));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(country));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(language));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(reference));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address1));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address2));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address3));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(media));\t\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address));\t\r\n\r\n\t\treturn true;\r\n\t}\r\n\telse{\r\n\t\treturn false;\r\n\t}\r\n}\r\n\/\/Params: requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address\r\n\/\/Audit Drawer \r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawer.partyId , \"Drawer\", \"\" , tw.local.odcRequest.Parties.Drawer.partyName, \r\n tw.local.odcRequest.Parties.Drawer.country, tw.local.odcRequest.Parties.Drawer.Language, tw.local.odcRequest.Parties.Drawer.Reference,\r\n tw.local.odcRequest.Parties.Drawer.address1, tw.local.odcRequest.Parties.Drawer.address2, tw.local.odcRequest.Parties.Drawer.address3,\"\",\"\");\r\n\r\n\/\/Audit Drawee\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawee.partyId , \"Drawee\", \"\", tw.local.odcRequest.Parties.Drawee.partyName,\r\ntw.local.odcRequest.Parties.Drawee.country, tw.local.odcRequest.Parties.Drawee.Language, tw.local.odcRequest.Parties.Drawee.Reference,\r\ntw.local.odcRequest.Parties.Drawee.address1, tw.local.odcRequest.Parties.Drawee.address2, tw.local.odcRequest.Parties.Drawee.address3,\"\",\"\" );\r\n\r\n\/\/Audit collecting Bank\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.collectingBank.id , \"collecting Bank\",tw.local.odcRequest.Parties.collectingBank.cif,\r\ntw.local.odcRequest.Parties.collectingBank.name, tw.local.odcRequest.Parties.collectingBank.country, tw.local.odcRequest.Parties.collectingBank.language,\r\ntw.local.odcRequest.Parties.collectingBank.reference, tw.local.odcRequest.Parties.collectingBank.address1, tw.local.odcRequest.Parties.collectingBank.address2,\r\ntw.local.odcRequest.Parties.collectingBank.address3, tw.local.odcRequest.Parties.collectingBank.media, tw.local.odcRequest.Parties.collectingBank.address);\r\n\r\n\/\/Audit Accountee\/ Case In Need\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.partyTypes.partyId, tw.local.odcRequest.Parties.partyTypes.partyType.value, tw.local.odcRequest.Parties.partyTypes.partyCIF,\r\ntw.local.odcRequest.Parties.partyTypes.partyName, tw.local.odcRequest.Parties.partyTypes.country, tw.local.odcRequest.Parties.partyTypes.language, tw.local.odcRequest.Parties.partyTypes.refrence,\r\ntw.local.odcRequest.Parties.partyTypes.address1, tw.local.odcRequest.Parties.partyTypes.address2, tw.local.odcRequest.Parties.partyTypes.address3, \"\", \"\");\r\n\r\n \/**-------------------------- Log Charges And Commissions Data --------------------------**\/\r\n\r\ni = 0;\r\n\r\nwhile (i &lt; tw.local.odcRequest.ChargesAndCommissions.listLength) {\r\n\t\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\n\t\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_CHARGESCOMMISSIONS ( \" \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, defaultCurrency, defaultAmount, chargeAmount, waiver, accountClass,\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" accountNo, branchCode, currency, balance, balanceSign, standardExRate,\"   \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \"negotiatedExRate, debitedAmount, COMPONENT\"\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)\";\r\n\t\t\t\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\t\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultAmount));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount));\r\n\t\r\n\tif(tw.local.odcRequest.ChargesAndCommissions[i].waiver)\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\"1\"));\r\n\telse\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\"0\"));\r\n\t\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount)\r\n\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount ={};\r\n\t    \r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass)\r\n\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass ={};\r\n\t    \r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value));\r\n\t\r\n    \r\n\tvar accountClass= tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value;\r\n\t\r\n\tif(accountClass == \"GL Account\"){\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo));\r\n\t}\r\n\t\/\/Customer Account\r\n\telse{\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo));\r\n\t}\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode));\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency)\r\n\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency = {};\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign));\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount)\r\n\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount = {};\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].component));\r\n\t\r\n    i++;\r\n}\r\n \/**-------------------------- Log FlexCube Transactions Data --------------------------**\/ \r\ni = 0;\r\n\r\nwhile (i &lt; tw.local.odcRequest.FcCollections.selectedTransactions.listLength) {\r\n\t\r\n\r\n\t\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\n\r\n \ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_FCTransaction ( \" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID,accountNo, postingDate, valueDate, transactionAmount, AMOUNTFORCURRENTREQUEST, TRANSACTIONREFNO \"\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?)\";\r\n\t\t\t\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\t\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest));\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber));\r\n    i++;\r\n}\r\n \/**-------------------------- Log Multi Tenor dates Data --------------------------**\/\r\ni = 0;\r\n\r\nwhile (i &lt; tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.listLength) {\r\n\t\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\n\r\n \ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_MultiTenorDates ( \" \r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, installmentDate, installmentAmount \"\t\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\n\t\t\t\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\t\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date));\t\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount));\r\n\r\n    i++;\r\n}\r\n     "]}},{"targetRef":"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Insert ODC Request Execute SQL","declaredType":"sequenceFlow","id":"aeebf34e-e506-4186-8d36-a9ad0378f4f6","sourceRef":"4f8c5959-0089-4977-8ac8-802b96f20d99"},{"startQuantity":1,"outgoing":["cdaf90c4-7974-40d9-8c80-2cd09e52dc77"],"incoming":["fd084925-7d7f-49d7-8989-1e1357be3d46","7919967e-0eb7-4dc7-804f-4b4f47f29532","87a6fb28-a343-4a33-812e-e6eb0a6463f0","6371f870-49c9-4241-87f2-2d5a5de4eb27","ef61f92e-9713-4cb1-8aef-8065c9fa7428"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":1458,"y":203,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Audit Create Data\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"fcc15744-4366-40c0-863f-44d3fbef78dd","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"646302c3-7d88-4f2d-8840-dec30757da05","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"cdaf90c4-7974-40d9-8c80-2cd09e52dc77","sourceRef":"fcc15744-4366-40c0-863f-44d3fbef78dd"},{"startQuantity":1,"outgoing":["c9f833af-b3c3-453e-8b46-df827d4264eb"],"extensionElements":{"nodeVisualInfo":[{"color":"#FFE14F","width":95,"x":207,"y":187,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+33311 +\" - ServiceName : AUDIT CREATE DATA : START\");\r\n \r\n"]},"name":"Test Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"c32dfa16-a852-4585-896b-47642ffee8bd","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.odcRequest.appInfo.instanceID= \"33311\";\r\n tw.local.odcRequest.requestNo= \"12345678911111_03\";\r\n tw.local.odcRequest.requestNature.name= \"new\";\r\n tw.local.odcRequest.requestType.name = \"collect \"\r\n tw.local.odcRequest.requestDate= new TWDate();\r\n tw.local.odcRequest.BasicDetails.requestState=\"Final\";\r\n tw.local.odcRequest.appInfo.status=\"Completed\";\r\n tw.local.odcRequest.parentRequestNo=\"12345678911111\";\r\n tw.local.odcRequest.BasicDetails.flexCubeContractNo=\"11112222\";\r\n tw.local.odcRequest.BasicDetails.contractStage=\"stage3\";\r\n tw.local.odcRequest.BasicDetails.exportPurpose.name=\"exportP\";\r\n tw.local.odcRequest.BasicDetails.paymentTerms.name= \"PT\";\r\n tw.local.odcRequest.BasicDetails.productCategory.name= \"PCpc\";\r\n tw.local.odcRequest.BasicDetails.commodityDescription= \"CD11\";\r\n tw.local.odcRequest.CustomerInfo.cif= \"1234\";\r\n tw.local.odcRequest.CustomerInfo.customerName=\"smsma\";\r\n tw.local.odcRequest.ContractCreation.baseDate= new TWDate();\r\n tw.local.odcRequest.ContractCreation.valueDate = new TWDate();\r\n tw.local.odcRequest.ContractCreation.tenorDays= 3;\r\n tw.local.odcRequest.ContractCreation.transitDays= 4;\r\n tw.local.odcRequest.ContractCreation.maturityDate = new TWDate();\r\n tw.local.odcRequest.ContractCreation.userReference= \"1223231\";\r\n \/**********************************************************************************\/\r\n tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\n tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"bill 03\";\r\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\n tw.local.odcRequest.BasicDetails.Bills[1] = new tw.object.Bills();\r\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingRef = \"Bill 04\";\r\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingDate = new TWDate();\r\n \r\n tw.local.odcRequest.BasicDetails.Bills[2] = new tw.object.Bills();\r\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingRef = \"Bill 05\";\r\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingDate = new TWDate();\r\n \r\n tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\n tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"Inv 11\";\r\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\n\/\/ tw.local.odcRequest.BasicDetails.Invoice[1] = new tw.object.Invoice();\r\n\/\/ tw.local.odcRequest.BasicDetails.Invoice[1].invoiceNo = \"Inv 08\";\r\n\/\/  tw.local.odcRequest.BasicDetails.Invoice[1].invoiceDate = new TWDate();\r\n \r\n \/******************************************************************************************\/\r\n\r\n tw.local.odcRequest.Parties = new tw.object.odcParties();\r\n tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\r\n tw.local.odcRequest.Parties.Drawer.partyId = \"00000000\";\r\n tw.local.odcRequest.Parties.Drawer.partyName = \"Party01\";\r\n tw.local.odcRequest.Parties.Drawer.country = \"Egp\";\r\n tw.local.odcRequest.Parties.Drawer.Language = \"EN\";\r\n tw.local.odcRequest.Parties.Drawer.Reference = \"1234\";\r\n tw.local.odcRequest.Parties.Drawer.address1 = \"add1\";\r\n tw.local.odcRequest.Parties.Drawer.address2 = \"add2\";\r\n tw.local.odcRequest.Parties.Drawer.address3 = \"add3\";\r\n tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\r\n tw.local.odcRequest.Parties.Drawee.partyId = \"********\";\r\n tw.local.odcRequest.Parties.Drawee.partyName = \"Party02\";\r\n tw.local.odcRequest.Parties.Drawee.country = \"EUR\";\r\n tw.local.odcRequest.Parties.Drawee.Language = \"arabic\";\r\n tw.local.odcRequest.Parties.Drawee.Reference = \"ref1\";\r\n tw.local.odcRequest.Parties.Drawee.address1 = \"address1\";\r\n tw.local.odcRequest.Parties.Drawee.address2 = \"address2\";\r\n tw.local.odcRequest.Parties.Drawee.address3 = \"address3\";\r\n tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\r\n tw.local.odcRequest.Parties.collectingBank.id = \"********\";\r\n tw.local.odcRequest.Parties.collectingBank.name = \"Party03\";\r\n tw.local.odcRequest.Parties.collectingBank.country = \"Egypt\";\r\n tw.local.odcRequest.Parties.collectingBank.language = \"french\";\r\n tw.local.odcRequest.Parties.collectingBank.reference = \"ref2\";\r\n tw.local.odcRequest.Parties.collectingBank.address1 = \"address1\";\r\n tw.local.odcRequest.Parties.collectingBank.address2 = \"address2\";\r\n tw.local.odcRequest.Parties.collectingBank.address3 = \"address3\";\r\n tw.local.odcRequest.Parties.collectingBank.cif = \"********\";\r\n tw.local.odcRequest.Parties.collectingBank.media = \"swift\";\r\n tw.local.odcRequest.Parties.collectingBank.address = \"address\";\r\n tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\r\n tw.local.odcRequest.Parties.partyTypes.partyCIF = \"********\";\r\n tw.local.odcRequest.Parties.partyTypes.partyId = \"********\";\r\n tw.local.odcRequest.Parties.partyTypes.partyName = \"Party04\";\r\n tw.local.odcRequest.Parties.partyTypes.country = \"italy\";\r\n tw.local.odcRequest.Parties.partyTypes.language = \"italian\";\r\n tw.local.odcRequest.Parties.partyTypes.refrence = \"reference0\";\r\n tw.local.odcRequest.Parties.partyTypes.address1 = \"add11\";\r\n tw.local.odcRequest.Parties.partyTypes.address2 = \"add22\";\r\n tw.local.odcRequest.Parties.partyTypes.address3 = \"add33\";\r\n tw.local.odcRequest.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.Parties.partyTypes.partyType.name = \"Case In Need\";\r\n tw.local.odcRequest.Parties.partyTypes.partyType.value = \"Case In Need\";\r\n\/\/ ------------------------------------------------------------------------------\r\n tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\n tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\n tw.local.odcRequest.ChargesAndCommissions[0].component = \"component\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \"EGP\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \"EGP\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 11.0;\r\n tw.local.odcRequest.ChargesAndCommissions[0].waiver = false;\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"GL Account\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"1111\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"111\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.branchCode = \"055\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \"EGP\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \"EGP\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balance = 200;\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balanceSign = \"D\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.isOverDraft = true;\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.standardExRate = 990.0;\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 990.0;\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.amountInAccount = 99.9;\r\n tw.local.odcRequest.ChargesAndCommissions[0].rateType = \"\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].description = \"\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultPercentage = 99.9;\r\n tw.local.odcRequest.ChargesAndCommissions[0].changePercentage = 99.9;\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultAmount = 99.1;\r\n tw.local.odcRequest.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\n tw.local.odcRequest.ChargesAndCommissions[0].flatAmount = 900.0;\r\n \/******************************************************************************************\/\r\n\r\n tw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\n tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.FcCollections.currency.name = \"\";\r\n tw.local.odcRequest.FcCollections.currency.value = \"Egypt\";\r\n tw.local.odcRequest.FcCollections.standardExchangeRate = 119.0;\r\n tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 99.9;\r\n tw.local.odcRequest.FcCollections.fromDate = new TWDate();\r\n tw.local.odcRequest.FcCollections.ToDate = new TWDate();\r\n tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.FcCollections.accountNo.name = \"\";\r\n tw.local.odcRequest.FcCollections.accountNo.value = \"11999\";\r\n tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \"22222\";\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \"11199\";\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 111.0;\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 109.0;\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \"Egypt\";\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 190.0;\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency =99.0;\r\n tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \"\";\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \"\";\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\n tw.local.odcRequest.FcCollections.isReversed = false;\r\n tw.local.odcRequest.FcCollections.usedAmount = 999.0;\r\n tw.local.odcRequest.FcCollections.calculatedAmount = 119.0;\r\n tw.local.odcRequest.FcCollections.totalAllocatedAmount = 999.9;\r\n tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0].name = \"11119\";\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0].value = \"11119\";\r\n \/******************************************************************************************\/\r\n\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 100.0; \r\n\r\ntw.local.odcRequest.appInfo.instanceID= \"1234\";\r\ntw.local.odcRequest.appInfo.stepName=\"Create Act06\";\r\ntw.local.odcRequest.appInfo.subStatus=\"Completed\";\r\n"]}},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"new TWDate()"}]},"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"date","isCollection":false,"declaredType":"dataObject","id":"2056.f401dcad-832c-452f-823c-6fca66361cee"},{"targetRef":"*************-4299-8059-f22e8c1ef414","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To update?","declaredType":"sequenceFlow","id":"7197dad2-4b36-4cc6-8e17-52094b099c7c","sourceRef":"9f4eb6e8-ca0b-4b3f-8785-678607e11258"},{"startQuantity":1,"outgoing":["13e4407a-2000-4320-81ff-e1245b2f17b5"],"incoming":["55643b29-ffc3-4ee6-89ee-92865f61e092"],"extensionElements":{"postAssignmentScript":["tw.local.sqlOut= tw.local.sql;\r\ntw.local.reqID= tw.local.requestID;"],"nodeVisualInfo":[{"width":95,"x":832,"y":173,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START--Update Path\");\r\n\r\n\r\n\r\n\r\n\r\n"]},"name":"Update Request Info","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8895b0f8-2ec0-49f3-8848-9ddc17cb68e2","scriptFormat":"text\/x-javascript","script":{"content":["\r\n\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\n\/*********************************************************************************************************************************************\/\r\n\t\r\ntw.local.sql= \"UPDATE \"+ tw.env.DBSchema +  \".ODC_RequestInfo SET (\"\r\n\t\t+ \" requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,         \t\t       \"  \r\n\t\t+ \" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \t\t\t \"  \r\n\t\t+ \" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \"  \r\n\t\t+ \" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \"  \r\n\t\t+ \" amountDefAvalization, collectableAmount,\"\r\n\t\t+ \"outstandingAmount, maturityDate, maturityDays,\"\r\n\t\t+ \"financeApprovalNo,\"\r\n\t\t+\"hubCode,  \"  \r\n\t\t+ \" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \t\t\t\t\t  \t\t\t \"  \r\n\t\t+ \" CBEClassification, shipmentMethod, shipmentDate, \t\t\t\t\t\t \t\t\t\t\t\t \"  \r\n\t\t+ \" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \t\t\t \"  \r\n\t\t+ \" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \t,ISLIQUIDATED ,\t \" \r\n\t\t+ \" BPMInstanceNumber, currentStepName, substatus, \t\t\t\t\t\t\t\t\t\t\t \t\"\r\n\t\t+\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \"\r\n\t\t+ \" ) \"\r\n\t\t+\" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\"\r\n\t\t+\",?,?,?,?,?,?,?,?,?\"\r\n\t\t+\")\"\r\n\t\t+\" WHERE requestNo = ? \";\r\n\t\t\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\"0\")); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \r\n\/\/\r\n\/\/TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \r\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\n\r\n"]}},{"startQuantity":1,"outgoing":["6878fe71-cf06-4406-817b-c4ec9fb17b03"],"incoming":["13e4407a-2000-4320-81ff-e1245b2f17b5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":963,"y":173,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Update Request Info","dataInputAssociation":[{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]},{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"c5082aee-343a-452e-8b33-35b492b4251b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"c5082aee-343a-452e-8b33-35b492b4251b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Update Request Info","declaredType":"sequenceFlow","id":"13e4407a-2000-4320-81ff-e1245b2f17b5","sourceRef":"8895b0f8-2ec0-49f3-8848-9ddc17cb68e2"},{"startQuantity":1,"outgoing":["f4368575-7433-403f-8e26-387c7f93456b"],"incoming":["6878fe71-cf06-4406-817b-c4ec9fb17b03"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1080,"y":173,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["\r\n \r\n"]},"name":"Delete ODC Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"3b354799-bd42-4854-868d-e0629c388301","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\n\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\n\/********************************************************************************\/\r\n\r\n\/**-------------------------------------------------------------------------**\/\r\n\/**-------------------------- DELETE All ODC Data ---------------------------**\/\r\n\/**-------------------------------------------------------------------------**\/\r\n\r\nvar sqlStatementLen= 0;\r\n\/**************************************************************************************************************************************************************\/\r\n\/**------------------------------This function is used to delete multiple records from table using the requestId---------------------------------------------**\/\r\n\/**----------------------------------------------- tableName= the name of the table in the DB ---------------------------------------------------------------**\/\r\n\/**************************************************************************************************************************************************************\/\r\nfunction DeleteRecordsByID(tableName){\r\n\t \r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\n\t\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" DELETE FROM \"+ tw.env.DBSchema + \".\"+ tableName + \" WHERE REQUESRID= ? \";\r\n\t\t\t\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\n  \t \r\n}\r\n\/**************************************************************************************************************************************************************\/\r\n\r\n\/**-------------------------- Delete Data from Tables by request id --------------------------**\/\r\n\r\n\tDeleteRecordsByID (      \"ODC_Invoice\"      );\r\n\tDeleteRecordsByID (      \"ODC_Bills\"        );\r\n\tDeleteRecordsByID (   \"ODC_PartiesInfo\"     );\t\r\n\tDeleteRecordsByID ( \"ODC_CHARGESCOMMISSIONS\");\r\n\tDeleteRecordsByID (   \"ODC_FCTransaction\"   );\r\n\tDeleteRecordsByID ( \"ODC_MultiTenorDates\"   );\t\r\n\r\n "]}},{"targetRef":"3b354799-bd42-4854-868d-e0629c388301","extensionElements":{"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Delete ODC Data","declaredType":"sequenceFlow","id":"6878fe71-cf06-4406-817b-c4ec9fb17b03","sourceRef":"c5082aee-343a-452e-8b33-35b492b4251b"},{"targetRef":"548669f1-7641-4c40-8867-17f51c191b64","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Execute SQL Statements","declaredType":"sequenceFlow","id":"f4368575-7433-403f-8e26-387c7f93456b","sourceRef":"3b354799-bd42-4854-868d-e0629c388301"},{"startQuantity":1,"outgoing":["f0ce710d-dacd-48c1-87a4-b97b7d2ef860"],"incoming":["f4368575-7433-403f-8e26-387c7f93456b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1190,"y":173,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Delete ODC Request Execute SQL","dataInputAssociation":[{"targetRef":"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.628ceac6-aa42-426b-97c7-540674f12f38","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"SQLResult\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"548669f1-7641-4c40-8867-17f51c191b64","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.24961b69-3b9b-4311-8707-6b6dfffaf207"]}],"calledElement":"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af"},{"targetRef":"4f8c5959-0089-4977-8ac8-802b96f20d99","extensionElements":{"endStateId":["guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Insert ODC Data","declaredType":"sequenceFlow","id":"f0ce710d-dacd-48c1-87a4-b97b7d2ef860","sourceRef":"548669f1-7641-4c40-8867-17f51c191b64"},{"startQuantity":1,"outgoing":["3f77b7cb-8b99-48b5-844b-6fdfa82c5b42"],"incoming":["4deb808a-8acb-44c0-818d-8f2dbea1d4cb"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1073,"y":56,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get Request ID","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"29ef6559-8a76-4830-84f5-e12eee5f40a1","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.requestID= tw.local.results[0].rows[0].data[0];\r\n"]}},{"targetRef":"4f8c5959-0089-4977-8ac8-802b96f20d99","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Insert Invoice Data","declaredType":"sequenceFlow","id":"3f77b7cb-8b99-48b5-844b-6fdfa82c5b42","sourceRef":"29ef6559-8a76-4830-84f5-e12eee5f40a1"},{"startQuantity":1,"outgoing":["b85ff298-f81c-4996-807b-ca607cbe39e9"],"incoming":["c9f833af-b3c3-453e-8b46-df827d4264eb","f71d7316-fa39-41e3-8bd4-1dcd87381d2d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":354,"y":103,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START\");\r\n \r\n\r\n\r\n\r\n"]},"name":"Search for requestNo","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b","scriptFormat":"text\/x-javascript","script":{"content":["\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\n\/***********************************************************************************\/\r\n\ttw.local.sql=\"SELECT ID FROM \"+ tw.env.DBSchema + \".ODC_REQUESTINFO WHERE REQUESTNO = ? \"\r\n\t\t\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\n\r\n\r\n"]}},{"startQuantity":1,"outgoing":["7197dad2-4b36-4cc6-8e17-52094b099c7c"],"incoming":["b85ff298-f81c-4996-807b-ca607cbe39e9"],"extensionElements":{"postAssignmentScript":["\r\nif (!!tw.local.results &amp;&amp; tw.local.results.listLength &gt; 0 )\r\n\ttw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;\r\n\t\r\n\t "],"nodeVisualInfo":[{"width":95,"x":479,"y":103,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get ID of Request SQL Execute Statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["1"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"9f4eb6e8-ca0b-4b3f-8785-678607e11258","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"9f4eb6e8-ca0b-4b3f-8785-678607e11258","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get ID of Request SQL Execute Statement","declaredType":"sequenceFlow","id":"b85ff298-f81c-4996-807b-ca607cbe39e9","sourceRef":"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b"},{"outgoing":["c27dcbbc-8a95-4f89-8f2f-45efe37c2fad","55643b29-ffc3-4ee6-89ee-92865f61e092"],"incoming":["7197dad2-4b36-4cc6-8e17-52094b099c7c"],"default":"c27dcbbc-8a95-4f89-8f2f-45efe37c2fad","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":32,"x":682,"y":122,"declaredType":"TNodeVisualInfo","height":32}]},"name":"update?","declaredType":"exclusiveGateway","id":"*************-4299-8059-f22e8c1ef414"},{"targetRef":"1988ef7c-d47c-41ca-8f40-ab05373c7958","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"c27dcbbc-8a95-4f89-8f2f-45efe37c2fad","sourceRef":"*************-4299-8059-f22e8c1ef414"},{"targetRef":"8895b0f8-2ec0-49f3-8848-9ddc17cb68e2","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestID\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"55643b29-ffc3-4ee6-89ee-92865f61e092","sourceRef":"*************-4299-8059-f22e8c1ef414"},{"targetRef":"2f5bf898-59ef-4548-80f2-bfecfcd3fc4b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init","declaredType":"sequenceFlow","id":"1004085d-935b-4f0b-8b7d-00f0e577a11a","sourceRef":"081c9314-33e1-4935-8aa2-fd628f60e06c"},{"targetRef":"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Search for requestNo","declaredType":"sequenceFlow","id":"c9f833af-b3c3-453e-8b46-df827d4264eb","sourceRef":"c32dfa16-a852-4585-896b-47642ffee8bd"},{"parallelMultiple":false,"outgoing":["fd084925-7d7f-49d7-8989-1e1357be3d46"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"582948a9-cdc3-457a-84e4-8902e477907a","otherAttributes":{"eventImplId":"30d16f80-5b70-4f1f-8634-12dbbf5fd5c4"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1384,"y":226,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error Event","declaredType":"intermediateCatchEvent","id":"17ed215d-500f-49f1-8c31-7bd7cfcd3e67"},{"targetRef":"fcc15744-4366-40c0-863f-44d3fbef78dd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"fd084925-7d7f-49d7-8989-1e1357be3d46","sourceRef":"17ed215d-500f-49f1-8c31-7bd7cfcd3e67"},{"startQuantity":1,"outgoing":["f71d7316-fa39-41e3-8bd4-1dcd87381d2d"],"incoming":["1004085d-935b-4f0b-8b7d-00f0e577a11a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":178,"y":103,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2f5bf898-59ef-4548-80f2-bfecfcd3fc4b","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar requestType= tw.epv.RequestType.Create+\"\";\r\nif (tw.local.odcRequest.requestType.value == requestType){\r\n\ttw.local.odcRequest.parentRequestNo= tw.local.odcRequest.requestNo;\r\n\ttw.local.odcRequest.BasicDetails.parentRequestNo= tw.local.odcRequest.requestNo;\r\n\t\r\n\t}\r\n\r\n"]}},{"targetRef":"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"f71d7316-fa39-41e3-8bd4-1dcd87381d2d","sourceRef":"2f5bf898-59ef-4548-80f2-bfecfcd3fc4b"},{"parallelMultiple":false,"outgoing":["6371f870-49c9-4241-87f2-2d5a5de4eb27"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"1c246876-97df-422c-841d-f03f1aff1e32"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"794653c5-6b68-4d7c-81e9-7f67f750847b","otherAttributes":{"eventImplId":"6052246d-788e-4bdf-8abf-44441276f2a0"}}],"attachedToRef":"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1488,"y":114,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"15dbe67a-a7c9-4140-8add-4e119dc15301","outputSet":{}},{"parallelMultiple":false,"outgoing":["87a6fb28-a343-4a33-812e-e6eb0a6463f0"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"124edf7c-ca1a-4ac9-8c62-af467eeedfbe"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"70012514-bb4c-468c-89fb-4965e328da43","otherAttributes":{"eventImplId":"26862378-5ff7-4f34-8c29-6a0a3b1bd431"}}],"attachedToRef":"548669f1-7641-4c40-8867-17f51c191b64","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1225,"y":231,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"4f33d610-6ca6-4031-8084-0666bcc26181","outputSet":{}},{"parallelMultiple":false,"outgoing":["7919967e-0eb7-4dc7-804f-4b4f47f29532"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"49982f30-6256-48ef-82dc-48b29d350b1b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"513b40ec-e25c-43e4-8e50-a73ad4502c30","otherAttributes":{"eventImplId":"7cf5af0d-181d-4961-878f-f7a6f08ef2be"}}],"attachedToRef":"c5082aee-343a-452e-8b33-35b492b4251b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":972,"y":231,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"a48629c7-9082-4589-8d93-8b735c1dcc82","outputSet":{}},{"targetRef":"fcc15744-4366-40c0-863f-44d3fbef78dd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"7919967e-0eb7-4dc7-804f-4b4f47f29532","sourceRef":"a48629c7-9082-4589-8d93-8b735c1dcc82"},{"targetRef":"fcc15744-4366-40c0-863f-44d3fbef78dd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"87a6fb28-a343-4a33-812e-e6eb0a6463f0","sourceRef":"4f33d610-6ca6-4031-8084-0666bcc26181"},{"targetRef":"fcc15744-4366-40c0-863f-44d3fbef78dd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"6371f870-49c9-4241-87f2-2d5a5de4eb27","sourceRef":"15dbe67a-a7c9-4140-8add-4e119dc15301"},{"parallelMultiple":false,"outgoing":["ef61f92e-9713-4cb1-8aef-8065c9fa7428"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"92c496b5-54cd-4f7d-8297-2bc9367cd880"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"6aed0f61-ac99-4f37-8726-1ba2504371d7","otherAttributes":{"eventImplId":"f042136e-6141-4382-89eb-4cf18cec5008"}}],"attachedToRef":"6b290809-0a1a-487e-8fcf-899da748dc05","extensionElements":{"nodeVisualInfo":[{"width":24,"x":990,"y":114,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"bf43fdb4-37f4-4208-80a8-16c178b90bd9","outputSet":{}},{"targetRef":"fcc15744-4366-40c0-863f-44d3fbef78dd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"ef61f92e-9713-4cb1-8aef-8065c9fa7428","sourceRef":"bf43fdb4-37f4-4208-80a8-16c178b90bd9"}],"laneSet":[{"id":"c0d17625-3a14-4a88-8ea7-d9e8d453b9c1","lane":[{"flowNodeRef":["081c9314-33e1-4935-8aa2-fd628f60e06c","646302c3-7d88-4f2d-8840-dec30757da05","1988ef7c-d47c-41ca-8f40-ab05373c7958","6b290809-0a1a-487e-8fcf-899da748dc05","6d3651c3-0746-4c2c-807b-a4f4d7ac6d81","4f8c5959-0089-4977-8ac8-802b96f20d99","fcc15744-4366-40c0-863f-44d3fbef78dd","c32dfa16-a852-4585-896b-47642ffee8bd","8895b0f8-2ec0-49f3-8848-9ddc17cb68e2","c5082aee-343a-452e-8b33-35b492b4251b","3b354799-bd42-4854-868d-e0629c388301","548669f1-7641-4c40-8867-17f51c191b64","29ef6559-8a76-4830-84f5-e12eee5f40a1","ec4e3ae8-5b80-40c3-8718-4361f96b6b4b","9f4eb6e8-ca0b-4b3f-8785-678607e11258","*************-4299-8059-f22e8c1ef414","17ed215d-500f-49f1-8c31-7bd7cfcd3e67","2f5bf898-59ef-4548-80f2-bfecfcd3fc4b","15dbe67a-a7c9-4140-8add-4e119dc15301","4f33d610-6ca6-4031-8084-0666bcc26181","a48629c7-9082-4589-8d93-8b735c1dcc82","bf43fdb4-37f4-4208-80a8-16c178b90bd9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5e7ccb09-8e69-4a08-833a-ede6a123e618","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Audit Create Data","declaredType":"process","id":"1.7ee96dd0-834b-44cb-af41-b21585627e49","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.254cf8eb-2743-4c53-8c52-e51c8c22884e"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"reqID","isCollection":false,"id":"2055.59dc474f-3e47-40ee-8737-ad21d25eb436"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sqlOut","isCollection":false,"id":"2055.0d27ff21-d378-41ac-801e-065cf08cc7a7"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"e9b2480e-1992-4de1-8261-e524f562a11d","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.afad40c5-a38b-475c-8154-b4dabd94b6fe"]}],"outputSet":[{"dataOutputRefs":["2055.254cf8eb-2743-4c53-8c52-e51c8c22884e","2055.59dc474f-3e47-40ee-8737-ad21d25eb436","2055.0d27ff21-d378-41ac-801e-065cf08cc7a7"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"create\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"1111222224444\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.afad40c5-a38b-475c-8154-b4dabd94b6fe</processParameterId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>921a8d09-0241-40aa-b26d-56763bde4083</guid>
            <versionId>d6430302-e92c-4838-bf40-17fde8631b82</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.254cf8eb-2743-4c53-8c52-e51c8c22884e</processParameterId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>27b88540-64e1-412f-8f1d-6eb9f65153e2</guid>
            <versionId>bbcbe250-9db3-4cc0-863b-611911d5396b</versionId>
        </processParameter>
        <processParameter name="reqID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.59dc474f-3e47-40ee-8737-ad21d25eb436</processParameterId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>880debf0-3945-4cf4-8c93-8073f2226151</guid>
            <versionId>0dfb194f-01b9-4735-b744-649f299af4f6</versionId>
        </processParameter>
        <processParameter name="sqlOut">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0d27ff21-d378-41ac-801e-065cf08cc7a7</processParameterId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4150cb3c-a546-4bfb-92f7-428814f5b3dc</guid>
            <versionId>bd51019a-aa17-4340-96aa-797d701e9c41</versionId>
        </processParameter>
        <processVariable name="sqlParameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c7095e57-5f5f-49e1-89d1-7aa304a64524</processVariableId>
            <description isNull="true" />
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>dbaa3cf7-1305-44d5-901a-90ac61268cb8</guid>
            <versionId>8225c37a-f39c-430d-b7c0-55948a35ec4c</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.116007eb-077a-4059-8f3b-1ff2f6837c3d</processVariableId>
            <description isNull="true" />
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d06a24c1-4c06-4c7d-8369-d3d82ac05902</guid>
            <versionId>78fb4085-cc44-4e81-9446-179285baee98</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cb80769f-3387-4e8b-8b5e-82a6dfd69a8e</processVariableId>
            <description isNull="true" />
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc152c6c-785e-4f2c-9602-a31cc72ade0a</guid>
            <versionId>b5dc6e07-09bb-46c0-964c-733f02d6f4f4</versionId>
        </processVariable>
        <processVariable name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.63cfedee-d6e5-4a3c-8e47-d9d6176aaf3f</processVariableId>
            <description isNull="true" />
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>0</defaultValue>
            <guid>cb65610b-6282-4160-a247-7c40704d9008</guid>
            <versionId>a0b7dca5-f374-4a69-964b-8fa1a9ef60c5</versionId>
        </processVariable>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ce409362-d245-4476-8bac-bdafde3da38f</processVariableId>
            <description isNull="true" />
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f5b6523e-a12d-41b1-b726-39e05fd58a03</guid>
            <versionId>74a7e0d3-6457-4b71-92b4-ac9abc9186f3</versionId>
        </processVariable>
        <processVariable name="date">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f401dcad-832c-452f-823c-6fca66361cee</processVariableId>
            <description isNull="true" />
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>new TWDate()</defaultValue>
            <guid>03238a08-35d9-40e4-8033-2eadaf16c4c9</guid>
            <versionId>3984438d-6e2d-4a88-8309-1470bbcd956d</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.548669f1-7641-4c40-8867-17f51c191b64</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Delete ODC Request Execute SQL</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:251d</guid>
            <versionId>16bbc3a5-8862-42d6-8e43-c3f73feac4b5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1190" y="173">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d</errorHandlerItem>
                <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af</attachedProcessRef>
                <guid>1207fcbe-a39d-4fe6-8b27-791fa2ab30d8</guid>
                <versionId>1e899175-4a77-47af-b23c-32345d563e25</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c1a7d243-b5a4-4277-a424-7d5ceaeca375</parameterMappingId>
                    <processParameterId>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</processParameterId>
                    <parameterMappingParentId>3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c3dd68b0-6841-4ee7-816f-40a99110ad67</guid>
                    <versionId>1f8f2d1a-46c3-407e-9e8c-9719d69fa249</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.04338fe6-619b-4803-aa52-12e8a1552b44</parameterMappingId>
                    <processParameterId>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</processParameterId>
                    <parameterMappingParentId>3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>dec739ce-1440-41d8-8f85-c0cdb3841ac8</guid>
                    <versionId>505a685f-2149-446c-9840-01c63f243913</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f265f013-d7af-45ef-b9d7-3884a43624c3</parameterMappingId>
                    <processParameterId>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</processParameterId>
                    <parameterMappingParentId>3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>008b0565-bd1a-4bde-ae77-356768f633a8</guid>
                    <versionId>84309312-9c2c-4235-8519-cce0263d11fe</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.58ec74bc-9acd-467c-9184-3b6744d65def</parameterMappingId>
                    <processParameterId>2055.628ceac6-aa42-426b-97c7-540674f12f38</processParameterId>
                    <parameterMappingParentId>3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7442c9d1-317f-46a3-a142-68710efebb02</guid>
                    <versionId>c1589603-37de-474d-a907-825de74cd773</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Insert ODC Request Execute SQL</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.312838e0-7389-43f9-906e-3991ac664612</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
            <guid>guid:68332a9f3202b062:612a65b7:18aa4a1e041:-6f4a</guid>
            <versionId>263947c3-e524-4f71-b73d-f463a4bb4f47</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1427" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomRight</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d</errorHandlerItem>
                <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.312838e0-7389-43f9-906e-3991ac664612</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af</attachedProcessRef>
                <guid>84f882c4-7f81-4a85-80bd-9c871ba45113</guid>
                <versionId>9389ae06-dc55-41ea-845b-f21982d82cb3</versionId>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bdf4cc12-e06c-45c3-9b25-ae3e00035879</parameterMappingId>
                    <processParameterId>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</processParameterId>
                    <parameterMappingParentId>3012.312838e0-7389-43f9-906e-3991ac664612</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>b0926685-d4a9-4879-895e-5193134e644a</guid>
                    <versionId>39cb94cf-29d6-43e8-a41a-9f287b7341d6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.460e6f9e-a88f-4cbb-9df7-a8c8cbe22d49</parameterMappingId>
                    <processParameterId>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</processParameterId>
                    <parameterMappingParentId>3012.312838e0-7389-43f9-906e-3991ac664612</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d41c8b2d-a27a-4480-a62b-1d200f2b146e</guid>
                    <versionId>4964e24c-b88c-42a6-9f12-f4619ad3e743</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.320c370b-5eaf-4a5b-b4f0-4148bb1e8114</parameterMappingId>
                    <processParameterId>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</processParameterId>
                    <parameterMappingParentId>3012.312838e0-7389-43f9-906e-3991ac664612</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8203d21f-9463-48ee-9465-a4f6a9e2525f</guid>
                    <versionId>c5e9916e-7e30-4499-b8c3-3760664c18d3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.093d176d-cf7c-4429-bffe-6807fc1cfe62</parameterMappingId>
                    <processParameterId>2055.628ceac6-aa42-426b-97c7-540674f12f38</processParameterId>
                    <parameterMappingParentId>3012.312838e0-7389-43f9-906e-3991ac664612</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>58f08d48-1493-451d-9375-5509b8e4e349</guid>
                    <versionId>d825bbb8-9d26-420b-9034-5f84c8664be4</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6b290809-0a1a-487e-8fcf-899da748dc05</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
            <guid>guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3ed7</guid>
            <versionId>292b72ce-9bda-4e00-ade4-12644a6395ea</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.78478636-bda8-4ac6-b938-81ea06531fdd</processItemPrePostId>
                <processItemId>2025.6b290809-0a1a-487e-8fcf-899da748dc05</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>de60251b-c3b5-4f54-9d8d-2e0b2047a841</guid>
                <versionId>37300624-d646-4be5-97be-7204b01d9f1f</versionId>
            </processPrePosts>
            <layoutData x="955" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d</errorHandlerItem>
                <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>db52592a-d54c-4ccf-b560-356f5a179e3d</guid>
                <versionId>245a3991-1cb2-492b-b329-4410a437a936</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fb975ad6-d66e-465c-a2c5-73ef259e5901</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c9c70372-3d68-44c5-8381-88273cf4de52</guid>
                    <versionId>2281673d-173c-4b62-bc8e-a3f4c79ed386</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0e3ad9a5-7556-4eb9-9879-9412b1cc03d3</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>900a540a-1b9f-4ddc-992f-8e026ebc39b1</guid>
                    <versionId>62680f46-2a65-4b24-a6dc-a8f41fa96063</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.64db4079-b70c-4a32-aa14-411e21ea0582</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0c5531dd-8860-4e88-acc5-e2ac082465a4</guid>
                    <versionId>a7559674-b92e-41ad-a227-0e7f15e7f743</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.49a039e8-747a-4c67-85a8-93f01dec0593</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>09d0a855-c388-4877-a2ca-431acbd3d45c</guid>
                    <versionId>cbca44c5-c1d4-4749-bdfd-f3b9251c9d24</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fa1e329b-8689-4fb7-8c00-665f2d063676</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>8a002454-d9e6-48e9-ba38-31de99482d34</guid>
                    <versionId>e3d23ec5-a7a1-4dfd-b6b9-c6405824b85a</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Update Request Info</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7abba84e-a437-4e81-89c8-46808854585a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:-6cb8</guid>
            <versionId>333bab8e-3229-4792-a471-8bc539a02cc4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.cf11e8c2-ff27-4a6c-857d-1f9370278d1c</processItemPrePostId>
                <processItemId>2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID  +" - ServiceName : AUDIT CREATE DATA : START--Update Path");&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</script>
                <guid>fe7aa395-99c7-4211-9af6-87cf69cb97e9</guid>
                <versionId>c7a7d6d6-5511-450e-b420-2e362d183347</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.cc61e741-33e3-4772-add8-8211b632258e</processItemPrePostId>
                <processItemId>2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</processItemId>
                <location>2</location>
                <script>tw.local.sqlOut= tw.local.sql;&#xD;
tw.local.reqID= tw.local.requestID;</script>
                <guid>ebe44ac5-1fe5-4635-b160-672fa5729cd7</guid>
                <versionId>e0b1be3b-aae4-4295-bbd4-2793068f7b34</versionId>
            </processPrePosts>
            <layoutData x="832" y="173">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7abba84e-a437-4e81-89c8-46808854585a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/*********************************************************************************************************************************************/&#xD;
	&#xD;
tw.local.sql= "UPDATE "+ tw.env.DBSchema +  ".ODC_RequestInfo SET ("&#xD;
		+ " requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,         		       "  &#xD;
		+ " fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  			 "  &#xD;
		+ " cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         "  &#xD;
		+ " productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, "  &#xD;
		+ " amountDefAvalization, collectableAmount,"&#xD;
		+ "outstandingAmount, maturityDate, maturityDays,"&#xD;
		+ "financeApprovalNo,"&#xD;
		+"hubCode,  "  &#xD;
		+ " hubName, collectionCurrency, standardExRate, negotiatedExRate,          					  			 "  &#xD;
		+ " CBEClassification, shipmentMethod, shipmentDate, 						 						 "  &#xD;
		+ " importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, 			 "  &#xD;
		+ " bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  	,ISLIQUIDATED ,	 " &#xD;
		+ " BPMInstanceNumber, currentStepName, substatus, 											 	"&#xD;
		+"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN "&#xD;
		+ " ) "&#xD;
		+" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?"&#xD;
		+",?,?,?,?,?,?,?,?,?"&#xD;
		+")"&#xD;
		+" WHERE requestNo = ? ";&#xD;
		&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter("0")); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); &#xD;
//&#xD;
//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); &#xD;
 tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>cfb2a556-37b4-439b-9643-8bbb789d400b</guid>
                <versionId>2c2ca018-47ab-4a32-ad57-35505cf776a4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.646302c3-7d88-4f2d-8840-dec30757da05</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.a85aecba-3c78-4608-bb87-660ff73006a1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de5</guid>
            <versionId>3b6924a8-7359-41a4-b099-145f4d74cf54</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.31c7a11e-57d5-4b01-a565-77d45fed6255</processItemPrePostId>
                <processItemId>2025.646302c3-7d88-4f2d-8840-dec30757da05</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID +" - ServiceName : AUDIT CREATE DATA : END");&#xD;
&#xD;
tw.local.reqID= tw.local.requestID;</script>
                <guid>ebe498f9-997b-4d33-a952-16a22e1d9ab8</guid>
                <versionId>ea659bbb-80bc-4bdc-b2c3-c65ef27cdeac</versionId>
            </processPrePosts>
            <layoutData x="1555" y="79">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.a85aecba-3c78-4608-bb87-660ff73006a1</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>5c135035-7fd2-4ffb-a40b-55d7c6f4a498</guid>
                <versionId>7d37599a-3c4b-4be8-8b14-d3a42dd91570</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d</guid>
            <versionId>5a7ec6b3-ab73-4f92-9841-ea4269131755</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="1458" y="203">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>028f2c63-7a09-42c9-942d-caef7c65c110</guid>
                <versionId>76197891-eb54-44a8-a52e-0ef62626aa41</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.eb7090af-c3bf-4e33-aad7-54510e6ec9b9</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0fac2513-fd5b-4fc5-a22b-81142edd6546</guid>
                    <versionId>35c7f1d2-3dc0-45a7-89b9-fc70fe204515</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.08a09a5c-0bee-40bf-82b4-777fbe2c5d57</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Audit Create Data"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9473c360-bd00-47f7-bd91-9e008ef5e988</guid>
                    <versionId>6fe8e39a-015a-486b-9bc8-753557e8e5cf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d3d40c54-231f-49e4-8763-e1e7d39ba90f</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>*************-45d5-8d18-8259bcf69443</guid>
                    <versionId>b9a51297-2741-48f6-ab13-d4c3e50e40f2</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c32dfa16-a852-4585-896b-47642ffee8bd</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Test Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2579a8e0-0063-4d22-96c4-b8858061415c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:266f6f4955d8489f:7b0b9b81:18b5b81b903:5e4b</guid>
            <versionId>89e597f7-1efb-4dc9-8cd0-3f80e41d48e4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.ebdedfcf-91d9-463d-9350-c20785f9965e</processItemPrePostId>
                <processItemId>2025.c32dfa16-a852-4585-896b-47642ffee8bd</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+33311 +" - ServiceName : AUDIT CREATE DATA : START");&#xD;
 &#xD;
</script>
                <guid>3fae409b-f06c-4996-af04-f915feb8a936</guid>
                <versionId>da273725-b3fd-4621-bd0e-1c1b156b7412</versionId>
            </processPrePosts>
            <layoutData x="207" y="187">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2579a8e0-0063-4d22-96c4-b8858061415c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script> tw.local.odcRequest.appInfo.instanceID= "33311";&#xD;
 tw.local.odcRequest.requestNo= "12345678911111_03";&#xD;
 tw.local.odcRequest.requestNature.name= "new";&#xD;
 tw.local.odcRequest.requestType.name = "collect "&#xD;
 tw.local.odcRequest.requestDate= new TWDate();&#xD;
 tw.local.odcRequest.BasicDetails.requestState="Final";&#xD;
 tw.local.odcRequest.appInfo.status="Completed";&#xD;
 tw.local.odcRequest.parentRequestNo="12345678911111";&#xD;
 tw.local.odcRequest.BasicDetails.flexCubeContractNo="11112222";&#xD;
 tw.local.odcRequest.BasicDetails.contractStage="stage3";&#xD;
 tw.local.odcRequest.BasicDetails.exportPurpose.name="exportP";&#xD;
 tw.local.odcRequest.BasicDetails.paymentTerms.name= "PT";&#xD;
 tw.local.odcRequest.BasicDetails.productCategory.name= "PCpc";&#xD;
 tw.local.odcRequest.BasicDetails.commodityDescription= "CD11";&#xD;
 tw.local.odcRequest.CustomerInfo.cif= "1234";&#xD;
 tw.local.odcRequest.CustomerInfo.customerName="smsma";&#xD;
 tw.local.odcRequest.ContractCreation.baseDate= new TWDate();&#xD;
 tw.local.odcRequest.ContractCreation.valueDate = new TWDate();&#xD;
 tw.local.odcRequest.ContractCreation.tenorDays= 3;&#xD;
 tw.local.odcRequest.ContractCreation.transitDays= 4;&#xD;
 tw.local.odcRequest.ContractCreation.maturityDate = new TWDate();&#xD;
 tw.local.odcRequest.ContractCreation.userReference= "1223231";&#xD;
 /**********************************************************************************/&#xD;
 tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "bill 03";&#xD;
 tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[1] = new tw.object.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingRef = "Bill 04";&#xD;
 tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingDate = new TWDate();&#xD;
 &#xD;
 tw.local.odcRequest.BasicDetails.Bills[2] = new tw.object.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingRef = "Bill 05";&#xD;
 tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingDate = new TWDate();&#xD;
 &#xD;
 tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
 tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
 tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "Inv 11";&#xD;
 tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
// tw.local.odcRequest.BasicDetails.Invoice[1] = new tw.object.Invoice();&#xD;
// tw.local.odcRequest.BasicDetails.Invoice[1].invoiceNo = "Inv 08";&#xD;
//  tw.local.odcRequest.BasicDetails.Invoice[1].invoiceDate = new TWDate();&#xD;
 &#xD;
 /******************************************************************************************/&#xD;
&#xD;
 tw.local.odcRequest.Parties = new tw.object.odcParties();&#xD;
 tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();&#xD;
 tw.local.odcRequest.Parties.Drawer.partyId = "00000000";&#xD;
 tw.local.odcRequest.Parties.Drawer.partyName = "Party01";&#xD;
 tw.local.odcRequest.Parties.Drawer.country = "Egp";&#xD;
 tw.local.odcRequest.Parties.Drawer.Language = "EN";&#xD;
 tw.local.odcRequest.Parties.Drawer.Reference = "1234";&#xD;
 tw.local.odcRequest.Parties.Drawer.address1 = "add1";&#xD;
 tw.local.odcRequest.Parties.Drawer.address2 = "add2";&#xD;
 tw.local.odcRequest.Parties.Drawer.address3 = "add3";&#xD;
 tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();&#xD;
 tw.local.odcRequest.Parties.Drawee.partyId = "********";&#xD;
 tw.local.odcRequest.Parties.Drawee.partyName = "Party02";&#xD;
 tw.local.odcRequest.Parties.Drawee.country = "EUR";&#xD;
 tw.local.odcRequest.Parties.Drawee.Language = "arabic";&#xD;
 tw.local.odcRequest.Parties.Drawee.Reference = "ref1";&#xD;
 tw.local.odcRequest.Parties.Drawee.address1 = "address1";&#xD;
 tw.local.odcRequest.Parties.Drawee.address2 = "address2";&#xD;
 tw.local.odcRequest.Parties.Drawee.address3 = "address3";&#xD;
 tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
 tw.local.odcRequest.Parties.collectingBank.id = "********";&#xD;
 tw.local.odcRequest.Parties.collectingBank.name = "Party03";&#xD;
 tw.local.odcRequest.Parties.collectingBank.country = "Egypt";&#xD;
 tw.local.odcRequest.Parties.collectingBank.language = "french";&#xD;
 tw.local.odcRequest.Parties.collectingBank.reference = "ref2";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address1 = "address1";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address2 = "address2";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address3 = "address3";&#xD;
 tw.local.odcRequest.Parties.collectingBank.cif = "********";&#xD;
 tw.local.odcRequest.Parties.collectingBank.media = "swift";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address = "address";&#xD;
 tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyCIF = "********";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyId = "********";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyName = "Party04";&#xD;
 tw.local.odcRequest.Parties.partyTypes.country = "italy";&#xD;
 tw.local.odcRequest.Parties.partyTypes.language = "italian";&#xD;
 tw.local.odcRequest.Parties.partyTypes.refrence = "reference0";&#xD;
 tw.local.odcRequest.Parties.partyTypes.address1 = "add11";&#xD;
 tw.local.odcRequest.Parties.partyTypes.address2 = "add22";&#xD;
 tw.local.odcRequest.Parties.partyTypes.address3 = "add33";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyType.name = "Case In Need";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyType.value = "Case In Need";&#xD;
// ------------------------------------------------------------------------------&#xD;
 tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].component = "component";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 11.0;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].waiver = false;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.value = "GL Account";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.glAccountNo = "1111";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "111";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.branchCode = "055";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balance = 200;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balanceSign = "D";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.isOverDraft = true;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.standardExRate = 990.0;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 990.0;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.amountInAccount = 99.9;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].rateType = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].description = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultPercentage = 99.9;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].changePercentage = 99.9;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultAmount = 99.1;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].basicAmountCurrency = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].flatAmount = 900.0;&#xD;
 /******************************************************************************************/&#xD;
&#xD;
 tw.local.odcRequest.FcCollections = new tw.object.FCCollections();&#xD;
 tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.currency.name = "";&#xD;
 tw.local.odcRequest.FcCollections.currency.value = "Egypt";&#xD;
 tw.local.odcRequest.FcCollections.standardExchangeRate = 119.0;&#xD;
 tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 99.9;&#xD;
 tw.local.odcRequest.FcCollections.fromDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.ToDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.accountNo.name = "";&#xD;
 tw.local.odcRequest.FcCollections.accountNo.value = "11999";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = "22222";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = "11199";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 111.0;&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 109.0;&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = "Egypt";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 190.0;&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency =99.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = "";&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = "";&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = "";&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.isReversed = false;&#xD;
 tw.local.odcRequest.FcCollections.usedAmount = 999.0;&#xD;
 tw.local.odcRequest.FcCollections.calculatedAmount = 119.0;&#xD;
 tw.local.odcRequest.FcCollections.totalAllocatedAmount = 999.9;&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts[0].name = "11119";&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts[0].value = "11119";&#xD;
 /******************************************************************************************/&#xD;
&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 100.0; &#xD;
&#xD;
tw.local.odcRequest.appInfo.instanceID= "1234";&#xD;
tw.local.odcRequest.appInfo.stepName="Create Act06";&#xD;
tw.local.odcRequest.appInfo.subStatus="Completed";&#xD;
</script>
                <isRule>false</isRule>
                <guid>e2fdfde5-2257-4d11-ac09-1307a8123876</guid>
                <versionId>4c3f3c8e-8ab5-441a-92c1-4e209cb74f91</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1988ef7c-d47c-41ca-8f40-ab05373c7958</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Insert Request_info &amp; Retrieve Request ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.3df8b291-5cb3-4ac6-8986-8c041cedaaae</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de8</guid>
            <versionId>98bf12b5-874e-440d-b1f7-adb207e26bc4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.4615599f-7bb7-4d93-afcf-a98ba521703c</processItemPrePostId>
                <processItemId>2025.1988ef7c-d47c-41ca-8f40-ab05373c7958</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID  +" - ServiceName : AUDIT CREATE DATA : START--Insert Path");&#xD;
&#xD;
&#xD;
&#xD;
</script>
                <guid>7ea2dcdf-d555-4014-8857-4136fa15523b</guid>
                <versionId>5da75e1f-c023-4b0d-9016-df720d55a712</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.c5f4499b-4ebe-49b3-82a8-a91bcfbf51be</processItemPrePostId>
                <processItemId>2025.1988ef7c-d47c-41ca-8f40-ab05373c7958</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>116861cf-3fb0-4484-a21d-64e03061c910</guid>
                <versionId>ac0697e6-feed-4dde-95bf-f6c5b1ece73a</versionId>
            </processPrePosts>
            <layoutData x="838" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.3df8b291-5cb3-4ac6-8986-8c041cedaaae</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/***********************************************************************************/&#xD;
	tw.local.sql=" SELECT ID FROM FINAL TABLE ( "&#xD;
			+ " INSERT INTO "+ tw.env.DBSchema + ".ODC_RequestInfo ( "&#xD;
               	+ " requestNo, requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,               "  &#xD;
			+ " fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  			 "  &#xD;
			+ " cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         "  &#xD;
			+ " productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, "  &#xD;
			+ " amountDefAvalization, collectableAmount, outstandingAmount, maturityDate, maturityDays, financeApprovalNo, hubCode,  "  &#xD;
			+ " hubName, collectionCurrency, standardExRate, negotiatedExRate,          					  			 "  &#xD;
			+ "   CBEClassification, shipmentMethod, shipmentDate, 						 					 "  &#xD;
			+ " importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, 			 "  &#xD;
			+ " bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  	,ISLIQUIDATED,	 " &#xD;
			+"  BPMInstanceNumber, currentStepName, substatus,											 "&#xD;
			+"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN "&#xD;
			+ " ) VALUES" &#xD;
			+ " (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?"&#xD;
			+",?,?,?,?,?,?,?)"&#xD;
			+ " )";&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter("0")); &#xD;
 &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); &#xD;
  &#xD;
///&#xD;
&#xD;
//CHARGESACCOUNT&#xD;
//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); &#xD;
 tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); &#xD;
&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>433d4df9-751b-48b8-bc88-0b5d5bfa7029</guid>
                <versionId>24eaab97-560d-4830-847e-ef712ba90464</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Insert ODC Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.de154948-673f-492c-9733-c02ca530b79e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:68332a9f3202b062:612a65b7:18aa9f8699f:5c16</guid>
            <versionId>a2413efd-ee77-4af7-bef8-4d92d7181f41</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.6407e712-0264-4a97-909f-169a71a1367a</processItemPrePostId>
                <processItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>ddc4af8f-b653-484c-b1b7-ac68daa76c0e</guid>
                <versionId>bfdebec1-578f-4115-8605-1edd09710ca6</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2d788eba-1880-4a50-9ba6-1ce01572cc40</processItemPrePostId>
                <processItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>c206effa-f71c-4b34-a520-3fa3aa2b5609</guid>
                <versionId>fdc3a4cb-c317-4981-845d-fd0db593a4cb</versionId>
            </processPrePosts>
            <layoutData x="1313" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.de154948-673f-492c-9733-c02ca530b79e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/***********************************************************************************/&#xD;
&#xD;
/**-----------------------------------------------------------------------**/&#xD;
/**-------------------------- Log All ODC Data ---------------------------**/&#xD;
/**-----------------------------------------------------------------------**/&#xD;
&#xD;
var i = 0;&#xD;
var sqlStatementLen= 0;&#xD;
&#xD;
/**-------------------------- Log Invoice Data --------------------------**/&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.BasicDetails.Invoice.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_Invoice ( " &#xD;
								  + " requesrID, invoiceNo , invoiceDate" &#xD;
								  + " ) VALUES" + " (?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate));&#xD;
	&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log Bills Data --------------------------**/&#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.BasicDetails.Bills.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_Bills ( " &#xD;
															  + " requesrID, billOfLadingRef, billOfLadingDate" &#xD;
															  + " ) VALUES" + " (?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate));&#xD;
&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log Parties Data --------------------------**/&#xD;
&#xD;
/**-------------------------- Set SQL query to add party record --------------------------**/&#xD;
function AuditParty(requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address){&#xD;
&#xD;
	if(partyId != null || partyId != ""){&#xD;
		&#xD;
		sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	&#xD;
		tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	&#xD;
		tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_PartiesInfo ( " &#xD;
															  + " requesrID, partyId, partyType, cif, partyName,Country,Language," &#xD;
															  +"  Reference, Address1, Address2, Address3, Media, Address" &#xD;
															  + " ) VALUES" + " (?,?,?,?,?,?,?,?,?,?,?,?,?) ";&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(requesrID));&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyId));&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyType));&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(cif));		&#xD;
&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyName));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(country));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(language));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(reference));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address1));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address2));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address3));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(media));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address));	&#xD;
&#xD;
		return true;&#xD;
	}&#xD;
	else{&#xD;
		return false;&#xD;
	}&#xD;
}&#xD;
//Params: requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address&#xD;
//Audit Drawer &#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawer.partyId , "Drawer", "" , tw.local.odcRequest.Parties.Drawer.partyName, &#xD;
 tw.local.odcRequest.Parties.Drawer.country, tw.local.odcRequest.Parties.Drawer.Language, tw.local.odcRequest.Parties.Drawer.Reference,&#xD;
 tw.local.odcRequest.Parties.Drawer.address1, tw.local.odcRequest.Parties.Drawer.address2, tw.local.odcRequest.Parties.Drawer.address3,"","");&#xD;
&#xD;
//Audit Drawee&#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawee.partyId , "Drawee", "", tw.local.odcRequest.Parties.Drawee.partyName,&#xD;
tw.local.odcRequest.Parties.Drawee.country, tw.local.odcRequest.Parties.Drawee.Language, tw.local.odcRequest.Parties.Drawee.Reference,&#xD;
tw.local.odcRequest.Parties.Drawee.address1, tw.local.odcRequest.Parties.Drawee.address2, tw.local.odcRequest.Parties.Drawee.address3,"","" );&#xD;
&#xD;
//Audit collecting Bank&#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.collectingBank.id , "collecting Bank",tw.local.odcRequest.Parties.collectingBank.cif,&#xD;
tw.local.odcRequest.Parties.collectingBank.name, tw.local.odcRequest.Parties.collectingBank.country, tw.local.odcRequest.Parties.collectingBank.language,&#xD;
tw.local.odcRequest.Parties.collectingBank.reference, tw.local.odcRequest.Parties.collectingBank.address1, tw.local.odcRequest.Parties.collectingBank.address2,&#xD;
tw.local.odcRequest.Parties.collectingBank.address3, tw.local.odcRequest.Parties.collectingBank.media, tw.local.odcRequest.Parties.collectingBank.address);&#xD;
&#xD;
//Audit Accountee/ Case In Need&#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.partyTypes.partyId, tw.local.odcRequest.Parties.partyTypes.partyType.value, tw.local.odcRequest.Parties.partyTypes.partyCIF,&#xD;
tw.local.odcRequest.Parties.partyTypes.partyName, tw.local.odcRequest.Parties.partyTypes.country, tw.local.odcRequest.Parties.partyTypes.language, tw.local.odcRequest.Parties.partyTypes.refrence,&#xD;
tw.local.odcRequest.Parties.partyTypes.address1, tw.local.odcRequest.Parties.partyTypes.address2, tw.local.odcRequest.Parties.partyTypes.address3, "", "");&#xD;
&#xD;
 /**-------------------------- Log Charges And Commissions Data --------------------------**/&#xD;
&#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.ChargesAndCommissions.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_CHARGESCOMMISSIONS ( " 															&#xD;
															  + " requesrID, defaultCurrency, defaultAmount, chargeAmount, waiver, accountClass,"&#xD;
															  + " accountNo, branchCode, currency, balance, balanceSign, standardExRate,"   &#xD;
															  + "negotiatedExRate, debitedAmount, COMPONENT"	&#xD;
															  + " ) VALUES" + " (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultAmount));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount));&#xD;
	&#xD;
	if(tw.local.odcRequest.ChargesAndCommissions[i].waiver)&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter("1"));&#xD;
	else&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter("0"));&#xD;
	&#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount)&#xD;
	    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount ={};&#xD;
	    &#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass)&#xD;
	    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass ={};&#xD;
	    &#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value));&#xD;
	&#xD;
    &#xD;
	var accountClass= tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value;&#xD;
	&#xD;
	if(accountClass == "GL Account"){&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo));&#xD;
	}&#xD;
	//Customer Account&#xD;
	else{&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo));&#xD;
	}&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode));&#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency)&#xD;
	      tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency = {};&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign));&#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount)&#xD;
	      tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount = {};&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].component));&#xD;
	&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log FlexCube Transactions Data --------------------------**/ &#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.FcCollections.selectedTransactions.listLength) {&#xD;
	&#xD;
&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
 	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_FCTransaction ( " &#xD;
															  + " requesrID,accountNo, postingDate, valueDate, transactionAmount, AMOUNTFORCURRENTREQUEST, TRANSACTIONREFNO "	&#xD;
															  + " ) VALUES" + " (?,?,?,?,?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest));&#xD;
      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber));&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log Multi Tenor dates Data --------------------------**/&#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
 	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_MultiTenorDates ( " &#xD;
															  + " requesrID, installmentDate, installmentAmount "	&#xD;
															  + " ) VALUES" + " (?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date));	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount));&#xD;
&#xD;
    i++;&#xD;
}&#xD;
     </script>
                <isRule>false</isRule>
                <guid>44fbf293-0d39-40d0-9c2a-3102e653328a</guid>
                <versionId>203c7f75-a96d-4dd0-8daf-d1d398d30540</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Init</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8b422e90-9e71-427b-ad5c-4842e7323bf4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:d96</guid>
            <versionId>c15fc4a4-ed5c-46e0-9f83-4a0a80035bbf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="178" y="103">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8b422e90-9e71-427b-ad5c-4842e7323bf4</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
var requestType= tw.epv.RequestType.Create+"";&#xD;
if (tw.local.odcRequest.requestType.value == requestType){&#xD;
	tw.local.odcRequest.parentRequestNo= tw.local.odcRequest.requestNo;&#xD;
	tw.local.odcRequest.BasicDetails.parentRequestNo= tw.local.odcRequest.requestNo;&#xD;
	&#xD;
	}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>23e2dca2-ecd3-44db-83bb-5adc6a739746</guid>
                <versionId>ce194249-9898-40ed-8ea2-d37103267c36</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.*************-4299-8059-f22e8c1ef414</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>update?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.7a2e0c57-f0c3-4b28-af22-9d2ef60d758f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1d22</guid>
            <versionId>c1fc5a93-4949-4795-aabf-ffd110046ffa</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.1d0b86fa-92a4-4f3e-9e0f-40df36bb282d</processItemPrePostId>
                <processItemId>2025.*************-4299-8059-f22e8c1ef414</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>e5a15f2b-4fdd-44d4-ade8-03e0ad504450</guid>
                <versionId>ea8f14a3-2dd6-4fdd-b567-9ebac5d1ceed</versionId>
            </processPrePosts>
            <layoutData x="682" y="122">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.7a2e0c57-f0c3-4b28-af22-9d2ef60d758f</switchId>
                <guid>a3538f35-f999-4682-a12e-4de5da393f2f</guid>
                <versionId>ab1d6396-6760-46ff-881e-ae1e809a37bf</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.c734b7e8-d4af-4c88-99c4-7ca3ee35a271</switchConditionId>
                    <switchId>3013.7a2e0c57-f0c3-4b28-af22-9d2ef60d758f</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:33ea</endStateId>
                    <condition>tw.local.requestID	  &gt;	  0</condition>
                    <guid>8a2bf000-7fca-46c8-8ca2-4198f34240af</guid>
                    <versionId>6b4c35d7-3df5-4866-8f27-5b28fe0d313e</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c5082aee-343a-452e-8b33-35b492b4251b</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>SQL Update Request Info</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:-6cb2</guid>
            <versionId>da3aca42-bf4a-4298-a5d2-03dadec3c44d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="963" y="173">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomLeft</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d</errorHandlerItem>
                <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>74aacb6c-d487-479a-975e-15d4a8a066bf</guid>
                <versionId>1872907d-436c-440d-87e8-5e375494c306</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7cb3a2b5-db36-46f6-971b-24a353b686b4</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5e8442c1-d38c-49a8-b459-2e2acb44e06f</guid>
                    <versionId>66e74df1-0905-4680-b9eb-da9f19750dc8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7ad108b9-8bc1-413a-bd57-0b5b18dec262</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>65b9cbe0-add4-4d4d-935d-4c385744c991</guid>
                    <versionId>af099eac-c00b-474b-90c6-acd9f718dcdd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1b2d1a6f-afda-4c7e-8fd6-a1debc676f7d</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>2b336a9b-f29c-40c4-b718-35fdbd8c3f65</guid>
                    <versionId>c0d10068-116c-47a5-83cd-6d6521ae70e8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.404616b1-7430-40d9-b518-8ac3def53c7a</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>55509f8d-6d9d-47a3-ac75-266609dc6d22</guid>
                    <versionId>f203abe8-d349-401c-8c07-583ccad688e3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c5dd03f6-6e48-4402-8ec7-00ff25634c07</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>c934e0aa-f434-455d-93e2-ed56888ab295</guid>
                    <versionId>f3dbff2e-fa8c-4241-a2e4-7b400f077349</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Get ID of Request SQL Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.097f5a82-06c9-48af-8b7e-4e3b258e7310</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1cfa</guid>
            <versionId>db1fd768-0c77-4662-b999-dfd7daac2678</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.239a0eea-c553-47cc-bcd0-2dccc9dbee23</processItemPrePostId>
                <processItemId>2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258</processItemId>
                <location>2</location>
                <script>&#xD;
if (!!tw.local.results &amp;&amp; tw.local.results.listLength &gt; 0 )&#xD;
	tw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;&#xD;
	&#xD;
	 </script>
                <guid>7f129082-979c-4efd-8c4d-3d105388fa47</guid>
                <versionId>0808f4b1-381e-4cd6-8f1c-08cfe2043058</versionId>
            </processPrePosts>
            <layoutData x="479" y="103">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.097f5a82-06c9-48af-8b7e-4e3b258e7310</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>699d2fd6-9d14-4bda-aed7-e38f077952dc</guid>
                <versionId>577ddef4-b96f-4495-b756-0c1d7052d81d</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dc115463-758a-496e-97d4-0946e73e3e97</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.097f5a82-06c9-48af-8b7e-4e3b258e7310</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>80e784f6-7c98-477e-bddc-d20ba67ffb94</guid>
                    <versionId>2a8f2417-3d8e-4d32-8808-20f643a36674</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.aed10e43-3a87-4083-bb30-8514f34638cb</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.097f5a82-06c9-48af-8b7e-4e3b258e7310</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>286335f4-2ae5-434a-8dde-47b8e0f87818</guid>
                    <versionId>6c04f7ac-421d-47e5-b0ce-04ffc026883c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1569b70a-4aa7-4d43-b8a0-76170c2fe06c</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.097f5a82-06c9-48af-8b7e-4e3b258e7310</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a6274008-05e9-4179-9a31-77c2f7282611</guid>
                    <versionId>6e42360d-419b-4dd3-acf3-f191ce8d811c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.77ff8740-be75-48a6-ac86-8a69ad11094e</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.097f5a82-06c9-48af-8b7e-4e3b258e7310</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3652503a-bcd3-4832-a799-004921fd7b61</guid>
                    <versionId>ab955b94-df41-4849-8a9f-d1401ceaee8a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f0e9621c-03be-4109-a5ca-4491812e5681</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.097f5a82-06c9-48af-8b7e-4e3b258e7310</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>6f654c19-ea28-4834-8498-946021db58e1</guid>
                    <versionId>d28fdd3f-a2b0-4744-928a-2aa4840027c9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Search for requestNo</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.1053282e-f91c-4068-904e-507c5b565eca</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1ce9</guid>
            <versionId>e7642cec-291b-49b4-9cf5-8b8dc1991a3b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.c8f96937-8411-4322-8eb0-aea0b3c74f5d</processItemPrePostId>
                <processItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID  +" - ServiceName : AUDIT CREATE DATA : START");&#xD;
 &#xD;
&#xD;
&#xD;
&#xD;
</script>
                <guid>6c4937c5-a5cf-414f-ad15-a2564f6cac56</guid>
                <versionId>ff955c53-7652-4cec-aa6b-8fcfff9b6714</versionId>
            </processPrePosts>
            <layoutData x="354" y="103">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.1053282e-f91c-4068-904e-507c5b565eca</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/***********************************************************************************/&#xD;
	tw.local.sql="SELECT ID FROM "+ tw.env.DBSchema + ".ODC_REQUESTINFO WHERE REQUESTNO = ? "&#xD;
		&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>e7699340-932a-4778-b668-abcc08ad906f</guid>
                <versionId>2bd2dcba-3a42-44c0-bc30-2e73bade0a9e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3b354799-bd42-4854-868d-e0629c388301</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Delete ODC Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4836d075-adcf-469e-84da-0dae4932d63e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:-6531</guid>
            <versionId>eac4edc5-8337-4a40-bbe8-3baae3218d0b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2049c474-fa37-4b83-8dc0-96437b8c942d</processItemPrePostId>
                <processItemId>2025.3b354799-bd42-4854-868d-e0629c388301</processItemId>
                <location>1</location>
                <script>&#xD;
 &#xD;
</script>
                <guid>29ed20dd-45ea-4896-a674-6b677d254e79</guid>
                <versionId>51f46c03-d89a-4188-b082-de3ba8f34acb</versionId>
            </processPrePosts>
            <layoutData x="1080" y="173">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4836d075-adcf-469e-84da-0dae4932d63e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/********************************************************************************/&#xD;
&#xD;
/**-------------------------------------------------------------------------**/&#xD;
/**-------------------------- DELETE All ODC Data ---------------------------**/&#xD;
/**-------------------------------------------------------------------------**/&#xD;
&#xD;
var sqlStatementLen= 0;&#xD;
/**************************************************************************************************************************************************************/&#xD;
/**------------------------------This function is used to delete multiple records from table using the requestId---------------------------------------------**/&#xD;
/**----------------------------------------------- tableName= the name of the table in the DB ---------------------------------------------------------------**/&#xD;
/**************************************************************************************************************************************************************/&#xD;
function DeleteRecordsByID(tableName){&#xD;
	 &#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " DELETE FROM "+ tw.env.DBSchema + "."+ tableName + " WHERE REQUESRID= ? ";&#xD;
			&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
  	 &#xD;
}&#xD;
/**************************************************************************************************************************************************************/&#xD;
&#xD;
/**-------------------------- Delete Data from Tables by request id --------------------------**/&#xD;
&#xD;
	DeleteRecordsByID (      "ODC_Invoice"      );&#xD;
	DeleteRecordsByID (      "ODC_Bills"        );&#xD;
	DeleteRecordsByID (   "ODC_PartiesInfo"     );	&#xD;
	DeleteRecordsByID ( "ODC_CHARGESCOMMISSIONS");&#xD;
	DeleteRecordsByID (   "ODC_FCTransaction"   );&#xD;
	DeleteRecordsByID ( "ODC_MultiTenorDates"   );	&#xD;
&#xD;
 </script>
                <isRule>false</isRule>
                <guid>fab56f63-bcc6-4b52-ad09-38401ac4a476</guid>
                <versionId>10b68829-e85a-4d25-9665-68203b886d0d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.29ef6559-8a76-4830-84f5-e12eee5f40a1</processItemId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <name>Get Request ID</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6428baf7-9b9c-42bd-bef6-ef32b663ae36</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:2577</guid>
            <versionId>f2a648b9-54cb-4059-962d-8251be410e26</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1073" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6428baf7-9b9c-42bd-bef6-ef32b663ae36</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.requestID= tw.local.results[0].rows[0].data[0];&#xD;
</script>
                <isRule>false</isRule>
                <guid>82b8715a-d611-425f-8ad5-8b885caaaa5a</guid>
                <versionId>c05c7e1f-b1fd-401f-a468-a343dc65da12</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.cde99b1b-0da2-4f57-88a6-01e62e5061ec</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <guid>8aeebfb7-dbe1-428c-a89d-1fc09d435ac3</guid>
            <versionId>27c78313-54b6-4131-bb0a-dd5cc3cf9327</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b</startingProcessItemId>
        <errorHandlerItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</errorHandlerItemId>
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="99" y="126">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <globalExceptionHandler>
            <layoutData x="1384" y="226">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </globalExceptionHandler>
        <globalExceptionLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </globalExceptionLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Audit Create Data" id="1.7ee96dd0-834b-44cb-af41-b21585627e49" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="e9b2480e-1992-4de1-8261-e524f562a11d" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.afad40c5-a38b-475c-8154-b4dabd94b6fe">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "create";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "1111222224444";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.254cf8eb-2743-4c53-8c52-e51c8c22884e" />
                        
                        
                        <ns16:dataOutput name="reqID" itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" id="2055.59dc474f-3e47-40ee-8737-ad21d25eb436" />
                        
                        
                        <ns16:dataOutput name="sqlOut" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0d27ff21-d378-41ac-801e-065cf08cc7a7" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.afad40c5-a38b-475c-8154-b4dabd94b6fe</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.254cf8eb-2743-4c53-8c52-e51c8c22884e</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.59dc474f-3e47-40ee-8737-ad21d25eb436</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.0d27ff21-d378-41ac-801e-065cf08cc7a7</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c0d17625-3a14-4a88-8ea7-d9e8d453b9c1">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5e7ccb09-8e69-4a08-833a-ede6a123e618" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>081c9314-33e1-4935-8aa2-fd628f60e06c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>646302c3-7d88-4f2d-8840-dec30757da05</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1988ef7c-d47c-41ca-8f40-ab05373c7958</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6b290809-0a1a-487e-8fcf-899da748dc05</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6d3651c3-0746-4c2c-807b-a4f4d7ac6d81</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4f8c5959-0089-4977-8ac8-802b96f20d99</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fcc15744-4366-40c0-863f-44d3fbef78dd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c32dfa16-a852-4585-896b-47642ffee8bd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c5082aee-343a-452e-8b33-35b492b4251b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3b354799-bd42-4854-868d-e0629c388301</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>548669f1-7641-4c40-8867-17f51c191b64</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>29ef6559-8a76-4830-84f5-e12eee5f40a1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9f4eb6e8-ca0b-4b3f-8785-678607e11258</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>*************-4299-8059-f22e8c1ef414</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>17ed215d-500f-49f1-8c31-7bd7cfcd3e67</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2f5bf898-59ef-4548-80f2-bfecfcd3fc4b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>15dbe67a-a7c9-4140-8add-4e119dc15301</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4f33d610-6ca6-4031-8084-0666bcc26181</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a48629c7-9082-4589-8d93-8b735c1dcc82</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bf43fdb4-37f4-4208-80a8-16c178b90bd9</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="081c9314-33e1-4935-8aa2-fd628f60e06c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="99" y="126" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1004085d-935b-4f0b-8b7d-00f0e577a11a</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="646302c3-7d88-4f2d-8840-dec30757da05">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1555" y="79" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de5</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID +" - ServiceName : AUDIT CREATE DATA : END");&#xD;
&#xD;
tw.local.reqID= tw.local.requestID;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4a775d57-f5a3-421f-89c6-8f0cb17b3efa</ns16:incoming>
                        
                        
                        <ns16:incoming>cdaf90c4-7974-40d9-8c80-2cd09e52dc77</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert Request_info &amp; Retrieve Request ID" id="1988ef7c-d47c-41ca-8f40-ab05373c7958">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="838" y="56" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID  +" - ServiceName : AUDIT CREATE DATA : START--Insert Path");&#xD;
&#xD;
&#xD;
&#xD;
</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c27dcbbc-8a95-4f89-8f2f-45efe37c2fad</ns16:incoming>
                        
                        
                        <ns16:outgoing>b3af8797-0b4e-411f-804d-fc11e6ac8cbb</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/***********************************************************************************/&#xD;
	tw.local.sql=" SELECT ID FROM FINAL TABLE ( "&#xD;
			+ " INSERT INTO "+ tw.env.DBSchema + ".ODC_RequestInfo ( "&#xD;
               	+ " requestNo, requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,               "  &#xD;
			+ " fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  			 "  &#xD;
			+ " cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         "  &#xD;
			+ " productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, "  &#xD;
			+ " amountDefAvalization, collectableAmount, outstandingAmount, maturityDate, maturityDays, financeApprovalNo, hubCode,  "  &#xD;
			+ " hubName, collectionCurrency, standardExRate, negotiatedExRate,          					  			 "  &#xD;
			+ "   CBEClassification, shipmentMethod, shipmentDate, 						 					 "  &#xD;
			+ " importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, 			 "  &#xD;
			+ " bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  	,ISLIQUIDATED,	 " &#xD;
			+"  BPMInstanceNumber, currentStepName, substatus,											 "&#xD;
			+"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN "&#xD;
			+ " ) VALUES" &#xD;
			+ " (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?"&#xD;
			+",?,?,?,?,?,?,?)"&#xD;
			+ " )";&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter("0")); &#xD;
 &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); &#xD;
  &#xD;
///&#xD;
&#xD;
//CHARGESACCOUNT&#xD;
//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); &#xD;
 tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); &#xD;
&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="1988ef7c-d47c-41ca-8f40-ab05373c7958" targetRef="6b290809-0a1a-487e-8fcf-899da748dc05" name="To SQL Execute Statement" id="b3af8797-0b4e-411f-804d-fc11e6ac8cbb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Statement" id="6b290809-0a1a-487e-8fcf-899da748dc05">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="955" y="56" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b3af8797-0b4e-411f-804d-fc11e6ac8cbb</ns16:incoming>
                        
                        
                        <ns16:outgoing>4deb808a-8acb-44c0-818d-8f2dbea1d4cb</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6b290809-0a1a-487e-8fcf-899da748dc05" targetRef="29ef6559-8a76-4830-84f5-e12eee5f40a1" name="To Get Request ID" id="4deb808a-8acb-44c0-818d-8f2dbea1d4cb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="sqlParameters" id="2056.c7095e57-5f5f-49e1-89d1-7aa304a64524">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLParameter();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLParameter();
autoObject[0].value = null;
autoObject[0].type = "";
autoObject[0].mode = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.116007eb-077a-4059-8f3b-1ff2f6837c3d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.cb80769f-3387-4e8b-8b5e-82a6dfd69a8e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="requestID" id="2056.63cfedee-d6e5-4a3c-8e47-d9d6176aaf3f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">0</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:callActivity calledElement="1.89ee1e72-5c6b-44ed-9e55-158a6cb613af" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Insert ODC Request Execute SQL" id="6d3651c3-0746-4c2c-807b-a4f4d7ac6d81">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1427" y="56" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>aeebf34e-e506-4186-8d36-a9ad0378f4f6</ns16:incoming>
                        
                        
                        <ns16:outgoing>4a775d57-f5a3-421f-89c6-8f0cb17b3efa</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.628ceac6-aa42-426b-97c7-540674f12f38</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.ce409362-d245-4476-8bac-bdafde3da38f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();
autoObject[0].sql = "";
autoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0].value = null;
autoObject[0].parameters[0].type = "";
autoObject[0].parameters[0].mode = "";
autoObject[0].maxRows = 0;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:sequenceFlow sourceRef="6d3651c3-0746-4c2c-807b-a4f4d7ac6d81" targetRef="646302c3-7d88-4f2d-8840-dec30757da05" name="To End" id="4a775d57-f5a3-421f-89c6-8f0cb17b3efa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert ODC Data" id="4f8c5959-0089-4977-8ac8-802b96f20d99">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1313" y="56" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f0ce710d-dacd-48c1-87a4-b97b7d2ef860</ns16:incoming>
                        
                        
                        <ns16:incoming>3f77b7cb-8b99-48b5-844b-6fdfa82c5b42</ns16:incoming>
                        
                        
                        <ns16:outgoing>aeebf34e-e506-4186-8d36-a9ad0378f4f6</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/***********************************************************************************/&#xD;
&#xD;
/**-----------------------------------------------------------------------**/&#xD;
/**-------------------------- Log All ODC Data ---------------------------**/&#xD;
/**-----------------------------------------------------------------------**/&#xD;
&#xD;
var i = 0;&#xD;
var sqlStatementLen= 0;&#xD;
&#xD;
/**-------------------------- Log Invoice Data --------------------------**/&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.BasicDetails.Invoice.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_Invoice ( " &#xD;
								  + " requesrID, invoiceNo , invoiceDate" &#xD;
								  + " ) VALUES" + " (?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate));&#xD;
	&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log Bills Data --------------------------**/&#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.BasicDetails.Bills.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_Bills ( " &#xD;
															  + " requesrID, billOfLadingRef, billOfLadingDate" &#xD;
															  + " ) VALUES" + " (?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate));&#xD;
&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log Parties Data --------------------------**/&#xD;
&#xD;
/**-------------------------- Set SQL query to add party record --------------------------**/&#xD;
function AuditParty(requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address){&#xD;
&#xD;
	if(partyId != null || partyId != ""){&#xD;
		&#xD;
		sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	&#xD;
		tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	&#xD;
		tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_PartiesInfo ( " &#xD;
															  + " requesrID, partyId, partyType, cif, partyName,Country,Language," &#xD;
															  +"  Reference, Address1, Address2, Address3, Media, Address" &#xD;
															  + " ) VALUES" + " (?,?,?,?,?,?,?,?,?,?,?,?,?) ";&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(requesrID));&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyId));&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyType));&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(cif));		&#xD;
&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyName));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(country));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(language));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(reference));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address1));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address2));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address3));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(media));	&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address));	&#xD;
&#xD;
		return true;&#xD;
	}&#xD;
	else{&#xD;
		return false;&#xD;
	}&#xD;
}&#xD;
//Params: requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address&#xD;
//Audit Drawer &#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawer.partyId , "Drawer", "" , tw.local.odcRequest.Parties.Drawer.partyName, &#xD;
 tw.local.odcRequest.Parties.Drawer.country, tw.local.odcRequest.Parties.Drawer.Language, tw.local.odcRequest.Parties.Drawer.Reference,&#xD;
 tw.local.odcRequest.Parties.Drawer.address1, tw.local.odcRequest.Parties.Drawer.address2, tw.local.odcRequest.Parties.Drawer.address3,"","");&#xD;
&#xD;
//Audit Drawee&#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawee.partyId , "Drawee", "", tw.local.odcRequest.Parties.Drawee.partyName,&#xD;
tw.local.odcRequest.Parties.Drawee.country, tw.local.odcRequest.Parties.Drawee.Language, tw.local.odcRequest.Parties.Drawee.Reference,&#xD;
tw.local.odcRequest.Parties.Drawee.address1, tw.local.odcRequest.Parties.Drawee.address2, tw.local.odcRequest.Parties.Drawee.address3,"","" );&#xD;
&#xD;
//Audit collecting Bank&#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.collectingBank.id , "collecting Bank",tw.local.odcRequest.Parties.collectingBank.cif,&#xD;
tw.local.odcRequest.Parties.collectingBank.name, tw.local.odcRequest.Parties.collectingBank.country, tw.local.odcRequest.Parties.collectingBank.language,&#xD;
tw.local.odcRequest.Parties.collectingBank.reference, tw.local.odcRequest.Parties.collectingBank.address1, tw.local.odcRequest.Parties.collectingBank.address2,&#xD;
tw.local.odcRequest.Parties.collectingBank.address3, tw.local.odcRequest.Parties.collectingBank.media, tw.local.odcRequest.Parties.collectingBank.address);&#xD;
&#xD;
//Audit Accountee/ Case In Need&#xD;
AuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.partyTypes.partyId, tw.local.odcRequest.Parties.partyTypes.partyType.value, tw.local.odcRequest.Parties.partyTypes.partyCIF,&#xD;
tw.local.odcRequest.Parties.partyTypes.partyName, tw.local.odcRequest.Parties.partyTypes.country, tw.local.odcRequest.Parties.partyTypes.language, tw.local.odcRequest.Parties.partyTypes.refrence,&#xD;
tw.local.odcRequest.Parties.partyTypes.address1, tw.local.odcRequest.Parties.partyTypes.address2, tw.local.odcRequest.Parties.partyTypes.address3, "", "");&#xD;
&#xD;
 /**-------------------------- Log Charges And Commissions Data --------------------------**/&#xD;
&#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.ChargesAndCommissions.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_CHARGESCOMMISSIONS ( " 															&#xD;
															  + " requesrID, defaultCurrency, defaultAmount, chargeAmount, waiver, accountClass,"&#xD;
															  + " accountNo, branchCode, currency, balance, balanceSign, standardExRate,"   &#xD;
															  + "negotiatedExRate, debitedAmount, COMPONENT"	&#xD;
															  + " ) VALUES" + " (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultAmount));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount));&#xD;
	&#xD;
	if(tw.local.odcRequest.ChargesAndCommissions[i].waiver)&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter("1"));&#xD;
	else&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter("0"));&#xD;
	&#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount)&#xD;
	    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount ={};&#xD;
	    &#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass)&#xD;
	    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass ={};&#xD;
	    &#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value));&#xD;
	&#xD;
    &#xD;
	var accountClass= tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value;&#xD;
	&#xD;
	if(accountClass == "GL Account"){&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo));&#xD;
	}&#xD;
	//Customer Account&#xD;
	else{&#xD;
		tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo));&#xD;
	}&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode));&#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency)&#xD;
	      tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency = {};&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign));&#xD;
	if(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount)&#xD;
	      tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount = {};&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].component));&#xD;
	&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log FlexCube Transactions Data --------------------------**/ &#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.FcCollections.selectedTransactions.listLength) {&#xD;
	&#xD;
&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
 	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_FCTransaction ( " &#xD;
															  + " requesrID,accountNo, postingDate, valueDate, transactionAmount, AMOUNTFORCURRENTREQUEST, TRANSACTIONREFNO "	&#xD;
															  + " ) VALUES" + " (?,?,?,?,?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest));&#xD;
      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber));&#xD;
    i++;&#xD;
}&#xD;
 /**-------------------------- Log Multi Tenor dates Data --------------------------**/&#xD;
i = 0;&#xD;
&#xD;
while (i &lt; tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.listLength) {&#xD;
	&#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
&#xD;
 	tw.local.sqlStatements[sqlStatementLen].sql = " INSERT INTO "+ tw.env.DBSchema + ".ODC_MultiTenorDates ( " &#xD;
															  + " requesrID, installmentDate, installmentAmount "	&#xD;
															  + " ) VALUES" + " (?,?,?)";&#xD;
			&#xD;
 	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date));	&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount));&#xD;
&#xD;
    i++;&#xD;
}&#xD;
     </ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4f8c5959-0089-4977-8ac8-802b96f20d99" targetRef="6d3651c3-0746-4c2c-807b-a4f4d7ac6d81" name="To Insert ODC Request Execute SQL" id="aeebf34e-e506-4186-8d36-a9ad0378f4f6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exception Handling" id="fcc15744-4366-40c0-863f-44d3fbef78dd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1458" y="203" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fd084925-7d7f-49d7-8989-1e1357be3d46</ns16:incoming>
                        
                        
                        <ns16:incoming>7919967e-0eb7-4dc7-804f-4b4f47f29532</ns16:incoming>
                        
                        
                        <ns16:incoming>87a6fb28-a343-4a33-812e-e6eb0a6463f0</ns16:incoming>
                        
                        
                        <ns16:incoming>6371f870-49c9-4241-87f2-2d5a5de4eb27</ns16:incoming>
                        
                        
                        <ns16:incoming>ef61f92e-9713-4cb1-8aef-8065c9fa7428</ns16:incoming>
                        
                        
                        <ns16:outgoing>cdaf90c4-7974-40d9-8c80-2cd09e52dc77</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Audit Create Data"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="fcc15744-4366-40c0-863f-44d3fbef78dd" targetRef="646302c3-7d88-4f2d-8840-dec30757da05" name="To End" id="cdaf90c4-7974-40d9-8c80-2cd09e52dc77">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Test Data" id="c32dfa16-a852-4585-896b-47642ffee8bd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="207" y="187" width="95" height="70" color="#FFE14F" />
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+33311 +" - ServiceName : AUDIT CREATE DATA : START");&#xD;
 &#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c9f833af-b3c3-453e-8b46-df827d4264eb</ns16:outgoing>
                        
                        
                        <ns16:script> tw.local.odcRequest.appInfo.instanceID= "33311";&#xD;
 tw.local.odcRequest.requestNo= "12345678911111_03";&#xD;
 tw.local.odcRequest.requestNature.name= "new";&#xD;
 tw.local.odcRequest.requestType.name = "collect "&#xD;
 tw.local.odcRequest.requestDate= new TWDate();&#xD;
 tw.local.odcRequest.BasicDetails.requestState="Final";&#xD;
 tw.local.odcRequest.appInfo.status="Completed";&#xD;
 tw.local.odcRequest.parentRequestNo="12345678911111";&#xD;
 tw.local.odcRequest.BasicDetails.flexCubeContractNo="11112222";&#xD;
 tw.local.odcRequest.BasicDetails.contractStage="stage3";&#xD;
 tw.local.odcRequest.BasicDetails.exportPurpose.name="exportP";&#xD;
 tw.local.odcRequest.BasicDetails.paymentTerms.name= "PT";&#xD;
 tw.local.odcRequest.BasicDetails.productCategory.name= "PCpc";&#xD;
 tw.local.odcRequest.BasicDetails.commodityDescription= "CD11";&#xD;
 tw.local.odcRequest.CustomerInfo.cif= "1234";&#xD;
 tw.local.odcRequest.CustomerInfo.customerName="smsma";&#xD;
 tw.local.odcRequest.ContractCreation.baseDate= new TWDate();&#xD;
 tw.local.odcRequest.ContractCreation.valueDate = new TWDate();&#xD;
 tw.local.odcRequest.ContractCreation.tenorDays= 3;&#xD;
 tw.local.odcRequest.ContractCreation.transitDays= 4;&#xD;
 tw.local.odcRequest.ContractCreation.maturityDate = new TWDate();&#xD;
 tw.local.odcRequest.ContractCreation.userReference= "1223231";&#xD;
 /**********************************************************************************/&#xD;
 tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "bill 03";&#xD;
 tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[1] = new tw.object.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingRef = "Bill 04";&#xD;
 tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingDate = new TWDate();&#xD;
 &#xD;
 tw.local.odcRequest.BasicDetails.Bills[2] = new tw.object.Bills();&#xD;
 tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingRef = "Bill 05";&#xD;
 tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingDate = new TWDate();&#xD;
 &#xD;
 tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
 tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
 tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "Inv 11";&#xD;
 tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
// tw.local.odcRequest.BasicDetails.Invoice[1] = new tw.object.Invoice();&#xD;
// tw.local.odcRequest.BasicDetails.Invoice[1].invoiceNo = "Inv 08";&#xD;
//  tw.local.odcRequest.BasicDetails.Invoice[1].invoiceDate = new TWDate();&#xD;
 &#xD;
 /******************************************************************************************/&#xD;
&#xD;
 tw.local.odcRequest.Parties = new tw.object.odcParties();&#xD;
 tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();&#xD;
 tw.local.odcRequest.Parties.Drawer.partyId = "00000000";&#xD;
 tw.local.odcRequest.Parties.Drawer.partyName = "Party01";&#xD;
 tw.local.odcRequest.Parties.Drawer.country = "Egp";&#xD;
 tw.local.odcRequest.Parties.Drawer.Language = "EN";&#xD;
 tw.local.odcRequest.Parties.Drawer.Reference = "1234";&#xD;
 tw.local.odcRequest.Parties.Drawer.address1 = "add1";&#xD;
 tw.local.odcRequest.Parties.Drawer.address2 = "add2";&#xD;
 tw.local.odcRequest.Parties.Drawer.address3 = "add3";&#xD;
 tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();&#xD;
 tw.local.odcRequest.Parties.Drawee.partyId = "********";&#xD;
 tw.local.odcRequest.Parties.Drawee.partyName = "Party02";&#xD;
 tw.local.odcRequest.Parties.Drawee.country = "EUR";&#xD;
 tw.local.odcRequest.Parties.Drawee.Language = "arabic";&#xD;
 tw.local.odcRequest.Parties.Drawee.Reference = "ref1";&#xD;
 tw.local.odcRequest.Parties.Drawee.address1 = "address1";&#xD;
 tw.local.odcRequest.Parties.Drawee.address2 = "address2";&#xD;
 tw.local.odcRequest.Parties.Drawee.address3 = "address3";&#xD;
 tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
 tw.local.odcRequest.Parties.collectingBank.id = "********";&#xD;
 tw.local.odcRequest.Parties.collectingBank.name = "Party03";&#xD;
 tw.local.odcRequest.Parties.collectingBank.country = "Egypt";&#xD;
 tw.local.odcRequest.Parties.collectingBank.language = "french";&#xD;
 tw.local.odcRequest.Parties.collectingBank.reference = "ref2";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address1 = "address1";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address2 = "address2";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address3 = "address3";&#xD;
 tw.local.odcRequest.Parties.collectingBank.cif = "********";&#xD;
 tw.local.odcRequest.Parties.collectingBank.media = "swift";&#xD;
 tw.local.odcRequest.Parties.collectingBank.address = "address";&#xD;
 tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyCIF = "********";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyId = "********";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyName = "Party04";&#xD;
 tw.local.odcRequest.Parties.partyTypes.country = "italy";&#xD;
 tw.local.odcRequest.Parties.partyTypes.language = "italian";&#xD;
 tw.local.odcRequest.Parties.partyTypes.refrence = "reference0";&#xD;
 tw.local.odcRequest.Parties.partyTypes.address1 = "add11";&#xD;
 tw.local.odcRequest.Parties.partyTypes.address2 = "add22";&#xD;
 tw.local.odcRequest.Parties.partyTypes.address3 = "add33";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyType.name = "Case In Need";&#xD;
 tw.local.odcRequest.Parties.partyTypes.partyType.value = "Case In Need";&#xD;
// ------------------------------------------------------------------------------&#xD;
 tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].component = "component";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 11.0;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].waiver = false;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.value = "GL Account";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.glAccountNo = "1111";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "111";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.branchCode = "055";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = "EGP";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balance = 200;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balanceSign = "D";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.isOverDraft = true;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.standardExRate = 990.0;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 990.0;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.amountInAccount = 99.9;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].rateType = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].description = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultPercentage = 99.9;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].changePercentage = 99.9;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].defaultAmount = 99.1;&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].basicAmountCurrency = "";&#xD;
 tw.local.odcRequest.ChargesAndCommissions[0].flatAmount = 900.0;&#xD;
 /******************************************************************************************/&#xD;
&#xD;
 tw.local.odcRequest.FcCollections = new tw.object.FCCollections();&#xD;
 tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.currency.name = "";&#xD;
 tw.local.odcRequest.FcCollections.currency.value = "Egypt";&#xD;
 tw.local.odcRequest.FcCollections.standardExchangeRate = 119.0;&#xD;
 tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 99.9;&#xD;
 tw.local.odcRequest.FcCollections.fromDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.ToDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.accountNo.name = "";&#xD;
 tw.local.odcRequest.FcCollections.accountNo.value = "11999";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = "22222";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = "11199";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 111.0;&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 109.0;&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = "Egypt";&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 190.0;&#xD;
 tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency =99.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = "";&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = "";&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = "";&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
 tw.local.odcRequest.FcCollections.isReversed = false;&#xD;
 tw.local.odcRequest.FcCollections.usedAmount = 999.0;&#xD;
 tw.local.odcRequest.FcCollections.calculatedAmount = 119.0;&#xD;
 tw.local.odcRequest.FcCollections.totalAllocatedAmount = 999.9;&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts[0].name = "11119";&#xD;
 tw.local.odcRequest.FcCollections.listOfAccounts[0].value = "11119";&#xD;
 /******************************************************************************************/&#xD;
&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();&#xD;
 tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 100.0; &#xD;
&#xD;
tw.local.odcRequest.appInfo.instanceID= "1234";&#xD;
tw.local.odcRequest.appInfo.stepName="Create Act06";&#xD;
tw.local.odcRequest.appInfo.subStatus="Completed";&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="date" id="2056.f401dcad-832c-452f-823c-6fca66361cee">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">new TWDate()</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:sequenceFlow sourceRef="9f4eb6e8-ca0b-4b3f-8785-678607e11258" targetRef="*************-4299-8059-f22e8c1ef414" name="To update?" id="7197dad2-4b36-4cc6-8e17-52094b099c7c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Update Request Info" id="8895b0f8-2ec0-49f3-8848-9ddc17cb68e2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="832" y="173" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID  +" - ServiceName : AUDIT CREATE DATA : START--Update Path");&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript>tw.local.sqlOut= tw.local.sql;&#xD;
tw.local.reqID= tw.local.requestID;</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>55643b29-ffc3-4ee6-89ee-92865f61e092</ns16:incoming>
                        
                        
                        <ns16:outgoing>13e4407a-2000-4320-81ff-e1245b2f17b5</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/*********************************************************************************************************************************************/&#xD;
	&#xD;
tw.local.sql= "UPDATE "+ tw.env.DBSchema +  ".ODC_RequestInfo SET ("&#xD;
		+ " requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,         		       "  &#xD;
		+ " fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  			 "  &#xD;
		+ " cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         "  &#xD;
		+ " productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, "  &#xD;
		+ " amountDefAvalization, collectableAmount,"&#xD;
		+ "outstandingAmount, maturityDate, maturityDays,"&#xD;
		+ "financeApprovalNo,"&#xD;
		+"hubCode,  "  &#xD;
		+ " hubName, collectionCurrency, standardExRate, negotiatedExRate,          					  			 "  &#xD;
		+ " CBEClassification, shipmentMethod, shipmentDate, 						 						 "  &#xD;
		+ " importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, 			 "  &#xD;
		+ " bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  	,ISLIQUIDATED ,	 " &#xD;
		+ " BPMInstanceNumber, currentStepName, substatus, 											 	"&#xD;
		+"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN "&#xD;
		+ " ) "&#xD;
		+" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?"&#xD;
		+",?,?,?,?,?,?,?,?,?"&#xD;
		+")"&#xD;
		+" WHERE requestNo = ? ";&#xD;
		&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter("0")); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); &#xD;
//&#xD;
//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); &#xD;
 tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Update Request Info" id="c5082aee-343a-452e-8b33-35b492b4251b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="963" y="173" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>13e4407a-2000-4320-81ff-e1245b2f17b5</ns16:incoming>
                        
                        
                        <ns16:outgoing>6878fe71-cf06-4406-817b-c4ec9fb17b03</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8895b0f8-2ec0-49f3-8848-9ddc17cb68e2" targetRef="c5082aee-343a-452e-8b33-35b492b4251b" name="To SQL Update Request Info" id="13e4407a-2000-4320-81ff-e1245b2f17b5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Delete ODC Data" id="3b354799-bd42-4854-868d-e0629c388301">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1080" y="173" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>&#xD;
 &#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6878fe71-cf06-4406-817b-c4ec9fb17b03</ns16:incoming>
                        
                        
                        <ns16:outgoing>f4368575-7433-403f-8e26-387c7f93456b</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();&#xD;
/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/********************************************************************************/&#xD;
&#xD;
/**-------------------------------------------------------------------------**/&#xD;
/**-------------------------- DELETE All ODC Data ---------------------------**/&#xD;
/**-------------------------------------------------------------------------**/&#xD;
&#xD;
var sqlStatementLen= 0;&#xD;
/**************************************************************************************************************************************************************/&#xD;
/**------------------------------This function is used to delete multiple records from table using the requestId---------------------------------------------**/&#xD;
/**----------------------------------------------- tableName= the name of the table in the DB ---------------------------------------------------------------**/&#xD;
/**************************************************************************************************************************************************************/&#xD;
function DeleteRecordsByID(tableName){&#xD;
	 &#xD;
	sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;&#xD;
	tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	&#xD;
	tw.local.sqlStatements[sqlStatementLen].sql = " DELETE FROM "+ tw.env.DBSchema + "."+ tableName + " WHERE REQUESRID= ? ";&#xD;
			&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));&#xD;
  	 &#xD;
}&#xD;
/**************************************************************************************************************************************************************/&#xD;
&#xD;
/**-------------------------- Delete Data from Tables by request id --------------------------**/&#xD;
&#xD;
	DeleteRecordsByID (      "ODC_Invoice"      );&#xD;
	DeleteRecordsByID (      "ODC_Bills"        );&#xD;
	DeleteRecordsByID (   "ODC_PartiesInfo"     );	&#xD;
	DeleteRecordsByID ( "ODC_CHARGESCOMMISSIONS");&#xD;
	DeleteRecordsByID (   "ODC_FCTransaction"   );&#xD;
	DeleteRecordsByID ( "ODC_MultiTenorDates"   );	&#xD;
&#xD;
 </ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="c5082aee-343a-452e-8b33-35b492b4251b" targetRef="3b354799-bd42-4854-868d-e0629c388301" name="To Delete ODC Data" id="6878fe71-cf06-4406-817b-c4ec9fb17b03">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3b354799-bd42-4854-868d-e0629c388301" targetRef="548669f1-7641-4c40-8867-17f51c191b64" name="To Execute SQL Statements" id="f4368575-7433-403f-8e26-387c7f93456b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.89ee1e72-5c6b-44ed-9e55-158a6cb613af" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Delete ODC Request Execute SQL" id="548669f1-7641-4c40-8867-17f51c191b64">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1190" y="173" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f4368575-7433-403f-8e26-387c7f93456b</ns16:incoming>
                        
                        
                        <ns16:outgoing>f0ce710d-dacd-48c1-87a4-b97b7d2ef860</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c007e1f5-b9cb-4b02-bf74-bc982112aade</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.628ceac6-aa42-426b-97c7-540674f12f38</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.24961b69-3b9b-4311-8707-6b6dfffaf207</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="548669f1-7641-4c40-8867-17f51c191b64" targetRef="4f8c5959-0089-4977-8ac8-802b96f20d99" name="To Insert ODC Data" id="f0ce710d-dacd-48c1-87a4-b97b7d2ef860">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get Request ID" id="29ef6559-8a76-4830-84f5-e12eee5f40a1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1073" y="56" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4deb808a-8acb-44c0-818d-8f2dbea1d4cb</ns16:incoming>
                        
                        
                        <ns16:outgoing>3f77b7cb-8b99-48b5-844b-6fdfa82c5b42</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.requestID= tw.local.results[0].rows[0].data[0];&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="29ef6559-8a76-4830-84f5-e12eee5f40a1" targetRef="4f8c5959-0089-4977-8ac8-802b96f20d99" name="To Insert Invoice Data" id="3f77b7cb-8b99-48b5-844b-6fdfa82c5b42">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Search for requestNo" id="ec4e3ae8-5b80-40c3-8718-4361f96b6b4b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="354" y="103" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.appInfo.instanceID  +" - ServiceName : AUDIT CREATE DATA : START");&#xD;
 &#xD;
&#xD;
&#xD;
&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c9f833af-b3c3-453e-8b46-df827d4264eb</ns16:incoming>
                        
                        
                        <ns16:incoming>f71d7316-fa39-41e3-8bd4-1dcd87381d2d</ns16:incoming>
                        
                        
                        <ns16:outgoing>b85ff298-f81c-4996-807b-ca607cbe39e9</ns16:outgoing>
                        
                        
                        <ns16:script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
/***********************************************************************************/&#xD;
	tw.local.sql="SELECT ID FROM "+ tw.env.DBSchema + ".ODC_REQUESTINFO WHERE REQUESTNO = ? "&#xD;
		&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get ID of Request SQL Execute Statement" id="9f4eb6e8-ca0b-4b3f-8785-678607e11258">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="479" y="103" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>&#xD;
if (!!tw.local.results &amp;&amp; tw.local.results.listLength &gt; 0 )&#xD;
	tw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;&#xD;
	&#xD;
	 </ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b85ff298-f81c-4996-807b-ca607cbe39e9</ns16:incoming>
                        
                        
                        <ns16:outgoing>7197dad2-4b36-4cc6-8e17-52094b099c7c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="ec4e3ae8-5b80-40c3-8718-4361f96b6b4b" targetRef="9f4eb6e8-ca0b-4b3f-8785-678607e11258" name="To Get ID of Request SQL Execute Statement" id="b85ff298-f81c-4996-807b-ca607cbe39e9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="c27dcbbc-8a95-4f89-8f2f-45efe37c2fad" name="update?" id="*************-4299-8059-f22e8c1ef414">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="682" y="122" width="32" height="32" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7197dad2-4b36-4cc6-8e17-52094b099c7c</ns16:incoming>
                        
                        
                        <ns16:outgoing>c27dcbbc-8a95-4f89-8f2f-45efe37c2fad</ns16:outgoing>
                        
                        
                        <ns16:outgoing>55643b29-ffc3-4ee6-89ee-92865f61e092</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="*************-4299-8059-f22e8c1ef414" targetRef="1988ef7c-d47c-41ca-8f40-ab05373c7958" name="No" id="c27dcbbc-8a95-4f89-8f2f-45efe37c2fad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="*************-4299-8059-f22e8c1ef414" targetRef="8895b0f8-2ec0-49f3-8848-9ddc17cb68e2" name="Yes" id="55643b29-ffc3-4ee6-89ee-92865f61e092">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestID	  &gt;	  0</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="081c9314-33e1-4935-8aa2-fd628f60e06c" targetRef="2f5bf898-59ef-4548-80f2-bfecfcd3fc4b" name="To Init" id="1004085d-935b-4f0b-8b7d-00f0e577a11a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c32dfa16-a852-4585-896b-47642ffee8bd" targetRef="ec4e3ae8-5b80-40c3-8718-4361f96b6b4b" name="To Search for requestNo" id="c9f833af-b3c3-453e-8b46-df827d4264eb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:intermediateCatchEvent name="Error Event" id="17ed215d-500f-49f1-8c31-7bd7cfcd3e67">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1384" y="226" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>fd084925-7d7f-49d7-8989-1e1357be3d46</ns16:outgoing>
                        
                        
                        <ns16:errorEventDefinition id="582948a9-cdc3-457a-84e4-8902e477907a" eventImplId="30d16f80-5b70-4f1f-8634-12dbbf5fd5c4">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:intermediateCatchEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="17ed215d-500f-49f1-8c31-7bd7cfcd3e67" targetRef="fcc15744-4366-40c0-863f-44d3fbef78dd" name="To Exception Handling" id="fd084925-7d7f-49d7-8989-1e1357be3d46">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init" id="2f5bf898-59ef-4548-80f2-bfecfcd3fc4b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="178" y="103" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1004085d-935b-4f0b-8b7d-00f0e577a11a</ns16:incoming>
                        
                        
                        <ns16:outgoing>f71d7316-fa39-41e3-8bd4-1dcd87381d2d</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
var requestType= tw.epv.RequestType.Create+"";&#xD;
if (tw.local.odcRequest.requestType.value == requestType){&#xD;
	tw.local.odcRequest.parentRequestNo= tw.local.odcRequest.requestNo;&#xD;
	tw.local.odcRequest.BasicDetails.parentRequestNo= tw.local.odcRequest.requestNo;&#xD;
	&#xD;
	}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2f5bf898-59ef-4548-80f2-bfecfcd3fc4b" targetRef="ec4e3ae8-5b80-40c3-8718-4361f96b6b4b" name="To Exclusive Gateway" id="f71d7316-fa39-41e3-8bd4-1dcd87381d2d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6d3651c3-0746-4c2c-807b-a4f4d7ac6d81" parallelMultiple="false" name="Error" id="15dbe67a-a7c9-4140-8add-4e119dc15301">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1488" y="114" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>6371f870-49c9-4241-87f2-2d5a5de4eb27</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="1c246876-97df-422c-841d-f03f1aff1e32" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="794653c5-6b68-4d7c-81e9-7f67f750847b" eventImplId="6052246d-788e-4bdf-8abf-44441276f2a0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="548669f1-7641-4c40-8867-17f51c191b64" parallelMultiple="false" name="Error1" id="4f33d610-6ca6-4031-8084-0666bcc26181">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1225" y="231" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>87a6fb28-a343-4a33-812e-e6eb0a6463f0</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="124edf7c-ca1a-4ac9-8c62-af467eeedfbe" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="70012514-bb4c-468c-89fb-4965e328da43" eventImplId="26862378-5ff7-4f34-8c29-6a0a3b1bd431">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="c5082aee-343a-452e-8b33-35b492b4251b" parallelMultiple="false" name="Error2" id="a48629c7-9082-4589-8d93-8b735c1dcc82">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="972" y="231" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>7919967e-0eb7-4dc7-804f-4b4f47f29532</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="49982f30-6256-48ef-82dc-48b29d350b1b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="513b40ec-e25c-43e4-8e50-a73ad4502c30" eventImplId="7cf5af0d-181d-4961-878f-f7a6f08ef2be">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a48629c7-9082-4589-8d93-8b735c1dcc82" targetRef="fcc15744-4366-40c0-863f-44d3fbef78dd" name="To Exception Handling" id="7919967e-0eb7-4dc7-804f-4b4f47f29532">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4f33d610-6ca6-4031-8084-0666bcc26181" targetRef="fcc15744-4366-40c0-863f-44d3fbef78dd" name="To Exception Handling" id="87a6fb28-a343-4a33-812e-e6eb0a6463f0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="15dbe67a-a7c9-4140-8add-4e119dc15301" targetRef="fcc15744-4366-40c0-863f-44d3fbef78dd" name="To Exception Handling" id="6371f870-49c9-4241-87f2-2d5a5de4eb27">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6b290809-0a1a-487e-8fcf-899da748dc05" parallelMultiple="false" name="Error3" id="bf43fdb4-37f4-4208-80a8-16c178b90bd9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="990" y="114" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ef61f92e-9713-4cb1-8aef-8065c9fa7428</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="92c496b5-54cd-4f7d-8297-2bc9367cd880" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="6aed0f61-ac99-4f37-8726-1ba2504371d7" eventImplId="f042136e-6141-4382-89eb-4cf18cec5008">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="bf43fdb4-37f4-4208-80a8-16c178b90bd9" targetRef="fcc15744-4366-40c0-863f-44d3fbef78dd" name="To Exception Handling" id="ef61f92e-9713-4cb1-8aef-8065c9fa7428">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4a775d57-f5a3-421f-89c6-8f0cb17b3efa</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81</fromProcessItemId>
            <endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</endStateId>
            <toProcessItemId>2025.646302c3-7d88-4f2d-8840-dec30757da05</toProcessItemId>
            <guid>26e7479c-4a48-4890-ad23-8beeeca631d4</guid>
            <versionId>641ea491-48df-4d22-b2fe-79d2db932b78</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81</fromProcessItemId>
            <toProcessItemId>2025.646302c3-7d88-4f2d-8840-dec30757da05</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b3af8797-0b4e-411f-804d-fc11e6ac8cbb</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1988ef7c-d47c-41ca-8f40-ab05373c7958</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6b290809-0a1a-487e-8fcf-899da748dc05</toProcessItemId>
            <guid>16ec94c4-9761-48ae-b7c5-da8a9abaa8ae</guid>
            <versionId>670bf9d6-7301-4f6b-82d0-0afa9f9b4339</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1988ef7c-d47c-41ca-8f40-ab05373c7958</fromProcessItemId>
            <toProcessItemId>2025.6b290809-0a1a-487e-8fcf-899da748dc05</toProcessItemId>
        </link>
        <link name="To Insert ODC Data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f0ce710d-dacd-48c1-87a4-b97b7d2ef860</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.548669f1-7641-4c40-8867-17f51c191b64</fromProcessItemId>
            <endStateId>guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb</endStateId>
            <toProcessItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</toProcessItemId>
            <guid>76d94d4d-c194-4af1-96d8-dccaeb2cb105</guid>
            <versionId>6d22eb7d-34d7-4e1e-b1a9-3f885aca4285</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftBottom" portType="2" />
            <fromProcessItemId>2025.548669f1-7641-4c40-8867-17f51c191b64</fromProcessItemId>
            <toProcessItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c27dcbbc-8a95-4f89-8f2f-45efe37c2fad</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.*************-4299-8059-f22e8c1ef414</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.1988ef7c-d47c-41ca-8f40-ab05373c7958</toProcessItemId>
            <guid>0fc35133-b84b-4cd1-a2e9-0505e3581cb7</guid>
            <versionId>73667a9e-12ca-42a5-a34d-0281196a7fcf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.*************-4299-8059-f22e8c1ef414</fromProcessItemId>
            <toProcessItemId>2025.1988ef7c-d47c-41ca-8f40-ab05373c7958</toProcessItemId>
        </link>
        <link name="To update?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7197dad2-4b36-4cc6-8e17-52094b099c7c</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.*************-4299-8059-f22e8c1ef414</toProcessItemId>
            <guid>ab04cb3a-ac4f-4124-98c3-cd824e7284f0</guid>
            <versionId>ac6adfbe-626b-4292-9e74-ee0a4098d15c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258</fromProcessItemId>
            <toProcessItemId>2025.*************-4299-8059-f22e8c1ef414</toProcessItemId>
        </link>
        <link name="To Insert Invoice Data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3f77b7cb-8b99-48b5-844b-6fdfa82c5b42</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.29ef6559-8a76-4830-84f5-e12eee5f40a1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</toProcessItemId>
            <guid>478f9544-0afe-4d56-b356-5cffa560d76b</guid>
            <versionId>c6d5342f-78be-4b26-b43c-a5557338423d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.29ef6559-8a76-4830-84f5-e12eee5f40a1</fromProcessItemId>
            <toProcessItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</toProcessItemId>
        </link>
        <link name="To Search for requestNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c9f833af-b3c3-453e-8b46-df827d4264eb</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c32dfa16-a852-4585-896b-47642ffee8bd</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</toProcessItemId>
            <guid>0fd27909-94e6-44b5-ba88-ddcfec313f1c</guid>
            <versionId>d4a337c8-08eb-42a6-bd90-a6e51eae15cf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c32dfa16-a852-4585-896b-47642ffee8bd</fromProcessItemId>
            <toProcessItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</toProcessItemId>
        </link>
        <link name="To Get Request ID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4deb808a-8acb-44c0-818d-8f2dbea1d4cb</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6b290809-0a1a-487e-8fcf-899da748dc05</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.29ef6559-8a76-4830-84f5-e12eee5f40a1</toProcessItemId>
            <guid>0f461d4b-8199-45c6-8b70-d0745e56642b</guid>
            <versionId>db827bd7-71ba-4592-a403-279dea40349b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6b290809-0a1a-487e-8fcf-899da748dc05</fromProcessItemId>
            <toProcessItemId>2025.29ef6559-8a76-4830-84f5-e12eee5f40a1</toProcessItemId>
        </link>
        <link name="To Insert ODC Request Execute SQL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.aeebf34e-e506-4186-8d36-a9ad0378f4f6</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81</toProcessItemId>
            <guid>32ae8671-07ff-4b33-b61a-c7b0f4bb5a7a</guid>
            <versionId>df98d776-6597-447e-866f-f4003c6c2468</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4f8c5959-0089-4977-8ac8-802b96f20d99</fromProcessItemId>
            <toProcessItemId>2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.cdaf90c4-7974-40d9-8c80-2cd09e52dc77</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.646302c3-7d88-4f2d-8840-dec30757da05</toProcessItemId>
            <guid>35ec832b-12cf-4232-8ac8-0667b160db45</guid>
            <versionId>e345ddb2-4da0-4cdd-b118-c7c1583399f5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.fcc15744-4366-40c0-863f-44d3fbef78dd</fromProcessItemId>
            <toProcessItemId>2025.646302c3-7d88-4f2d-8840-dec30757da05</toProcessItemId>
        </link>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f71d7316-fa39-41e3-8bd4-1dcd87381d2d</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</toProcessItemId>
            <guid>16950bed-7a56-4080-b562-aae160c3d16d</guid>
            <versionId>e6a42832-d155-44bd-aef4-f48b58e369e3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b</fromProcessItemId>
            <toProcessItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</toProcessItemId>
        </link>
        <link name="To Execute SQL Statements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f4368575-7433-403f-8e26-387c7f93456b</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3b354799-bd42-4854-868d-e0629c388301</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.548669f1-7641-4c40-8867-17f51c191b64</toProcessItemId>
            <guid>f64fb201-0260-49b4-a87a-def10f01bf94</guid>
            <versionId>ec831c93-9db6-4cfc-a921-701e84d4a4b2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3b354799-bd42-4854-868d-e0629c388301</fromProcessItemId>
            <toProcessItemId>2025.548669f1-7641-4c40-8867-17f51c191b64</toProcessItemId>
        </link>
        <link name="To Delete ODC Data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6878fe71-cf06-4406-817b-c4ec9fb17b03</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c5082aee-343a-452e-8b33-35b492b4251b</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.3b354799-bd42-4854-868d-e0629c388301</toProcessItemId>
            <guid>41fadb9e-fb13-401b-b541-ea6af8dab3a6</guid>
            <versionId>ed72cbba-90bd-4cf8-88ee-9077f59fa36b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c5082aee-343a-452e-8b33-35b492b4251b</fromProcessItemId>
            <toProcessItemId>2025.3b354799-bd42-4854-868d-e0629c388301</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.55643b29-ffc3-4ee6-89ee-92865f61e092</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.*************-4299-8059-f22e8c1ef414</fromProcessItemId>
            <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:33ea</endStateId>
            <toProcessItemId>2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</toProcessItemId>
            <guid>4400d639-f526-4380-94ad-ef5a45879fcb</guid>
            <versionId>f01a1154-2628-4ad2-be70-4b740113abd9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.*************-4299-8059-f22e8c1ef414</fromProcessItemId>
            <toProcessItemId>2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</toProcessItemId>
        </link>
        <link name="To Get ID of Request SQL Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b85ff298-f81c-4996-807b-ca607cbe39e9</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258</toProcessItemId>
            <guid>5777cd51-5211-4784-9b79-6a81aee85d52</guid>
            <versionId>f18818a9-646b-46f4-90a9-7cba759d6c7b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b</fromProcessItemId>
            <toProcessItemId>2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258</toProcessItemId>
        </link>
        <link name="To SQL Update Request Info">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.13e4407a-2000-4320-81ff-e1245b2f17b5</processLinkId>
            <processId>1.7ee96dd0-834b-44cb-af41-b21585627e49</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c5082aee-343a-452e-8b33-35b492b4251b</toProcessItemId>
            <guid>d6f960cc-2f2c-4e66-9251-f071a1a24b90</guid>
            <versionId>f7f9cdcc-e5e9-4c06-8fae-8f3208a713ba</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2</fromProcessItemId>
            <toProcessItemId>2025.c5082aee-343a-452e-8b33-35b492b4251b</toProcessItemId>
        </link>
    </process>
</teamworks>

