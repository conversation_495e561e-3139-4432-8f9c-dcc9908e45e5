{"id": "24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4", "versionId": "810f835e-ee93-4052-91c7-1db6459ebc54", "name": "Branch compliance Representative Checkers", "type": "participant", "typeName": "Participant", "details": {}, "_fullObjectData": {"teamworks": {"participant": {"id": "24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4", "name": "Branch compliance Representative Checkers", "lastModified": "1691143034828", "lastModifiedBy": "heba", "participantId": "24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4", "participantDefinition": {"isNull": "true"}, "simulationGroupSize": "2", "capacityType": "1", "definitionType": "3", "percentAvailable": {"isNull": "true"}, "percentEfficiency": {"isNull": "true"}, "cost": "10.00", "currencyCode": {"isNull": "true"}, "image": {"isNull": "true"}, "serviceMembersRef": {"isNull": "true"}, "managersRef": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"team\":[{\"members\":{\"Users\":[{\"name\":\"abdelrahman.saleh\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"heba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"somaia\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}],\"UserGroups\":[{\"name\":\"BPM_ODC_BR_COMP_REP_CHKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails\",\"type\":\"StandardMembers\"}]},\"documentation\":[{\"content\":[],\"textFormat\":\"text\\/plain\"}],\"name\":\"Branch compliance Representative Checkers\",\"declaredType\":\"resource\",\"id\":\"24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.w3.org\\/1999\\/XPath\",\"id\":\"24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4\"}", "externalId": {"isNull": "true"}, "description": "", "guid": "guid:d694a63221635d5b:6baf87c4:18969a9a6e2:74a9", "versionId": "810f835e-ee93-4052-91c7-1db6459ebc54", "standardMembers": {"standardMember": [{"type": "Group", "name": "BPM_ODC_BR_COMP_REP_CHKR"}, {"type": "User", "name": "abdelrahman.saleh"}, {"type": "User", "name": "heba"}, {"type": "User", "name": "so<PERSON>ia"}]}, "teamAssignments": ""}}}}