{"id": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "versionId": "0f8b5d23-2ad9-4e92-a134-ff82524258aa", "name": "Generate Remittance Letter", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "Generate Remittance Letter", "lastModified": "1700020717717", "lastModifiedBy": "<PERSON><PERSON>", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-16a0", "versionId": "0f8b5d23-2ad9-4e92-a134-ff82524258aa", "dependencySummary": "<dependencySummary id=\"bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f15\">\r\n  <artifactReference id=\"bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f14\">\r\n    <refId>1.7f097f90-cf8f-43fa-875f-f106fe9cac70</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f13\">\r\n      <name>operationId</name>\r\n      <value>{http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n</dependencySummary>", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.2521cec4-3685-4925-848f-52ab82439a5d\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":209,\"y\":77,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"98eaadf2-a3e9-4432-8cc8-70ef8c5970c6\"},{\"incoming\":[\"6f0096bd-6183-4c60-81fb-1d5ef2324a64\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1017,\"y\":77,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-169e\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"3a8ae9f4-a4b5-4024-863f-a0b0817b39c7\"},{\"targetRef\":\"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exclusive Gateway\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2521cec4-3685-4925-848f-52ab82439a5d\",\"sourceRef\":\"98eaadf2-a3e9-4432-8cc8-70ef8c5970c6\"},{\"outgoing\":[\"4c44551e-24bb-4ce9-8c6c-b0e99527615b\"],\"incoming\":[\"078c569a-3fac-4dfa-8c82-6d494c69d3de\",\"e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6\"],\"matchAllSearchCriteria\":true,\"extensionElements\":{\"postAssignmentScript\":[\"log.info(\\\" ServiceName : Get Doc Gen Temp Folder: END\\\");\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":505,\"y\":53,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"log.info(\\\"ProcessInstance : \\\"+tw.system.currentProcessInstanceID +\\\" - ServiceName : Contract Document Generation : START\\\");\\r\\nlog.info(\\\" ServiceName : Get Doc Gen Temp Folder: START\\\");\"],\"activityType\":[\"ContentTask\"]},\"operationRef\":\"FOLDER_OP_GET_FOLDER_BY_PATH\",\"implementation\":\"##WebService\",\"serverName\":\"useMappingServer\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask\",\"startQuantity\":1,\"name\":\"Get Doc Gen Temp Folder\",\"dataInputAssociation\":[{\"targetRef\":\"SERVER_NAME\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"FileNet\\\"\"]}}]},{\"targetRef\":\"PATH\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderPath\"]}}]}],\"isForCompensation\":false,\"useMappedVariable\":true,\"completionQuantity\":1,\"id\":\"b4ca5615-841a-4b96-8f71-17df1c09d08e\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.docGenTempFolder\"]}}],\"sourceRef\":[\"FOLDER\"]}],\"orderOverride\":false},{\"targetRef\":\"df1ded4c-8bfd-44d8-8f0f-b837239503ce\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Script Task1\",\"declaredType\":\"sequenceFlow\",\"id\":\"4c44551e-24bb-4ce9-8c6c-b0e99527615b\",\"sourceRef\":\"b4ca5615-841a-4b96-8f71-17df1c09d08e\"},{\"startQuantity\":1,\"outgoing\":[\"456da453-0ff0-48d2-8993-02260083c2ad\"],\"incoming\":[\"4c44551e-24bb-4ce9-8c6c-b0e99527615b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":649,\"y\":54,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Prepare request\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"df1ded4c-8bfd-44d8-8f0f-b837239503ce\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"function stringToList(string) {\\r\\n\\tvar stringList = new tw.object.listOf.String();\\r\\n\\tstringList[0] = string;\\r\\n\\treturn stringList;\\r\\n}\\r\\n\\r\\nfunction floatToStringList(float) {\\r\\n\\tvar stringList = new tw.object.listOf.String();\\r\\n\\tstringList[0] = \\\"\\\" + float;\\r\\n\\treturn stringList;\\r\\n}\\r\\n\\r\\nfunction booleanToStringList(boolean) {\\r\\n\\tvar stringList = new tw.object.listOf.String();\\r\\n\\tif (boolean)\\r\\n\\t\\tstringList[0] = \\\"\\u0646\\u0639\\u0645\\\"\\r\\n\\telse\\r\\n\\t\\tstringList[0] = \\\"\\u0644\\u0627\\\"\\r\\n\\treturn stringList;\\r\\n}\\r\\n\\r\\nfunction formatDate(date) {\\r\\n\\tdate.setHours(date.getHours() + 2);\\r\\n\\tvar d = new Date(date),\\r\\n\\t\\tday = '' + (d.getDate()),\\r\\n\\t\\tmonth = '' + (d.getMonth() + 1),\\r\\n\\t\\tyear = d.getFullYear();\\r\\n\\r\\n\\tif (month.length < 2)\\r\\n\\t\\tmonth = '0' + month;\\r\\n\\tif (day.length < 2)\\r\\n\\t\\tday = '0' + day;\\r\\n\\r\\n\\treturn [day, month, year].join('\\/');\\r\\n}\\r\\n\\r\\ntw.local.request = new tw.object.DocumentGenerationRequest();\\r\\ntw.local.request.templates = new tw.object.listOf.RequestTemplatePojo();\\r\\n\\/\\/tw.local.request.templates[0] = new tw.object.RequestTemplatePojo();\\r\\n\\r\\ntw.local.request.appName = \\\"ODC create\\/Amend\\\";\\r\\ntw.local.request.purposeCode = new tw.object.listOf.String();\\r\\ntw.local.request.purposeCode[0] = \\\"PUR1\\\";\\r\\n\\r\\ntw.local.request.programCode = \\\"PROG1\\\";\\r\\n\\/\\/\\\"PROG1\\\";\\r\\n\\r\\ntw.local.request.secure = true;\\r\\n\\r\\ntw.local.paramsValues = new tw.object.Map();\\r\\n\\r\\n\\r\\n\\/\\/---------------------------------------------------\\r\\nvar ecmInfo = new tw.object.EcmDocumentInformation();\\r\\necmInfo.docClassName = \\\"Document\\\";\\r\\necmInfo.docProperties = \\\"\\\";\\r\\necmInfo.folderId = tw.local.docGenTempFolder.path;\\r\\n\\r\\nif (tw.local.generationStatus == \\\"Generate\\\") {\\r\\n\\tvar title = \\\"\\\";\\r\\n}\\r\\nelse {\\r\\n\\tif (tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value == \\\"other\\\") {\\r\\n\\t\\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\\r\\n\\t}\\r\\n\\telse {\\r\\n\\t\\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value;\\r\\n\\t}\\r\\n}\\r\\n\\/\\/var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value + tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\\r\\ntw.local.paramsValues.put(\\\"Title001\\\", stringToList(title));\\r\\nvar date = new tw.object.Date();\\r\\ntw.local.paramsValues.put(\\\"Date001\\\", stringToList(formatDate(date)));\\r\\nvar swiftCode = \\\"NBEGEGCX\\\" + tw.local.odcRequest.FinancialDetailsFO.executionHub.value;\\r\\n\\r\\ntw.local.paramsValues.put(\\\"Swift001\\\", stringToList(swiftCode));\\r\\ntw.local.paramsValues.put(\\\"Int001\\\", stringToList(tw.local.odcRequest.ImporterDetails.bank));\\r\\ntw.local.paramsValues.put(\\\"Int002\\\", stringToList(tw.local.odcRequest.ImporterDetails.bankAddress));\\r\\ntw.local.paramsValues.put(\\\"Int003\\\", stringToList(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\\r\\ntw.local.paramsValues.put(\\\"Int004\\\", stringToList(tw.local.odcRequest.ImporterDetails.BICCode));\\r\\ntw.local.paramsValues.put(\\\"Int005\\\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerName));\\r\\ntw.local.paramsValues.put(\\\"Int006\\\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerAddress));\\r\\ntw.local.paramsValues.put(\\\"Int007\\\", stringToList(tw.local.odcRequest.ImporterDetails.importerName));\\r\\ntw.local.paramsValues.put(\\\"Int008\\\", stringToList(tw.local.odcRequest.ImporterDetails.importerAddress));\\r\\ntw.local.paramsValues.put(\\\"Int009\\\", stringToList(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\\r\\ntw.local.paramsValues.put(\\\"Int010\\\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\\r\\ntw.local.paramsValues.put(\\\"Int011\\\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\\r\\nvar claimedAmount = tw.local.odcRequest.FinancialDetailsFO.collectableAmount + tw.local.odcRequest.FinancialDetailsFO.ourCharges;\\r\\ntw.local.paramsValues.put(\\\"Int012\\\", floatToStringList(claimedAmount));\\r\\ntw.local.paramsValues.put(\\\"Int013\\\", stringToList(tw.local.odcRequest.FinancialDetailsBR.currency.name));\\r\\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name == \\\"001\\\") {\\r\\n\\ttw.local.paramsValues.put(\\\"Int014\\\", stringToList(\\\"Sight\\\"));\\r\\n}\\r\\nelse {\\r\\n\\ttw.local.paramsValues.put(\\\"Int014\\\", stringToList(formatDate(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date)));\\r\\n}\\r\\ntw.local.paramsValues.put(\\\"Int015\\\", stringToList(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\\r\\n\\r\\nvar deliveryTerms = new tw.object.listOf.String();\\r\\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms.listLength; i++) {\\r\\n\\tdeliveryTerms[i] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[i];\\r\\n}\\r\\nif (tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength != 0) {\\r\\n\\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength; i++) {\\r\\n\\t\\tdeliveryTerms[deliveryTerms.length] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra[i];\\r\\n\\t}\\r\\n}\\r\\n\\r\\ntw.local.paramsValues.put(\\\"Int016\\\", deliveryTerms);\\r\\n\\r\\nvar paymentInstructions = new tw.object.listOf.String();\\r\\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions.listLength; i++) {\\r\\n\\tpaymentInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[i];\\r\\n}\\r\\nvar length = paymentInstructions.length;\\r\\nif (tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength != 0) {\\r\\n\\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength; i++) {\\r\\n\\t\\tpaymentInstructions[paymentInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra[i];\\r\\n\\t}\\r\\n}\\r\\n\\r\\ntw.local.paramsValues.put(\\\"Int017\\\", paymentInstructions);\\r\\n\\r\\nvar instructions = new tw.object.listOf.String();\\r\\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.Instructions.listLength; i++) {\\r\\n\\tinstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.Instructions[i];\\r\\n}\\r\\nif (tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength != 0) {\\r\\n\\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength; i++) {\\r\\n\\t\\tinstructions[instructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra[i];\\r\\n\\t}\\r\\n}\\r\\ntw.local.paramsValues.put(\\\"Int018\\\", instructions);\\r\\n\\r\\nvar specialInstructions = new tw.object.listOf.String();\\r\\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions.listLength; i++) {\\r\\n\\tspecialInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[i];\\r\\n}\\r\\nif (tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength != 0) {\\r\\n\\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength; i++) {\\r\\n\\t\\tspecialInstructions[specialInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra[i];\\r\\n\\t}\\r\\n}\\r\\n\\r\\ntw.local.paramsValues.put(\\\"Int019\\\", specialInstructions);\\r\\n\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nvar documentNames = new tw.object.listOf.String();\\r\\nvar noOfCopies = new tw.object.listOf.String();\\r\\nvar noOfOriginals = new tw.object.listOf.String();\\r\\nvar docsCount =0;\\r\\nfor (var i = 0; i < tw.local.odcRequest.attachmentDetails.attachment.listLength; i++) {\\r\\n\\tif (!!tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals || !!tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies) {\\r\\n\\t\\tif (tw.local.odcRequest.attachmentDetails.attachment[i].name == \\\"\\\" || tw.local.odcRequest.attachmentDetails.attachment[i].name == null) {\\r\\n\\t\\t\\tdocumentNames[docsCount] = \\\"\\\";\\r\\n\\t\\t}\\r\\n\\t\\telse {\\r\\n\\t\\t\\tdocumentNames[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].name;\\r\\n\\t\\t}\\r\\n\\t\\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == \\\"\\\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == null) {\\r\\n\\t\\t\\tnoOfOriginals[docsCount] = \\\"\\\";\\r\\n\\t\\t}\\r\\n\\t\\telse {\\r\\n\\t\\t\\tnoOfOriginals[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals + \\\"\\\";\\r\\n\\t\\t}\\r\\n\\t\\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == \\\"\\\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == null) {\\r\\n\\t\\t\\tnoOfCopies[docsCount] = \\\"\\\";\\r\\n\\t\\t}\\r\\n\\t\\telse {\\r\\n\\t\\t\\tnoOfCopies[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies + \\\"\\\";\\r\\n\\t\\t}\\r\\n\\t\\tdocsCount++;\\r\\n\\r\\n\\t}\\r\\n\\/\\/\\telse{\\r\\n\\/\\/\\t\\tcontinue;\\r\\n\\/\\/\\t}\\r\\n}\\r\\nif(docsCount>0){\\r\\n\\ttw.local.paramsValues.put(\\\"intr001\\\", documentNames);\\r\\n\\ttw.local.paramsValues.put(\\\"intr002\\\", noOfOriginals);\\r\\n\\ttw.local.paramsValues.put(\\\"intr003\\\", noOfCopies);\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\/\\/tw.local.paramsValues.put(\\\"intr001\\\",specialInstructions);\\r\\n\\/\\/tw.local.paramsValues.put(\\\"intr002\\\",specialInstructions);\\r\\n\\/\\/tw.local.paramsValues.put(\\\"intr003\\\",specialInstructions);\\r\\n\\r\\ntw.local.request.paramsValues = tw.local.paramsValues;\\r\\n\\/\\/--------------------------Generate Docs-------------------\\r\\nvar template = new tw.object.RequestTemplatePojo();\\r\\ntemplate.secure = true;\\r\\ntemplate.ecmDocInfo = ecmInfo;\\r\\ntemplate.templateCode = \\\"ODCRemittanceLetter\\\";\\r\\ntemplate.generatedDocName = \\\"remittance Letter\\\";\\r\\ntw.local.request.templates.insertIntoList(tw.local.request.templates.listLength, template);\\r\\n\"]}},{\"targetRef\":\"7044d967-7568-4a9e-8f73-a0143e928089\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Get template names\",\"declaredType\":\"sequenceFlow\",\"id\":\"456da453-0ff0-48d2-8993-02260083c2ad\",\"sourceRef\":\"df1ded4c-8bfd-44d8-8f0f-b837239503ce\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject[0].objectTypeId = \\\"\\\";\\nautoObject[0].value = null;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9\",\"name\":\"properties\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.79ad9803-626f-4181-8c3c-770f087a21ec\"},{\"itemSubjectRef\":\"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"name\":\"templateName\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.d77cfee1-b5f8-48c8-8413-9f4deccdd93a\"},{\"itemSubjectRef\":\"itm.12.90c5b1d3-3fa1-4b3b-ab27-220b0652dc55\",\"name\":\"paramsValues\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.88fc3cd0-f6cb-465e-8f19-9802902cea33\"},{\"itemSubjectRef\":\"itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063\",\"name\":\"request\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1d5f3808-737b-48d5-8993-0aef345100bd\"},{\"outgoing\":[\"6f0096bd-6183-4c60-81fb-1d5ef2324a64\"],\"incoming\":[\"456da453-0ff0-48d2-8993-02260083c2ad\"],\"extensionElements\":{\"postAssignmentScript\":[\"log.info(\\\" ServiceName : Document Generation: END\\\");\\r\\ntw.local.docID = tw.local.response.templatesResponse[0].ecmDocName;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":859,\"y\":54,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"log.info(\\\" ServiceName : Document Generation: START\\\");\"],\"activityType\":[\"ServiceTask\"],\"externalServiceRefPO\":[\"1.7f097f90-cf8f-43fa-875f-f106fe9cac70\"],\"externalServiceRef\":[\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}_4f57dabd-7522-45c1-a6cb-ae30e4578c97\"]},\"operationRef\":\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed\",\"implementation\":\"##WebService\",\"declaredType\":\"serviceTask\",\"startQuantity\":1,\"name\":\"Generate Document\",\"dataInputAssociation\":[{\"targetRef\":\"_5f31e534-f82c-4bed-8b97-f4b50bb07fb8\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.request\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"7044d967-7568-4a9e-8f73-a0143e928089\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.response\"]}}],\"sourceRef\":[\"_66fa2129-ae2c-47d6-aa59-b4f60f4a6c96\"]}]},{\"targetRef\":\"3a8ae9f4-a4b5-4024-863f-a0b0817b39c7\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"6f0096bd-6183-4c60-81fb-1d5ef2324a64\",\"sourceRef\":\"7044d967-7568-4a9e-8f73-a0143e928089\"},{\"itemSubjectRef\":\"itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc\",\"name\":\"response\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8194bbbc-8a6e-43a4-865f-51db9a4c83ec\"},{\"itemSubjectRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"name\":\"folderID\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.47435375-900d-463a-8170-bb2c5ac59b17\"},{\"startQuantity\":1,\"outgoing\":[\"4a81ac30-2f20-4653-83e0-0b0c4aa45d0c\"],\"incoming\":[\"6945970e-2c78-4a60-8fcd-6be760d2a3f6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":712,\"y\":311,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Generate Remittance Letter\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"56d069f7-8b98-48b1-800b-dd3a134083b1\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"bc8b682b-6442-40c7-88cc-2da045c87a52\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"4a81ac30-2f20-4653-83e0-0b0c4aa45d0c\",\"sourceRef\":\"56d069f7-8b98-48b1-800b-dd3a134083b1\"},{\"outgoing\":[\"078c569a-3fac-4dfa-8c82-6d494c69d3de\",\"0b7e9890-ebed-4616-8d5c-037bbf967812\"],\"incoming\":[\"2027.2521cec4-3685-4925-848f-52ab82439a5d\"],\"default\":\"078c569a-3fac-4dfa-8c82-6d494c69d3de\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":314,\"y\":73,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"regenerate?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc\"},{\"targetRef\":\"b4ca5615-841a-4b96-8f71-17df1c09d08e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"078c569a-3fac-4dfa-8c82-6d494c69d3de\",\"sourceRef\":\"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc\"},{\"targetRef\":\"13c3f7c0-c045-4e71-88f4-4a56be3e590d\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"!!tw.local.odcRequest.templateDocID\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"0b7e9890-ebed-4616-8d5c-037bbf967812\",\"sourceRef\":\"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc\"},{\"itemSubjectRef\":\"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a\",\"name\":\"docs\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.56f0e5cd-36e3-4171-8118-3182c75106a1\"},{\"outgoing\":[\"e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6\"],\"incoming\":[\"0b7e9890-ebed-4616-8d5c-037bbf967812\"],\"matchAllSearchCriteria\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":401,\"y\":185,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"ContentTask\"]},\"operationRef\":\"DOC_OP_DELETE_DOCUMENT\",\"implementation\":\"##WebService\",\"serverName\":\"useMappingServer\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask\",\"startQuantity\":1,\"name\":\"Delete Doc\",\"dataInputAssociation\":[{\"targetRef\":\"SERVER_NAME\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"FileNet\\\"\"]}}]},{\"targetRef\":\"ALL_VERSIONS\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"true\"]}}]},{\"targetRef\":\"DOCUMENT_ID\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.templateDocID\"]}}]}],\"isForCompensation\":false,\"useMappedVariable\":true,\"completionQuantity\":1,\"id\":\"13c3f7c0-c045-4e71-88f4-4a56be3e590d\",\"orderOverride\":false},{\"targetRef\":\"b4ca5615-841a-4b96-8f71-17df1c09d08e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Doc Gen Temp Folder\",\"declaredType\":\"sequenceFlow\",\"id\":\"e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6\",\"sourceRef\":\"13c3f7c0-c045-4e71-88f4-4a56be3e590d\"},{\"parallelMultiple\":false,\"outgoing\":[\"6945970e-2c78-4a60-8fcd-6be760d2a3f6\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"98eb20b4-0d3e-401c-8b7f-d01d8b88083b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"f1a42aaa-61ef-46c9-88ea-255111151e90\",\"otherAttributes\":{\"eventImplId\":\"4e7012cf-723f-425a-8175-9f1a6178fd44\"}}],\"attachedToRef\":\"13c3f7c0-c045-4e71-88f4-4a56be3e590d\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":436,\"y\":243,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"21c155e3-195c-40b3-891c-9ec39b9f93b7\",\"outputSet\":{}},{\"targetRef\":\"56d069f7-8b98-48b1-800b-dd3a134083b1\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"6945970e-2c78-4a60-8fcd-6be760d2a3f6\",\"sourceRef\":\"21c155e3-195c-40b3-891c-9ec39b9f93b7\"},{\"incoming\":[\"4a81ac30-2f20-4653-83e0-0b0c4aa45d0c\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"e75eccda-e8f4-41d4-8c9b-d3c6e4538dc0\",\"otherAttributes\":{\"eventImplId\":\"e1f379d2-8f3b-4915-826a-0cf9bb01de4c\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":935,\"y\":408,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"bc8b682b-6442-40c7-88cc-2da045c87a52\"}],\"laneSet\":[{\"id\":\"9213a0a3-4f14-498b-870a-bdd484aa7ff9\",\"lane\":[{\"flowNodeRef\":[\"98eaadf2-a3e9-4432-8cc8-70ef8c5970c6\",\"3a8ae9f4-a4b5-4024-863f-a0b0817b39c7\",\"b4ca5615-841a-4b96-8f71-17df1c09d08e\",\"df1ded4c-8bfd-44d8-8f0f-b837239503ce\",\"7044d967-7568-4a9e-8f73-a0143e928089\",\"56d069f7-8b98-48b1-800b-dd3a134083b1\",\"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc\",\"13c3f7c0-c045-4e71-88f4-4a56be3e590d\",\"21c155e3-195c-40b3-891c-9ec39b9f93b7\",\"bc8b682b-6442-40c7-88cc-2da045c87a52\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"c4d72eee-6884-4051-8394-1ddf22790a12\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Generate Remittance Letter\",\"declaredType\":\"process\",\"id\":\"1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183\",\"name\":\"docGenTempFolder\",\"isCollection\":false,\"id\":\"2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.a37faedf-a8f5-421e-8773-5c59cb9d3888\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"id\":\"2055.ad3e5165-9152-45e2-8bef-7be47d669999\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"docID\",\"isCollection\":false,\"id\":\"2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.4996fad4-3fac-48ef-8f5b-05525969b773\",\"2055.cb493271-2446-49c9-80fd-c8bfca9646bc\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417\",\"2055.a37faedf-a8f5-421e-8773-5c59cb9d3888\",\"2055.ad3e5165-9152-45e2-8bef-7be47d669999\",\"2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"{7015CC8B-0000-CF4A-AB45-A92089CC7DB7}\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject.folderPath = \\\"\\/\\u0623\\u0631\\u0634\\u064a\\u0641 \\u0639\\u0645\\u0644\\u0627\\u0621 \\u0627\\u0644\\u0628\\u0646\\u0643 \\u0627\\u0644\\u0627\\u0647\\u0644\\u064a\\/\\u0639\\u0645\\u0644\\u0627\\u0621 \\u0627\\u0644\\u0628\\u0646\\u0643\\/03024659\\/DC Outward\\/00104230000209ODC\\/Issuance\\\";\\nautoObject.templateDocID = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.4996fad4-3fac-48ef-8f5b-05525969b773\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"generationStatus\",\"isCollection\":false,\"id\":\"2055.cb493271-2446-49c9-80fd-c8bfca9646bc\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4996fad4-3fac-48ef-8f5b-05525969b773", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"{7015CC8B-0000-CF4A-AB45-A92089CC7DB7}\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"/أرشيف عملاء البنك الاهلي/عملاء البنك/03024659/DC Outward/00104230000209ODC/Issuance\";\r\nautoObject.templateDocID = \"\";\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "e9f2d199-df16-45fc-879c-6b639e2eae0b", "versionId": "ac8236eb-70cb-4e7d-a460-90b104ed95c2"}, {"name": "generationStatus", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.cb493271-2446-49c9-80fd-c8bfca9646bc", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "35fa516f-769e-4e3e-b493-f354e5b6e991", "versionId": "8c06cb3f-ca43-4488-aee3-08e53dbac21a"}, {"name": "docGenTempFolder", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "2", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "299c1c92-057a-4469-b9be-22ccfa8bbf91", "versionId": "059b42c7-26de-4e3c-824d-339079664b74"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a37faedf-a8f5-421e-8773-5c59cb9d3888", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "13c073a7-d802-42ce-91a5-e356b7ee5e2e", "versionId": "606d86a1-f7ec-4dd5-8815-5645c0faf768"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ad3e5165-9152-45e2-8bef-7be47d669999", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6965740b-203c-4fa1-9e9d-e9ca395d15e3", "versionId": "b3b75ed0-eec7-42cc-bbfc-be28287670a5"}, {"name": "docID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "805055e6-be6a-4379-860d-d0713b65b490", "versionId": "e985f59a-a1cd-40a3-b19a-10e461af8cc8"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c3371967-77c0-4272-901f-9f67262b643e", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "3", "isArrayOf": "false", "classId": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "seq": "205", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6ed4f6a8-b00b-4fcf-8358-389c76d9fc9f", "versionId": "d02622c9-141b-4846-91c2-e2bd53bb2f9d"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.98097b94-2b12-4bea-a2ab-a62c610d2510", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "222", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "8d79654b-0c8c-4c6d-8327-9032f39d0a8d", "versionId": "6bcd9905-12ec-454d-8a24-a4e0015c92ac"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.7474d81e-18d3-4732-8a36-07e6f72c583f", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "223", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "4c5d56f2-3ae4-4963-8cab-bb415de47161", "versionId": "c8a5f67f-23de-4de8-afbf-3c04bd77772f"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0b414eaa-a71d-4cd9-965e-efcefc32c027", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "224", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "628e1f44-73e0-4e5e-9f57-97d574c0580d", "versionId": "e3eed660-c619-4336-a353-5acdd2372470"}], "processVariable": [{"name": "properties", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.79ad9803-626f-4181-8c3c-770f087a21ec", "description": {"isNull": "true"}, "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.a4275847-1a37-4d3c-9289-0462d5afbca9", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject[0].objectTypeId = \"\";\r\nautoObject[0].value = null;\r\nautoObject", "guid": "86cbedf5-6a3e-4804-a9ef-fd603204341e", "versionId": "4a08d7dd-0d71-43ef-9c31-90e2c7433bd8"}, {"name": "templateName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d77cfee1-b5f8-48c8-8413-9f4deccdd93a", "description": {"isNull": "true"}, "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "6a31c723-8462-42da-b2e0-d6bca8548769", "versionId": "3fa0074b-9a22-4b7f-906d-a6e056e79dd5"}, {"name": "paramsV<PERSON>ues", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.88fc3cd0-f6cb-465e-8f19-9802902cea33", "description": {"isNull": "true"}, "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.90c5b1d3-3fa1-4b3b-ab27-220b0652dc55", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "15f23f1a-c095-4346-8397-c9c2cbeb370f", "versionId": "e295226a-d7bb-4475-8ab6-58edeb313cfa"}, {"name": "request", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1d5f3808-737b-48d5-8993-0aef345100bd", "description": {"isNull": "true"}, "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "/12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d26d18ae-9d5f-4a50-b7d0-4f32d147f272", "versionId": "b4898c83-6cc5-48cb-8a2f-41bf0f3155a1"}, {"name": "response", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8194bbbc-8a6e-43a4-865f-51db9a4c83ec", "description": {"isNull": "true"}, "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "/12.a0722085-9246-4af1-b86e-8a21be7e94cc", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "61a9c9b1-5a5a-4d25-891d-8b7008b15333", "versionId": "c44a6938-a31e-48dc-b8c0-5d10e6a5b521"}, {"name": "folderID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.47435375-900d-463a-8170-bb2c5ac59b17", "description": {"isNull": "true"}, "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "e0a67c09-de75-4872-9099-e004c3b2e16a", "versionId": "13b3ade4-9d14-4300-a444-a7cc8c01fbad"}, {"name": "docs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.56f0e5cd-36e3-4171-8118-3182c75106a1", "description": {"isNull": "true"}, "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "namespace": "2", "seq": "7", "isArrayOf": "true", "isTransient": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.490f939c-3c6d-4ef7-9707-33b5b618877a", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f706efb9-1b4b-4eb6-acf7-6ec6b60b19ab", "versionId": "03272da3-6800-487e-97c2-44c7243bc713"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "regenerate?", "tWComponentName": "Switch", "tWComponentId": "3013.1d9387be-9862-4023-8f8a-595ae3d9949f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:16d6", "versionId": "0000af5f-782a-40c5-bf01-4183b15866af", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "314", "y": "73", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.1d9387be-9862-4023-8f8a-595ae3d9949f", "guid": "9d3f597d-0c65-4b22-9265-bc84e4d75eaf", "versionId": "ab23816e-bcf4-4eba-9f58-25e3c04ad2c6", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.aee81b16-c29b-4e02-87d5-04555dfed37c", "switchId": "3013.1d9387be-9862-4023-8f8a-595ae3d9949f", "seq": "1", "endStateId": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f12", "condition": "!!tw.local.odcRequest.templateDocID", "guid": "45bfb906-9b64-47ce-bd87-7cda9e313da2", "versionId": "afc54b01-ca27-46e8-b722-e699fa0e6ea7"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7044d967-7568-4a9e-8f73-a0143e928089", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "Generate Document", "tWComponentName": "SKELConnector", "tWComponentId": "3034.cb0ddfa4-5b82-4494-b28f-6b8f5a03d6fb", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1403", "versionId": "1551904e-059a-4101-b614-e107411612ea", "externalServiceRef": "/1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "externalServiceOp": "_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed", "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.581b2560-24b1-43cf-85cf-33b780778bc7", "processItemId": "2025.7044d967-7568-4a9e-8f73-a0143e928089", "location": "1", "script": "log.info(\" ServiceName : Document Generation: START\");", "guid": "a2d28ffa-e2a3-49c6-9409-5557c963008e", "versionId": "92634e61-cdb0-42a1-8cc0-868d62ffc3b3"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.a1b62852-ee8c-4c29-bbbd-c927ce68aea4", "processItemId": "2025.7044d967-7568-4a9e-8f73-a0143e928089", "location": "2", "script": "log.info(\" ServiceName : Document Generation: END\");\r\r\ntw.local.docID = tw.local.response.templatesResponse[0].ecmDocName;", "guid": "b7591f42-669a-4681-afae-eef31856c8a8", "versionId": "948f6540-986d-4a67-97ba-6b39fbeb340c"}], "layoutData": {"x": "859", "y": "54", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "skelConnectorId": "3034.cb0ddfa4-5b82-4494-b28f-6b8f5a03d6fb", "definition": "<config type=\"com.lombardisoftware.client.persistence.SKELConnectorConfiguration\">\r\n  <inputParameters>\r\n    <parameter>\r\n      <name>_5f31e534-f82c-4bed-8b97-f4b50bb07fb8</name>\r\n      <type>itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063</type>\r\n      <description />\r\n      <defaultValue />\r\n      <argumentVariable>tw.local.request</argumentVariable>\r\n      <isArray></isArray>\r\n    </parameter>\r\n  </inputParameters>\r\n  <outputParameters>\r\n    <parameter>\r\n      <name>_66fa2129-ae2c-47d6-aa59-b4f60f4a6c96</name>\r\n      <type>itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc</type>\r\n      <description />\r\n      <defaultValue />\r\n      <argumentVariable>tw.local.response</argumentVariable>\r\n      <isArray></isArray>\r\n    </parameter>\r\n  </outputParameters>\r\n  <extendedProperties />\r\n</config>", "guid": "7199283f-9cf0-47dc-899c-bde63961a94e", "versionId": "b57ccadb-fc52-4e6f-9fe7-1e9ea4b05634"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bc8b682b-6442-40c7-88cc-2da045c87a52", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.1da008ca-96d9-47f7-8d3c-df19828c13ef", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:539c", "versionId": "3d1d9c3e-e537-4fa1-9387-03500c0e2590", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "935", "y": "408", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.1da008ca-96d9-47f7-8d3c-df19828c13ef", "message": "", "faultStyle": "1", "guid": "79b6794d-f53a-4279-ade5-53b8d7841641", "versionId": "c8ccdac6-4cff-4ffc-83c9-3e7ba39725b0", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.88153111-9dcf-4b75-a50c-43eaea4ba621", "processParameterId": "2055.c3371967-77c0-4272-901f-9f67262b643e", "parameterMappingParentId": "3007.1da008ca-96d9-47f7-8d3c-df19828c13ef", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "isList": "false", "isInput": "false", "guid": "beb23170-7158-439c-a38c-dc367bd62411", "versionId": "15336b51-c71a-439f-80ad-8e4695a45117", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "Delete Doc", "tWComponentName": "ECMConnector", "tWComponentId": "3030.37a71276-8dc4-4f99-824c-7e3ab2603c87", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.56d069f7-8b98-48b1-800b-dd3a134083b1", "guid": "guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:178d", "versionId": "4787c012-14f1-4748-97b8-f1a474bec666", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "401", "y": "185", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:651a1a6abf396537:64776e00:18baeba64af:-3298", "errorHandlerItemId": "2025.56d069f7-8b98-48b1-800b-dd3a134083b1", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "ecmConnectorId": "3030.37a71276-8dc4-4f99-824c-7e3ab2603c87", "definition": "<config type=\"com.lombardisoftware.client.persistence.ECMConnectorConfiguration\">\r\r\n  <inputParameters>\r\r\n    <parameter>\r\r\n      <name>documentId</name>\r\r\n      <type>ECMID</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.odcRequest.templateDocID</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>allVersions</name>\r\r\n      <type>Boolean</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>true</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>serverName</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>\"FileNet\"</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </inputParameters>\r\r\n  <outputParameters />\r\r\n  <matchAllSearchCriteria>true</matchAllSearchCriteria>\r\r\n  <searchParameters />\r\r\n  <selectParameters />\r\r\n  <orderOverride>false</orderOverride>\r\r\n  <orderOverrideValue></orderOverrideValue>\r\r\n  <operationType>DOC_OP_DELETE_DOCUMENT</operationType>\r\r\n  <server>useMappingServer</server>\r\r\n  <objectTypeSelection>DOCUMENT</objectTypeSelection>\r\r\n  <useMappedVariable>true</useMappedVariable>\r\r\n  <faultParameterId>2055.0b414eaa-a71d-4cd9-965e-efcefc32c027</faultParameterId>\r\r\n</config>", "guid": "1d339d7e-2c26-4429-933c-4d1419c97a04", "versionId": "ad42daff-99d1-41b8-8ec3-0504db179185"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3a8ae9f4-a4b5-4024-863f-a0b0817b39c7", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.580aca1a-03d5-408a-9599-8e18babd5f43", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-169e", "versionId": "6f045014-0ce1-45da-a638-992e9b8da9c3", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1017", "y": "77", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.580aca1a-03d5-408a-9599-8e18babd5f43", "haltProcess": "false", "guid": "9e973ace-4170-412f-8523-7ba669b4196e", "versionId": "bad0d1ad-c521-4451-974b-4a3b47437cbf"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "Prepare request", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.81651db4-f4a5-4632-a003-1b7b6fdbd044", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-160e", "versionId": "bc64a280-d1da-491e-9d90-64dda27a0fa3", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "649", "y": "54", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.81651db4-f4a5-4632-a003-1b7b6fdbd044", "scriptTypeId": "2", "isActive": "true", "script": "function stringToList(string) {\r\r\n\tvar stringList = new tw.object.listOf.String();\r\r\n\tstringList[0] = string;\r\r\n\treturn stringList;\r\r\n}\r\r\n\r\r\nfunction floatToStringList(float) {\r\r\n\tvar stringList = new tw.object.listOf.String();\r\r\n\tstringList[0] = \"\" + float;\r\r\n\treturn stringList;\r\r\n}\r\r\n\r\r\nfunction booleanToStringList(boolean) {\r\r\n\tvar stringList = new tw.object.listOf.String();\r\r\n\tif (boolean)\r\r\n\t\tstringList[0] = \"نعم\"\r\r\n\telse\r\r\n\t\tstringList[0] = \"لا\"\r\r\n\treturn stringList;\r\r\n}\r\r\n\r\r\nfunction formatDate(date) {\r\r\n\tdate.setHours(date.getHours() + 2);\r\r\n\tvar d = new Date(date),\r\r\n\t\tday = '' + (d.getDate()),\r\r\n\t\tmonth = '' + (d.getMonth() + 1),\r\r\n\t\tyear = d.getFullYear();\r\r\n\r\r\n\tif (month.length < 2)\r\r\n\t\tmonth = '0' + month;\r\r\n\tif (day.length < 2)\r\r\n\t\tday = '0' + day;\r\r\n\r\r\n\treturn [day, month, year].join('/');\r\r\n}\r\r\n\r\r\ntw.local.request = new tw.object.DocumentGenerationRequest();\r\r\ntw.local.request.templates = new tw.object.listOf.RequestTemplatePojo();\r\r\n//tw.local.request.templates[0] = new tw.object.RequestTemplatePojo();\r\r\n\r\r\ntw.local.request.appName = \"ODC create/Amend\";\r\r\ntw.local.request.purposeCode = new tw.object.listOf.String();\r\r\ntw.local.request.purposeCode[0] = \"PUR1\";\r\r\n\r\r\ntw.local.request.programCode = \"PROG1\";\r\r\n//\"PROG1\";\r\r\n\r\r\ntw.local.request.secure = true;\r\r\n\r\r\ntw.local.paramsValues = new tw.object.Map();\r\r\n\r\r\n\r\r\n//---------------------------------------------------\r\r\nvar ecmInfo = new tw.object.EcmDocumentInformation();\r\r\necmInfo.docClassName = \"Document\";\r\r\necmInfo.docProperties = \"\";\r\r\necmInfo.folderId = tw.local.docGenTempFolder.path;\r\r\n\r\r\nif (tw.local.generationStatus == \"Generate\") {\r\r\n\tvar title = \"\";\r\r\n}\r\r\nelse {\r\r\n\tif (tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value == \"other\") {\r\r\n\t\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\r\r\n\t}\r\r\n\telse {\r\r\n\t\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value;\r\r\n\t}\r\r\n}\r\r\n//var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value + tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\r\r\ntw.local.paramsValues.put(\"Title001\", stringToList(title));\r\r\nvar date = new tw.object.Date();\r\r\ntw.local.paramsValues.put(\"Date001\", stringToList(formatDate(date)));\r\r\nvar swiftCode = \"NBEGEGCX\" + tw.local.odcRequest.FinancialDetailsFO.executionHub.value;\r\r\n\r\r\ntw.local.paramsValues.put(\"Swift001\", stringToList(swiftCode));\r\r\ntw.local.paramsValues.put(\"Int001\", stringToList(tw.local.odcRequest.ImporterDetails.bank));\r\r\ntw.local.paramsValues.put(\"Int002\", stringToList(tw.local.odcRequest.ImporterDetails.bankAddress));\r\r\ntw.local.paramsValues.put(\"Int003\", stringToList(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\r\ntw.local.paramsValues.put(\"Int004\", stringToList(tw.local.odcRequest.ImporterDetails.BICCode));\r\r\ntw.local.paramsValues.put(\"Int005\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerName));\r\r\ntw.local.paramsValues.put(\"Int006\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerAddress));\r\r\ntw.local.paramsValues.put(\"Int007\", stringToList(tw.local.odcRequest.ImporterDetails.importerName));\r\r\ntw.local.paramsValues.put(\"Int008\", stringToList(tw.local.odcRequest.ImporterDetails.importerAddress));\r\r\ntw.local.paramsValues.put(\"Int009\", stringToList(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\r\ntw.local.paramsValues.put(\"Int010\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\r\ntw.local.paramsValues.put(\"Int011\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\r\nvar claimedAmount = tw.local.odcRequest.FinancialDetailsFO.collectableAmount + tw.local.odcRequest.FinancialDetailsFO.ourCharges;\r\r\ntw.local.paramsValues.put(\"Int012\", floatToStringList(claimedAmount));\r\r\ntw.local.paramsValues.put(\"Int013\", stringToList(tw.local.odcRequest.FinancialDetailsBR.currency.name));\r\r\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name == \"001\") {\r\r\n\ttw.local.paramsValues.put(\"Int014\", stringToList(\"Sight\"));\r\r\n}\r\r\nelse {\r\r\n\ttw.local.paramsValues.put(\"Int014\", stringToList(formatDate(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date)));\r\r\n}\r\r\ntw.local.paramsValues.put(\"Int015\", stringToList(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\r\n\r\r\nvar deliveryTerms = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms.listLength; i++) {\r\r\n\tdeliveryTerms[i] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[i];\r\r\n}\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength; i++) {\r\r\n\t\tdeliveryTerms[deliveryTerms.length] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra[i];\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.paramsValues.put(\"Int016\", deliveryTerms);\r\r\n\r\r\nvar paymentInstructions = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions.listLength; i++) {\r\r\n\tpaymentInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[i];\r\r\n}\r\r\nvar length = paymentInstructions.length;\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength; i++) {\r\r\n\t\tpaymentInstructions[paymentInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra[i];\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.paramsValues.put(\"Int017\", paymentInstructions);\r\r\n\r\r\nvar instructions = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.Instructions.listLength; i++) {\r\r\n\tinstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.Instructions[i];\r\r\n}\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength; i++) {\r\r\n\t\tinstructions[instructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra[i];\r\r\n\t}\r\r\n}\r\r\ntw.local.paramsValues.put(\"Int018\", instructions);\r\r\n\r\r\nvar specialInstructions = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions.listLength; i++) {\r\r\n\tspecialInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[i];\r\r\n}\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength; i++) {\r\r\n\t\tspecialInstructions[specialInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra[i];\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.paramsValues.put(\"Int019\", specialInstructions);\r\r\n\r\r\n////////////////////////////////////////////////////////////////////////////////\r\r\nvar documentNames = new tw.object.listOf.String();\r\r\nvar noOfCopies = new tw.object.listOf.String();\r\r\nvar noOfOriginals = new tw.object.listOf.String();\r\r\nvar docsCount =0;\r\r\nfor (var i = 0; i < tw.local.odcRequest.attachmentDetails.attachment.listLength; i++) {\r\r\n\tif (!!tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals || !!tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies) {\r\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].name == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].name == null) {\r\r\n\t\t\tdocumentNames[docsCount] = \"\";\r\r\n\t\t}\r\r\n\t\telse {\r\r\n\t\t\tdocumentNames[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].name;\r\r\n\t\t}\r\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == null) {\r\r\n\t\t\tnoOfOriginals[docsCount] = \"\";\r\r\n\t\t}\r\r\n\t\telse {\r\r\n\t\t\tnoOfOriginals[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals + \"\";\r\r\n\t\t}\r\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == null) {\r\r\n\t\t\tnoOfCopies[docsCount] = \"\";\r\r\n\t\t}\r\r\n\t\telse {\r\r\n\t\t\tnoOfCopies[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies + \"\";\r\r\n\t\t}\r\r\n\t\tdocsCount++;\r\r\n\r\r\n\t}\r\r\n//\telse{\r\r\n//\t\tcontinue;\r\r\n//\t}\r\r\n}\r\r\nif(docsCount>0){\r\r\n\ttw.local.paramsValues.put(\"intr001\", documentNames);\r\r\n\ttw.local.paramsValues.put(\"intr002\", noOfOriginals);\r\r\n\ttw.local.paramsValues.put(\"intr003\", noOfCopies);\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n//tw.local.paramsValues.put(\"intr001\",specialInstructions);\r\r\n//tw.local.paramsValues.put(\"intr002\",specialInstructions);\r\r\n//tw.local.paramsValues.put(\"intr003\",specialInstructions);\r\r\n\r\r\ntw.local.request.paramsValues = tw.local.paramsValues;\r\r\n//--------------------------Generate Docs-------------------\r\r\nvar template = new tw.object.RequestTemplatePojo();\r\r\ntemplate.secure = true;\r\r\ntemplate.ecmDocInfo = ecmInfo;\r\r\ntemplate.templateCode = \"ODCRemittanceLetter\";\r\r\ntemplate.generatedDocName = \"remittance Letter\";\r\r\ntw.local.request.templates.insertIntoList(tw.local.request.templates.listLength, template);\r\r\n", "isRule": "false", "guid": "90118122-6e47-440f-98c6-201a45c6cf42", "versionId": "f3760ef8-189e-47ea-a969-6d9807131a6d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.56d069f7-8b98-48b1-800b-dd3a134083b1", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:651a1a6abf396537:64776e00:18baeba64af:-3298", "versionId": "bdc99aad-385e-4458-9228-8ef1c6c9dfde", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "712", "y": "311", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "2d3847d4-bc06-4292-bb2f-094f0b39f872", "versionId": "2ba7eeac-b0dc-47f6-b3cc-57d60c77116b", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.93ffc81f-1a26-4877-978b-c43a5709e4f4", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "1c9955c5-e285-4bae-8bd4-84dfee4b310c", "versionId": "03051c67-0d12-42a7-a53d-bb4b158e46bf", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.dbe92ea4-a9c8-421b-b337-9226e9c9d5b4", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "b0d6fd7b-6488-4ee0-832d-120aed7f8864", "versionId": "96401f41-703d-437c-8a68-2bf2b200dfb3", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b2d9bcc4-aaf1-40f7-b211-f02604a0ab09", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2", "useDefault": "false", "value": "\"Generate Remittance Letter\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ebc49c71-b1bf-4ead-8fc0-eaf08387a19e", "versionId": "d484534f-6873-4a9c-a326-bda186e07e7c", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b4ca5615-841a-4b96-8f71-17df1c09d08e", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "name": "Get Doc Gen Temp Folder", "tWComponentName": "ECMConnector", "tWComponentId": "3030.3ce9a66d-a3ed-4ed8-90b2-13c6b0680d40", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1610", "versionId": "f00a9250-57b5-46c8-beba-74a535360fe1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.08d7a07a-a4be-41bd-948e-365b1e0e4902", "processItemId": "2025.b4ca5615-841a-4b96-8f71-17df1c09d08e", "location": "1", "script": "log.info(\"ProcessInstance : \"+tw.system.currentProcessInstanceID +\" - ServiceName : Contract Document Generation : START\");\r\r\nlog.info(\" ServiceName : Get Doc Gen Temp Folder: START\");", "guid": "afdfd841-3482-43b7-b053-7375fec1a76e", "versionId": "26e5d969-582f-4fd8-a8d6-36956e8e2b7f"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.00041e72-5de7-4308-87c6-178cbe9b5701", "processItemId": "2025.b4ca5615-841a-4b96-8f71-17df1c09d08e", "location": "2", "script": "log.info(\" ServiceName : Get Doc Gen Temp Folder: END\");", "guid": "4cacff6e-8f7c-4d35-a039-c8571c1ff103", "versionId": "622ce84d-c8f9-4a16-8eb9-608b367d5a1a"}], "layoutData": {"x": "505", "y": "53", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "ecmConnectorId": "3030.3ce9a66d-a3ed-4ed8-90b2-13c6b0680d40", "definition": "<config type=\"com.lombardisoftware.client.persistence.ECMConnectorConfiguration\">\r\r\n  <inputParameters>\r\r\n    <parameter>\r\r\n      <name>path</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.odcRequest.folderPath</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>serverName</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>\"FileNet\"</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </inputParameters>\r\r\n  <outputParameters>\r\r\n    <parameter>\r\r\n      <name>folder</name>\r\r\n      <type>ECMFolder</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.docGenTempFolder</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </outputParameters>\r\r\n  <matchAllSearchCriteria>true</matchAllSearchCriteria>\r\r\n  <searchParameters />\r\r\n  <selectParameters />\r\r\n  <orderOverride>false</orderOverride>\r\r\n  <orderOverrideValue></orderOverrideValue>\r\r\n  <operationType>FOLDER_OP_GET_FOLDER_BY_PATH</operationType>\r\r\n  <server>useMappingServer</server>\r\r\n  <objectTypeSelection>DOCUMENT</objectTypeSelection>\r\r\n  <useMappedVariable>true</useMappedVariable>\r\r\n  <faultParameterId>2055.7474d81e-18d3-4732-8a36-07e6f72c583f</faultParameterId>\r\r\n</config>", "guid": "7297e89a-1727-428e-88de-bb998fc89468", "versionId": "23b63278-55a6-4a80-b888-8998763f6400"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "209", "y": "77", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Generate Remittance Letter", "id": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.4996fad4-3fac-48ef-8f5b-05525969b773", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"{7015CC8B-0000-CF4A-AB45-A92089CC7DB7}\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"/أرشيف عملاء البنك الاهلي/عملاء البنك/03024659/DC Outward/00104230000209ODC/Issuance\";\r\nautoObject.templateDocID = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "generationStatus", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.cb493271-2446-49c9-80fd-c8bfca9646bc"}], "ns16:dataOutput": [{"name": "docGenTempFolder", "itemSubjectRef": "itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183", "isCollection": "false", "id": "2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a37faedf-a8f5-421e-8773-5c59cb9d3888"}, {"name": "isSuccessful", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.ad3e5165-9152-45e2-8bef-7be47d669999"}, {"name": "docID", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939"}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.4996fad4-3fac-48ef-8f5b-05525969b773", "2055.cb493271-2446-49c9-80fd-c8bfca9646bc"]}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417", "2055.a37faedf-a8f5-421e-8773-5c59cb9d3888", "2055.ad3e5165-9152-45e2-8bef-7be47d669999", "2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939"]}}, "ns16:laneSet": {"id": "9213a0a3-4f14-498b-870a-bdd484aa7ff9", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "c4d72eee-6884-4051-8394-1ddf22790a12", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["98eaadf2-a3e9-4432-8cc8-70ef8c5970c6", "3a8ae9f4-a4b5-4024-863f-a0b0817b39c7", "b4ca5615-841a-4b96-8f71-17df1c09d08e", "df1ded4c-8bfd-44d8-8f0f-b837239503ce", "7044d967-7568-4a9e-8f73-a0143e928089", "56d069f7-8b98-48b1-800b-dd3a134083b1", "c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "13c3f7c0-c045-4e71-88f4-4a56be3e590d", "21c155e3-195c-40b3-891c-9ec39b9f93b7", "bc8b682b-6442-40c7-88cc-2da045c87a52"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "98eaadf2-a3e9-4432-8cc8-70ef8c5970c6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "209", "y": "77", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.2521cec4-3685-4925-848f-52ab82439a5d"}, "ns16:endEvent": [{"name": "End", "id": "3a8ae9f4-a4b5-4024-863f-a0b0817b39c7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1017", "y": "77", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-169e"}, "ns16:incoming": "6f0096bd-6183-4c60-81fb-1d5ef2324a64"}, {"name": "End Event", "id": "bc8b682b-6442-40c7-88cc-2da045c87a52", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "935", "y": "408", "width": "24", "height": "24"}}, "ns16:incoming": "4a81ac30-2f20-4653-83e0-0b0c4aa45d0c", "ns16:errorEventDefinition": {"id": "e75eccda-e8f4-41d4-8c9b-d3c6e4538dc0", "eventImplId": "e1f379d2-8f3b-4915-826a-0cf9bb01de4c", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "98eaadf2-a3e9-4432-8cc8-70ef8c5970c6", "targetRef": "c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "name": "To Exclusive Gateway", "id": "2027.2521cec4-3685-4925-848f-52ab82439a5d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "b4ca5615-841a-4b96-8f71-17df1c09d08e", "targetRef": "df1ded4c-8bfd-44d8-8f0f-b837239503ce", "name": "To Script Task1", "id": "4c44551e-24bb-4ce9-8c6c-b0e99527615b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "df1ded4c-8bfd-44d8-8f0f-b837239503ce", "targetRef": "7044d967-7568-4a9e-8f73-a0143e928089", "name": "To Get template names", "id": "456da453-0ff0-48d2-8993-02260083c2ad", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "7044d967-7568-4a9e-8f73-a0143e928089", "targetRef": "3a8ae9f4-a4b5-4024-863f-a0b0817b39c7", "name": "To End", "id": "6f0096bd-6183-4c60-81fb-1d5ef2324a64", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "56d069f7-8b98-48b1-800b-dd3a134083b1", "targetRef": "bc8b682b-6442-40c7-88cc-2da045c87a52", "name": "To End", "id": "4a81ac30-2f20-4653-83e0-0b0c4aa45d0c", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "targetRef": "b4ca5615-841a-4b96-8f71-17df1c09d08e", "name": "no", "id": "078c569a-3fac-4dfa-8c82-6d494c69d3de", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "targetRef": "13c3f7c0-c045-4e71-88f4-4a56be3e590d", "name": "yes", "id": "0b7e9890-ebed-4616-8d5c-037bbf967812", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "!!tw.local.odcRequest.templateDocID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "13c3f7c0-c045-4e71-88f4-4a56be3e590d", "targetRef": "b4ca5615-841a-4b96-8f71-17df1c09d08e", "name": "To Get Doc Gen Temp Folder", "id": "e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "21c155e3-195c-40b3-891c-9ec39b9f93b7", "targetRef": "56d069f7-8b98-48b1-800b-dd3a134083b1", "name": "To Exception Handling", "id": "6945970e-2c78-4a60-8fcd-6be760d2a3f6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns4:contentTask": [{"serverName": "useMappingServer", "operationRef": "FOLDER_OP_GET_FOLDER_BY_PATH", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Get Doc Gen Temp Folder", "id": "b4ca5615-841a-4b96-8f71-17df1c09d08e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "505", "y": "53", "width": "95", "height": "70"}, "ns4:activityType": "ContentTask", "ns3:postAssignmentScript": "log.info(\" ServiceName : Get Doc Gen Temp Folder: END\");", "ns3:preAssignmentScript": "log.info(\"ProcessInstance : \"+tw.system.currentProcessInstanceID +\" - ServiceName : Contract Document Generation : START\");\r\r\nlog.info(\" ServiceName : Get Doc Gen Temp Folder: START\");"}, "ns16:incoming": ["078c569a-3fac-4dfa-8c82-6d494c69d3de", "e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6"], "ns16:outgoing": "4c44551e-24bb-4ce9-8c6c-b0e99527615b", "ns16:dataInputAssociation": [{"ns16:targetRef": "SERVER_NAME", "ns16:assignment": {"ns16:from": {"_": "\"FileNet\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "PATH", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.folderPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "FOLDER", "ns16:assignment": {"ns16:to": {"_": "tw.local.docGenTempFolder", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183"}}}}, {"serverName": "useMappingServer", "operationRef": "DOC_OP_DELETE_DOCUMENT", "name": "Delete Doc", "id": "13c3f7c0-c045-4e71-88f4-4a56be3e590d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "401", "y": "185", "width": "95", "height": "70"}, "ns4:activityType": "ContentTask"}, "ns16:incoming": "0b7e9890-ebed-4616-8d5c-037bbf967812", "ns16:outgoing": "e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6", "ns16:dataInputAssociation": [{"ns16:targetRef": "SERVER_NAME", "ns16:assignment": {"ns16:from": {"_": "\"FileNet\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "ALL_VERSIONS", "ns16:assignment": {"ns16:from": {"_": "true", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:targetRef": "DOCUMENT_ID", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.templateDocID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Prepare request", "id": "df1ded4c-8bfd-44d8-8f0f-b837239503ce", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "649", "y": "54", "width": "95", "height": "70"}}, "ns16:incoming": "4c44551e-24bb-4ce9-8c6c-b0e99527615b", "ns16:outgoing": "456da453-0ff0-48d2-8993-02260083c2ad", "ns16:script": "function stringToList(string) {\r\r\n\tvar stringList = new tw.object.listOf.String();\r\r\n\tstringList[0] = string;\r\r\n\treturn stringList;\r\r\n}\r\r\n\r\r\nfunction floatToStringList(float) {\r\r\n\tvar stringList = new tw.object.listOf.String();\r\r\n\tstringList[0] = \"\" + float;\r\r\n\treturn stringList;\r\r\n}\r\r\n\r\r\nfunction booleanToStringList(boolean) {\r\r\n\tvar stringList = new tw.object.listOf.String();\r\r\n\tif (boolean)\r\r\n\t\tstringList[0] = \"نعم\"\r\r\n\telse\r\r\n\t\tstringList[0] = \"لا\"\r\r\n\treturn stringList;\r\r\n}\r\r\n\r\r\nfunction formatDate(date) {\r\r\n\tdate.setHours(date.getHours() + 2);\r\r\n\tvar d = new Date(date),\r\r\n\t\tday = '' + (d.getDate()),\r\r\n\t\tmonth = '' + (d.getMonth() + 1),\r\r\n\t\tyear = d.getFullYear();\r\r\n\r\r\n\tif (month.length < 2)\r\r\n\t\tmonth = '0' + month;\r\r\n\tif (day.length < 2)\r\r\n\t\tday = '0' + day;\r\r\n\r\r\n\treturn [day, month, year].join('/');\r\r\n}\r\r\n\r\r\ntw.local.request = new tw.object.DocumentGenerationRequest();\r\r\ntw.local.request.templates = new tw.object.listOf.RequestTemplatePojo();\r\r\n//tw.local.request.templates[0] = new tw.object.RequestTemplatePojo();\r\r\n\r\r\ntw.local.request.appName = \"ODC create/Amend\";\r\r\ntw.local.request.purposeCode = new tw.object.listOf.String();\r\r\ntw.local.request.purposeCode[0] = \"PUR1\";\r\r\n\r\r\ntw.local.request.programCode = \"PROG1\";\r\r\n//\"PROG1\";\r\r\n\r\r\ntw.local.request.secure = true;\r\r\n\r\r\ntw.local.paramsValues = new tw.object.Map();\r\r\n\r\r\n\r\r\n//---------------------------------------------------\r\r\nvar ecmInfo = new tw.object.EcmDocumentInformation();\r\r\necmInfo.docClassName = \"Document\";\r\r\necmInfo.docProperties = \"\";\r\r\necmInfo.folderId = tw.local.docGenTempFolder.path;\r\r\n\r\r\nif (tw.local.generationStatus == \"Generate\") {\r\r\n\tvar title = \"\";\r\r\n}\r\r\nelse {\r\r\n\tif (tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value == \"other\") {\r\r\n\t\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\r\r\n\t}\r\r\n\telse {\r\r\n\t\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value;\r\r\n\t}\r\r\n}\r\r\n//var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value + tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\r\r\ntw.local.paramsValues.put(\"Title001\", stringToList(title));\r\r\nvar date = new tw.object.Date();\r\r\ntw.local.paramsValues.put(\"Date001\", stringToList(formatDate(date)));\r\r\nvar swiftCode = \"NBEGEGCX\" + tw.local.odcRequest.FinancialDetailsFO.executionHub.value;\r\r\n\r\r\ntw.local.paramsValues.put(\"Swift001\", stringToList(swiftCode));\r\r\ntw.local.paramsValues.put(\"Int001\", stringToList(tw.local.odcRequest.ImporterDetails.bank));\r\r\ntw.local.paramsValues.put(\"Int002\", stringToList(tw.local.odcRequest.ImporterDetails.bankAddress));\r\r\ntw.local.paramsValues.put(\"Int003\", stringToList(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\r\ntw.local.paramsValues.put(\"Int004\", stringToList(tw.local.odcRequest.ImporterDetails.BICCode));\r\r\ntw.local.paramsValues.put(\"Int005\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerName));\r\r\ntw.local.paramsValues.put(\"Int006\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerAddress));\r\r\ntw.local.paramsValues.put(\"Int007\", stringToList(tw.local.odcRequest.ImporterDetails.importerName));\r\r\ntw.local.paramsValues.put(\"Int008\", stringToList(tw.local.odcRequest.ImporterDetails.importerAddress));\r\r\ntw.local.paramsValues.put(\"Int009\", stringToList(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\r\ntw.local.paramsValues.put(\"Int010\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\r\ntw.local.paramsValues.put(\"Int011\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\r\nvar claimedAmount = tw.local.odcRequest.FinancialDetailsFO.collectableAmount + tw.local.odcRequest.FinancialDetailsFO.ourCharges;\r\r\ntw.local.paramsValues.put(\"Int012\", floatToStringList(claimedAmount));\r\r\ntw.local.paramsValues.put(\"Int013\", stringToList(tw.local.odcRequest.FinancialDetailsBR.currency.name));\r\r\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name == \"001\") {\r\r\n\ttw.local.paramsValues.put(\"Int014\", stringToList(\"Sight\"));\r\r\n}\r\r\nelse {\r\r\n\ttw.local.paramsValues.put(\"Int014\", stringToList(formatDate(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date)));\r\r\n}\r\r\ntw.local.paramsValues.put(\"Int015\", stringToList(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\r\n\r\r\nvar deliveryTerms = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms.listLength; i++) {\r\r\n\tdeliveryTerms[i] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[i];\r\r\n}\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength; i++) {\r\r\n\t\tdeliveryTerms[deliveryTerms.length] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra[i];\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.paramsValues.put(\"Int016\", deliveryTerms);\r\r\n\r\r\nvar paymentInstructions = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions.listLength; i++) {\r\r\n\tpaymentInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[i];\r\r\n}\r\r\nvar length = paymentInstructions.length;\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength; i++) {\r\r\n\t\tpaymentInstructions[paymentInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra[i];\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.paramsValues.put(\"Int017\", paymentInstructions);\r\r\n\r\r\nvar instructions = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.Instructions.listLength; i++) {\r\r\n\tinstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.Instructions[i];\r\r\n}\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength; i++) {\r\r\n\t\tinstructions[instructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra[i];\r\r\n\t}\r\r\n}\r\r\ntw.local.paramsValues.put(\"Int018\", instructions);\r\r\n\r\r\nvar specialInstructions = new tw.object.listOf.String();\r\r\nfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions.listLength; i++) {\r\r\n\tspecialInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[i];\r\r\n}\r\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength != 0) {\r\r\n\tfor (var i = 0; i < tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength; i++) {\r\r\n\t\tspecialInstructions[specialInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra[i];\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.paramsValues.put(\"Int019\", specialInstructions);\r\r\n\r\r\n////////////////////////////////////////////////////////////////////////////////\r\r\nvar documentNames = new tw.object.listOf.String();\r\r\nvar noOfCopies = new tw.object.listOf.String();\r\r\nvar noOfOriginals = new tw.object.listOf.String();\r\r\nvar docsCount =0;\r\r\nfor (var i = 0; i < tw.local.odcRequest.attachmentDetails.attachment.listLength; i++) {\r\r\n\tif (!!tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals || !!tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies) {\r\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].name == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].name == null) {\r\r\n\t\t\tdocumentNames[docsCount] = \"\";\r\r\n\t\t}\r\r\n\t\telse {\r\r\n\t\t\tdocumentNames[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].name;\r\r\n\t\t}\r\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == null) {\r\r\n\t\t\tnoOfOriginals[docsCount] = \"\";\r\r\n\t\t}\r\r\n\t\telse {\r\r\n\t\t\tnoOfOriginals[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals + \"\";\r\r\n\t\t}\r\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == null) {\r\r\n\t\t\tnoOfCopies[docsCount] = \"\";\r\r\n\t\t}\r\r\n\t\telse {\r\r\n\t\t\tnoOfCopies[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies + \"\";\r\r\n\t\t}\r\r\n\t\tdocsCount++;\r\r\n\r\r\n\t}\r\r\n//\telse{\r\r\n//\t\tcontinue;\r\r\n//\t}\r\r\n}\r\r\nif(docsCount>0){\r\r\n\ttw.local.paramsValues.put(\"intr001\", documentNames);\r\r\n\ttw.local.paramsValues.put(\"intr002\", noOfOriginals);\r\r\n\ttw.local.paramsValues.put(\"intr003\", noOfCopies);\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n//tw.local.paramsValues.put(\"intr001\",specialInstructions);\r\r\n//tw.local.paramsValues.put(\"intr002\",specialInstructions);\r\r\n//tw.local.paramsValues.put(\"intr003\",specialInstructions);\r\r\n\r\r\ntw.local.request.paramsValues = tw.local.paramsValues;\r\r\n//--------------------------Generate Docs-------------------\r\r\nvar template = new tw.object.RequestTemplatePojo();\r\r\ntemplate.secure = true;\r\r\ntemplate.ecmDocInfo = ecmInfo;\r\r\ntemplate.templateCode = \"ODCRemittanceLetter\";\r\r\ntemplate.generatedDocName = \"remittance Letter\";\r\r\ntw.local.request.templates.insertIntoList(tw.local.request.templates.listLength, template);\r\r\n"}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9", "isCollection": "true", "name": "properties", "id": "2056.79ad9803-626f-4181-8c3c-770f087a21ec", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject[0].objectTypeId = \"\";\r\nautoObject[0].value = null;\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "isCollection": "true", "name": "templateName", "id": "2056.d77cfee1-b5f8-48c8-8413-9f4deccdd93a"}, {"itemSubjectRef": "itm.12.90c5b1d3-3fa1-4b3b-ab27-220b0652dc55", "isCollection": "false", "name": "paramsV<PERSON>ues", "id": "2056.88fc3cd0-f6cb-465e-8f19-9802902cea33"}, {"itemSubjectRef": "itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "isCollection": "false", "name": "request", "id": "2056.1d5f3808-737b-48d5-8993-0aef345100bd"}, {"itemSubjectRef": "itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc", "isCollection": "false", "name": "response", "id": "2056.8194bbbc-8a6e-43a4-865f-51db9a4c83ec"}, {"itemSubjectRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01", "isCollection": "false", "name": "folderID", "id": "2056.47435375-900d-463a-8170-bb2c5ac59b17"}, {"itemSubjectRef": "itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a", "isCollection": "true", "name": "docs", "id": "2056.56f0e5cd-36e3-4171-8118-3182c75106a1"}], "ns16:serviceTask": {"xmlns:ns23": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service", "operationRef": "ns23:_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Generate Document", "id": "7044d967-7568-4a9e-8f73-a0143e928089", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "859", "y": "54", "width": "95", "height": "70"}, "ns4:activityType": "ServiceTask", "ns3:externalServiceRefPO": "1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "ns3:externalServiceRef": "ns23:_4f57dabd-7522-45c1-a6cb-ae30e4578c97", "ns3:preAssignmentScript": "log.info(\" ServiceName : Document Generation: START\");", "ns3:postAssignmentScript": "log.info(\" ServiceName : Document Generation: END\");\r\r\ntw.local.docID = tw.local.response.templatesResponse[0].ecmDocName;"}, "ns16:incoming": "456da453-0ff0-48d2-8993-02260083c2ad", "ns16:outgoing": "6f0096bd-6183-4c60-81fb-1d5ef2324a64", "ns16:dataInputAssociation": {"ns16:targetRef": "_5f31e534-f82c-4bed-8b97-f4b50bb07fb8", "ns16:assignment": {"ns16:from": {"_": "tw.local.request", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "_66fa2129-ae2c-47d6-aa59-b4f60f4a6c96", "ns16:assignment": {"ns16:to": {"_": "tw.local.response", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc"}}}}, "ns16:callActivity": {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exception Handling", "id": "56d069f7-8b98-48b1-800b-dd3a134083b1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "712", "y": "311", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "6945970e-2c78-4a60-8fcd-6be760d2a3f6", "ns16:outgoing": "4a81ac30-2f20-4653-83e0-0b0c4aa45d0c", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Generate Remittance Letter\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, "ns16:exclusiveGateway": {"default": "078c569a-3fac-4dfa-8c82-6d494c69d3de", "name": "regenerate?", "id": "c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "314", "y": "73", "width": "32", "height": "32"}}, "ns16:incoming": "2027.2521cec4-3685-4925-848f-52ab82439a5d", "ns16:outgoing": ["078c569a-3fac-4dfa-8c82-6d494c69d3de", "0b7e9890-ebed-4616-8d5c-037bbf967812"]}, "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "13c3f7c0-c045-4e71-88f4-4a56be3e590d", "parallelMultiple": "false", "name": "Error", "id": "21c155e3-195c-40b3-891c-9ec39b9f93b7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "436", "y": "243", "width": "24", "height": "24"}}, "ns16:outgoing": "6945970e-2c78-4a60-8fcd-6be760d2a3f6", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "98eb20b4-0d3e-401c-8b7f-d01d8b88083b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "f1a42aaa-61ef-46c9-88ea-255111151e90", "eventImplId": "4e7012cf-723f-425a-8175-9f1a6178fd44", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": [{"name": "yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.0b7e9890-ebed-4616-8d5c-037bbf967812", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc"], "endStateId": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f12", "toProcessItemId": ["2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d", "2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d"], "guid": "5c3a7dc0-72fa-4141-a258-58770550d98c", "versionId": "52e72bfa-196d-4634-987a-556fe858a01f", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Script Task1", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4c44551e-24bb-4ce9-8c6c-b0e99527615b", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b4ca5615-841a-4b96-8f71-17df1c09d08e", "2025.b4ca5615-841a-4b96-8f71-17df1c09d08e"], "endStateId": "Out", "toProcessItemId": ["2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce", "2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce"], "guid": "0ff28d29-cbaf-4f7e-96f2-a16a4afdb98f", "versionId": "60acb62e-f508-40e4-930e-2674bbd4fedc", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "no", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.078c569a-3fac-4dfa-8c82-6d494c69d3de", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc", "2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.b4ca5615-841a-4b96-8f71-17df1c09d08e", "2025.b4ca5615-841a-4b96-8f71-17df1c09d08e"], "guid": "0aa9dfe9-f931-495d-97f3-a71405cb53b0", "versionId": "7566527a-056d-4770-8350-467ddc617531", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4a81ac30-2f20-4653-83e0-0b0c4aa45d0c", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.56d069f7-8b98-48b1-800b-dd3a134083b1", "2025.56d069f7-8b98-48b1-800b-dd3a134083b1"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.bc8b682b-6442-40c7-88cc-2da045c87a52", "2025.bc8b682b-6442-40c7-88cc-2da045c87a52"], "guid": "8a867ed1-6d7f-409b-961d-9b7e78218b91", "versionId": "76d28167-6255-4a6c-9679-325898f9ecf8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To Get template names", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.456da453-0ff0-48d2-8993-02260083c2ad", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce", "2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce"], "endStateId": "Out", "toProcessItemId": ["2025.7044d967-7568-4a9e-8f73-a0143e928089", "2025.7044d967-7568-4a9e-8f73-a0143e928089"], "guid": "38d9cf2c-75a3-4dc1-a5a0-8604d9049b41", "versionId": "7935028e-efff-44b5-92dd-bbe643dd5bad", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get Doc Gen Temp Folder", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d", "2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d"], "endStateId": "Out", "toProcessItemId": ["2025.b4ca5615-841a-4b96-8f71-17df1c09d08e", "2025.b4ca5615-841a-4b96-8f71-17df1c09d08e"], "guid": "a5a79dbf-df6e-4745-acf6-5d06afb365dc", "versionId": "c39fe154-b0b1-49b9-bf8b-5584860a018b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6f0096bd-6183-4c60-81fb-1d5ef2324a64", "processId": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.7044d967-7568-4a9e-8f73-a0143e928089", "2025.7044d967-7568-4a9e-8f73-a0143e928089"], "endStateId": "Out", "toProcessItemId": ["2025.3a8ae9f4-a4b5-4024-863f-a0b0817b39c7", "2025.3a8ae9f4-a4b5-4024-863f-a0b0817b39c7"], "guid": "80cb1eed-dbea-4108-a901-3c6cedf9dd53", "versionId": "d1bc8998-1466-4ef1-bfcc-58ca62aa6e6b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}