<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.ce54fd48-8513-44df-9dc5-530fe191c83f" name="Get terms and conditions">
        <lastModified>1698234095930</lastModified>
        <lastModifiedBy>fatma</lastModifiedBy>
        <processId>1.ce54fd48-8513-44df-9dc5-530fe191c83f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.ad0eda60-3875-409a-887e-ee7a077e432e</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f3a</guid>
        <versionId>3f6df8cc-626f-4428-a80d-46cc65930974</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:266f6f4955d8489f:7b0b9b81:18b66504d45:-5e7c" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.2f066134-1b26-4c7e-881d-df58956dc62c"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"e835a1e4-8674-4163-88cb-1c0d3661f9b1"},{"incoming":["b6239c32-c26f-40e6-8174-47ea729ca063"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f38"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"358814d6-186a-4437-8e31-aa400b3d1f71"},{"targetRef":"ad0eda60-3875-409a-887e-ee7a077e432e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set payment instructions list","declaredType":"sequenceFlow","id":"2027.2f066134-1b26-4c7e-881d-df58956dc62c","sourceRef":"e835a1e4-8674-4163-88cb-1c0d3661f9b1"},{"startQuantity":1,"outgoing":["b6239c32-c26f-40e6-8174-47ea729ca063"],"incoming":["2027.2f066134-1b26-4c7e-881d-df58956dc62c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":288,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set payment instructions list","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ad0eda60-3875-409a-887e-ee7a077e432e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = new tw.object.listOf.String();\r\n\r\nif(tw.local.data == tw.epv.TermsAndConditions.paymentTerms)\r\n{\r\n\ttw.local.results[0] = \"PLEASE CREDIT SAID AMOUNT TO OUR A\/C NO. 544-7-12954HELD WITH (CHASUS33)UNDER TESTED SWIFT MESSAGE TO US(NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\";\r\n\ttw.local.results[1] = \"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A\/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\";\r\n}\r\nelse if(tw.local.data == tw.epv.TermsAndConditions.instructions)\r\n{\r\n\ttw.local.results[0] = \"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\";\r\n\ttw.local.results[1] = \"PLEASE ADVISE FATE OF DOCUMENTS AT YOUR EARLIEST CONVENIENCE UNDER TESTED SWIFT MSG.\";\r\n}\r\nelse if(tw.local.data == tw.epv.TermsAndConditions.specialInstructions)\r\n{\r\n\ttw.local.results[0] = \"ALL YOUR CHARGES &amp; EXPENSES IF ANY AND ALL CHARGES OUTSIDE EGYPT TO BE BORNE BY THE DRAWEE. DO NOT WAIVE. IF THE DRAWEE REFUSED TO PAY CHARGES THE DOCUMENTS MUST NOT BE DELIVERED\";\r\n\ttw.local.results[1] = \"IF YOU ARE NOT IN A POSITION ALLOWING YOU TO EXECUTE OUR INSTRUCTIONS PLEASE DO NOT DELIVER DOCUMENTS TO THE DRAWEE AND INFORM US PROMPTLY, PENDING RECEIPT OUR WRITTEN INSTRUCTIONS.\";\r\n}\r\nelse\r\n{\r\n\ttw.local.results[0] = \"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A\/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF.\";\r\n\ttw.local.results[1] = \"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\";\t\r\n}\r\n"]}},{"targetRef":"358814d6-186a-4437-8e31-aa400b3d1f71","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"b6239c32-c26f-40e6-8174-47ea729ca063","sourceRef":"ad0eda60-3875-409a-887e-ee7a077e432e"}],"laneSet":[{"id":"179d730f-8cf9-48e1-8b10-ace0f96bc93d","lane":[{"flowNodeRef":["e835a1e4-8674-4163-88cb-1c0d3661f9b1","358814d6-186a-4437-8e31-aa400b3d1f71","ad0eda60-3875-409a-887e-ee7a077e432e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"1821e614-0bda-45d5-85a2-2dacdf950a23","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get terms and conditions","declaredType":"process","id":"1.ce54fd48-8513-44df-9dc5-530fe191c83f","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.38336c6e-f954-46a3-8007-33a22f69718c"}],"extensionElements":{"localizationResourceLinks":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.769dc134-1d15-4dd4-a967-c5f61cf352dc","epvProcessLinkId":"c27477af-95f6-437a-83f1-eca8efa12af3","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2"]}],"outputSet":[{"dataOutputRefs":["2055.38336c6e-f954-46a3-8007-33a22f69718c"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2</processParameterId>
            <processId>1.ce54fd48-8513-44df-9dc5-530fe191c83f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b68a625b-058f-4619-8802-34cdeb820669</guid>
            <versionId>60af74aa-fdd2-4f78-91ca-24fa1fb73913</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.38336c6e-f954-46a3-8007-33a22f69718c</processParameterId>
            <processId>1.ce54fd48-8513-44df-9dc5-530fe191c83f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>476f32be-43cf-43f1-9213-0bd151f09c9a</guid>
            <versionId>e6ccdc52-2f5b-4f84-8943-528b1b568ea9</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ad0eda60-3875-409a-887e-ee7a077e432e</processItemId>
            <processId>1.ce54fd48-8513-44df-9dc5-530fe191c83f</processId>
            <name>Set payment instructions list</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2cec8a59-1090-4c6c-8250-ffb6a35be5d6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f31</guid>
            <versionId>0f03ef4c-c889-42a6-9ed6-e76521e1b8f7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="288" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2cec8a59-1090-4c6c-8250-ffb6a35be5d6</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.String();&#xD;
&#xD;
if(tw.local.data == tw.epv.TermsAndConditions.paymentTerms)&#xD;
{&#xD;
	tw.local.results[0] = "PLEASE CREDIT SAID AMOUNT TO OUR A/C NO. 544-7-12954HELD WITH (CHASUS33)UNDER TESTED SWIFT MESSAGE TO US(NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.";&#xD;
	tw.local.results[1] = "PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.";&#xD;
}&#xD;
else if(tw.local.data == tw.epv.TermsAndConditions.instructions)&#xD;
{&#xD;
	tw.local.results[0] = "PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.";&#xD;
	tw.local.results[1] = "PLEASE ADVISE FATE OF DOCUMENTS AT YOUR EARLIEST CONVENIENCE UNDER TESTED SWIFT MSG.";&#xD;
}&#xD;
else if(tw.local.data == tw.epv.TermsAndConditions.specialInstructions)&#xD;
{&#xD;
	tw.local.results[0] = "ALL YOUR CHARGES &amp; EXPENSES IF ANY AND ALL CHARGES OUTSIDE EGYPT TO BE BORNE BY THE DRAWEE. DO NOT WAIVE. IF THE DRAWEE REFUSED TO PAY CHARGES THE DOCUMENTS MUST NOT BE DELIVERED";&#xD;
	tw.local.results[1] = "IF YOU ARE NOT IN A POSITION ALLOWING YOU TO EXECUTE OUR INSTRUCTIONS PLEASE DO NOT DELIVER DOCUMENTS TO THE DRAWEE AND INFORM US PROMPTLY, PENDING RECEIPT OUR WRITTEN INSTRUCTIONS.";&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.results[0] = "PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF.";&#xD;
	tw.local.results[1] = "PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.";	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>fbab90df-a978-4c73-80a1-3a1e8a43db3c</guid>
                <versionId>fd22750a-4dc5-444e-94e6-b20eb0da5902</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.358814d6-186a-4437-8e31-aa400b3d1f71</processItemId>
            <processId>1.ce54fd48-8513-44df-9dc5-530fe191c83f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.f5fa2d0b-1f8f-4ce8-bc82-0e1226a9203d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f38</guid>
            <versionId>8a42cde2-250f-42e9-b131-60a2bd0291ae</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.f5fa2d0b-1f8f-4ce8-bc82-0e1226a9203d</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>534493eb-60ac-40f9-b1f5-ec705a09934e</guid>
                <versionId>592141e4-190d-4481-8312-8c898a852796</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.5f63d5a1-3503-426b-90fa-a19663baab61</epvProcessLinkId>
            <epvId>/21.769dc134-1d15-4dd4-a967-c5f61cf352dc</epvId>
            <processId>1.ce54fd48-8513-44df-9dc5-530fe191c83f</processId>
            <guid>499d3dd2-d193-4f74-96d7-6efad5963238</guid>
            <versionId>6e5637cd-f34a-45b3-a3cd-963fadc72f84</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.ad0eda60-3875-409a-887e-ee7a077e432e</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get terms and conditions" id="1.ce54fd48-8513-44df-9dc5-530fe191c83f" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks />
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.769dc134-1d15-4dd4-a967-c5f61cf352dc" epvProcessLinkId="c27477af-95f6-437a-83f1-eca8efa12af3" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.38336c6e-f954-46a3-8007-33a22f69718c" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.38336c6e-f954-46a3-8007-33a22f69718c</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="179d730f-8cf9-48e1-8b10-ace0f96bc93d">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="1821e614-0bda-45d5-85a2-2dacdf950a23" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>e835a1e4-8674-4163-88cb-1c0d3661f9b1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>358814d6-186a-4437-8e31-aa400b3d1f71</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ad0eda60-3875-409a-887e-ee7a077e432e</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="e835a1e4-8674-4163-88cb-1c0d3661f9b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.2f066134-1b26-4c7e-881d-df58956dc62c</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="358814d6-186a-4437-8e31-aa400b3d1f71">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f38</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b6239c32-c26f-40e6-8174-47ea729ca063</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="e835a1e4-8674-4163-88cb-1c0d3661f9b1" targetRef="ad0eda60-3875-409a-887e-ee7a077e432e" name="To Set payment instructions list" id="2027.2f066134-1b26-4c7e-881d-df58956dc62c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set payment instructions list" id="ad0eda60-3875-409a-887e-ee7a077e432e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="288" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.2f066134-1b26-4c7e-881d-df58956dc62c</ns16:incoming>
                        
                        
                        <ns16:outgoing>b6239c32-c26f-40e6-8174-47ea729ca063</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.String();&#xD;
&#xD;
if(tw.local.data == tw.epv.TermsAndConditions.paymentTerms)&#xD;
{&#xD;
	tw.local.results[0] = "PLEASE CREDIT SAID AMOUNT TO OUR A/C NO. 544-7-12954HELD WITH (CHASUS33)UNDER TESTED SWIFT MESSAGE TO US(NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.";&#xD;
	tw.local.results[1] = "PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.";&#xD;
}&#xD;
else if(tw.local.data == tw.epv.TermsAndConditions.instructions)&#xD;
{&#xD;
	tw.local.results[0] = "PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.";&#xD;
	tw.local.results[1] = "PLEASE ADVISE FATE OF DOCUMENTS AT YOUR EARLIEST CONVENIENCE UNDER TESTED SWIFT MSG.";&#xD;
}&#xD;
else if(tw.local.data == tw.epv.TermsAndConditions.specialInstructions)&#xD;
{&#xD;
	tw.local.results[0] = "ALL YOUR CHARGES &amp; EXPENSES IF ANY AND ALL CHARGES OUTSIDE EGYPT TO BE BORNE BY THE DRAWEE. DO NOT WAIVE. IF THE DRAWEE REFUSED TO PAY CHARGES THE DOCUMENTS MUST NOT BE DELIVERED";&#xD;
	tw.local.results[1] = "IF YOU ARE NOT IN A POSITION ALLOWING YOU TO EXECUTE OUR INSTRUCTIONS PLEASE DO NOT DELIVER DOCUMENTS TO THE DRAWEE AND INFORM US PROMPTLY, PENDING RECEIPT OUR WRITTEN INSTRUCTIONS.";&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.results[0] = "PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF.";&#xD;
	tw.local.results[1] = "PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.";	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ad0eda60-3875-409a-887e-ee7a077e432e" targetRef="358814d6-186a-4437-8e31-aa400b3d1f71" name="To End" id="b6239c32-c26f-40e6-8174-47ea729ca063">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b6239c32-c26f-40e6-8174-47ea729ca063</processLinkId>
            <processId>1.ce54fd48-8513-44df-9dc5-530fe191c83f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ad0eda60-3875-409a-887e-ee7a077e432e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.358814d6-186a-4437-8e31-aa400b3d1f71</toProcessItemId>
            <guid>c993fafc-0884-4c0e-9ed5-768e76ad2c8f</guid>
            <versionId>07cdbab1-fdca-4884-82d2-4f08ab478c5e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ad0eda60-3875-409a-887e-ee7a077e432e</fromProcessItemId>
            <toProcessItemId>2025.358814d6-186a-4437-8e31-aa400b3d1f71</toProcessItemId>
        </link>
    </process>
</teamworks>

