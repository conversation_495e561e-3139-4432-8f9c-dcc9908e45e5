{"id": "64.3361b968-662d-491d-8e02-666b6b3648ec", "versionId": "5bc4218b-087f-424c-9ba1-0eb7aced65ca", "name": "Attachment 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "attach", "configOptions": ["canUpdate", "canCreate", "canDelete", "ECMProperties", "remittanceLetter<PERSON>ath", "updateProperties"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.add = function() {\r\r\n//\talert(\"adding\");\r\r\n////\tthis.context.binding.get(\"value\").set(\"name\", \"Others\");\r\r\n////\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n////\t\r\r\n//}\r\r\n//this.context.options.selectedAttach.get(\"value\").get(\"name\")\r\r\n\r\r\nthis.test2 = function(){\r\r\n\tthis.context.options.updateProperties.get(\"value\").get(0).set(\"name\", \"Bate5aaa\");\r\r\n\tconsole.log(this.context.options.updateProperties.get(\"value\").items)\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\nvar flag = false\r\r\nthis.makeflagtrue = function () {\r\r\n\tflag = true;\r\r\n\talert(\"makeing\");\r\r\n}\r\r\nthis.addAttacmentValidation=function () {\r\r\n\talert(flag);\r\r\n\tvar len = this.context.binding.get(\"value\").length();\r\r\n\tthis.context.binding.get(\"value\").get(len-1).set(\"name\", \"others\")\r\r\n//\t\r\r\n//\tthis.context.binding.get(\"value\").get(len).set(\"name\", \"Others\");\r\r\n//\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n\r\r\n\t\r\r\n}\r\r\nthis.visControl = function  () {\r\r\n\r\r\n\tif (this.context.options.visiable.get(\"value\")) {\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,false);\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}\r\r\nthis.setCMISquery = function  () {\r\r\n\tvar parent=this.context.options.parentPath;\r\r\n\tvar query =\"SELECT * FROM cmis:Document WHERE IN_TREE('\"+parent+\"')\";\r\r\n\tthis.context.options.cmisQuery.set(\"value\", query);\r\r\n}"}]}, "hasDetails": true}