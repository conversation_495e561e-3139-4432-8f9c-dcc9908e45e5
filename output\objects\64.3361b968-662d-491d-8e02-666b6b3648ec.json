{"id": "64.3361b968-662d-491d-8e02-666b6b3648ec", "versionId": "5bc4218b-087f-424c-9ba1-0eb7aced65ca", "name": "Attachment 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "attach", "configOptions": ["canUpdate", "canCreate", "canDelete", "ECMProperties", "remittanceLetter<PERSON>ath", "updateProperties"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.add = function() {\r\r\n//\talert(\"adding\");\r\r\n////\tthis.context.binding.get(\"value\").set(\"name\", \"Others\");\r\r\n////\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n////\t\r\r\n//}\r\r\n//this.context.options.selectedAttach.get(\"value\").get(\"name\")\r\r\n\r\r\nthis.test2 = function(){\r\r\n\tthis.context.options.updateProperties.get(\"value\").get(0).set(\"name\", \"Bate5aaa\");\r\r\n\tconsole.log(this.context.options.updateProperties.get(\"value\").items)\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\nvar flag = false\r\r\nthis.makeflagtrue = function () {\r\r\n\tflag = true;\r\r\n\talert(\"makeing\");\r\r\n}\r\r\nthis.addAttacmentValidation=function () {\r\r\n\talert(flag);\r\r\n\tvar len = this.context.binding.get(\"value\").length();\r\r\n\tthis.context.binding.get(\"value\").get(len-1).set(\"name\", \"others\")\r\r\n//\t\r\r\n//\tthis.context.binding.get(\"value\").get(len).set(\"name\", \"Others\");\r\r\n//\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n\r\r\n\t\r\r\n}\r\r\nthis.visControl = function  () {\r\r\n\r\r\n\tif (this.context.options.visiable.get(\"value\")) {\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,false);\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}\r\r\nthis.setCMISquery = function  () {\r\r\n\tvar parent=this.context.options.parentPath;\r\r\n\tvar query =\"SELECT * FROM cmis:Document WHERE IN_TREE('\"+parent+\"')\";\r\r\n\tthis.context.options.cmisQuery.set(\"value\", query);\r\r\n}"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.3361b968-662d-491d-8e02-666b6b3648ec", "name": "Attachment 2", "lastModified": "1730732899589", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isTemplate": "false", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>783eb8a0-73aa-4698-8a59-e50353bca554</ns2:id><ns2:layoutItemId>Panel1</ns2:layoutItemId><ns2:configData><ns2:id>8726f0bd-a851-46f9-86ec-4513711356ff</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Attachment</ns2:value></ns2:configData><ns2:configData><ns2:id>1fa72790-b486-43b4-81da-5db5e5c1b241</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>32fb414d-13dc-4d89-811e-b6a041e58646</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>7b983a25-b893-4b09-8bdc-45f18726ac08</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>36d1a8f6-3bbb-4d6d-87ce-580f9a9cb2d2</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>211f5cdf-b0c2-4985-8b9c-e594125859f2</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>//view.visControl()</ns2:value></ns2:configData><ns2:viewUUID>64.455e44ab-b77b-4337-b3f9-435e234fb569</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>1eda16db-f2da-41e4-8c4b-b320317e537f</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:CustomHTML\" version=\"8550\"><ns2:id>4dca52db-ef32-4131-83a4-160218a19aa8</ns2:id><ns2:layoutItemId>CustomHTML1</ns2:layoutItemId><ns2:configData><ns2:id>ab8d7dd3-b405-4c4e-82a8-219a29cdae9e</ns2:id><ns2:optionName>@customHTML.contentType</ns2:optionName><ns2:value>TEXT</ns2:value></ns2:configData><ns2:configData><ns2:id>34632d4e-2261-4b27-8f02-e9d2dfba4205</ns2:id><ns2:optionName>@customHTML.textContent</ns2:optionName><ns2:value>&lt;style&gt;\r\n.spark-ui {\r\n    background: rgb(255, 255, 255);\r\n   // width: 99%;\r\n}\r\n.CoachView.Panel&gt;.panel.SPARKPanel {\r\nborder-collapse: separate;\r\nborder-spacing: 0;\r\nbox-shadow: 0px 0px 12px #001B5929!important;\r\nborder-top: 5px solid #00643e!important;\r\nborder-radius: 10px!important;\r\n}\r\n\r\n.panel-primary.panel-dark&gt;.panel-heading {\r\nbackground: transparent !important;\r\nborder-color: transparent;\r\ncolor: #fff;\r\n}\r\n\r\n.panel-primary.panel-dark {\r\nborder-color: transparent!important;\r\n}\r\n.panel-primary.panel-dark &gt; .panel-heading .panel-title {\r\n    color: rgb(0, 101, 71);\r\n    font: normal normal 600 24px/45px Cairo;\r\n}\r\n\r\n.form-control {\r\n    border-top-color: rgb(205, 205, 205);\r\n    border-bottom-color: rgb(205,205,205);\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    padding: 12px 12px;\r\n    color: #181A1D !important;\r\n    background: #FFFFFF 0% 0% no-repeat padding-box;\r\n    border: 1px solid #ddd;\r\n    border-radius: 8px;\r\n}\r\n.SPARKWell .stat-cell .bg-icon {\r\n    line-height: normal;\r\n    height: 100%;\r\n    overflow: hidden;\r\n //   width: 97%;\r\n    border-radius: inherit;\r\n    box-shadow: 0px 0px 12px #001B5929!important;\r\n    margin-bottom: 6px;\r\n    border-radius: 10px!important;\r\n    border-top: 5px solid !important;\r\n   // margin-right: 49px;\r\n}\r\n\r\n//.form-control[disabled] {\r\n//    background: rgb(242 242 242);\r\n//}\r\n\r\n.bg-success {\r\n    background: #00654726 !important;\r\n}\r\n//.Single_Select select.placeHolder {\r\n//    color: #0002037a;\r\n//}\r\n\r\n.panel-group .panel-heading+.panel-collapse .panel-body {\r\n     border-top: 1px solid #fff;\r\n}\r\n\r\n.panel {\r\n\t     margin-bottom: 18px;\r\n\t     border-radius: 2px;\r\n\t     border-width: 0px;\r\n}\r\n\r\n.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {\r\n\t     background: #fff;\r\n\t     color: #006547;\r\n\t     border-color: #fff;\r\n\t     padding: 15px;\r\n\t     border-radius: 10px!important;\r\n}\r\n\r\n.CoachView.Collapsible_Panel&gt;.panel.SPARKCPanel {\r\n\t     border-collapse: collapse;\r\n\t     box-shadow: 0px 0px 22px #001B5929!important;\r\n}\r\n.SPARKCPanel &gt; .panel-heading {\r\n    padding: 0px;\r\n    border-top-left-radius: 1px;\r\n    border-top-right-radius: 1px;\r\n    box-shadow: 0px 0px 0px #001B5929!important;\r\n    border-top: 5px solid #00643e!important;\r\n    border-radius: 10px!important;\r\n    border-spacing: 0;\r\n    border-collapse: separate;\r\n}\r\n.panel-body {\r\n    background: #fff;\r\n    margin: 0;\r\n    padding-bottom: 15px;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    padding-top: 15px;\r\n    border-bottom-left-radius: 10px;\r\n    border-bottom-right-radius: 10px;\r\n}\r\n\r\n.panel-group .panel {\r\n    border-radius: 10px;\r\n}\r\n\r\n.Radio_Button_Group .radio3, .Radio_Button_Group .checkbox3{\r\n    width: 50%;\r\n}\r\n.radio3 &gt; input + span{\r\n    display: inline-flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n}\r\n//.input-group.no-border&gt;.input-group-addon {\r\n//    border-radius: 10px;\r\n//    min-width: 54px;\r\n//    height: 54px;\r\n//    top: -25px;\r\n//}\r\n\r\n//.form-group .input{\r\n//    padding-left:73px!important;\r\n//}\r\n//.Input_Group .outer, .control-label{\r\n//padding-left:73px;\r\n//}\r\n//.Single_Select, .control-label{\r\n//padding-left:20!important;\r\n//}\r\n.Input_Group .ContentBox {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    padding-left: 20px;\r\n}\r\n\r\n.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {\r\n    background: #fff;\r\n    color: #006547;\r\n    border-color: #fff;\r\n    border-radius: 10px!important;\r\n    font-size: 18px;\r\n    font-weight: 900;\r\n    padding: 15px;\r\n    font: normal normal 600 24px/45px Cairo;\r\n}\r\n\r\n.btn:not(.SPARKIcon), .btn:not(.SPARKIcon).btn-outline:not([role=\"img\"]):not(.SPARKIcon):focus, .btn:not(.SPARKIcon).btn-outline.active:not([role=\"img\"]), .btn:not(.SPARKIcon).btn-outline:not([role=\"img\"]):active {\r\n    padding: 12px 25px;\r\n}\r\n.btn-success, .btn-success:not([role=\"img\"]):focus {\r\n    color: #FFFFFF;\r\n    fill: #006547;\r\n    border-color: #006643;\r\n    border-bottom-color: #006643;\r\n    background: #006643;\r\n    background-image: linear-gradient(to bottom, #006643 0, #006643 100%) !important;\r\n    background-repeat: repeat-x;\r\n}\r\n\r\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{\r\nletter-spacing: 0px;\r\ncolor: #181A1D !important;\r\nopacity: 1;\r\nfont: normal normal normal 16px/20px Cairo;\r\nbackground: transparent;\r\nfont-weight: bold;\r\nborder: 1px solid #000203;\r\n}\r\n//.Single_Select&gt;.form-group&gt;.input&gt;select {\r\n//\r\n//    border-top-style: ridge;\r\n//    border-bottom-style: outset;\r\n//    border-right-style: outset;\r\n//    border-left-style: ridge;\r\n//    border-top-width: revert;\r\n//    border-left-width: revert;\r\n//    border-bottom-width: revert;\r\n//    border-right-width: revert;\r\n//}\r\nselect.input-sm, .input-sm.form-control, .input-sm.form-control[type='text'] {\r\n    height: 40px;\r\n    line-height: 1.33;\r\n}\r\n\r\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {\r\n    letter-spacing: 0px;\r\n    color: #181A1D !important;\r\n    opacity: 1;\r\n    font: 14px Cairo;\r\n    background: transparent;\r\n    font-weight: lighter;\r\n}\r\n\r\n\r\n.btn-success:not([role=\"img\"]):hover {\r\n    color: #ffffff;\r\n    fill: #3D8A70;\r\n    border-color: #3D8A70;\r\n    border-bottom-color: #429c42;\r\n    background: #3D8A70;\r\n    background-image: -webkit-linear-gradient(top, #3D8A70 0, #3D8A70 100%) !important;\r\n    background-image: linear-gradient(to bottom, #3D8A70 0, #3D8A70 100%) !important;\r\n    background-repeat: repeat-x;\r\n}\r\n\r\n.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle:hover {\r\n    background: rgb(255 255 255 / 15%);\r\n}\r\n.panel-group .SPARKTable &gt; .panel-heading {\r\n    border-color: rgb(240, 240, 240);\r\n    border-bottom-width: 2px\r\n;\r\n    border-bottom-style: solid;\r\n    background: #006643 0% 0% no-repeat padding-box;\r\n    color: white;\r\n}\r\n.Output_Text&gt;.form-group&gt;.input&gt;p {\r\n  \r\n    padding-right: 1em;\r\n \r\n}\r\n\r\n\r\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {\r\n   \r\n    font-weight: 600;\r\n}\r\n\r\n\r\n\r\n\r\n[class*=\"BPM_Resp_\"] .bpm-label-default {\r\n height: 30px;\r\n    font: normal normal normal 16px/20px Cairo !important;\r\n    letter-spacing: 0px;\r\n    opacity: 1;\r\n    color: #8B8C8E;\r\n}\r\n\r\n\r\n\r\n\r\n//.ECMPropertiesContainer{\r\n//display: none; \r\n//}\r\n\r\n\r\n.Output_Text&gt;.form-group&gt;.input&gt;p{\r\nunicode-bidi: plaintext ;\r\ntext-align: inherit;\r\n}\r\n\r\n.CoachViewRTL {\r\n \r\n    text-align: right !important;\r\n}\r\n\r\n.CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-RightAlign&gt;*, .CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-CenterAlign&gt;* {\r\n    text-align: right;\r\n}\r\n\r\n.noIScroll.alignJustify {\r\n    text-align: inherit;\r\n}\r\n.radio3 &gt; input + span {\r\n\r\n    unicode-bidi: plaintext;\r\n\r\n}\r\n\r\n.radio3 &gt; input:checked + span::after {\r\n \r\n  top: calc(50% - 3px) !important;\r\n}\r\n.switcher-primary .switcher-state-off {\r\n    color: #000203;\r\n}\r\n.CoachViewRTL .Single_Select &gt;{\r\n unicode-bidi: plaintext;\r\n\r\n}\r\n\r\ninput[type=\"checkbox\"]:checked[disabled] {\r\n    right: 6%;\r\n    opacity: 1;\r\n    width: fit-content;\r\n    left: 3px;\r\n}\r\n\r\n.Tab_Section&gt;div&gt;.nav-tabs-mnu {\r\n    position: unset;\r\n   }\r\n   \r\na {\r\n    color: #333;\r\n    text-decoration: none;\r\n}\r\n.datepicker thead th {\r\n   \r\n    color: #006547;\r\n&lt;/style&gt;</ns2:value></ns2:configData></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>9e13d333-1f7a-422b-8d3d-d9cf288a45a8</ns2:id><ns2:layoutItemId>Single_Select1</ns2:layoutItemId><ns2:configData><ns2:id>d703b27c-9386-445e-8f47-91e93e1ab2d8</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Single select</ns2:value></ns2:configData><ns2:configData><ns2:id>44276f2d-9566-4c28-8bbb-c75f4ee0db58</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>74376610-e2a8-4d48-89ec-2a560f36575c</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>c5558739-5c0f-4aa0-8984-ca18ac4436b7</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.test2()</ns2:value></ns2:configData><ns2:configData><ns2:id>532e2e42-1c5a-4dee-8b10-8ebc7945fe82</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>L</ns2:value></ns2:configData><ns2:configData><ns2:id>aca7247a-78a3-47eb-8cc7-1a80fcbc2f9d</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value>[{\"name\":\"y\",\"value\":\"y\"},{\"name\":\"n\",\"value\":\"n\"}]</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.options.remittanceLetterPath</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>e96194ee-98df-475a-8e61-e19fcf7ccdd3</ns2:id><ns2:layoutItemId>ECM_Document_List1</ns2:layoutItemId><ns2:configData><ns2:id>a349c3a4-415d-4e5a-8416-058dd0e2e526</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Attachment List</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>fa9a1d3c-7816-4ca4-84a5-192cb1b9edba</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2ae3111f-e825-4ae6-8888-f53445f0f9ea</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>fa29e0a9-42c0-4d07-8d31-9e4bd49fdc16</ns2:id><ns2:optionName>allowCreate</ns2:optionName><ns2:value>tw.options.canCreate</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ba3c7de7-cee3-4140-8be9-40beb845b6ba</ns2:id><ns2:optionName>allowUpdate</ns2:optionName><ns2:value>tw.options.canUpdate</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>d848278d-788d-4115-8c91-b438224ee3cc</ns2:id><ns2:optionName>ecmServerConfigurationName</ns2:optionName><ns2:value>FileNet</ns2:value></ns2:configData><ns2:configData><ns2:id>87fd5632-a8df-461a-86d7-b013d28a70b9</ns2:id><ns2:optionName>documentObjectTypeId</ns2:optionName><ns2:value>ODCDocuments</ns2:value></ns2:configData><ns2:configData><ns2:id>ab6ceebe-f94d-4d01-852d-7a8e3e242fa4</ns2:id><ns2:optionName>folderPath</ns2:optionName><ns2:value>tw.options.ECMProperties.fullPath</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>c15c1358-741a-4ff4-89bf-f139bdbc53c0</ns2:id><ns2:optionName>tableStyle</ns2:optionName><ns2:value>H</ns2:value></ns2:configData><ns2:configData><ns2:id>5785a387-f42e-4a3c-8c32-75dfaba9f027</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>8aa4ea31-58d4-42b5-878b-5beb6abc9a2b</ns2:id><ns2:optionName>@className</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2d5015c2-6e98-4ac1-8fed-4118d84d0ef2</ns2:id><ns2:optionName>@htmlOverrides</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>67a64e37-e517-41ca-89a1-05b6400349f9</ns2:id><ns2:optionName>eventON_FILECLICKED</ns2:optionName><ns2:value>${File_Viewer1}.setUrl(doc.url);</ns2:value></ns2:configData><ns2:configData><ns2:id>437b516d-b6be-4fc7-8ecf-0e96f98c9fe3</ns2:id><ns2:optionName>allowDelete</ns2:optionName><ns2:value>tw.options.canDelete</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>6565f717-0124-4259-8447-0c0020a7448b</ns2:id><ns2:optionName>defaultProperties</ns2:optionName><ns2:value>tw.options.ECMProperties.defaultProperties[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>58c60e58-f244-4cc5-8b26-18038bcccfa5</ns2:id><ns2:optionName>cmisQuery</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>1aaf13e4-fd41-4c82-8c6c-f6f75836b072</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"EDITABLE\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.c752c898-7a35-4c96-857a-feed3f59aeee</ns2:viewUUID><ns2:binding></ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>cffc8860-0460-4d0a-8ed9-404a89e7c0c6</ns2:id><ns2:layoutItemId>ECM_Document_List2</ns2:layoutItemId><ns2:configData><ns2:id>d6329f62-bf6c-49c1-815e-64103a1015c8</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Remittance Letter List</ns2:value></ns2:configData><ns2:configData><ns2:id>970e9398-dceb-44e3-80de-6076ef2dac86</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>e9c536ec-93be-4d2a-84e3-4d5d2e3f0ed2</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>4b35744a-0f73-4cf6-8475-fee0d826f7c6</ns2:id><ns2:optionName>ecmServerConfigurationName</ns2:optionName><ns2:value>FileNet</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ec40a5eb-17c8-422c-85f0-25dc637e50c2</ns2:id><ns2:optionName>folderPath</ns2:optionName><ns2:value>tw.options.remittanceLetterPath</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>6cdb5422-f037-4077-837a-537f9b54f090</ns2:id><ns2:optionName>eventON_FILECLICKED</ns2:optionName><ns2:value>${File_Viewer1}.setUrl(doc.url);</ns2:value></ns2:configData><ns2:configData><ns2:id>9956f0eb-9ff6-4328-8abf-57b81fb698b5</ns2:id><ns2:optionName>defaultProperties</ns2:optionName><ns2:value>tw.options.ECMProperties.defaultProperties[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>2f563413-7563-4d5d-80ab-b655372e8465</ns2:id><ns2:optionName>pageSize</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.c752c898-7a35-4c96-857a-feed3f59aeee</ns2:viewUUID></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>17704ab8-2ea1-4e01-8c68-171e412d3116</ns2:id><ns2:layoutItemId>File_Viewer1</ns2:layoutItemId><ns2:configData><ns2:id>0c2eb382-272b-4cd4-86f4-a7dbaafe9e96</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>File Viewer</ns2:value></ns2:configData><ns2:configData><ns2:id>579f8c77-b64f-4a96-8ab2-7b96ba85064c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>a7a4da6f-f07b-49da-8079-ba0f61c5cd83</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>75bd4e64-0ecc-443a-81ce-87b5f11846bc</ns2:id><ns2:optionName>fitWidth</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>3e0054dd-bb58-48e8-850d-6cc0c7dc5733</ns2:id><ns2:optionName>fitHeight</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>601e6485-2ce8-4d16-8cdb-d7c0acf0fc59</ns2:id><ns2:optionName>ctrlsPlacement</ns2:optionName><ns2:value>N</ns2:value></ns2:configData><ns2:configData><ns2:id>38c7d603-7b35-4b90-8151-d44594c9e51d</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>b4204483-1c6b-4079-8315-2c768950060c</ns2:id><ns2:optionName>height</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.1bd959ad-3c75-48c9-975b-8f234d664180</ns2:viewUUID><ns2:binding></ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>448429b2-6e10-4489-83ba-f7f2137bbf5d</ns2:id><ns2:layoutItemId>Table</ns2:layoutItemId><ns2:configData><ns2:id>89b1b092-9965-4c07-89e9-27f23a8dd894</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Document Validation</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>7e30035a-0303-448f-83d9-607df6ee3f6c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>01b5cd69-73fb-4909-8b24-44f2a77fd5bf</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>efa22bc5-cc6e-4bf8-8fd1-58f824bc556e</ns2:id><ns2:optionName>eventON_ADDREC</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>7b38f561-cae9-4012-8f51-b83dfd829d30</ns2:id><ns2:optionName>selectionMode</ns2:optionName><ns2:value>M</ns2:value></ns2:configData><ns2:configData><ns2:id>6b628fdd-d07f-41bb-8e55-9ecac2a86599</ns2:id><ns2:optionName>showAddButton</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>0382e5c1-990e-465e-8b44-3323e36078e2</ns2:id><ns2:optionName>eventON_ROWSLOADED</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c215e61f-e423-4d51-8bad-699bdac3b9aa</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>ceee38f3-c0f5-459b-8219-9e78a2721ea1</ns2:id><ns2:optionName>eventON_NEWCELL</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>5a334b29-7be8-4b85-80d1-d342ac8626af</ns2:id><ns2:optionName>eventON_DELREC</ns2:optionName><ns2:value>if(item.name !=\"Others\"){\r\nalert(\"You can not delete this row\");\r\nreturn false;\r\n}</ns2:value></ns2:configData><ns2:configData><ns2:id>21019cee-c120-42e9-8da8-b65bdf55e5a7</ns2:id><ns2:optionName>showDeleteButton</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>d894129c-6b13-4117-861c-596bc46e3352</ns2:id><ns2:optionName>tableStyle</ns2:optionName><ns2:value>H</ns2:value></ns2:configData><ns2:configData><ns2:id>1a39cfb0-b43f-40d4-83bb-ad5bee2276b3</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>11c4ae8e-a2e9-4d47-8bb0-3a3141288a90</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>0e8d023b-a797-4c8c-86bd-4c1e285699b1</ns2:id><ns2:optionName>eventON_ALLROWS</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>9a188e69-47ea-4feb-88ff-a78a82e271bb</ns2:id><ns2:optionName>eventON_ROWSEL</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.f515b79f-fe61-4bd3-8e26-72f00155d139</ns2:viewUUID><ns2:binding>tw.businessData.attach[]</ns2:binding><ns2:contentBoxContrib><ns2:id>d63a21c3-1a8e-4318-831d-088bdd93dbd3</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>dfa2c52f-d43b-4367-873a-8879346441ae</ns2:id><ns2:layoutItemId>docmentTypeName</ns2:layoutItemId><ns2:configData><ns2:id>049334d6-f8eb-4219-a69a-155d62bda42c</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>e7ffadc9-f0a3-4a46-8f82-7e71a9ce12ed</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>English Name</ns2:value></ns2:configData><ns2:configData><ns2:id>f6aa12e1-dd10-4904-87e1-0f184ed92e68</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>060e1bde-1fef-4eae-876e-0edd40ae4650</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>HIDE</ns2:value></ns2:configData><ns2:configData><ns2:id>65f38cf1-01af-417a-8948-289a2004aa11</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>d55e0dfd-27f7-4f78-85e0-d5ce75a1dc8f</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID><ns2:binding>tw.businessData.attach.currentItem.name</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>5ea5a02b-38c5-4e6b-8212-62c882808d70</ns2:id><ns2:layoutItemId>arabicName</ns2:layoutItemId><ns2:configData><ns2:id>0fcbc91e-4e81-4387-866e-d5fba0d5209e</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Arabic Name</ns2:value></ns2:configData><ns2:configData><ns2:id>17f0a702-6e23-45b2-8ed5-a8ee6afe7e14</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>9bd6c3ff-50db-4f43-88a6-a0671e14956c</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>HIDE</ns2:value></ns2:configData><ns2:configData><ns2:id>b8955721-5b36-4a50-89cf-2c94b35eae83</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID><ns2:binding>tw.businessData.attach.currentItem.arabicName</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>508dca81-0fb4-4776-8974-f92c58cb253c</ns2:id><ns2:layoutItemId>documentTypeDescription</ns2:layoutItemId><ns2:configData><ns2:id>d95d3dff-0b4d-4e2d-9d50-f9f62a93ca05</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>3824e553-3be4-40b6-8673-71cb51ef8718</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Description</ns2:value></ns2:configData><ns2:configData><ns2:id>de0e92d3-9dd7-4424-833f-2361c152655f</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>747fbc8c-b37f-440c-8d02-e56fa2303cec</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>HIDE</ns2:value></ns2:configData><ns2:configData><ns2:id>3d0615ca-302f-464b-87c1-8cb5f31fb129</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>0d97e532-96d1-476c-8e32-ee4aaca53508</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>//if(newText != oldText){return confirm (\"Are you sure you want to change the text field?\");}\r\n//if(oldText!=\"\"){\r\n//alert(\"test\")\r\n//\tnewText=oldText;\r\n//}\r\n//alert(oldText!=\"\");\r\n\r\n//${documentTypeDescription[0]}.setText(\"test\");</ns2:value></ns2:configData><ns2:configData><ns2:id>358373cb-49ed-4cb7-81cc-e6fbd11c6932</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>409b1a10-e09c-40bd-818b-68a3e446a1ea</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>\r\nif(me.getText()!=\"\"){me.setEnabled(false);}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.attach.currentItem.description</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>f2556963-52e5-46dc-86af-910c0e1574f4</ns2:id><ns2:layoutItemId>Integer2</ns2:layoutItemId><ns2:configData><ns2:id>80e3c150-f2c7-4cf8-8f19-6e7acaabcaee</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Originals</ns2:value></ns2:configData><ns2:configData><ns2:id>cbffec9c-d781-4dad-82f1-ffddf254a9ae</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>e7a31dce-92f6-470e-8162-5148d7d01b28</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.a6946c4c-f73d-4ced-9216-90018985ca96</ns2:viewUUID><ns2:binding>tw.businessData.attach.currentItem.numOfOriginals</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>3b06dce4-7be7-40a8-8517-c8f8a2cbc822</ns2:id><ns2:layoutItemId>Integer1</ns2:layoutItemId><ns2:configData><ns2:id>ad0e35d5-560d-4cb3-8fc4-35a9994c99c1</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Copies</ns2:value></ns2:configData><ns2:configData><ns2:id>4d9f4bf6-9afa-429c-8156-430c01ee2394</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>866b1bad-c71b-4a81-8d59-1598cefefb09</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.a6946c4c-f73d-4ced-9216-90018985ca96</ns2:viewUUID><ns2:binding>tw.businessData.attach.currentItem.numOfCopies</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>4db533fd-2687-45da-8f0f-7308a936a0c1</ns2:id><ns2:layoutItemId>Button1</ns2:layoutItemId><ns2:configData><ns2:id>ea745958-30ce-48e7-8868-2105d6024d7d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Add Other Document</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>724171f8-cd9f-44d3-859c-430d43ab238f</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>f19823af-39b8-4717-8ef1-3bed8135090c</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>5e620287-f282-4a2c-8df4-1c62a7018d90</ns2:id><ns2:optionName>eventON_CLICK</ns2:optionName><ns2:value>\r\n${Table}.appendElement({\"name\":\"Others\", \"arabicName\":\"اخرى\", \"description\":\"\"});\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>2f841f73-6539-4795-8c0f-294d2cf5d34b</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>14038dd8-5adf-4d4c-87dd-1cb2e42f4352</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID></ns2:contributions></ns2:contentBoxContrib></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "true", "loadJsFunction": {"isNull": "true"}, "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": {"isNull": "true"}, "changeJsFunction": {"isNull": "true"}, "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "3bdccbeb-ea7e-49cb-9f1b-7b92c93382d0", "versionId": "5bc4218b-087f-424c-9ba1-0eb7aced65ca", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "bindingType": {"name": "attach", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewBindingTypeId": "65.26cd06b4-b3da-4a0b-a1bb-64d7e0864cc4", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isList": "true", "classId": "/12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "seq": "0", "description": {"isNull": "true"}, "guid": "60424403-8b39-4fe8-8de7-7b0cd3188e22", "versionId": "87233c3a-10e0-4a46-8999-eb85fe03f574"}, "configOption": [{"name": "canUpdate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.265144f6-8102-4362-b81a-b53c82965278", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "0", "description": "", "groupName": "", "guid": "dbef6596-545c-4c44-a2a1-fc84a2e487ab", "versionId": "9c59d8eb-0370-4d4e-b05d-7c1688635171"}, {"name": "canCreate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.15dae8c3-dd14-4d46-b43c-6a4734413937", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "1", "description": "", "groupName": "", "guid": "acf7e92d-26de-4839-b73f-ed11703535b1", "versionId": "ba210ce2-53b2-4e90-a2c1-ace0de581457"}, {"name": "canDelete", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.f79d5eeb-9c8d-43ab-8c60-448940b62a78", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "2", "description": "", "groupName": "", "guid": "a123cbf2-5224-4b49-a084-d94feda4e831", "versionId": "cbc4df57-c14c-4899-a83c-7d78e4792adc"}, {"name": "ECMProperties", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.71ab1713-8be4-4ad1-93fa-722b88df881f", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "/12.b698dbfb-84da-40a5-9db3-676815055e65", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "3", "description": "", "groupName": "", "guid": "64b6df05-0d0c-4ce2-8ba8-0e2aa108be13", "versionId": "8a1f8c4d-2ad3-4434-a227-7faac9c3dd1d"}, {"name": "remittanceLetter<PERSON>ath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.73f8d932-44b1-466e-aa5d-a192a8036f48", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "4", "description": "", "groupName": "", "guid": "98dc4b8b-9c65-46ea-aaf5-2f82b536a696", "versionId": "aba6fe8b-5788-4bdb-b253-8981bb843bd8"}, {"name": "updateProperties", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.20041aeb-10ec-49de-9373-96b13933358d", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "5", "description": "", "groupName": "", "guid": "31940ba5-870d-4505-aac7-ffe7a11c45c7", "versionId": "efde5be8-dcc8-4374-874b-54b5c989fb62"}], "inlineScript": {"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.8f9c5b99-0e27-4e5c-98b9-6f6eb17eb2f3", "coachViewId": "64.3361b968-662d-491d-8e02-666b6b3648ec", "scriptType": "JS", "scriptBlock": "//this.add = function() {\r\r\n//\talert(\"adding\");\r\r\n////\tthis.context.binding.get(\"value\").set(\"name\", \"Others\");\r\r\n////\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n////\t\r\r\n//}\r\r\n//this.context.options.selectedAttach.get(\"value\").get(\"name\")\r\r\n\r\r\nthis.test2 = function(){\r\r\n\tthis.context.options.updateProperties.get(\"value\").get(0).set(\"name\", \"Bate5aaa\");\r\r\n\tconsole.log(this.context.options.updateProperties.get(\"value\").items)\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\nvar flag = false\r\r\nthis.makeflagtrue = function () {\r\r\n\tflag = true;\r\r\n\talert(\"makeing\");\r\r\n}\r\r\nthis.addAttacmentValidation=function () {\r\r\n\talert(flag);\r\r\n\tvar len = this.context.binding.get(\"value\").length();\r\r\n\tthis.context.binding.get(\"value\").get(len-1).set(\"name\", \"others\")\r\r\n//\t\r\r\n//\tthis.context.binding.get(\"value\").get(len).set(\"name\", \"Others\");\r\r\n//\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n\r\r\n\t\r\r\n}\r\r\nthis.visControl = function  () {\r\r\n\r\r\n\tif (this.context.options.visiable.get(\"value\")) {\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,false);\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}\r\r\nthis.setCMISquery = function  () {\r\r\n\tvar parent=this.context.options.parentPath;\r\r\n\tvar query =\"SELECT * FROM cmis:Document WHERE IN_TREE('\"+parent+\"')\";\r\r\n\tthis.context.options.cmisQuery.set(\"value\", query);\r\r\n}", "seq": "0", "description": "", "guid": "ea2e0e44-4bf3-477b-aa59-ed52ece17efe", "versionId": "c96c6f3f-9442-4ce9-a56e-e3881d6e83d3"}}}}, "hasDetails": true}