<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8" name="Test Date">
        <lastModified>1740336633439</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.288bfafe-8fb2-4942-8545-daa4e775273b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-768b</guid>
        <versionId>ef91abe5-1a3c-439b-88b8-8efbc4c61758</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:faf129f3cc554db6:5a40ed45:195332c2764:-3b3f" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f28d5e9a-7328-4e66-8365-9145c615272c"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":105,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"65c14556-75a5-4b76-8842-cf701d236581"},{"incoming":["8a8a523a-a85f-405e-8047-2f089e63a066","6bb7ccf3-0755-4917-8285-ab7cfb79ac5d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-7689"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"103b0b4a-d192-4db9-8d39-6b99b2b220dc"},{"targetRef":"288bfafe-8fb2-4942-8545-daa4e775273b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.f28d5e9a-7328-4e66-8365-9145c615272c","sourceRef":"65c14556-75a5-4b76-8842-cf701d236581"},{"startQuantity":1,"outgoing":["6bb7ccf3-0755-4917-8285-ab7cfb79ac5d"],"incoming":["2027.f28d5e9a-7328-4e66-8365-9145c615272c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":301,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"288bfafe-8fb2-4942-8545-daa4e775273b","scriptFormat":"text\/x-javascript","script":{"content":["\/\/tw.local.output1 = convertToUTCDate( tw.local.input1 );\r\n\/\/tw.local.output2  = tw.new.test.mother;\r\n\/\/tw.local.  TWService.Types.ServiceFlow.length\r\n\r\ntw.system.executeServiceByName(\"Create\");"]}},{"targetRef":"103b0b4a-d192-4db9-8d39-6b99b2b220dc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"6bb7ccf3-0755-4917-8285-ab7cfb79ac5d","sourceRef":"288bfafe-8fb2-4942-8545-daa4e775273b"},{"startQuantity":1,"outgoing":["8a8a523a-a85f-405e-8047-2f089e63a066"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":469,"y":117,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Test Child Error","dataInputAssociation":[{"targetRef":"2055.094f9338-8841-4f14-80be-d92350c25b90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a9f667e9-9a2e-47e4-8d46-6dda48e774e3","calledElement":"1.5d77055c-98a8-4191-9b74-c7120a5823be"},{"targetRef":"103b0b4a-d192-4db9-8d39-6b99b2b220dc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:-2aa4"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8a8a523a-a85f-405e-8047-2f089e63a066","sourceRef":"a9f667e9-9a2e-47e4-8d46-6dda48e774e3"},{"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4ed43a7e-**************-ef118b827dab","otherAttributes":{"eventImplId":"7c2226f7-22a2-4b49-837b-33ac28bd0930"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":640,"y":160,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.input1"]}}]}],"declaredType":"endEvent","id":"cce65dfe-2b1b-4ead-8dc1-f7ae9b6c673b"}],"laneSet":[{"id":"c2be9559-0df4-4651-8202-08ba26986972","lane":[{"flowNodeRef":["65c14556-75a5-4b76-8842-cf701d236581","103b0b4a-d192-4db9-8d39-6b99b2b220dc","288bfafe-8fb2-4942-8545-daa4e775273b","a9f667e9-9a2e-47e4-8d46-6dda48e774e3","cce65dfe-2b1b-4ead-8dc1-f7ae9b6c673b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5219c1cd-6cc3-4230-85c3-62b6ab77d144","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Test Date","declaredType":"process","id":"1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"output1","isCollection":false,"id":"2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812"},{"itemSubjectRef":"itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6","name":"output2","isCollection":true,"id":"2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0"}],"inputSet":[{"dataInputRefs":["2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5"]}],"outputSet":[{"dataOutputRefs":["2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9","2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812","2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"2024-11-24\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"input1","isCollection":false,"id":"2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="input1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5</processParameterId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"2024-11-24"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>70fe15fd-e992-4411-8237-a8e116f1cecb</guid>
            <versionId>627d4e92-79b1-4b0b-ab19-d6e871595da7</versionId>
        </processParameter>
        <processParameter name="output1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9</processParameterId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>58698208-a2a8-4aa6-81c3-dc4423119b71</guid>
            <versionId>61f9999d-748a-4aa4-aaba-e3b88a6ff28b</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812</processParameterId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>279e7227-72ca-486c-bc8a-a74d9fef7e78</guid>
            <versionId>9215db4a-b461-499f-9a98-877a8f918f4a</versionId>
        </processParameter>
        <processParameter name="output2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0</processParameterId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.90d4772d-4081-4a73-a8c2-e7f904511cd6</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5efd38e1-50e4-4407-8bd0-33676639ee7c</guid>
            <versionId>8f2c54c4-f9b6-4810-88da-54ce2519d725</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8375bc3d-a5d9-46a0-8395-d175745ed292</processParameterId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e3c62666-d884-441f-ae14-d8b5c5b9f367</guid>
            <versionId>e321567c-c7f4-4993-a6cb-dbee30086c00</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.288bfafe-8fb2-4942-8545-daa4e775273b</processItemId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.fb7d14e9-68ee-478a-b24f-f3185501948c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-7683</guid>
            <versionId>1bc4dda5-53ce-4b9b-8188-a23a4249606a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="301" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.fb7d14e9-68ee-478a-b24f-f3185501948c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//tw.local.output1 = convertToUTCDate( tw.local.input1 );&#xD;
//tw.local.output2  = tw.new.test.mother;&#xD;
//tw.local.  TWService.Types.ServiceFlow.length&#xD;
&#xD;
tw.system.executeServiceByName("Create");</script>
                <isRule>false</isRule>
                <guid>f02f692a-3804-4fa5-913d-b725a13ce169</guid>
                <versionId>60edab57-67e7-42e8-ac9e-5f5b96c47a5b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cce65dfe-2b1b-4ead-8dc1-f7ae9b6c673b</processItemId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.9c52d51d-0202-4ca8-ab8f-23b0f04fcf1b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:faf129f3cc554db6:5a40ed45:195332c2764:-5572</guid>
            <versionId>4cfe34a6-cb39-41de-a3d2-281061a6e147</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="640" y="160">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.9c52d51d-0202-4ca8-ab8f-23b0f04fcf1b</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>4b396d67-ccdc-42fd-98af-f9e54d91e2a0</guid>
                <versionId>b1b4b885-fe23-4e82-ad26-09fdd2e19ee3</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4a040631-9280-4d79-a16a-b720f61010ca</parameterMappingId>
                    <processParameterId>2055.8375bc3d-a5d9-46a0-8395-d175745ed292</processParameterId>
                    <parameterMappingParentId>3007.9c52d51d-0202-4ca8-ab8f-23b0f04fcf1b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.input1</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>36440ade-830a-499c-961a-0cd9ab4a9673</guid>
                    <versionId>8a06f2ce-70da-4d7b-ac70-8dbd9c5f1a3f</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a9f667e9-9a2e-47e4-8d46-6dda48e774e3</processItemId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <name>Test Child Error</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.06d0d6e5-0c1e-4c5a-a295-d63508353301</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f7f6553919972802:-74de7c2c:193f7b0288a:-6012</guid>
            <versionId>9a19d7e9-ac84-4653-8ca1-780865e1ad0a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="469" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.06d0d6e5-0c1e-4c5a-a295-d63508353301</subProcessId>
                <attachedProcessRef>/1.5d77055c-98a8-4191-9b74-c7120a5823be</attachedProcessRef>
                <guid>0226c081-1924-406f-bc68-16ff64e0629b</guid>
                <versionId>926abc02-5696-4594-add1-6b41d787ffc9</versionId>
                <parameterMapping name="Path">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.35fcd98c-a947-4ab3-88a6-25b6d6085f65</parameterMappingId>
                    <processParameterId>2055.094f9338-8841-4f14-80be-d92350c25b90</processParameterId>
                    <parameterMappingParentId>3012.06d0d6e5-0c1e-4c5a-a295-d63508353301</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>""</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ab936c54-d0c8-42ac-bb7a-f99a6255d362</guid>
                    <versionId>55f75eb5-bd48-41bc-90f7-e241fc6fbf00</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e1359b38-d47c-450d-8349-ba4e89c1a686</parameterMappingId>
                    <processParameterId>2055.9ff5ab2a-ddd9-4338-81b2-f5b9b9237fa8</processParameterId>
                    <parameterMappingParentId>3012.06d0d6e5-0c1e-4c5a-a295-d63508353301</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>739f52cf-4ae0-4b17-be58-f3fa2727e45d</guid>
                    <versionId>971210bb-fa86-4a96-ac2e-7d2057c60c56</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="FolderID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.09f86f07-9f73-4d86-ab8c-74b69b811deb</parameterMappingId>
                    <processParameterId>2055.41d4d7e4-dd09-4686-87cc-5816b6aaa1f8</processParameterId>
                    <parameterMappingParentId>3012.06d0d6e5-0c1e-4c5a-a295-d63508353301</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>90112262-13ab-4b2a-9d52-613758a52420</guid>
                    <versionId>9ced1384-b3e4-48a1-a24a-47ced3f0e492</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.103b0b4a-d192-4db9-8d39-6b99b2b220dc</processItemId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.03e1e9de-c635-4915-93f1-2ed872724c92</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-7689</guid>
            <versionId>e06288aa-4b01-4e19-8ecd-38728cb011d9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.03e1e9de-c635-4915-93f1-2ed872724c92</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>e3643268-6448-481e-bb0f-2fa6207f1adb</guid>
                <versionId>6acddf6f-0179-4754-b847-a81335e46618</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.288bfafe-8fb2-4942-8545-daa4e775273b</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="105" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="Test Date" id="1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:isSecured>true</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:dataInput name="input1" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">"2024-11-24"</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="output1" itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" id="2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9" />
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812" />
                        <ns16:dataOutput name="output2" itemSubjectRef="itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6" isCollection="true" id="2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0" />
                        <ns16:inputSet>
                            <ns16:dataInputRefs>2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5</ns16:dataInputRefs>
                        </ns16:inputSet>
                        <ns16:outputSet>
                            <ns16:dataOutputRefs>2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9</ns16:dataOutputRefs>
                            <ns16:dataOutputRefs>2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812</ns16:dataOutputRefs>
                            <ns16:dataOutputRefs>2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0</ns16:dataOutputRefs>
                        </ns16:outputSet>
                    </ns16:ioSpecification>
                    <ns16:laneSet id="c2be9559-0df4-4651-8202-08ba26986972">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5219c1cd-6cc3-4230-85c3-62b6ab77d144" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>65c14556-75a5-4b76-8842-cf701d236581</ns16:flowNodeRef>
                            <ns16:flowNodeRef>103b0b4a-d192-4db9-8d39-6b99b2b220dc</ns16:flowNodeRef>
                            <ns16:flowNodeRef>288bfafe-8fb2-4942-8545-daa4e775273b</ns16:flowNodeRef>
                            <ns16:flowNodeRef>a9f667e9-9a2e-47e4-8d46-6dda48e774e3</ns16:flowNodeRef>
                            <ns16:flowNodeRef>cce65dfe-2b1b-4ead-8dc1-f7ae9b6c673b</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="65c14556-75a5-4b76-8842-cf701d236581">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="105" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.f28d5e9a-7328-4e66-8365-9145c615272c</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:endEvent name="End" id="103b0b4a-d192-4db9-8d39-6b99b2b220dc">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            <ns3:endStateId>guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-7689</ns3:endStateId>
                        </ns16:extensionElements>
                        <ns16:incoming>8a8a523a-a85f-405e-8047-2f089e63a066</ns16:incoming>
                        <ns16:incoming>6bb7ccf3-0755-4917-8285-ab7cfb79ac5d</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="65c14556-75a5-4b76-8842-cf701d236581" targetRef="288bfafe-8fb2-4942-8545-daa4e775273b" name="To Script Task" id="2027.f28d5e9a-7328-4e66-8365-9145c615272c">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="288bfafe-8fb2-4942-8545-daa4e775273b">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="301" y="57" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.f28d5e9a-7328-4e66-8365-9145c615272c</ns16:incoming>
                        <ns16:outgoing>6bb7ccf3-0755-4917-8285-ab7cfb79ac5d</ns16:outgoing>
                        <ns16:script>//tw.local.output1 = convertToUTCDate( tw.local.input1 );&#xD;
//tw.local.output2  = tw.new.test.mother;&#xD;
//tw.local.  TWService.Types.ServiceFlow.length&#xD;
&#xD;
tw.system.executeServiceByName("Create");</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="288bfafe-8fb2-4942-8545-daa4e775273b" targetRef="103b0b4a-d192-4db9-8d39-6b99b2b220dc" name="To End" id="6bb7ccf3-0755-4917-8285-ab7cfb79ac5d">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:callActivity calledElement="1.5d77055c-98a8-4191-9b74-c7120a5823be" name="Test Child Error" id="a9f667e9-9a2e-47e4-8d46-6dda48e774e3">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="469" y="117" width="95" height="70" />
                            <ns4:activityType>CalledProcess</ns4:activityType>
                        </ns16:extensionElements>
                        <ns16:outgoing>8a8a523a-a85f-405e-8047-2f089e63a066</ns16:outgoing>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.094f9338-8841-4f14-80be-d92350c25b90</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">""</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                    </ns16:callActivity>
                    <ns16:sequenceFlow sourceRef="a9f667e9-9a2e-47e4-8d46-6dda48e774e3" targetRef="103b0b4a-d192-4db9-8d39-6b99b2b220dc" name="To End" id="8a8a523a-a85f-405e-8047-2f089e63a066">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-2aa4</ns3:endStateId>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:endEvent name="End Event" id="cce65dfe-2b1b-4ead-8dc1-f7ae9b6c673b">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="640" y="160" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:dataInputAssociation>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.input1</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:errorEventDefinition id="4ed43a7e-**************-ef118b827dab" eventImplId="7c2226f7-22a2-4b49-837b-33ac28bd0930">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                    <ns4:errorCode />
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:endEvent>
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6bb7ccf3-0755-4917-8285-ab7cfb79ac5d</processLinkId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.288bfafe-8fb2-4942-8545-daa4e775273b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.103b0b4a-d192-4db9-8d39-6b99b2b220dc</toProcessItemId>
            <guid>618751e0-0329-4ffd-8ec7-84ece1371043</guid>
            <versionId>3d613731-8fc0-4cd6-9bcc-f6666edf41fd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.288bfafe-8fb2-4942-8545-daa4e775273b</fromProcessItemId>
            <toProcessItemId>2025.103b0b4a-d192-4db9-8d39-6b99b2b220dc</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8a8a523a-a85f-405e-8047-2f089e63a066</processLinkId>
            <processId>1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a9f667e9-9a2e-47e4-8d46-6dda48e774e3</fromProcessItemId>
            <endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-2aa4</endStateId>
            <toProcessItemId>2025.103b0b4a-d192-4db9-8d39-6b99b2b220dc</toProcessItemId>
            <guid>1e141be7-33b6-45a1-8ea3-f50c6ff972ba</guid>
            <versionId>bc2a0607-5e65-4815-880f-4a49f55cd8a0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a9f667e9-9a2e-47e4-8d46-6dda48e774e3</fromProcessItemId>
            <toProcessItemId>2025.103b0b4a-d192-4db9-8d39-6b99b2b220dc</toProcessItemId>
        </link>
    </process>
</teamworks>

