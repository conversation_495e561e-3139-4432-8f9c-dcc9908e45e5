<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9" name="Create CIF Folder">
        <lastModified>1700139575569</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.df84ae73-6813-4bdb-88f9-c31e052200ab</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-5473</guid>
        <versionId>f98ead3b-39b5-422a-b96a-f066cc63e6a5</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:76b" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.7f6a6915-30fe-4b0a-89da-2f7704af83ad"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":65,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"c5de2388-f1e3-4451-877c-b246c0c7244c"},{"incoming":["8479b56b-2fd3-4e73-893f-bce5fefe4610","6e5bcdea-0ff8-4d68-8061-6523b5e52e94","249a3738-22b2-4f7f-8579-7009531a9471"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":560,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:-5471"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c974ce3d-925a-4337-8f72-1ac8caea12cf"},{"targetRef":"df84ae73-6813-4bdb-88f9-c31e052200ab","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.7f6a6915-30fe-4b0a-89da-2f7704af83ad","sourceRef":"c5de2388-f1e3-4451-877c-b246c0c7244c"},{"outgoing":["91e0abaf-cf35-4bd7-83f3-8bfa925d4ffd"],"incoming":["2027.7f6a6915-30fe-4b0a-89da-2f7704af83ad"],"matchAllSearchCriteria":true,"extensionElements":{"postAssignmentScript":["log.info(\" ServiceName : Get Customer Folder If Exists : END\");"],"nodeVisualInfo":[{"width":95,"x":126,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\" ServiceName : Get Customer Folder If Exists : START\");"],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_FOLDER_BY_PATH","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Customer Folder If Exists","dataInputAssociation":[{"targetRef":"PATH","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.FILENET_ROOT_PATH + \"\/\" +tw.local.CIF"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"df84ae73-6813-4bdb-88f9-c31e052200ab","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","declaredType":"TFormalExpression","content":["tw.local.customerFolder"]}}],"sourceRef":["FOLDER"]}],"orderOverride":false},{"itemSubjectRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","name":"customerFolder","isCollection":false,"declaredType":"dataObject","id":"2056.5463d03f-9c52-4718-8d4d-d58a8128dc20"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9","name":"properties","isCollection":true,"declaredType":"dataObject","id":"2056.7d279a03-d8f5-4bb8-8cf8-4292fcf1cd91"},{"startQuantity":1,"outgoing":["8479b56b-2fd3-4e73-893f-bce5fefe4610"],"incoming":["91e0abaf-cf35-4bd7-83f3-8bfa925d4ffd"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":297,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"set customer folder id","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2a748093-7a3e-48e4-8433-3f480c663704","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.folderID = tw.local.customerFolder.objectId;"]}},{"targetRef":"2a748093-7a3e-48e4-8433-3f480c663704","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To set customer folder id","declaredType":"sequenceFlow","id":"91e0abaf-cf35-4bd7-83f3-8bfa925d4ffd","sourceRef":"df84ae73-6813-4bdb-88f9-c31e052200ab"},{"targetRef":"c974ce3d-925a-4337-8f72-1ac8caea12cf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8479b56b-2fd3-4e73-893f-bce5fefe4610","sourceRef":"2a748093-7a3e-48e4-8433-3f480c663704"},{"startQuantity":1,"outgoing":["400017fa-7cbb-4fb6-8406-25114b688707"],"incoming":["310acf72-e8c4-4ae9-8db1-bf6887596604"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":125,"y":285,"declaredType":"TNodeVisualInfo","height":70}]},"name":"set customer  folder Properties","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"e661ec76-4f9b-4485-807d-8d1310d738fe","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.properties = new tw.object.listOf.ECMProperty();\r\n\r\nfunction addProp(typeID , value)\r\n{\r\n\tvar porp = new tw.object.ECMProperty();\r\n\tporp.objectTypeId = typeID;\r\n\tporp.value = value;\r\n\ttw.local.properties.insertIntoList(tw.local.properties.listLength, porp);\r\n}\r\n\r\naddProp(\"\"+tw.epv.ECMProperties.arabicName , tw.local.customerName);\r\naddProp(\"\"+tw.epv.ECMProperties.customerCIF , tw.local.CIF);\r\naddProp(\"\"+tw.epv.ECMProperties.branchCode , tw.local.branchCode);\r\naddProp(\"\"+tw.epv.ECMProperties.customerType , \"\"+tw.epv.ECMProperties.Corporate);\r\n\r\n"]}},{"targetRef":"6d803c6e-12ce-488d-8177-8e3c324ae883","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To create customer folder","declaredType":"sequenceFlow","id":"400017fa-7cbb-4fb6-8406-25114b688707","sourceRef":"e661ec76-4f9b-4485-807d-8d1310d738fe"},{"parallelMultiple":false,"outgoing":["e04aba8f-4b50-479e-8d70-888c62793d88"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"7965b0c3-3ae7-4088-8f2f-1fd9abb7047c"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"40a70d10-da16-4459-8288-b95056ab1b7c","otherAttributes":{"eventImplId":"686dd618-e267-4bb9-86dc-4579f09e2c48"}}],"attachedToRef":"2a748093-7a3e-48e4-8433-3f480c663704","extensionElements":{"nodeVisualInfo":[{"width":24,"x":332,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"1291d1a9-76d7-4d29-8f19-ecf607a2d4dc","outputSet":{}},{"parallelMultiple":false,"outgoing":["4a21d3b5-7855-4dc1-86de-40a627f64385"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ca4b05b3-2324-4b1d-86c5-fc1fae99d2d6"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"500f3cb0-2a80-4c81-8697-053841e1c91b","otherAttributes":{"eventImplId":"8596ea45-f18f-4e6b-8a76-809c3feabbe8"}}],"attachedToRef":"e661ec76-4f9b-4485-807d-8d1310d738fe","extensionElements":{"nodeVisualInfo":[{"width":24,"x":186,"y":273,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"f5dd0de0-181f-4da4-8463-d53367bf0417","outputSet":{}},{"targetRef":"6664c13f-7f93-4140-8915-f7216758a523","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"4a21d3b5-7855-4dc1-86de-40a627f64385","sourceRef":"f5dd0de0-181f-4da4-8463-d53367bf0417"},{"targetRef":"6664c13f-7f93-4140-8915-f7216758a523","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"e04aba8f-4b50-479e-8d70-888c62793d88","sourceRef":"1291d1a9-76d7-4d29-8f19-ecf607a2d4dc"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.69d24116-7498-4049-877b-20aab5568d53"},{"outgoing":["6e5bcdea-0ff8-4d68-8061-6523b5e52e94"],"incoming":["400017fa-7cbb-4fb6-8406-25114b688707"],"matchAllSearchCriteria":true,"extensionElements":{"postAssignmentScript":["java.lang.Thread.sleep(\"2000\");\r\nlog.info(\" ServiceName : Create Customer Folder : END\");"],"nodeVisualInfo":[{"width":95,"x":413,"y":285,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\" ServiceName : Create Customer Folder : START\");"],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_CREATE_FOLDER","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"create customer folder","dataInputAssociation":[{"targetRef":"NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.CIF"]}}]},{"targetRef":"PARENT_FOLDER_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.FILENET_ROOT_PATH"]}}]},{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"FileNet\""]}}]},{"targetRef":"PROPERTIES","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9","declaredType":"TFormalExpression","content":["tw.local.properties"]}}]},{"targetRef":"OBJECT_TYPE_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.epv.ECMProperties.customerFolder"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"6d803c6e-12ce-488d-8177-8e3c324ae883","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderID"]}}],"sourceRef":["FOLDER_ID"]}],"orderOverride":false},{"parallelMultiple":false,"outgoing":["db881e9d-eb0b-4836-8c5f-d979a7d8d581"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"20cd450c-e805-4a41-87a3-bd1e6108119d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"bcdc8bdf-ad21-4847-8499-9b8a90cd79d2","otherAttributes":{"eventImplId":"24b77cc1-3ee7-4315-87c7-5efbebef3365"}}],"attachedToRef":"6d803c6e-12ce-488d-8177-8e3c324ae883","extensionElements":{"nodeVisualInfo":[{"width":24,"x":448,"y":273,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Copy of Error1","declaredType":"boundaryEvent","id":"6c8f0fc1-3caa-4b77-8984-d113c99d7a59","outputSet":{}},{"targetRef":"c974ce3d-925a-4337-8f72-1ac8caea12cf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"6e5bcdea-0ff8-4d68-8061-6523b5e52e94","sourceRef":"6d803c6e-12ce-488d-8177-8e3c324ae883"},{"targetRef":"6664c13f-7f93-4140-8915-f7216758a523","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"db881e9d-eb0b-4836-8c5f-d979a7d8d581","sourceRef":"6c8f0fc1-3caa-4b77-8984-d113c99d7a59"},{"startQuantity":1,"outgoing":["249a3738-22b2-4f7f-8579-7009531a9471"],"incoming":["e04aba8f-4b50-479e-8d70-888c62793d88","db881e9d-eb0b-4836-8c5f-d979a7d8d581","4a21d3b5-7855-4dc1-86de-40a627f64385"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":296,"y":189,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exp Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Create CIF Folder\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6664c13f-7f93-4140-8915-f7216758a523","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.4066c07f-885f-4a47-83ac-293707e931b8"},{"targetRef":"c974ce3d-925a-4337-8f72-1ac8caea12cf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"249a3738-22b2-4f7f-8579-7009531a9471","sourceRef":"6664c13f-7f93-4140-8915-f7216758a523"},{"parallelMultiple":false,"outgoing":["310acf72-e8c4-4ae9-8db1-bf6887596604"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"64f50ee8-e815-4b8f-8d9c-3d22a4995aa2"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ed7911b3-1863-4ba7-8663-f486e71e4c92","otherAttributes":{"eventImplId":"ea29cd76-7ed6-46e7-8f69-3b376d57ca39"}}],"attachedToRef":"df84ae73-6813-4bdb-88f9-c31e052200ab","extensionElements":{"nodeVisualInfo":[{"width":24,"x":161,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"644e4e0c-3c69-406a-8ba0-04074b248659","outputSet":{}},{"targetRef":"e661ec76-4f9b-4485-807d-8d1310d738fe","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To set customer  folder Properties","declaredType":"sequenceFlow","id":"310acf72-e8c4-4ae9-8db1-bf6887596604","sourceRef":"644e4e0c-3c69-406a-8ba0-04074b248659"}],"laneSet":[{"id":"47d881cf-7003-4d06-84ec-ed26623e516d","lane":[{"flowNodeRef":["c5de2388-f1e3-4451-877c-b246c0c7244c","c974ce3d-925a-4337-8f72-1ac8caea12cf","df84ae73-6813-4bdb-88f9-c31e052200ab","2a748093-7a3e-48e4-8433-3f480c663704","e661ec76-4f9b-4485-807d-8d1310d738fe","1291d1a9-76d7-4d29-8f19-ecf607a2d4dc","f5dd0de0-181f-4da4-8463-d53367bf0417","6d803c6e-12ce-488d-8177-8e3c324ae883","6c8f0fc1-3caa-4b77-8984-d113c99d7a59","6664c13f-7f93-4140-8915-f7216758a523","644e4e0c-3c69-406a-8ba0-04074b248659"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"8e387a91-6da6-4b03-8d6f-b3fd1cc2f4d8","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create CIF Folder","declaredType":"process","id":"1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"id":"2055.4e7ecd5e-3285-4d63-88df-44e8a1f7a537"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.b559e5cb-fad2-46e0-8540-8535f2c365ba"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.51a88928-60d9-48d1-b133-fa98f61b49a6","epvProcessLinkId":"f8022856-5c78-4f7b-85d4-7fc3b7ac8f5c","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.379eeaf3-2fbd-46b8-8720-0d5e04521d39","2055.87070747-bae6-493a-84b1-00f2ae7364a0","2055.108cede2-d4dd-41ae-8c9a-0eb1cf7cab0e","2055.452059d6-ebcf-4624-8932-cf8ff613f6e9"]}],"outputSet":[{"dataOutputRefs":["2055.4e7ecd5e-3285-4d63-88df-44e8a1f7a537","2055.b559e5cb-fad2-46e0-8540-8535f2c365ba"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"hebaaa\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerName","isCollection":false,"id":"2055.379eeaf3-2fbd-46b8-8720-0d5e04521d39"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"c\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerType","isCollection":false,"id":"2055.87070747-bae6-493a-84b1-00f2ae7364a0"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"12222\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"CIF","isCollection":false,"id":"2055.108cede2-d4dd-41ae-8c9a-0eb1cf7cab0e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"001\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.452059d6-ebcf-4624-8932-cf8ff613f6e9"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="customerName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.379eeaf3-2fbd-46b8-8720-0d5e04521d39</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ef79a0e0-a756-4959-b61b-e999f1742381</guid>
            <versionId>d47a4359-015f-43f5-acd6-3f33ff846423</versionId>
        </processParameter>
        <processParameter name="customerType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.87070747-bae6-493a-84b1-00f2ae7364a0</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4059b596-bf36-4dc9-b902-3a14964048ca</guid>
            <versionId>22e121ba-1080-4c90-b0da-f4a4a3bae9d3</versionId>
        </processParameter>
        <processParameter name="CIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.108cede2-d4dd-41ae-8c9a-0eb1cf7cab0e</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a953d996-cca4-414d-9cdc-0bcf9ad9f5ae</guid>
            <versionId>8ccb2e00-9002-407c-af4b-adf8b50ab0ac</versionId>
        </processParameter>
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.452059d6-ebcf-4624-8932-cf8ff613f6e9</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a5303c63-bb7c-4f88-a04a-53a7e3622968</guid>
            <versionId>4ca28e10-01bc-4970-8f7a-1cc25ae22a60</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4e7ecd5e-3285-4d63-88df-44e8a1f7a537</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1f10d8a4-435c-4c7e-aa16-763edefa3df1</guid>
            <versionId>644382f0-63f4-46ee-8a2e-2a612423e6f9</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b559e5cb-fad2-46e0-8540-8535f2c365ba</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f593a7cb-115f-4685-bd34-218397f057e2</guid>
            <versionId>a43b00fd-4cce-4c43-bbd6-f7105a377464</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.55da5ef9-5c2e-45a9-a9fd-a882730755d7</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>131</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>66f1dd23-e114-4a88-93c0-6e8c23c9bae5</guid>
            <versionId>12eebe7a-23bf-42bf-9ea4-9701b0d31577</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6a3f06a9-5436-4e14-8fde-b9e3ac6101f1</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>132</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>316ae9f6-6b35-4964-baaf-5b490d8226c2</guid>
            <versionId>850d0b64-b29b-4e8d-8841-bbc154c76986</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.887d354d-90e7-4287-b5b1-d28edd66cfdc</processParameterId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>133</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>498f1208-5d58-42fd-a3e6-ec85bbc5a856</guid>
            <versionId>03fd8221-c2f4-4299-b9d2-ceb24ccc48fa</versionId>
        </processParameter>
        <processVariable name="customerFolder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5463d03f-9c52-4718-8d4d-d58a8128dc20</processVariableId>
            <description isNull="true" />
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>98017e0d-f23b-4855-bb4f-ad6da7344177</guid>
            <versionId>1fe0870b-a6dc-4194-8e5f-34a8051391bf</versionId>
        </processVariable>
        <processVariable name="properties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7d279a03-d8f5-4bb8-8cf8-4292fcf1cd91</processVariableId>
            <description isNull="true" />
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.a4275847-1a37-4d3c-9289-0462d5afbca9</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d4ea65f7-1d92-4fec-8298-a2a579ceac7f</guid>
            <versionId>3a586ee9-6ede-41a5-8f2b-fa0c83f83c39</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.69d24116-7498-4049-877b-20aab5568d53</processVariableId>
            <description isNull="true" />
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bba49d3b-242a-41cf-af28-7847fe8093d4</guid>
            <versionId>3313d7fe-3ea8-4c33-a854-50286b9bd27a</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4066c07f-885f-4a47-83ac-293707e931b8</processVariableId>
            <description isNull="true" />
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>64c561a7-74ba-42b5-8cae-cc7b0b9b5dd9</guid>
            <versionId>4fd5b364-92a9-4cdf-a7a3-45fad3ff4f07</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</processItemId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <name>Exp Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.bfbcdaf9-3b95-4b82-aa30-a59f94a1fa27</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cdc758d910e20d2d:-607ed7:18af686ab01:-6354</guid>
            <versionId>017ee3d4-37b9-4b78-9982-06643d78a31a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="296" y="189">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.bfbcdaf9-3b95-4b82-aa30-a59f94a1fa27</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>2106ecc5-612f-4edf-b711-20a6abe09df4</guid>
                <versionId>db64c5df-8882-40e6-b7cd-339f7678bcc0</versionId>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2bf97a54-324d-42e7-9482-26a315bb42f4</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.bfbcdaf9-3b95-4b82-aa30-a59f94a1fa27</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>2097cd40-2975-416f-aa28-449eab87f14d</guid>
                    <versionId>7b77ec54-2178-41e5-9148-0fce38d3f848</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7245e090-bdd8-4333-a381-edbdd888b242</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.bfbcdaf9-3b95-4b82-aa30-a59f94a1fa27</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Create CIF Folder"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d9623638-fb0b-445e-8f23-f1786e5f5dc2</guid>
                    <versionId>a824458c-2067-458e-9dc5-b4f7872508d7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fcf93ef0-cf5d-49c9-8ff1-6339a1b51f2b</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.bfbcdaf9-3b95-4b82-aa30-a59f94a1fa27</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>746d12f9-681f-4921-9971-fb38d2f40ccd</guid>
                    <versionId>dc06f0f6-c55e-4790-8f54-85a8704ef3a2</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c974ce3d-925a-4337-8f72-1ac8caea12cf</processItemId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.53eeba06-5984-4029-adc9-938097c51d58</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-5471</guid>
            <versionId>2209580a-6bb6-477b-8b1e-4803c8ee7f7d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="560" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.53eeba06-5984-4029-adc9-938097c51d58</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2e8caee5-ca13-4dc8-8cbd-c0999e26feae</guid>
                <versionId>d281773a-743a-4414-9a81-c319caef22ba</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e661ec76-4f9b-4485-807d-8d1310d738fe</processItemId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <name>set customer  folder Properties</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.f4b2e546-36f1-48bd-93f2-ff1e96ea0e68</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-52eb</guid>
            <versionId>54aa768c-0e64-4717-b5e0-8680d06df13d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="125" y="285">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>topRight</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:cdc758d910e20d2d:-607ed7:18af686ab01:-6354</errorHandlerItem>
                <errorHandlerItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="topCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.f4b2e546-36f1-48bd-93f2-ff1e96ea0e68</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.properties = new tw.object.listOf.ECMProperty();&#xD;
&#xD;
function addProp(typeID , value)&#xD;
{&#xD;
	var porp = new tw.object.ECMProperty();&#xD;
	porp.objectTypeId = typeID;&#xD;
	porp.value = value;&#xD;
	tw.local.properties.insertIntoList(tw.local.properties.listLength, porp);&#xD;
}&#xD;
&#xD;
addProp(""+tw.epv.ECMProperties.arabicName , tw.local.customerName);&#xD;
addProp(""+tw.epv.ECMProperties.customerCIF , tw.local.CIF);&#xD;
addProp(""+tw.epv.ECMProperties.branchCode , tw.local.branchCode);&#xD;
addProp(""+tw.epv.ECMProperties.customerType , ""+tw.epv.ECMProperties.Corporate);&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>f046f345-4641-40e5-9d2a-8bebb0815519</guid>
                <versionId>fe57fc84-4028-4e86-aaf0-ba7fb98e0857</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6d803c6e-12ce-488d-8177-8e3c324ae883</processItemId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <name>create customer folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.74078bdd-95c8-436d-965f-d269cdae0890</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</errorHandlerItemId>
            <guid>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac6fe7417:-221b</guid>
            <versionId>67cfd4d8-bc9a-4359-ab9b-a3b465f0abee</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.0a2dfba9-bfc7-4c0d-a42c-22e07ff75de9</processItemPrePostId>
                <processItemId>2025.6d803c6e-12ce-488d-8177-8e3c324ae883</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName : Create Customer Folder : START");</script>
                <guid>db6d12d3-76db-4fd9-94a6-6b3e6ed8fc67</guid>
                <versionId>1fa0a130-103f-4aed-9850-3ca314fe34ec</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.138533fd-831d-4557-9bf2-bf7dc83d49e9</processItemPrePostId>
                <processItemId>2025.6d803c6e-12ce-488d-8177-8e3c324ae883</processItemId>
                <location>2</location>
                <script>java.lang.Thread.sleep("2000");&#xD;
log.info(" ServiceName : Create Customer Folder : END");</script>
                <guid>df402885-5730-44f0-b894-765e6a13b8e2</guid>
                <versionId>36590f68-dfac-4cc0-8264-457621e735b9</versionId>
            </processPrePosts>
            <layoutData x="413" y="285">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Copy of Error1</name>
                <locationId>topCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:cdc758d910e20d2d:-607ed7:18af686ab01:-6354</errorHandlerItem>
                <errorHandlerItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="topCenter" portType="1" />
                    <toPort locationId="rightBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.74078bdd-95c8-436d-965f-d269cdae0890</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;objectTypeId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.epv.ECMProperties.customerFolder&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;parentFolderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.env.FILENET_ROOT_PATH&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;name&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.CIF&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;properties&lt;/name&gt;&#xD;
      &lt;type&gt;ECMProperty&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.properties&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;true&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"FileNet"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderID&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_CREATE_FOLDER&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.6a3f06a9-5436-4e14-8fde-b9e3ac6101f1&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>04640f84-0484-4139-8ee0-73392ca5bbd2</guid>
                <versionId>35585642-74cf-4b7a-bcce-8deb3d541712</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2a748093-7a3e-48e4-8433-3f480c663704</processItemId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <name>set customer folder id</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c61e850d-f75d-407e-8e55-1b2ce5373a16</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-5373</guid>
            <versionId>8b32701e-10fe-47a0-baa2-055f1a1ab1fc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="297" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:cdc758d910e20d2d:-607ed7:18af686ab01:-6354</errorHandlerItem>
                <errorHandlerItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c61e850d-f75d-407e-8e55-1b2ce5373a16</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.folderID = tw.local.customerFolder.objectId;</script>
                <isRule>false</isRule>
                <guid>a48f01dc-4168-44c4-900d-770fcacc420e</guid>
                <versionId>23c40583-489f-4b34-95ad-d27a455402bb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df84ae73-6813-4bdb-88f9-c31e052200ab</processItemId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <name>Get Customer Folder If Exists</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.f00c087c-e749-4918-937c-5ad7f8b19867</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e661ec76-4f9b-4485-807d-8d1310d738fe</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-5443</guid>
            <versionId>f5845678-327f-4199-a00a-f6de68711395</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.0227201c-35cd-491b-9c9d-1b4c559c7785</processItemPrePostId>
                <processItemId>2025.df84ae73-6813-4bdb-88f9-c31e052200ab</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Get Customer Folder If Exists : END");</script>
                <guid>31916714-002c-4eba-8003-ababadf3c0e2</guid>
                <versionId>966ccd49-6532-4b55-914e-ad336e237aeb</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.8abb82b7-af0b-4b3c-b34d-6e09656a65de</processItemPrePostId>
                <processItemId>2025.df84ae73-6813-4bdb-88f9-c31e052200ab</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName : Get Customer Folder If Exists : START");</script>
                <guid>4cbca834-33c4-4dcb-a5f7-59d61db2bc3a</guid>
                <versionId>c11519a2-21a9-4372-b809-f91a266da0af</versionId>
            </processPrePosts>
            <layoutData x="126" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:fef170a08f25d496:5466e087:189df5f8551:-52eb</errorHandlerItem>
                <errorHandlerItemId>2025.e661ec76-4f9b-4485-807d-8d1310d738fe</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.f00c087c-e749-4918-937c-5ad7f8b19867</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;path&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.env.FILENET_ROOT_PATH + "/" +tw.local.CIF&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folder&lt;/name&gt;&#xD;
      &lt;type&gt;ECMFolder&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.customerFolder&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_FOLDER_BY_PATH&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.887d354d-90e7-4287-b5b1-d28edd66cfdc&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>5828c9d6-a506-4da9-9ab6-263a918423b1</guid>
                <versionId>6f1c0ffd-20c7-4b56-8f6c-834b7d243c89</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.2e560caa-8e56-4c4d-9e7c-99585d2a3c2c</epvProcessLinkId>
            <epvId>/21.51a88928-60d9-48d1-b133-fa98f61b49a6</epvId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <guid>80425263-ff5d-44d8-9af2-3c665aa3a8db</guid>
            <versionId>3ea7472c-5768-4f4f-9761-41673cee6fe6</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.df84ae73-6813-4bdb-88f9-c31e052200ab</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="65" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Create CIF Folder" id="1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.51a88928-60d9-48d1-b133-fa98f61b49a6" epvProcessLinkId="f8022856-5c78-4f7b-85d4-7fc3b7ac8f5c" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="customerName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.379eeaf3-2fbd-46b8-8720-0d5e04521d39">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"hebaaa"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="customerType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.87070747-bae6-493a-84b1-00f2ae7364a0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"c"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="CIF" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.108cede2-d4dd-41ae-8c9a-0eb1cf7cab0e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"12222"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.452059d6-ebcf-4624-8932-cf8ff613f6e9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"001"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.4e7ecd5e-3285-4d63-88df-44e8a1f7a537" />
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b559e5cb-fad2-46e0-8540-8535f2c365ba" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.379eeaf3-2fbd-46b8-8720-0d5e04521d39</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.87070747-bae6-493a-84b1-00f2ae7364a0</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.108cede2-d4dd-41ae-8c9a-0eb1cf7cab0e</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.452059d6-ebcf-4624-8932-cf8ff613f6e9</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.4e7ecd5e-3285-4d63-88df-44e8a1f7a537</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.b559e5cb-fad2-46e0-8540-8535f2c365ba</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="47d881cf-7003-4d06-84ec-ed26623e516d">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="8e387a91-6da6-4b03-8d6f-b3fd1cc2f4d8" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>c5de2388-f1e3-4451-877c-b246c0c7244c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c974ce3d-925a-4337-8f72-1ac8caea12cf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>df84ae73-6813-4bdb-88f9-c31e052200ab</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2a748093-7a3e-48e4-8433-3f480c663704</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e661ec76-4f9b-4485-807d-8d1310d738fe</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1291d1a9-76d7-4d29-8f19-ecf607a2d4dc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f5dd0de0-181f-4da4-8463-d53367bf0417</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6d803c6e-12ce-488d-8177-8e3c324ae883</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6c8f0fc1-3caa-4b77-8984-d113c99d7a59</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6664c13f-7f93-4140-8915-f7216758a523</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>644e4e0c-3c69-406a-8ba0-04074b248659</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="c5de2388-f1e3-4451-877c-b246c0c7244c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="65" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.7f6a6915-30fe-4b0a-89da-2f7704af83ad</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c974ce3d-925a-4337-8f72-1ac8caea12cf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="560" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-5471</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8479b56b-2fd3-4e73-893f-bce5fefe4610</ns16:incoming>
                        
                        
                        <ns16:incoming>6e5bcdea-0ff8-4d68-8061-6523b5e52e94</ns16:incoming>
                        
                        
                        <ns16:incoming>249a3738-22b2-4f7f-8579-7009531a9471</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="c5de2388-f1e3-4451-877c-b246c0c7244c" targetRef="df84ae73-6813-4bdb-88f9-c31e052200ab" name="To End" id="2027.7f6a6915-30fe-4b0a-89da-2f7704af83ad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns4:contentTask serverName="FileNet" operationRef="FOLDER_OP_GET_FOLDER_BY_PATH" name="Get Customer Folder If Exists" id="df84ae73-6813-4bdb-88f9-c31e052200ab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="126" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName : Get Customer Folder If Exists : START");</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Get Customer Folder If Exists : END");</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.7f6a6915-30fe-4b0a-89da-2f7704af83ad</ns16:incoming>
                        
                        
                        <ns16:outgoing>91e0abaf-cf35-4bd7-83f3-8bfa925d4ffd</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PATH</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.FILENET_ROOT_PATH + "/" +tw.local.CIF</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183">tw.local.customerFolder</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183" isCollection="false" name="customerFolder" id="2056.5463d03f-9c52-4718-8d4d-d58a8128dc20" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9" isCollection="true" name="properties" id="2056.7d279a03-d8f5-4bb8-8cf8-4292fcf1cd91">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false" />
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="set customer folder id" id="2a748093-7a3e-48e4-8433-3f480c663704">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="297" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>91e0abaf-cf35-4bd7-83f3-8bfa925d4ffd</ns16:incoming>
                        
                        
                        <ns16:outgoing>8479b56b-2fd3-4e73-893f-bce5fefe4610</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.folderID = tw.local.customerFolder.objectId;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="df84ae73-6813-4bdb-88f9-c31e052200ab" targetRef="2a748093-7a3e-48e4-8433-3f480c663704" name="To set customer folder id" id="91e0abaf-cf35-4bd7-83f3-8bfa925d4ffd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="2a748093-7a3e-48e4-8433-3f480c663704" targetRef="c974ce3d-925a-4337-8f72-1ac8caea12cf" name="To End" id="8479b56b-2fd3-4e73-893f-bce5fefe4610">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="set customer  folder Properties" id="e661ec76-4f9b-4485-807d-8d1310d738fe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="125" y="285" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>310acf72-e8c4-4ae9-8db1-bf6887596604</ns16:incoming>
                        
                        
                        <ns16:outgoing>400017fa-7cbb-4fb6-8406-25114b688707</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.properties = new tw.object.listOf.ECMProperty();&#xD;
&#xD;
function addProp(typeID , value)&#xD;
{&#xD;
	var porp = new tw.object.ECMProperty();&#xD;
	porp.objectTypeId = typeID;&#xD;
	porp.value = value;&#xD;
	tw.local.properties.insertIntoList(tw.local.properties.listLength, porp);&#xD;
}&#xD;
&#xD;
addProp(""+tw.epv.ECMProperties.arabicName , tw.local.customerName);&#xD;
addProp(""+tw.epv.ECMProperties.customerCIF , tw.local.CIF);&#xD;
addProp(""+tw.epv.ECMProperties.branchCode , tw.local.branchCode);&#xD;
addProp(""+tw.epv.ECMProperties.customerType , ""+tw.epv.ECMProperties.Corporate);&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e661ec76-4f9b-4485-807d-8d1310d738fe" targetRef="6d803c6e-12ce-488d-8177-8e3c324ae883" name="To create customer folder" id="400017fa-7cbb-4fb6-8406-25114b688707">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="2a748093-7a3e-48e4-8433-3f480c663704" parallelMultiple="false" name="Error" id="1291d1a9-76d7-4d29-8f19-ecf607a2d4dc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="332" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e04aba8f-4b50-479e-8d70-888c62793d88</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="7965b0c3-3ae7-4088-8f2f-1fd9abb7047c" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="40a70d10-da16-4459-8288-b95056ab1b7c" eventImplId="686dd618-e267-4bb9-86dc-4579f09e2c48">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e661ec76-4f9b-4485-807d-8d1310d738fe" parallelMultiple="false" name="Error2" id="f5dd0de0-181f-4da4-8463-d53367bf0417">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="186" y="273" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4a21d3b5-7855-4dc1-86de-40a627f64385</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ca4b05b3-2324-4b1d-86c5-fc1fae99d2d6" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="500f3cb0-2a80-4c81-8697-053841e1c91b" eventImplId="8596ea45-f18f-4e6b-8a76-809c3feabbe8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f5dd0de0-181f-4da4-8463-d53367bf0417" targetRef="6664c13f-7f93-4140-8915-f7216758a523" name="To End Event" id="4a21d3b5-7855-4dc1-86de-40a627f64385">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1291d1a9-76d7-4d29-8f19-ecf607a2d4dc" targetRef="6664c13f-7f93-4140-8915-f7216758a523" name="To End Event" id="e04aba8f-4b50-479e-8d70-888c62793d88">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.69d24116-7498-4049-877b-20aab5568d53" />
                    
                    
                    <ns4:contentTask serverName="FileNet" useMappedVariable="true" matchAllSearchCriteria="true" orderOverride="false" implementation="##WebService" operationRef="FOLDER_OP_CREATE_FOLDER" isForCompensation="false" startQuantity="1" completionQuantity="1" name="create customer folder" id="6d803c6e-12ce-488d-8177-8e3c324ae883">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="413" y="285" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>java.lang.Thread.sleep("2000");&#xD;
log.info(" ServiceName : Create Customer Folder : END");</ns3:postAssignmentScript>
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName : Create Customer Folder : START");</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>400017fa-7cbb-4fb6-8406-25114b688707</ns16:incoming>
                        
                        
                        <ns16:outgoing>6e5bcdea-0ff8-4d68-8061-6523b5e52e94</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.CIF</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PARENT_FOLDER_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.FILENET_ROOT_PATH</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"FileNet"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PROPERTIES</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9">tw.local.properties</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>OBJECT_TYPE_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.epv.ECMProperties.customerFolder</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER_ID</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6d803c6e-12ce-488d-8177-8e3c324ae883" parallelMultiple="false" name="Copy of Error1" id="6c8f0fc1-3caa-4b77-8984-d113c99d7a59">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="448" y="273" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>db881e9d-eb0b-4836-8c5f-d979a7d8d581</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="20cd450c-e805-4a41-87a3-bd1e6108119d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="bcdc8bdf-ad21-4847-8499-9b8a90cd79d2" eventImplId="24b77cc1-3ee7-4315-87c7-5efbebef3365">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="6d803c6e-12ce-488d-8177-8e3c324ae883" targetRef="c974ce3d-925a-4337-8f72-1ac8caea12cf" name="To End" id="6e5bcdea-0ff8-4d68-8061-6523b5e52e94">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6c8f0fc1-3caa-4b77-8984-d113c99d7a59" targetRef="6664c13f-7f93-4140-8915-f7216758a523" name="To End Event" id="db881e9d-eb0b-4836-8c5f-d979a7d8d581">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exp Handling" id="6664c13f-7f93-4140-8915-f7216758a523">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="296" y="189" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e04aba8f-4b50-479e-8d70-888c62793d88</ns16:incoming>
                        
                        
                        <ns16:incoming>db881e9d-eb0b-4836-8c5f-d979a7d8d581</ns16:incoming>
                        
                        
                        <ns16:incoming>4a21d3b5-7855-4dc1-86de-40a627f64385</ns16:incoming>
                        
                        
                        <ns16:outgoing>249a3738-22b2-4f7f-8579-7009531a9471</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Create CIF Folder"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.4066c07f-885f-4a47-83ac-293707e931b8" />
                    
                    
                    <ns16:sequenceFlow sourceRef="6664c13f-7f93-4140-8915-f7216758a523" targetRef="c974ce3d-925a-4337-8f72-1ac8caea12cf" name="To End" id="249a3738-22b2-4f7f-8579-7009531a9471">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="df84ae73-6813-4bdb-88f9-c31e052200ab" parallelMultiple="false" name="Error1" id="644e4e0c-3c69-406a-8ba0-04074b248659">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="161" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>310acf72-e8c4-4ae9-8db1-bf6887596604</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="64f50ee8-e815-4b8f-8d9c-3d22a4995aa2" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="ed7911b3-1863-4ba7-8663-f486e71e4c92" eventImplId="ea29cd76-7ed6-46e7-8f69-3b376d57ca39">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="644e4e0c-3c69-406a-8ba0-04074b248659" targetRef="e661ec76-4f9b-4485-807d-8d1310d738fe" name="To set customer  folder Properties" id="310acf72-e8c4-4ae9-8db1-bf6887596604">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6e5bcdea-0ff8-4d68-8061-6523b5e52e94</processLinkId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6d803c6e-12ce-488d-8177-8e3c324ae883</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c974ce3d-925a-4337-8f72-1ac8caea12cf</toProcessItemId>
            <guid>4583842a-2f29-4d3d-8492-0ed6fd188658</guid>
            <versionId>301a613a-788b-4dce-a7f8-7ebf440f06e8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.6d803c6e-12ce-488d-8177-8e3c324ae883</fromProcessItemId>
            <toProcessItemId>2025.c974ce3d-925a-4337-8f72-1ac8caea12cf</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8479b56b-2fd3-4e73-893f-bce5fefe4610</processLinkId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2a748093-7a3e-48e4-8433-3f480c663704</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c974ce3d-925a-4337-8f72-1ac8caea12cf</toProcessItemId>
            <guid>da8869e2-7d76-473f-82bd-053ce04027ea</guid>
            <versionId>47e1f870-c6d1-4fc5-98ec-e8ebcaa85fb6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2a748093-7a3e-48e4-8433-3f480c663704</fromProcessItemId>
            <toProcessItemId>2025.c974ce3d-925a-4337-8f72-1ac8caea12cf</toProcessItemId>
        </link>
        <link name="To create customer folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.400017fa-7cbb-4fb6-8406-25114b688707</processLinkId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e661ec76-4f9b-4485-807d-8d1310d738fe</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6d803c6e-12ce-488d-8177-8e3c324ae883</toProcessItemId>
            <guid>4199e200-be1b-4081-8d2b-022c192b9623</guid>
            <versionId>66c89102-ea0d-40b6-829b-1003089c29cd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e661ec76-4f9b-4485-807d-8d1310d738fe</fromProcessItemId>
            <toProcessItemId>2025.6d803c6e-12ce-488d-8177-8e3c324ae883</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.249a3738-22b2-4f7f-8579-7009531a9471</processLinkId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.c974ce3d-925a-4337-8f72-1ac8caea12cf</toProcessItemId>
            <guid>96990fb8-2789-465d-8cbe-62d80c000473</guid>
            <versionId>6d01302f-0645-4505-b5ac-fb0d8cdc525e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.6664c13f-7f93-4140-8915-f7216758a523</fromProcessItemId>
            <toProcessItemId>2025.c974ce3d-925a-4337-8f72-1ac8caea12cf</toProcessItemId>
        </link>
        <link name="To set customer folder id">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.91e0abaf-cf35-4bd7-83f3-8bfa925d4ffd</processLinkId>
            <processId>1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.df84ae73-6813-4bdb-88f9-c31e052200ab</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.2a748093-7a3e-48e4-8433-3f480c663704</toProcessItemId>
            <guid>0a070040-d727-428a-abac-8eb19533bb1a</guid>
            <versionId>f82a89a3-19fe-4e99-9282-afc0f5f4ce2c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.df84ae73-6813-4bdb-88f9-c31e052200ab</fromProcessItemId>
            <toProcessItemId>2025.2a748093-7a3e-48e4-8433-3f480c663704</toProcessItemId>
        </link>
    </process>
</teamworks>

