<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d" name="ItmDef_1412542389_2e5f0222_20a7_4c21_bdaa_5284bb68cf3d">
        <lastModified>1693480732480</lastModified>
        <lastModifiedBy>abdelrahman.saleh</lastModifiedBy>
        <classId>12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>false</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType>3</extensionType>
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d</externalId>
        <dependencySummary>&lt;dependencySummary id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173e"&gt;
  &lt;artifactReference id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173d"&gt;
    &lt;refId&gt;/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4&lt;/refId&gt;
    &lt;refType&gt;1&lt;/refType&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173c"&gt;
      &lt;name&gt;externalId&lt;/name&gt;
      &lt;value&gt;http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173b"&gt;
      &lt;name&gt;mimeType&lt;/name&gt;
      &lt;value&gt;xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
  &lt;/artifactReference&gt;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","complexType":[{"annotation":{"documentation":[{}],"appinfo":[{"shared":[false],"advancedProperties":[{"namespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","typeName":"generateDocumentUsingPOSTResponse"}],"shadow":[false]}]},"sequence":{"element":[{"annotation":{"documentation":[{"content":["Document Generated Successfully"]}],"appinfo":[{"propertyName":["generateDocumentUsingPOST_200"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","_minOccurs":0,"typeName":"DocumentGenerationResponse","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":1}]}]},"name":"generateDocumentUsingPOST_200","type":"{http:\/\/NBEODCR}BrokenReference","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.a0722085-9246-4af1-b86e-8a21be7e94cc"}}]},"name":"ItmDef_1412542389_2e5f0222_20a7_4c21_bdaa_5284bb68cf3d"}],"id":"_12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d"}</jsonData>
        <description isNull="true" />
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1767</guid>
        <versionId>fadb4a5e-db8e-42be-9c01-28ab590158e1</versionId>
        <definition>
            <property>
                <name>generateDocumentUsingPOST_200</name>
                <description>Document Generated Successfully</description>
                <classRef>/12.a0722085-9246-4af1-b86e-8a21be7e94cc</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>DocumentGenerationResponse</typeName>
                    <typeNamespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>1</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name>generateDocumentUsingPOSTResponse</name>
                <namespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</namespace>
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

