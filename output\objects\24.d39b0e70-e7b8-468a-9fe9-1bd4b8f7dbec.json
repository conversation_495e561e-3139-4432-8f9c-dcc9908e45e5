{"id": "24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec", "versionId": "0b943e44-4716-4f4d-8001-267362d691f6", "name": "HUB compliance Representative Checker", "type": "participant", "typeName": "Participant", "details": {}, "_fullObjectData": {"teamworks": {"participant": {"id": "24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec", "name": "HUB compliance Representative Checker", "lastModified": "1691144627819", "lastModifiedBy": "heba", "participantId": "24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec", "participantDefinition": {"isNull": "true"}, "simulationGroupSize": "2", "capacityType": "1", "definitionType": "3", "percentAvailable": {"isNull": "true"}, "percentEfficiency": {"isNull": "true"}, "cost": "10.00", "currencyCode": {"isNull": "true"}, "image": {"isNull": "true"}, "serviceMembersRef": {"isNull": "true"}, "managersRef": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"team\":[{\"members\":{\"Users\":[{\"name\":\"abdelrahman.saleh\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"somaia\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"heba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}],\"UserGroups\":[{\"name\":\"BPM_ODC_HUB_077_COMP_CHKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"BPM_ODC_HUB_100_COMP_CHKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"BPM_ODC_HUB_200_COMP_CHKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"BPM_ODC_HUB_310_COMP_CHKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"BPM_ODC_HUB_380_COMP_CHKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"BPM_ODC_HUB_599_COMP_CHKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails\",\"type\":\"StandardMembers\"}]},\"documentation\":[{\"content\":[],\"textFormat\":\"text\\/plain\"}],\"name\":\"HUB compliance Representative Checker\",\"declaredType\":\"resource\",\"id\":\"24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.w3.org\\/1999\\/XPath\",\"id\":\"24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec\"}", "externalId": {"isNull": "true"}, "description": "", "guid": "guid:d694a63221635d5b:6baf87c4:18969a9a6e2:74af", "versionId": "0b943e44-4716-4f4d-8001-267362d691f6", "standardMembers": {"standardMember": [{"type": "Group", "name": "BPM_ODC_HUB_077_COMP_CHKR"}, {"type": "Group", "name": "BPM_ODC_HUB_100_COMP_CHKR"}, {"type": "Group", "name": "BPM_ODC_HUB_200_COMP_CHKR"}, {"type": "Group", "name": "BPM_ODC_HUB_310_COMP_CHKR"}, {"type": "Group", "name": "BPM_ODC_HUB_380_COMP_CHKR"}, {"type": "Group", "name": "BPM_ODC_HUB_599_COMP_CHKR"}, {"type": "User", "name": "abdelrahman.saleh"}, {"type": "User", "name": "so<PERSON>ia"}, {"type": "User", "name": "heba"}]}, "teamAssignments": ""}}}}