<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2f93c4b5-368e-4a13-aff6-b12926260bb3" name="retrieve DB lookups">
        <lastModified>1700581241655</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.5b3aae23-ece8-45dd-8ddf-11d4d19d94c1</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a22</guid>
        <versionId>a98912a2-804a-4147-876d-4038ef0000fb</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-2e91" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.2dd5aef2-4ac0-4734-841e-55e63549b5d7"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"ff0df4eb-8368-48d3-8b79-c54ebec943cd"},{"incoming":["720f5dc2-3633-458a-8109-68cac7ee11a1","e445c3eb-dce3-4275-844f-a999bddccd2a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":890,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"6a6f241f-2873-4df9-89c1-c1d7fd6c8af9"},{"targetRef":"5b3aae23-ece8-45dd-8ddf-11d4d19d94c1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Prepare Query","declaredType":"sequenceFlow","id":"2027.2dd5aef2-4ac0-4734-841e-55e63549b5d7","sourceRef":"ff0df4eb-8368-48d3-8b79-c54ebec943cd"},{"startQuantity":1,"outgoing":["519e6a46-10e9-4a48-8c53-d05a28c6f4a8"],"incoming":["*************-409e-8925-1d6ff8b643b0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":318,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Execute","dataInputAssociation":[{"targetRef":"2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.query"]}}]},{"targetRef":"2055.7081e3db-2301-4308-b93d-61cf78b25816","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"SQLResult\""]}}]},{"targetRef":"2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.ec350cc0-a909-411a-b0c2-96e08b779c85","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.a5856f85-7a86-4327-9481-b1df1c075ff9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_APP"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"84cdfe89-c26f-4e1c-87b0-eca265551561","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.sqlResults"]}}],"sourceRef":["2055.21cdb854-d222-4fc8-b991-17aef09de0c4"]}],"calledElement":"1.8ca80af0-a727-4b90-9e04-21b32cd0c65c"},{"targetRef":"3c657226-3c84-4938-812b-b4e36c01ea43","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Map output","declaredType":"sequenceFlow","id":"519e6a46-10e9-4a48-8c53-d05a28c6f4a8","sourceRef":"84cdfe89-c26f-4e1c-87b0-eca265551561"},{"startQuantity":1,"outgoing":["*************-409e-8925-1d6ff8b643b0"],"incoming":["2027.2dd5aef2-4ac0-4734-841e-55e63549b5d7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":114,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Prepare Query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"5b3aae23-ece8-45dd-8ddf-11d4d19d94c1","scriptFormat":"text\/plain","script":{"content":["tw.local.query\nselect * from &lt;#=tw.local.data#&gt;\r\n\r\n\r\n\r\n\r\n\r\n"]}},{"targetRef":"84cdfe89-c26f-4e1c-87b0-eca265551561","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Execute","declaredType":"sequenceFlow","id":"*************-409e-8925-1d6ff8b643b0","sourceRef":"5b3aae23-ece8-45dd-8ddf-11d4d19d94c1"},{"startQuantity":1,"outgoing":["720f5dc2-3633-458a-8109-68cac7ee11a1"],"incoming":["ae916b9d-2889-4929-8a64-b9a2c58f5af6"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":707,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"08b86076-abbc-45f2-8270-1762810a6c71","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = new tw.object.listOf.NameValuePair();\r\nif(tw.local.sqlResults != null)\r\n{\r\n\tfor(var i=0;i&lt;tw.local.sqlResults[0].rows.listLength;i++)\r\n\t{\r\n\t\ttw.local.results[i] = new tw.object.NameValuePair()\r\n\t\ttw.local.results[i].name = tw.local.sqlResults[0].rows[i].data[1];\r\n\t\ttw.local.results[i].value = tw.local.sqlResults[0].rows[i].data[2];\r\n\t}\r\n\t\r\n}\r\n\r\n"]}},{"targetRef":"6a6f241f-2873-4df9-89c1-c1d7fd6c8af9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"720f5dc2-3633-458a-8109-68cac7ee11a1","sourceRef":"08b86076-abbc-45f2-8270-1762810a6c71"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"query","isCollection":false,"declaredType":"dataObject","id":"2056.41b991f9-22b6-4ab2-873c-8d2824b1ede2"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"sqlResults","isCollection":true,"declaredType":"dataObject","id":"2056.82a7250e-a015-40e5-83d6-69978dacdd6e"},{"outgoing":["ae916b9d-2889-4929-8a64-b9a2c58f5af6","d7f6e510-50b5-49c7-83e3-9d29022c1082"],"incoming":["519e6a46-10e9-4a48-8c53-d05a28c6f4a8"],"default":"ae916b9d-2889-4929-8a64-b9a2c58f5af6","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":488,"y":77,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is currency table?","declaredType":"exclusiveGateway","id":"3c657226-3c84-4938-812b-b4e36c01ea43"},{"targetRef":"08b86076-abbc-45f2-8270-1762810a6c71","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Map output","declaredType":"sequenceFlow","id":"ae916b9d-2889-4929-8a64-b9a2c58f5af6","sourceRef":"3c657226-3c84-4938-812b-b4e36c01ea43"},{"startQuantity":1,"outgoing":["e445c3eb-dce3-4275-844f-a999bddccd2a"],"incoming":["d7f6e510-50b5-49c7-83e3-9d29022c1082"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":726,"y":186,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map currencies","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"090daaad-07d1-4796-896a-602fd7598793","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = new tw.object.listOf.NameValuePair();\r\nif(tw.local.sqlResults != null)\r\n{\r\n\tfor(var i=0;i&lt;tw.local.sqlResults[0].rows.listLength;i++)\r\n\t{\r\n\t\ttw.local.results[i] = new tw.object.NameValuePair()\r\n\t\ttw.local.results[i].name = tw.local.sqlResults[0].rows[i].data[1];\r\n\t\ttw.local.results[i].value = tw.local.sqlResults[0].rows[i].data[1];\r\n\t}\r\n\t\r\n}\r\n\r\n"]}},{"targetRef":"090daaad-07d1-4796-896a-602fd7598793","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.data\t  ==\t  \"BPM.IDC_CURRENCY\""]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Map currencies","declaredType":"sequenceFlow","id":"d7f6e510-50b5-49c7-83e3-9d29022c1082","sourceRef":"3c657226-3c84-4938-812b-b4e36c01ea43"},{"targetRef":"6a6f241f-2873-4df9-89c1-c1d7fd6c8af9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e445c3eb-dce3-4275-844f-a999bddccd2a","sourceRef":"090daaad-07d1-4796-896a-602fd7598793"}],"laneSet":[{"id":"151af834-af62-4e96-8516-e3dea90220a6","lane":[{"flowNodeRef":["ff0df4eb-8368-48d3-8b79-c54ebec943cd","6a6f241f-2873-4df9-89c1-c1d7fd6c8af9","84cdfe89-c26f-4e1c-87b0-eca265551561","5b3aae23-ece8-45dd-8ddf-11d4d19d94c1","08b86076-abbc-45f2-8270-1762810a6c71","3c657226-3c84-4938-812b-b4e36c01ea43","090daaad-07d1-4796-896a-602fd7598793"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"0ad2d7ed-877f-4e49-8f1d-ba2515d20c1b","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"retrieve DB lookups","declaredType":"process","id":"1.2f93c4b5-368e-4a13-aff6-b12926260bb3","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.91436cf4-7a08-4359-8624-bbfa8c1147c0"}],"inputSet":[{"dataInputRefs":["2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e"]}],"outputSet":[{"dataOutputRefs":["2055.91436cf4-7a08-4359-8624-bbfa8c1147c0"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"BPM.ODC_Execution_Hubs\"\r\n\/\/\"BPM.ODC_Account_Class\"\r\n\/\/\"BPM.ODC_Remittance_Letter_Title\"\r\n\/\/\"BPM.IDC_EXECUTION_HUBs\"\r\n\/\/\"BPM.ODC_Export_Product_Category\"\r\n\/\/\/\/\"BPM.ODC_Account_Class\"\r\n\/\/\"BPM.IDC_CURRENCY\"\r\n\/\/\/\/odcLookupsTable.PaymentTerms\r\n\/\/\"BPM.ODC_Execution_Hubs\"\r\n\/\/\"BPM.ODC_Export_Purpose\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e</processParameterId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"BPM.ODC_Execution_Hubs"&#xD;
//"BPM.ODC_Account_Class"&#xD;
//"BPM.ODC_Remittance_Letter_Title"&#xD;
//"BPM.IDC_EXECUTION_HUBs"&#xD;
//"BPM.ODC_Export_Product_Category"&#xD;
////"BPM.ODC_Account_Class"&#xD;
//"BPM.IDC_CURRENCY"&#xD;
////odcLookupsTable.PaymentTerms&#xD;
//"BPM.ODC_Execution_Hubs"&#xD;
//"BPM.ODC_Export_Purpose"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>edcd53c1-7c50-4c9a-9e72-e80ab80e02d1</guid>
            <versionId>686427fe-9f7a-4588-b60f-0860f1c188c9</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.91436cf4-7a08-4359-8624-bbfa8c1147c0</processParameterId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c76190c6-7443-4ff3-9fe6-0c5a2aa98f9f</guid>
            <versionId>e3a69d15-9311-4fdc-a5d3-1c23869fb1ce</versionId>
        </processParameter>
        <processVariable name="query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.41b991f9-22b6-4ab2-873c-8d2824b1ede2</processVariableId>
            <description isNull="true" />
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a6835af0-d1fb-4839-951a-8368aa47a4e1</guid>
            <versionId>b4958a00-f803-44ae-83c3-df4b7777243b</versionId>
        </processVariable>
        <processVariable name="sqlResults">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.82a7250e-a015-40e5-83d6-69978dacdd6e</processVariableId>
            <description isNull="true" />
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0f2c0008-74d8-48f5-8538-7521a7582096</guid>
            <versionId>bc87820c-f8b5-4cdb-a7dc-701980b96caa</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.84cdfe89-c26f-4e1c-87b0-eca265551561</processItemId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <name>Execute</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1f15</guid>
            <versionId>142226f8-0641-4ff8-b873-95fda2a79a2a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="318" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.8ca80af0-a727-4b90-9e04-21b32cd0c65c</attachedProcessRef>
                <guid>a126aeb0-df64-48ce-8d1e-5d12726b4982</guid>
                <versionId>4c2827d9-8494-4bd1-994c-78978c1c78c5</versionId>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.97b3ed57-c406-4509-a7f6-9d4f3d2c454b</parameterMappingId>
                    <processParameterId>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</processParameterId>
                    <parameterMappingParentId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.query</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6ca7dc50-71ca-4c6d-b97e-a0c63958c1b6</guid>
                    <versionId>1de21612-4de7-4cde-9756-cf92095f446a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9670325d-ef09-4b06-8ccb-988366e88c09</parameterMappingId>
                    <processParameterId>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</processParameterId>
                    <parameterMappingParentId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_APP</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>704fb184-53c9-4bcb-a8fc-deb42dde3da7</guid>
                    <versionId>4ec09df5-d20e-46bb-9053-11a57704fcb4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1a57b2aa-0835-42eb-954c-1b552c79faa3</parameterMappingId>
                    <processParameterId>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</processParameterId>
                    <parameterMappingParentId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>26948d45-10f5-4095-8040-e2d9336c8129</guid>
                    <versionId>8950faf4-90f1-4f6b-becf-b8f8ec62bc66</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="returnType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.37c69d9e-45ea-4175-9e48-512dafe7b09f</parameterMappingId>
                    <processParameterId>2055.7081e3db-2301-4308-b93d-61cf78b25816</processParameterId>
                    <parameterMappingParentId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"SQLResult"</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a2f59803-b661-4d6e-b19b-31f7d74c96f0</guid>
                    <versionId>9ade166c-d6f8-4c09-9129-90773a0b2957</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5c251277-4c2e-4b8c-85e0-59943bcdb4c9</parameterMappingId>
                    <processParameterId>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</processParameterId>
                    <parameterMappingParentId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlResults</value>
                    <classRef>/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a2e27e97-2b14-452e-81ff-6c7a020ae95a</guid>
                    <versionId>9e3e2340-5c9d-4cb2-88ac-c4e91077d3c7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dc84065e-516d-4ea7-b207-6c9247728502</parameterMappingId>
                    <processParameterId>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</processParameterId>
                    <parameterMappingParentId>3012.b799d81e-9872-4fb6-9682-36d73427d0e4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f01f08a8-4eca-4003-b85f-16fc17e2ef67</guid>
                    <versionId>d3b648dc-c833-4300-875b-dd80edb4567b</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.08b86076-abbc-45f2-8270-1762810a6c71</processItemId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4c025173-61eb-457f-a3c6-9212a9dc44c4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1f17</guid>
            <versionId>93de2a6a-3165-4d2d-b7b7-067bea5bacf4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="707" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4c025173-61eb-457f-a3c6-9212a9dc44c4</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(tw.local.sqlResults != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.sqlResults[0].rows.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = new tw.object.NameValuePair()&#xD;
		tw.local.results[i].name = tw.local.sqlResults[0].rows[i].data[1];&#xD;
		tw.local.results[i].value = tw.local.sqlResults[0].rows[i].data[2];&#xD;
	}&#xD;
	&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>745bb6e8-8481-4ed8-84a8-1d0a5493736c</guid>
                <versionId>d3144703-eb88-47cc-b809-42bb9997a247</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3c657226-3c84-4938-812b-b4e36c01ea43</processItemId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <name>is currency table?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.2b81d2fb-b22a-499e-9530-c5349f249ad1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bcdaa728b:-7eee</guid>
            <versionId>a2f83b97-651a-43bf-b4dd-a22e7313d2c3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="488" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.2b81d2fb-b22a-499e-9530-c5349f249ad1</switchId>
                <guid>63e1bbb3-1f7d-4bd1-b932-871616bd7ea4</guid>
                <versionId>65e535a5-9ee6-4b5c-8e3a-3fc18eb28872</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.b654a814-8c19-43dc-937b-6afaac6826bc</switchConditionId>
                    <switchId>3013.2b81d2fb-b22a-499e-9530-c5349f249ad1</switchId>
                    <seq>1</seq>
                    <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-2e92</endStateId>
                    <condition>tw.local.data	  ==	  "BPM.IDC_CURRENCY"</condition>
                    <guid>3d323af9-e632-4b21-92e9-675b799773fe</guid>
                    <versionId>a356cbb3-6d87-45ee-abf6-44969472298c</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.090daaad-07d1-4796-896a-602fd7598793</processItemId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <name>Map currencies</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.96cecc4a-4b94-4261-b7b6-0011c51d16b5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bcdaa728b:-7eef</guid>
            <versionId>aa198976-a42a-4176-99c9-c8ac5fdb02ea</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="726" y="186">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.96cecc4a-4b94-4261-b7b6-0011c51d16b5</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(tw.local.sqlResults != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.sqlResults[0].rows.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = new tw.object.NameValuePair()&#xD;
		tw.local.results[i].name = tw.local.sqlResults[0].rows[i].data[1];&#xD;
		tw.local.results[i].value = tw.local.sqlResults[0].rows[i].data[1];&#xD;
	}&#xD;
	&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>e5f55d24-dabe-4b79-a4ad-54d1011bbaec</guid>
                <versionId>bc91cd34-59fc-4076-aa77-6480051dbbb7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5b3aae23-ece8-45dd-8ddf-11d4d19d94c1</processItemId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <name>Prepare Query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9909b0d2-ba9e-4bb7-92cd-5adabc42a4bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1f16</guid>
            <versionId>ad4eec85-47ef-4de9-b512-89647592c4ae</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="114" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9909b0d2-ba9e-4bb7-92cd-5adabc42a4bb</scriptId>
                <scriptTypeId>128</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.query
select * from &lt;#=tw.local.data#&gt;&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>fa2d11d0-a514-42cb-8dba-04a88e92c32a</guid>
                <versionId>f67a2544-768b-4f86-936f-73ec4b66b34b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6a6f241f-2873-4df9-89c1-c1d7fd6c8af9</processItemId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.e4a4eb50-4a9a-4f99-804e-fc9529e6b110</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24</guid>
            <versionId>da003dc2-0f7c-4b12-84b6-da808f260f41</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="890" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.e4a4eb50-4a9a-4f99-804e-fc9529e6b110</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>5af48d90-e459-40df-bb9a-7dff38f0176b</guid>
                <versionId>38896cdf-2209-428b-bd3e-ddf9935258b3</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.5b3aae23-ece8-45dd-8ddf-11d4d19d94c1</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="retrieve DB lookups" id="1.2f93c4b5-368e-4a13-aff6-b12926260bb3" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"BPM.ODC_Execution_Hubs"&#xD;
//"BPM.ODC_Account_Class"&#xD;
//"BPM.ODC_Remittance_Letter_Title"&#xD;
//"BPM.IDC_EXECUTION_HUBs"&#xD;
//"BPM.ODC_Export_Product_Category"&#xD;
////"BPM.ODC_Account_Class"&#xD;
//"BPM.IDC_CURRENCY"&#xD;
////odcLookupsTable.PaymentTerms&#xD;
//"BPM.ODC_Execution_Hubs"&#xD;
//"BPM.ODC_Export_Purpose"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.91436cf4-7a08-4359-8624-bbfa8c1147c0" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.91436cf4-7a08-4359-8624-bbfa8c1147c0</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="151af834-af62-4e96-8516-e3dea90220a6">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="0ad2d7ed-877f-4e49-8f1d-ba2515d20c1b" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>ff0df4eb-8368-48d3-8b79-c54ebec943cd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6a6f241f-2873-4df9-89c1-c1d7fd6c8af9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>84cdfe89-c26f-4e1c-87b0-eca265551561</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5b3aae23-ece8-45dd-8ddf-11d4d19d94c1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>08b86076-abbc-45f2-8270-1762810a6c71</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3c657226-3c84-4938-812b-b4e36c01ea43</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>090daaad-07d1-4796-896a-602fd7598793</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="ff0df4eb-8368-48d3-8b79-c54ebec943cd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.2dd5aef2-4ac0-4734-841e-55e63549b5d7</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="6a6f241f-2873-4df9-89c1-c1d7fd6c8af9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="890" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>720f5dc2-3633-458a-8109-68cac7ee11a1</ns16:incoming>
                        
                        
                        <ns16:incoming>e445c3eb-dce3-4275-844f-a999bddccd2a</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="ff0df4eb-8368-48d3-8b79-c54ebec943cd" targetRef="5b3aae23-ece8-45dd-8ddf-11d4d19d94c1" name="To Prepare Query" id="2027.2dd5aef2-4ac0-4734-841e-55e63549b5d7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8ca80af0-a727-4b90-9e04-21b32cd0c65c" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Execute" id="84cdfe89-c26f-4e1c-87b0-eca265551561">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="318" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>*************-409e-8925-1d6ff8b643b0</ns16:incoming>
                        
                        
                        <ns16:outgoing>519e6a46-10e9-4a48-8c53-d05a28c6f4a8</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e3bfe8f3-c801-4ef8-bb77-77156e3b0326</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.query</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.7081e3db-2301-4308-b93d-61cf78b25816</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"SQLResult"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b3c4f58-67b5-49b0-8df4-bdb407fbe076</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ec350cc0-a909-411a-b0c2-96e08b779c85</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5856f85-7a86-4327-9481-b1df1c075ff9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_APP</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.21cdb854-d222-4fc8-b991-17aef09de0c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.sqlResults</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="84cdfe89-c26f-4e1c-87b0-eca265551561" targetRef="3c657226-3c84-4938-812b-b4e36c01ea43" name="To Map output" id="519e6a46-10e9-4a48-8c53-d05a28c6f4a8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/plain" name="Prepare Query" id="5b3aae23-ece8-45dd-8ddf-11d4d19d94c1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="114" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.2dd5aef2-4ac0-4734-841e-55e63549b5d7</ns16:incoming>
                        
                        
                        <ns16:outgoing>*************-409e-8925-1d6ff8b643b0</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.query
select * from &lt;#=tw.local.data#&gt;&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="5b3aae23-ece8-45dd-8ddf-11d4d19d94c1" targetRef="84cdfe89-c26f-4e1c-87b0-eca265551561" name="To Execute" id="*************-409e-8925-1d6ff8b643b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="08b86076-abbc-45f2-8270-1762810a6c71">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="707" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ae916b9d-2889-4929-8a64-b9a2c58f5af6</ns16:incoming>
                        
                        
                        <ns16:outgoing>720f5dc2-3633-458a-8109-68cac7ee11a1</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(tw.local.sqlResults != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.sqlResults[0].rows.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = new tw.object.NameValuePair()&#xD;
		tw.local.results[i].name = tw.local.sqlResults[0].rows[i].data[1];&#xD;
		tw.local.results[i].value = tw.local.sqlResults[0].rows[i].data[2];&#xD;
	}&#xD;
	&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="08b86076-abbc-45f2-8270-1762810a6c71" targetRef="6a6f241f-2873-4df9-89c1-c1d7fd6c8af9" name="To End" id="720f5dc2-3633-458a-8109-68cac7ee11a1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="query" id="2056.41b991f9-22b6-4ab2-873c-8d2824b1ede2" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="sqlResults" id="2056.82a7250e-a015-40e5-83d6-69978dacdd6e" />
                    
                    
                    <ns16:exclusiveGateway default="ae916b9d-2889-4929-8a64-b9a2c58f5af6" name="is currency table?" id="3c657226-3c84-4938-812b-b4e36c01ea43">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="488" y="77" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>519e6a46-10e9-4a48-8c53-d05a28c6f4a8</ns16:incoming>
                        
                        
                        <ns16:outgoing>ae916b9d-2889-4929-8a64-b9a2c58f5af6</ns16:outgoing>
                        
                        
                        <ns16:outgoing>d7f6e510-50b5-49c7-83e3-9d29022c1082</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="3c657226-3c84-4938-812b-b4e36c01ea43" targetRef="08b86076-abbc-45f2-8270-1762810a6c71" name="To Map output" id="ae916b9d-2889-4929-8a64-b9a2c58f5af6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map currencies" id="090daaad-07d1-4796-896a-602fd7598793">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="726" y="186" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d7f6e510-50b5-49c7-83e3-9d29022c1082</ns16:incoming>
                        
                        
                        <ns16:outgoing>e445c3eb-dce3-4275-844f-a999bddccd2a</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(tw.local.sqlResults != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.sqlResults[0].rows.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = new tw.object.NameValuePair()&#xD;
		tw.local.results[i].name = tw.local.sqlResults[0].rows[i].data[1];&#xD;
		tw.local.results[i].value = tw.local.sqlResults[0].rows[i].data[1];&#xD;
	}&#xD;
	&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="3c657226-3c84-4938-812b-b4e36c01ea43" targetRef="090daaad-07d1-4796-896a-602fd7598793" name="To Map currencies" id="d7f6e510-50b5-49c7-83e3-9d29022c1082">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.data	  ==	  "BPM.IDC_CURRENCY"</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="090daaad-07d1-4796-896a-602fd7598793" targetRef="6a6f241f-2873-4df9-89c1-c1d7fd6c8af9" name="To End" id="e445c3eb-dce3-4275-844f-a999bddccd2a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e445c3eb-dce3-4275-844f-a999bddccd2a</processLinkId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.090daaad-07d1-4796-896a-602fd7598793</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6a6f241f-2873-4df9-89c1-c1d7fd6c8af9</toProcessItemId>
            <guid>62353046-50cd-4c46-be90-8cc27ade4080</guid>
            <versionId>0c2ef816-9447-4c78-98a5-ea962663dff2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.090daaad-07d1-4796-896a-602fd7598793</fromProcessItemId>
            <toProcessItemId>2025.6a6f241f-2873-4df9-89c1-c1d7fd6c8af9</toProcessItemId>
        </link>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.519e6a46-10e9-4a48-8c53-d05a28c6f4a8</processLinkId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.84cdfe89-c26f-4e1c-87b0-eca265551561</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:-9d2f0f9:11586bfc263:-7f6f</endStateId>
            <toProcessItemId>2025.3c657226-3c84-4938-812b-b4e36c01ea43</toProcessItemId>
            <guid>e212ccdb-ab8b-4450-b8bd-557ca42e67af</guid>
            <versionId>45097fab-845d-43b8-acc7-8c58975feb45</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.84cdfe89-c26f-4e1c-87b0-eca265551561</fromProcessItemId>
            <toProcessItemId>2025.3c657226-3c84-4938-812b-b4e36c01ea43</toProcessItemId>
        </link>
        <link name="To Execute">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.*************-409e-8925-1d6ff8b643b0</processLinkId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5b3aae23-ece8-45dd-8ddf-11d4d19d94c1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.84cdfe89-c26f-4e1c-87b0-eca265551561</toProcessItemId>
            <guid>af891799-c944-4c49-aa94-9831901f2c08</guid>
            <versionId>5c7a80df-fdbf-4ea4-a9ff-70aed8c4bf67</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5b3aae23-ece8-45dd-8ddf-11d4d19d94c1</fromProcessItemId>
            <toProcessItemId>2025.84cdfe89-c26f-4e1c-87b0-eca265551561</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.720f5dc2-3633-458a-8109-68cac7ee11a1</processLinkId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.08b86076-abbc-45f2-8270-1762810a6c71</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6a6f241f-2873-4df9-89c1-c1d7fd6c8af9</toProcessItemId>
            <guid>1974f55b-**************-b052f08630f0</guid>
            <versionId>793a442a-502c-4e14-a8f8-9da8b02b82f4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.08b86076-abbc-45f2-8270-1762810a6c71</fromProcessItemId>
            <toProcessItemId>2025.6a6f241f-2873-4df9-89c1-c1d7fd6c8af9</toProcessItemId>
        </link>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ae916b9d-2889-4929-8a64-b9a2c58f5af6</processLinkId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3c657226-3c84-4938-812b-b4e36c01ea43</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.08b86076-abbc-45f2-8270-1762810a6c71</toProcessItemId>
            <guid>7c6ec51d-12b1-4544-994c-ce1e1d0f69a5</guid>
            <versionId>bc64e1f2-ae39-459e-9275-23269ccff4fd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3c657226-3c84-4938-812b-b4e36c01ea43</fromProcessItemId>
            <toProcessItemId>2025.08b86076-abbc-45f2-8270-1762810a6c71</toProcessItemId>
        </link>
        <link name="To Map currencies">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d7f6e510-50b5-49c7-83e3-9d29022c1082</processLinkId>
            <processId>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3c657226-3c84-4938-812b-b4e36c01ea43</fromProcessItemId>
            <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-2e92</endStateId>
            <toProcessItemId>2025.090daaad-07d1-4796-896a-602fd7598793</toProcessItemId>
            <guid>0ae33326-2c20-48af-90e6-e7568fe81a8e</guid>
            <versionId>d1c70c08-9d06-4fe1-a622-ac7fd50a41a0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3c657226-3c84-4938-812b-b4e36c01ea43</fromProcessItemId>
            <toProcessItemId>2025.090daaad-07d1-4796-896a-602fd7598793</toProcessItemId>
        </link>
    </process>
</teamworks>

