{"id": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "versionId": "64571bed-66b5-41fa-87ad-9a9c8819f9f3", "name": "Contract Liquidation", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (this.context.options.isChecker.get(\"value\")) {\r\r\n\r\r\n\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(false);\r\r\n\tthis.ui.get(\"debitedAccountNo1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(false);\r\r\n}else{\r\r\n//\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(true);\r\r\n//\tthis.ui.get(\"debitedAccountNo1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(true);\r\r\n}", "bindingType": "odcRequest", "configOptions": ["glAccountVerified", "contractLiqVis", "customerAccounts", "exchangeRate", "<PERSON><PERSON><PERSON><PERSON>", "contractLiquidatedMSG", "verifyGLMsg"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//Validate Debited Nostro/Vostro Account\t\t\t\t\t\t\t\t\t\t\r\r\n this.valiadateAccCurrency = function (value)\r\r\n {\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo   = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\");\t\r\r\n\tvar currency    = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\tvar liqCurency  = this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"liqCurrency\");\t\r\r\n\t\r\r\n\tif (accountNo == value.getData() && currency != liqCurency) { \r\r\n//\t\t value.setData(\"\");\r\r\n//\t\t this.ui.get( \"debitedAccountNo1\").setValid(false,\"This account's currency must be the same as liquidation currency\");\t\r\r\n \tbreak;\r\r\n \t}\r\r\n\telse\r\r\n\t\t this.ui.get( \"debitedAccountNo1\").setValid(true);\t\r\r\n\t}//end of for\r\r\n}\r\r\n\r\r\n//Init Credit Account Data\r\r\nthis.setAccountInfo = function (value)\r\r\n {\r\r\n\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo= this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") ? this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") : \"\";\r\r\n\t\tif (accountNo == value.getData()) { \r\r\n\t\t\tvar branchCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"branchCode\");\r\r\n\t\t\tvar currency = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\t\t\tvar balance = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balance\");\r\r\n\t\t\tvar balanceSign = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balanceType\");\r\r\n\t\t\tvar classCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t \t\tclassCode= classCode? classCode.substring(0,1):\"\";\r\r\n\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"branchCode\", branchCode);\r\r\n\t \t\t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balance\", balance);\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balanceSign\", balanceSign);\r\r\n//\t \t\tconsole.log(\"------------------classCode: \"+classCode);\r\r\n\t \t\tif(classCode == \"O\" || classCode == \"D\")\r\r\n\t \t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",true);\r\r\n\t\t\telse\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",false);\r\r\n\t\t\t\t\t\t\r\r\n//\t\t\tvar od= this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n//\t\t\tconsole.log(\"------------------is over draft : \"+od);\t\t \t\t\r\r\n\t \t\t//SET CURRENCY\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"currency\", {} );\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"name\", currency);\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"value\", currency);\r\r\n\t\t\t\r\r\n\t\t\t//SET EXCHANGE RATE\r\r\n//\t\t\tview.setNegoRate(currency);\t\r\r\n \t\t}\r\r\n\t}\r\r\n}\r\r\n //Set credited Amount\r\r\n this.setNegoRate = function(value)\r\r\n {\t\r\r\n\r\r\n//\tconsole.log(\"----------------------INSIDE setNegoRate\");\r\r\n  \tvar liqCurr   =  this.ui.get(\"LiquidateionCurrency\").getData()? this.ui.get(\"LiquidateionCurrency\").getData():\"\";  \t\t \t\r\r\n  \tvar accCurr   =  value.getData()? value.getData():\"\"; \t \t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr == accCurr ) ) { \r\r\n\t\tthis.ui.get(\"standardExRate\").setData(1);\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setData(1);\r\r\n  \t\tthis.ui.get(\"negotiatedExRate\").setEnabled(false); \t\r\r\n \t} \t\t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr != accCurr ) ){\r\r\n//\t\t//call get exchange rate service\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n//\t\t console.log(\"---------- liqCurr != accCurr -------------------------\");\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setEnabled(true);\r\r\n//  \t\tvar inputCurrency={fromCurrency : liqCurr , toCurrency : accCurr};\r\r\n//  \t\tconsole.log(\"----------inputCurrency \"+inputCurrency);\r\r\n  \t\t\r\r\n//  \t\tvar parsedInputCurrency =  JSON.stringify(inputCurrency);\r\r\n//  \t\tconsole.log(\"----------parsedInputCurrency=   \"+parsedInputCurrency);\r\r\n//     \tconsole.log(\"*******************************************************************\");\r\r\n//\t\talert(parsedInputCurrency);\r\r\n//\t\tthis.ui.get(\"SC_getExRate\").execute(parsedInputCurrency);\r\r\n\t\t\r\r\n\t\tconcatedCurrency = {ccFrom : liqCurr , ccTo : accCurr , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\tinputCurr = JSON.stringify(concatedCurrency);\r\r\n\r\r\n\t\tthis.ui.get(\"GetExchangeRate\").execute(inputCurr);\r\r\n \t}\t\t\t\r\r\n}\r\r\n//SET Credited Amount From Exchange Service\r\r\n this.SetCreditAmountDetails = function(value){\r\r\n\r\r\n\tconsole.log(\"----------------------INSIDE SetCreditAmountDetails\");\r\r\n\tvar exRate = this.context.options.exchangeRate.get(\"value\");\r\r\n\r\r\n\tconsole.log(\"--------------- rate \"+ exRate);\r\r\n\tconsole.log(\"---------------ExRate fixed =  \"+ exRate.toFixed(6));\r\r\n\t\r\r\n\tthis.ui.get(\"standardExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\t\r\r\n\r\r\n } \r\r\n//calculate credited amount in cuurency\r\r\nthis.setAmountInCurrency = function(){\r\r\n\tvar liqAmount  =  this.ui.get(\"LiquidationAmount\").getData()? this.ui.get(\"LiquidationAmount\").getData():0;\r\r\n\tvar negoExRate =  this.ui.get(\"negotiatedExRate\").getData()? this.ui.get(\"negotiatedExRate\").getData():0;\r\r\n\t\r\r\n\tif( liqAmount > 0   &&   negoExRate > 0 )\r\r\n\t\tthis.ui.get(\"creditedAmountinAccCurrency\").setData(liqAmount * negoExRate);\r\r\n}\r\r\n\r\r\n//validate overDraft\r\r\nthis.validateOverDraft = function () {\r\r\n\tvar AccBalance          =  this.ui.get(\"AccountBalance\").getData()? this.ui.get(\"AccountBalance\").getData() : 0;\r\r\n\tvar amountinAccCurrency =  this.ui.get(\"creditedAmountinAccCurrency\").getData()? this.ui.get(\"creditedAmountinAccCurrency\").getData() : 0;\r\r\n\tvar overDraft           =  this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n\t\r\r\n\tif(amountinAccCurrency > AccBalance){\r\r\n\t\tif (overDraft) \r\r\n\t\t\t{\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"WARNING: Amount in account currency should be less than Account Balance\");\r\r\n\t\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"ERROR: Amount in Account Currency > Account Balance and Account Type is not Overdraft\");\r\r\n\t\t\t}\r\r\n\t\r\r\n\t}else{\r\r\n\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(true);\r\r\n\t}//end of if\r\r\n\t\r\r\n}\r\r\n\r\r\n//On load of account class - set vis according to account class (customer & gl)\r\r\nthis.accountNumVis = function (value) {\r\r\n    var accountClass = value.getData().name;\r\r\n    \r\r\n    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n        if (accountClass == \"Customer Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true,true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true,true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n\r\r\n        } else if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(true);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false,true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false,true);\r\r\n\r\r\n        }\r\r\n    } else {\r\r\n        this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n        this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n        \r\r\n        if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"glAccountNo\").setEnabled(false);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n   \r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false, true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false, true);\r\r\n            \r\r\n        } else {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\t\tthis.ui.get(\"customerAccountNo\").setEnabled(false);\r\r\n\t\t\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true, true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n        }\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of account class - reset data before choosing another account\r\r\nthis.resetAccountInfo = function (value){\r\r\n    this.ui.get(\"glAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"customerAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"AccountBalance\").setData(\"\");\r\r\n    this.ui.get(\"AccountBranchCode\").setData(\"\");\r\r\n    this.ui.get(\"BalanceSign\").setData(\"\");\r\r\n    this.ui.get(\"accountCurrency\").setData(\"\");\r\r\n    \r\r\n    this.ui.get(\"standardExRate\").setData(\"\");\r\r\n    this.ui.get(\"creditedAmountinAccCurrency\").setData(\"\");\r\r\n    this.ui.get(\"negotiatedExRate\").setData(\"\");\r\r\n    this.ui.get(\"verifiedText\").setData(\"\");\r\r\n    \r\r\n    this.accountNumVis(value);\r\r\n}"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "name": "Contract Liquidation", "lastModified": "*************", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isTemplate": "false", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>64008dad-b373-44c9-8a49-2cfa730e00d4</ns2:id><ns2:layoutItemId>Pnl_ContractLiquidation</ns2:layoutItemId><ns2:configData><ns2:id>9eb6ce8f-7d47-4321-82df-afc2f093651e</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Contract Liquidation</ns2:value></ns2:configData><ns2:configData><ns2:id>5b2a8b52-56b4-45fa-82f5-06be9bf86d3f</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>f0286872-0b23-40c1-8212-a112083428e2</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>06b0e628-48e1-4e09-8ff6-5e0399711d3c</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>ec7c5e89-3eef-4583-87de-6c8ab599ec39</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>b3049375-b305-43d3-8f8c-a1b9ad61f625</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>19128fcb-79f8-48eb-8d90-0dda7a561f1c</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>f31a7b24-0f79-49ba-8a52-cecce3cc20ed</ns2:id><ns2:layoutItemId>VerifyLiquidationCompleted</ns2:layoutItemId><ns2:configData><ns2:id>7eda8c0e-6e71-4de6-8ace-b0d4bc3f6b51</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Verify Liquidation Completed</ns2:value></ns2:configData><ns2:configData><ns2:id>f1be446e-cb01-410d-831e-1df30662361c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>9c0e70b2-324a-4d50-8c7f-a36412046878</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>HIDE</ns2:value></ns2:configData><ns2:configData><ns2:id>49d2908f-ec0e-496f-8e98-83b6a3914462</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>87e290b5-a1d0-490b-8455-1fd394d41495</ns2:id><ns2:optionName>weightStyle</ns2:optionName><ns2:value>M</ns2:value></ns2:configData><ns2:configData><ns2:id>6769d24f-ae6d-42bb-863e-95be6b24dfa6</ns2:id><ns2:optionName>textAlignment</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"L\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>9ba9b9cd-f299-4204-828d-f91714288e08</ns2:id><ns2:optionName>labelPlacement</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"T\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>8a3544f9-51d1-4f9f-8c34-3daf6a737a6f</ns2:id><ns2:optionName>textWrap</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"N\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>49df585f-d637-4feb-858d-3054af368f99</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"L\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>3dda244e-cb12-4ab3-891f-9effabddcfd3</ns2:id><ns2:optionName>labelWeightNormal</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":false}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>e0989184-a81a-41c6-8292-0db4e981d7a6</ns2:id><ns2:optionName>@margin</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>71228a14-b0ed-4450-8976-97d1f58f20d8</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>0f87e9a6-c935-4866-80e9-1dac0a34f1a7</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"20%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID><ns2:binding>tw.options.contractLiquidatedMSG</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>ac2f8d8f-746b-4e3c-8682-0d9d98f8cff4</ns2:id><ns2:layoutItemId>Horizontal_Layout3</ns2:layoutItemId><ns2:configData><ns2:id>089a1f0d-f929-4ed2-8650-8aafc412b536</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 3</ns2:value></ns2:configData><ns2:configData><ns2:id>f3300722-161f-4042-8107-c3086ff37831</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>aff1f342-88f6-4c91-865f-83a22a40cea9</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>0c586275-1681-4e3b-8a07-4645fe76622a</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>6c4d1f46-ca44-4a36-86f1-73c11d016d23</ns2:id><ns2:layoutItemId>informCADAboutTheCollection1</ns2:layoutItemId><ns2:configData><ns2:id>335b3ed1-7a61-48a8-8f41-94ec3d46b0f0</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Inform CAD About The Collection</ns2:value></ns2:configData><ns2:configData><ns2:id>84b3bf0d-0510-4979-8d93-08d8fcd83628</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>bf116bae-71eb-471e-8f6f-81437c9794c4</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"0px 0px 0px 5px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>44dd2b70-1ca9-4eda-8e8e-0e6d5d74c25e</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.contractLiqVis</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.fffd1628-baee-44e8-b7ca-5ae48644b0be</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.OdcCollection.informCADAboutTheCollection</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>017b7adc-a02f-493d-87d4-ca2dcd32f1b5</ns2:id><ns2:layoutItemId>pnl_liqSummary</ns2:layoutItemId><ns2:configData><ns2:id>0bf9bee2-8ffb-4692-8ef6-46b58ec503bf</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Liquidation Summary</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>1b299696-77b7-4bc4-8b75-67cfdac1644c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>29f2e6f0-2750-47a8-89e8-cfce1577a3b7</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>f07d10b7-b399-46fe-80de-7006d09dc047</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>0d277039-900c-4782-8873-a48a76c82e7d</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.contractLiqVis</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>2b43c343-23c4-4056-8623-85bdba368c9f</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>aa7ae6d7-95df-449d-8626-a293e7def208</ns2:id><ns2:layoutItemId>HL_LiquidationSummary</ns2:layoutItemId><ns2:configData><ns2:id>2cf0eb7a-a185-4cd5-8aa7-ca7497cc62c3</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 5</ns2:value></ns2:configData><ns2:configData><ns2:id>1e3ea9a6-a83c-4843-8c10-f21456c4aa85</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>6775511b-770e-488d-8ca2-a6192557e29d</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>79f6d452-1847-4f36-8dfa-e2f8d92791b9</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>815c786e-46f8-450d-897f-931d5863e4af</ns2:id><ns2:layoutItemId>Vertical_Layout3</ns2:layoutItemId><ns2:configData><ns2:id>ec7031fa-3e0a-4b14-83b9-e103174150f5</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 6</ns2:value></ns2:configData><ns2:configData><ns2:id>0b6de49f-3c0c-4240-8b81-f039c3c98fa8</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c5d4a05b-0e10-4610-8a1d-b0c3faf2b4bd</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>ae9790ca-940e-4dd1-8083-1d941cbd1ef0</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>e22f1db5-829e-48de-8c4e-3419aec89280</ns2:id><ns2:layoutItemId>LiquidationAmount</ns2:layoutItemId><ns2:configData><ns2:id>4d26862f-0e1e-446e-a3a9-1e338c01e87a</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>2ebd61d8-81c2-4a03-837a-b178c6f208a8</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Liquidation Amount</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>bf0e6cd5-d1f7-4b50-88c6-baccd48ed21b</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>d4f08811-67ee-4acf-8948-da3423ae7dda</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>846f5b6d-eba8-44aa-84b6-bcf6dbaf1c49</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.setAmountInCurrency();</ns2:value></ns2:configData><ns2:configData><ns2:id>7cf6929c-576c-44dc-80a3-73d683938428</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.liqAmount</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d1c2b908-96af-4c8f-8077-832e65eeb246</ns2:id><ns2:layoutItemId>DebitValueDate</ns2:layoutItemId><ns2:configData><ns2:id>3f031915-9e31-4389-868b-4bcf06517bbd</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Debit Value Date</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>1e7ba6bd-3cd2-4849-85de-bc8e8764b3bc</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>47b1d34a-77db-4f6e-8a8d-50350d4edea0</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>36cccd08-c5cb-41cb-8cc1-b4dff5195b58</ns2:id><ns2:optionName>format</ns2:optionName><ns2:value>dd/MM/yyyy</ns2:value></ns2:configData><ns2:configData><ns2:id>d78f95bc-425d-486a-8241-c16f2a492294</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>268aa232-ab6a-4071-8aad-0b6e92c7a606</ns2:id><ns2:optionName>enableCalendarIcon</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>89335233-9dc4-44f5-8473-fa216a73dfe7</ns2:id><ns2:optionName>startDate</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.54643ff2-8363-4976-bb5e-d4eb1094cca3</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.debitValueDate</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>6133f216-cde3-4a02-8f3e-8aa7b6460109</ns2:id><ns2:layoutItemId>Vertical_Layout6</ns2:layoutItemId><ns2:configData><ns2:id>29689cc0-7ca0-4fc7-88a3-297a00d52529</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 7</ns2:value></ns2:configData><ns2:configData><ns2:id>143f5ff8-8540-4d23-82ef-82a786617183</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>3540eae3-f57b-468c-8865-52b0e684a717</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>88b06a98-**************-d9b35c49b21a</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>2d4f8cfb-a991-4916-8bda-6efe2395a8e3</ns2:id><ns2:layoutItemId>LiquidateionCurrency</ns2:layoutItemId><ns2:configData><ns2:id>bc89dcc9-8212-423a-81d3-793edf2237f9</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Liquidation Currency</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>0b79f0eb-d4b0-4001-8961-7dadbbfd62cc</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2a217931-e333-44e5-8017-d672a5e8876d</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>8459bb0c-8cd9-4561-8728-fa5dfee89df4</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.liqCurrency</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>cc804b0b-3fa9-4b58-8cfa-abbf32ecd643</ns2:id><ns2:layoutItemId>CreditValueDate</ns2:layoutItemId><ns2:configData><ns2:id>d476bb94-c96b-445f-8ee1-1387690cecfd</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Credit Value Date</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>ec494eb7-0865-471a-80fd-e0e96a7b4de5</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2b3351a3-e4d7-4b4b-8ff3-22c0030b4c90</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>b858bba5-8c96-4c1a-8f93-d1164c9916ff</ns2:id><ns2:optionName>format</ns2:optionName><ns2:value>dd/MM/yyyy</ns2:value></ns2:configData><ns2:configData><ns2:id>f4d5ab61-3d66-448a-8c6b-8b4bbc3fe5da</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>24c2dde9-0184-4732-8eae-eab7f939bd0c</ns2:id><ns2:optionName>enableCalendarIcon</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:viewUUID>64.54643ff2-8363-4976-bb5e-d4eb1094cca3</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditValueDate</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d09ad629-08ba-4ef1-85d5-db3bf6622976</ns2:id><ns2:layoutItemId>Collapsible_Panel1</ns2:layoutItemId><ns2:configData><ns2:id>732db181-2224-4f4e-8c2c-587a3c07aae4</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Debited Nostro/Vostro Account</ns2:value></ns2:configData><ns2:configData><ns2:id>500b9a00-5956-426a-8624-2e1ee831136e</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>d47dda74-70cc-4e99-8cbf-62901378c11e</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>e42922a8-bcb7-4c73-831f-59f78db55f24</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>386f71aa-ee9a-4349-86ca-2dc038a2d7c1</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.contractLiqVis</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>0d8f71a5-3647-478c-84d3-523ec35b07a3</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>528d6b6b-0e72-4df5-81cd-4503ad65d2ba</ns2:id><ns2:layoutItemId>debitedAccountNo1</ns2:layoutItemId><ns2:configData><ns2:id>17449be5-2fe7-41be-9142-fcddabe57464</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>446a9bd1-5e82-4f6f-8042-dbf5f0c0309c</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Number</ns2:value></ns2:configData><ns2:configData><ns2:id>aad8d974-f914-4d46-8448-f50fecff4583</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>74a0890a-880d-4797-8700-b157a6631cf2</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>B</ns2:value></ns2:configData><ns2:configData><ns2:id>2af504ca-7a2d-4b3d-88b3-1d50093ec917</ns2:id><ns2:optionName>itemList</ns2:optionName><ns2:value>tw.options.customerAccounts[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>2fa571bc-f455-4da5-801a-e0379c883d51</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"accountNO\",\"optionDisplayProperty\":\"accountNO\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>7e8c8364-8391-4fb6-83ec-f8dd911c5d02</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"accountNO\",\"optionDisplayProperty\":\"accountNO\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>d1d07134-9fdc-40b4-8c42-cea441320385</ns2:id><ns2:optionName>@width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"50%\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>5bc7a0e1-1594-4c3a-8397-f3c835455bc6</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>var debitedAccountNo = me.getData()? me.getData():\"\";\r\nview.valiadateAccCurrency(me);\r\n\r\n\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>503bb00f-f7a4-4d0e-8e8f-42b20f91ca0d</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.debitedAccountNo</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>7afd1522-7b6e-486c-8a7a-f9fee538db17</ns2:id><ns2:layoutItemId>HL_DebitedNostro</ns2:layoutItemId><ns2:configData><ns2:id>b6963f13-ca6e-4933-81c9-845a33fb5350</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 4</ns2:value></ns2:configData><ns2:configData><ns2:id>5760d46a-155b-46e3-82b7-ecc1f9d4079c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>186b1b86-cbcf-4454-8527-b7edc3ccaa5a</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>f8023f6a-5494-4c09-8ce4-7c80809eaa71</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>776aa739-850b-4f22-8679-7d08a8ccb6b7</ns2:id><ns2:layoutItemId>Vertical_Layout1</ns2:layoutItemId><ns2:configData><ns2:id>ba6210c9-e181-45e6-85a1-bd3c6a175c9c</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 2</ns2:value></ns2:configData><ns2:configData><ns2:id>aeeab602-b624-4ae5-89fb-d0c2c32bd019</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c6288af9-dfef-47b5-856b-83f9a854d925</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>f6847d94-a423-4026-8f8e-a23a173a8ff9</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>7c371273-0709-46a3-8417-215e27a4dde9</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>9b408126-6369-4924-8ec9-964dcc0190e3</ns2:id><ns2:layoutItemId>Debited_Nostro_Vostro_Account_Number</ns2:layoutItemId><ns2:configData><ns2:id>3a71ab64-a3d4-47d0-b512-a97690c03fad</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>478030aa-4a6c-4b83-85fc-48901f73d421</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Number</ns2:value></ns2:configData><ns2:configData><ns2:id>075d92bb-3767-485e-83eb-b507ae98a054</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>860ad736-5bac-48e6-8467-5c5322f8851a</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>ac268ae5-fa4a-4294-81fe-e92fe028abb7</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value>if(potential.length &gt; 19)\r\n{\r\n\tme.setValid(false , \"max lenght is 19 digits\");\r\n\treturn false;\r\n}\r\nelse\r\n{\r\n\tme.setValid(true);\r\n\treturn true;\r\n}</ns2:value></ns2:configData><ns2:configData><ns2:id>3cbd9eb9-1cc6-4345-8abd-27154ca939f5</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>if(isNaN(Number(me.getData()))){ //check if this is number\r\n\tme.setValid(false , \"must be digits\");\r\n\tme.setData(\"\");\r\n\t${verify}.setEnabled(false);\r\n\treturn false;\r\n}else if(me.getData().length == 19) //check the lenght\r\n{\r\n\tme.setValid(true);\r\n\t${verify}.setEnabled(true);\r\n\treturn true;\r\n}\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>8ad2265e-279f-4ce2-8c3b-55c21c7f4eef</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>if(isNaN(Number(me.getData()))){\r\n\tme.setValid(false , \"must be digits\");\r\n\tme.setData(\"\");\r\n\t${verify}.setEnabled(false);\r\n\treturn false;\r\n}else if((me.getData() != \"\" &amp;&amp; me.getData() != null &amp;&amp; me.getData() != undefined))\r\n{\r\n\tif (me.getData().length == 19) {\r\n\t\tme.setValid(true);\r\n\t\t${verify}.setEnabled(true);\r\n\t\treturn true;\r\n\t}\r\n\t\r\n}\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>956e2fea-88eb-430e-89f2-0c884e7b413e</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.debitedAccountNo</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>8dd8c007-0a56-4d16-821a-56ebe9215422</ns2:id><ns2:layoutItemId>Vertical_Layout2</ns2:layoutItemId><ns2:configData><ns2:id>d49b959f-ee90-41a6-8b2b-82e2a4ace602</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 3</ns2:value></ns2:configData><ns2:configData><ns2:id>654db115-2ad6-4bd6-821c-917b047a79b9</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>0bd9a3fb-7b31-42e9-8ac4-1c74559662c8</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>dd477b51-92b1-4b11-85e6-3974dc9df577</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>e69655fb-a907-4575-85b4-0df4e2ab53fc</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>1a83cf7f-b7d3-4c01-87e8-84356dc6b0db</ns2:id><ns2:layoutItemId>Output_Text1</ns2:layoutItemId><ns2:configData><ns2:id>f488de5d-5239-4196-8eb9-23703a80d4ec</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value> </ns2:value></ns2:configData><ns2:configData><ns2:id>b8cd6339-062c-4b08-821b-4959880a1138</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>fb0822c2-b3b5-4181-8188-c512b424938c</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>edbad6be-9dd5-4a55-8ec2-b43d3e2df59a</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"HIDDEN\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>971c74db-0d5d-4c7f-81e0-f71775cf3ed2</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"M\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>b60315de-8801-4a7e-8211-18b8b0662ab4</ns2:id><ns2:layoutItemId>verify</ns2:layoutItemId><ns2:configData><ns2:id>37b6cdcb-957f-4025-843b-3f0d732ab784</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Verify</ns2:value></ns2:configData><ns2:configData><ns2:id>d74cc100-ac9d-4e11-89b3-b12424f714c3</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>f4eef873-061e-49ac-8e5b-0839f5060a3a</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>4c74ce01-8073-4dc8-8f02-5ba649b2f3ad</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>e09d596c-e0ff-4e93-87cd-a14fce826513</ns2:id><ns2:optionName>shapeStyle</ns2:optionName><ns2:value>R</ns2:value></ns2:configData><ns2:configData><ns2:id>805b1990-b971-49db-8052-e112bf31956c</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>8466b9ed-dee5-44af-888c-2904e82209dc</ns2:id><ns2:layoutItemId>Vertical_Settlement</ns2:layoutItemId><ns2:configData><ns2:id>669bcaac-3bf8-456a-8483-14f39d884305</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout</ns2:value></ns2:configData><ns2:configData><ns2:id>ea3ebea6-9d9b-49d4-8b6f-f239af434cb3</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>fc1aaa70-3ce9-44df-82b4-3f64d13a933e</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>658d3a28-2d8c-4959-8961-4f6277e37f46</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>6ac38661-e2e2-492c-8a6e-c304fb35dc27</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>2083048a-1d49-4fb8-8385-00bc022064f9</ns2:id><ns2:layoutItemId>Pnl_SettlementAccount</ns2:layoutItemId><ns2:configData><ns2:id>7ecebc3e-040f-46f1-8eee-de28ea4cb301</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Settlement (Credited) Account</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>df676eb7-6a2b-40be-86a4-e3a736505875</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>a36b5be7-ff3e-4ed4-816a-badafbf0786b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>9ea3c802-595f-4e90-8921-e73d05ee040b</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.contractLiqVis</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>94ab3cb5-f315-4a04-8525-cda5678de443</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>5acf21f6-b1c4-4008-8917-ef0478209f41</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>5168919f-f54b-45ee-8349-540e1b6b3e9a</ns2:id><ns2:layoutItemId>Pnl_CreditedAccount</ns2:layoutItemId><ns2:configData><ns2:id>711ef860-b0b6-412f-827e-f5e7f91790f7</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Credited Account Details</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>50021d36-58a5-45e3-88fc-0120178297af</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>b5582074-800d-4645-8c8d-23339ed1af64</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>4e7cb4f8-a4e9-4ee8-8da7-6f0837235a4d</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>688b27a1-efd6-4538-8d2c-5c7408cc34b4</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>5a6c41df-420a-4821-8290-c2552aadc4b4</ns2:id><ns2:layoutItemId>HL_CreditedAccount</ns2:layoutItemId><ns2:configData><ns2:id>059a6021-b8e1-46f3-8eee-5967ea2702b8</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout</ns2:value></ns2:configData><ns2:configData><ns2:id>a4178e81-a1f5-49df-8e5e-9eb7ef27d821</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c2d28ae7-306d-423b-8ac0-f8f0b7d2581a</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>6f445ce1-1e30-424c-8ada-a7b71002f749</ns2:id><ns2:optionName>@margin</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"0px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>18853e01-d2a0-4fc1-853e-fe1e8e76bb33</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>115db6b4-b894-464f-8936-a4c1e0e82122</ns2:id><ns2:layoutItemId>Vertical_Layout4</ns2:layoutItemId><ns2:configData><ns2:id>87569bb2-e7e4-4dc9-885d-b6b102b625ba</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 4</ns2:value></ns2:configData><ns2:configData><ns2:id>562f34d9-0f80-48b6-8084-b672c5af1d55</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>a230880e-fdce-4d34-8219-81fb02daa0ba</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>1ea7fc60-0215-46e1-81f6-e6facd9cab5c</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"50%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>fd63a3a9-d8a7-4d8a-8273-c9ef0c3e920d</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>6fe4b163-7e1c-443b-86da-94ee16f77a1c</ns2:id><ns2:layoutItemId>AccountClass</ns2:layoutItemId><ns2:configData><ns2:id>7c7d0e18-de8d-4968-8fe5-d071f4a2c16f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Class (Customer / GL)</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>cb3f6a60-9f5f-4741-8c08-033078d98057</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c78ac4e6-79ec-498d-8604-0790b767e371</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>d93a9830-8d71-4fc6-862c-bbc4a904fb24</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>L</ns2:value></ns2:configData><ns2:configData><ns2:id>89c7932d-080a-4651-8f5f-5f1d75910ce5</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"value\",\"optionDisplayProperty\":\"name\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>e8dc7324-72a5-43df-8d19-5e6e046549b7</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"value\",\"optionDisplayProperty\":\"name\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>6b07eb84-8a38-4dca-8091-0323fb873602</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value>[{\"name\":\"Customer Account\",\"value\":\"Customer Account\"},{\"name\":\"GL Account\",\"value\":\"GL Account\"}]</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>03e22311-758d-431b-89e4-dcdf7a1b122c</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.resetAccountInfo(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>0ad3d28a-00e2-4319-83d7-65138c787e1e</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>view.accountNumVis(me);</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.accountClass</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>50e1e584-71e4-4855-8077-dce228ec4812</ns2:id><ns2:layoutItemId>customerAccountNo</ns2:layoutItemId><ns2:configData><ns2:id>b63f219d-3ec7-4540-8a0b-db05acbdbf30</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Number</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>0c6c9155-ec24-4299-8163-41c2479bdd55</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>7a207079-b1a8-4118-866c-13bb5a6b67fb</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>6b4c8a4e-c5aa-45e9-8984-13ee91bf301b</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>B</ns2:value></ns2:configData><ns2:configData><ns2:id>218ef1d3-5cd1-4fd9-8a05-2bce10e179ac</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"accountNO\",\"optionDisplayProperty\":\"accountNO\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>c260a891-60cf-4f64-8870-e975aee8bdac</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"accountNO\",\"optionDisplayProperty\":\"accountNO\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>736b8f8b-f712-4455-804f-b8284e151c52</ns2:id><ns2:optionName>itemList</ns2:optionName><ns2:value>tw.options.customerAccounts[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>13989b9b-b80b-4bcb-88cc-db973da11895</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>660c1206-ef70-4d69-88fe-676369695b7d</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>var y= me.getData();\r\n\r\nview.setAccountInfo(me);\r\nvar currObj= ${accountCurrency};\r\n//view.setNegoRate(currObj);\r\nview.validateOverDraft();\r\n\r\n</ns2:value></ns2:configData><ns2:configData><ns2:id>7978aa51-6e7f-46fc-80e0-05effa4130e9</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value>tw.businessData.odcRequest.customerAndPartyAccountList[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>0425123d-b03d-489c-868a-a2c5bffa57b2</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>b2cf20bb-40cd-4bf2-8880-dcb57566773a</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</ns2:value></ns2:configData><ns2:configData><ns2:id>b11f39b5-34e1-4e0e-8ce8-bb7233179e76</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.businessData.odcRequest.Parties.partyTypes.partyCIF</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>7bf94a2b-52fb-4389-8bbd-00a527f1e21d</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>fff1b78d-4658-487c-8411-285bfab42c51</ns2:id><ns2:layoutItemId>Horizontal_Layout2</ns2:layoutItemId><ns2:configData><ns2:id>c31e815b-9cca-4d4a-8951-2033ca8785eb</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 2</ns2:value></ns2:configData><ns2:configData><ns2:id>93b16fc0-54d8-4c1b-8250-f20add322a97</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>9edf7903-a46e-4dcf-86df-0cf8a26bc481</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>d730d183-3506-431c-8c98-905db2d5be96</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>eb6b3a97-a443-4b9f-8f7f-92a0f46eac9e</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>b282db09-c352-443b-81cc-faa093de70e2</ns2:id><ns2:layoutItemId>glAccountNo</ns2:layoutItemId><ns2:configData><ns2:id>d53264ce-00e5-488c-83b0-898c6710a2e8</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>GL Account Number</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>cc7e2b71-b707-4e38-8e7b-636a859903f9</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>6eab5c4a-9c5a-46ee-804a-ac05ae08fd84</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>ee146d60-8eca-4b9d-8800-81118891a1dc</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>L</ns2:value></ns2:configData><ns2:configData><ns2:id>0d9202da-d716-4aa4-88a6-8d64825d1a7b</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>390b3ba7-db27-4ec0-8a9c-d731f4463eab</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>2d563f47-60e0-4773-8968-8a691fde4e0c</ns2:id><ns2:optionName>itemList</ns2:optionName><ns2:value>tw.businessData.IDCContract.settlementAccounts.currentItem.accountNumberList[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>80696bcd-f5ea-4ad4-8164-87c9045accc1</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>95db1201-0ca0-4d3a-8850-b1e2d6671f65</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>6dfb782e-d77f-4510-8dda-b6cec0e30194</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>d24b3c81-30d9-4f1b-87c3-ddeaeea47193</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>bcde88ac-7df2-4e5b-86b3-082f9e99a26b</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>63bf750b-ba80-4745-8abd-a127046d2de8</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value>${verifyGlAccountBtn}.setEnabled(true);\r\n${verifiedText}.setData(\"\");\r\nif(potential.length &gt; 9){\r\n\tme.setValid(false , \"must be 9 characters\");\r\n//\tme.setData(\"\");\r\n\treturn false;\r\n}else{\r\n\tme.setValid(true);\r\n\treturn true;\r\n}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.glAccountNo</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>184a9deb-5f24-4663-8957-289c527a6008</ns2:id><ns2:layoutItemId>verifyGlAccountBtn</ns2:layoutItemId><ns2:configData><ns2:id>a53819fc-4b21-4778-8ebe-88218a4dfa7f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Verify GL</ns2:value></ns2:configData><ns2:configData><ns2:id>5ca03850-ead3-4278-8b19-9afa776b6ead</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>98a08649-99bf-4cec-88fd-5d53b7e8fb0d</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>4b4e733d-20d8-4f31-8766-3c39306d6acf</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"20px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>304138ec-dbc6-426a-8fa5-eea82d25f292</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"30%\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>d0328e4a-d958-4ff9-8101-8bb0cffca98f</ns2:id><ns2:optionName>eventON_CLICK</ns2:optionName><ns2:value>${SC_verifyGlAccount}.execute();</ns2:value></ns2:configData><ns2:configData><ns2:id>e66c01d3-9b56-4553-8825-c94a014daa84</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>bf4950cc-e5af-4eb7-8a9c-38c0aec256e3</ns2:id><ns2:optionName>shapeStyle</ns2:optionName><ns2:value>R</ns2:value></ns2:configData><ns2:configData><ns2:id>77551d96-9c5b-40c5-8d71-742ce1f5a901</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>${verifyGlAccountBtn}.setEnabled(false);</ns2:value></ns2:configData><ns2:configData><ns2:id>4e835ba8-9a2f-4a03-86e1-8864333b209f</ns2:id><ns2:optionName>preventMultipleClicks</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>327fdaa4-cfbe-41cc-8c2d-7c031a6c4653</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID><ns2:binding>tw.options.glAccountVerified</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>edac7c06-b2ad-42f7-8441-80961eb7ef70</ns2:id><ns2:layoutItemId>verifiedText</ns2:layoutItemId><ns2:configData><ns2:id>31b95e76-6b4a-4488-88d9-d8a468901216</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Verified text</ns2:value></ns2:configData><ns2:configData><ns2:id>f208cb24-6cce-43ec-8eb2-d1c0d90d6396</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c3ee4cc8-ca94-49a0-80e6-25f3c8577fe4</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>HIDE</ns2:value></ns2:configData><ns2:configData><ns2:id>13a75c56-a067-42f7-8511-a5ed6e0e9316</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>549fceff-9a8c-4f40-8952-210e564b670b</ns2:id><ns2:optionName>weightStyle</ns2:optionName><ns2:value>M</ns2:value></ns2:configData><ns2:configData><ns2:id>a119257c-b992-4c9d-8106-c313084cc841</ns2:id><ns2:optionName>textAlignment</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"C\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>c8cd16a3-9300-4aa7-82e3-4f3dc5faf7f5</ns2:id><ns2:optionName>labelPlacement</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"T\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>b1d40aad-7332-420e-809f-54244fad77d8</ns2:id><ns2:optionName>textWrap</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"N\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>e039d2fe-2a7e-430b-8992-80c76ca373de</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"L\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>85f5dc81-01fd-49d1-8194-3c4bfd60faba</ns2:id><ns2:optionName>labelWeightNormal</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":false}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>3c2b2ec1-cee5-4c1f-8e06-b03933b3c226</ns2:id><ns2:optionName>@margin</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"0px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>4deb9320-ab16-43aa-88f8-7e5ce2fc7f02</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"30px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>22ffac11-788b-4b71-807d-ba1458d418e0</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"20%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID><ns2:binding>tw.options.verifyGLMsg</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>755776d0-e7fd-4e7d-8400-9acd3014b447</ns2:id><ns2:layoutItemId>accountCurrency</ns2:layoutItemId><ns2:configData><ns2:id>4eda7b15-5a51-4849-8da5-9c3a1d3d302a</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Currency</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>5dcd40fa-47f2-4a0e-8ee4-0009d16f2910</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>098b198c-de12-4c0d-802e-a2cf8a54ddc8</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>af4de72a-4f68-44d4-8ec1-d93d5bea70d4</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>4a1910de-7785-4ccc-844b-9493003d3007</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>3a9e2c9d-7bcc-4e82-822a-0026918be770</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>6a1ced53-e512-403d-8de2-16e53aa25444</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>8b0bda98-e854-42b3-8aca-bf530ea9ee58</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.resource.odcLookupsTable.Currency</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>44b79f0b-06c2-402c-8654-49883c4ddb79</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>//debugger;\r\nview.setNegoRate(me);\r\n </ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>38b47343-b003-461c-842f-0f398daae853</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2522bdec-dee3-429d-824d-34f3695b6a6b</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value>[{\"name\":\"s\",\"value\":\"d\"}]</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.currency.value</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>1fa71af2-a6f7-4a4c-8187-1a710662bc42</ns2:id><ns2:layoutItemId>SC_verifyGlAccount</ns2:layoutItemId><ns2:configData><ns2:id>6b272bd9-c927-4d48-8943-deb6d2b399c5</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Check GL Account</ns2:value></ns2:configData><ns2:configData><ns2:id>8cc3754e-32ec-4cd9-8143-a0beadbbbafc</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>fab2f76c-667e-4662-8fff-0ce66fec83cc</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>112c2c14-6433-4bd5-85ce-09a133de5fe4</ns2:id><ns2:optionName>attachedService</ns2:optionName><ns2:value>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</ns2:value></ns2:configData><ns2:configData><ns2:id>d925ea70-7a41-4495-89d2-3955be25339b</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.glAccountNo</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>eba92fb4-5332-459c-8d0f-04abac6a0424</ns2:id><ns2:optionName>eventON_SVCRESULT</ns2:optionName><ns2:value>var glAccountVerified = me.getData();\r\n\r\nif(glAccountVerified){\r\n//\t${BalanceSign}.setEnabled(true);\r\n//\t${AccountBalance}.setEnabled(true);\r\n\t${BalanceSign}.setData(\"\");\r\n\t${AccountBalance}.setData(0);\r\n\t${glAccountNo}.setValid(true);\r\n\t${verifiedText}.setData(\"Verified\");\r\n}\r\nelse{\r\n\t${glAccountNo}.setValid(false,\"GL account is not verifed\");\r\n\t${verifiedText}.setData(\"\");\r\n}\r\n</ns2:value></ns2:configData><ns2:viewUUID>64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9</ns2:viewUUID><ns2:binding>tw.options.glAccountVerified</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>7bec45b6-4c9f-446b-8718-39abb1212723</ns2:id><ns2:layoutItemId>Vertical_Layout5</ns2:layoutItemId><ns2:configData><ns2:id>f9692171-b7d4-4094-826c-1099a16d6229</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 5</ns2:value></ns2:configData><ns2:configData><ns2:id>6e7df79a-8d13-4884-84ac-ef0f8527a6d9</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>5187d560-3780-45c6-81fe-7d6fc4934e45</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>c6054020-1c0f-40d6-8a19-57006c743a54</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>63cccbbb-9c8c-4852-8a2a-168a6a986a98</ns2:id><ns2:layoutItemId>AccountBranchCode</ns2:layoutItemId><ns2:configData><ns2:id>9dbf809a-06c7-4fe3-841e-82beec2b325d</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>214e2c12-8f90-4685-8b8f-ad4a82e9420d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Branch Code</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>78aa0de3-8822-4ae4-8eeb-207cd993393e</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>1560a804-35a0-49f2-86ce-eaf89f91515b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>96e26a31-cf4b-4ca1-8c0b-a7cb67e23029</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>fedbf534-d85c-4696-8959-c91fb8006392</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>e82a882a-cb45-4c6e-8cdd-7ddf091d561d</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.branchCode</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>87d95487-8d85-4009-8046-9a9471ea5178</ns2:id><ns2:layoutItemId>BalanceSign</ns2:layoutItemId><ns2:configData><ns2:id>7a6a3697-062f-401b-8363-9ade59d2fa99</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Balance Sign</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>149fe0bc-7b42-4e6c-845c-ddb7313797fc</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>59e761b9-63e3-4a16-827e-55f0d09018e2</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>d6dfe788-5549-4aa6-8a48-aeb38464fa16</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.balanceSign</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>0e5d960e-923b-4407-8432-c7903cce39f5</ns2:id><ns2:layoutItemId>AccountBalance</ns2:layoutItemId><ns2:configData><ns2:id>0d778acb-998d-4b50-85e5-62c238449ca8</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>d2838c3e-3482-43bc-80f9-2bcda9f9453b</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Balance</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>5ca025eb-8227-4cee-899a-f56ed44f767f</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>60d889ba-0090-4f68-8bbc-a817fa1eef22</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>5743478d-8d86-4641-8535-e73f04bf8798</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>e205b312-40ad-4a4e-87ec-cb6a222a80ea</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"18px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>7761e24d-43ef-4d84-8b87-69fea790425f</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAccount.balance</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>779ea393-4e15-4b17-8141-679110add35f</ns2:id><ns2:layoutItemId>DebitedAmount1</ns2:layoutItemId><ns2:configData><ns2:id>c35a326b-5dac-4659-89ea-5f8e37121466</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Credited Amount</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>e344b31b-9d3f-4092-85e5-4a37aebf20ac</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>8869bc86-d265-45e9-8cc5-128da94492cf</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>3fb518d0-09b8-478e-8eb4-c9f1f1ceea6d</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>899b02d0-b847-4642-829a-f586cde2df0e</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>7568de69-e047-4bbb-8597-8c01b487d382</ns2:id><ns2:layoutItemId>HL_DebitedAmount</ns2:layoutItemId><ns2:configData><ns2:id>95a54169-b8f0-4d33-8374-7ddcb47c6d9f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 7</ns2:value></ns2:configData><ns2:configData><ns2:id>cccf9b77-d442-4959-8e99-ab4cec67810d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>cc2b113d-a35f-4f06-85df-2b15c7a81502</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>29932035-2b39-4322-8b3a-1f73c16706a2</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>f21498d1-c710-4a7d-820b-542e053e29c0</ns2:id><ns2:layoutItemId>Vertical_Layout8</ns2:layoutItemId><ns2:configData><ns2:id>a78b6f01-f151-42a7-8885-013c88cecd62</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 9</ns2:value></ns2:configData><ns2:configData><ns2:id>6611364c-a470-4495-84c4-618d3662132d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>27f0022a-f3f5-47c4-80be-d0e993ec8d81</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>3e629fcd-496c-4532-8f98-28113e785611</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>63e49dba-5c34-441c-8fe9-4a20d686154a</ns2:id><ns2:layoutItemId>standardExRate</ns2:layoutItemId><ns2:configData><ns2:id>32077596-8ee3-4cd4-8bcf-584e488fcebb</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>6</ns2:value></ns2:configData><ns2:configData><ns2:id>9493388f-06de-4fb5-88b3-bed58b68c4ea</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Standard Exchange Rate</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>f181ad04-0c66-4ced-8299-3c7ee5d86c56</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>60997a8f-6974-41d8-8a12-73c750ce71a7</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>5d6e59cf-5ba4-4aac-81a3-a1aef744f2fa</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAmount.standardExRate</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>4251935a-b53e-4ccf-8655-09822b8b2351</ns2:id><ns2:layoutItemId>negotiatedExRate</ns2:layoutItemId><ns2:configData><ns2:id>efc947e4-3510-41e2-8fdf-ce0a5a35cf61</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>6</ns2:value></ns2:configData><ns2:configData><ns2:id>e77d48c5-ca8e-40ef-87ff-2c14cc0d4642</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Negotiated Exchange Rate</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>a2e73e92-6b9c-460a-845b-f59a5258367b</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>1e720890-a6d5-45d5-825c-5b15ec516af1</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>0bfa71ab-9ccb-44f6-85d5-f036f48b4787</ns2:id><ns2:optionName>hideThousandsSeparator</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>10d75a83-dd08-4e72-8445-4d537865cf82</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.setAmountInCurrency();</ns2:value></ns2:configData><ns2:configData><ns2:id>dfd3ad87-025d-4f4b-81d0-e0f81a386773</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAmount.negotiatedExRate</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>f72979a7-1c9b-482b-845a-315f3bda40c6</ns2:id><ns2:layoutItemId>GetExchangeRate</ns2:layoutItemId><ns2:configData><ns2:id>d9f9734c-4f06-4f5a-883d-00dd01b9076d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Get Exchange Rate</ns2:value></ns2:configData><ns2:configData><ns2:id>1b5fa3c5-**************-21b2c1bfe007</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>dc79a69d-72cd-40de-8062-b69d62d42de0</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>39ac4576-cd33-492c-87b1-95c6623f4771</ns2:id><ns2:optionName>attachedService</ns2:optionName><ns2:value>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</ns2:value></ns2:configData><ns2:configData><ns2:id>b2c80f8e-fa51-4a55-8d6a-e58b723f6e0a</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value></ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>d3472a58-35ba-4329-8073-5d6d2652ca87</ns2:id><ns2:optionName>eventON_SVCINVOKE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>64eaa825-b682-4de8-85ef-e4af3fb82e19</ns2:id><ns2:optionName>eventON_SVCRESULT</ns2:optionName><ns2:value>view.SetCreditAmountDetails(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>9bb40043-4690-4bc5-848d-9f534de642ff</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value>alert(error.errorText);</ns2:value></ns2:configData><ns2:configData><ns2:id>4204fef6-491d-4e8d-856d-b1dcbb994a2f</ns2:id><ns2:optionName>busyIndicator</ns2:optionName><ns2:value>C</ns2:value></ns2:configData><ns2:viewUUID>64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9</ns2:viewUUID><ns2:binding>tw.options.exchangeRate</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>bba5e89b-ad12-41b8-8f39-201316c7e349</ns2:id><ns2:layoutItemId>Vertical_Layout9</ns2:layoutItemId><ns2:configData><ns2:id>37958a7c-628a-4a87-8e07-caf6a7ede3d6</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 10</ns2:value></ns2:configData><ns2:configData><ns2:id>75388bf9-23a6-45e2-8a3d-c87930fb6af2</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>d599b51e-ab76-489c-8584-7d36bb820dea</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>2bf7fa8b-e4d9-4270-8c17-6e060f06984f</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>90e38edd-4272-41e8-804a-3125ccee419d</ns2:id><ns2:layoutItemId>creditedAmountinAccCurrency</ns2:layoutItemId><ns2:configData><ns2:id>54cfafb0-fe9f-44cd-80ee-6df5599e33ef</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>c2fe2f97-f8cc-42c7-8861-07e45034a8c1</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Credited Amount in Account Currency</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>fe0f32cc-c1b0-44df-8eab-1a42385b0dce</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>632d548b-ed92-4b6d-817c-1abfa054f539</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>a4ecffab-690f-4cdb-8fd8-ac642eb36970</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>79cfd151-85e3-47a5-853b-9c326f4fe81f</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>//view.validateOverDraft();</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.ContractLiquidation.creditedAmount.amountInAccount</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "true", "loadJsFunction": "if (this.context.options.isChecker.get(\"value\")) {\r\r\n\r\r\n\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(false);\r\r\n\tthis.ui.get(\"debitedAccountNo1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(false);\r\r\n}else{\r\r\n//\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(true);\r\r\n//\tthis.ui.get(\"debitedAccountNo1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(true);\r\r\n}\r\r\n\r\r\n", "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": "", "changeJsFunction": {"isNull": "true"}, "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "70c87f8a-306b-408e-9148-538e08591cd6", "versionId": "64571bed-66b5-41fa-87ad-9a9c8819f9f3", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "bindingType": {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewBindingTypeId": "65.d5d1b087-9967-4d07-8c4f-568f7fc95283", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "0", "description": {"isNull": "true"}, "guid": "c6b9ce9c-66a2-4eff-af8e-b733675af808", "versionId": "304e0305-04e0-4d01-aa21-455d6de14dea"}, "configOption": [{"name": "glAccountVerified", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.8f841d73-9c14-4670-981a-78cbeb140c63", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "0", "description": "", "groupName": "", "guid": "1d27a4dd-b5b1-4305-bf98-8d728748e924", "versionId": "08939d4a-efec-4454-9fde-35f850b70d90"}, {"name": "contractLiqVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.8495aee5-868a-4465-a5d8-e595f1ea6b7c", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "1", "description": "", "groupName": "", "guid": "414a4847-d10a-423a-8ab7-35bbaa16ab8e", "versionId": "f46001d0-24eb-4db7-b308-4f0fb777361b"}, {"name": "customerAccounts", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.86e4fccd-056a-431f-b38f-36eabb1df8e0", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "2", "description": "", "groupName": "", "guid": "d75c5cdc-9be4-4bd9-a0e9-16521565ae03", "versionId": "18869e3a-3e00-4d76-8a51-f0b9f0bf61a2"}, {"name": "exchangeRate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.5c81ba55-d74e-4141-ac26-9981a5747d50", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "3", "description": "", "groupName": "", "guid": "2a3452ea-7ee1-4c45-9696-055d52356ff1", "versionId": "3c9efb6f-71af-460f-8a57-42d75b70b552"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.5a74b291-2a3e-4b09-a441-5dab4d9ac10b", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "4", "description": "", "groupName": "", "guid": "26b401ed-c987-454d-af4f-5b9b6bc5460f", "versionId": "d50d2312-8cc0-4e2c-aa19-7ced323e5e2b"}, {"name": "contractLiquidatedMSG", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.ee7c176f-6c43-4af0-b8bd-655da001772b", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "5", "description": "", "groupName": "", "guid": "e74e9143-3be3-4ad3-98f7-16b6b3f55ab1", "versionId": "0758ce15-e629-4684-85d0-6611134c6afb"}, {"name": "verifyGLMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.6d0d1c2c-37ff-4587-aac2-ee429a74fffd", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "6", "description": "", "groupName": "", "guid": "0e352c3a-3103-48f7-868c-3f2fc09766e1", "versionId": "15c3a3d4-bc91-4312-b40e-89f61e700f14"}], "inlineScript": {"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.b42d9f4f-c212-4941-9e51-d9541063a769", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "scriptType": "JS", "scriptBlock": " //Validate Debited Nostro/Vostro Account\t\t\t\t\t\t\t\t\t\t\r\r\n this.valiadateAccCurrency = function (value)\r\r\n {\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo   = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\");\t\r\r\n\tvar currency    = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\tvar liqCurency  = this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"liqCurrency\");\t\r\r\n\t\r\r\n\tif (accountNo == value.getData() && currency != liqCurency) { \r\r\n//\t\t value.setData(\"\");\r\r\n//\t\t this.ui.get( \"debitedAccountNo1\").setValid(false,\"This account's currency must be the same as liquidation currency\");\t\r\r\n \tbreak;\r\r\n \t}\r\r\n\telse\r\r\n\t\t this.ui.get( \"debitedAccountNo1\").setValid(true);\t\r\r\n\t}//end of for\r\r\n}\r\r\n\r\r\n//Init Credit Account Data\r\r\nthis.setAccountInfo = function (value)\r\r\n {\r\r\n\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo= this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") ? this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") : \"\";\r\r\n\t\tif (accountNo == value.getData()) { \r\r\n\t\t\tvar branchCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"branchCode\");\r\r\n\t\t\tvar currency = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\t\t\tvar balance = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balance\");\r\r\n\t\t\tvar balanceSign = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balanceType\");\r\r\n\t\t\tvar classCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t \t\tclassCode= classCode? classCode.substring(0,1):\"\";\r\r\n\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"branchCode\", branchCode);\r\r\n\t \t\t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balance\", balance);\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balanceSign\", balanceSign);\r\r\n//\t \t\tconsole.log(\"------------------classCode: \"+classCode);\r\r\n\t \t\tif(classCode == \"O\" || classCode == \"D\")\r\r\n\t \t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",true);\r\r\n\t\t\telse\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",false);\r\r\n\t\t\t\t\t\t\r\r\n//\t\t\tvar od= this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n//\t\t\tconsole.log(\"------------------is over draft : \"+od);\t\t \t\t\r\r\n\t \t\t//SET CURRENCY\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"currency\", {} );\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"name\", currency);\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"value\", currency);\r\r\n\t\t\t\r\r\n\t\t\t//SET EXCHANGE RATE\r\r\n//\t\t\tview.setNegoRate(currency);\t\r\r\n \t\t}\r\r\n\t}\r\r\n}\r\r\n //Set credited Amount\r\r\n this.setNegoRate = function(value)\r\r\n {\t\r\r\n\r\r\n//\tconsole.log(\"----------------------INSIDE setNegoRate\");\r\r\n  \tvar liqCurr   =  this.ui.get(\"LiquidateionCurrency\").getData()? this.ui.get(\"LiquidateionCurrency\").getData():\"\";  \t\t \t\r\r\n  \tvar accCurr   =  value.getData()? value.getData():\"\"; \t \t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr == accCurr ) ) { \r\r\n\t\tthis.ui.get(\"standardExRate\").setData(1);\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setData(1);\r\r\n  \t\tthis.ui.get(\"negotiatedExRate\").setEnabled(false); \t\r\r\n \t} \t\t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr != accCurr ) ){\r\r\n//\t\t//call get exchange rate service\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n//\t\t console.log(\"---------- liqCurr != accCurr -------------------------\");\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setEnabled(true);\r\r\n//  \t\tvar inputCurrency={fromCurrency : liqCurr , toCurrency : accCurr};\r\r\n//  \t\tconsole.log(\"----------inputCurrency \"+inputCurrency);\r\r\n  \t\t\r\r\n//  \t\tvar parsedInputCurrency =  JSON.stringify(inputCurrency);\r\r\n//  \t\tconsole.log(\"----------parsedInputCurrency=   \"+parsedInputCurrency);\r\r\n//     \tconsole.log(\"*******************************************************************\");\r\r\n//\t\talert(parsedInputCurrency);\r\r\n//\t\tthis.ui.get(\"SC_getExRate\").execute(parsedInputCurrency);\r\r\n\t\t\r\r\n\t\tconcatedCurrency = {ccFrom : liqCurr , ccTo : accCurr , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\tinputCurr = JSON.stringify(concatedCurrency);\r\r\n\r\r\n\t\tthis.ui.get(\"GetExchangeRate\").execute(inputCurr);\r\r\n \t}\t\t\t\r\r\n}\r\r\n//SET Credited Amount From Exchange Service\r\r\n this.SetCreditAmountDetails = function(value){\r\r\n\r\r\n\tconsole.log(\"----------------------INSIDE SetCreditAmountDetails\");\r\r\n\tvar exRate = this.context.options.exchangeRate.get(\"value\");\r\r\n\r\r\n\tconsole.log(\"--------------- rate \"+ exRate);\r\r\n\tconsole.log(\"---------------ExRate fixed =  \"+ exRate.toFixed(6));\r\r\n\t\r\r\n\tthis.ui.get(\"standardExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\t\r\r\n\r\r\n } \r\r\n//calculate credited amount in cuurency\r\r\nthis.setAmountInCurrency = function(){\r\r\n\tvar liqAmount  =  this.ui.get(\"LiquidationAmount\").getData()? this.ui.get(\"LiquidationAmount\").getData():0;\r\r\n\tvar negoExRate =  this.ui.get(\"negotiatedExRate\").getData()? this.ui.get(\"negotiatedExRate\").getData():0;\r\r\n\t\r\r\n\tif( liqAmount > 0   &&   negoExRate > 0 )\r\r\n\t\tthis.ui.get(\"creditedAmountinAccCurrency\").setData(liqAmount * negoExRate);\r\r\n}\r\r\n\r\r\n//validate overDraft\r\r\nthis.validateOverDraft = function () {\r\r\n\tvar AccBalance          =  this.ui.get(\"AccountBalance\").getData()? this.ui.get(\"AccountBalance\").getData() : 0;\r\r\n\tvar amountinAccCurrency =  this.ui.get(\"creditedAmountinAccCurrency\").getData()? this.ui.get(\"creditedAmountinAccCurrency\").getData() : 0;\r\r\n\tvar overDraft           =  this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n\t\r\r\n\tif(amountinAccCurrency > AccBalance){\r\r\n\t\tif (overDraft) \r\r\n\t\t\t{\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"WARNING: Amount in account currency should be less than Account Balance\");\r\r\n\t\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"ERROR: Amount in Account Currency > Account Balance and Account Type is not Overdraft\");\r\r\n\t\t\t}\r\r\n\t\r\r\n\t}else{\r\r\n\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(true);\r\r\n\t}//end of if\r\r\n\t\r\r\n}\r\r\n\r\r\n//On load of account class - set vis according to account class (customer & gl)\r\r\nthis.accountNumVis = function (value) {\r\r\n    var accountClass = value.getData().name;\r\r\n    \r\r\n    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n        if (accountClass == \"Customer Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true,true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true,true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n\r\r\n        } else if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(true);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false,true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false,true);\r\r\n\r\r\n        }\r\r\n    } else {\r\r\n        this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n        this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n        \r\r\n        if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"glAccountNo\").setEnabled(false);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n   \r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false, true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false, true);\r\r\n            \r\r\n        } else {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\t\tthis.ui.get(\"customerAccountNo\").setEnabled(false);\r\r\n\t\t\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true, true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n        }\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of account class - reset data before choosing another account\r\r\nthis.resetAccountInfo = function (value){\r\r\n    this.ui.get(\"glAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"customerAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"AccountBalance\").setData(\"\");\r\r\n    this.ui.get(\"AccountBranchCode\").setData(\"\");\r\r\n    this.ui.get(\"BalanceSign\").setData(\"\");\r\r\n    this.ui.get(\"accountCurrency\").setData(\"\");\r\r\n    \r\r\n    this.ui.get(\"standardExRate\").setData(\"\");\r\r\n    this.ui.get(\"creditedAmountinAccCurrency\").setData(\"\");\r\r\n    this.ui.get(\"negotiatedExRate\").setData(\"\");\r\r\n    this.ui.get(\"verifiedText\").setData(\"\");\r\r\n    \r\r\n    this.accountNumVis(value);\r\r\n}\r\r\n", "seq": "0", "description": "", "guid": "04ac8a78-ea46-420e-9f4d-98714a4126b3", "versionId": "8a6d8cf5-3ede-4f93-aceb-8704f237962b"}, "localization": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewLocalResId": "69.3aef3728-aa12-4628-be67-46105d5c4db4", "coachViewId": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "resourceBundleGroupId": "/50.********-d2e4-4682-b3ef-b9b22266bb5a", "seq": "0", "guid": "68c1db32-1ccb-4518-b2fe-f25aed06f851", "versionId": "ee26ce01-5493-43fd-ac34-749bf1277ed5"}}}}, "hasDetails": true}