{"id": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "versionId": "64571bed-66b5-41fa-87ad-9a9c8819f9f3", "name": "Contract Liquidation", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (this.context.options.isChecker.get(\"value\")) {\r\r\n\r\r\n\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(false);\r\r\n\tthis.ui.get(\"debitedAccountNo1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(false);\r\r\n}else{\r\r\n//\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(true);\r\r\n//\tthis.ui.get(\"debitedAccountNo1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(true);\r\r\n}", "bindingType": "odcRequest", "configOptions": ["glAccountVerified", "contractLiqVis", "customerAccounts", "exchangeRate", "<PERSON><PERSON><PERSON><PERSON>", "contractLiquidatedMSG", "verifyGLMsg"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//Validate Debited Nostro/Vostro Account\t\t\t\t\t\t\t\t\t\t\r\r\n this.valiadateAccCurrency = function (value)\r\r\n {\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo   = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\");\t\r\r\n\tvar currency    = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\tvar liqCurency  = this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"liqCurrency\");\t\r\r\n\t\r\r\n\tif (accountNo == value.getData() && currency != liqCurency) { \r\r\n//\t\t value.setData(\"\");\r\r\n//\t\t this.ui.get( \"debitedAccountNo1\").setValid(false,\"This account's currency must be the same as liquidation currency\");\t\r\r\n \tbreak;\r\r\n \t}\r\r\n\telse\r\r\n\t\t this.ui.get( \"debitedAccountNo1\").setValid(true);\t\r\r\n\t}//end of for\r\r\n}\r\r\n\r\r\n//Init Credit Account Data\r\r\nthis.setAccountInfo = function (value)\r\r\n {\r\r\n\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo= this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") ? this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") : \"\";\r\r\n\t\tif (accountNo == value.getData()) { \r\r\n\t\t\tvar branchCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"branchCode\");\r\r\n\t\t\tvar currency = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\t\t\tvar balance = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balance\");\r\r\n\t\t\tvar balanceSign = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balanceType\");\r\r\n\t\t\tvar classCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t \t\tclassCode= classCode? classCode.substring(0,1):\"\";\r\r\n\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"branchCode\", branchCode);\r\r\n\t \t\t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balance\", balance);\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balanceSign\", balanceSign);\r\r\n//\t \t\tconsole.log(\"------------------classCode: \"+classCode);\r\r\n\t \t\tif(classCode == \"O\" || classCode == \"D\")\r\r\n\t \t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",true);\r\r\n\t\t\telse\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",false);\r\r\n\t\t\t\t\t\t\r\r\n//\t\t\tvar od= this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n//\t\t\tconsole.log(\"------------------is over draft : \"+od);\t\t \t\t\r\r\n\t \t\t//SET CURRENCY\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"currency\", {} );\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"name\", currency);\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"value\", currency);\r\r\n\t\t\t\r\r\n\t\t\t//SET EXCHANGE RATE\r\r\n//\t\t\tview.setNegoRate(currency);\t\r\r\n \t\t}\r\r\n\t}\r\r\n}\r\r\n //Set credited Amount\r\r\n this.setNegoRate = function(value)\r\r\n {\t\r\r\n\r\r\n//\tconsole.log(\"----------------------INSIDE setNegoRate\");\r\r\n  \tvar liqCurr   =  this.ui.get(\"LiquidateionCurrency\").getData()? this.ui.get(\"LiquidateionCurrency\").getData():\"\";  \t\t \t\r\r\n  \tvar accCurr   =  value.getData()? value.getData():\"\"; \t \t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr == accCurr ) ) { \r\r\n\t\tthis.ui.get(\"standardExRate\").setData(1);\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setData(1);\r\r\n  \t\tthis.ui.get(\"negotiatedExRate\").setEnabled(false); \t\r\r\n \t} \t\t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr != accCurr ) ){\r\r\n//\t\t//call get exchange rate service\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n//\t\t console.log(\"---------- liqCurr != accCurr -------------------------\");\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setEnabled(true);\r\r\n//  \t\tvar inputCurrency={fromCurrency : liqCurr , toCurrency : accCurr};\r\r\n//  \t\tconsole.log(\"----------inputCurrency \"+inputCurrency);\r\r\n  \t\t\r\r\n//  \t\tvar parsedInputCurrency =  JSON.stringify(inputCurrency);\r\r\n//  \t\tconsole.log(\"----------parsedInputCurrency=   \"+parsedInputCurrency);\r\r\n//     \tconsole.log(\"*******************************************************************\");\r\r\n//\t\talert(parsedInputCurrency);\r\r\n//\t\tthis.ui.get(\"SC_getExRate\").execute(parsedInputCurrency);\r\r\n\t\t\r\r\n\t\tconcatedCurrency = {ccFrom : liqCurr , ccTo : accCurr , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\tinputCurr = JSON.stringify(concatedCurrency);\r\r\n\r\r\n\t\tthis.ui.get(\"GetExchangeRate\").execute(inputCurr);\r\r\n \t}\t\t\t\r\r\n}\r\r\n//SET Credited Amount From Exchange Service\r\r\n this.SetCreditAmountDetails = function(value){\r\r\n\r\r\n\tconsole.log(\"----------------------INSIDE SetCreditAmountDetails\");\r\r\n\tvar exRate = this.context.options.exchangeRate.get(\"value\");\r\r\n\r\r\n\tconsole.log(\"--------------- rate \"+ exRate);\r\r\n\tconsole.log(\"---------------ExRate fixed =  \"+ exRate.toFixed(6));\r\r\n\t\r\r\n\tthis.ui.get(\"standardExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\t\r\r\n\r\r\n } \r\r\n//calculate credited amount in cuurency\r\r\nthis.setAmountInCurrency = function(){\r\r\n\tvar liqAmount  =  this.ui.get(\"LiquidationAmount\").getData()? this.ui.get(\"LiquidationAmount\").getData():0;\r\r\n\tvar negoExRate =  this.ui.get(\"negotiatedExRate\").getData()? this.ui.get(\"negotiatedExRate\").getData():0;\r\r\n\t\r\r\n\tif( liqAmount > 0   &&   negoExRate > 0 )\r\r\n\t\tthis.ui.get(\"creditedAmountinAccCurrency\").setData(liqAmount * negoExRate);\r\r\n}\r\r\n\r\r\n//validate overDraft\r\r\nthis.validateOverDraft = function () {\r\r\n\tvar AccBalance          =  this.ui.get(\"AccountBalance\").getData()? this.ui.get(\"AccountBalance\").getData() : 0;\r\r\n\tvar amountinAccCurrency =  this.ui.get(\"creditedAmountinAccCurrency\").getData()? this.ui.get(\"creditedAmountinAccCurrency\").getData() : 0;\r\r\n\tvar overDraft           =  this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n\t\r\r\n\tif(amountinAccCurrency > AccBalance){\r\r\n\t\tif (overDraft) \r\r\n\t\t\t{\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"WARNING: Amount in account currency should be less than Account Balance\");\r\r\n\t\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"ERROR: Amount in Account Currency > Account Balance and Account Type is not Overdraft\");\r\r\n\t\t\t}\r\r\n\t\r\r\n\t}else{\r\r\n\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(true);\r\r\n\t}//end of if\r\r\n\t\r\r\n}\r\r\n\r\r\n//On load of account class - set vis according to account class (customer & gl)\r\r\nthis.accountNumVis = function (value) {\r\r\n    var accountClass = value.getData().name;\r\r\n    \r\r\n    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n        if (accountClass == \"Customer Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true,true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true,true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n\r\r\n        } else if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(true);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false,true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false,true);\r\r\n\r\r\n        }\r\r\n    } else {\r\r\n        this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n        this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n        \r\r\n        if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"glAccountNo\").setEnabled(false);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n   \r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false, true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false, true);\r\r\n            \r\r\n        } else {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\t\tthis.ui.get(\"customerAccountNo\").setEnabled(false);\r\r\n\t\t\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true, true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n        }\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of account class - reset data before choosing another account\r\r\nthis.resetAccountInfo = function (value){\r\r\n    this.ui.get(\"glAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"customerAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"AccountBalance\").setData(\"\");\r\r\n    this.ui.get(\"AccountBranchCode\").setData(\"\");\r\r\n    this.ui.get(\"BalanceSign\").setData(\"\");\r\r\n    this.ui.get(\"accountCurrency\").setData(\"\");\r\r\n    \r\r\n    this.ui.get(\"standardExRate\").setData(\"\");\r\r\n    this.ui.get(\"creditedAmountinAccCurrency\").setData(\"\");\r\r\n    this.ui.get(\"negotiatedExRate\").setData(\"\");\r\r\n    this.ui.get(\"verifiedText\").setData(\"\");\r\r\n    \r\r\n    this.accountNumVis(value);\r\r\n}"}]}, "hasDetails": true}