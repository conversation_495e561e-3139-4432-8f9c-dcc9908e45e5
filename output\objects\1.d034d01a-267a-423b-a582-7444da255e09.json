{"id": "1.d034d01a-267a-423b-a582-7444da255e09", "versionId": "cf53053e-c203-4386-b5cb-2dd80b75ae52", "name": "Get Request Nature", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.d034d01a-267a-423b-a582-7444da255e09", "name": "Get Request Nature", "lastModified": "1692079066533", "lastModifiedBy": "so<PERSON>ia", "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.e9baf714-1938-4062-8684-574cee95f542", "2025.e9baf714-1938-4062-8684-574cee95f542"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7ca0", "versionId": "cf53053e-c203-4386-b5cb-2dd80b75ae52", "dependencySummary": "<dependencySummary id=\"bpdid:e6d52ec61513ed03:232a663a:189f6396e2d:-280f\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"df88fc22-ad44-4515-88d9-15548f8c73f4\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"12c3c2bc-2d7c-4a7b-8e2c-3d8375b98f4d\"},{\"incoming\":[\"e4b14567-ae35-478a-88aa-b2840ea33150\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":314,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7ca2\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"06c87b1a-629b-4423-819d-b84b7b3308b0\"},{\"startQuantity\":1,\"outgoing\":[\"e4b14567-ae35-478a-88aa-b2840ea33150\"],\"incoming\":[\"df88fc22-ad44-4515-88d9-15548f8c73f4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":138,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Script Task\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"e9baf714-1938-4062-8684-574cee95f542\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"function getNVP (iName, iVal)\\r\\n{\\r\\n\\tvar temp   = new tw.object.NameValuePair();\\r\\n\\ttemp.name  = iName;\\r\\n\\ttemp.value = iVal;\\r\\n\\treturn temp;\\r\\n}\\r\\n\\r\\ntw.local.results=  new tw.object.listOf.NameValuePair();\\r\\n\\t\\t\\r\\ntw.local.results[tw.local.results.listLength] = getNVP( \\\"New Request\\\"   ,     tw.epv.RequestNature.NewRequest+   \\\"\\\"  );\\r\\ntw.local.results[tw.local.results.listLength] = getNVP( \\\"Update Request\\\"   ,     tw.epv.RequestNature.UpdateRequest+   \\\"\\\"  );\\r\\n\"]}},{\"targetRef\":\"e9baf714-1938-4062-8684-574cee95f542\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Script Task\",\"declaredType\":\"sequenceFlow\",\"id\":\"df88fc22-ad44-4515-88d9-15548f8c73f4\",\"sourceRef\":\"12c3c2bc-2d7c-4a7b-8e2c-3d8375b98f4d\"},{\"targetRef\":\"06c87b1a-629b-4423-819d-b84b7b3308b0\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e4b14567-ae35-478a-88aa-b2840ea33150\",\"sourceRef\":\"e9baf714-1938-4062-8684-574cee95f542\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorData\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.5c1cb572-0d09-4583-8310-05243d3dede6\"}],\"laneSet\":[{\"id\":\"3ee4ac55-c0eb-4582-8022-f51ca703e02f\",\"lane\":[{\"flowNodeRef\":[\"12c3c2bc-2d7c-4a7b-8e2c-3d8375b98f4d\",\"06c87b1a-629b-4423-819d-b84b7b3308b0\",\"e9baf714-1938-4062-8684-574cee95f542\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"5c6a0b16-f7d4-4c19-8e8e-9d44022f1749\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get Request Nature\",\"declaredType\":\"process\",\"id\":\"1.d034d01a-267a-423b-a582-7444da255e09\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"results\",\"isCollection\":true,\"id\":\"2055.80eb50e9-1059-4257-888c-1b70238b7436\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7\",\"epvProcessLinkId\":\"f81ea7c8-3678-4bb7-804d-1fc199dcbdba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.80eb50e9-1059-4257-888c-1b70238b7436\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"null\"}]},\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"data\",\"isCollection\":false,\"id\":\"2055.*************-4984-8f56-b291bb4d9d07\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.*************-4984-8f56-b291bb4d9d07", "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "1", "hasDefault": "true", "defaultValue": "null", "isLocked": "false", "description": {"isNull": "true"}, "guid": "3aa7b01a-ac82-41a3-8d9f-49836f4c384a", "versionId": "91798c4d-b934-436c-a625-569e5f14aed9"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.80eb50e9-1059-4257-888c-1b70238b7436", "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "parameterType": "2", "isArrayOf": "true", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f57e77c6-21d4-4e40-b27b-2a9e8b5312bf", "versionId": "be6db58f-2b38-42ed-a541-343d5b7a91ff"}], "processVariable": {"name": "errorData", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5c1cb572-0d09-4583-8310-05243d3dede6", "description": {"isNull": "true"}, "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "557bb5ef-fa56-44af-b6cf-c25efe6b21cf", "versionId": "5a9f81ad-6733-445a-8db2-65a5d2b8988f"}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.06c87b1a-629b-4423-819d-b84b7b3308b0", "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.1581f646-e3d1-4483-bc4d-4df238d885ab", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7ca2", "versionId": "2b310cc8-90c1-42a1-a0ea-3396acdada1a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "314", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.1581f646-e3d1-4483-bc4d-4df238d885ab", "haltProcess": "false", "guid": "a79f2602-3adb-4554-82f6-ac065f6edc7a", "versionId": "47308c7a-f6f7-48e3-a3d5-b330d1343e80"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e9baf714-1938-4062-8684-574cee95f542", "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "name": "Script Task", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.51da3091-2c41-4faf-b782-c1adf431af95", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7cb8", "versionId": "5b08006d-02a9-453c-b202-8f652d72e530", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "138", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.51da3091-2c41-4faf-b782-c1adf431af95", "scriptTypeId": "2", "isActive": "true", "script": "function getNVP (iName, iVal)\r\r\n{\r\r\n\tvar temp   = new tw.object.NameValuePair();\r\r\n\ttemp.name  = iName;\r\r\n\ttemp.value = iVal;\r\r\n\treturn temp;\r\r\n}\r\r\n\r\r\ntw.local.results=  new tw.object.listOf.NameValuePair();\r\r\n\t\t\r\r\ntw.local.results[tw.local.results.listLength] = getNVP( \"New Request\"   ,     tw.epv.RequestNature.NewRequest+   \"\"  );\r\r\ntw.local.results[tw.local.results.listLength] = getNVP( \"Update Request\"   ,     tw.epv.RequestNature.UpdateRequest+   \"\"  );\r\r\n", "isRule": "false", "guid": "b22cb613-688a-4a6c-9c9c-681aa37f8dfb", "versionId": "20cec3e6-cd5d-4c8b-b51b-37aba408ddc1"}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.ef79e578-21ce-4c0c-bc93-5441a30bd9ad", "epvId": "/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "guid": "418c45b0-8a3e-43c8-84ad-a529494e6f41", "versionId": "635b7282-ce73-4bfb-a32f-8c902ac15ddf"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Request Nature", "id": "1.d034d01a-267a-423b-a582-7444da255e09", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "epvProcessLinkId": "f81ea7c8-3678-4bb7-804d-1fc199dcbdba"}}}, "ns16:dataInput": {"name": "data", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.*************-4984-8f56-b291bb4d9d07", "ns16:extensionElements": {"ns3:defaultValue": {"_": "null", "useDefault": "true"}}}, "ns16:dataOutput": {"name": "results", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "true", "id": "2055.80eb50e9-1059-4257-888c-1b70238b7436"}, "ns16:inputSet": "", "ns16:outputSet": {"ns16:dataOutputRefs": "2055.80eb50e9-1059-4257-888c-1b70238b7436"}}, "ns16:laneSet": {"id": "3ee4ac55-c0eb-4582-8022-f51ca703e02f", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "5c6a0b16-f7d4-4c19-8e8e-9d44022f1749", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["12c3c2bc-2d7c-4a7b-8e2c-3d8375b98f4d", "06c87b1a-629b-4423-819d-b84b7b3308b0", "e9baf714-1938-4062-8684-574cee95f542"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "12c3c2bc-2d7c-4a7b-8e2c-3d8375b98f4d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "df88fc22-ad44-4515-88d9-15548f8c73f4"}, "ns16:endEvent": {"name": "End", "id": "06c87b1a-629b-4423-819d-b84b7b3308b0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "314", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7ca2"}, "ns16:incoming": "e4b14567-ae35-478a-88aa-b2840ea33150"}, "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Script Task", "id": "e9baf714-1938-4062-8684-574cee95f542", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "138", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "df88fc22-ad44-4515-88d9-15548f8c73f4", "ns16:outgoing": "e4b14567-ae35-478a-88aa-b2840ea33150", "ns16:script": "function getNVP (iName, iVal)\r\r\n{\r\r\n\tvar temp   = new tw.object.NameValuePair();\r\r\n\ttemp.name  = iName;\r\r\n\ttemp.value = iVal;\r\r\n\treturn temp;\r\r\n}\r\r\n\r\r\ntw.local.results=  new tw.object.listOf.NameValuePair();\r\r\n\t\t\r\r\ntw.local.results[tw.local.results.listLength] = getNVP( \"New Request\"   ,     tw.epv.RequestNature.NewRequest+   \"\"  );\r\r\ntw.local.results[tw.local.results.listLength] = getNVP( \"Update Request\"   ,     tw.epv.RequestNature.UpdateRequest+   \"\"  );\r\r\n"}, "ns16:sequenceFlow": [{"sourceRef": "12c3c2bc-2d7c-4a7b-8e2c-3d8375b98f4d", "targetRef": "e9baf714-1938-4062-8684-574cee95f542", "name": "To <PERSON>ript Task", "id": "df88fc22-ad44-4515-88d9-15548f8c73f4", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "e9baf714-1938-4062-8684-574cee95f542", "targetRef": "06c87b1a-629b-4423-819d-b84b7b3308b0", "name": "To End", "id": "e4b14567-ae35-478a-88aa-b2840ea33150", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:dataObject": {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorData", "id": "2056.5c1cb572-0d09-4583-8310-05243d3dede6"}}}}, "link": {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e4b14567-ae35-478a-88aa-b2840ea33150", "processId": "1.d034d01a-267a-423b-a582-7444da255e09", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e9baf714-1938-4062-8684-574cee95f542", "2025.e9baf714-1938-4062-8684-574cee95f542"], "endStateId": "Out", "toProcessItemId": ["2025.06c87b1a-629b-4423-819d-b84b7b3308b0", "2025.06c87b1a-629b-4423-819d-b84b7b3308b0"], "guid": "89e8d7a7-d57e-4cb2-a414-86004546840a", "versionId": "693be2b9-f8ce-4e82-a4a6-8bef040277e2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}}}}, "subType": "12", "hasDetails": false}