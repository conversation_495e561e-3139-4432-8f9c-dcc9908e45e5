{"id": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "versionId": "2d44f93c-7507-483a-9a42-c82155bd0166", "name": "Col01 - Create ODC Collection Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "routingDetails", "hasDefault": false, "type": "1"}, {"name": "parentPath", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}, {"name": "customerAccounts", "hasDefault": false, "type": "2"}, {"name": "parentPath", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "errorMessageVis", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "isSuccessful", "hasDefault": false}, {"name": "ldapUserProfile", "hasDefault": false}, {"name": "parentRequestNoVIS", "hasDefault": false}, {"name": "error", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "requestTypeVIS", "hasDefault": false}, {"name": "glAccountVerified", "hasDefault": false}, {"name": "accounteePartyCif", "hasDefault": false}, {"name": "code", "hasDefault": false}, {"name": "fullPath", "hasDefault": false}, {"name": "serviceName", "hasDefault": false}, {"name": "isfound", "hasDefault": false}, {"name": "message", "hasDefault": false}, {"name": "ValidationMessage", "hasDefault": false}, {"name": "customerAndPartyCifs", "hasDefault": false}, {"name": "rate", "hasDefault": false}, {"name": "calculatedChangeAmnt", "hasDefault": false}, {"name": "selectedIndex", "hasDefault": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "hasDefault": false}, {"name": "exchangeRate", "hasDefault": false}, {"name": "contractLiquidatedMSG", "hasDefault": false}, {"name": "verifyGLMsg", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Coach", "id": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Set Status And Sub Status", "id": "2025.0d904442-1dd4-4df6-8233-4024a60b0373", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Update History", "id": "2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Create ECM Folder", "id": "2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Set ECM default properties", "id": "2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Customer Account Details", "id": "2025.0329ea67-2d4f-42e1-820b-1a787ba00c89", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Retrieve Request Number", "id": "2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Copy of Set ECM default properties", "id": "2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit Collection Data", "id": "2025.8ac7db6a-a3f2-40ee-8ed4-950fba71236e", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Charges Completed", "id": "2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Retrieve Product Code", "id": "2025.d27bf540-679c-4767-8059-acaba3a0fe48", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Debited Nostro Vostro Account", "id": "2025.089c7969-235e-42a6-8fd7-0ca88d4fbda3", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Customer and Party Account List", "id": "2025.af31c948-21e2-4363-8234-74199d1a467d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Valid?", "id": "2025.0498ad64-a11a-4297-8edc-edb674ae3556", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Is Liquidated?", "id": "2025.e46d5c23-43ac-4ee4-8c70-639786a54d52", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "is Liquidated?", "id": "2025.12d89ada-06f5-45ab-8d7d-0112f50eacdd", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "log history?", "id": "2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "audited successfully?", "id": "2025.8e37f535-a92e-4d3a-88a6-c21f436f9585", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Has ProductCode?", "id": "2025.284f7314-8e0a-4308-88b4-ac5db2f03168", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "Init", "id": "2025.be89ff1b-cbda-4adf-81c1-5d29594b12e6", "script": "tw.local.isChecker = false;\r\r\n\r\r\n//---------------------- Init appInfo  \"This object is used in Header CV\"---------------------------------\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\t\r\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\ntw.local.odcRequest.appInfo.requestName = \"ODC collection\";\r\r\ntw.local.odcRequest.appInfo.requestType = tw.local.odcRequest.requestType.name;\r\r\ntw.local.odcRequest.appInfo.status =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.subStatus =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo; //tw.system.processInstance.id;\r\r\n\r\r\n//---------------------------------Init Step log ---------------------------------------------------\t \t \r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step = tw.epv.Col_ScreenNames.ODCCol01; \r\r\n\r\r\n //----------------------------------------------------------------------------------------------\r\r\n//Init actionConditions \"This object used by service (Get Actions by ScreenName 2) \"\r\r\ntw.local.actionConditions={};\r\r\ntw.local.actionConditions.complianceApproval= false;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.odcRequest.stepLog.action;\r\r\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\t \r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\t\r\r\n\r\r\n//Init liquidation data\r\r\ntw.local.odcRequest.ContractLiquidation.liqCurrency= tw.local.odcRequest.FinancialDetailsBR.currency.value;\r\r\n//init request state \r\r\ntw.local.odcRequest.BasicDetails.requestState=\"undergoing collection\";\r\r\n\r\r\nif(tw.local.odcRequest.isLiquidated == false)\t{\r\r\n\ttw.local.odcRequest.ContractLiquidation= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAmount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount.currency={};\r\r\n\t}\r\r\n\t\r\r\ntw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\r\r\ntw.local.odcRequest.BasicDetails.requestType = \ttw.local.odcRequest.requestType.name;\r\r\n\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass={};\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.name= \"Customer Account\";\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value= \"Customer Account\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Validation", "id": "2025.2cd1e05e-e819-41b6-8d95-dfb27dd0b710", "script": "//\t debugger;\r\r\ntw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel){\r\r\n    mandatory(tw.local.odcRequest.stepLog.comment, \"tw.local.odcRequest.stepLog.comment\");\r\r\n}\r\r\n\r\r\nif (true) {\r\r\n\r\r\n    validateLiq();\r\r\n}\r\r\n\r\r\n//------------------------------------------------- Contract Liquidation VALIDATION -----------------------------------------\r\r\nfunction validateLiq() {\r\r\n    //debugger;\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.liqAmount, \"tw.local.odcRequest.ContractLiquidation.liqAmount\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditValueDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitValueDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\");\r\r\n    \r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.creditValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\", \"Invalid date\", \"Credit Value Date should not be less than request date\");\r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.debitValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\", \"Invalid date\", \"Debit Value Date should not be less than request date\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitedAccountNo, \"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\");\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value == \"GL Account\") {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo\");\r\r\n\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified == false) {\r\r\n            tw.local.errorMessage += \"<li>\" + \"GL Account is not verified.\" + \"</li>\";\r\r\n            tw.system.coachValidation.addValidationError(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"GL Account is not verified.\");\r\r\n        }\r\r\n\r\r\n        //debugger;\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified) {\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode\");\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.currency\");\r\r\n        }\r\r\n\r\r\n        //Validate OverDraft\r\r\n        if (tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount > tw.local.odcRequest.ContractLiquidation.creditedAccount.balance && tw.local.odcRequest.ContractLiquidation.creditedAccount.isOverDraft) {\r\r\n            addError(\"tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\")\r\r\n        }\r\r\n    \r\r\n    } else {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo\");\r\r\n    }\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.liqAmount > tw.local.odcRequest.FinancialDetailsFO.outstandingAmount) {\r\r\n        addError(\"tw.local.odcRequest.ContractLiquidation.liqAmount\", \"Liquidation Amount must be <= ODC Outstanding Amount\");\r\r\n        tw.local.errorMessage += \"<li>\" + \"Liquidation Amount must be less than or equal to ODC Outstanding Amount.\" + \"</li>\";\r\r\n    }\r\r\n\r\r\n    //----------------------------------------------- Charges and Commissions VALIDATION -------------------------------------\t\t\r\r\n    for (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\r\n\r\r\n        //Description - Flat Amount\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            //        mandatory(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\");\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 2, \"Shouldn't be less than 2 character\", \"Change Amount: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 14, \"Shouldn't be more than 14 character\", \"Change Amount:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n        //Fixed Rate\r\r\n        } else {\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 2, \"Shouldn't be less than 2 character\", \"Change Percentage: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 14, \"Shouldn't be more than 14 character\", \"Change Percentage:\" + \"Shouldn't be more than 14 character\");\r\r\n        }\r\r\n\r\r\n        //skip validation if waiver or changeAmnt < 0\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].waiver == false && tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n\r\r\n            //Validate debited accounts\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.accountClass\");\r\r\n\r\r\n            //GL Account\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value == \"GL Account\") {\r\r\n\r\r\n                if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\r\n                    addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\", \"GL Account Not Verified\");\r\r\n                }\r\r\n\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.currency.value\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.branchCode\");\r\r\n\r\r\n                //Customer Account\r\r\n            } else {\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            }\r\r\n\r\r\n            //DebitedAmount\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\r\r\n                validateDecimal(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", \"Must be Decimal(16,10)\")\r\r\n            }\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 2, \"Shouldn't be less than 2 character\", \"Negotiated Exchange Rate: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 14, \"Shouldn't be more than 14 character\", \"Negotiated Exchange Rate:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n            //Correct Validation but Waiting confirmation on what to do if GL account\r\r\n            if ((tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\r\r\n                tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance) && tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\");\r\r\n            }\r\r\n        }\r\r\n    }\r\r\n}   //end of validation function\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\r\n    tw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n    fromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the date1 is less than date2\r\r\n*\t\r\r\n* EX:checkDate( date, requestDate ,  'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction checkDate(date, requestDate, dateName, controlMessage, validationMessage) {\r\r\n    if (date != null && date != undefined && date < requestDate) {\r\r\n        addError(dateName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field != null && field != undefined && field.length < len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field.length > len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field, fieldName) {\r\r\n    if (field == null || field == undefined) {\r\r\n        addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n        mandatoryTriggered = true;\r\r\n        return false;\r\r\n    }\r\r\n    else {\r\r\n        switch (typeof field) {\r\r\n            case \"string\":\r\r\n                if (field.trim() != undefined && field.trim() != null && field.trim().length == 0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            case \"number\":\r\r\n                if (field == 0.0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                if (field < 0) {\r\r\n                    var msg = \"Invalid Value, This field can not be negative value.\";\r\r\n                    addError(fieldName, msg, msg, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n\r\r\n            case \"boolean\":\r\r\n                if (field == false) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            default:\r\r\n\r\r\n                // VALIDATE DATE OBJECT\r\r\n                if (field && field.getTime && isFinite(field.getTime())) { }\r\r\n\r\r\n                else {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n        }\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n\r\r\n//Validate Decimal(10,6)\r\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,4}(\\.\\d{1,6})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\n\r\r\n//Validate Decimal(14,2)\r\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\r\n    validationMessage = controlMessage;\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,12}(\\.\\d{1,2})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\ntw.local.errorMessage != null ? tw.local.errorMessageVis = \"EDITABLE\" : tw.local.errorMessageVis = \"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Set Liq Flag", "id": "2025.cd5b6d91-8537-44c2-8483-79d91585bc68", "script": "tw.local.odcRequest.isLiquidated= true;\r\r\ntw.local.odcRequest.stepLog.action=\"\";\r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\r\r\n\r\r\ntw.local.isChecker = true;\r\r\ntw.local.contractLiquidatedMSG = \"Contract Liquidated Successfully\";\r\r\n//IMPORTANT : comment this line if you want to test from inside CSHS!!!\r\r\nwindow.location.reload();", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Set Attachments list", "id": "2025.74cef49e-b3e2-42aa-81f0-238dc5de00ef", "script": "if(!tw.local.odcRequest.attachmentDetails)\r\r\n\ttw.local.odcRequest.attachmentDetails= {};\r\r\n\t\r\r\nif (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {\r\r\n\ttw.local.odcRequest.attachmentDetails.attachment = []; \r\r\n\t\r\r\n}\t\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"طلب العميل\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].name = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].description = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].name = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].description = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].arabicName = \"فاتورة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].name = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].description = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].name = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].description = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].name = \"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].description =\"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].name = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].description = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].name = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].description = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].name = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].description = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].name = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].description = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].description = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].name = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].description = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].name = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].description = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].name = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].description = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].name = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].description = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].description = \"Bill of exchange/draft\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].arabicName = \"الكمبيالة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].name = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].description = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].name = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].description = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].arabicName = \"\";;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].name = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].description = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].arabicName = \"\";\r\r\n\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].name = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].description = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = {};\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Client-<PERSON>", "id": "2025.930604c9-ac9a-474f-88f3-998e271c68e6", "script": "tw.error.data", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Col01 - Create ODC Collection Request", "lastModified": "1716982817105", "lastModifiedBy": "mohamed.reda", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.0e2ba1eb-4c5b-4ef0-92fa-ae15d9eab69d", "2025.0e2ba1eb-4c5b-4ef0-92fa-ae15d9eab69d"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "true", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:7dd7", "versionId": "2d44f93c-7507-483a-9a42-c82155bd0166", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"mobileReady\":[true],\"userTaskImplementation\":[{\"artifact\":[{\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":196,\"x\":1121,\"y\":10,\"declaredType\":\"TNodeVisualInfo\",\"height\":72}]},\"declaredType\":\"textAnnotation\",\"text\":{\"content\":[\"\\/\\/IMPORTANT : comment ths last line on (Set Liq Flag script) if you want to test from inside CSHS!!!\"]},\"id\":\"f5971490-8436-4241-85a4-878810f50042\",\"textFormat\":\"text\\/plain\"}],\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.5a0bf287-5dbb-4cf8-84c3-b0f6f375c4f6\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":-71,\"y\":199,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"7800076a-9c61-4d46-8fe5-61eda608de2f\"},{\"outgoing\":[\"2027.8ebe87c1-8533-4f25-83cb-24907c146d75\",\"2027.4c92b8b9-010a-45a8-8d5b-0ba165ad5df3\"],\"incoming\":[\"2027.636dd5a7-5fa4-479c-8391-90dea46b8d50\",\"2027.7faa0719-e927-4841-8b1d-b4bb154c666e\",\"2027.3de7292b-4475-43dd-838d-9114302ffd0f\",\"2027.65484a87-94c7-49cd-8dd2-04d8479aae9d\",\"2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b\",\"2027.5a0bf287-5dbb-4cf8-84c3-b0f6f375c4f6\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":760,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"],\"preAssignmentScript\":[\"\\r\\nif(tw.local.odcRequest.ContractLiquidation.liqCurrency== null || tw.local.odcRequest.ContractLiquidation.liqCurrency == \\\"\\\") \\r\\n\\ttw.local.odcRequest.ContractLiquidation.liqCurrency= \\\"USD\\\";\\r\\n\\t\\r\\n\\t\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Contract_Liquidation1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bc906cd5-1b43-49fb-899d-cabca5acfa8e\",\"optionName\":\"@label\",\"value\":\"Contract Liquidation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f43e7550-20ab-40e2-8b6f-555fbc7a7fcf\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97b8c6a1-4a4c-46c3-8636-71178065fd38\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f07b0a4c-42ad-4665-8211-9f61d8014f31\",\"optionName\":\"cif\",\"value\":\"tw.local.odcRequest.Parties.partyTypes.partyCIF\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6738b37a-96d9-4e53-8486-7c8a0308ee42\",\"optionName\":\"isGlAccount\",\"value\":\"tw.local.isGlAccount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5b0023d9-26ad-40fd-84fe-0092dec7a799\",\"optionName\":\"customerAccounts\",\"value\":\"tw.local.odcRequest.customerAndPartyAccountList[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"44e61d5d-d04f-4494-8276-a96d7c26131c\",\"optionName\":\"glAccountVerified\",\"value\":\"tw.local.glAccountVerified\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8e3ffba0-7e90-4319-89d2-2cc47766b791\",\"optionName\":\"isChecker\",\"value\":\"tw.local.isChecker\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"88ba196a-2e0a-429f-8ccd-2f744c362efd\",\"optionName\":\"contractLiquidatedMSG\",\"value\":\"tw.local.contractLiquidatedMSG\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"62fe5b1e-ffa1-4b73-8338-56dcb07f3d79\",\"optionName\":\"verifyGLMsg\",\"value\":\"tw.local.verifyGLMsg\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6bac171a-70ee-4f51-866e-279afe83e8b0\",\"optionName\":\"exchangeRate\",\"value\":\"tw.local.exchangeRate\"}],\"viewUUID\":\"64.14ee925a-157a-48e5-9ab8-7b8879adbe5b\",\"binding\":\"tw.local.odcRequest\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"3a917dbe-ea3c-41a8-8c4e-feb95a130e78\",\"version\":\"8550\"},{\"layoutItemId\":\"Charges_And_Commissions_CV_21\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"79f54cfb-3f33-4ef7-858f-a38674262f9d\",\"optionName\":\"@label\",\"value\":\"Charges And Commissions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"911958c1-a887-449b-8755-389d4e7d2123\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e4eea5bb-2cfa-4d0c-8718-12fe36d5295a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"591022a9-e70d-42e0-81cb-acbfc61aa581\",\"optionName\":\"processType\",\"value\":\"LIQD\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4dacacd4-f2d2-40f2-8d6f-bc8448c0ca8e\",\"optionName\":\"amountCollectableByNBE\",\"value\":\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6fad35b0-6227-4c7c-8f07-630557071f74\",\"optionName\":\"exRate\",\"value\":\"tw.local.rate\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2a3fa55d-c346-4a32-8abd-3c27dd5c51c9\",\"optionName\":\"chargesCustomerAccountList\",\"value\":\"tw.local.odcRequest.customerAndPartyAccountList[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8213ce95-714a-4614-8205-b04208bb4157\",\"optionName\":\"calculatedChangeAmnt\",\"value\":\"tw.local.calculatedChangeAmnt\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c0e9dd94-85a4-426d-88f6-96dbb4bbe633\",\"optionName\":\"index\",\"value\":\"tw.local.selectedIndex\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f79267d2-d80e-47d0-80af-569e27abcb3e\",\"optionName\":\"isChecker\",\"value\":\"tw.local.isChecker\"}],\"viewUUID\":\"64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52\",\"binding\":\"tw.local.odcRequest.ChargesAndCommissions[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"8a678e26-**************-83943d6c7afa\",\"version\":\"8550\"},{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9b21a244-7684-40a4-87df-e49428d013f3\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7acd67eb-9a60-4b90-88cb-543066e77808\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0293df39-1587-46e3-83d2-4a0918846c3d\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2907c973-dfb0-4c89-8c91-cfb4abf56cb1\",\"optionName\":\"basicDetailsVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f1fd81f3-ec3d-4c01-80ce-3d1a31c5409a\",\"optionName\":\"requestVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9b13236c-9c9a-46f3-8066-7b2f8bb9c70e\",\"optionName\":\"requestTypeVIS\",\"value\":\"READONLY\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fcf313ce-e0ff-452f-87bf-1df7e6fc97c3\",\"optionName\":\"parentRequestNoVis\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"806c40a1-9a12-4b34-8078-7b34d3ab2993\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cc14c2c1-a593-4f82-8b94-7d24629d0279\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"NONE\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cc5fb8aa-9f2a-4e63-859f-210dba97a3e0\",\"optionName\":\"contractStageVIS\",\"value\":\"NONE\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"532735ae-dee4-4bf2-86e4-a116bfe2eecf\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"36b1599e-83de-4212-89c2-db792affb341\",\"optionName\":\"@label\",\"value\":\"Customer Info\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f0b9d1a7-96ab-4c99-868c-2861681fbb50\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"05b59a76-a5a1-4190-8109-0cc2442c3241\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d5bd2cf7-cf68-4e98-8bd6-c9a2e72d69c7\",\"optionName\":\"customerInfoVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2f279f23-07e4-46f0-8f52-cf2f9dc1e04a\",\"optionName\":\"requestTypeVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"45f8ea33-f85b-47dd-8047-babb2c82c869\",\"optionName\":\"listsVIS\",\"value\":\"READONLY\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"8f5f2747-73fd-4c2b-85b0-2f72bc4b2914\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"62a2d995-e92f-4c2d-85c8-6ddc773f9bf7\",\"optionName\":\"@label\",\"value\":\"Financial Details - Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"50285e85-d620-4be2-88e6-3a7d6178e41f\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"05584db9-79dc-423b-875e-e097ca606909\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"aedd8df5-a82e-4e3d-8534-7207a23afe1e\",\"optionName\":\"financialDetailsVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"42614bca-fec8-463d-886d-69c9a2a21408\",\"optionName\":\"fcCollectionVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d2a5acc4-d48e-4922-8ce3-5ef577d5bfbf\",\"optionName\":\"documentAmountVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"236b2450-aa3a-47fd-8e76-1eeea7fa5de7\",\"optionName\":\"currencyVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2c7d4a6b-3469-49b4-8694-9bbd44d5bf6c\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d3b50ee4-6fe5-4d99-8cea-cc90a0ffa85f\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"98c22cf5-d006-4ce1-8578-38fb9d4a10f0\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"078ff71d-c1db-42e2-8b6f-503a29653467\",\"optionName\":\"@label\",\"value\":\"Attachments\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cb030272-bcda-46bd-8dc0-747419cf6bd1\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ca345b3c-21b8-45e4-8ec5-404a5efe4563\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"65b7bfc3-08e8-44ef-856f-d370965a4497\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bf275b39-f339-4c2e-82fa-565dcd7598b9\",\"optionName\":\"canCreate\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e8bc6189-9682-4906-83b6-71dba109fd48\",\"optionName\":\"canDelete\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2af97230-d7dd-46b8-8ebb-dac7832f2594\",\"optionName\":\"canUpdate\",\"value\":\"true\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"9f4746bb-5f14-4387-8e13-15fcb74c27ac\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d2bdd5c0-f21f-4df9-8d4f-703b07f60391\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4e56f2e9-2267-40d7-8c47-6ac04eafcac1\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0b11b893-737b-4a6d-8120-413b3b4ade9a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"820a42fa-e501-408e-8ec1-0f13dc2649af\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"5f7d81ad-8ce2-4bb5-8d2a-df9ddf7a469d\"}],\"layoutItemId\":\"Tab_Section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ea1eb403-5a6a-4696-86fe-0f8d21c9441a\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a833f7fe-412d-4991-814a-beb023555da6\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1556a30e-990f-415e-8d6c-6f02596a5d9a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"9181030c-bbfb-4bb2-80d3-b075fb0396a0\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"38aa82d9-e867-4713-8baa-0c3620981e06\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97e1a2bf-7518-4b2d-86fd-49a64596da75\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3de82d5d-d1bb-4b53-8d59-8ac22baef4ee\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"06c8e540-e5dc-4f91-896d-1a9407110497\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"df201b11-8810-4f02-8d38-c75474141602\",\"optionName\":\"terminateReasonVIS\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a269fc56-5d1e-4316-85de-067553ef3196\",\"optionName\":\"errorPanelVIS\",\"value\":\"tw.local.errorMessageVis\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0f789f32-ff1a-4347-890d-42b22c7892f4\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5fa863b5-7bf4-4210-8d18-347b03f94cde\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c3994762-d884-480e-8c1c-4d5bca764c34\",\"optionName\":\"complianceApprovalVis\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3ced2ef0-d5a2-4ec9-803b-82b20b2e66dd\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"38d6d627-94c0-4831-8c42-2b008b046c67\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2d4f7514-3c2e-4e83-8e23-539e50a54965\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3398b5f5-fd3a-4f3d-84f8-5b94be63ac8f\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1606c373-1cfb-453e-8652-425992d67950\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7140178b-5f69-4b79-80b6-2c5d72698c5b\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"None\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"77b286bd-3738-49e3-8c24-11f97411a1fe\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Coach\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\"},{\"incoming\":[\"2027.c1ebf2f8-4232-41e8-8cda-d8fe0f78c763\",\"2027.bf2ae001-93f0-48cc-8308-299f31ba3e82\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1975,\"y\":199,\"declaredType\":\"TNodeVisualInfo\",\"height\":44}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"bd7713f0-26f4-411c-a13c-bd4bf8846a29\"},{\"targetRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To is Liquidated?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5a0bf287-5dbb-4cf8-84c3-b0f6f375c4f6\",\"sourceRef\":\"7800076a-9c61-4d46-8fe5-61eda608de2f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.653db24d-c64d-412d-8c14-ec9e5c3f46ec\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessageVis\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1d5065f2-8f3a-4d7c-8ee5-70d9ae7f65bf\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.f8c94287-6a30-4333-8c4c-13fd7742fee2\"},{\"startQuantity\":1,\"outgoing\":[\"2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b\"],\"incoming\":[\"2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5\"],\"default\":\"2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b\",\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":186,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Init\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.be89ff1b-cbda-4adf-81c1-5d29594b12e6\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.isChecker = false;\\r\\n\\r\\n\\/\\/---------------------- Init appInfo  \\\"This object is used in Header CV\\\"---------------------------------\\r\\nvar date = new Date();\\r\\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\\/' +(date.getMonth() + 1) + '\\/' + date.getFullYear();\\r\\n\\t\\r\\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\\\"( \\\"+ tw.system.user.name+\\\")\\\";\\r\\ntw.local.odcRequest.appInfo.requestName = \\\"ODC collection\\\";\\r\\ntw.local.odcRequest.appInfo.requestType = tw.local.odcRequest.requestType.name;\\r\\ntw.local.odcRequest.appInfo.status =\\\"Initiated\\\";\\r\\ntw.local.odcRequest.appInfo.subStatus =\\\"Initiated\\\";\\r\\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol01;\\r\\ntw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo; \\/\\/tw.system.processInstance.id;\\r\\n\\r\\n\\/\\/---------------------------------Init Step log ---------------------------------------------------\\t \\t \\r\\ntw.local.odcRequest.stepLog={};\\r\\ntw.local.odcRequest.stepLog.startTime = new Date();\\r\\ntw.local.odcRequest.stepLog.step = tw.epv.Col_ScreenNames.ODCCol01; \\r\\n\\r\\n \\/\\/----------------------------------------------------------------------------------------------\\r\\n\\/\\/Init actionConditions \\\"This object used by service (Get Actions by ScreenName 2) \\\"\\r\\ntw.local.actionConditions={};\\r\\ntw.local.actionConditions.complianceApproval= false;\\r\\ntw.local.actionConditions.lastStepAction= tw.local.odcRequest.stepLog.action;\\r\\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol01;\\r\\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\\r\\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\\t \\r\\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\\t\\r\\n\\r\\n\\/\\/Init liquidation data\\r\\ntw.local.odcRequest.ContractLiquidation.liqCurrency= tw.local.odcRequest.FinancialDetailsBR.currency.value;\\r\\n\\/\\/init request state \\r\\ntw.local.odcRequest.BasicDetails.requestState=\\\"undergoing collection\\\";\\r\\n\\r\\nif(tw.local.odcRequest.isLiquidated == false)\\t{\\r\\n\\ttw.local.odcRequest.ContractLiquidation= {};\\r\\n\\ttw.local.odcRequest.ContractLiquidation.creditedAccount= {};\\r\\n\\ttw.local.odcRequest.ContractLiquidation.creditedAmount= {};\\r\\n\\ttw.local.odcRequest.ContractLiquidation.creditedAccount.currency={};\\r\\n\\t}\\r\\n\\t\\r\\ntw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\\r\\ntw.local.odcRequest.BasicDetails.requestType = \\ttw.local.odcRequest.requestType.name;\\r\\n\\r\\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass={};\\r\\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.name= \\\"Customer Account\\\";\\r\\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value= \\\"Customer Account\\\";\\r\\n\\r\\n\\r\\n\"]}},{\"outgoing\":[\"2027.636dd5a7-5fa4-479c-8391-90dea46b8d50\"],\"incoming\":[\"2027.4c92b8b9-010a-45a8-8d5b-0ba165ad5df3\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.636dd5a7-5fa4-479c-8391-90dea46b8d50\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":803,\"y\":304,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.d0e44a57-c5bc-441e-8b1f-62fcdc4850e5\"},{\"targetRef\":\"2025.2cd1e05e-e819-41b6-8d95-dfb27dd0b710\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"42dd92fa-be2a-4d01-8ae0-3aec571876c7\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8ebe87c1-8533-4f25-83cb-24907c146d75\",\"sourceRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\"},{\"startQuantity\":1,\"outgoing\":[\"2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25\"],\"incoming\":[\"2027.8ebe87c1-8533-4f25-83cb-24907c146d75\"],\"default\":\"2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":936,\"y\":179,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.2cd1e05e-e819-41b6-8d95-dfb27dd0b710\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/\\t debugger;\\r\\ntw.local.errorMessage = \\\"\\\";\\r\\nvar mandatoryTriggered = false;\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\r\\n\\/\\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\\r\\nmandatory(tw.local.odcRequest.stepLog.action, \\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\r\\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel){\\r\\n    mandatory(tw.local.odcRequest.stepLog.comment, \\\"tw.local.odcRequest.stepLog.comment\\\");\\r\\n}\\r\\n\\r\\nif (true) {\\r\\n\\r\\n    validateLiq();\\r\\n}\\r\\n\\r\\n\\/\\/------------------------------------------------- Contract Liquidation VALIDATION -----------------------------------------\\r\\nfunction validateLiq() {\\r\\n    \\/\\/debugger;\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.liqAmount, \\\"tw.local.odcRequest.ContractLiquidation.liqAmount\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditValueDate, \\\"tw.local.odcRequest.ContractLiquidation.creditValueDate\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitValueDate, \\\"tw.local.odcRequest.ContractLiquidation.debitValueDate\\\");\\r\\n    \\r\\n    checkDate(tw.local.odcRequest.ContractLiquidation.creditValueDate, tw.local.odcRequest.requestDate, \\\"tw.local.odcRequest.ContractLiquidation.creditValueDate\\\", \\\"Invalid date\\\", \\\"Credit Value Date should not be less than request date\\\");\\r\\n    checkDate(tw.local.odcRequest.ContractLiquidation.debitValueDate, tw.local.odcRequest.requestDate, \\\"tw.local.odcRequest.ContractLiquidation.debitValueDate\\\", \\\"Invalid date\\\", \\\"Debit Value Date should not be less than request date\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitedAccountNo, \\\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\\\");\\r\\n\\r\\n    if (tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value == \\\"GL Account\\\") {\\r\\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo\\\");\\r\\n\\r\\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified == false) {\\r\\n            tw.local.errorMessage += \\\"<li>\\\" + \\\"GL Account is not verified.\\\" + \\\"<\\/li>\\\";\\r\\n            tw.system.coachValidation.addValidationError(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \\\"GL Account is not verified.\\\");\\r\\n        }\\r\\n\\r\\n        \\/\\/debugger;\\r\\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified) {\\r\\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode\\\");\\r\\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.value, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.currency\\\");\\r\\n        }\\r\\n\\r\\n        \\/\\/Validate OverDraft\\r\\n        if (tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount > tw.local.odcRequest.ContractLiquidation.creditedAccount.balance && tw.local.odcRequest.ContractLiquidation.creditedAccount.isOverDraft) {\\r\\n            addError(\\\"tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount\\\", \\\"ERROR: Must be <= Account Balance\\\")\\r\\n        }\\r\\n    \\r\\n    } else {\\r\\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo\\\");\\r\\n    }\\r\\n\\r\\n    if (tw.local.odcRequest.ContractLiquidation.liqAmount > tw.local.odcRequest.FinancialDetailsFO.outstandingAmount) {\\r\\n        addError(\\\"tw.local.odcRequest.ContractLiquidation.liqAmount\\\", \\\"Liquidation Amount must be <= ODC Outstanding Amount\\\");\\r\\n        tw.local.errorMessage += \\\"<li>\\\" + \\\"Liquidation Amount must be less than or equal to ODC Outstanding Amount.\\\" + \\\"<\\/li>\\\";\\r\\n    }\\r\\n\\r\\n    \\/\\/----------------------------------------------- Charges and Commissions VALIDATION -------------------------------------\\t\\t\\r\\n    for (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\\r\\n\\r\\n        \\/\\/Description - Flat Amount\\r\\n        if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \\\"Flat Amount\\\") {\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null) {\\r\\n                addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", \\\"Must be >= 0\\\");\\r\\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\\r\\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", \\\"Must be Decimal(14,2)\\\")\\r\\n            }\\r\\n            \\/\\/        mandatory(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\");\\r\\n\\r\\n            \\/\\/ minLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", 2, \\\"Shouldn't be less than 2 character\\\", \\\"Change Amount: Shouldn't be less than 2 character\\\");\\r\\n            \\/\\/ maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", 14, \\\"Shouldn't be more than 14 character\\\", \\\"Change Amount:\\\" + \\\"Shouldn't be more than 14 character\\\");\\r\\n\\r\\n        \\/\\/Fixed Rate\\r\\n        } else {\\r\\n\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null) {\\r\\n                addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", \\\"Must be >= 0\\\");\\r\\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\\r\\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", \\\"Must be Decimal(14,2)\\\")\\r\\n            }\\r\\n            \\/\\/ minLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", 2, \\\"Shouldn't be less than 2 character\\\", \\\"Change Percentage: Shouldn't be less than 2 character\\\");\\r\\n            \\/\\/ maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", 14, \\\"Shouldn't be more than 14 character\\\", \\\"Change Percentage:\\\" + \\\"Shouldn't be more than 14 character\\\");\\r\\n        }\\r\\n\\r\\n        \\/\\/skip validation if waiver or changeAmnt < 0\\r\\n        if (tw.local.odcRequest.ChargesAndCommissions[i].waiver == false && tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\\r\\n\\r\\n            \\/\\/Validate debited accounts\\r\\n            \\/\\/ mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.customerAccountNo\\\");\\r\\n            \\/\\/ mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.accountClass\\\");\\r\\n\\r\\n            \\/\\/GL Account\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value == \\\"GL Account\\\") {\\r\\n\\r\\n                if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\\r\\n                    addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.glAccountNo\\\", \\\"GL Account Not Verified\\\");\\r\\n                }\\r\\n\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.glAccountNo\\\");\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.currency.value\\\");\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.branchCode\\\");\\r\\n\\r\\n                \\/\\/Customer Account\\r\\n            } else {\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.customerAccountNo\\\");\\r\\n            }\\r\\n\\r\\n            \\/\\/DebitedAmount\\r\\n\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\\r\\n                validateDecimal(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.negotiatedExRate\\\", \\\"Must be Decimal(16,10)\\\")\\r\\n            }\\r\\n\\r\\n            \\/\\/ minLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.negotiatedExRate\\\", 2, \\\"Shouldn't be less than 2 character\\\", \\\"Negotiated Exchange Rate: Shouldn't be less than 2 character\\\");\\r\\n            \\/\\/ maxLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.negotiatedExRate\\\", 14, \\\"Shouldn't be more than 14 character\\\", \\\"Negotiated Exchange Rate:\\\" + \\\"Shouldn't be more than 14 character\\\");\\r\\n\\r\\n            \\/\\/Correct Validation but Waiting confirmation on what to do if GL account\\r\\n            if ((tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\\r\\n                tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance) && tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false) {\\r\\n                addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.amountInAccount\\\", \\\"ERROR: Must be <= Account Balance\\\");\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n}   \\/\\/end of validation function\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- Validation Functions ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/*\\r\\n* =========================================================================================================\\r\\n*  \\r\\n* Add a coach validation error \\r\\n* \\t\\t\\r\\n* EX:\\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\\r\\n*\\r\\n* =========================================================================================================\\r\\n*\\/\\r\\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\\r\\n    tw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n    fromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\n\\/*\\r\\n* =======================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the date1 is less than date2\\r\\n*\\t\\r\\n* EX:checkDate( date, requestDate ,  'this is a validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =======================================================================================================================\\r\\n*\\/\\r\\nfunction checkDate(date, requestDate, dateName, controlMessage, validationMessage) {\\r\\n    if (date != null && date != undefined && date < requestDate) {\\r\\n        addError(dateName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\/*\\r\\n* =================================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is less than given length\\r\\n*\\t\\r\\n* EX:\\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =================================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\\r\\n    if (field != null && field != undefined && field.length < len) {\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\/*\\r\\n* =======================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is greater than given length\\r\\n*\\t\\r\\n* EX:\\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =======================================================================================================================\\r\\n*\\/\\r\\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\\r\\n    if (field.length > len) {\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\/*\\r\\n* ==================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the field is null 'Mandatory'\\r\\n*\\t\\r\\n* EX:\\tnotNull(tw.local.name , 'tw.local.name')\\r\\n*\\r\\n* ==================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction mandatory(field, fieldName) {\\r\\n    if (field == null || field == undefined) {\\r\\n        addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n        mandatoryTriggered = true;\\r\\n        return false;\\r\\n    }\\r\\n    else {\\r\\n        switch (typeof field) {\\r\\n            case \\\"string\\\":\\r\\n                if (field.trim() != undefined && field.trim() != null && field.trim().length == 0) {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                break;\\r\\n            case \\\"number\\\":\\r\\n                if (field == 0.0) {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                if (field < 0) {\\r\\n                    var msg = \\\"Invalid Value, This field can not be negative value.\\\";\\r\\n                    addError(fieldName, msg, msg, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                break;\\r\\n\\r\\n            case \\\"boolean\\\":\\r\\n                if (field == false) {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                break;\\r\\n            default:\\r\\n\\r\\n                \\/\\/ VALIDATE DATE OBJECT\\r\\n                if (field && field.getTime && isFinite(field.getTime())) { }\\r\\n\\r\\n                else {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n        }\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\r\\n\\/\\/Validate Decimal(10,6)\\r\\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\\r\\n    \\/\\/ Regular expression pattern for decimal validation\\r\\n    var decimalPattern = \\/^\\\\d{1,4}(\\\\.\\\\d{1,6})?$\\/;\\r\\n\\r\\n    \\/\\/ Check if the decimal matches the pattern\\r\\n    if (!decimalPattern.test(field)) {\\r\\n        \\/\\/ Decimal is valid\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    } else {\\r\\n        \\/\\/ Decimal is invalid\\r\\n        return true;\\r\\n    }\\r\\n}\\r\\n\\r\\n\\/\\/Validate Decimal(14,2)\\r\\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\\r\\n    validationMessage = controlMessage;\\r\\n    \\/\\/ Regular expression pattern for decimal validation\\r\\n    var decimalPattern = \\/^\\\\d{1,12}(\\\\.\\\\d{1,2})?$\\/;\\r\\n\\r\\n    \\/\\/ Check if the decimal matches the pattern\\r\\n    if (!decimalPattern.test(field)) {\\r\\n        \\/\\/ Decimal is valid\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    } else {\\r\\n        \\/\\/ Decimal is invalid\\r\\n        return true;\\r\\n    }\\r\\n}\\r\\ntw.local.errorMessage != null ? tw.local.errorMessageVis = \\\"EDITABLE\\\" : tw.local.errorMessageVis = \\\"NONE\\\";\"]}},{\"outgoing\":[\"2027.bf2ae001-93f0-48cc-8308-299f31ba3e82\",\"2027.3eb187df-6798-4532-831c-f3943a96139c\"],\"incoming\":[\"2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25\"],\"default\":\"2027.bf2ae001-93f0-48cc-8308-299f31ba3e82\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1057,\"y\":198,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.0498ad64-a11a-4297-8edc-edb674ae3556\"},{\"targetRef\":\"2025.0498ad64-a11a-4297-8edc-edb674ae3556\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25\",\"sourceRef\":\"2025.2cd1e05e-e819-41b6-8d95-dfb27dd0b710\"},{\"targetRef\":\"bd7713f0-26f4-411c-a13c-bd4bf8846a29\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.bf2ae001-93f0-48cc-8308-299f31ba3e82\",\"sourceRef\":\"2025.0498ad64-a11a-4297-8edc-edb674ae3556\"},{\"incoming\":[\"2027.3eb187df-6798-4532-831c-f3943a96139c\",\"2027.90b70243-5a30-47be-8f4d-f00bd0e0968b\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":71,\"x\":1093,\"y\":314,\"declaredType\":\"TNodeVisualInfo\",\"height\":44}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}],\"preAssignmentScript\":[]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.888c4b9a-9fe3-4d9a-89fa-253db4480d44\"},{\"targetRef\":\"2025.888c4b9a-9fe3-4d9a-89fa-253db4480d44\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  !=\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3eb187df-6798-4532-831c-f3943a96139c\",\"sourceRef\":\"2025.0498ad64-a11a-4297-8edc-edb674ae3556\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.eb65bb62-bdde-49f0-8e18-************\"},{\"itemSubjectRef\":\"itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2\",\"name\":\"ldapUserProfile\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d99d5cbf-ba11-4e37-850b-200d74087f56\"},{\"outgoing\":[\"2027.f51aa2f2-028a-4d37-838c-c7684270c006\"],\"incoming\":[\"2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":1351,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.f51aa2f2-028a-4d37-838c-c7684270c006\",\"name\":\"Set Status And Sub Status\",\"dataInputAssociation\":[{\"targetRef\":\"2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\"]}}]},{\"targetRef\":\"2055.6010a917-3b0f-4c13-8973-51c4eede4cd9\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.stepName\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.0d904442-1dd4-4df6-8233-4024a60b0373\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}],\"sourceRef\":[\"2055.71a67d77-b802-42cf-8cf2-380a9594a9c2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}],\"sourceRef\":[\"2055.ce1f6bfc-b971-4978-80c8-092f5d40c209\"]}],\"calledElement\":\"1.81656d33-5348-479b-a7af-5631356d9476\"},{\"targetRef\":\"2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f51aa2f2-028a-4d37-838c-c7684270c006\",\"sourceRef\":\"2025.0d904442-1dd4-4df6-8233-4024a60b0373\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNoVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7362e801-01de-4688-890a-7d97f6662a9d\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7066f302-9d63-41d1-8111-fa98a5382b32\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"documentation\":[{\"content\":[\"to be deleted\\u00a0\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c9d26fb0-c6c4-4835-8283-e9fb2c177c30\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"documentation\":[{\"content\":[\"to be deleted\\u00a0\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"requestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.956aea2f-b087-4efd-8c4f-e533a6d31b54\"},{\"outgoing\":[\"2027.4f289ccc-**************-abbf4de7f9ba\"],\"incoming\":[\"2027.4ce9e417-e403-4d25-8a20-f4050c61dc84\",\"2027.f51aa2f2-028a-4d37-838c-c7684270c006\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1754,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.4f289ccc-**************-abbf4de7f9ba\",\"name\":\"Update History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.userRole.RevAct01\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.a617c560-c740-484e-89de-0931088cdc6c\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"glAccountVerified\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b60ac548-1c32-4f4e-8016-73b44d024442\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"accounteePartyCif\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.3a374913-eb91-4e0f-85d9-86c86cc66465\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"077\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"documentation\":[{\"content\":[\"to be deleted<div><br \\/><\\/div><div>001<\\/div>\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"code\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6a1c20ce-b779-4524-835b-d45c71c9ac6b\"},{\"targetRef\":\"2025.d0e44a57-c5bc-441e-8b1f-62fcdc4850e5\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"fb368007-d3ad-449a-8ffb-f56e2a35e4cc\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4c92b8b9-010a-45a8-8d5b-0ba165ad5df3\",\"sourceRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\"},{\"targetRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.636dd5a7-5fa4-479c-8391-90dea46b8d50\",\"sourceRef\":\"2025.d0e44a57-c5bc-441e-8b1f-62fcdc4850e5\"},{\"outgoing\":[\"2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb\",\"2027.bba3c37b-ac6e-4aeb-802b-ab0f9c907141\"],\"default\":\"2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1204,\"y\":195,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}],\"preAssignmentScript\":[\"tw.local.contractLiquidatedMSG = \\\"\\\";\"]},\"name\":\"Is Liquidated?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.e46d5c23-43ac-4ee4-8c70-639786a54d52\"},{\"targetRef\":\"2025.0d904442-1dd4-4df6-8233-4024a60b0373\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.Col_Actions.createLiq\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb\",\"sourceRef\":\"2025.e46d5c23-43ac-4ee4-8c70-639786a54d52\"},{\"targetRef\":\"2025.cd5b6d91-8537-44c2-8483-79d91585bc68\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.Col_Actions.createLiq\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.bba3c37b-ac6e-4aeb-802b-ab0f9c907141\",\"sourceRef\":\"2025.e46d5c23-43ac-4ee4-8c70-639786a54d52\"},{\"startQuantity\":1,\"outgoing\":[\"2027.90b70243-5a30-47be-8f4d-f00bd0e0968b\"],\"incoming\":[\"2027.bba3c37b-ac6e-4aeb-802b-ab0f9c907141\"],\"default\":\"2027.90b70243-5a30-47be-8f4d-f00bd0e0968b\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1195,\"y\":291,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Liq Flag\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.cd5b6d91-8537-44c2-8483-79d91585bc68\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.isLiquidated= true;\\r\\ntw.local.odcRequest.stepLog.action=\\\"\\\";\\r\\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\\r\\n\\r\\ntw.local.isChecker = true;\\r\\ntw.local.contractLiquidatedMSG = \\\"Contract Liquidated Successfully\\\";\\r\\n\\/\\/IMPORTANT : comment this line if you want to test from inside CSHS!!!\\r\\nwindow.location.reload();\\r\\n\"]}},{\"targetRef\":\"2025.888c4b9a-9fe3-4d9a-89fa-253db4480d44\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"leftCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To update actions list\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.90b70243-5a30-47be-8f4d-f00bd0e0968b\",\"sourceRef\":\"2025.cd5b6d91-8537-44c2-8483-79d91585bc68\"},{\"outgoing\":[\"2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e\"],\"incoming\":[\"2027.71bcd28a-81f6-44a4-8301-a22662997a00\"],\"extensionElements\":{\"postAssignmentScript\":[\"tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;\\/\\/ tw.local.parentPath\\r\\ntw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;\\r\\n\\r\\nif(!!tw.local.error && tw.local.error.errorText!=null)\\r\\n\\ttw.local.errorMessage+= tw.local.error.errorText;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":466,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.serviceName=\\\"Create\\\"\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e\",\"name\":\"Create ECM Folder\",\"dataInputAssociation\":[{\"targetRef\":\"2055.4156964b-1c67-40bc-8f62-3804c71cf908\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.e2ce0eed-342c-4942-8214-83e964b550e5\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.hubCode\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderID\"]}}],\"sourceRef\":[\"2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}],\"sourceRef\":[\"2055.5f955245-0538-4e40-80a6-12f45c3102f3\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.214c7268-80d0-444d-8702-dd0d5462dbe7\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.fullPath\"]}}],\"sourceRef\":[\"2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}],\"sourceRef\":[\"2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.676e3a06-e2cc-4855-84d6-6f82a350500a\"]}],\"calledElement\":\"1.46b984a3-b4ad-405a-abd3-8631f907efe4\"},{\"startQuantity\":1,\"outgoing\":[\"2027.0268cbe4-24b1-483d-86e8-f760a263aa20\"],\"default\":\"2027.0268cbe4-24b1-483d-86e8-f760a263aa20\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":294,\"y\":326,\"declaredType\":\"TNodeVisualInfo\",\"height\":69}]},\"name\":\"Set Attachments list\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.74cef49e-b3e2-42aa-81f0-238dc5de00ef\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(!tw.local.odcRequest.attachmentDetails)\\r\\n\\ttw.local.odcRequest.attachmentDetails= {};\\r\\n\\t\\r\\nif (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {\\r\\n\\ttw.local.odcRequest.attachmentDetails.attachment = []; \\r\\n\\t\\r\\n}\\t\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \\\"Customer Request\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \\\"Customer Request\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \\\"\\u0637\\u0644\\u0628 \\u0627\\u0644\\u0639\\u0645\\u064a\\u0644\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1].name = \\\"BILL OF EXCHANGE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1].description = \\\"BILL OF EXCHANGE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2].name = \\\"Invoice\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2].description = \\\"Invoice\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2].arabicName = \\\"\\u0641\\u0627\\u062a\\u0648\\u0631\\u0629\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3].name = \\\"BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3].description = \\\"BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4].name = \\\"AIRWAY BILL\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4].description = \\\"AIRWAY BILL\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4].arabicName = \\\"\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5].name = \\\"TRUCK CONSIGNMENT NOTE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5].description =\\\"TRUCK CONSIGNMENT NOTE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6].name = \\\"N\\/N BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6].description = \\\"N\\/N BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7].name = \\\"COURIER \\/ POST RECEIPT\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7].description = \\\"COURIER \\/ POST RECEIPT\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7].arabicName = \\\"\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8].name = \\\"PACKING LIST\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8].description = \\\"PACKING LIST\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9].name = \\\"CERTIFICATE OF ORIGIN\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9].description = \\\"CERTIFICATE OF ORIGIN\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9].arabicName = \\\"\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10].name = \\\"CERTIFICATE OF ANALYSIS\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10].description = \\\"CERTIFICATE OF ANALYSIS\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11].name = \\\"INSURANCE POLICY \\/ CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11].description = \\\"INSURANCE POLICY \\/ CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12].name = \\\"BENEFECIARY DECLARATION\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12].description = \\\"BENEFECIARY DECLARATION\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13].name = \\\"NON RADIOACTIVE CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13].description = \\\"NON RADIOACTIVE CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14].name = \\\"PHYTOSANITARY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14].description = \\\"PHYTOSANITARY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15].name = \\\"CERTIFICATE OF ANALYSIS\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15].description = \\\"Bill of exchange\\/draft\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15].arabicName = \\\"\\u0627\\u0644\\u0643\\u0645\\u0628\\u064a\\u0627\\u0644\\u0629\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16].name = \\\"HEALTH CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16].description = \\\"HEALTH CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17].name = \\\"INSPECTION CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17].description = \\\"INSPECTION CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17].arabicName = \\\"\\\";;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18].name = \\\"WARRANTY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18].description = \\\"WARRANTY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18].arabicName = \\\"\\\";\\r\\n\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19].name = \\\"TEST CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19].description = \\\"TEST CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties = {};\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};\\r\\n\"]}},{\"outgoing\":[\"2027.71bcd28a-81f6-44a4-8301-a22662997a00\"],\"incoming\":[\"2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":327,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.71bcd28a-81f6-44a4-8301-a22662997a00\",\"name\":\"Set ECM default properties\",\"dataInputAssociation\":[{\"targetRef\":\"2055.399c7a58-00b5-4451-9813-41c0b9652088\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.25394215-074f-4b79-8e84-9a96d32cc83b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.attachmentDetails\"]}}],\"sourceRef\":[\"2055.7d269650-ee48-4101-80db-2807cf921562\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.0261e8ad-a540-4682-88c5-87dff3eab23c\"]}],\"calledElement\":\"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8\"},{\"targetRef\":\"2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set ECM default properties\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.0268cbe4-24b1-483d-86e8-f760a263aa20\",\"sourceRef\":\"2025.74cef49e-b3e2-42aa-81f0-238dc5de00ef\"},{\"targetRef\":\"2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Create ECM Folder\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.71bcd28a-81f6-44a4-8301-a22662997a00\",\"sourceRef\":\"2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d\"},{\"targetRef\":\"2025.0329ea67-2d4f-42e1-820b-1a787ba00c89\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e\",\"sourceRef\":\"2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"fullPath\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c421b885-1bba-4e13-830d-64b95f2d6f8f\"},{\"outgoing\":[\"2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb\"],\"incoming\":[\"2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e\",\"2027.7a7ab062-35fd-4923-8ed8-ffa201c87355\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":602,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true],\"preAssignmentScript\":[\"tw.local.accounteePartyCif = \\\"\\\";\\r\\n\\r\\nif(!!tw.local.odcRequest.Parties && !!tw.local.odcRequest.Parties.partyTypes && tw.local.odcRequest.Parties.partyTypes.partyCIF !=null ) \\r\\n\\ttw.local.accounteePartyCif= tw.local.odcRequest.Parties.partyTypes.partyCIF;\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb\",\"name\":\"Get Customer Account Details\",\"dataInputAssociation\":[{\"targetRef\":\"2055.7a0225d0-1ea8-4907-afba-e3a98de88df1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.d471dcbe-97c5-4911-87c7-7008dadc3a15\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.accounteePartyCif\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.0329ea67-2d4f-42e1-820b-1a787ba00c89\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerAccounts\"]}}],\"sourceRef\":[\"2055.5855796c-8062-4709-8c2a-18f470d6d879\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.a32d29e2-af5d-4f6f-8041-8778849c9737\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.328cd87f-3306-4c20-8923-b7515b1cb782\"]}],\"calledElement\":\"1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"serviceName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.cc04b478-5aa3-4f21-8665-712167bbe276\"},{\"incoming\":[\"2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1939,\"y\":272,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.a1bb86e3-acbc-4da9-8262-637e0190103e\"},{\"outgoing\":[\"2027.dda38f6b-4df2-40ca-8929-4c92203af369\",\"2027.7a7ab062-35fd-4923-8ed8-ffa201c87355\"],\"default\":\"2027.dda38f6b-4df2-40ca-8929-4c92203af369\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":-39,\"y\":196,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"is Liquidated?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.12d89ada-06f5-45ab-8d7d-0112f50eacdd\"},{\"targetRef\":\"2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"NO\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.dda38f6b-4df2-40ca-8929-4c92203af369\",\"sourceRef\":\"2025.12d89ada-06f5-45ab-8d7d-0112f50eacdd\"},{\"targetRef\":\"2025.0329ea67-2d4f-42e1-820b-1a787ba00c89\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.isLiquidated\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"YES\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7a7ab062-35fd-4923-8ed8-ffa201c87355\",\"sourceRef\":\"2025.12d89ada-06f5-45ab-8d7d-0112f50eacdd\"},{\"outgoing\":[\"2027.c1ebf2f8-4232-41e8-8cda-d8fe0f78c763\",\"2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902\"],\"incoming\":[\"2027.4f289ccc-**************-abbf4de7f9ba\"],\"default\":\"2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1872,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"log history?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359\"},{\"targetRef\":\"2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To log history?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4f289ccc-**************-abbf4de7f9ba\",\"sourceRef\":\"2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e\"},{\"targetRef\":\"bd7713f0-26f4-411c-a13c-bd4bf8846a29\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c1ebf2f8-4232-41e8-8cda-d8fe0f78c763\",\"sourceRef\":\"2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359\"},{\"targetRef\":\"2025.a1bb86e3-acbc-4da9-8262-637e0190103e\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage!= null) || (tw.local.errorMessage!= \\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902\",\"sourceRef\":\"2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359\"},{\"outgoing\":[\"2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5\"],\"incoming\":[\"2027.dda38f6b-4df2-40ca-8929-4c92203af369\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":46,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5\",\"name\":\"Retrieve Request Number\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}],\"sourceRef\":[\"2055.29fabc80-90b8-4cad-81e3-1c319c6f595a\"]}],\"calledElement\":\"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546\"},{\"targetRef\":\"2025.be89ff1b-cbda-4adf-81c1-5d29594b12e6\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5\",\"sourceRef\":\"2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64\"},{\"startQuantity\":1,\"incoming\":[\"2027.0268cbe4-24b1-483d-86e8-f760a263aa20\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":409,\"y\":331,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"name\":\"Copy of Set ECM default properties\",\"dataInputAssociation\":[{\"targetRef\":\"2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.6f216377-34de-4f5e-8ab5-adc2796733ee\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.customerName\"]}}]},{\"targetRef\":\"2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user.fullName\"]}}]},{\"targetRef\":\"2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.processInstance.id\"]}}]},{\"targetRef\":\"2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.eac829db-66fe-43f5-810c-6faa514533a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties\"]}}],\"sourceRef\":[\"2055.81cce609-b3fe-4f11-809f-c3a599908595\"]}],\"calledElement\":\"1.2d3ab562-82df-48a5-9de7-f5d964218191\"},{\"startQuantity\":1,\"outgoing\":[\"2027.10ea1012-03d1-4f0f-8f9a-46195e38f084\"],\"default\":\"2027.10ea1012-03d1-4f0f-8f9a-46195e38f084\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1476,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\r\\ntw.local.odcRequest.BasicDetails.requestState=tw.epv.RequestState.Collection ;\\r\\n\\r\\n\"]},\"name\":\"Audit Collection Data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e39bfaa6-c863-41f9-8061-0e371dff89cb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.isLiquidated\"]}}]},{\"targetRef\":\"2055.5d4b901c-324e-4bea-8f10-e160a656c696\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.OdcCollection.amount\"]}}]},{\"targetRef\":\"2055.b51575f2-8ce0-48d0-8179-71d12e0440e7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.OdcCollection.currency\"]}}]},{\"targetRef\":\"2055.84daf689-5f95-458f-8b7f-d8c08459d4c1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.20995cf3-6a12-4378-8292-51106389c796\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNature.name\"]}}]},{\"targetRef\":\"2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.name\"]}}]},{\"targetRef\":\"2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestDate\"]}}]},{\"targetRef\":\"2055.85dca7ee-4057-4dcd-878f-b924dff64190\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.328377fd-ccc9-4119-80ca-435deb518aee\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]},{\"targetRef\":\"2055.37b99722-adca-4c0b-8d6d-aa2eeae29994\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}]},{\"targetRef\":\"2055.b0be0c94-0742-4365-875f-1b01b63caf0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]},{\"targetRef\":\"2055.27a871f0-6893-4366-80d9-133f55bffddb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]},{\"targetRef\":\"2055.ebcd1729-7d20-4759-81b3-e98e9f554767\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}]},{\"targetRef\":\"2055.debd9766-ed8e-45c7-8bbb-c471a2567088\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.8ac7db6a-a3f2-40ee-8ed4-950fba71236e\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.8d379594-e94f-4a21-8222-396c4ba9b2e1\"]}],\"calledElement\":\"1.40b72dbc-5d84-4b6d-9621-4e738d6838a1\"},{\"outgoing\":[\"2027.9cb639ba-**************-f73939dd1673\",\"2027.4ce9e417-e403-4d25-8a20-f4050c61dc84\"],\"incoming\":[\"2027.10ea1012-03d1-4f0f-8f9a-46195e38f084\"],\"default\":\"2027.9cb639ba-**************-f73939dd1673\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1574,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"audited successfully?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.8e37f535-a92e-4d3a-88a6-c21f436f9585\"},{\"incoming\":[\"2027.9cb639ba-**************-f73939dd1673\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1693,\"y\":279,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Copy of Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.33b378b0-ff93-4f1e-8d79-8682948ccfcc\"},{\"targetRef\":\"2025.8e37f535-a92e-4d3a-88a6-c21f436f9585\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To audited successfully?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.10ea1012-03d1-4f0f-8f9a-46195e38f084\",\"sourceRef\":\"2025.8ac7db6a-a3f2-40ee-8ed4-950fba71236e\"},{\"targetRef\":\"2025.33b378b0-ff93-4f1e-8d79-8682948ccfcc\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage!= null || tw.local.errorMessage!=\\\"\\\"\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9cb639ba-**************-f73939dd1673\",\"sourceRef\":\"2025.8e37f535-a92e-4d3a-88a6-c21f436f9585\"},{\"targetRef\":\"2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Update History\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4ce9e417-e403-4d25-8a20-f4050c61dc84\",\"sourceRef\":\"2025.8e37f535-a92e-4d3a-88a6-c21f436f9585\"},{\"targetRef\":\"2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set ECM default properties\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b\",\"sourceRef\":\"2025.be89ff1b-cbda-4adf-81c1-5d29594b12e6\"},{\"outgoing\":[\"2027.ed10ecc4-4cbd-4b9a-80bb-36c2df6abaa6\",\"2027.7faa0719-e927-4841-8b1d-b4bb154c666e\"],\"default\":\"2027.7faa0719-e927-4841-8b1d-b4bb154c666e\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":772,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Has ProductCode?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.284f7314-8e0a-4308-88b4-ac5db2f03168\"},{\"outgoing\":[\"2027.2cf47d3e-16cd-4687-81e1-bff7219335ed\"],\"incoming\":[\"2027.ed10ecc4-4cbd-4b9a-80bb-36c2df6abaa6\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"activityAdHocSettings\":[{\"hidden\":false,\"repeatable\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityAdHocSettings\",\"triggerType\":\"Automatic\",\"option\":\"Required\"}],\"nodeVisualInfo\":[{\"width\":95,\"x\":757,\"y\":-68,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true],\"activityPreconditions\":[{\"documentTriggerMode\":\"LegacyCase\",\"sourceFolderReferenceType\":\"FolderId\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityPreconditions\",\"triggerType\":\"NoPreconditions\",\"matchAll\":true}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.2cf47d3e-16cd-4687-81e1-bff7219335ed\",\"name\":\"Get Charges Completed\",\"dataInputAssociation\":[{\"targetRef\":\"2055.12683c54-ad5b-4c68-b734-93d1dea93938\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ChargesAndCommissions\"]}}]},{\"targetRef\":\"2055.7d751a23-0e1b-4987-a9bc-41b25f644f54\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value\"]}}]},{\"targetRef\":\"2055.66b62639-4042-49bf-9544-dbb15f00f3be\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.value\"]}}]},{\"targetRef\":\"2055.86004a6f-9b72-4f74-a69a-aed7c869a281\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"]}}]},{\"targetRef\":\"2055.1001aa15-5c17-4db8-ab30-0263431130b2\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.FinancialDetailsBR.currency.value\"]}}]},{\"targetRef\":\"2055.ad7e0a45-5cb2-437f-b705-2ff310914291\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]},{\"targetRef\":\"2055.25041969-c52c-4425-8c47-741f31a7fa66\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\"}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ChargesAndCommissions\"]}}],\"sourceRef\":[\"2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0\"]}],\"calledElement\":\"1.47164ac0-82bf-4670-b0f1-51d0e36f4463\"},{\"targetRef\":\"2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \\\"\\\"\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ed10ecc4-4cbd-4b9a-80bb-36c2df6abaa6\",\"sourceRef\":\"2025.284f7314-8e0a-4308-88b4-ac5db2f03168\"},{\"targetRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \\\"\\\"\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7faa0719-e927-4841-8b1d-b4bb154c666e\",\"sourceRef\":\"2025.284f7314-8e0a-4308-88b4-ac5db2f03168\"},{\"outgoing\":[\"2027.3de7292b-4475-43dd-838d-9114302ffd0f\"],\"incoming\":[\"2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":603,\"y\":290,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\/\\/tw.local.odcRequest.parentRequestNo = \\\"00104230000170\\\";\\r\\n\\/\\/tw.local.odcRequest.requestType.value = tw.epv.RequestType.Collection\\r\\n\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.3de7292b-4475-43dd-838d-9114302ffd0f\",\"name\":\"Retrieve Product Code\",\"dataInputAssociation\":[{\"targetRef\":\"2055.739ec1bd-4791-48bc-830c-f00e254474a3\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.d27bf540-679c-4767-8059-acaba3a0fe48\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value\"]}}],\"sourceRef\":[\"2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isfound\"]}}],\"sourceRef\":[\"2055.e6db1787-627d-4373-80cc-75c8940bc32d\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.message\"]}}],\"sourceRef\":[\"2055.e1811b7a-e326-4b9c-863e-59e994c41703\"]}],\"calledElement\":\"1.0ed017b2-b582-4d5c-b5b5-3649c89ee192\"},{\"targetRef\":\"2025.d27bf540-679c-4767-8059-acaba3a0fe48\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Retrieve Product Code\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb\",\"sourceRef\":\"2025.0329ea67-2d4f-42e1-820b-1a787ba00c89\"},{\"targetRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3de7292b-4475-43dd-838d-9114302ffd0f\",\"sourceRef\":\"2025.d27bf540-679c-4767-8059-acaba3a0fe48\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isfound\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a5838696-ca01-4729-87b8-cb3baf3ce6df\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"message\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8519f6bb-4cd5-46cd-8252-a8176ff229c2\"},{\"startQuantity\":1,\"outgoing\":[\"2027.65484a87-94c7-49cd-8dd2-04d8479aae9d\"],\"default\":\"2027.65484a87-94c7-49cd-8dd2-04d8479aae9d\",\"extensionElements\":{\"mode\":[\"InvokeService\"],\"postAssignmentScript\":[\"if (tw.local.ValidationMessage == \\\"\\\" && tw.local.ValidationMessage == null) {\\r\\n\\tif (tw.local.errorMessage == \\\"\\\" && tw.local.errorMessage == null) {\\r\\n\\t\\talert(\\\"Valid\\\");\\r\\n\\t}\\r\\n}else{\\r\\n tw.system.coachValidation.addValidationError(\\\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\\\", tw.local.ValidationMessage);\\r\\n}\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":793,\"y\":393,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"name\":\"Get Debited Nostro Vostro Account\",\"dataInputAssociation\":[{\"targetRef\":\"2055.723db670-6afc-4c61-8bff-61069e97a6df\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\"]}}]},{\"targetRef\":\"2055.a4b64bc1-a6e1-4866-82c6-48a1bd9a6693\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractLiquidation.liqCurrency\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.089c7969-235e-42a6-8fd7-0ca88d4fbda3\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.6d466c39-b0d0-4414-8065-f99b230bcf07\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.ValidationMessage\"]}}],\"sourceRef\":[\"2055.63de69c3-31f4-45df-8a71-32f0f2f65fd2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractLiquidation.debitedAccountName\"]}}],\"sourceRef\":[\"2055.3387da0f-2282-4ae0-8159-9dc014499f8f\"]}],\"calledElement\":\"1.672c16cb-04d7-4e67-a904-779d9009e1ce\"},{\"targetRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.65484a87-94c7-49cd-8dd2-04d8479aae9d\",\"sourceRef\":\"2025.089c7969-235e-42a6-8fd7-0ca88d4fbda3\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"ValidationMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8780a059-b42d-49e7-82e4-ad5b87292d8e\"},{\"outgoing\":[\"2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b\"],\"incoming\":[\"2027.2cf47d3e-16cd-4687-81e1-bff7219335ed\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":755,\"y\":-168,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true],\"preAssignmentScript\":[\"tw.local.customerAndPartyCifs = [];\\r\\nvar customerCIF = tw.local.odcRequest.CustomerInfo.cif;\\r\\n\\r\\nvar partyCIF = tw.local.odcRequest.Parties.partyTypes.partyId;\\r\\nvar caseInNeedCIF = tw.local.odcRequest.Parties.caseInNeed.partyId;\\r\\n\\r\\n\\/\\/Build Cifs list with valid values or \\\"\\\" as default\\r\\n!!customerCIF ? tw.local.customerAndPartyCifs[0] = customerCIF : tw.local.customerAndPartyCifs[0] = \\\"\\\";\\r\\n\\r\\n!!partyCIF ? tw.local.customerAndPartyCifs[1] = partyCIF : tw.local.customerAndPartyCifs[1] = \\\"\\\";\\r\\n\\r\\n!!caseInNeedCIF ? tw.local.customerAndPartyCifs[2] = caseInNeedCIF : tw.local.customerAndPartyCifs[2] = \\\"\\\";\\r\\n\\r\\n\\/\\/remove duplicate cifs using SET\\r\\ntw.local.customerAndPartyCifs = [...new Set(tw.local.customerAndPartyCifs)];\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b\",\"name\":\"Get Customer and Party Account List\",\"dataInputAssociation\":[{\"targetRef\":\"2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerAndPartyCifs\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.af31c948-21e2-4363-8234-74199d1a467d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.customerAndPartyAccountList\"]}}],\"sourceRef\":[\"2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5\"]}],\"calledElement\":\"1.b92996ee-f375-4dc1-8c83-cfc4a49384a2\"},{\"targetRef\":\"2025.af31c948-21e2-4363-8234-74199d1a467d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Customer and Party Account List\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2cf47d3e-16cd-4687-81e1-bff7219335ed\",\"sourceRef\":\"2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"customerAndPartyCifs\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.cccca974-00d9-44bf-871d-0cb67d061dd5\"},{\"targetRef\":\"2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"leftCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b\",\"sourceRef\":\"2025.af31c948-21e2-4363-8234-74199d1a467d\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"rate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.addd2895-783a-42a8-8aa8-33fa2f2d0094\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"calculatedChangeAmnt\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.17a10bb1-20a6-40a0-8860-031eb84e9441\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"selectedIndex\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.78460a99-4397-47a7-8516-1efd15e65e26\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isChecker\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.5cc31eb3-2313-4e5f-8a33-842b4ac32a07\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"exchangeRate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.047ac412-5327-4029-8d29-2b9491db48bf\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractLiquidatedMSG\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.aa9d3a1f-4418-4d20-837f-11db247828c6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"verifyGLMsg\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.635cda99-e14d-4441-80d9-2a05f7adcf2c\"},{\"startQuantity\":1,\"incoming\":[\"2027.d2576e89-c82c-4b73-8410-61fc6c0cc480\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":397,\"y\":522,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Client-Side Script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.930604c9-ac9a-474f-88f3-998e271c68e6\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.error.data\"]}},{\"parallelMultiple\":false,\"outgoing\":[\"2027.d2576e89-c82c-4b73-8410-61fc6c0cc480\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2025.35937466-48ed-45bc-81d2-07b979456720\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\"}],\"attachedToRef\":\"2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01\",\"extensionElements\":{\"default\":[\"2027.d2576e89-c82c-4b73-8410-61fc6c0cc480\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":444,\"y\":389,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"2025.788f47f5-d371-4fb4-8593-3c1ca350efab\",\"outputSet\":{}},{\"targetRef\":\"2025.930604c9-ac9a-474f-88f3-998e271c68e6\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Client-Side Script\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d2576e89-c82c-4b73-8410-61fc6c0cc480\",\"sourceRef\":\"2025.788f47f5-d371-4fb4-8593-3c1ca350efab\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"c9a3bd34-d69c-4e98-a09d-d8b078dcd047\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"984a4522-dd97-49c3-973b-7e372dcb870d\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Col01 - Create ODC Collection Request\",\"declaredType\":\"globalUserTask\",\"id\":\"1.6255f65f-d7ce-452d-9058-3f856ea792e0\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.115324d0-8168-495d-8187-b6a3c2434105\"},{\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"customerAccounts\",\"isCollection\":true,\"id\":\"2055.f0a5931f-96f0-4329-81d0-83ef90e6192f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.a759db86-0dc8-41dc-8faf-40ea72941541\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd\",\"epvProcessLinkId\":\"87b4df33-7920-4ce7-86b1-893067e50ef0\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.e22cd1cb-4788-4edd-beb4-825e8f27f335\",\"epvProcessLinkId\":\"00fa02af-a828-48c4-89b1-5582241f99cb\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.93c5c002-7ac4-4283-83ee-63b8662f9223\",\"epvProcessLinkId\":\"74ed432e-5125-4d4d-8504-d1a1245fb100\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"04161144-e1f9-43bb-8e07-bf171bc54528\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7\",\"epvProcessLinkId\":\"e474676d-2c7a-4a4d-84d2-415932aae2f4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"a86f9fd0-6f96-403d-8b05-bc3945f285e2\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.062854b5-6513-4da8-84ab-0126f90e550d\",\"epvProcessLinkId\":\"e3108d03-1589-46fe-8951-79bfb122d51b\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"_dcce9938-6b07-42b3-8c25-4d1129b311da\"}],\"outputSet\":[{\"id\":\"_3d1abe30-59bc-49ab-adbc-1dda116bcaa2\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = {};\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = [];\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = [];\\nautoObject.FcCollections.listOfAccounts[0] = {};\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = [];\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = {};\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.Drawer = {};\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = {};\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = {};\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = {};\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = {};\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.Parties.caseInNeed = {};\\nautoObject.Parties.caseInNeed.partyCIF = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyId = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyName = \\\"\\\";\\nautoObject.Parties.caseInNeed.country = \\\"\\\";\\nautoObject.Parties.caseInNeed.language = \\\"\\\";\\nautoObject.Parties.caseInNeed.refrence = \\\"\\\";\\nautoObject.Parties.caseInNeed.address1 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address2 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address3 = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType = {};\\nautoObject.Parties.caseInNeed.partyType.name = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].isGLFound = false;\\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \\\"\\\";\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.debitedAccountName = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject.folderPath = \\\"\\\";\\nautoObject.templateDocID = \\\"\\\";\\nautoObject.requestID = 0;\\nautoObject.customerAndPartyAccountList = [];\\nautoObject.customerAndPartyAccountList[0] = {};\\nautoObject.customerAndPartyAccountList[0].accountNO = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].currencyCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].branchCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\\nautoObject.customerAndPartyAccountList[0].typeCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerName = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerNo = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].frozen = false;\\nautoObject.customerAndPartyAccountList[0].dormant = false;\\nautoObject.customerAndPartyAccountList[0].noDebit = false;\\nautoObject.customerAndPartyAccountList[0].noCredit = false;\\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountClassCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balanceType = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountStatus = \\\"\\\";\\nautoObject.tradeFoComment = \\\"\\\";\\nautoObject.exeHubMkrComment = \\\"\\\";\\nautoObject.compcheckerComment = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.hubCode = \\\"\\\";\\nautoObject.branchCode = \\\"\\\";\\nautoObject.initiatorUser = \\\"\\\";\\nautoObject.branchName = \\\"\\\";\\nautoObject.hubName = \\\"\\\";\\nautoObject.branchSeq = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"name\":\"routingDetails\",\"isCollection\":false,\"id\":\"2055.fb57a244-f263-444b-8dc2-734946370a33\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"45ec65ff-5b5a-4c61-afe7-2d71600a4b81\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "47c6c592-7845-4bd6-bee4-b2ae9741aada", "versionId": "8fa6c44d-de4f-438a-85a0-77588bb25905"}, {"name": "routingDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.fb57a244-f263-444b-8dc2-734946370a33", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "parameterType": "1", "isArrayOf": "false", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9a00efe2-6f76-449c-91c2-035728bfb5a9", "versionId": "d2a01859-a9b3-4745-950e-44d544d006f7"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f9cd3e10-087d-485c-9c57-5dd2de885ba9", "versionId": "d8fc073e-f523-4a01-87ca-5784556e6699"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.115324d0-8168-495d-8187-b6a3c2434105", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f94b0129-a055-4dd0-9f0c-e895ef9f747d", "versionId": "841777a3-7da9-4591-9db8-fb4ea815782b"}, {"name": "customerAccounts", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.f0a5931f-96f0-4329-81d0-83ef90e6192f", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "parameterType": "2", "isArrayOf": "true", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "a4a3182b-577e-43a4-9a25-880d946d4146", "versionId": "9b62f9de-7173-4ad7-bb3a-34e2f7aa249d"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "dcd4d733-30a8-40c8-ae90-06aa6da854a3", "versionId": "922480d8-f617-46e0-a28d-4ff410cb27e8"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.653db24d-c64d-412d-8c14-ec9e5c3f46ec", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3166c162-cd4b-4cbf-8245-cfa878d64342", "versionId": "6196860b-372c-4cfb-afde-a54ac40d8ec0"}, {"name": "errorMessageVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1d5065f2-8f3a-4d7c-8ee5-70d9ae7f65bf", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4da1c9bd-3059-426b-9560-9ea6110d7eee", "versionId": "a25dcd61-441d-42e7-b72c-7225624a75aa"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f8c94287-6a30-4333-8c4c-13fd7742fee2", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f4e0322b-**************-8253342ef4f6", "versionId": "098d325a-82fa-445c-a374-a65e7d336b05"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.eb65bb62-bdde-49f0-8e18-************", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "6147c011-5ae1-439b-944d-da081b239f63", "versionId": "37e78313-0965-46b5-a6b3-02f4478e8f56"}, {"name": "ldapUserProfile", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d99d5cbf-ba11-4e37-850b-200d74087f56", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9e985ef5-958d-4da3-ab3a-aa18292fc74c", "versionId": "28c15010-f573-490c-8374-3ae5c3174bd7"}, {"name": "parentRequestNoVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7362e801-01de-4688-890a-7d97f6662a9d", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c378a142-671b-4873-ab02-a64835f1ffc3", "versionId": "5176cd10-bf83-4532-a8b0-077cb6ed026b"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7066f302-9d63-41d1-8111-fa98a5382b32", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "12cab7d1-fa91-45f8-9091-2ca9bac9023f", "versionId": "f6fe65c4-0db6-4adf-ba23-269bdfc22019"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c9d26fb0-c6c4-4835-8283-e9fb2c177c30", "description": "to be deleted ", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "6ef84eb8-93f9-4e90-87f6-beb179ba41dd", "versionId": "e5ec5795-37ac-4a85-9705-b2b49d87342d"}, {"name": "requestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.956aea2f-b087-4efd-8c4f-e533a6d31b54", "description": "to be deleted ", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "90a559dc-6c85-49d6-aa63-f56e15b32489", "versionId": "ae72d8c7-bc9c-4ea8-a1e1-c982f0148be6"}, {"name": "glAccountVerified", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b60ac548-1c32-4f4e-8016-73b44d024442", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b8886ed4-3e14-4552-8ffc-c4778c283511", "versionId": "b5a7bef4-3468-475b-b1c6-4f71f0da7d24"}, {"name": "accounteePartyCif", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3a374913-eb91-4e0f-85d9-86c86cc66465", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "85a0f0f7-3398-446c-80fb-46c37a60e4a8", "versionId": "7e68bd0e-200c-4824-95c7-8b38f9049848"}, {"name": "code", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6a1c20ce-b779-4524-835b-d45c71c9ac6b", "description": "to be deleted<div><br /></div><div>001</div>", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "12", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ec70dbf8-8afc-446f-bd97-5e349a007750", "versionId": "99f81526-01b8-4223-9dc8-a550a9d5f612"}, {"name": "fullPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c421b885-1bba-4e13-830d-64b95f2d6f8f", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "13", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "71df60ef-6e61-48a9-8fc9-b0d48439d1e6", "versionId": "c2d2b05d-e921-4086-b825-f2ca41c84388"}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.cc04b478-5aa3-4f21-8665-712167bbe276", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "14", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7d4e7027-ede2-4ce7-89d0-d228deaa83db", "versionId": "7c8fd381-54c7-404a-97be-945360e814b9"}, {"name": "isfound", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a5838696-ca01-4729-87b8-cb3baf3ce6df", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "15", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7ff9311c-5fe4-443e-8573-55d13d41166d", "versionId": "423ed9af-6199-4043-96fc-c5302f308ac6"}, {"name": "message", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8519f6bb-4cd5-46cd-8252-a8176ff229c2", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "16", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1820f0f3-c904-4356-94a9-dda46549ae49", "versionId": "1d4abc4d-7918-46dc-a209-ae71accefc83"}, {"name": "ValidationMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8780a059-b42d-49e7-82e4-ad5b87292d8e", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "17", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "8fa9bc54-2e3c-4837-8dba-8dd7f6768e94", "versionId": "535eddc1-6a70-4135-a3ea-fb017f9597ff"}, {"name": "customerAndPartyCifs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.cccca974-00d9-44bf-871d-0cb67d061dd5", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "18", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "2698106a-ffec-46b6-a6a6-58d18f02b2f1", "versionId": "40567ef1-3db9-4c23-b3ae-2dffcc6cb54c"}, {"name": "rate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.addd2895-783a-42a8-8aa8-33fa2f2d0094", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "19", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c945f091-e657-4e58-94c7-c97b382def4e", "versionId": "15f4773d-f928-4d64-875a-6c1cf615ba29"}, {"name": "calculatedChangeAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.17a10bb1-20a6-40a0-8860-031eb84e9441", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "20", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c01e4aca-3cce-4c8a-8f16-c514b11dbffe", "versionId": "9f4a74ff-b96e-4623-9505-3666c983fefc"}, {"name": "selectedIndex", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.78460a99-4397-47a7-8516-1efd15e65e26", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "21", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a9b47204-a822-4e49-8b4d-6f7a31f55297", "versionId": "396f6c80-6c7b-4865-a2bd-36479c1c8c02"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5cc31eb3-2313-4e5f-8a33-842b4ac32a07", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "22", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "88cf5f3e-3611-4567-900b-************", "versionId": "09b2c373-d1d2-4e8d-b1d7-8ad8a69ac717"}, {"name": "exchangeRate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.047ac412-5327-4029-8d29-2b9491db48bf", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "23", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a0788d6b-97d7-48bd-a0bd-db24e3df48ff", "versionId": "0e06f783-dba1-4bda-bf12-1c492bec07bf"}, {"name": "contractLiquidatedMSG", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.aa9d3a1f-4418-4d20-837f-11db247828c6", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "24", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "6a703484-5738-4a0b-a5e3-8ee7842bca56", "versionId": "ce0eb977-939f-4c43-ba35-a23dc2c2b508"}, {"name": "verifyGLMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.635cda99-e14d-4441-80d9-2a05f7adcf2c", "description": {"isNull": "true"}, "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "namespace": "2", "seq": "25", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "78a79b03-27b8-4002-b348-6a022558b1c4", "versionId": "00f77201-bfe6-42c2-a17c-2b611da16376"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0d904442-1dd4-4df6-8233-4024a60b0373", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Set Status And Sub Status", "tWComponentName": "SubProcess", "tWComponentId": "3012.2f9f29fe-a9a9-43a8-8019-afe09d9cdd1b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:25fd907f15501a6a:510e674:189fee9ec23:-53b", "versionId": "1c62a370-d376-4a68-abcc-db8f2a8316aa", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.2f9f29fe-a9a9-43a8-8019-afe09d9cdd1b", "attachedProcessRef": "/1.81656d33-5348-479b-a7af-5631356d9476", "guid": "ad4eab1d-c0f1-4264-adb3-98ca0e3c6f15", "versionId": "cf5128ab-f3e3-4bf2-88d7-6fb7ffbd6aed"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Create ECM Folder", "tWComponentName": "SubProcess", "tWComponentId": "3012.e931889c-9e36-4207-bda7-e2d80a3ca344", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:2149", "versionId": "1f5edb4a-66df-4642-bbbc-65e7311d9bf6", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.e931889c-9e36-4207-bda7-e2d80a3ca344", "attachedProcessRef": "/1.46b984a3-b4ad-405a-abd3-8631f907efe4", "guid": "398fc66c-46c6-4057-a4ee-9975155167ce", "versionId": "f79ad2d0-08aa-46a2-8eb6-25a245a9f401"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0329ea67-2d4f-42e1-820b-1a787ba00c89", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Get Customer Account Details", "tWComponentName": "SubProcess", "tWComponentId": "3012.44df3341-df49-40ca-a5c7-6bdf91ef9ef5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac6fe7417:3b19", "versionId": "3b43c72a-6ab6-49fd-9bfc-61d99413bbd8", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.44df3341-df49-40ca-a5c7-6bdf91ef9ef5", "attachedProcessRef": "/1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285", "guid": "a817bda6-aafe-4b54-981a-bde7671c9359", "versionId": "f30cf7da-7198-4450-9efc-d9bff1ee7813"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Copy of Set ECM default properties", "tWComponentName": "SubProcess", "tWComponentId": "3012.63e22751-d5c6-437a-86a4-119b00304df0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a6be0630e884ccca:-427a0b52:18bb428309d:-3a03", "versionId": "5cf10fe3-6a7b-4df4-8ed3-8f1509fc7983", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.63e22751-d5c6-437a-86a4-119b00304df0", "attachedProcessRef": "/1.2d3ab562-82df-48a5-9de7-f5d964218191", "guid": "4c30537d-f8d2-4874-b3b9-6e1ce633acfb", "versionId": "91c2b2bd-6f33-418e-8f97-96c31b306786"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Get Charges Completed", "tWComponentName": "SubProcess", "tWComponentId": "3012.78c76a4a-815b-48db-b9d8-8673055a6681", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:528e", "versionId": "617031be-15a6-4dd3-b5ae-958bf2e08a8b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.78c76a4a-815b-48db-b9d8-8673055a6681", "attachedProcessRef": "/1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "guid": "ba958880-1dc0-4272-a65a-b2aa27fb118d", "versionId": "b77977c8-4923-4013-9af6-557e992bb8c8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8ac7db6a-a3f2-40ee-8ed4-950fba71236e", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Audit Collection Data", "tWComponentName": "SubProcess", "tWComponentId": "3012.19cd4b27-dbc9-4477-a3de-2c4a56b5de50", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a6be0630e884ccca:-427a0b52:18bb428309d:-2361", "versionId": "6279f098-6bf4-4af2-a679-96c8f690e3da", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.19cd4b27-dbc9-4477-a3de-2c4a56b5de50", "attachedProcessRef": "/1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "guid": "fb3fd7b1-3d95-4349-92ba-b8266242b514", "versionId": "0e77ecef-c70a-41f2-8916-6fa4099bf5fe"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Set ECM default properties", "tWComponentName": "SubProcess", "tWComponentId": "3012.39621f53-9a02-4457-af4c-1710a1357655", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:214a", "versionId": "71a2b34e-a47d-4068-b7f5-35dc57d7d2d0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.39621f53-9a02-4457-af4c-1710a1357655", "attachedProcessRef": "/1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "guid": "3b883e41-1ca4-4e78-913a-e53ad680abbc", "versionId": "bdc4134f-3fc3-4cc2-8762-dcdd4849432a"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0e2ba1eb-4c5b-4ef0-92fa-ae15d9eab69d", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.09f49fa6-00cc-4626-a0a2-597312b8c635", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:7dd9", "versionId": "77120265-fed8-4558-b475-ee2dab6053a9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.089c7969-235e-42a6-8fd7-0ca88d4fbda3", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Get Debited Nostro Vostro Account", "tWComponentName": "SubProcess", "tWComponentId": "3012.78853a95-58cf-4e4f-90dd-0732bea23e2e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5505", "versionId": "8225f1f2-4ed8-4b1f-8a82-5bfadc7f3992", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.78853a95-58cf-4e4f-90dd-0732bea23e2e", "attachedProcessRef": "/1.672c16cb-04d7-4e67-a904-779d9009e1ce", "guid": "67b83970-0237-41d0-9a9f-f26be4f7c130", "versionId": "4d07c56a-a2cd-4eda-9fdc-b361dc74e466"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Retrieve Request Number", "tWComponentName": "SubProcess", "tWComponentId": "3012.5833bc81-0c26-4e56-9d89-5b019a2b6c77", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-a95", "versionId": "88ab1599-3d0c-45eb-9781-52a7d1f49847", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5833bc81-0c26-4e56-9d89-5b019a2b6c77", "attachedProcessRef": "/1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "guid": "d7e2ebb7-b4f9-4d21-9607-b778afd0941a", "versionId": "26f1d629-f28d-49eb-beac-1483c3900acf"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Update History", "tWComponentName": "SubProcess", "tWComponentId": "3012.85bfc29b-3f3a-4574-beb6-c53c20fbf127", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a50e4858f:979", "versionId": "9f07ca12-ab43-4050-87a7-6ee838a092fb", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.85bfc29b-3f3a-4574-beb6-c53c20fbf127", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "cbe784fc-9dd3-4405-8ba4-144df3aa951c", "versionId": "9b380e6c-e860-4d16-8b06-dd79a8db6bee"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d27bf540-679c-4767-8059-acaba3a0fe48", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Retrieve Product Code", "tWComponentName": "SubProcess", "tWComponentId": "3012.faca4ddd-d951-4cd4-8e9d-d96dda60b0b8", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6bdd", "versionId": "d1decf08-2a83-404f-95dc-5af9872d5863", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.faca4ddd-d951-4cd4-8e9d-d96dda60b0b8", "attachedProcessRef": "/1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "guid": "5badd34e-c07a-4876-807c-4a7d3cc5ae0f", "versionId": "c789dc5d-91e7-4e9c-9e18-11960b8d8fe7"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7a68f35f-ddb4-4f2c-84c5-51d75156a4c6", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.2b407b87-b9f7-4e20-af73-82b7cb3b019d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:7dd8", "versionId": "db2921f6-73f5-4b5e-8eab-e4dc71933cd7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.2b407b87-b9f7-4e20-af73-82b7cb3b019d", "haltProcess": "false", "guid": "77edb29a-51af-4123-a7c2-4fb7ae7949de", "versionId": "ab3046b7-6cc9-49a1-8a4c-feb669974bb9"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.af31c948-21e2-4363-8234-74199d1a467d", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "name": "Get Customer and Party Account List", "tWComponentName": "SubProcess", "tWComponentId": "3012.a5adcbee-a4d7-434e-a670-0ef71b4f9045", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-3e8e", "versionId": "f500f307-c9b3-456a-b735-95645b031a92", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.a5adcbee-a4d7-434e-a670-0ef71b4f9045", "attachedProcessRef": "/1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "guid": "384e3765-629a-4401-ad29-ac2b673922ef", "versionId": "8404d07f-4f54-4745-9fde-0a1bbbb066f6"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.0aa2326d-6c72-4cf9-907b-9f9cf9f105bb", "epvId": "/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "ebf1d6d5-788e-4cb2-9ea5-6d05daaf6bcd", "versionId": "1f32d557-5f97-40d4-9daf-64680af602b2"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.a93cf020-b28b-44dc-880d-deb143f6c665", "epvId": "/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "ddf46236-79ba-476b-805f-1e6545023509", "versionId": "4ccef406-872b-42ed-8789-7a3b6814c715"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.2ed2e058-9608-4e66-b765-fa37e7ca20a5", "epvId": "/21.062854b5-6513-4da8-84ab-0126f90e550d", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "f477c1a2-a2e9-4b66-81aa-0c94e91bc9a8", "versionId": "4e70a5a4-6b7a-4a1c-90f0-7656ab65f16d"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.f4718d87-2f16-45da-af7d-68f541b36c9e", "epvId": "/21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "b7e73af8-d2f2-4d8a-825c-efbf7aa89f92", "versionId": "501ec60f-cc9d-4795-8b8b-970e79c592b9"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.b471e5a4-dc96-4c46-abb4-e19f8b02ff5d", "epvId": "/21.93c5c002-7ac4-4283-83ee-63b8662f9223", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "19afc93d-5079-4256-a58f-7a7857ce42fd", "versionId": "82323f22-d37b-4bbd-8f81-b713edd718a2"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.82643908-5ea3-4656-bbe8-3b5957b8bf10", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "191f96f4-e2a0-4637-8abe-48a1f90b6c2b", "versionId": "aaa8eb3e-554d-48cf-8dbd-8e1c53c57de3"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.e84e1ce5-c0ca-4bb7-aa5b-1051dc1b1d08", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "a8a77e2d-b86a-45e3-9ccd-f8e3f9efda02", "versionId": "d98272d5-19d4-4d27-b24c-71900a905db8"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.14619c78-ccb8-4fce-ba20-c8d018c8bf10", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "guid": "ef5eaf60-6735-4981-a4fb-df8f91fcdb5d", "versionId": "f4086ae9-929d-4ffa-bd02-14abe6af1d85"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "45ec65ff-5b5a-4c61-afe7-2d71600a4b81", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "Col01 - Create ODC Collection Request", "id": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "984a4522-dd97-49c3-973b-7e372dcb870d", "ns16:startEvent": {"name": "Start", "id": "7800076a-9c61-4d46-8fe5-61eda608de2f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "-71", "y": "199", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.5a0bf287-5dbb-4cf8-84c3-b0f6f375c4f6"}, "ns3:formTask": {"name": "Coach", "id": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "ns16:extensionElements": {"ns3:validationStayOnPagePaths": "okbutton", "ns13:nodeVisualInfo": {"x": "760", "y": "177", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": "\r\r\nif(tw.local.odcRequest.ContractLiquidation.liqCurrency== null || tw.local.odcRequest.ContractLiquidation.liqCurrency == \"\") \r\r\n\ttw.local.odcRequest.ContractLiquidation.liqCurrency= \"USD\";\r\r\n\t\r\r\n\t"}, "ns16:incoming": ["2027.636dd5a7-5fa4-479c-8391-90dea46b8d50", "2027.7faa0719-e927-4841-8b1d-b4bb154c666e", "2027.3de7292b-4475-43dd-838d-9114302ffd0f", "2027.65484a87-94c7-49cd-8dd2-04d8479aae9d", "2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b", "2027.5a0bf287-5dbb-4cf8-84c3-b0f6f375c4f6"], "ns16:outgoing": ["2027.8ebe87c1-8533-4f25-83cb-24907c146d75", "2027.4c92b8b9-010a-45a8-8d5b-0ba165ad5df3"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "77b286bd-3738-49e3-8c24-11f97411a1fe", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "97e1a2bf-7518-4b2d-86fd-49a64596da75", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "3de82d5d-d1bb-4b53-8d59-8ac22baef4ee", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "06c8e540-e5dc-4f91-896d-1a9407110497", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "df201b11-8810-4f02-8d38-c75474141602", "ns19:optionName": "terminateReasonVIS", "ns19:value": "NONE"}, {"ns19:id": "a269fc56-5d1e-4316-85de-067553ef3196", "ns19:optionName": "errorPanelVIS", "ns19:value": "tw.local.errorMessageVis", "ns19:valueType": "dynamic"}, {"ns19:id": "0f789f32-ff1a-4347-890d-42b22c7892f4", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "5fa863b5-7bf4-4210-8d18-347b03f94cde", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "c3994762-d884-480e-8c1c-4d5bca764c34", "ns19:optionName": "complianceApprovalVis", "ns19:value": "NONE"}, {"ns19:id": "3ced2ef0-d5a2-4ec9-803b-82b20b2e66dd", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "38d6d627-94c0-4831-8c42-2b008b046c67", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "2d4f7514-3c2e-4e83-8e23-539e50a54965", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "3398b5f5-fd3a-4f3d-84f8-5b94be63ac8f", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "1606c373-1cfb-453e-8652-425992d67950", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "None"}, {"ns19:id": "7140178b-5f69-4b79-80b6-2c5d72698c5b", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "None"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "38aa82d9-e867-4713-8baa-0c3620981e06", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "9181030c-bbfb-4bb2-80d3-b075fb0396a0", "ns19:layoutItemId": "Tab_Section1", "ns19:configData": [{"ns19:id": "ea1eb403-5a6a-4696-86fe-0f8d21c9441a", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "a833f7fe-412d-4991-814a-beb023555da6", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "1556a30e-990f-415e-8d6c-6f02596a5d9a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "5f7d81ad-8ce2-4bb5-8d2a-df9ddf7a469d", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "3a917dbe-ea3c-41a8-8c4e-feb95a130e78", "ns19:layoutItemId": "Contract_Liquidation1", "ns19:configData": [{"ns19:id": "bc906cd5-1b43-49fb-899d-cabca5acfa8e", "ns19:optionName": "@label", "ns19:value": "Contract Liquidation"}, {"ns19:id": "f43e7550-20ab-40e2-8b6f-555fbc7a7fcf", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "97b8c6a1-4a4c-46c3-8636-71178065fd38", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "f07b0a4c-42ad-4665-8211-9f61d8014f31", "ns19:optionName": "cif", "ns19:value": "tw.local.odcRequest.Parties.partyTypes.partyCIF", "ns19:valueType": "dynamic"}, {"ns19:id": "6738b37a-96d9-4e53-8486-7c8a0308ee42", "ns19:optionName": "isGlAccount", "ns19:value": "tw.local.isGlAccount", "ns19:valueType": "dynamic"}, {"ns19:id": "5b0023d9-26ad-40fd-84fe-0092dec7a799", "ns19:optionName": "customerAccounts", "ns19:value": "tw.local.odcRequest.customerAndPartyAccountList[]", "ns19:valueType": "dynamic"}, {"ns19:id": "44e61d5d-d04f-4494-8276-a96d7c26131c", "ns19:optionName": "glAccountVerified", "ns19:value": "tw.local.glAccountVerified", "ns19:valueType": "dynamic"}, {"ns19:id": "8e3ffba0-7e90-4319-89d2-2cc47766b791", "ns19:optionName": "<PERSON><PERSON><PERSON><PERSON>", "ns19:value": "tw.local.isChecker", "ns19:valueType": "dynamic"}, {"ns19:id": "88ba196a-2e0a-429f-8ccd-2f744c362efd", "ns19:optionName": "contractLiquidatedMSG", "ns19:value": "tw.local.contractLiquidatedMSG", "ns19:valueType": "dynamic"}, {"ns19:id": "62fe5b1e-ffa1-4b73-8338-56dcb07f3d79", "ns19:optionName": "verifyGLMsg", "ns19:value": "tw.local.verifyGLMsg", "ns19:valueType": "dynamic"}, {"ns19:id": "6bac171a-70ee-4f51-866e-279afe83e8b0", "ns19:optionName": "exchangeRate", "ns19:value": "tw.local.exchangeRate", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "ns19:binding": "tw.local.odcRequest"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "8a678e26-**************-83943d6c7afa", "ns19:layoutItemId": "Charges_And_Commissions_CV_21", "ns19:configData": [{"ns19:id": "79f54cfb-3f33-4ef7-858f-a38674262f9d", "ns19:optionName": "@label", "ns19:value": "Charges And Commissions"}, {"ns19:id": "911958c1-a887-449b-8755-389d4e7d2123", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "e4eea5bb-2cfa-4d0c-8718-12fe36d5295a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "591022a9-e70d-42e0-81cb-acbfc61aa581", "ns19:optionName": "processType", "ns19:value": "LIQD"}, {"ns19:id": "4dacacd4-f2d2-40f2-8d6f-bc8448c0ca8e", "ns19:optionName": "amountCollectableByNBE", "ns19:value": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "6fad35b0-6227-4c7c-8f07-630557071f74", "ns19:optionName": "exRate", "ns19:value": "tw.local.rate", "ns19:valueType": "dynamic"}, {"ns19:id": "2a3fa55d-c346-4a32-8abd-3c27dd5c51c9", "ns19:optionName": "chargesCustomerAccountList", "ns19:value": "tw.local.odcRequest.customerAndPartyAccountList[]", "ns19:valueType": "dynamic"}, {"ns19:id": "8213ce95-714a-4614-8205-b04208bb4157", "ns19:optionName": "calculatedChangeAmnt", "ns19:value": "tw.local.calculatedChangeAmnt", "ns19:valueType": "dynamic"}, {"ns19:id": "c0e9dd94-85a4-426d-88f6-96dbb4bbe633", "ns19:optionName": "index", "ns19:value": "tw.local.selectedIndex", "ns19:valueType": "dynamic"}, {"ns19:id": "f79267d2-d80e-47d0-80af-569e27abcb3e", "ns19:optionName": "<PERSON><PERSON><PERSON><PERSON>", "ns19:value": "tw.local.isChecker", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "ns19:binding": "tw.local.odcRequest.ChargesAndCommissions[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "532735ae-dee4-4bf2-86e4-a116bfe2eecf", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "9b21a244-7684-40a4-87df-e49428d013f3", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "7acd67eb-9a60-4b90-88cb-543066e77808", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "0293df39-1587-46e3-83d2-4a0918846c3d", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "2907c973-dfb0-4c89-8c91-cfb4abf56cb1", "ns19:optionName": "basicDetailsVIS", "ns19:value": "READONLY"}, {"ns19:id": "f1fd81f3-ec3d-4c01-80ce-3d1a31c5409a", "ns19:optionName": "requestVIS", "ns19:value": "READONLY"}, {"ns19:id": "9b13236c-9c9a-46f3-8066-7b2f8bb9c70e", "ns19:optionName": "requestTypeVIS", "ns19:value": "READONLY"}, {"ns19:id": "fcf313ce-e0ff-452f-87bf-1df7e6fc97c3", "ns19:optionName": "parentRequestNoVis", "ns19:value": "NONE", "ns19:valueType": "static"}, {"ns19:id": "806c40a1-9a12-4b34-8078-7b34d3ab2993", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "READONLY"}, {"ns19:id": "cc14c2c1-a593-4f82-8b94-7d24629d0279", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "NONE"}, {"ns19:id": "cc5fb8aa-9f2a-4e63-859f-210dba97a3e0", "ns19:optionName": "contractStageVIS", "ns19:value": "NONE", "ns19:valueType": "static"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "8f5f2747-73fd-4c2b-85b0-2f72bc4b2914", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "36b1599e-83de-4212-89c2-db792affb341", "ns19:optionName": "@label", "ns19:value": "Customer Info"}, {"ns19:id": "f0b9d1a7-96ab-4c99-868c-2861681fbb50", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "05b59a76-a5a1-4190-8109-0cc2442c3241", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "d5bd2cf7-cf68-4e98-8bd6-c9a2e72d69c7", "ns19:optionName": "customerInfoVIS", "ns19:value": "READONLY"}, {"ns19:id": "2f279f23-07e4-46f0-8f52-cf2f9dc1e04a", "ns19:optionName": "requestTypeVis", "ns19:value": "READONLY"}, {"ns19:id": "45f8ea33-f85b-47dd-8047-babb2c82c869", "ns19:optionName": "listsVIS", "ns19:value": "READONLY"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "98c22cf5-d006-4ce1-8578-38fb9d4a10f0", "ns19:layoutItemId": "Financial_Details_Branch1", "ns19:configData": [{"ns19:id": "62a2d995-e92f-4c2d-85c8-6ddc773f9bf7", "ns19:optionName": "@label", "ns19:value": "Financial Details - Branch"}, {"ns19:id": "50285e85-d620-4be2-88e6-3a7d6178e41f", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "05584db9-79dc-423b-875e-e097ca606909", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "aedd8df5-a82e-4e3d-8534-7207a23afe1e", "ns19:optionName": "financialDetailsVis", "ns19:value": "READONLY"}, {"ns19:id": "42614bca-fec8-463d-886d-69c9a2a21408", "ns19:optionName": "fcCollectionVis", "ns19:value": "READONLY"}, {"ns19:id": "d2a5acc4-d48e-4922-8ce3-5ef577d5bfbf", "ns19:optionName": "documentAmountVis", "ns19:value": "READONLY"}, {"ns19:id": "236b2450-aa3a-47fd-8e76-1eeea7fa5de7", "ns19:optionName": "currencyVis", "ns19:value": "READONLY"}, {"ns19:id": "2c7d4a6b-3469-49b4-8694-9bbd44d5bf6c", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "d3b50ee4-6fe5-4d99-8cea-cc90a0ffa85f", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "9f4746bb-5f14-4387-8e13-15fcb74c27ac", "ns19:layoutItemId": "Attachment1", "ns19:configData": [{"ns19:id": "078ff71d-c1db-42e2-8b6f-503a29653467", "ns19:optionName": "@label", "ns19:value": "Attachments"}, {"ns19:id": "cb030272-bcda-46bd-8dc0-747419cf6bd1", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "ca345b3c-21b8-45e4-8ec5-404a5efe4563", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "65b7bfc3-08e8-44ef-856f-d370965a4497", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "bf275b39-f339-4c2e-82fa-565dcd7598b9", "ns19:optionName": "canCreate", "ns19:value": "true"}, {"ns19:id": "e8bc6189-9682-4906-83b6-71dba109fd48", "ns19:optionName": "canDelete", "ns19:value": "true"}, {"ns19:id": "2af97230-d7dd-46b8-8ebb-dac7832f2594", "ns19:optionName": "canUpdate", "ns19:value": "true"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "820a42fa-e501-408e-8ec1-0f13dc2649af", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "d2bdd5c0-f21f-4df9-8d4f-703b07f60391", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "4e56f2e9-2267-40d7-8c47-6ac04eafcac1", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "0b11b893-737b-4a6d-8120-413b3b4ade9a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:endEvent": {"name": "End", "id": "bd7713f0-26f4-411c-a13c-bd4bf8846a29", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1975", "y": "199", "width": "24", "height": "44", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.c1ebf2f8-4232-41e8-8cda-d8fe0f78c763", "2027.bf2ae001-93f0-48cc-8308-299f31ba3e82"]}, "ns16:sequenceFlow": [{"sourceRef": "7800076a-9c61-4d46-8fe5-61eda608de2f", "targetRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "name": "To is Liquidated?", "id": "2027.5a0bf287-5dbb-4cf8-84c3-b0f6f375c4f6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "targetRef": "2025.2cd1e05e-e819-41b6-8d95-dfb27dd0b710", "name": "To End", "id": "2027.8ebe87c1-8533-4f25-83cb-24907c146d75", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "42dd92fa-be2a-4d01-8ae0-3aec571876c7", "ns3:coachEventPath": "DC_Templete1/submit"}}}, {"sourceRef": "2025.2cd1e05e-e819-41b6-8d95-dfb27dd0b710", "targetRef": "2025.0498ad64-a11a-4297-8edc-edb674ae3556", "name": "To Valid?", "id": "2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}}, {"sourceRef": "2025.0498ad64-a11a-4297-8edc-edb674ae3556", "targetRef": "bd7713f0-26f4-411c-a13c-bd4bf8846a29", "name": "Yes", "id": "2027.bf2ae001-93f0-48cc-8308-299f31ba3e82", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.0498ad64-a11a-4297-8edc-edb674ae3556", "targetRef": "2025.888c4b9a-9fe3-4d9a-89fa-253db4480d44", "name": "No", "id": "2027.3eb187df-6798-4532-831c-f3943a96139c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  !=\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.0d904442-1dd4-4df6-8233-4024a60b0373", "targetRef": "2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e", "name": "To End", "id": "2027.f51aa2f2-028a-4d37-838c-c7684270c006", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "targetRef": "2025.d0e44a57-c5bc-441e-8b1f-62fcdc4850e5", "name": "To Postpone", "id": "2027.4c92b8b9-010a-45a8-8d5b-0ba165ad5df3", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "fb368007-d3ad-449a-8ffb-f56e2a35e4cc", "ns3:coachEventPath": "DC_Templete1/saveState"}}}, {"sourceRef": "2025.d0e44a57-c5bc-441e-8b1f-62fcdc4850e5", "targetRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "name": "To Coach", "id": "2027.636dd5a7-5fa4-479c-8391-90dea46b8d50", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.e46d5c23-43ac-4ee4-8c70-639786a54d52", "targetRef": "2025.0d904442-1dd4-4df6-8233-4024a60b0373", "name": "No", "id": "2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.Col_Actions.createLiq", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.e46d5c23-43ac-4ee4-8c70-639786a54d52", "targetRef": "2025.cd5b6d91-8537-44c2-8483-79d91585bc68", "name": "Yes", "id": "2027.bba3c37b-ac6e-4aeb-802b-ab0f9c907141", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.Col_Actions.createLiq", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.cd5b6d91-8537-44c2-8483-79d91585bc68", "targetRef": "2025.888c4b9a-9fe3-4d9a-89fa-253db4480d44", "name": "To update actions list", "id": "2027.90b70243-5a30-47be-8f4d-f00bd0e0968b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "leftCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.74cef49e-b3e2-42aa-81f0-238dc5de00ef", "targetRef": "2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01", "name": "To Set ECM default properties", "id": "2027.0268cbe4-24b1-483d-86e8-f760a263aa20", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d", "targetRef": "2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb", "name": "To Create ECM Folder", "id": "2027.71bcd28a-81f6-44a4-8301-a22662997a00", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb", "targetRef": "2025.0329ea67-2d4f-42e1-820b-1a787ba00c89", "name": "To Coach", "id": "2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.12d89ada-06f5-45ab-8d7d-0112f50eacdd", "targetRef": "2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64", "name": "NO", "id": "2027.dda38f6b-4df2-40ca-8929-4c92203af369", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.12d89ada-06f5-45ab-8d7d-0112f50eacdd", "targetRef": "2025.0329ea67-2d4f-42e1-820b-1a787ba00c89", "name": "YES", "id": "2027.7a7ab062-35fd-4923-8ed8-ffa201c87355", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.isLiquidated", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e", "targetRef": "2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359", "name": "To log history?", "id": "2027.4f289ccc-**************-abbf4de7f9ba", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359", "targetRef": "bd7713f0-26f4-411c-a13c-bd4bf8846a29", "name": "Yes", "id": "2027.c1ebf2f8-4232-41e8-8cda-d8fe0f78c763", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359", "targetRef": "2025.a1bb86e3-acbc-4da9-8262-637e0190103e", "name": "No", "id": "2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage!= null) || (tw.local.errorMessage!= \"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64", "targetRef": "2025.be89ff1b-cbda-4adf-81c1-5d29594b12e6", "name": "To Coach", "id": "2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.8ac7db6a-a3f2-40ee-8ed4-950fba71236e", "targetRef": "2025.8e37f535-a92e-4d3a-88a6-c21f436f9585", "name": "To audited successfully?", "id": "2027.10ea1012-03d1-4f0f-8f9a-46195e38f084", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.8e37f535-a92e-4d3a-88a6-c21f436f9585", "targetRef": "2025.33b378b0-ff93-4f1e-8d79-8682948ccfcc", "name": "No", "id": "2027.9cb639ba-**************-f73939dd1673", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.errorMessage!= null || tw.local.errorMessage!=\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.8e37f535-a92e-4d3a-88a6-c21f436f9585", "targetRef": "2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e", "name": "To Update History", "id": "2027.4ce9e417-e403-4d25-8a20-f4050c61dc84", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.be89ff1b-cbda-4adf-81c1-5d29594b12e6", "targetRef": "2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d", "name": "To Set ECM default properties", "id": "2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.284f7314-8e0a-4308-88b4-ac5db2f03168", "targetRef": "2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf", "name": "yes", "id": "2027.ed10ecc4-4cbd-4b9a-80bb-36c2df6abaa6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.284f7314-8e0a-4308-88b4-ac5db2f03168", "targetRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "name": "no", "id": "2027.7faa0719-e927-4841-8b1d-b4bb154c666e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "rightTop", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.0329ea67-2d4f-42e1-820b-1a787ba00c89", "targetRef": "2025.d27bf540-679c-4767-8059-acaba3a0fe48", "name": "To Retrieve Product Code", "id": "2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.d27bf540-679c-4767-8059-acaba3a0fe48", "targetRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "name": "To Coach", "id": "2027.3de7292b-4475-43dd-838d-9114302ffd0f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.089c7969-235e-42a6-8fd7-0ca88d4fbda3", "targetRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "name": "To Coach", "id": "2027.65484a87-94c7-49cd-8dd2-04d8479aae9d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "rightBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf", "targetRef": "2025.af31c948-21e2-4363-8234-74199d1a467d", "name": "To Get Customer and Party Account List", "id": "2027.2cf47d3e-16cd-4687-81e1-bff7219335ed", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.af31c948-21e2-4363-8234-74199d1a467d", "targetRef": "2025.a54f759f-5a8e-4534-9b01-e72fbb495a0c", "name": "To Coach", "id": "2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "leftCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.788f47f5-d371-4fb4-8593-3c1ca350efab", "targetRef": "2025.930604c9-ac9a-474f-88f3-998e271c68e6", "name": "To Client-<PERSON>", "id": "2027.d2576e89-c82c-4b73-8410-61fc6c0cc480", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.653db24d-c64d-412d-8c14-ec9e5c3f46ec"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessageVis", "id": "2056.1d5065f2-8f3a-4d7c-8ee5-70d9ae7f65bf"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.f8c94287-6a30-4333-8c4c-13fd7742fee2"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.eb65bb62-bdde-49f0-8e18-************"}, {"itemSubjectRef": "itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2", "isCollection": "false", "name": "ldapUserProfile", "id": "2056.d99d5cbf-ba11-4e37-850b-200d74087f56"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNoVIS", "id": "2056.7362e801-01de-4688-890a-7d97f6662a9d"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.7066f302-9d63-41d1-8111-fa98a5382b32"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.c9d26fb0-c6c4-4835-8283-e9fb2c177c30", "ns16:documentation": {"_": "to be deleted ", "textFormat": "text/plain"}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestTypeVIS", "id": "2056.956aea2f-b087-4efd-8c4f-e533a6d31b54", "ns16:documentation": {"_": "to be deleted ", "textFormat": "text/plain"}}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "glAccountVerified", "id": "2056.b60ac548-1c32-4f4e-8016-73b44d024442"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "accounteePartyCif", "id": "2056.3a374913-eb91-4e0f-85d9-86c86cc66465"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "code", "id": "2056.6a1c20ce-b779-4524-835b-d45c71c9ac6b", "ns16:documentation": {"_": "to be deleted<div><br /></div><div>001</div>", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"077\"", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "fullPath", "id": "2056.c421b885-1bba-4e13-830d-64b95f2d6f8f"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "serviceName", "id": "2056.cc04b478-5aa3-4f21-8665-712167bbe276"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isfound", "id": "2056.a5838696-ca01-4729-87b8-cb3baf3ce6df"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "message", "id": "2056.8519f6bb-4cd5-46cd-8252-a8176ff229c2"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "ValidationMessage", "id": "2056.8780a059-b42d-49e7-82e4-ad5b87292d8e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "customerAndPartyCifs", "id": "2056.cccca974-00d9-44bf-871d-0cb67d061dd5"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "rate", "id": "2056.addd2895-783a-42a8-8aa8-33fa2f2d0094"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "calculatedChangeAmnt", "id": "2056.17a10bb1-20a6-40a0-8860-031eb84e9441"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "selectedIndex", "id": "2056.78460a99-4397-47a7-8516-1efd15e65e26"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "<PERSON><PERSON><PERSON><PERSON>", "id": "2056.5cc31eb3-2313-4e5f-8a33-842b4ac32a07"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "exchangeRate", "id": "2056.047ac412-5327-4029-8d29-2b9491db48bf"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractLiquidatedMSG", "id": "2056.aa9d3a1f-4418-4d20-837f-11db247828c6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "verifyGLMsg", "id": "2056.635cda99-e14d-4441-80d9-2a05f7adcf2c"}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "default": "2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b", "name": "Init", "id": "2025.be89ff1b-cbda-4adf-81c1-5d29594b12e6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "186", "y": "175", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5", "ns16:outgoing": "2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b", "ns16:script": "tw.local.isChecker = false;\r\r\n\r\r\n//---------------------- Init appInfo  \"This object is used in Header CV\"---------------------------------\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\t\r\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\ntw.local.odcRequest.appInfo.requestName = \"ODC collection\";\r\r\ntw.local.odcRequest.appInfo.requestType = tw.local.odcRequest.requestType.name;\r\r\ntw.local.odcRequest.appInfo.status =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.subStatus =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo; //tw.system.processInstance.id;\r\r\n\r\r\n//---------------------------------Init Step log ---------------------------------------------------\t \t \r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step = tw.epv.Col_ScreenNames.ODCCol01; \r\r\n\r\r\n //----------------------------------------------------------------------------------------------\r\r\n//Init actionConditions \"This object used by service (Get Actions by ScreenName 2) \"\r\r\ntw.local.actionConditions={};\r\r\ntw.local.actionConditions.complianceApproval= false;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.odcRequest.stepLog.action;\r\r\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\t \r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\t\r\r\n\r\r\n//Init liquidation data\r\r\ntw.local.odcRequest.ContractLiquidation.liqCurrency= tw.local.odcRequest.FinancialDetailsBR.currency.value;\r\r\n//init request state \r\r\ntw.local.odcRequest.BasicDetails.requestState=\"undergoing collection\";\r\r\n\r\r\nif(tw.local.odcRequest.isLiquidated == false)\t{\r\r\n\ttw.local.odcRequest.ContractLiquidation= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAmount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount.currency={};\r\r\n\t}\r\r\n\t\r\r\ntw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\r\r\ntw.local.odcRequest.BasicDetails.requestType = \ttw.local.odcRequest.requestType.name;\r\r\n\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass={};\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.name= \"Customer Account\";\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value= \"Customer Account\";\r\r\n\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "default": "2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25", "name": "Validation", "id": "2025.2cd1e05e-e819-41b6-8d95-dfb27dd0b710", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "936", "y": "179", "width": "95", "height": "70", "color": "#95D087"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.8ebe87c1-8533-4f25-83cb-24907c146d75", "ns16:outgoing": "2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25", "ns16:script": "//\t debugger;\r\r\ntw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel){\r\r\n    mandatory(tw.local.odcRequest.stepLog.comment, \"tw.local.odcRequest.stepLog.comment\");\r\r\n}\r\r\n\r\r\nif (true) {\r\r\n\r\r\n    validateLiq();\r\r\n}\r\r\n\r\r\n//------------------------------------------------- Contract Liquidation VALIDATION -----------------------------------------\r\r\nfunction validateLiq() {\r\r\n    //debugger;\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.liqAmount, \"tw.local.odcRequest.ContractLiquidation.liqAmount\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditValueDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitValueDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\");\r\r\n    \r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.creditValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\", \"Invalid date\", \"Credit Value Date should not be less than request date\");\r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.debitValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\", \"Invalid date\", \"Debit Value Date should not be less than request date\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitedAccountNo, \"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\");\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value == \"GL Account\") {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo\");\r\r\n\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified == false) {\r\r\n            tw.local.errorMessage += \"<li>\" + \"GL Account is not verified.\" + \"</li>\";\r\r\n            tw.system.coachValidation.addValidationError(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"GL Account is not verified.\");\r\r\n        }\r\r\n\r\r\n        //debugger;\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified) {\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode\");\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.currency\");\r\r\n        }\r\r\n\r\r\n        //Validate OverDraft\r\r\n        if (tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount > tw.local.odcRequest.ContractLiquidation.creditedAccount.balance && tw.local.odcRequest.ContractLiquidation.creditedAccount.isOverDraft) {\r\r\n            addError(\"tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\")\r\r\n        }\r\r\n    \r\r\n    } else {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo\");\r\r\n    }\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.liqAmount > tw.local.odcRequest.FinancialDetailsFO.outstandingAmount) {\r\r\n        addError(\"tw.local.odcRequest.ContractLiquidation.liqAmount\", \"Liquidation Amount must be <= ODC Outstanding Amount\");\r\r\n        tw.local.errorMessage += \"<li>\" + \"Liquidation Amount must be less than or equal to ODC Outstanding Amount.\" + \"</li>\";\r\r\n    }\r\r\n\r\r\n    //----------------------------------------------- Charges and Commissions VALIDATION -------------------------------------\t\t\r\r\n    for (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\r\n\r\r\n        //Description - Flat Amount\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            //        mandatory(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\");\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 2, \"Shouldn't be less than 2 character\", \"Change Amount: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 14, \"Shouldn't be more than 14 character\", \"Change Amount:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n        //Fixed Rate\r\r\n        } else {\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 2, \"Shouldn't be less than 2 character\", \"Change Percentage: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 14, \"Shouldn't be more than 14 character\", \"Change Percentage:\" + \"Shouldn't be more than 14 character\");\r\r\n        }\r\r\n\r\r\n        //skip validation if waiver or changeAmnt < 0\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].waiver == false && tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n\r\r\n            //Validate debited accounts\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.accountClass\");\r\r\n\r\r\n            //GL Account\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value == \"GL Account\") {\r\r\n\r\r\n                if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\r\n                    addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\", \"GL Account Not Verified\");\r\r\n                }\r\r\n\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.currency.value\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.branchCode\");\r\r\n\r\r\n                //Customer Account\r\r\n            } else {\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            }\r\r\n\r\r\n            //DebitedAmount\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\r\r\n                validateDecimal(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", \"Must be Decimal(16,10)\")\r\r\n            }\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 2, \"Shouldn't be less than 2 character\", \"Negotiated Exchange Rate: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 14, \"Shouldn't be more than 14 character\", \"Negotiated Exchange Rate:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n            //Correct Validation but Waiting confirmation on what to do if GL account\r\r\n            if ((tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\r\r\n                tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance) && tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\");\r\r\n            }\r\r\n        }\r\r\n    }\r\r\n}   //end of validation function\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\r\n    tw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n    fromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the date1 is less than date2\r\r\n*\t\r\r\n* EX:checkDate( date, requestDate ,  'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction checkDate(date, requestDate, dateName, controlMessage, validationMessage) {\r\r\n    if (date != null && date != undefined && date < requestDate) {\r\r\n        addError(dateName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field != null && field != undefined && field.length < len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field.length > len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field, fieldName) {\r\r\n    if (field == null || field == undefined) {\r\r\n        addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n        mandatoryTriggered = true;\r\r\n        return false;\r\r\n    }\r\r\n    else {\r\r\n        switch (typeof field) {\r\r\n            case \"string\":\r\r\n                if (field.trim() != undefined && field.trim() != null && field.trim().length == 0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            case \"number\":\r\r\n                if (field == 0.0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                if (field < 0) {\r\r\n                    var msg = \"Invalid Value, This field can not be negative value.\";\r\r\n                    addError(fieldName, msg, msg, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n\r\r\n            case \"boolean\":\r\r\n                if (field == false) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            default:\r\r\n\r\r\n                // VALIDATE DATE OBJECT\r\r\n                if (field && field.getTime && isFinite(field.getTime())) { }\r\r\n\r\r\n                else {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n        }\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n\r\r\n//Validate Decimal(10,6)\r\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,4}(\\.\\d{1,6})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\n\r\r\n//Validate Decimal(14,2)\r\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\r\n    validationMessage = controlMessage;\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,12}(\\.\\d{1,2})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\ntw.local.errorMessage != null ? tw.local.errorMessageVis = \"EDITABLE\" : tw.local.errorMessageVis = \"NONE\";"}, {"scriptFormat": "text/x-javascript", "default": "2027.90b70243-5a30-47be-8f4d-f00bd0e0968b", "name": "Set Liq Flag", "id": "2025.cd5b6d91-8537-44c2-8483-79d91585bc68", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1195", "y": "291", "width": "95", "height": "70"}}, "ns16:incoming": "2027.bba3c37b-ac6e-4aeb-802b-ab0f9c907141", "ns16:outgoing": "2027.90b70243-5a30-47be-8f4d-f00bd0e0968b", "ns16:script": "tw.local.odcRequest.isLiquidated= true;\r\r\ntw.local.odcRequest.stepLog.action=\"\";\r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\r\r\n\r\r\ntw.local.isChecker = true;\r\r\ntw.local.contractLiquidatedMSG = \"Contract Liquidated Successfully\";\r\r\n//IMPORTANT : comment this line if you want to test from inside CSHS!!!\r\r\nwindow.location.reload();\r\r\n"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.0268cbe4-24b1-483d-86e8-f760a263aa20", "name": "Set Attachments list", "id": "2025.74cef49e-b3e2-42aa-81f0-238dc5de00ef", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "294", "y": "326", "width": "95", "height": "69"}}, "ns16:outgoing": "2027.0268cbe4-24b1-483d-86e8-f760a263aa20", "ns16:script": "if(!tw.local.odcRequest.attachmentDetails)\r\r\n\ttw.local.odcRequest.attachmentDetails= {};\r\r\n\t\r\r\nif (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {\r\r\n\ttw.local.odcRequest.attachmentDetails.attachment = []; \r\r\n\t\r\r\n}\t\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"طلب العميل\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].name = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].description = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].name = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].description = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].arabicName = \"فاتورة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].name = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].description = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].name = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].description = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].name = \"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].description =\"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].name = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].description = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].name = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].description = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].name = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].description = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].name = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].description = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].description = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].name = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].description = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].name = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].description = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].name = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].description = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].name = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].description = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].description = \"Bill of exchange/draft\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].arabicName = \"الكمبيالة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].name = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].description = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].name = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].description = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].arabicName = \"\";;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].name = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].description = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].arabicName = \"\";\r\r\n\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].name = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].description = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = {};\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Client-<PERSON>", "id": "2025.930604c9-ac9a-474f-88f3-998e271c68e6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "397", "y": "522", "width": "95", "height": "70"}}, "ns16:incoming": "2027.d2576e89-c82c-4b73-8410-61fc6c0cc480", "ns16:script": "tw.error.data"}], "ns16:intermediateThrowEvent": [{"name": "Postpone", "id": "2025.d0e44a57-c5bc-441e-8b1f-62fcdc4850e5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "803", "y": "304", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.636dd5a7-5fa4-479c-8391-90dea46b8d50"}, "ns16:incoming": "2027.4c92b8b9-010a-45a8-8d5b-0ba165ad5df3", "ns16:outgoing": "2027.636dd5a7-5fa4-479c-8391-90dea46b8d50", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page", "id": "2025.888c4b9a-9fe3-4d9a-89fa-253db4480d44", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1093", "y": "314", "width": "71", "height": "44"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": ""}, "ns16:incoming": ["2027.3eb187df-6798-4532-831c-f3943a96139c", "2027.90b70243-5a30-47be-8f4d-f00bd0e0968b"], "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.a1bb86e3-acbc-4da9-8262-637e0190103e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1939", "y": "272", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902", "ns3:stayOnPageEventDefinition": ""}, {"name": "Copy of Stay on page 1", "id": "2025.33b378b0-ff93-4f1e-8d79-8682948ccfcc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1693", "y": "279", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.9cb639ba-**************-f73939dd1673", "ns3:stayOnPageEventDefinition": ""}], "ns16:exclusiveGateway": [{"default": "2027.bf2ae001-93f0-48cc-8308-299f31ba3e82", "name": "Valid?", "id": "2025.0498ad64-a11a-4297-8edc-edb674ae3556", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1057", "y": "198", "width": "32", "height": "32"}}, "ns16:incoming": "2027.da9e7e56-6c63-403a-8135-0f9c7f1c5c25", "ns16:outgoing": ["2027.bf2ae001-93f0-48cc-8308-299f31ba3e82", "2027.3eb187df-6798-4532-831c-f3943a96139c"]}, {"default": "2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb", "name": "Is Liquidated?", "id": "2025.e46d5c23-43ac-4ee4-8c70-639786a54d52", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1204", "y": "195", "width": "32", "height": "32"}, "ns3:preAssignmentScript": "tw.local.contractLiquidatedMSG = \"\";"}, "ns16:outgoing": ["2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb", "2027.bba3c37b-ac6e-4aeb-802b-ab0f9c907141"]}, {"default": "2027.dda38f6b-4df2-40ca-8929-4c92203af369", "name": "is Liquidated?", "id": "2025.12d89ada-06f5-45ab-8d7d-0112f50eacdd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "-39", "y": "196", "width": "32", "height": "32"}}, "ns16:outgoing": ["2027.dda38f6b-4df2-40ca-8929-4c92203af369", "2027.7a7ab062-35fd-4923-8ed8-ffa201c87355"]}, {"default": "2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902", "name": "log history?", "id": "2025.673fa4c7-7b9c-4cc6-85e2-50b292bc6359", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1872", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.4f289ccc-**************-abbf4de7f9ba", "ns16:outgoing": ["2027.c1ebf2f8-4232-41e8-8cda-d8fe0f78c763", "2027.cd4ac67d-ab85-44d3-841e-ad45eea2e902"]}, {"default": "2027.9cb639ba-**************-f73939dd1673", "gatewayDirection": "Unspecified", "name": "audited successfully?", "id": "2025.8e37f535-a92e-4d3a-88a6-c21f436f9585", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1574", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.10ea1012-03d1-4f0f-8f9a-46195e38f084", "ns16:outgoing": ["2027.9cb639ba-**************-f73939dd1673", "2027.4ce9e417-e403-4d25-8a20-f4050c61dc84"]}, {"default": "2027.7faa0719-e927-4841-8b1d-b4bb154c666e", "gatewayDirection": "Unspecified", "name": "Has ProductCode?", "id": "2025.284f7314-8e0a-4308-88b4-ac5db2f03168", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "772", "y": "57", "width": "32", "height": "32"}}, "ns16:outgoing": ["2027.ed10ecc4-4cbd-4b9a-80bb-36c2df6abaa6", "2027.7faa0719-e927-4841-8b1d-b4bb154c666e"]}], "ns16:callActivity": [{"calledElement": "1.81656d33-5348-479b-a7af-5631356d9476", "default": "2027.f51aa2f2-028a-4d37-838c-c7684270c006", "name": "Set Status And Sub Status", "id": "2025.0d904442-1dd4-4df6-8233-4024a60b0373", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1351", "y": "177", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": ""}, "ns16:incoming": "2027.f403f4a1-ecdd-4f36-80dc-fc50a9e7c8eb", "ns16:outgoing": "2027.f51aa2f2-028a-4d37-838c-c7684270c006", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.stepLog.action", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.6010a917-3b0f-4c13-8973-51c4eede4cd9", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.stepName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.71a67d77-b802-42cf-8cf2-380a9594a9c2", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.ce1f6bfc-b971-4978-80c8-092f5d40c209", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "default": "2027.4f289ccc-**************-abbf4de7f9ba", "name": "Update History", "id": "2025.1ad295eb-8d1d-4899-8c3d-7ed7fe7a011e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1754", "y": "178", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": ["2027.4ce9e417-e403-4d25-8a20-f4050c61dc84", "2027.f51aa2f2-028a-4d37-838c-c7684270c006"], "ns16:outgoing": "2027.4f289ccc-**************-abbf4de7f9ba", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.epv.userRole.RevAct01", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.a617c560-c740-484e-89de-0931088cdc6c", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.46b984a3-b4ad-405a-abd3-8631f907efe4", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e", "name": "Create ECM Folder", "id": "2025.a3a22a14-3d36-48ae-86a1-fd30e6063bbb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "466", "y": "176", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.serviceName=\"Create\"", "ns3:postAssignmentScript": "tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;// tw.local.parentPath\r\r\ntw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;\r\r\n\r\r\nif(!!tw.local.error && tw.local.error.errorText!=null)\r\r\n\ttw.local.errorMessage+= tw.local.error.errorText;"}, "ns16:incoming": "2027.71bcd28a-81f6-44a4-8301-a22662997a00", "ns16:outgoing": "2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.4156964b-1c67-40bc-8f62-3804c71cf908", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.e2ce0eed-342c-4942-8214-83e964b550e5", "ns16:assignment": {"ns16:from": {"_": "tw.local.routingDetails.hubCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:sourceRef": "2055.5f955245-0538-4e40-80a6-12f45c3102f3", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.214c7268-80d0-444d-8702-dd0d5462dbe7", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7", "ns16:assignment": {"ns16:to": {"_": "tw.local.fullPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2", "ns16:assignment": {"ns16:to": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.676e3a06-e2cc-4855-84d6-6f82a350500a", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.71bcd28a-81f6-44a4-8301-a22662997a00", "name": "Set ECM default properties", "id": "2025.874c1e86-99d2-4bb6-8416-e615ab5a2e4d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "327", "y": "176", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": "2027.9e00bb22-408c-4c8e-83bf-0c5a3eba151b", "ns16:outgoing": "2027.71bcd28a-81f6-44a4-8301-a22662997a00", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.399c7a58-00b5-4451-9813-41c0b9652088", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.c28023fb-b45e-4b63-ae36-97e6df6421bc", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.25394215-074f-4b79-8e84-9a96d32cc83b", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.7d269650-ee48-4101-80db-2807cf921562", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.attachmentDetails", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9"}}}, {"ns16:sourceRef": "2055.0261e8ad-a540-4682-88c5-87dff3eab23c", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb", "name": "Get Customer Account Details", "id": "2025.0329ea67-2d4f-42e1-820b-1a787ba00c89", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "602", "y": "176", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.accounteePartyCif = \"\";\r\r\n\r\r\nif(!!tw.local.odcRequest.Parties && !!tw.local.odcRequest.Parties.partyTypes && tw.local.odcRequest.Parties.partyTypes.partyCIF !=null ) \r\r\n\ttw.local.accounteePartyCif= tw.local.odcRequest.Parties.partyTypes.partyCIF;", "ns3:mode": "InvokeService", "ns3:autoMap": "true", "ns3:postAssignmentScript": ""}, "ns16:incoming": ["2027.6c60576a-1e30-4aa8-8d0d-a31ee4569c0e", "2027.7a7ab062-35fd-4923-8ed8-ffa201c87355"], "ns16:outgoing": "2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.7a0225d0-1ea8-4907-afba-e3a98de88df1", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.d471dcbe-97c5-4911-87c7-7008dadc3a15", "ns16:assignment": {"ns16:from": {"_": "tw.local.accounteePartyCif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.5855796c-8062-4709-8c2a-18f470d6d879", "ns16:assignment": {"ns16:to": {"_": "tw.local.customerAccounts", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"}}}, {"ns16:sourceRef": "2055.a32d29e2-af5d-4f6f-8041-8778849c9737", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.328cd87f-3306-4c20-8923-b7515b1cb782", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "default": "2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5", "name": "Retrieve Request Number", "id": "2025.16c2d15d-ad69-41f8-876b-a0d9486eaa64", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "46", "y": "176", "width": "95", "height": "70"}}, "ns16:incoming": "2027.dda38f6b-4df2-40ca-8929-4c92203af369", "ns16:outgoing": "2027.ea7640ea-b47b-4e2c-8976-5d0e9fe4f3e5", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.e1ca7465-4084-405d-8ea6-ab3f3762de92", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.29fabc80-90b8-4cad-81e3-1c319c6f595a", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.2d3ab562-82df-48a5-9de7-f5d964218191", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Copy of Set ECM default properties", "id": "2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "409", "y": "331", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": "2027.0268cbe4-24b1-483d-86e8-f760a263aa20", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.6f216377-34de-4f5e-8ab5-adc2796733ee", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.customerName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d", "ns16:assignment": {"ns16:from": {"_": "tw.system.user.fullName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701", "ns16:assignment": {"ns16:from": {"_": "tw.system.processInstance.id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81cce609-b3fe-4f11-809f-c3a599908595", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.eac829db-66fe-43f5-810c-6faa514533a2"}}}}, {"calledElement": "1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.10ea1012-03d1-4f0f-8f9a-46195e38f084", "name": "Audit Collection Data", "id": "2025.8ac7db6a-a3f2-40ee-8ed4-950fba71236e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1476", "y": "178", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "\r\r\ntw.local.odcRequest.BasicDetails.requestState=tw.epv.RequestState.Collection ;\r\r\n\r\r\n"}, "ns16:outgoing": "2027.10ea1012-03d1-4f0f-8f9a-46195e38f084", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.e39bfaa6-c863-41f9-8061-0e371dff89cb", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.isLiquidated", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:targetRef": "2055.5d4b901c-324e-4bea-8f10-e160a656c696", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.OdcCollection.amount", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}}}, {"ns16:targetRef": "2055.b51575f2-8ce0-48d0-8179-71d12e0440e7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.OdcCollection.currency", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.84daf689-5f95-458f-8b7f-d8c08459d4c1", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.20995cf3-6a12-4378-8292-51106389c796", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNature.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestDate", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}}}, {"ns16:targetRef": "2055.85dca7ee-4057-4dcd-878f-b924dff64190", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.328377fd-ccc9-4119-80ca-435deb518aee", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.37b99722-adca-4c0b-8d6d-aa2eeae29994", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b0be0c94-0742-4365-875f-1b01b63caf0c", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.27a871f0-6893-4366-80d9-133f55bffddb", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.ebcd1729-7d20-4759-81b3-e98e9f554767", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.debd9766-ed8e-45c7-8bbb-c471a2567088", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.8d379594-e94f-4a21-8222-396c4ba9b2e1", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.2cf47d3e-16cd-4687-81e1-bff7219335ed", "name": "Get Charges Completed", "id": "2025.415f9af6-9446-40cf-8fd1-98d5fc66dbaf", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "757", "y": "-68", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:activityAdHocSettings": {"repeatable": "false", "hidden": "false", "triggerType": "Automatic", "option": "Required"}, "ns3:autoMap": "true", "ns3:activityPreconditions": {"triggerType": "NoPreconditions", "documentTriggerMode": "LegacyCase", "matchAll": "true", "sourceFolderReferenceType": "FolderId"}}, "ns16:incoming": "2027.ed10ecc4-4cbd-4b9a-80bb-36c2df6abaa6", "ns16:outgoing": "2027.2cf47d3e-16cd-4687-81e1-bff7219335ed", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.12683c54-ad5b-4c68-b734-93d1dea93938", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ChargesAndCommissions", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.e3018bad-b453-4bf5-96fd-09a5141cd061"}}}, {"ns16:targetRef": "2055.7d751a23-0e1b-4987-a9bc-41b25f644f54", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ContractCreation.productCode.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.66b62639-4042-49bf-9544-dbb15f00f3be", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.86004a6f-9b72-4f74-a69a-aed7c869a281", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}}}, {"ns16:targetRef": "2055.1001aa15-5c17-4db8-ab30-0263431130b2", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.FinancialDetailsBR.currency.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.ad7e0a45-5cb2-437f-b705-2ff310914291", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.25041969-c52c-4425-8c47-741f31a7fa66", "ns16:assignment": {"ns16:from": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.ChargesAndCommissions", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061"}}}}, {"calledElement": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "default": "2027.3de7292b-4475-43dd-838d-9114302ffd0f", "name": "Retrieve Product Code", "id": "2025.d27bf540-679c-4767-8059-acaba3a0fe48", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "603", "y": "290", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "//tw.local.odcRequest.parentRequestNo = \"00104230000170\";\r\r\n//tw.local.odcRequest.requestType.value = tw.epv.RequestType.Collection\r\r\n"}, "ns16:incoming": "2027.d3b7bdc2-dd81-44a8-8f2e-f662970d7ceb", "ns16:outgoing": "2027.3de7292b-4475-43dd-838d-9114302ffd0f", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.739ec1bd-4791-48bc-830c-f00e254474a3", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.ContractCreation.productCode.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.e6db1787-627d-4373-80cc-75c8940bc32d", "ns16:assignment": {"ns16:to": {"_": "tw.local.isfound", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.e1811b7a-e326-4b9c-863e-59e994c41703", "ns16:assignment": {"ns16:to": {"_": "tw.local.message", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.672c16cb-04d7-4e67-a904-779d9009e1ce", "default": "2027.65484a87-94c7-49cd-8dd2-04d8479aae9d", "name": "Get Debited Nostro Vostro Account", "id": "2025.089c7969-235e-42a6-8fd7-0ca88d4fbda3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "793", "y": "393", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true", "ns3:postAssignmentScript": "if (tw.local.ValidationMessage == \"\" && tw.local.ValidationMessage == null) {\r\r\n\tif (tw.local.errorMessage == \"\" && tw.local.errorMessage == null) {\r\r\n\t\talert(\"Valid\");\r\r\n\t}\r\r\n}else{\r\r\n tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\", tw.local.ValidationMessage);\r\r\n}"}, "ns16:outgoing": "2027.65484a87-94c7-49cd-8dd2-04d8479aae9d", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.723db670-6afc-4c61-8bff-61069e97a6df", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ContractLiquidation.debitedAccountNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.a4b64bc1-a6e1-4866-82c6-48a1bd9a6693", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ContractLiquidation.liqCurrency", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.6d466c39-b0d0-4414-8065-f99b230bcf07", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.63de69c3-31f4-45df-8a71-32f0f2f65fd2", "ns16:assignment": {"ns16:to": {"_": "tw.local.ValidationMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.3387da0f-2282-4ae0-8159-9dc014499f8f", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.ContractLiquidation.debitedAccountName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b", "name": "Get Customer and Party Account List", "id": "2025.af31c948-21e2-4363-8234-74199d1a467d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "755", "y": "-168", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true", "ns3:preAssignmentScript": "tw.local.customerAndPartyCifs = [];\r\r\nvar customerCIF = tw.local.odcRequest.CustomerInfo.cif;\r\r\n\r\r\nvar partyCIF = tw.local.odcRequest.Parties.partyTypes.partyId;\r\r\nvar caseInNeedCIF = tw.local.odcRequest.Parties.caseInNeed.partyId;\r\r\n\r\r\n//Build Cifs list with valid values or \"\" as default\r\r\n!!customerCIF ? tw.local.customerAndPartyCifs[0] = customerCIF : tw.local.customerAndPartyCifs[0] = \"\";\r\r\n\r\r\n!!partyCIF ? tw.local.customerAndPartyCifs[1] = partyCIF : tw.local.customerAndPartyCifs[1] = \"\";\r\r\n\r\r\n!!caseInNeedCIF ? tw.local.customerAndPartyCifs[2] = caseInNeedCIF : tw.local.customerAndPartyCifs[2] = \"\";\r\r\n\r\r\n//remove duplicate cifs using SET\r\r\ntw.local.customerAndPartyCifs = [...new Set(tw.local.customerAndPartyCifs)];"}, "ns16:incoming": "2027.2cf47d3e-16cd-4687-81e1-bff7219335ed", "ns16:outgoing": "2027.c49a4b5d-3daa-4dca-8fdd-73b38b06c48b", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518", "ns16:assignment": {"ns16:from": {"_": "tw.local.customerAndPartyCifs", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.customerAndPartyAccountList", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"}}}, {"ns16:sourceRef": "2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "2025.4f78f87c-8a77-46e1-8963-4cd8a3a58f01", "parallelMultiple": "false", "name": "Error", "id": "2025.788f47f5-d371-4fb4-8593-3c1ca350efab", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "444", "y": "389", "width": "24", "height": "24"}, "ns3:default": "2027.d2576e89-c82c-4b73-8410-61fc6c0cc480"}, "ns16:outgoing": "2027.d2576e89-c82c-4b73-8410-61fc6c0cc480", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "2025.35937466-48ed-45bc-81d2-07b979456720"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:textAnnotation": {"textFormat": "text/plain", "id": "f5971490-8436-4241-85a4-878810f50042", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1121", "y": "10", "width": "196", "height": "72"}}, "ns16:text": "//IMPORTANT : comment ths last line on (Set Liq Flag script) if you want to test from inside CSHS!!!"}, "ns3:htmlHeaderTag": {"id": "c9a3bd34-d69c-4e98-a09d-d8b078dcd047", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:mobileReady": "true", "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "epvProcessLinkId": "87b4df33-7920-4ce7-86b1-893067e50ef0"}, {"epvId": "21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "epvProcessLinkId": "00fa02af-a828-48c4-89b1-5582241f99cb"}, {"epvId": "21.93c5c002-7ac4-4283-83ee-63b8662f9223", "epvProcessLinkId": "74ed432e-5125-4d4d-8504-d1a1245fb100"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "04161144-e1f9-43bb-8e07-bf171bc54528"}, {"epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "epvProcessLinkId": "e474676d-2c7a-4a4d-84d2-415932aae2f4"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "a86f9fd0-6f96-403d-8b05-bc3945f285e2"}, {"epvId": "21.062854b5-6513-4da8-84ab-0126f90e550d", "epvProcessLinkId": "e3108d03-1589-46fe-8951-79bfb122d51b"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.a759db86-0dc8-41dc-8faf-40ea72941541"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = {};\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = [];\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = [];\r\nautoObject.FcCollections.listOfAccounts[0] = {};\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = [];\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = {};\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.Drawer = {};\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = {};\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = {};\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = {};\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = {};\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.Parties.caseInNeed = {};\r\nautoObject.Parties.caseInNeed.partyCIF = \"\";\r\nautoObject.Parties.caseInNeed.partyId = \"\";\r\nautoObject.Parties.caseInNeed.partyName = \"\";\r\nautoObject.Parties.caseInNeed.country = \"\";\r\nautoObject.Parties.caseInNeed.language = \"\";\r\nautoObject.Parties.caseInNeed.refrence = \"\";\r\nautoObject.Parties.caseInNeed.address1 = \"\";\r\nautoObject.Parties.caseInNeed.address2 = \"\";\r\nautoObject.Parties.caseInNeed.address3 = \"\";\r\nautoObject.Parties.caseInNeed.partyType = {};\r\nautoObject.Parties.caseInNeed.partyType.name = \"\";\r\nautoObject.Parties.caseInNeed.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].isGLFound = false;\r\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.debitedAccountName = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"\";\r\nautoObject.templateDocID = \"\";\r\nautoObject.requestID = 0;\r\nautoObject.customerAndPartyAccountList = [];\r\nautoObject.customerAndPartyAccountList[0] = {};\r\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\r\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\r\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\r\nautoObject.customerAndPartyAccountList[0].frozen = false;\r\nautoObject.customerAndPartyAccountList[0].dormant = false;\r\nautoObject.customerAndPartyAccountList[0].noDebit = false;\r\nautoObject.customerAndPartyAccountList[0].noCredit = false;\r\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\r\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\r\nautoObject.tradeFoComment = \"\";\r\nautoObject.exeHubMkrComment = \"\";\r\nautoObject.compcheckerComment = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "routingDetails", "itemSubjectRef": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "isCollection": "false", "id": "2055.fb57a244-f263-444b-8dc2-734946370a33", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.hubCode = \"\";\r\nautoObject.branchCode = \"\";\r\nautoObject.initiatorUser = \"\";\r\nautoObject.branchName = \"\";\r\nautoObject.hubName = \"\";\r\nautoObject.branchSeq = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95"}], "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.115324d0-8168-495d-8187-b6a3c2434105"}, {"name": "customerAccounts", "itemSubjectRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isCollection": "true", "id": "2055.f0a5931f-96f0-4329-81d0-83ef90e6192f"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4"}], "ns16:inputSet": {"id": "_dcce9938-6b07-42b3-8c25-4d1129b311da"}, "ns16:outputSet": {"id": "_3d1abe30-59bc-49ab-adbc-1dda116bcaa2"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1f5d365e-2d42-4bb9-a41f-c5cbc8fd0480", "processId": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.0e2ba1eb-4c5b-4ef0-92fa-ae15d9eab69d", "2025.0e2ba1eb-4c5b-4ef0-92fa-ae15d9eab69d"], "endStateId": "Out", "toProcessItemId": ["2025.7a68f35f-ddb4-4f2c-84c5-51d75156a4c6", "2025.7a68f35f-ddb4-4f2c-84c5-51d75156a4c6"], "guid": "390f23a5-2e65-4df6-ac19-7c1e478c39eb", "versionId": "9e0146e5-742f-47b2-b5f4-7e4b12079e7a", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}