<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9" name="ACT06 -Print Remittance Letter">
        <lastModified>1732197274209</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.974f7094-9186-49c3-82f6-db4f1cc961d5</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:4a5a0677488e996f:-34e0d142:1899236511b:-5777</guid>
        <versionId>d2ff0bc2-3f2f-40de-8897-6c929dcae36d</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.3b729580-9c2a-4e6a-b4b6-93fe294e447d"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":113,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"c711e944-e5e7-42e7-9c36-d164f20346a4"},{"outgoing":["2027.4acf17b7-6d32-48f5-8b42-1a393335baca","2027.5ca616ef-4aec-4bf9-829c-d52930279bf0"],"incoming":["2027.ec0f2317-c6d2-49db-8b83-5502e37e791c","2027.91de833e-dc58-4b5b-8365-1a1d691e67ec","2027.1721a516-1161-4ae5-8452-************"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":350,"y":177,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Basic_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"86c78cef-1f3c-43f0-8d41-66492a5f0110","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7416abc6-b699-49d1-89dc-a33809e26d88","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9412dc28-9a17-4a0f-8bbe-574b5beb52d4","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e95c2e32-07bd-456a-8260-55b9e53265a1","optionName":"flexCubeContractNoVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5eebd313-97c1-4458-8339-9e14d0c9a108","optionName":"basicDetailsCVVIS","value":"Readonly"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"15784a0d-7edf-4b76-8583-405cee775daa","optionName":"parentRequestNoVis","value":"tw.local.parentRequestNo"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7474d1bf-6be3-4be3-8f71-82e15060c2a9","optionName":"contractStageVIS","value":"tw.local.contractStageVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1e148971-3bcf-4ba3-87c4-fa640652ce54","optionName":"multiTenorDatesVIS","value":"None"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"7e68018f-d537-4584-80bb-4e7761e97a48","version":"8550"},{"layoutItemId":"Customer_Information_cv1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"077e93ed-810d-4fd4-8bb2-d21b04ab721c","optionName":"@label","value":"Customer Info."},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ef568cc0-6dcb-4d0c-8355-3623664ea331","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"280f4bba-ac2e-4dc4-8bd8-36fff08d794e","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f97037e4-33cf-4274-8209-a99159f496d0","optionName":"customerInfoVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1feb3688-737b-406a-82ea-f83c5d186cb9","optionName":"listsVIS","value":"None"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"268b2c33-9af0-43d8-8d8b-78436c49c8d6","version":"8550"},{"layoutItemId":"Financial_Details_Branch_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"467a2fee-d5bf-45e8-827a-6fc3cc11e4d7","optionName":"@label","value":"Financial Details - Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3546fd16-7aa9-4707-80e5-125e19cbde07","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1c54b0ca-9183-4d28-8e19-24e7e3266b19","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"46ae30b7-08da-455a-824e-6cc80387ae48","optionName":"financialDetailsCVVis","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"afef5bf0-d3c0-4d17-81f9-4c7b6e5e71f8","optionName":"currencyDocAmountVIS","value":"Readonly"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"756ef8b5-02eb-4a55-86a0-8d426791d367","version":"8550"},{"layoutItemId":"FC_Collections_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2a7f5c09-69c5-4a8e-8e7b-ffbbb6325483","optionName":"@label","value":"FC Collections"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9b27ab66-0c2c-4042-8fd8-a7711fecec78","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"41d996e3-788a-4c4d-845f-cdfa15650f53","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9d89440c-b073-4e6b-822e-585f1a5d6d50","optionName":"FCVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"10dbbe11-9cc5-452c-8589-02d38ffedfed","optionName":"collectionCurrencyVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ea312100-a878-499b-8fca-ef7b593a421d","optionName":"negotiatedExchangeRateVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7d13ddb7-5756-42e6-8232-ef50196c7998","optionName":"addBtnVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"398ece67-f0ee-4a63-83d1-6e952a0818b2","optionName":"retrieveBtnVis","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9e539ade-b4dd-4186-84bd-27921c95af02","optionName":"activityType","value":"read"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5884090c-fcd3-4526-80f2-67a90c8da501","optionName":"customerCif","value":"tw.local.odcRequest.cif"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","binding":"tw.local.odcRequest.FcCollections","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"69454a2c-d20c-47c8-8a17-f12204a38ff9","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3173f6b9-93e6-44a3-8764-688be8430539","optionName":"@label","value":"Attachments"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"62ca54db-8098-4c14-8108-65867182e5a0","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"98a7fb90-00ac-4bd3-8fca-5a2f0bd8a109","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9aa0516e-cf6a-43c8-8fff-cd54f8d50d90","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7616841b-7960-417b-80a0-9fff14d8593a","optionName":"remittanceLetterPath","value":"tw.local.odcRequest.folderPath"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"3c5e341b-3686-4c12-8e9a-d528f34d12b6","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"96ed0cce-3a0a-4982-88db-7568c6f6b3c8","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c011e63c-3608-4661-85f2-93c7d0e759e6","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65697140-ba06-461f-895d-0c0c22921883","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"2eb97755-41d7-4ac9-8c10-2de66dcb5f60","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"ccc6cb85-fba4-4adc-80dd-07cf6959de84"}],"layoutItemId":"Tabs1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3f984588-4ab2-44af-8e6e-bef60353a08f","optionName":"@label","value":"Tabs"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"793ca8e8-d0e6-4b55-86c8-b11ec9667a7e","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"145baff7-d18b-4219-8f83-55b4ccff3205","optionName":"@width","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"99%\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"015fbc4f-c318-47aa-8ee3-a3990cca8182","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1d520a3e-c745-45ad-88d7-9da57c271567","optionName":"tabsStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1357eccf-5c4b-4266-81bb-bd7d6b2b818d","optionName":"colorStyle","value":"P"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"77c72096-597d-48f4-875b-76f3594ed6ae","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"2daa6aa6-0808-47b3-83c6-978cfc300a2f"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b7c6bdaa-49b3-45cb-89d5-83efd0dd5321","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d6d7381f-e367-4dca-8402-224357ffb741","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"692b5655-1d98-4c46-811f-ea2b2e2250a5","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"10b28994-74a1-404d-8e3d-ebd43e784b89","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bb8b2aa9-7124-4604-8ff9-25ca0af41ca8","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3ab1a850-e7a8-4976-88e6-afeb53570538","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"24f03d4e-042a-4842-8a8b-5ad4327b56f9","optionName":"complianceApprovalVis","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a7e82150-82f3-4ae7-86df-e5e73e570ed4","optionName":"terminateReasonVIS","value":"NONE"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d219e8c6-97c1-4a1e-83ee-d2ccb7107606","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"03e7869b-a2d3-461d-8661-1feea87732b9","optionName":"actionConditions","value":"tw.local.actionConditions"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6b26645d-7028-4fc7-8534-24d99bdd9112","optionName":"errorPanelVIS","value":"tw.local.errorPanelVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d5079582-23cb-4bc0-8d12-c356d99dc4dd","optionName":"returnReasonVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"be75abab-d028-4b72-8ed8-2f010b977e6a","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"03d495b7-cc53-43e8-8752-ea71692962a4","optionName":"exeHubMkrCommentVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a81bda6c-aa45-46d6-8019-17a46202f030","optionName":"tradeFoCommentVis","value":"None"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"19126455-141e-4854-8915-dae81b9a9aed","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Print Remittance Letter","isForCompensation":false,"completionQuantity":1,"id":"2025.47043a08-3af8-43a7-9282-e01bcbf9b736"},{"targetRef":"2025.6d30e2e5-1071-4f8a-8a6c-736e5911e306","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Print Remittance Letter","declaredType":"sequenceFlow","id":"2027.3b729580-9c2a-4e6a-b4b6-93fe294e447d","sourceRef":"c711e944-e5e7-42e7-9c36-d164f20346a4"},{"targetRef":"2025.7863ccfc-912e-41f2-8bd5-cda57d9a4fae","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"8c873709-8964-4a6d-8676-5e876f1dad40","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.4acf17b7-6d32-48f5-8b42-1a393335baca","sourceRef":"2025.47043a08-3af8-43a7-9282-e01bcbf9b736"},{"outgoing":["2027.ec0f2317-c6d2-49db-8b83-5502e37e791c"],"incoming":["2027.5ca616ef-4aec-4bf9-829c-d52930279bf0"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.ec0f2317-c6d2-49db-8b83-5502e37e791c"],"nodeVisualInfo":[{"width":24,"x":374,"y":46,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.e890f783-e450-46c7-8c59-a1811d24226a"},{"targetRef":"2025.e890f783-e450-46c7-8c59-a1811d24226a","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"c19ca4e6-0d9c-4ff8-80dc-66d9c73c8ad4","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.5ca616ef-4aec-4bf9-829c-d52930279bf0","sourceRef":"2025.47043a08-3af8-43a7-9282-e01bcbf9b736"},{"targetRef":"2025.47043a08-3af8-43a7-9282-e01bcbf9b736","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Print Remittance Letter","declaredType":"sequenceFlow","id":"2027.ec0f2317-c6d2-49db-8b83-5502e37e791c","sourceRef":"2025.e890f783-e450-46c7-8c59-a1811d24226a"},{"startQuantity":1,"outgoing":["2027.bc31c553-d031-43c8-83a1-0fbff275a123"],"incoming":["2027.4acf17b7-6d32-48f5-8b42-1a393335baca"],"default":"2027.bc31c553-d031-43c8-83a1-0fbff275a123","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":528,"y":176,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.7863ccfc-912e-41f2-8bd5-cda57d9a4fae","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false;\r\n \r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\t\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\t"]}},{"outgoing":["2027.ec9a0b5a-55ae-4096-8dcf-bb297acf7805","2027.91de833e-dc58-4b5b-8365-1a1d691e67ec"],"incoming":["2027.bc31c553-d031-43c8-83a1-0fbff275a123"],"default":"2027.91de833e-dc58-4b5b-8365-1a1d691e67ec","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":660,"y":195,"declaredType":"TNodeVisualInfo","height":32}]},"name":"valid?","declaredType":"exclusiveGateway","id":"2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423"},{"targetRef":"2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To valid?","declaredType":"sequenceFlow","id":"2027.bc31c553-d031-43c8-83a1-0fbff275a123","sourceRef":"2025.7863ccfc-912e-41f2-8bd5-cda57d9a4fae"},{"targetRef":"2025.19cca302-626e-4194-8e94-7f4acaf378ec","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.ec9a0b5a-55ae-4096-8dcf-bb297acf7805","sourceRef":"2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423"},{"targetRef":"2025.47043a08-3af8-43a7-9282-e01bcbf9b736","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.91de833e-dc58-4b5b-8365-1a1d691e67ec","sourceRef":"2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.bc1fac3a-8722-417d-8b9c-c2214775a3d7"},{"startQuantity":1,"outgoing":["2027.1721a516-1161-4ae5-8452-************"],"incoming":["2027.3b729580-9c2a-4e6a-b4b6-93fe294e447d"],"default":"2027.1721a516-1161-4ae5-8452-************","extensionElements":{"nodeVisualInfo":[{"width":95,"x":209,"y":177,"declaredType":"TNodeVisualInfo","height":70}]},"name":"init Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.6d30e2e5-1071-4f8a-8a6c-736e5911e306","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.stepLog = {};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT06;\r\n\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT06;\r\ntw.local.odcRequest.appInfo.appID = tw.system.processInstance.id;\r\ntw.local.odcRequest.appInfo.instanceID=  tw.local.odcRequest.requestNo;\r\n\r\ntw.local.actionConditions = {};\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT06;\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT05;\r\n\r\n\r\n\/*Visibilty Conditions*\/\r\n\/*Basic Details CV Visibility*\/\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nif(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest){\r\n\ttw.local.parentRequestNo = \"Readonly\";\r\n}\t\r\nelse{\r\n\ttw.local.parentRequestNo = \"None\";\r\n}\t\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\n}\r\nelse{\r\ntw.local.contractStageVIS = \"NONE\";\r\n}\r\n\r\ntw.local.errorPanelVIS=\"NONE\";"]}},{"targetRef":"2025.47043a08-3af8-43a7-9282-e01bcbf9b736","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Actions","declaredType":"sequenceFlow","id":"2027.1721a516-1161-4ae5-8452-************","sourceRef":"2025.6d30e2e5-1071-4f8a-8a6c-736e5911e306"},{"startQuantity":1,"outgoing":["2027.76bc57dc-a720-4f88-813d-6a813267b608"],"incoming":["2027.ec9a0b5a-55ae-4096-8dcf-bb297acf7805"],"default":"2027.76bc57dc-a720-4f88-813d-6a813267b608","extensionElements":{"nodeVisualInfo":[{"width":95,"x":736,"y":173,"declaredType":"TNodeVisualInfo","height":70}]},"name":"setting Status and substatus","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.19cca302-626e-4194-8e94-7f4acaf378ec","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.completeRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status= tw.epv.Status.completed;\r\n\ttw.local.odcRequest.appInfo.status=  tw.epv.Status.completed;\r\n}\t\r\n\r\ntw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;"]}},{"targetRef":"2025.9dcf4b4d-2ad4-43cf-82d3-7eab8bf80487","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To History","declaredType":"sequenceFlow","id":"2027.76bc57dc-a720-4f88-813d-6a813267b608","sourceRef":"2025.19cca302-626e-4194-8e94-7f4acaf378ec"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.031c57f3-ba26-4bca-8382-92953ebe3cee"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorPanelVIS","isCollection":false,"declaredType":"dataObject","id":"2056.7a1c7257-f352-4e16-8814-c8e2ba53a162"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestTypeVIS","isCollection":false,"declaredType":"dataObject","id":"2056.b32587b1-0291-47d9-852f-012917c80e60"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"contractStageVIS","isCollection":false,"declaredType":"dataObject","id":"2056.8bf41c9c-7731-46e0-85c0-7eb264b5bd6c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNo","isCollection":false,"declaredType":"dataObject","id":"2056.22096e3e-1f7c-486b-848b-db534711dd99"},{"outgoing":["2027.317998b0-0918-4767-8100-2272566f44bc"],"incoming":["2027.76bc57dc-a720-4f88-813d-6a813267b608"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":840,"y":173,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.317998b0-0918-4767-8100-2272566f44bc","name":"Update request state and status in DB","dataInputAssociation":[{"targetRef":"2055.a84d91ec-e620-4417-85c4-7dd7db58ff31","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}]},{"targetRef":"2055.c5194a72-2de4-483f-823c-47d5b98b572c","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.status"]}}]},{"targetRef":"2055.3974caa7-9f10-4f46-8275-17080a25476e","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.BasicDetails.requestState"]}}]},{"targetRef":"2055.b05449cf-c459-4405-809c-888b00e3e968","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.subStatus"]}}]},{"targetRef":"2055.b537f107-9e83-46e0-8979-6fe5796c7a7d","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.stepName"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.9dcf4b4d-2ad4-43cf-82d3-7eab8bf80487","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.8e0cea15-236a-4b79-86a8-f93756a4ac86"]}],"calledElement":"1.2cab04cd-6063-4a13-b148-ec9788e07bf4"},{"targetRef":"2025.ba6441f4-a47c-4ade-8473-06b48d21dada","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.317998b0-0918-4767-8100-2272566f44bc","sourceRef":"2025.9dcf4b4d-2ad4-43cf-82d3-7eab8bf80487"},{"startQuantity":1,"outgoing":["2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4"],"default":"2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4","extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":960,"y":173,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"name":"Audit ODC Request","dataInputAssociation":[{"targetRef":"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.7569d0d0-d902-4552-87e2-9d97948fa454","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.254cf8eb-2743-4c53-8c52-e51c8c22884e"]}],"calledElement":"1.7ee96dd0-834b-44cb-af41-b21585627e49"},{"incoming":["2027.fae5ba3f-13cb-4572-85f7-fed96225b1e8"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1400,"y":195,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}],"preAssignmentScript":[]},"name":"End","declaredType":"endEvent","id":"2025.608bdd27-f341-4c0e-8134-85457164a566"},{"outgoing":["2027.58065560-aaf1-43b2-8562-8c8f5030649f"],"incoming":["2027.4211557d-8ce3-4cc9-8d9f-07a658349f92","2027.317998b0-0918-4767-8100-2272566f44bc"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":1149,"y":174,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.58065560-aaf1-43b2-8562-8c8f5030649f","name":"Audit Request History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.epv.userRole.CACT06"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.ba6441f4-a47c-4ade-8473-06b48d21dada","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"outgoing":["2027.fae5ba3f-13cb-4572-85f7-fed96225b1e8","2027.c34e77f3-3403-4997-8ab1-25db39aa762e"],"incoming":["2027.58065560-aaf1-43b2-8562-8c8f5030649f"],"default":"2027.c34e77f3-3403-4997-8ab1-25db39aa762e","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1267,"y":193,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Audited","declaredType":"exclusiveGateway","id":"2025.8b945e85-e037-45cd-8957-98c5abf6d085"},{"outgoing":["2027.4211557d-8ce3-4cc9-8d9f-07a658349f92","2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7"],"incoming":["2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4"],"default":"2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1059,"y":193,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Audited?","declaredType":"exclusiveGateway","id":"2025.cca282dc-c044-434e-888a-53be11859d23"},{"incoming":["2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7","2027.c34e77f3-3403-4997-8ab1-25db39aa762e"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1147,"y":272,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.1ca3c9cb-2f92-4a45-83dc-69e12bcdaf8f"},{"targetRef":"2025.8b945e85-e037-45cd-8957-98c5abf6d085","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Audited","declaredType":"sequenceFlow","id":"2027.58065560-aaf1-43b2-8562-8c8f5030649f","sourceRef":"2025.ba6441f4-a47c-4ade-8473-06b48d21dada"},{"targetRef":"2025.608bdd27-f341-4c0e-8134-85457164a566","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.fae5ba3f-13cb-4572-85f7-fed96225b1e8","sourceRef":"2025.8b945e85-e037-45cd-8957-98c5abf6d085"},{"targetRef":"2025.cca282dc-c044-434e-888a-53be11859d23","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Audited?","declaredType":"sequenceFlow","id":"2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4","sourceRef":"2025.7569d0d0-d902-4552-87e2-9d97948fa454"},{"targetRef":"2025.ba6441f4-a47c-4ade-8473-06b48d21dada","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.4211557d-8ce3-4cc9-8d9f-07a658349f92","sourceRef":"2025.cca282dc-c044-434e-888a-53be11859d23"},{"targetRef":"2025.1ca3c9cb-2f92-4a45-83dc-69e12bcdaf8f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7","sourceRef":"2025.cca282dc-c044-434e-888a-53be11859d23"},{"targetRef":"2025.1ca3c9cb-2f92-4a45-83dc-69e12bcdaf8f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.c34e77f3-3403-4997-8ab1-25db39aa762e","sourceRef":"2025.8b945e85-e037-45cd-8957-98c5abf6d085"},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1185,"y":654,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Client-Side Human Service","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.1370adb0-4619-46c7-8572-ea805fb71a19"},{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.9b86a02d-4130-4bdb-88b6-32e404acc1da"],"isInterrupting":true,"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"extensionElements":{"default":["2027.9b86a02d-4130-4bdb-88b6-32e404acc1da"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"2025.28561790-af2e-4637-8aff-0b9ba776aa85"},{"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":700,"y":200,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.eecfb206-d555-4339-8d3c-77cdc0cf2791"},{"startQuantity":1,"incoming":["2027.47c209d7-131b-42a0-85c3-4562a44a9ea5"],"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"okbutton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7c59242f-88ea-41ee-8496-7f2034ea0bb1","optionName":"@bpmDefaultCreated","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"154f968a-9c94-4d7a-8a88-c486e6dbe161","optionName":"@label","value":"OK"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"dd109388-4768-4396-886d-cbe403b23c59"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":357,"y":94,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"name":"Coach","isForCompensation":false,"completionQuantity":1,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"id":"2025.a8e5ce81-4111-4083-884b-9143517170b0","cachePage":false},{"outgoing":["2027.47c209d7-131b-42a0-85c3-4562a44a9ea5"],"incoming":["2027.9b86a02d-4130-4bdb-88b6-32e404acc1da"],"default":"2027.47c209d7-131b-42a0-85c3-4562a44a9ea5","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":241,"y":201,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"2025.7d13e76b-d81b-4e5d-8b71-711ce9e67035"},{"targetRef":"2025.7d13e76b-d81b-4e5d-8b71-711ce9e67035","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.9b86a02d-4130-4bdb-88b6-32e404acc1da","sourceRef":"2025.28561790-af2e-4637-8aff-0b9ba776aa85"},{"targetRef":"2025.a8e5ce81-4111-4083-884b-9143517170b0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.47c209d7-131b-42a0-85c3-4562a44a9ea5","sourceRef":"2025.7d13e76b-d81b-4e5d-8b71-711ce9e67035"}],"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":356,"y":472,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Error","triggeredByEvent":true,"isForCompensation":false,"completionQuantity":1,"declaredType":"subProcess","id":"2025.ab80936c-bb1b-45a0-807e-5ee172105352"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"c7ffdca5-af0c-46f1-be8a-59eb32c2b5d3","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"b6f29f02-152c-457f-9b8c-312cb207bbdb","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"ACT06 -Print Remittance Letter","declaredType":"globalUserTask","id":"1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9","ioSpecification":{"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.e8fd55db-313a-4d5b-8257-9e17cb650c50"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"f68bc80e-4d60-4e79-8b8f-2e2002c603c8","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"0451981c-4602-4a46-8814-4054938f757b","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"b66c22e2-22fb-4127-86a5-aa84171aedc6","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.340b122c-2fdf-400c-822c-b0c52fb7b022","epvProcessLinkId":"bec1b314-50cf-4d83-889f-9fcd049898b9","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"0d239441-d4d4-49c2-895c-0d218cd5e022","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"ab6f7f12-6d7c-490d-8efe-a3b95264bd0d","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.062854b5-6513-4da8-84ab-0126f90e550d","epvProcessLinkId":"99e439cd-3e1d-4991-8dd7-9b145a414c93","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"_bb5c9500-d37d-4108-9e62-25b291a7340d"}],"outputSet":[{"id":"_b376490f-d110-44d7-b4cd-13d09b3625ce"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.8cdb34c3-63b3-4765-83e8-d4f953fdc147"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"0da7c34d-eb3e-40fd-8015-9ba5a08c53ce"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8cdb34c3-63b3-4765-83e8-d4f953fdc147</processParameterId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>764913f1-da84-42d7-8cdd-d670e44cd658</guid>
            <versionId>6f2d1c9c-4af4-4be7-9014-eb9166be5ae4</versionId>
        </processParameter>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bc1fac3a-8722-417d-8b9c-c2214775a3d7</processVariableId>
            <description isNull="true" />
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0940dd38-635d-41dd-98dd-4eef299e1c98</guid>
            <versionId>3b5e7ba4-2b74-43db-8503-366df5b35c03</versionId>
        </processVariable>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.031c57f3-ba26-4bca-8382-92953ebe3cee</processVariableId>
            <description isNull="true" />
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7c3f697b-56a8-4234-9bd5-855c1a9c3cb8</guid>
            <versionId>b5c06d46-4f72-4b6d-a5a7-83587e90cb8f</versionId>
        </processVariable>
        <processVariable name="errorPanelVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7a1c7257-f352-4e16-8814-c8e2ba53a162</processVariableId>
            <description isNull="true" />
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f5237e30-94b8-40c2-a211-9355dedd3cb7</guid>
            <versionId>16c42c8d-44ec-4c69-8a32-d31bfabc566c</versionId>
        </processVariable>
        <processVariable name="requestTypeVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b32587b1-0291-47d9-852f-012917c80e60</processVariableId>
            <description isNull="true" />
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>90876127-3d59-4e0f-adfb-23b1e11ea166</guid>
            <versionId>f55d7060-1be8-43db-8683-1399c9eeb1df</versionId>
        </processVariable>
        <processVariable name="contractStageVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8bf41c9c-7731-46e0-85c0-7eb264b5bd6c</processVariableId>
            <description isNull="true" />
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d5266e44-daa2-426b-84f9-b4efef5d74b2</guid>
            <versionId>27ba0133-4b2b-4758-97a7-8a100d520832</versionId>
        </processVariable>
        <processVariable name="parentRequestNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.22096e3e-1f7c-486b-848b-db534711dd99</processVariableId>
            <description isNull="true" />
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>500a06f9-fd4f-4dbf-8647-e240d260dde6</guid>
            <versionId>7a1a44e1-7023-4cbb-a657-74698feea455</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7569d0d0-d902-4552-87e2-9d97948fa454</processItemId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <name>Audit ODC Request</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d4da5bb7-7d1e-459b-8116-4f92f463bf37</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:34c6</guid>
            <versionId>2280f460-280d-408a-9dfb-cdc7a1a80974</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d4da5bb7-7d1e-459b-8116-4f92f463bf37</subProcessId>
                <attachedProcessRef>/1.7ee96dd0-834b-44cb-af41-b21585627e49</attachedProcessRef>
                <guid>d702a9ac-f371-409a-b0a4-7de68768e836</guid>
                <versionId>57fdbc2f-2246-49d9-ae8d-5603e877364b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9dcf4b4d-2ad4-43cf-82d3-7eab8bf80487</processItemId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <name>Update request state and status in DB</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d65b334e-2177-40b3-94d6-52bc75684648</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aff098473ecd546d:1d42df0a:18b1b2b8841:-76a1</guid>
            <versionId>3da4c1ee-a0b9-4ae7-a90e-f81374eee057</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d65b334e-2177-40b3-94d6-52bc75684648</subProcessId>
                <attachedProcessRef>/1.2cab04cd-6063-4a13-b148-ec9788e07bf4</attachedProcessRef>
                <guid>6b3be3da-c87c-469f-ba1f-8d2c2dfdfe77</guid>
                <versionId>11708446-64d5-439f-b6c1-f5ad03509727</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ba6441f4-a47c-4ade-8473-06b48d21dada</processItemId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <name>Audit Request History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.2bc4a51b-b251-4fab-a2a5-d5d7e34551e1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:34c7</guid>
            <versionId>4fc1cb54-e122-463e-ac9b-667b56ca44a9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.2bc4a51b-b251-4fab-a2a5-d5d7e34551e1</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>6b7c68f2-5225-4c4f-ab26-9bde0e36819c</guid>
                <versionId>f0fc5003-8490-4c62-879c-5eea50717dbb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.dd8b765e-4b65-451e-93e7-32debc406e8d</processItemId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.250a0d34-1ccb-4fdd-a299-a6f6451183cc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a5a0677488e996f:-34e0d142:1899236511b:-5776</guid>
            <versionId>a0ece83c-4833-4094-94e2-94b5785c8975</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.250a0d34-1ccb-4fdd-a299-a6f6451183cc</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>80c2f81f-fe29-4c41-9634-7b8a95405a00</guid>
                <versionId>5152ec59-322c-41fb-a7f5-9a61ddee199c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.974f7094-9186-49c3-82f6-db4f1cc961d5</processItemId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.2f5a7ac2-07be-414f-b4dc-aff5550cd9bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a5a0677488e996f:-34e0d142:1899236511b:-5775</guid>
            <versionId>bdad33e0-0133-4432-a3c9-c17b8d814709</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1370adb0-4619-46c7-8572-ea805fb71a19</processItemId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <name>Client-Side Human Service</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.837822fc-d098-4387-9b43-cd310c7b09df</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2340c4cc899e0880:-54556756:19341292354:-3da7</guid>
            <versionId>c678dd42-d9c4-48d9-9e6a-2216df66aa6d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.837822fc-d098-4387-9b43-cd310c7b09df</subProcessId>
                <attachedProcessRef isNull="true" />
                <guid>c30fd25b-63ef-472d-a80b-9fd309881597</guid>
                <versionId>6dc04b94-ca42-4a25-b288-25cbb35a903d</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.513936bc-f02d-43ac-bbfa-8349d15ffb15</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>ffde8a42-6079-4c08-9682-b9799d0bbebd</guid>
            <versionId>03453737-e4dc-4cc4-9645-897c5b186273</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.c64cfc56-d139-44e1-b4a1-5cb2121567d3</epvProcessLinkId>
            <epvId>/21.340b122c-2fdf-400c-822c-b0c52fb7b022</epvId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>796dcead-333c-4606-9f2b-a4b006e8b522</guid>
            <versionId>051d08ec-23c3-4b3b-ae05-3ea96de0803c</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.144a13dc-59db-473b-a735-0ef28b2e0010</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>1c3c72d3-4785-42e3-9b54-7ff0f0ccabb6</guid>
            <versionId>320a2523-84cf-4205-8310-7887ff2f9f54</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f2ec83ad-bab3-4c91-8cce-5ba7140ccc09</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>9cc52728-7c11-420c-8d1f-303d9a7a013a</guid>
            <versionId>3315fb76-a673-4ec4-a11f-1368e88a54b4</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.4cf7596c-5aa8-49cd-baba-28e591637493</epvProcessLinkId>
            <epvId>/21.062854b5-6513-4da8-84ab-0126f90e550d</epvId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>131db31e-138c-4d2e-8273-3aa4a08e0a14</guid>
            <versionId>39e45b37-1cd0-4f5b-8373-4902fe35b539</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.96aa6104-6d3e-4a2f-8638-60f2571ee5f2</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>42855da2-27ba-4c98-8e06-d576eaf7d780</guid>
            <versionId>8ef37b46-3a0c-4092-9041-8a3010f7a75d</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.a2953d6a-2b8e-41e8-8bcc-377345aa1c7d</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>07f4fedb-02f7-4627-a9bc-f8e9186f343e</guid>
            <versionId>e6606233-843f-44c8-95d6-850976c3dc86</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.85785557-ad37-4796-95dc-4de57a0f02cd</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <guid>63e5570f-150d-4e10-96e5-6991bb18fea0</guid>
            <versionId>34c9fe5b-d3d9-45f7-9378-6d84eaf58edd</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.974f7094-9186-49c3-82f6-db4f1cc961d5</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="0da7c34d-eb3e-40fd-8015-9ba5a08c53ce" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns16:globalUserTask name="ACT06 -Print Remittance Letter" id="1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9">
                    <ns16:documentation />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation id="b6f29f02-152c-457f-9b8c-312cb207bbdb">
                            <ns16:startEvent name="Start" id="c711e944-e5e7-42e7-9c36-d164f20346a4">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="113" y="200" width="24" height="24" color="#F8F8F8" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.3b729580-9c2a-4e6a-b4b6-93fe294e447d</ns16:outgoing>
                            </ns16:startEvent>
                            <ns3:formTask name="Print Remittance Letter" id="2025.47043a08-3af8-43a7-9282-e01bcbf9b736">
                                <ns16:extensionElements>
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns13:nodeVisualInfo x="350" y="177" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.ec0f2317-c6d2-49db-8b83-5502e37e791c</ns16:incoming>
                                <ns16:incoming>2027.91de833e-dc58-4b5b-8365-1a1d691e67ec</ns16:incoming>
                                <ns16:incoming>2027.1721a516-1161-4ae5-8452-************</ns16:incoming>
                                <ns16:outgoing>2027.4acf17b7-6d32-48f5-8b42-1a393335baca</ns16:outgoing>
                                <ns16:outgoing>2027.5ca616ef-4aec-4bf9-829c-d52930279bf0</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>19126455-141e-4854-8915-dae81b9a9aed</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>b7c6bdaa-49b3-45cb-89d5-83efd0dd5321</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d6d7381f-e367-4dca-8402-224357ffb741</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>692b5655-1d98-4c46-811f-ea2b2e2250a5</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>10b28994-74a1-404d-8e3d-ebd43e784b89</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>bb8b2aa9-7124-4604-8ff9-25ca0af41ca8</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>3ab1a850-e7a8-4976-88e6-afeb53570538</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>24f03d4e-042a-4842-8a8b-5ad4327b56f9</ns19:id>
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a7e82150-82f3-4ae7-86df-e5e73e570ed4</ns19:id>
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d219e8c6-97c1-4a1e-83ee-d2ccb7107606</ns19:id>
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>03e7869b-a2d3-461d-8661-1feea87732b9</ns19:id>
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6b26645d-7028-4fc7-8534-24d99bdd9112</ns19:id>
                                                    <ns19:optionName>errorPanelVIS</ns19:optionName>
                                                    <ns19:value>tw.local.errorPanelVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d5079582-23cb-4bc0-8d12-c356d99dc4dd</ns19:id>
                                                    <ns19:optionName>returnReasonVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>be75abab-d028-4b72-8ed8-2f010b977e6a</ns19:id>
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>03d495b7-cc53-43e8-8752-ea71692962a4</ns19:id>
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a81bda6c-aa45-46d6-8019-17a46202f030</ns19:id>
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>2daa6aa6-0808-47b3-83c6-978cfc300a2f</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>77c72096-597d-48f4-875b-76f3594ed6ae</ns19:id>
                                                        <ns19:layoutItemId>Tabs1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>3f984588-4ab2-44af-8e6e-bef60353a08f</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Tabs</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>793ca8e8-d0e6-4b55-86c8-b11ec9667a7e</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>145baff7-d18b-4219-8f83-55b4ccff3205</ns19:id>
                                                            <ns19:optionName>@width</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"99%"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>015fbc4f-c318-47aa-8ee3-a3990cca8182</ns19:id>
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>1d520a3e-c745-45ad-88d7-9da57c271567</ns19:id>
                                                            <ns19:optionName>tabsStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"S"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>1357eccf-5c4b-4266-81bb-bd7d6b2b818d</ns19:id>
                                                            <ns19:optionName>colorStyle</ns19:optionName>
                                                            <ns19:value>P</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>ccc6cb85-fba4-4adc-80dd-07cf6959de84</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>7e68018f-d537-4584-80bb-4e7761e97a48</ns19:id>
                                                                <ns19:layoutItemId>Basic_Details_CV1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>86c78cef-1f3c-43f0-8d41-66492a5f0110</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7416abc6-b699-49d1-89dc-a33809e26d88</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9412dc28-9a17-4a0f-8bbe-574b5beb52d4</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e95c2e32-07bd-456a-8260-55b9e53265a1</ns19:id>
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5eebd313-97c1-4458-8339-9e14d0c9a108</ns19:id>
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>15784a0d-7edf-4b76-8583-405cee775daa</ns19:id>
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    <ns19:value>tw.local.parentRequestNo</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7474d1bf-6be3-4be3-8f71-82e15060c2a9</ns19:id>
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    <ns19:value>tw.local.contractStageVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1e148971-3bcf-4ba3-87c4-fa640652ce54</ns19:id>
                                                                    <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>268b2c33-9af0-43d8-8d8b-78436c49c8d6</ns19:id>
                                                                <ns19:layoutItemId>Customer_Information_cv1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>077e93ed-810d-4fd4-8bb2-d21b04ab721c</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Customer Info.</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ef568cc0-6dcb-4d0c-8355-3623664ea331</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>280f4bba-ac2e-4dc4-8bd8-36fff08d794e</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f97037e4-33cf-4274-8209-a99159f496d0</ns19:id>
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1feb3688-737b-406a-82ea-f83c5d186cb9</ns19:id>
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>756ef8b5-02eb-4a55-86a0-8d426791d367</ns19:id>
                                                                <ns19:layoutItemId>Financial_Details_Branch_CV1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>467a2fee-d5bf-45e8-827a-6fc3cc11e4d7</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Financial Details - Branch</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3546fd16-7aa9-4707-80e5-125e19cbde07</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1c54b0ca-9183-4d28-8e19-24e7e3266b19</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>46ae30b7-08da-455a-824e-6cc80387ae48</ns19:id>
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>afef5bf0-d3c0-4d17-81f9-4c7b6e5e71f8</ns19:id>
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>69454a2c-d20c-47c8-8a17-f12204a38ff9</ns19:id>
                                                                <ns19:layoutItemId>FC_Collections_CV1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>2a7f5c09-69c5-4a8e-8e7b-ffbbb6325483</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>FC Collections</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9b27ab66-0c2c-4042-8fd8-a7711fecec78</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>41d996e3-788a-4c4d-845f-cdfa15650f53</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9d89440c-b073-4e6b-822e-585f1a5d6d50</ns19:id>
                                                                    <ns19:optionName>FCVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>10dbbe11-9cc5-452c-8589-02d38ffedfed</ns19:id>
                                                                    <ns19:optionName>collectionCurrencyVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ea312100-a878-499b-8fca-ef7b593a421d</ns19:id>
                                                                    <ns19:optionName>negotiatedExchangeRateVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7d13ddb7-5756-42e6-8232-ef50196c7998</ns19:id>
                                                                    <ns19:optionName>addBtnVIS</ns19:optionName>
                                                                    <ns19:value>NONE</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>398ece67-f0ee-4a63-83d1-6e952a0818b2</ns19:id>
                                                                    <ns19:optionName>retrieveBtnVis</ns19:optionName>
                                                                    <ns19:value>NONE</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9e539ade-b4dd-4186-84bd-27921c95af02</ns19:id>
                                                                    <ns19:optionName>activityType</ns19:optionName>
                                                                    <ns19:value>read</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5884090c-fcd3-4526-80f2-67a90c8da501</ns19:id>
                                                                    <ns19:optionName>customerCif</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.cif</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.FcCollections</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>3c5e341b-3686-4c12-8e9a-d528f34d12b6</ns19:id>
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>3173f6b9-93e6-44a3-8764-688be8430539</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Attachments</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>62ca54db-8098-4c14-8108-65867182e5a0</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>98a7fb90-00ac-4bd3-8fca-5a2f0bd8a109</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9aa0516e-cf6a-43c8-8fff-cd54f8d50d90</ns19:id>
                                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7616841b-7960-417b-80a0-9fff14d8593a</ns19:id>
                                                                    <ns19:optionName>remittanceLetterPath</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.folderPath</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>2eb97755-41d7-4ac9-8c10-2de66dcb5f60</ns19:id>
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>96ed0cce-3a0a-4982-88db-7568c6f6b3c8</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>History</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c011e63c-3608-4661-85f2-93c7d0e759e6</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>65697140-ba06-461f-895d-0c0c22921883</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:sequenceFlow sourceRef="c711e944-e5e7-42e7-9c36-d164f20346a4" targetRef="2025.6d30e2e5-1071-4f8a-8a6c-736e5911e306" name="To Print Remittance Letter" id="2027.3b729580-9c2a-4e6a-b4b6-93fe294e447d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.47043a08-3af8-43a7-9282-e01bcbf9b736" targetRef="2025.7863ccfc-912e-41f2-8bd5-cda57d9a4fae" name="To End" id="2027.4acf17b7-6d32-48f5-8b42-1a393335baca">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="8c873709-8964-4a6d-8676-5e876f1dad40">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.e890f783-e450-46c7-8c59-a1811d24226a">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="374" y="46" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:default>2027.ec0f2317-c6d2-49db-8b83-5502e37e791c</ns3:default>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.5ca616ef-4aec-4bf9-829c-d52930279bf0</ns16:incoming>
                                <ns16:outgoing>2027.ec0f2317-c6d2-49db-8b83-5502e37e791c</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.47043a08-3af8-43a7-9282-e01bcbf9b736" targetRef="2025.e890f783-e450-46c7-8c59-a1811d24226a" name="To Postpone" id="2027.5ca616ef-4aec-4bf9-829c-d52930279bf0">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="c19ca4e6-0d9c-4ff8-80dc-66d9c73c8ad4">
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.e890f783-e450-46c7-8c59-a1811d24226a" targetRef="2025.47043a08-3af8-43a7-9282-e01bcbf9b736" name="To Print Remittance Letter" id="2027.ec0f2317-c6d2-49db-8b83-5502e37e791c">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.bc31c553-d031-43c8-83a1-0fbff275a123" name="Validation Script" id="2025.7863ccfc-912e-41f2-8bd5-cda57d9a4fae">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="528" y="176" width="95" height="70" color="#95D087" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4acf17b7-6d32-48f5-8b42-1a393335baca</ns16:incoming>
                                <ns16:outgoing>2027.bc31c553-d031-43c8-83a1-0fbff275a123</ns16:outgoing>
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false;&#xD;
 &#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");	&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
	</ns16:script>
                            </ns16:scriptTask>
                            <ns16:exclusiveGateway default="2027.91de833e-dc58-4b5b-8365-1a1d691e67ec" name="valid?" id="2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="660" y="195" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.bc31c553-d031-43c8-83a1-0fbff275a123</ns16:incoming>
                                <ns16:outgoing>2027.ec9a0b5a-55ae-4096-8dcf-bb297acf7805</ns16:outgoing>
                                <ns16:outgoing>2027.91de833e-dc58-4b5b-8365-1a1d691e67ec</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.7863ccfc-912e-41f2-8bd5-cda57d9a4fae" targetRef="2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423" name="To valid?" id="2027.bc31c553-d031-43c8-83a1-0fbff275a123">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423" targetRef="2025.19cca302-626e-4194-8e94-7f4acaf378ec" name="yes" id="2027.ec9a0b5a-55ae-4096-8dcf-bb297acf7805">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.7f2b8bfc-ae5b-4e53-863e-436ed9908423" targetRef="2025.47043a08-3af8-43a7-9282-e01bcbf9b736" name="no" id="2027.91de833e-dc58-4b5b-8365-1a1d691e67ec">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.bc1fac3a-8722-417d-8b9c-c2214775a3d7" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.1721a516-1161-4ae5-8452-************" name="init Script" id="2025.6d30e2e5-1071-4f8a-8a6c-736e5911e306">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="209" y="177" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.3b729580-9c2a-4e6a-b4b6-93fe294e447d</ns16:incoming>
                                <ns16:outgoing>2027.1721a516-1161-4ae5-8452-************</ns16:outgoing>
                                <ns16:script>tw.local.odcRequest.stepLog = {};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT06;&#xD;
&#xD;
tw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT06;&#xD;
tw.local.odcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
tw.local.odcRequest.appInfo.instanceID=  tw.local.odcRequest.requestNo;&#xD;
&#xD;
tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;&#xD;
tw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT06;&#xD;
tw.local.actionConditions.userRole= tw.local.odcRequest.initiator;&#xD;
tw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT05;&#xD;
&#xD;
&#xD;
/*Visibilty Conditions*/&#xD;
/*Basic Details CV Visibility*/&#xD;
///////////////////////////////&#xD;
if(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest){&#xD;
	tw.local.parentRequestNo = "Readonly";&#xD;
}	&#xD;
else{&#xD;
	tw.local.parentRequestNo = "None";&#xD;
}	&#xD;
if(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){&#xD;
	tw.local.contractStageVIS = "READONLY";&#xD;
}&#xD;
else{&#xD;
tw.local.contractStageVIS = "NONE";&#xD;
}&#xD;
&#xD;
tw.local.errorPanelVIS="NONE";</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.6d30e2e5-1071-4f8a-8a6c-736e5911e306" targetRef="2025.47043a08-3af8-43a7-9282-e01bcbf9b736" name="To Get Actions" id="2027.1721a516-1161-4ae5-8452-************">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.76bc57dc-a720-4f88-813d-6a813267b608" name="setting Status and substatus" id="2025.19cca302-626e-4194-8e94-7f4acaf378ec">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="736" y="173" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.ec9a0b5a-55ae-4096-8dcf-bb297acf7805</ns16:incoming>
                                <ns16:outgoing>2027.76bc57dc-a720-4f88-813d-6a813267b608</ns16:outgoing>
                                <ns16:script>if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.completeRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status= tw.epv.Status.completed;&#xD;
	tw.local.odcRequest.appInfo.status=  tw.epv.Status.completed;&#xD;
}	&#xD;
&#xD;
tw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.19cca302-626e-4194-8e94-7f4acaf378ec" targetRef="2025.9dcf4b4d-2ad4-43cf-82d3-7eab8bf80487" name="To History" id="2027.76bc57dc-a720-4f88-813d-6a813267b608">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.031c57f3-ba26-4bca-8382-92953ebe3cee" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorPanelVIS" id="2056.7a1c7257-f352-4e16-8814-c8e2ba53a162" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestTypeVIS" id="2056.b32587b1-0291-47d9-852f-012917c80e60" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="contractStageVIS" id="2056.8bf41c9c-7731-46e0-85c0-7eb264b5bd6c" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentRequestNo" id="2056.22096e3e-1f7c-486b-848b-db534711dd99" />
                            <ns16:callActivity calledElement="1.2cab04cd-6063-4a13-b148-ec9788e07bf4" default="2027.317998b0-0918-4767-8100-2272566f44bc" name="Update request state and status in DB" id="2025.9dcf4b4d-2ad4-43cf-82d3-7eab8bf80487">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="840" y="173" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.76bc57dc-a720-4f88-813d-6a813267b608</ns16:incoming>
                                <ns16:outgoing>2027.317998b0-0918-4767-8100-2272566f44bc</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.a84d91ec-e620-4417-85c4-7dd7db58ff31</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.c5194a72-2de4-483f-823c-47d5b98b572c</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.status</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.3974caa7-9f10-4f46-8275-17080a25476e</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.BasicDetails.requestState</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.b05449cf-c459-4405-809c-888b00e3e968</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.subStatus</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.b537f107-9e83-46e0-8979-6fe5796c7a7d</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.stepName</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.8e0cea15-236a-4b79-86a8-f93756a4ac86</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.9dcf4b4d-2ad4-43cf-82d3-7eab8bf80487" targetRef="2025.ba6441f4-a47c-4ade-8473-06b48d21dada" name="To End" id="2027.317998b0-0918-4767-8100-2272566f44bc">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.7ee96dd0-834b-44cb-af41-b21585627e49" default="2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4" name="Audit ODC Request" id="2025.7569d0d0-d902-4552-87e2-9d97948fa454">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="960" y="173" width="95" height="70" />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.afad40c5-a38b-475c-8154-b4dabd94b6fe</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.254cf8eb-2743-4c53-8c52-e51c8c22884e</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:endEvent name="End" id="2025.608bdd27-f341-4c0e-8134-85457164a566">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1400" y="195" width="24" height="24" color="#F8F8F8" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.fae5ba3f-13cb-4572-85f7-fed96225b1e8</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.58065560-aaf1-43b2-8562-8c8f5030649f" name="Audit Request History" id="2025.ba6441f4-a47c-4ade-8473-06b48d21dada">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1149" y="174" width="95" height="70" />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4211557d-8ce3-4cc9-8d9f-07a658349f92</ns16:incoming>
                                <ns16:incoming>2027.317998b0-0918-4767-8100-2272566f44bc</ns16:incoming>
                                <ns16:outgoing>2027.58065560-aaf1-43b2-8562-8c8f5030649f</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.epv.userRole.CACT06</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:exclusiveGateway default="2027.c34e77f3-3403-4997-8ab1-25db39aa762e" name="Audited" id="2025.8b945e85-e037-45cd-8957-98c5abf6d085">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1267" y="193" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.58065560-aaf1-43b2-8562-8c8f5030649f</ns16:incoming>
                                <ns16:outgoing>2027.fae5ba3f-13cb-4572-85f7-fed96225b1e8</ns16:outgoing>
                                <ns16:outgoing>2027.c34e77f3-3403-4997-8ab1-25db39aa762e</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:exclusiveGateway default="2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7" name="Audited?" id="2025.cca282dc-c044-434e-888a-53be11859d23">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1059" y="193" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4</ns16:incoming>
                                <ns16:outgoing>2027.4211557d-8ce3-4cc9-8d9f-07a658349f92</ns16:outgoing>
                                <ns16:outgoing>2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.1ca3c9cb-2f92-4a45-83dc-69e12bcdaf8f">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1147" y="272" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7</ns16:incoming>
                                <ns16:incoming>2027.c34e77f3-3403-4997-8ab1-25db39aa762e</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.ba6441f4-a47c-4ade-8473-06b48d21dada" targetRef="2025.8b945e85-e037-45cd-8957-98c5abf6d085" name="To Audited" id="2027.58065560-aaf1-43b2-8562-8c8f5030649f">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.8b945e85-e037-45cd-8957-98c5abf6d085" targetRef="2025.608bdd27-f341-4c0e-8134-85457164a566" name="Yes" id="2027.fae5ba3f-13cb-4572-85f7-fed96225b1e8">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.7569d0d0-d902-4552-87e2-9d97948fa454" targetRef="2025.cca282dc-c044-434e-888a-53be11859d23" name="To Audited?" id="2027.a6410e83-16a4-43a5-86c9-3bccaa71edd4">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.cca282dc-c044-434e-888a-53be11859d23" targetRef="2025.ba6441f4-a47c-4ade-8473-06b48d21dada" name="Yes" id="2027.4211557d-8ce3-4cc9-8d9f-07a658349f92">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.cca282dc-c044-434e-888a-53be11859d23" targetRef="2025.1ca3c9cb-2f92-4a45-83dc-69e12bcdaf8f" name="No" id="2027.b26cb1ef-515c-4576-8634-dbca3cd81cd7">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.8b945e85-e037-45cd-8957-98c5abf6d085" targetRef="2025.1ca3c9cb-2f92-4a45-83dc-69e12bcdaf8f" name="No" id="2027.c34e77f3-3403-4997-8ab1-25db39aa762e">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity name="Client-Side Human Service" id="2025.1370adb0-4619-46c7-8572-ea805fb71a19">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1185" y="654" width="95" height="70" />
                                    <ns4:activityType>CalledProcess</ns4:activityType>
                                </ns16:extensionElements>
                            </ns16:callActivity>
                            <ns16:subProcess triggeredByEvent="true" name="Error" id="2025.ab80936c-bb1b-45a0-807e-5ee172105352">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="356" y="472" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="2025.28561790-af2e-4637-8aff-0b9ba776aa85">
                                    <ns16:extensionElements>
                                        <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                        <ns3:default>2027.9b86a02d-4130-4bdb-88b6-32e404acc1da</ns3:default>
                                    </ns16:extensionElements>
                                    <ns16:outgoing>2027.9b86a02d-4130-4bdb-88b6-32e404acc1da</ns16:outgoing>
                                    <ns16:errorEventDefinition>
                                        <ns16:extensionElements>
                                            <ns4:errorEventSettings>
                                                <ns4:catchAll>true</ns4:catchAll>
                                            </ns4:errorEventSettings>
                                        </ns16:extensionElements>
                                    </ns16:errorEventDefinition>
                                </ns16:startEvent>
                                <ns16:intermediateThrowEvent name="Stay on page" id="2025.eecfb206-d555-4339-8d3c-77cdc0cf2791">
                                    <ns16:extensionElements>
                                        <ns13:nodeVisualInfo x="700" y="200" width="24" height="24" />
                                        <ns3:navigationInstructions>
                                            <ns3:targetType>Default</ns3:targetType>
                                        </ns3:navigationInstructions>
                                    </ns16:extensionElements>
                                    <ns3:stayOnPageEventDefinition />
                                </ns16:intermediateThrowEvent>
                                <ns3:formTask name="Coach" id="2025.a8e5ce81-4111-4083-884b-9143517170b0">
                                    <ns16:extensionElements>
                                        <ns13:nodeVisualInfo x="357" y="94" width="95" height="70" />
                                        <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    </ns16:extensionElements>
                                    <ns16:incoming>2027.47c209d7-131b-42a0-85c3-4562a44a9ea5</ns16:incoming>
                                    <ns3:formDefinition>
                                        <ns19:coachDefinition>
                                            <ns19:layout>
                                                <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                    <ns19:id>dd109388-4768-4396-886d-cbe403b23c59</ns19:id>
                                                    <ns19:layoutItemId>okbutton</ns19:layoutItemId>
                                                    <ns19:configData>
                                                        <ns19:id>7c59242f-88ea-41ee-8496-7f2034ea0bb1</ns19:id>
                                                        <ns19:optionName>@bpmDefaultCreated</ns19:optionName>
                                                        <ns19:value>true</ns19:value>
                                                    </ns19:configData>
                                                    <ns19:configData>
                                                        <ns19:id>154f968a-9c94-4d7a-8a88-c486e6dbe161</ns19:id>
                                                        <ns19:optionName>@label</ns19:optionName>
                                                        <ns19:value>OK</ns19:value>
                                                    </ns19:configData>
                                                    <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                                </ns19:layoutItem>
                                            </ns19:layout>
                                        </ns19:coachDefinition>
                                    </ns3:formDefinition>
                                </ns3:formTask>
                                <ns16:exclusiveGateway default="2027.47c209d7-131b-42a0-85c3-4562a44a9ea5" name="Exclusive Gateway" id="2025.7d13e76b-d81b-4e5d-8b71-711ce9e67035">
                                    <ns16:extensionElements>
                                        <ns13:nodeVisualInfo x="241" y="201" width="32" height="32" />
                                    </ns16:extensionElements>
                                    <ns16:incoming>2027.9b86a02d-4130-4bdb-88b6-32e404acc1da</ns16:incoming>
                                    <ns16:outgoing>2027.47c209d7-131b-42a0-85c3-4562a44a9ea5</ns16:outgoing>
                                </ns16:exclusiveGateway>
                                <ns16:sequenceFlow sourceRef="2025.28561790-af2e-4637-8aff-0b9ba776aa85" targetRef="2025.7d13e76b-d81b-4e5d-8b71-711ce9e67035" name="To Exclusive Gateway" id="2027.9b86a02d-4130-4bdb-88b6-32e404acc1da">
                                    <ns16:extensionElements>
                                        <ns13:linkVisualInfo>
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            <ns13:showLabel>false</ns13:showLabel>
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        </ns13:linkVisualInfo>
                                    </ns16:extensionElements>
                                </ns16:sequenceFlow>
                                <ns16:sequenceFlow sourceRef="2025.7d13e76b-d81b-4e5d-8b71-711ce9e67035" targetRef="2025.a8e5ce81-4111-4083-884b-9143517170b0" name="To Coach" id="2027.47c209d7-131b-42a0-85c3-4562a44a9ea5">
                                    <ns16:extensionElements>
                                        <ns13:linkVisualInfo>
                                            <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            <ns13:showLabel>true</ns13:showLabel>
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        </ns13:linkVisualInfo>
                                    </ns16:extensionElements>
                                </ns16:sequenceFlow>
                            </ns16:subProcess>
                            <ns3:htmlHeaderTag id="c7ffdca5-af0c-46f1-be8a-59eb32c2b5d3">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="f68bc80e-4d60-4e79-8b8f-2e2002c603c8" />
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="0451981c-4602-4a46-8814-4054938f757b" />
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="b66c22e2-22fb-4127-86a5-aa84171aedc6" />
                                <ns3:epvProcessLinkRef epvId="21.340b122c-2fdf-400c-822c-b0c52fb7b022" epvProcessLinkId="bec1b314-50cf-4d83-889f-9fcd049898b9" />
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="0d239441-d4d4-49c2-895c-0d218cd5e022" />
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="ab6f7f12-6d7c-490d-8efe-a3b95264bd0d" />
                                <ns3:epvProcessLinkRef epvId="21.062854b5-6513-4da8-84ab-0126f90e550d" epvProcessLinkId="99e439cd-3e1d-4991-8dd7-9b145a414c93" />
                            </ns3:epvProcessLinks>
                            <ns3:localizationResourceLinks>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    <ns3:id>69.e8fd55db-313a-4d5b-8257-9e17cb650c50</ns3:id>
                                </ns3:resourceRef>
                            </ns3:localizationResourceLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.8cdb34c3-63b3-4765-83e8-d4f953fdc147">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false" />
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:inputSet id="_bb5c9500-d37d-4108-9e62-25b291a7340d" />
                        <ns16:outputSet id="_b376490f-d110-44d7-b4cd-13d09b3625ce" />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6d6e091a-bd7a-4329-b6b7-1296ca40b287</processLinkId>
            <processId>1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.974f7094-9186-49c3-82f6-db4f1cc961d5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.dd8b765e-4b65-451e-93e7-32debc406e8d</toProcessItemId>
            <guid>eb566c71-13df-4c6f-b400-fbc28cefbbea</guid>
            <versionId>03bf8f16-cc2f-42f6-8f6a-be3c674e1acf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.974f7094-9186-49c3-82f6-db4f1cc961d5</fromProcessItemId>
            <toProcessItemId>2025.dd8b765e-4b65-451e-93e7-32debc406e8d</toProcessItemId>
        </link>
    </process>
</teamworks>

