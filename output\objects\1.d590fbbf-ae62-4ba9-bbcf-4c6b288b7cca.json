{"id": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "versionId": "e3330927-d39f-472a-8e13-00fffb123dde", "name": "get search Criteria List", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "name": "get search Criteria List", "lastModified": "1697049915209", "lastModifiedBy": "heba", "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.b9758410-f877-485c-84f1-bd6b1038e639", "2025.b9758410-f877-485c-84f1-bd6b1038e639"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e48", "versionId": "e3330927-d39f-472a-8e13-00fffb123dde", "dependencySummary": "<dependencySummary id=\"bpdid:aff098473ecd546d:1d42df0a:18b1b2b8841:2b27\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"5a489bae-f3b2-4e4e-8483-811a8ce22eb7\"},{\"incoming\":[\"264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e4a\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"6ac8db85-c4f0-4845-8436-04d472b352e4\"},{\"targetRef\":\"b9758410-f877-485c-84f1-bd6b1038e639\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22\",\"sourceRef\":\"5a489bae-f3b2-4e4e-8483-811a8ce22eb7\"},{\"startQuantity\":1,\"outgoing\":[\"264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee\"],\"incoming\":[\"2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":181,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"search Criteria\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"b9758410-f877-485c-84f1-bd6b1038e639\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.search =new tw.object.listOf.NameValuePair();\\r\\n\\r\\ntw.local.search[0]= new tw.object.NameValuePair();\\r\\ntw.local.search[0].name = \\\"CIF\\\";\\r\\ntw.local.search[0].value = \\\"cif\\\";\\r\\n\\r\\ntw.local.search[1]= new tw.object.NameValuePair();\\r\\ntw.local.search[1].name = \\\"Request Type \\u0646\\u0648\\u0639 \\u0627\\u0644\\u0637\\u0644\\u0628\\\";\\r\\ntw.local.search[1].value = \\\"requestType\\\";\\r\\n\\r\\ntw.local.search[2]= new tw.object.NameValuePair();\\r\\ntw.local.search[2].name = \\\"BPM Request Number \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0637\\u0644\\u0628\\\";\\r\\ntw.local.search[2].value = \\\"bpmRequestNumber\\\";\\r\\n\\r\\ntw.local.search[3]= new tw.object.NameValuePair();\\r\\ntw.local.search[3].name = \\\"Request Status \\u062d\\u0627\\u0644\\u0647 \\u0627\\u0644\\u0637\\u0644\\u0628\\\";\\r\\ntw.local.search[3].value = \\\"requestStatus\\\";\\r\\n\\r\\ntw.local.search[4]= new tw.object.NameValuePair();\\r\\ntw.local.search[4].name = \\\"Request Date  \\u062a\\u0627\\u0631\\u064a\\u062e \\u0627\\u0644\\u0637\\u0644\\u0628\\\";\\r\\ntw.local.search[4].value = \\\"requestDate\\\";\\r\\n\\r\\ntw.local.search[5]= new tw.object.NameValuePair();\\r\\ntw.local.search[5].name = \\\"Invoice Number \\u0631\\u0642\\u0645 \\u0627\\u0644\\u0641\\u0627\\u062a\\u0648\\u0631\\u0647\\\";\\r\\ntw.local.search[5].value = \\\"invoiceNumber\\\";\\r\\n\\r\\ntw.local.search[6]= new tw.object.NameValuePair();\\r\\ntw.local.search[6].name = \\\"Bill Of Lading Ref \\u0631\\u0642\\u0645 \\u0628\\u0648\\u0644\\u064a\\u0635\\u0647 \\u0627\\u0644\\u0634\\u062d\\u0646\\\";\\r\\ntw.local.search[6].value = \\\"billOfLading\\\";\\r\\n\\r\\ntw.local.search[7]= new tw.object.NameValuePair();\\r\\ntw.local.search[7].name = \\\"Trade Fo Reference Number \\u0631\\u0642\\u0645 \\u0645\\u0648\\u0627\\u0641\\u0642\\u0629 \\u0648\\u062d\\u062f\\u0629 \\u062a\\u0645\\u0648\\u064a\\u0644 \\u0627\\u0644\\u062a\\u062c\\u0627\\u0631\\u0629\\\";\\r\\ntw.local.search[7].value = \\\"tradeFoRefNo\\\";\\r\\n\\r\\n\\r\\ntw.local.results = tw.local.search;\\r\\n\\r\\n\\r\\n\"]}},{\"targetRef\":\"6ac8db85-c4f0-4845-8436-04d472b352e4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee\",\"sourceRef\":\"b9758410-f877-485c-84f1-bd6b1038e639\"},{\"itemSubjectRef\":\"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"name\":\"search\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.3374f806-1875-4fde-88d1-53473372df47\"}],\"laneSet\":[{\"id\":\"a0f94208-8cab-4a77-8604-8d94bafcc9a0\",\"lane\":[{\"flowNodeRef\":[\"5a489bae-f3b2-4e4e-8483-811a8ce22eb7\",\"6ac8db85-c4f0-4845-8436-04d472b352e4\",\"b9758410-f877-485c-84f1-bd6b1038e639\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"43e2c23e-ca8e-4c67-83ed-e9158ecae48f\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"get search Criteria List\",\"declaredType\":\"process\",\"id\":\"1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"results\",\"isCollection\":true,\"id\":\"2055.8683896a-48aa-4699-8f02-f8e2b33f6489\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.0ac53667-a1c4-4086-b17a-f78b72a6299a\",\"epvProcessLinkId\":\"2c9f4115-c436-4fb5-8b7e-05670e498122\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.5ba0f331-49f6-4a09-818d-80878e8bb52d\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.8683896a-48aa-4699-8f02-f8e2b33f6489\"]}],\"dataInput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"data\",\"isCollection\":false,\"id\":\"2055.5ba0f331-49f6-4a09-818d-80878e8bb52d\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5ba0f331-49f6-4a09-818d-80878e8bb52d", "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "90e8de49-8852-4b7a-a66a-60a0162d0244", "versionId": "8f598dd3-debd-43f7-863a-7e52178a6c97"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.8683896a-48aa-4699-8f02-f8e2b33f6489", "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "parameterType": "2", "isArrayOf": "true", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4b8ec363-8cc7-401c-b214-7b3c622025cf", "versionId": "41c9253c-41fb-4a47-9d87-0d4b92987288"}], "processVariable": {"name": "search", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3374f806-1875-4fde-88d1-53473372df47", "description": {"isNull": "true"}, "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0adc1cb6-a19e-4d3d-a244-fab8bad7e291", "versionId": "ea981b3c-0e2b-44e2-8d7a-7e0ddafc487c"}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6ac8db85-c4f0-4845-8436-04d472b352e4", "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.05f60301-46b8-487c-9f95-9b12c9562c36", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e4a", "versionId": "8494410d-926a-419c-9820-d0939db7a55d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.05f60301-46b8-487c-9f95-9b12c9562c36", "haltProcess": "false", "guid": "2fcea4f2-00cc-49af-8730-30735f9812ed", "versionId": "21ed9dc2-55fb-45fd-b8b1-c03d96539bd5"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b9758410-f877-485c-84f1-bd6b1038e639", "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "name": "search Criteria", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.25147c16-173d-419a-95ef-7f3d30dac30f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e53", "versionId": "e19d88e4-9482-4024-a6c4-6f4babc2ecff", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "181", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.25147c16-173d-419a-95ef-7f3d30dac30f", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.search =new tw.object.listOf.NameValuePair();\r\r\n\r\r\ntw.local.search[0]= new tw.object.NameValuePair();\r\r\ntw.local.search[0].name = \"CIF\";\r\r\ntw.local.search[0].value = \"cif\";\r\r\n\r\r\ntw.local.search[1]= new tw.object.NameValuePair();\r\r\ntw.local.search[1].name = \"Request Type نوع الطلب\";\r\r\ntw.local.search[1].value = \"requestType\";\r\r\n\r\r\ntw.local.search[2]= new tw.object.NameValuePair();\r\r\ntw.local.search[2].name = \"BPM Request Number رقم الطلب\";\r\r\ntw.local.search[2].value = \"bpmRequestNumber\";\r\r\n\r\r\ntw.local.search[3]= new tw.object.NameValuePair();\r\r\ntw.local.search[3].name = \"Request Status حاله الطلب\";\r\r\ntw.local.search[3].value = \"requestStatus\";\r\r\n\r\r\ntw.local.search[4]= new tw.object.NameValuePair();\r\r\ntw.local.search[4].name = \"Request Date  تاريخ الطلب\";\r\r\ntw.local.search[4].value = \"requestDate\";\r\r\n\r\r\ntw.local.search[5]= new tw.object.NameValuePair();\r\r\ntw.local.search[5].name = \"Invoice Number رقم الفاتوره\";\r\r\ntw.local.search[5].value = \"invoiceNumber\";\r\r\n\r\r\ntw.local.search[6]= new tw.object.NameValuePair();\r\r\ntw.local.search[6].name = \"Bill Of Lading Ref رقم بوليصه الشحن\";\r\r\ntw.local.search[6].value = \"billOfLading\";\r\r\n\r\r\ntw.local.search[7]= new tw.object.NameValuePair();\r\r\ntw.local.search[7].name = \"Trade Fo Reference Number رقم موافقة وحدة تمويل التجارة\";\r\r\ntw.local.search[7].value = \"tradeFoRefNo\";\r\r\n\r\r\n\r\r\ntw.local.results = tw.local.search;\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "4eeb166c-2f1b-4c0b-9cc6-871ea96eb8a8", "versionId": "f7c233c1-9b0f-4b7c-995a-************"}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.12b60f4f-1f32-472e-983d-174f0b813f7e", "epvId": "/21.0ac53667-a1c4-4086-b17a-f78b72a6299a", "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "guid": "a8de7c47-021a-46ba-875b-45f7e45eccb3", "versionId": "af2119a4-f4fe-4d57-8714-99334bc15c66"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "get search Criteria List", "id": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.0ac53667-a1c4-4086-b17a-f78b72a6299a", "epvProcessLinkId": "2c9f4115-c436-4fb5-8b7e-05670e498122"}}}, "ns16:dataInput": {"name": "data", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.5ba0f331-49f6-4a09-818d-80878e8bb52d"}, "ns16:dataOutput": {"name": "results", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "true", "id": "2055.8683896a-48aa-4699-8f02-f8e2b33f6489"}, "ns16:inputSet": {"ns16:dataInputRefs": "2055.5ba0f331-49f6-4a09-818d-80878e8bb52d"}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.8683896a-48aa-4699-8f02-f8e2b33f6489"}}, "ns16:laneSet": {"id": "a0f94208-8cab-4a77-8604-8d94bafcc9a0", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "43e2c23e-ca8e-4c67-83ed-e9158ecae48f", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["5a489bae-f3b2-4e4e-8483-811a8ce22eb7", "6ac8db85-c4f0-4845-8436-04d472b352e4", "b9758410-f877-485c-84f1-bd6b1038e639"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "5a489bae-f3b2-4e4e-8483-811a8ce22eb7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22"}, "ns16:endEvent": {"name": "End", "id": "6ac8db85-c4f0-4845-8436-04d472b352e4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e4a"}, "ns16:incoming": "264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee"}, "ns16:sequenceFlow": [{"sourceRef": "5a489bae-f3b2-4e4e-8483-811a8ce22eb7", "targetRef": "b9758410-f877-485c-84f1-bd6b1038e639", "name": "To End", "id": "2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "b9758410-f877-485c-84f1-bd6b1038e639", "targetRef": "6ac8db85-c4f0-4845-8436-04d472b352e4", "name": "To End", "id": "264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "search Criteria", "id": "b9758410-f877-485c-84f1-bd6b1038e639", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "181", "y": "56", "width": "95", "height": "70"}}, "ns16:incoming": "2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22", "ns16:outgoing": "264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee", "ns16:script": "tw.local.search =new tw.object.listOf.NameValuePair();\r\r\n\r\r\ntw.local.search[0]= new tw.object.NameValuePair();\r\r\ntw.local.search[0].name = \"CIF\";\r\r\ntw.local.search[0].value = \"cif\";\r\r\n\r\r\ntw.local.search[1]= new tw.object.NameValuePair();\r\r\ntw.local.search[1].name = \"Request Type نوع الطلب\";\r\r\ntw.local.search[1].value = \"requestType\";\r\r\n\r\r\ntw.local.search[2]= new tw.object.NameValuePair();\r\r\ntw.local.search[2].name = \"BPM Request Number رقم الطلب\";\r\r\ntw.local.search[2].value = \"bpmRequestNumber\";\r\r\n\r\r\ntw.local.search[3]= new tw.object.NameValuePair();\r\r\ntw.local.search[3].name = \"Request Status حاله الطلب\";\r\r\ntw.local.search[3].value = \"requestStatus\";\r\r\n\r\r\ntw.local.search[4]= new tw.object.NameValuePair();\r\r\ntw.local.search[4].name = \"Request Date  تاريخ الطلب\";\r\r\ntw.local.search[4].value = \"requestDate\";\r\r\n\r\r\ntw.local.search[5]= new tw.object.NameValuePair();\r\r\ntw.local.search[5].name = \"Invoice Number رقم الفاتوره\";\r\r\ntw.local.search[5].value = \"invoiceNumber\";\r\r\n\r\r\ntw.local.search[6]= new tw.object.NameValuePair();\r\r\ntw.local.search[6].name = \"Bill Of Lading Ref رقم بوليصه الشحن\";\r\r\ntw.local.search[6].value = \"billOfLading\";\r\r\n\r\r\ntw.local.search[7]= new tw.object.NameValuePair();\r\r\ntw.local.search[7].name = \"Trade Fo Reference Number رقم موافقة وحدة تمويل التجارة\";\r\r\ntw.local.search[7].value = \"tradeFoRefNo\";\r\r\n\r\r\n\r\r\ntw.local.results = tw.local.search;\r\r\n\r\r\n\r\r\n"}, "ns16:dataObject": {"itemSubjectRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "isCollection": "true", "name": "search", "id": "2056.3374f806-1875-4fde-88d1-53473372df47"}}}}, "link": {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee", "processId": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b9758410-f877-485c-84f1-bd6b1038e639", "2025.b9758410-f877-485c-84f1-bd6b1038e639"], "endStateId": "Out", "toProcessItemId": ["2025.6ac8db85-c4f0-4845-8436-04d472b352e4", "2025.6ac8db85-c4f0-4845-8436-04d472b352e4"], "guid": "4b9983bf-3451-4bcc-8841-77e4e1513a8d", "versionId": "4c3106bf-d6ca-4509-8d62-7eda9ff0a473", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}}}}, "subType": "12", "hasDetails": false}