<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.93bd8272-8f96-42db-92a3-1bdb463c2c28" name="ClosureACT05 - ODC Closure Execution">
        <lastModified>1700641404276</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.30837b9b-3705-4078-9c2e-f9fb8474949e</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-348a</guid>
        <versionId>54a260cb-e48b-4db7-8b45-708bb909eb01</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb"],"isInterrupting":true,"extensionElements":{"default":["2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"ba48320a-5dfd-4915-aa88-32cc0a8edc27"},{"startQuantity":1,"outgoing":["2027.f4b244b8-1d11-4fc3-853e-19f916345e3c"],"incoming":["2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb"],"default":"2027.f4b244b8-1d11-4fc3-853e-19f916345e3c","extensionElements":{"nodeVisualInfo":[{"width":95,"x":179,"y":176,"declaredType":"TNodeVisualInfo","height":70}]},"name":"init Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a9e9a3c4-02d7-4de7-8855-458e053516df","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.stepLog ={};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step      =  tw.epv.ScreenNames.closureACT05;\r\n\r\n\r\ntw.local.actionconditions = {};\r\ntw.local.actionconditions.screenName     = tw.epv.ScreenNames.closureACT05;\r\n\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/*Initializing Request header*\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nvar date = new Date();\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\/' +(date.getMonth() + 1) + '\/' + date.getFullYear();\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT05;\r\n\/\/tw.local.odcRequest.BasicDetails.requestState = \"\";\r\n\r\n\t\r\n\t\t\r\n\t"]}},{"outgoing":["2027.bb0db777-3025-4765-88df-fd27048e8e58","2027.09b04157-382a-4e1c-860f-7c6b2e81e99b"],"incoming":["2027.f4b244b8-1d11-4fc3-853e-19f916345e3c","2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2","2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2"],"extensionElements":{"nodeVisualInfo":[{"color":"#800080","width":95,"x":360,"y":176,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Reversal_Closure_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a45fb5a7-2d06-4584-8d77-c08854cb5f69","optionName":"@label","value":"Closure"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8524ac39-b7d1-4acc-8e37-9a39f60d48df","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"43fdd737-cf8d-42f4-8336-b5bee21b9efd","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d258fac6-319d-4603-86d6-4e0ac1a4262d","optionName":"closureReasonVIS","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0b8bd259-584f-4f24-8714-c089cdfb1b05","optionName":"reversalReasonVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a6f3b55e-47fb-4de2-82e1-09924d1f6d1c","optionName":"executionHubVIS","value":"None"}],"viewUUID":"64.f0c268ac-0772-4735-af5b-5fc6caec30a1","binding":"tw.local.odcRequest.ReversalReason","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6d9c1300-7fd9-49f9-8cc8-c05d4b5a8010","version":"8550"},{"layoutItemId":"Basic_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b5e84df5-316e-4026-8fd0-ba1696b44a50","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b2b45b57-2aa7-4a1d-816c-5ad268cead66","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f6dc26de-d90a-4e8e-89f6-04d8f6179d4f","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"82d66d5a-5a84-425c-8b4c-6a26663dc7ef","optionName":"parentRequestNoVis","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5d1517e0-e6ef-47bb-8531-f03da30f6fc1","optionName":"basicDetailsCVVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f92607fe-5a55-4200-8559-fc4c1c6b3b86","optionName":"multiTenorDatesVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2bff3b65-4ac2-4efa-8d90-b9f92634a1e2","optionName":"contractStageVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"802ae077-ca46-48d8-82ac-1bb1524cee15","optionName":"flexCubeContractNoVIS","value":"None"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"866920b9-49f7-4a04-88bd-3e2e97bfd20d","version":"8550"},{"layoutItemId":"Customer_Information_cv1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"913814da-1b71-4b43-8d2a-e0ae915654ae","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"75de7873-b93a-46bb-8c66-83017539cd00","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1c9d2e4b-fdee-4b28-8d00-4bb87dd5617a","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"076c148f-bc2d-4f8d-8d98-0d0d8aef5a16","optionName":"customerInfoVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f1b3d48d-b033-4b96-87f6-53cc95792905","optionName":"listsVIS","value":"Readonly"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"588dbf30-5d16-4caf-8792-51a42bf1a542","version":"8550"},{"layoutItemId":"Financial_Details_Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cbd77d2c-df1d-4b8b-8708-efe77e0c1a8c","optionName":"@label","value":"Financial Details Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e71276fc-a06b-4272-808a-846f79241353","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"40558293-93bc-4b55-821c-6ba32cdba17a","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"adca4706-8288-4a07-82d1-604cea9c5906","optionName":"financialDetailsCVVis","value":"READONLY"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d6123b1c-e3fd-47be-8e0f-145739766389","optionName":"currencyDocAmountVIS","value":"READONLY"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"4fc83afa-5ad3-4700-8cc5-2133d12ab00e","version":"8550"},{"layoutItemId":"FC_Collections_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cc154d36-d02d-404d-8c00-4da0c57181b2","optionName":"@label","value":"FC Collections"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"754592f8-817a-4262-8652-46e432db166b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dcb224d0-db03-406a-858b-bda4ebe6ddca","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ccabc1e5-42e8-40cb-878c-fc13f6340666","optionName":"FCVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d736054d-af7a-4b1c-8bf5-336752a2366c","optionName":"retrieveBtnVis","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0df91f29-d8a3-450f-8b41-3c1c80b39506","optionName":"addBtnVIS","value":"NONE"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ac9ce7ee-da29-43ec-8071-e6ad6619d338","optionName":"customerCif","value":"tw.local.odcRequest.cif"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"766fb060-aff9-4f7f-8cb9-4d2a67664908","optionName":"collectionCurrencyVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c4dd49de-8bff-4450-805b-509e681c13c7","optionName":"negotiatedExchangeRateVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0ad232a5-6c7a-495d-870a-dfa3c29c2633","optionName":"requestCurrency","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"72ffb254-fd63-4f56-8033-97bf7ebe0fa7","optionName":"activityType","value":"read"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","binding":"tw.local.odcRequest.FcCollections","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ff378fb0-dce0-4235-8db9-ea9c3aa44117","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a7944e40-c8bc-43b5-827a-a2ba0b66cbe3","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"781a9682-3ef5-429b-8b6b-107bcce44f12","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bc70f217-3fb8-4a9e-82f5-9bc2f642c956","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3dfe2952-882a-4287-8a22-d7959836b417","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f9c48617-9d72-4bf9-849c-4dc0ef9813ec","optionName":"canCreate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"16ef9da6-e60c-4fab-8f93-9abe62b9ec56","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"35ba99f1-b19f-43cd-8eab-74b55fd56a61","optionName":"canDelete","value":"true"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"dfa7556f-70bf-478e-823b-013ab59727ee","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f37a5751-6b2a-425f-86b5-bc9daa3de62f","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2d314ca3-d861-4ebe-8794-ac65fc4d5fc7","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c42e3fff-7203-4694-8d36-31d63d55fc24","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"61f07e03-b1eb-47da-8ecb-f37d75f84755","optionName":"@visibility.script","value":"{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"9a8aa7e3-e355-472c-8c1e-f12bb795dd71","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"639fc11b-eab1-4764-8373-6b14eecc000e"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"502be496-980d-437b-8fa7-aeef8c732f57","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c5555ded-d0e3-4f33-862f-e90789f04d9e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1ff30e00-eff9-42f3-8522-54ea0d3776cf","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ce186131-c41e-46b4-8531-6bbe3b720254","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"6324e1b7-d024-4868-8504-982c41e37669"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c006df9c-305e-4490-8d4c-befad34678dc","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"59fe220f-c2bb-4a90-843d-953ee8eb705c","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5a2fcd6d-8b86-4e72-8dbd-22183f83e0d3","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4fed975b-db0c-4556-87b4-810b4e2f70f8","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"60eb4192-138a-4a7a-85d3-aecd92ed5529","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"14032b26-ccca-4694-8416-46d9aa317ba5","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"63694cfa-0ffc-498d-8dde-0962861d3aa8","optionName":"complianceApprovalVis","value":"NONE"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"36c492da-2b71-4937-8422-00b3b53a2db4","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"291d3fb0-00dd-4539-8f58-a18d8dfaf2c4","optionName":"actionConditions","value":"tw.local.actionconditions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6b346846-6a3d-48ac-8e12-d6deb0f5d569","optionName":"terminateReasonVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"914ca409-7ef2-4189-8b12-f4092d005171","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"10c340be-6dca-4a64-8370-ee5caf1d4b23","optionName":"tradeFoCommentVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"880340e8-2557-4204-8caa-262359def980","optionName":"exeHubMkrCommentVis","value":"None"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"e291cc1f-15d5-4fe4-8f93-8b918753b35a","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Review ODC Closure Request","isForCompensation":false,"completionQuantity":1,"id":"2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9"},{"startQuantity":1,"outgoing":["2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b"],"incoming":["2027.bb0db777-3025-4765-88df-fd27048e8e58"],"default":"2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":531,"y":179,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.8fbcf8ba-ad5d-4e2e-8c6c-e65d8e47e45e","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false;\r\n \r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\nmandatory(tw.local.odcRequest.ReversalReason.closureReason , \"tw.local.odcRequest.ReversalReason.closureReason\");\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo)\r\n{\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason , \"tw.local.odcRequest.stepLog.returnReason\");\r\n}\t\r\n\t\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\/\/function maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n\/\/{\r\n\/\/\tif (field.length &gt; len)\r\n\/\/\t{\r\n\/\/\t\taddError(fieldName , controlMessage , validationMessage);\r\n\/\/\t\treturn false;\r\n\/\/\t}\r\n\/\/\treturn true;\r\n\/\/}\r\n\t"]}},{"startQuantity":1,"outgoing":["2027.6c35c9c2-903f-4ec1-8640-7394401289cc"],"incoming":["2027.e9b654cc-73db-47b2-8d8d-d4cf30d6c17d"],"default":"2027.6c35c9c2-903f-4ec1-8640-7394401289cc","extensionElements":{"nodeVisualInfo":[{"width":95,"x":736,"y":180,"declaredType":"TNodeVisualInfo","height":70}]},"name":"setting status and substatus","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.7780afeb-eef7-402e-8ccd-c7ab326e6f20","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"In Execution\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending Execution Hub Closure Review \";\r\n\ttw.local.lastAction = \"\";\r\n}\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"In Execution\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending Execution Hub Closure Review\";\r\n\ttw.local.lastAction = tw.epv.CreationActions.returnToTradeFo;\r\n}"]}},{"outgoing":["2027.b5f50758-6fa8-441a-861f-346a3e844bb7"],"incoming":["2027.6c35c9c2-903f-4ec1-8640-7394401289cc"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":908,"y":180,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.b5f50758-6fa8-441a-861f-346a3e844bb7","name":"History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.epv.userRole.RevAct01"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.14b3f2d4-fccb-4bb4-8646-8afce6b41d18","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionconditions","isCollection":false,"declaredType":"dataObject","id":"2056.97bd0cf6-528d-4a88-84a9-4223452e7586"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.3168790e-4fd7-448c-8a3c-3a89cc89ff72"},{"targetRef":"2025.a9e9a3c4-02d7-4de7-8855-458e053516df","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To init Script","declaredType":"sequenceFlow","id":"2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb","sourceRef":"ba48320a-5dfd-4915-aa88-32cc0a8edc27"},{"targetRef":"2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review ODC Closure Request","declaredType":"sequenceFlow","id":"2027.f4b244b8-1d11-4fc3-853e-19f916345e3c","sourceRef":"2025.a9e9a3c4-02d7-4de7-8855-458e053516df"},{"targetRef":"2025.8fbcf8ba-ad5d-4e2e-8c6c-e65d8e47e45e","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"e7d611f7-cdc4-4e5d-87eb-b59ba6718d18","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validation script","declaredType":"sequenceFlow","id":"2027.bb0db777-3025-4765-88df-fd27048e8e58","sourceRef":"2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9"},{"targetRef":"2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To setting status and substatus","declaredType":"sequenceFlow","id":"2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b","sourceRef":"2025.8fbcf8ba-ad5d-4e2e-8c6c-e65d8e47e45e"},{"targetRef":"2025.14b3f2d4-fccb-4bb4-8646-8afce6b41d18","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To History","declaredType":"sequenceFlow","id":"2027.6c35c9c2-903f-4ec1-8640-7394401289cc","sourceRef":"2025.7780afeb-eef7-402e-8ccd-c7ab326e6f20"},{"incoming":["2027.544c78e3-ca66-4da9-881c-2bec3cf2293d"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1312,"y":202,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"2025.322e3610-840e-47ba-877d-00c295098b8c"},{"targetRef":"2025.f05c4aa2-2423-46a5-8f02-c3764a862803","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To audited?","declaredType":"sequenceFlow","id":"2027.b5f50758-6fa8-441a-861f-346a3e844bb7","sourceRef":"2025.14b3f2d4-fccb-4bb4-8646-8afce6b41d18"},{"outgoing":["2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2"],"incoming":["2027.09b04157-382a-4e1c-860f-7c6b2e81e99b"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2"],"nodeVisualInfo":[{"width":24,"x":367,"y":64,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.21c71af2-ac02-4366-824d-d0bf7c0dd604"},{"targetRef":"2025.21c71af2-ac02-4366-824d-d0bf7c0dd604","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"5527403b-21c2-42b4-8740-e6a7e75185c3","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.09b04157-382a-4e1c-860f-7c6b2e81e99b","sourceRef":"2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9"},{"targetRef":"2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review ODC Closure Request","declaredType":"sequenceFlow","id":"2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2","sourceRef":"2025.21c71af2-ac02-4366-824d-d0bf7c0dd604"},{"outgoing":["2027.e9b654cc-73db-47b2-8d8d-d4cf30d6c17d","2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2"],"incoming":["2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b"],"default":"2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":655,"y":198,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f"},{"targetRef":"2025.7780afeb-eef7-402e-8ccd-c7ab326e6f20","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.e9b654cc-73db-47b2-8d8d-d4cf30d6c17d","sourceRef":"2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f"},{"targetRef":"2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2","sourceRef":"2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f"},{"outgoing":["2027.544c78e3-ca66-4da9-881c-2bec3cf2293d","2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f"],"incoming":["2027.b5f50758-6fa8-441a-861f-346a3e844bb7"],"default":"2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1032,"y":199,"declaredType":"TNodeVisualInfo","height":32}]},"name":"audited?","declaredType":"exclusiveGateway","id":"2025.f05c4aa2-2423-46a5-8f02-c3764a862803"},{"targetRef":"2025.322e3610-840e-47ba-877d-00c295098b8c","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.544c78e3-ca66-4da9-881c-2bec3cf2293d","sourceRef":"2025.f05c4aa2-2423-46a5-8f02-c3764a862803"},{"incoming":["2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1058,"y":291,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.52317575-e214-4c23-8c47-7c78d25c3778"},{"targetRef":"2025.52317575-e214-4c23-8c47-7c78d25c3778","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f","sourceRef":"2025.f05c4aa2-2423-46a5-8f02-c3764a862803"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"13674448-b071-4741-960d-3ed41bfffd8c","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"daa24394-a198-4b5b-a9da-810dbc4f504b","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"ClosureACT05 - ODC Closure Execution","declaredType":"globalUserTask","id":"1.93bd8272-8f96-42db-92a3-1bdb463c2c28","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.fed13d4d-ae3e-4307-847d-fea93244b330"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.31aa1c84-386b-4b78-8766-571d25ab8d35"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.8bea9a41-23d6-440a-878b-f43f5c87df91"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"5215b168-070c-434c-874b-1fe469366792","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"729e97ea-16df-42ba-8ee1-e80a389b4265","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"6616f2cf-3a1b-4512-81d4-89db8b2d8a01","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"_e139b486-e3fd-495d-af81-2245f6d09b21"}],"outputSet":[{"id":"_42e03482-047f-467c-bb48-bcc4f70a1ac7"}],"dataInput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.7e82bcd2-ff2f-4edf-8e16-cb3fc06e5642"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.b7b1c9dc-eb0d-4c19-836b-e89ff2fa3215"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"64136fce-3dd8-4536-b180-b6fc2dd62d02"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7e82bcd2-ff2f-4edf-8e16-cb3fc06e5642</processParameterId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3d33dcb6-c013-441f-bbab-43b41ef1f25a</guid>
            <versionId>2d9943a4-225d-4d79-be5c-9ea5531c30e0</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b7b1c9dc-eb0d-4c19-836b-e89ff2fa3215</processParameterId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>15104220-db9f-4fe2-a967-ed2ba9266492</guid>
            <versionId>1afe2549-b0bf-4a34-8f77-58f80e8d3503</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fed13d4d-ae3e-4307-847d-fea93244b330</processParameterId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2f9ecac5-b9be-4a42-b755-a0d7fbdc0ddc</guid>
            <versionId>29c8572a-f0b2-4ea6-a16a-dbf9c91795d6</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.31aa1c84-386b-4b78-8766-571d25ab8d35</processParameterId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1f31ac35-326d-4e04-8359-6af22cd725d1</guid>
            <versionId>9dc018a7-5411-462c-a6d0-e6d3bc6444e5</versionId>
        </processParameter>
        <processVariable name="actionconditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.97bd0cf6-528d-4a88-84a9-4223452e7586</processVariableId>
            <description isNull="true" />
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>04bdcdd6-c331-4faf-8513-5d398c06fc99</guid>
            <versionId>51479cf0-29a3-4b44-8420-3ec8d2c08e52</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3168790e-4fd7-448c-8a3c-3a89cc89ff72</processVariableId>
            <description isNull="true" />
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5bd4f1b3-64df-4c0b-aace-288004fe4752</guid>
            <versionId>2158090b-53a4-431f-a7ef-ad27dcb9761e</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.14b3f2d4-fccb-4bb4-8646-8afce6b41d18</processItemId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <name>History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.0b58e358-3438-4ee8-a924-534024e8f9fb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a6cf6b670:-7556</guid>
            <versionId>07f0dfde-d256-48c2-a81a-340fdb2291ec</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.0b58e358-3438-4ee8-a924-534024e8f9fb</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>4f5b8a71-e081-4c23-a1aa-dfaf161af1e5</guid>
                <versionId>9dd775ed-3cfa-4586-8222-7a5bcfe6a593</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.56b07f75-fc97-489a-8ebb-d763ece26c2d</processItemId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.1956c218-0388-4406-af45-e247219f723e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3489</guid>
            <versionId>2be7b6b7-08f8-4a96-a9a6-8d03d226cda5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.1956c218-0388-4406-af45-e247219f723e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>d88c8902-6d2f-468d-8a69-efb2ea2ef6b5</guid>
                <versionId>7b263ea7-0f25-4a5e-9279-a7d62739e90e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.30837b9b-3705-4078-9c2e-f9fb8474949e</processItemId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.5cb1e0c0-0b84-4e66-b673-2c6509823d1d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3488</guid>
            <versionId>a47d5119-fc22-4a17-b2e4-8fe947e9fca2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f97ef88e-235e-4e16-8204-eca492e21c64</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <guid>f7f406c0-6259-43ff-9034-2786d06c1786</guid>
            <versionId>00ed6a08-0474-4e58-9aa3-733d12d09bcc</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.937a3328-b8bb-4e93-b371-e6f4b9723ae2</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <guid>dcd59ddd-4d24-442f-be67-ffe6ce86b44c</guid>
            <versionId>60c25887-849d-4f58-8698-f0b302eddd5a</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.d92b27ca-1b95-44a9-9caa-f95af9b9b51c</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <guid>50e114e6-a4dd-4ce9-a8af-4bd7a859734c</guid>
            <versionId>a3e1e39e-01b2-4030-b49e-7e8ff17b461b</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.49224a9d-abcd-431d-b907-e764789a9272</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <guid>66ef1429-539b-4b2f-8108-f42eb535ed1f</guid>
            <versionId>be920e22-0013-4096-8ac2-ee7508dc679d</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.30837b9b-3705-4078-9c2e-f9fb8474949e</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="64136fce-3dd8-4536-b180-b6fc2dd62d02" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="ClosureACT05 - ODC Closure Execution" id="1.93bd8272-8f96-42db-92a3-1bdb463c2c28">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="daa24394-a198-4b5b-a9da-810dbc4f504b">
                            
                            
                            <ns16:startEvent name="Start" id="ba48320a-5dfd-4915-aa88-32cc0a8edc27">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:default>2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f4b244b8-1d11-4fc3-853e-19f916345e3c" name="init Script" id="2025.a9e9a3c4-02d7-4de7-8855-458e053516df">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="179" y="176" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f4b244b8-1d11-4fc3-853e-19f916345e3c</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.odcRequest.stepLog ={};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step      =  tw.epv.ScreenNames.closureACT05;&#xD;
&#xD;
&#xD;
tw.local.actionconditions = {};&#xD;
tw.local.actionconditions.screenName     = tw.epv.ScreenNames.closureACT05;&#xD;
&#xD;
///////////////*Initializing Request header*//////////////////////////&#xD;
//////////////////////////////////////////////////////////////////////&#xD;
var date = new Date();&#xD;
tw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();&#xD;
tw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+"( "+ tw.system.user.name+")";&#xD;
tw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT05;&#xD;
//tw.local.odcRequest.BasicDetails.requestState = "";&#xD;
&#xD;
	&#xD;
		&#xD;
	</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Review ODC Closure Request" id="2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="360" y="176" width="95" height="70" color="#800080" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f4b244b8-1d11-4fc3-853e-19f916345e3c</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.bb0db777-3025-4765-88df-fd27048e8e58</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.09b04157-382a-4e1c-860f-7c6b2e81e99b</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>e291cc1f-15d5-4fe4-8f93-8b918753b35a</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c006df9c-305e-4490-8d4c-befad34678dc</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>59fe220f-c2bb-4a90-843d-953ee8eb705c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>5a2fcd6d-8b86-4e72-8dbd-22183f83e0d3</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>4fed975b-db0c-4556-87b4-810b4e2f70f8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>60eb4192-138a-4a7a-85d3-aecd92ed5529</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>14032b26-ccca-4694-8416-46d9aa317ba5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>63694cfa-0ffc-498d-8dde-0962861d3aa8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>36c492da-2b71-4937-8422-00b3b53a2db4</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>291d3fb0-00dd-4539-8f58-a18d8dfaf2c4</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.actionconditions</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>6b346846-6a3d-48ac-8e12-d6deb0f5d569</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>914ca409-7ef2-4189-8b12-f4092d005171</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>10c340be-6dca-4a64-8370-ee5caf1d4b23</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>880340e8-2557-4204-8caa-262359def980</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>6324e1b7-d024-4868-8504-982c41e37669</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>ce186131-c41e-46b4-8531-6bbe3b720254</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>502be496-980d-437b-8fa7-aeef8c732f57</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>c5555ded-d0e3-4f33-862f-e90789f04d9e</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>1ff30e00-eff9-42f3-8522-54ea0d3776cf</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>639fc11b-eab1-4764-8373-6b14eecc000e</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>6d9c1300-7fd9-49f9-8cc8-c05d4b5a8010</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Reversal_Closure_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a45fb5a7-2d06-4584-8d77-c08854cb5f69</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Closure</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8524ac39-b7d1-4acc-8e37-9a39f60d48df</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>43fdd737-cf8d-42f4-8336-b5bee21b9efd</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d258fac6-319d-4603-86d6-4e0ac1a4262d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>closureReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0b8bd259-584f-4f24-8714-c089cdfb1b05</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>reversalReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a6f3b55e-47fb-4de2-82e1-09924d1f6d1c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>executionHubVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f0c268ac-0772-4735-af5b-5fc6caec30a1</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.ReversalReason</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>866920b9-49f7-4a04-88bd-3e2e97bfd20d</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b5e84df5-316e-4026-8fd0-ba1696b44a50</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b2b45b57-2aa7-4a1d-816c-5ad268cead66</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f6dc26de-d90a-4e8e-89f6-04d8f6179d4f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>82d66d5a-5a84-425c-8b4c-6a26663dc7ef</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5d1517e0-e6ef-47bb-8531-f03da30f6fc1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f92607fe-5a55-4200-8559-fc4c1c6b3b86</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2bff3b65-4ac2-4efa-8d90-b9f92634a1e2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>802ae077-ca46-48d8-82ac-1bb1524cee15</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>588dbf30-5d16-4caf-8792-51a42bf1a542</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information_cv1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>913814da-1b71-4b43-8d2a-e0ae915654ae</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>75de7873-b93a-46bb-8c66-83017539cd00</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1c9d2e4b-fdee-4b28-8d00-4bb87dd5617a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>076c148f-bc2d-4f8d-8d98-0d0d8aef5a16</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f1b3d48d-b033-4b96-87f6-53cc95792905</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>4fc83afa-5ad3-4700-8cc5-2133d12ab00e</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details_Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cbd77d2c-df1d-4b8b-8708-efe77e0c1a8c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e71276fc-a06b-4272-808a-846f79241353</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>40558293-93bc-4b55-821c-6ba32cdba17a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>adca4706-8288-4a07-82d1-604cea9c5906</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d6123b1c-e3fd-47be-8e0f-145739766389</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>ff378fb0-dce0-4235-8db9-ea9c3aa44117</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>FC_Collections_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cc154d36-d02d-404d-8c00-4da0c57181b2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>FC Collections</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>754592f8-817a-4262-8652-46e432db166b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>dcb224d0-db03-406a-858b-bda4ebe6ddca</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ccabc1e5-42e8-40cb-878c-fc13f6340666</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>FCVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d736054d-af7a-4b1c-8bf5-336752a2366c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>retrieveBtnVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>NONE</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0df91f29-d8a3-450f-8b41-3c1c80b39506</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addBtnVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>NONE</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ac9ce7ee-da29-43ec-8071-e6ad6619d338</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerCif</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.cif</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>766fb060-aff9-4f7f-8cb9-4d2a67664908</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>collectionCurrencyVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c4dd49de-8bff-4450-805b-509e681c13c7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>negotiatedExchangeRateVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0ad232a5-6c7a-495d-870a-dfa3c29c2633</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestCurrency</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>72ffb254-fd63-4f56-8033-97bf7ebe0fa7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>activityType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>read</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FcCollections</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>dfa7556f-70bf-478e-823b-013ab59727ee</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a7944e40-c8bc-43b5-827a-a2ba0b66cbe3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>781a9682-3ef5-429b-8b6b-107bcce44f12</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bc70f217-3fb8-4a9e-82f5-9bc2f642c956</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3dfe2952-882a-4287-8a22-d7959836b417</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f9c48617-9d72-4bf9-849c-4dc0ef9813ec</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>16ef9da6-e60c-4fab-8f93-9abe62b9ec56</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>35ba99f1-b19f-43cd-8eab-74b55fd56a61</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>9a8aa7e3-e355-472c-8c1e-f12bb795dd71</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f37a5751-6b2a-425f-86b5-bc9daa3de62f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2d314ca3-d861-4ebe-8794-ac65fc4d5fc7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c42e3fff-7203-4694-8d36-31d63d55fc24</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>61f07e03-b1eb-47da-8ecb-f37d75f84755</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"@class":"com.ibm.bpm.coachNG.visibility.VisibilityRules","rules":[{"@class":"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule","var_conditions":[{"timeInMs":null,"var":"tw.local.isFirstTime","operand":true,"operator":0}],"action":"NONE"}],"defaultAction":"DEFAULT"}</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b" name="validation script" id="2025.8fbcf8ba-ad5d-4e2e-8c6c-e65d8e47e45e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="531" y="179" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.bb0db777-3025-4765-88df-fd27048e8e58</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b</ns16:outgoing>
                                
                                
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false;&#xD;
 &#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");&#xD;
mandatory(tw.local.odcRequest.ReversalReason.closureReason , "tw.local.odcRequest.ReversalReason.closureReason");&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.stepLog.returnReason , "tw.local.odcRequest.stepLog.returnReason");&#xD;
}	&#xD;
	&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
//function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
//{&#xD;
//	if (field.length &gt; len)&#xD;
//	{&#xD;
//		addError(fieldName , controlMessage , validationMessage);&#xD;
//		return false;&#xD;
//	}&#xD;
//	return true;&#xD;
//}&#xD;
	</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.6c35c9c2-903f-4ec1-8640-7394401289cc" name="setting status and substatus" id="2025.7780afeb-eef7-402e-8ccd-c7ab326e6f20">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="736" y="180" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.e9b654cc-73db-47b2-8d8d-d4cf30d6c17d</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.6c35c9c2-903f-4ec1-8640-7394401289cc</ns16:outgoing>
                                
                                
                                <ns16:script>if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="In Execution";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Pending Execution Hub Closure Review ";&#xD;
	tw.local.lastAction = "";&#xD;
}&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="In Execution";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Pending Execution Hub Closure Review";&#xD;
	tw.local.lastAction = tw.epv.CreationActions.returnToTradeFo;&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b5f50758-6fa8-441a-861f-346a3e844bb7" name="History" id="2025.14b3f2d4-fccb-4bb4-8646-8afce6b41d18">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="908" y="180" width="95" height="70" />
                                    
                                    
                                    <ns3:mode>InvokeService</ns3:mode>
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6c35c9c2-903f-4ec1-8640-7394401289cc</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b5f50758-6fa8-441a-861f-346a3e844bb7</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.epv.userRole.RevAct01</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionconditions" id="2056.97bd0cf6-528d-4a88-84a9-4223452e7586" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.3168790e-4fd7-448c-8a3c-3a89cc89ff72" />
                            
                            
                            <ns16:sequenceFlow sourceRef="ba48320a-5dfd-4915-aa88-32cc0a8edc27" targetRef="2025.a9e9a3c4-02d7-4de7-8855-458e053516df" name="To init Script" id="2027.c4ad0b2d-029d-4033-82c7-9d69adcddfbb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a9e9a3c4-02d7-4de7-8855-458e053516df" targetRef="2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9" name="To Review ODC Closure Request" id="2027.f4b244b8-1d11-4fc3-853e-19f916345e3c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9" targetRef="2025.8fbcf8ba-ad5d-4e2e-8c6c-e65d8e47e45e" name="To validation script" id="2027.bb0db777-3025-4765-88df-fd27048e8e58">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="e7d611f7-cdc4-4e5d-87eb-b59ba6718d18">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8fbcf8ba-ad5d-4e2e-8c6c-e65d8e47e45e" targetRef="2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f" name="To setting status and substatus" id="2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.7780afeb-eef7-402e-8ccd-c7ab326e6f20" targetRef="2025.14b3f2d4-fccb-4bb4-8646-8afce6b41d18" name="To History" id="2027.6c35c9c2-903f-4ec1-8640-7394401289cc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:endEvent name="End" id="2025.322e3610-840e-47ba-877d-00c295098b8c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1312" y="202" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.544c78e3-ca66-4da9-881c-2bec3cf2293d</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.14b3f2d4-fccb-4bb4-8646-8afce6b41d18" targetRef="2025.f05c4aa2-2423-46a5-8f02-c3764a862803" name="To audited?" id="2027.b5f50758-6fa8-441a-861f-346a3e844bb7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.21c71af2-ac02-4366-824d-d0bf7c0dd604">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="367" y="64" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.09b04157-382a-4e1c-860f-7c6b2e81e99b</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9" targetRef="2025.21c71af2-ac02-4366-824d-d0bf7c0dd604" name="To Stay on page" id="2027.09b04157-382a-4e1c-860f-7c6b2e81e99b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="5527403b-21c2-42b4-8740-e6a7e75185c3">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.21c71af2-ac02-4366-824d-d0bf7c0dd604" targetRef="2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9" name="To Review ODC Closure Request" id="2027.e4bc1a49-e45e-47cf-8dc2-7c3a1883efc2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2" name="Valid?" id="2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="655" y="198" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a33c1a34-52a5-4b5e-896a-82f1de3ab40b</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.e9b654cc-73db-47b2-8d8d-d4cf30d6c17d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f" targetRef="2025.7780afeb-eef7-402e-8ccd-c7ab326e6f20" name="Yes" id="2027.e9b654cc-73db-47b2-8d8d-d4cf30d6c17d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.e7646f7b-17c6-4615-8eb6-a6bc9ef3c64f" targetRef="2025.15c7dcee-deb6-4a8d-8b1c-01e0b139fea9" name="No" id="2027.16acfcfa-3691-4c27-89f8-5ef63ab8d7e2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f" name="audited?" id="2025.f05c4aa2-2423-46a5-8f02-c3764a862803">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1032" y="199" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.b5f50758-6fa8-441a-861f-346a3e844bb7</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.544c78e3-ca66-4da9-881c-2bec3cf2293d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f05c4aa2-2423-46a5-8f02-c3764a862803" targetRef="2025.322e3610-840e-47ba-877d-00c295098b8c" name="yes" id="2027.544c78e3-ca66-4da9-881c-2bec3cf2293d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.52317575-e214-4c23-8c47-7c78d25c3778">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1058" y="291" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.f05c4aa2-2423-46a5-8f02-c3764a862803" targetRef="2025.52317575-e214-4c23-8c47-7c78d25c3778" name="no" id="2027.caa22cf9-3f08-444d-8a19-d59f9af2db3f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="13674448-b071-4741-960d-3ed41bfffd8c">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.8bea9a41-23d6-440a-878b-f43f5c87df91</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="5215b168-070c-434c-874b-1fe469366792" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="729e97ea-16df-42ba-8ee1-e80a389b4265" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="6616f2cf-3a1b-4512-81d4-89db8b2d8a01" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.7e82bcd2-ff2f-4edf-8e16-cb3fc06e5642" />
                        
                        
                        <ns16:dataInput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b7b1c9dc-eb0d-4c19-836b-e89ff2fa3215" />
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.fed13d4d-ae3e-4307-847d-fea93244b330" />
                        
                        
                        <ns16:dataOutput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.31aa1c84-386b-4b78-8766-571d25ab8d35" />
                        
                        
                        <ns16:inputSet id="_e139b486-e3fd-495d-af81-2245f6d09b21" />
                        
                        
                        <ns16:outputSet id="_42e03482-047f-467c-bb48-bcc4f70a1ac7" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b8964281-d47e-482c-8a1a-7c87b50ce1e5</processLinkId>
            <processId>1.93bd8272-8f96-42db-92a3-1bdb463c2c28</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.30837b9b-3705-4078-9c2e-f9fb8474949e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.56b07f75-fc97-489a-8ebb-d763ece26c2d</toProcessItemId>
            <guid>3f39b46f-a9c3-460a-9fdd-0a9d5486333b</guid>
            <versionId>6472ac71-d165-4a0a-be2d-bbb34f1b7447</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.30837b9b-3705-4078-9c2e-f9fb8474949e</fromProcessItemId>
            <toProcessItemId>2025.56b07f75-fc97-489a-8ebb-d763ece26c2d</toProcessItemId>
        </link>
    </process>
</teamworks>

