{"id": "24.3bf11e60-c652-49be-a22b-e25b3ef79931", "versionId": "44277c59-ff55-499a-87f6-b1e01b622097", "name": "Branch compliance Representative Manager", "type": "participant", "typeName": "Participant", "details": {}, "_fullObjectData": {"teamworks": {"participant": {"id": "24.3bf11e60-c652-49be-a22b-e25b3ef79931", "name": "Branch compliance Representative Manager", "lastModified": "1691144180817", "lastModifiedBy": "heba", "participantId": "24.3bf11e60-c652-49be-a22b-e25b3ef79931", "participantDefinition": {"isNull": "true"}, "simulationGroupSize": "2", "capacityType": "1", "definitionType": "3", "percentAvailable": {"isNull": "true"}, "percentEfficiency": {"isNull": "true"}, "cost": "10.00", "currencyCode": {"isNull": "true"}, "image": {"isNull": "true"}, "serviceMembersRef": {"isNull": "true"}, "managersRef": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"team\":[{\"members\":{\"Users\":[{\"name\":\"heba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"abdelrahman.saleh\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"somaia\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}],\"UserGroups\":[{\"name\":\"BPM_ODC_BR_COMP_REP_MNGR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails\",\"type\":\"StandardMembers\"}]},\"documentation\":[{\"content\":[],\"textFormat\":\"text\\/plain\"}],\"name\":\"Branch compliance Representative Manager\",\"declaredType\":\"resource\",\"id\":\"24.3bf11e60-c652-49be-a22b-e25b3ef79931\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.w3.org\\/1999\\/XPath\",\"id\":\"24.3bf11e60-c652-49be-a22b-e25b3ef79931\"}", "externalId": {"isNull": "true"}, "description": "", "guid": "guid:d694a63221635d5b:6baf87c4:18969a9a6e2:771e", "versionId": "44277c59-ff55-499a-87f6-b1e01b622097", "standardMembers": {"standardMember": [{"type": "Group", "name": "BPM_ODC_BR_COMP_REP_MNGR"}, {"type": "User", "name": "heba"}, {"type": "User", "name": "abdelrahman.saleh"}, {"type": "User", "name": "so<PERSON>ia"}]}, "teamAssignments": ""}}}}