{"id": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "versionId": "3f5595f6-740a-432d-af7f-278d37e4845c", "name": "DC start request template", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "odcRequest", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "Untitled"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.showApprovals = function  () {\r\r\n//\tif (this.context.options.hasApprovals.get(\"value\")) {\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(true,true);\r\r\n//\t\tif (this.context.options.approvalsReadOnly.get(\"value\")) {\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(false);\r\r\n//\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(true);\r\r\n//\t\t}\r\r\n//\r\r\n//\t}else{\r\r\n//\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(false,true);\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//\r\r\n//this.showReturnReason = function  () {\r\r\n//\tif (this.context.options.hasReturnReason.get(\"value\") && ((this.context.options.selectedAction.get(\"value\") == \"Return To Trade FO\") || (this.context.options.selectedAction.get(\"value\") == \"Return to Initiator\") || (this.context.options.selectedAction.get(\"value\") == \"Return To Maker\"))) {\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(true,true);\r\r\n//\t}else{\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n//\r\r\n//this.showAction = function  () {\r\r\n//\tif (this.context.binding.get(\"value\").get(\"subStatus\") == \"\") {\r\r\n//\t\tthis.ui.get(\"Single_Select1\").setVisible(false,true);\r\r\n//\t\tthis.ui.get(\"Text1\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n\r\r\n//updateActionList = function  () {\r\r\n//\r\r\n//\r\r\n//console.log(this.context.options.errorPanelVIS.get(\"value\"));\r\r\n//\r\r\n////var compApp = ;\r\r\n//\r\r\n////\tif(this.context.options.complianceApproval.get(\"value\"))\r\r\n////\t{\r\r\n////\t\tthis.context.options.action.set(\"value\", [\"123\",\"34\"]);\r\r\n////\t}\r\r\n//}"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "name": "DC start request template", "lastModified": "1692013288444", "lastModifiedBy": "heba", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isTemplate": "true", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>bd85072d-7c74-4d39-8ce1-037a06009e0d</ns2:id><ns2:layoutItemId>Header_View1</ns2:layoutItemId><ns2:configData><ns2:id>9b5f4bc3-8427-4832-852a-c8f48faf98dd</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Header View</ns2:value></ns2:configData><ns2:configData><ns2:id>de5c6869-6947-4e71-8962-b6bcabb8da57</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>716e5467-6ca2-436c-8542-6799aa48ff5f</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.55afafe5-9321-40fd-9b9a-cbbd556a8005</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest.appInfo</ns2:binding></ns2:layoutItem><ns2:layoutItem xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>fe6673cd-c9c8-4352-8f2b-2a116a7db739</ns2:id><ns2:layoutItemId>GridLayoutContainer4</ns2:layoutItemId><ns2:configData><ns2:id>1cf5e062-c534-4392-8835-84a350a1a43b</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>e3ed836d-8431-481b-80ec-fdae3f7cc017</ns2:id><ns2:layoutItemId>GridLayoutContainer5</ns2:layoutItemId><ns2:configData><ns2:id>ae7c2394-eca4-4d90-8d79-8cff10979f82</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>HORIZONTAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>124b173b-a571-4d10-8953-301c7d6cd4c2</ns2:id><ns2:layoutItemId>GridLayoutCell4</ns2:layoutItemId><ns2:configData><ns2:id>049efd81-5559-4859-843d-93cc8c9f55b1</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":1},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>396d58d9-b954-46d4-81c7-c0432739407f</ns2:id><ns2:layoutItemId>GridLayoutContainer1</ns2:layoutItemId><ns2:configData><ns2:id>1e0aad31-6390-44b5-8568-10911f4e9a93</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":11},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>dfddd41d-4593-4320-8d92-e12f42d4d815</ns2:id><ns2:layoutItemId>GridLayoutContainer3</ns2:layoutItemId><ns2:configData><ns2:id>211242dc-bb7f-49b1-8f6a-8aea00b17ca9</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":11},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>HORIZONTAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>51501f53-9c8b-4682-8ad8-c8eedf9afbf3</ns2:id><ns2:layoutItemId>GridLayoutCell5</ns2:layoutItemId><ns2:configData><ns2:id>c7706d44-f248-41d5-882a-d79a986a3324</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":10},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>161b0099-bd57-42f5-8043-8a969826af8a</ns2:id><ns2:layoutItemId>Start_New_Request_CV1</ns2:layoutItemId><ns2:configData><ns2:id>0f05daee-c33b-42a8-8774-71d6bbcf38bf</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Start New Request CV</ns2:value></ns2:configData><ns2:configData><ns2:id>ee8a90e4-72af-4e4f-843b-517041691c2f</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>43c7cc4d-0195-4a06-880c-3a34bafb47c6</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>11be9154-754d-4230-858f-6f9a946ebbe7</ns2:id><ns2:optionName>errorMessgae</ns2:optionName><ns2:value>tw.options.errorMsg</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</ns2:viewUUID><ns2:binding>tw.businessData.odcRequest</ns2:binding></ns2:gridCellLayoutItems></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>e2a54f1e-e72f-4b2b-853f-dca14fed87fb</ns2:id><ns2:layoutItemId>GridLayoutCell3</ns2:layoutItemId><ns2:configData><ns2:id>ff28bdcb-b795-4c0e-8a03-d43b9fcbff05</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":1}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>2a21989c-ae42-4e84-80b2-3d4922dcd758</ns2:id><ns2:layoutItemId>GridLayoutContainer9</ns2:layoutItemId><ns2:configData><ns2:id>31c90a13-3856-4bd3-8b69-c46503d60e01</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":11}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>70b9b0e5-6d16-447e-8bbe-b80c337ea08b</ns2:id><ns2:layoutItemId>GridLayoutCell9</ns2:layoutItemId><ns2:configData><ns2:id>c738c7df-b6ef-4885-8835-3f53e009d91d</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":11}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>9f05068d-582f-43be-865d-11020b1ca62a</ns2:id><ns2:layoutItemId>Panel1</ns2:layoutItemId><ns2:configData><ns2:id>6ac9f238-57e3-4bb6-8d08-9a17436a7a1d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Error Description / تفاصيل الاخطاء</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>9bb5c3cb-cf1c-422f-80b1-d84877163191</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>00782e0f-a99b-4bbd-84fd-6bfa8832aeea</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>7d358143-c247-426e-8e72-2c821cbbc041</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.errorPanelVIS</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>7e2b0d39-4bc8-4652-8e5b-4bb7467a83c7</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>e57dd69a-5202-41e2-8556-2fe48150692c</ns2:id><ns2:optionName>lightColor</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:viewUUID>64.455e44ab-b77b-4337-b3f9-435e234fb569</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>ab66abcc-349c-4529-864a-4e5335bf3b6e</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>12c7ff22-ffb3-4fb1-8e15-edc6491f1d71</ns2:id><ns2:layoutItemId>Output_Text1</ns2:layoutItemId><ns2:configData><ns2:id>109b94af-b770-41dd-8be2-11780eb43d44</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>29046878-a72b-4501-8a9e-88098d46a741</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>b7c68760-c70f-49fb-8a7a-a135753d0ab0</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>c252a8ba-7fb3-4791-8ad5-968ecc777c92</ns2:id><ns2:optionName>@className</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>a029092d-88ed-4bc9-887f-60e199e6bc92</ns2:id><ns2:optionName>@htmlOverrides</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>b34d04e5-e7d1-499a-8506-86e37f144d7b</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>G</ns2:value></ns2:configData><ns2:configData><ns2:id>4f9dce2b-4568-48eb-809f-0134e6070267</ns2:id><ns2:optionName>weightStyle</ns2:optionName><ns2:value>B</ns2:value></ns2:configData><ns2:configData><ns2:id>af1bdedc-678d-451a-8216-fcb54a76fb7f</ns2:id><ns2:optionName>allowHTML</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID><ns2:binding>tw.options.errorMsg</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:gridCellLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "true", "loadJsFunction": {"isNull": "true"}, "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": {"isNull": "true"}, "changeJsFunction": "//updateActionList();", "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "0485aa05-30c1-4749-b165-d52b4847ff13", "versionId": "3f5595f6-740a-432d-af7f-278d37e4845c", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "bindingType": {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewBindingTypeId": "65.7b175874-654d-4450-9f43-72be0cc60235", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "0", "description": {"isNull": "true"}, "guid": "f80c3437-d86b-4c27-9174-688c826a3c89", "versionId": "0f4d0a5b-eeab-4456-bb11-812e3e14fd93"}, "configOption": [{"name": "stepLog", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.aad4aba7-f9cd-4932-a6c5-db390dbbf9f9", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "0", "description": "", "groupName": "", "guid": "dfcf1802-28a1-437a-95f7-44ea15263455", "versionId": "1cb69c96-b9ce-4564-abc2-5c1749c2d814"}, {"name": "action", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.cd9d3c00-04a5-4a76-ab3f-6c336587cab2", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "1", "description": "", "groupName": "", "guid": "0c33c1a2-ba4b-4141-a9a7-e25aee618bc2", "versionId": "bd71973a-22c9-4ba8-8600-109c526664dd"}, {"name": "selectedAction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.c43b4184-720a-434d-93db-fa5c377ff9ae", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "2", "description": "", "groupName": "", "guid": "7d68fe26-f8a2-4c57-8980-16d107e17811", "versionId": "9d72c78a-4e0d-43d7-a8f2-56b253727d2a"}, {"name": "complianceApproval", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.cf1864b2-f290-4bde-a3bd-3f6354444a9f", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "3", "description": "", "groupName": "", "guid": "75740912-16d0-4d5b-9239-6c26fc75d321", "versionId": "fef2add3-acaa-4329-abf6-ccedd107a842"}, {"name": "complianceApprovalVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.da5da78b-53db-4dbd-8796-43dd6c1f97d3", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "4", "description": "", "groupName": "", "guid": "6e818d0b-26d8-470b-b7e5-0d4782bb7f7e", "versionId": "321ced09-caa5-4338-87e8-26f75c49ed78"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.e10bccc3-fae0-43f5-9c3c-0a7e2afa56f8", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "5", "description": "", "groupName": "", "guid": "9d6c57de-fc3e-44c7-b908-370556cdaf4d", "versionId": "ec9d79d7-3b9d-41cf-b039-ad759f58a71c"}, {"name": "terminateReasonVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.a36ea4d8-24d4-4da0-8f43-935c51ee39a6", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "6", "description": "", "groupName": "", "guid": "6590ef4c-e3ac-4954-a5d6-41be8322da47", "versionId": "9521412a-f9b7-4dbb-827f-2c03389154e9"}, {"name": "errorPanelVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.540a1d3e-99e0-414b-a186-a88cd3326906", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "7", "description": "", "groupName": "", "guid": "058b9e04-8955-461d-98c2-5e8f7616e5fd", "versionId": "5397771f-9f74-45b3-8248-af4879f89bf8"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.bcc933d2-844c-40e1-8931-569c3576a782", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "8", "description": "", "groupName": "", "guid": "ff1dc183-a5c7-488a-a137-1c748441a607", "versionId": "496be749-6e28-4b3b-b1bb-c7e39b2b500a"}, {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.e4a90aa7-93ca-45f8-9408-dc15978e4614", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "9", "description": "", "groupName": "", "guid": "adfe79ad-ab9a-4cdc-98a2-5e5152f0d5da", "versionId": "05c622e1-f6c5-4987-96e5-98c49450d463"}], "inlineScript": {"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.030bc6b3-3f91-4b53-825b-aee460895474", "coachViewId": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "scriptType": "JS", "scriptBlock": "//this.showApprovals = function  () {\r\r\n//\tif (this.context.options.hasApprovals.get(\"value\")) {\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(true,true);\r\r\n//\t\tif (this.context.options.approvalsReadOnly.get(\"value\")) {\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(false);\r\r\n//\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(true);\r\r\n//\t\t}\r\r\n//\r\r\n//\t}else{\r\r\n//\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(false,true);\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//\r\r\n//this.showReturnReason = function  () {\r\r\n//\tif (this.context.options.hasReturnReason.get(\"value\") && ((this.context.options.selectedAction.get(\"value\") == \"Return To Trade FO\") || (this.context.options.selectedAction.get(\"value\") == \"Return to Initiator\") || (this.context.options.selectedAction.get(\"value\") == \"Return To Maker\"))) {\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(true,true);\r\r\n//\t}else{\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n//\r\r\n//this.showAction = function  () {\r\r\n//\tif (this.context.binding.get(\"value\").get(\"subStatus\") == \"\") {\r\r\n//\t\tthis.ui.get(\"Single_Select1\").setVisible(false,true);\r\r\n//\t\tthis.ui.get(\"Text1\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n\r\r\n//updateActionList = function  () {\r\r\n//\r\r\n//\r\r\n//console.log(this.context.options.errorPanelVIS.get(\"value\"));\r\r\n//\r\r\n////var compApp = ;\r\r\n//\r\r\n////\tif(this.context.options.complianceApproval.get(\"value\"))\r\r\n////\t{\r\r\n////\t\tthis.context.options.action.set(\"value\", [\"123\",\"34\"]);\r\r\n////\t}\r\r\n//}", "seq": "0", "description": "", "guid": "4825a2c9-3017-4b62-b396-676c20bf093c", "versionId": "d69232f8-d775-4df2-9a5d-00dc17199a26"}}}}, "hasDetails": true}