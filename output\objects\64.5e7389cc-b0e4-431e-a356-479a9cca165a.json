{"id": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "versionId": "3f5595f6-740a-432d-af7f-278d37e4845c", "name": "DC start request template", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "odcRequest", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "Untitled"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.showApprovals = function  () {\r\r\n//\tif (this.context.options.hasApprovals.get(\"value\")) {\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(true,true);\r\r\n//\t\tif (this.context.options.approvalsReadOnly.get(\"value\")) {\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(false);\r\r\n//\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(true);\r\r\n//\t\t}\r\r\n//\r\r\n//\t}else{\r\r\n//\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(false,true);\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//\r\r\n//this.showReturnReason = function  () {\r\r\n//\tif (this.context.options.hasReturnReason.get(\"value\") && ((this.context.options.selectedAction.get(\"value\") == \"Return To Trade FO\") || (this.context.options.selectedAction.get(\"value\") == \"Return to Initiator\") || (this.context.options.selectedAction.get(\"value\") == \"Return To Maker\"))) {\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(true,true);\r\r\n//\t}else{\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n//\r\r\n//this.showAction = function  () {\r\r\n//\tif (this.context.binding.get(\"value\").get(\"subStatus\") == \"\") {\r\r\n//\t\tthis.ui.get(\"Single_Select1\").setVisible(false,true);\r\r\n//\t\tthis.ui.get(\"Text1\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n\r\r\n//updateActionList = function  () {\r\r\n//\r\r\n//\r\r\n//console.log(this.context.options.errorPanelVIS.get(\"value\"));\r\r\n//\r\r\n////var compApp = ;\r\r\n//\r\r\n////\tif(this.context.options.complianceApproval.get(\"value\"))\r\r\n////\t{\r\r\n////\t\tthis.context.options.action.set(\"value\", [\"123\",\"34\"]);\r\r\n////\t}\r\r\n//}"}]}, "hasDetails": true}