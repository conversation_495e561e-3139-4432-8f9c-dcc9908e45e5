{"id": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "versionId": "b5c8a00c-4a4c-420f-95bd-6203311f9b9a", "name": "Act03 -  Review ODC Request by Trade Front Office", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "regeneratedRemittanceLetterTitleVIS", "hasDefault": false, "type": "1"}, {"name": "parentPath", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}, {"name": "isTradeFo", "hasDefault": false, "type": "2"}, {"name": "complianceApprovalInit", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "errorPanelVIS", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "paymentTerms", "hasDefault": false}, {"name": "deliveryTerms", "hasDefault": false}, {"name": "specialInstructions", "hasDefault": false}, {"name": "instructions", "hasDefault": false}, {"name": "bankRefVIS", "hasDefault": false}, {"name": "requestTypeVIS", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "parentRequestNoVIS", "hasDefault": false}, {"name": "multiTenorVIS", "hasDefault": false}, {"name": "todayDate", "hasDefault": false}, {"name": "reqID", "hasDefault": false}, {"name": "sqlOut", "hasDefault": false}, {"name": "error", "hasDefault": false}, {"name": "requestIdStr", "hasDefault": false}, {"name": "invalidTabs", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "ACT03", "id": "2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Audit Request History", "id": "2025.9184a73b-f996-43a3-8ed6-03ad856ded84", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit ODC Request", "id": "2025.de22d752-6311-442f-8c63-c262f8b43e2f", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Cancel and Delete Transactions", "id": "2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel request", "id": "2025.800694b0-0ed1-4b77-8191-203e5997c78d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get accounts", "id": "2025.e9e88217-d06d-4cc0-8294-3396855e634f", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Valid?", "id": "2025.818bf28b-f2ae-4ac8-804f-4746a15bf907", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited?", "id": "2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited?", "id": "2025.5fb79679-5296-4f7e-82b0-bebf028b1805", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "terminate?", "id": "2025.4f73ec24-8706-4d4d-8516-ae3a0527faae", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Exclusive Gateway", "id": "2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "Validation", "id": "2025.e83abf20-ae23-429e-8767-21c270da66f3", "script": "// lctest", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Init", "id": "2025.0e26d665-90eb-44c1-8abc-bc08cb0f1382", "script": "tw.local.errorMessage=\"\";\r\r\ntw.local.errorPanelVIS=\"NONE\";\r\r\ntw.local.todayDate = new Date();\r\r\n\r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime =new Date();\r\r\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT03;\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT03;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;\r\r\n\r\r\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\r\r\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\r\r\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\r\r\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\r\r\n/*app log*/\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT03;\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\r\n\ttw.local.parentRequestNoVIS = \"READONLY\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\n/*Document Generation Section*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.requestTypeVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.requestTypeVIS = \"NONE\";\t\r\r\n}\r\r\n/*Importer Details Visibility*/\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.bankRefVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.bankRefVIS = \"NONE\";\t\r\r\n}\r\r\n/* Financial Trade Fo CV*/\r\r\nif(tw.local.odcRequest.BasicDetails.paymentTerms.name == \"001\")\r\r\n{\r\r\n\ttw.local.multiTenorVIS = \"None\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.multiTenorVIS = \"Editable\";\r\r\n}\r\r\n\r\r\ntw.local.errorPanelVIS =\"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "setting status and substatus", "id": "2025.b8b40c99-c378-4565-82c7-e0b203559dc5", "script": "if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.obtainApprovals){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Trade Compliance Review\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator){\r\r\n\ttw.local.odcRequest.appInfo.status    = \" Initiated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \" Returned to Initiator\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"Terminated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Terminated\";\r\r\n}\r\r\n\r\r\ntw.local.isTradeFo = true;\r\r\ntw.local.complianceApprovalInit = tw.epv.userRole.CACT03;", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "Act03 -  Review ODC Request by Trade Front Office", "lastModified": "1734340039107", "lastModifiedBy": "mohamed.reda", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.781599a7-8906-4384-a1b9-1e732d81a61a", "2025.781599a7-8906-4384-a1b9-1e732d81a61a"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "true", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "b781a924-7ceb-4ba1-96d6-9913ed7d1d45", "versionId": "b5c8a00c-4a4c-420f-95bd-6203311f9b9a", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"mobileReady\":[true],\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.8966d7f5-3695-48e6-afd8-0bb99a19cadf\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":134,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"d8a94184-f412-41d7-a709-32777632c7f0\"},{\"outgoing\":[\"2027.7364bbd5-7c2d-4838-8f0b-04a07bad88b1\",\"2027.09dd92bb-0e55-4eb2-8dce-821414e97a2e\",\"2027.c56dfaba-c959-4ca0-87a1-f0370eb0e1a0\"],\"incoming\":[\"2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee\",\"2027.45fdb8cc-de8e-4616-8a0d-836266666d25\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":350,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"],\"preAssignmentScript\":[\"\\r\\n\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"0\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f138f6a9-e27a-413e-86bc-ddeb6d979ad2\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"569dfbbb-7e7c-408a-85bd-ad9a4c1499af\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cffdb4cc-b87d-4e77-8a7d-191bf3c42b63\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2e3ef5c6-9725-4a66-83f1-30f509e8faba\",\"optionName\":\"parentRequestNoVis\",\"value\":\"tw.local.parentRequestNoVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6ff35c29-f501-4bae-8e42-5be94b38bfa0\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d3a1bbed-3613-4d5d-86c0-4d4df0cbc285\",\"optionName\":\"contractStageVIS\",\"value\":\"tw.local.contractStageVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d6288c3f-4203-4cf2-8f23-71767b6c1a2c\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"Editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ca6c7267-ce79-40ae-8996-3cfb89db987c\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"tw.local.multiTenorVIS\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"7f199f4d-c7a9-497a-89b4-15228c4256c4\",\"version\":\"8550\"},{\"layoutItemId\":\"1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"346cb219-0124-426d-8fc0-9cf5e483c354\",\"optionName\":\"@label\",\"value\":\"Letter Generation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e9cd7085-34e9-4873-8116-6eb83f5b1115\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8836366c-f207-4a8a-8551-ba9e6a36955a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a493f4c4-b68e-43e6-8696-b5697a7cf0d4\",\"optionName\":\"instructions\",\"value\":\"tw.local.instructions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fd7b418e-9e08-46e9-85b7-560d79075d05\",\"optionName\":\"specialInstructions\",\"value\":\"tw.local.specialInstructions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0c3420e8-8315-4c79-8a6f-cb0a78abf3e3\",\"optionName\":\"paymentTerms\",\"value\":\"tw.local.paymentTerms\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6f2a42b2-3358-4bd5-8e03-bdc4744d97f2\",\"optionName\":\"deliveryTerms\",\"value\":\"tw.local.deliveryTerms\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"02e9ddec-84a4-44d6-85d9-d787655fe348\",\"optionName\":\"regeneratedRemittanceLetterTitleVIS\",\"value\":\"tw.local.regeneratedRemittanceLetterTitleVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"88647bb4-2f96-449b-8979-bd7d21358e2f\",\"optionName\":\"DocumentGenerationVIS\",\"value\":\"\\\"Editable\\\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ac5a54e1-77c7-4437-83a1-0e70ce5648b4\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8f0ee7af-370d-486d-8f9c-3c4f94188079\",\"optionName\":\"requestTypeVIS\",\"value\":\"tw.local.requestTypeVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"65126971-a36b-4259-8949-e69e6c39188c\",\"optionName\":\"remittanceLetterButton\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3b14afe9-87d6-42dc-8be6-bfae5f4fbf66\",\"optionName\":\"documentGenerationVIS\",\"value\":\"Editable\"}],\"viewUUID\":\"64.1d99aba8-195f-4eee-ba8c-a47926bc21e2\",\"binding\":\"tw.local.odcRequest.GeneratedDocumentInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"bf13348a-57c9-46f5-8b74-cba703b6aade\",\"version\":\"8550\"},{\"layoutItemId\":\"2\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4b7335dc-565a-4874-87fb-d6b5faf147d6\",\"optionName\":\"@label\",\"value\":\"Customer Info\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"12f7655f-8be9-42c7-82d8-fed7984dc3c2\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1c4ab8ed-1743-4726-8da8-92ad4490ee1c\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7975da5f-cc23-467f-8936-3cb87a9fb4bf\",\"optionName\":\"customerInfoVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"a1bb5a88-33a1-4a0c-8264-48202758152d\",\"version\":\"8550\"},{\"layoutItemId\":\"4\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"53c207e1-4e34-4e9f-826f-313f99a55eab\",\"optionName\":\"@label\",\"value\":\"Financial Details - Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ffcc6dc8-d25a-4f68-8c9e-7cc29724f213\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"69ba7164-e143-463f-87cb-765e8ed61190\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a0802725-4c63-4a8b-891d-a5cb1d5604b0\",\"optionName\":\"requestTradeFoVis\",\"value\":\"\\\"READONLY\\\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"be85e188-326e-4c9b-8632-f5559cf76111\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"7021f345-19f1-4c0c-8103-01315252d575\",\"version\":\"8550\"},{\"layoutItemId\":\"5\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"783cb246-2d9d-4ddb-87cc-33e490a2fc35\",\"optionName\":\"@label\",\"value\":\"Fc Collection\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4e21faf2-ce42-4a21-864e-80ccdda2e9eb\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8ed0576a-89e4-4fb5-82b5-206b1af02b43\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bd9a438f-896a-4f54-8c40-155179ca2cd9\",\"optionName\":\"customerCif\",\"value\":\"tw.local.odcRequest.CustomerInfo.cif\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fa5c3743-bd71-47a0-8a19-72baea19669a\",\"optionName\":\"activityType\",\"value\":\"write\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a874402b-f961-46fd-8b68-4e14770085aa\",\"optionName\":\"FCVIS\",\"value\":\"EDITABLE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9f8124fc-2f29-4867-8fb0-ae3eb6068c6d\",\"optionName\":\"addBtnVIS\",\"value\":\"EDITABLE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5e70b11e-e185-4efc-8673-47b3fd4066f5\",\"optionName\":\"negotiatedExchangeRateVIS\",\"value\":\"EDITABLE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"714193d4-39b3-4d6b-8b38-642787c113ca\",\"optionName\":\"collectionCurrencyVIS\",\"value\":\"EDITABLE\"}],\"viewUUID\":\"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe\",\"binding\":\"tw.local.odcRequest.FcCollections\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"223910ad-acc4-4fa9-8355-84dbb81e27ef\",\"version\":\"8550\"},{\"layoutItemId\":\"6\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5f436f93-934f-4a58-8ea1-85651ffb6de4\",\"optionName\":\"@label\",\"value\":\"Financial Details Trade FO\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"88372dd2-08ae-433e-8cd1-7358fda2cdb3\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"843ce6aa-9fb6-49cc-8f11-30491f52957f\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9083f0fd-5e15-4c85-84bf-7d94ff4f974b\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ee7ac591-231a-41fa-8479-eaf2d847f09e\",\"optionName\":\"act3VIS\",\"value\":\"EDITABLE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"25027f58-e250-4d5b-83fc-6cc975b57268\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"tw.local.multiTenorVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0835b4d7-eecb-44bb-88c8-aedc155524e6\",\"optionName\":\"todayDate\",\"value\":\"tw.local.todayDate\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"21358ad2-d5ea-4a84-8c13-ab31d3f60cd4\",\"optionName\":\"financialDetailsFOVis\",\"value\":\"EDITABLE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c9188f17-0100-4f17-8067-a2d014ee9f6b\",\"optionName\":\"documentAmount\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.documentAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"861e2e65-73e5-43c2-8e54-c2b7ca68882f\",\"optionName\":\"amountAdvanced\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\"}],\"viewUUID\":\"64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1\",\"binding\":\"tw.local.odcRequest.FinancialDetailsFO\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"e5f3d5cb-112c-4aee-86b9-c504e06f5d0f\",\"version\":\"8550\"},{\"layoutItemId\":\"7\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b1af5108-2f0c-4577-8a18-d5a9546c3c62\",\"optionName\":\"@label\",\"value\":\"Importer Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"63acc8fb-2ebc-4e32-8298-667fc98cad2b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3c58150e-97f2-48c4-8bed-6f765743d111\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cbe01ade-bf5e-4893-86b9-9964ba611787\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2fc7759a-1a33-4425-80c8-fa2a2ee55828\",\"optionName\":\"importerDetailsCVVIS\",\"value\":\"EDITABLE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d0b6409f-964d-4788-83cd-e44a45b87f60\",\"optionName\":\"bankRefVIS\",\"value\":\"tw.local.bankRefVIS\"}],\"viewUUID\":\"64.0ff96fd8-0740-4d17-887e-a56c8ef7921b\",\"binding\":\"tw.local.odcRequest.ImporterDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"a8221971-e5c4-4206-80f1-daba253145a0\",\"version\":\"8550\"},{\"layoutItemId\":\"8\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"aabac55e-4de5-49a5-8598-0745307fde70\",\"optionName\":\"@label\",\"value\":\"Products\\/Shipment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"65f1c620-138c-488c-889c-50fd2bae4e61\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d043bbe6-77d7-4118-8d39-b0f516f89978\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9918b730-99b8-48ef-845e-6429f43a76f3\",\"optionName\":\"productsVis\",\"value\":\"EDITABLE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0776602a-d237-43f7-8c7b-dc9ce4877bf7\",\"optionName\":\"shipmentVis\",\"value\":\"EDITABLE\"}],\"viewUUID\":\"64.5c068fcf-**************-6bb898b38609\",\"binding\":\"tw.local.odcRequest.ProductShipmentDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"53cbc386-3cb6-45a7-8054-d82635f3fcf9\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment_Comments_View1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"96edc205-324f-4941-8ed6-d3141ae5980c\",\"optionName\":\"@label\",\"value\":\"Attachment \"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"deb225d5-aae7-42dd-8f40-cc96b236244f\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97a68835-5a68-4cff-8473-7353d6e553d1\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8b66b932-805d-44b0-8e0d-5a8a8d0ea686\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7ae2bc8d-4459-4888-802e-719d979d6e47\",\"optionName\":\"remittanceLetterPath\",\"value\":\"tw.local.odcRequest.folderPath\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1269ec1b-5fe8-4378-84ee-7838e73a60ea\",\"optionName\":\"canUpdate\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d7518069-ce29-4bfc-81ba-e8f19b15d269\",\"optionName\":\"canCreate\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ba270e81-f60f-45ae-8ac5-948dabb6bcae\",\"optionName\":\"canDelete\",\"value\":\"true\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f5bd2e4d-**************-3776041b1aca\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"dfa2acb0-dd29-40a8-8344-df43cfbe5163\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4339aa5f-ab64-404f-858f-fe1bac3cadc5\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fd3cbd69-be5a-45de-8b15-74ed20c3397b\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"3d5d75ec-**************-134856b1ad9f\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"4f59508d-9e4b-4845-8f49-4b3cbefe2edf\"}],\"layoutItemId\":\"Tab_Section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2f8fccd3-9879-4e3e-8e48-17d3c6e63e01\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"29c98562-024e-47da-845a-3abfd51a7b46\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"462b95ce-b660-4c4a-8bfc-70f72c026223\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5601885e-a6bf-4bc1-8bec-2a7630731cba\",\"optionName\":\"colorStyle\",\"value\":\"P\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bccc37a3-b72d-43c8-8c95-cd4e3290ee52\",\"optionName\":\"sizeStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"S\\\"}]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cac524a9-787a-4f00-86e2-7e4893f304fa\",\"optionName\":\"tabsStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"S\\\"}]}\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f883482a-d226-4e45-8ef1-1188a8ded128\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"9c799fe1-8ec9-407f-842e-c0edaeb25c88\"}],\"layoutItemId\":\"ODC_Templete\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"181464ab-fb2d-416a-81cc-ecc576909a18\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"142337c6-2374-4a83-8447-c556028e0e09\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c890191c-653a-4b5e-8503-6e1133efe5e7\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"366b9b64-0ed4-43c6-88a6-5d2039676b3e\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"48937872-ef9b-4a24-8922-9ffcf4bf8c84\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d26f50e7-e132-4f11-82b0-3da304589c0f\",\"optionName\":\"buttonName\",\"value\":\"Submit\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6ced93af-e593-48f3-87e2-313d6a562130\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7dc8b8c7-0732-4d7d-82c2-6450dc15b713\",\"optionName\":\"approvals\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4de1b979-652e-4708-8395-ccec614834a4\",\"optionName\":\"complianceApprovalVis\",\"value\":\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d0cd978a-f34a-4fd7-8364-2441474fe56e\",\"optionName\":\"complianceApproval\",\"value\":\"tw.local.odcRequest.complianceApproval\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"07b2709f-2a5e-4ce6-8338-36f0d2712514\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"55448eb2-51a4-4c07-85f2-188208e65039\",\"optionName\":\"terminateReasonVIS\",\"value\":\"Editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ca00f5ca-9853-4159-8e3f-e63ef850afc0\",\"optionName\":\"errorPanelVIS\",\"value\":\"tw.local.errorPanelVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"806fb3bf-2688-4be1-8575-c047ba072833\",\"optionName\":\"data\",\"value\":\"tw.local.data\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b986abec-db9f-4b82-8aa9-c01113bfa345\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4643fde4-547c-41e0-8699-b3c97b07c3b3\",\"optionName\":\"approvalCommentVIS\",\"value\":\"Editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2d090fec-94cf-4f52-8f6e-480de100a595\",\"optionName\":\"approvalComment\",\"value\":\"tw.local.odcRequest.approvalComment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6f385165-1e71-4ceb-8edb-a81f46f4084c\",\"optionName\":\"returnReasonVIS\",\"value\":\"Editable\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c97079e2-54a3-47b6-8b18-520d773d5b71\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"Editable\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"55b14373-0c36-405d-82c1-28759f704dee\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2ffac703-9aa3-4a25-8833-26ba994865d7\",\"optionName\":\"tradeFoComment\",\"value\":\"tw.local.odcRequest.tradeFoComment\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"43f8c445-ed27-49f8-80ad-08fc3e514007\",\"optionName\":\"exeHubMkrComment\",\"value\":\"tw.local.odcRequest.exeHubMkrComment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ebf9c974-c56b-4d74-8981-154e3504b6f3\",\"optionName\":\"compcheckerCommentVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"76e221a2-d0a6-41b1-8c05-7474aedf17c4\",\"optionName\":\"compcheckerComment\",\"value\":\"tw.local.odcRequest.compcheckerComment\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"4329d93d-28af-4675-8b80-5ee05652e2ca\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"ACT03\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6\"},{\"incoming\":[\"2027.269226ea-58bc-4c21-8656-0b70d03b8a9d\",\"2027.2aec9ad6-a7f6-44f4-8521-b5f0e5230e6f\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1825,\"y\":246,\"declaredType\":\"TNodeVisualInfo\",\"height\":44}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"0a02d88c-8873-4302-bec6-1c1de85d6bcb\"},{\"targetRef\":\"2025.0e26d665-90eb-44c1-8abc-bc08cb0f1382\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8966d7f5-3695-48e6-afd8-0bb99a19cadf\",\"sourceRef\":\"d8a94184-f412-41d7-a709-32777632c7f0\"},{\"outgoing\":[\"2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee\"],\"incoming\":[\"2027.7364bbd5-7c2d-4838-8f0b-04a07bad88b1\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":338,\"y\":73,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.b0fbc0da-a09c-4440-ac8b-42fd4b101471\"},{\"targetRef\":\"2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To ACT03\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee\",\"sourceRef\":\"2025.b0fbc0da-a09c-4440-ac8b-42fd4b101471\"},{\"outgoing\":[\"2027.ea7e2c87-16ed-433e-812a-2d3bd3eaf548\",\"2027.cde813e2-a829-4401-8930-9ef9d87a3ed2\"],\"incoming\":[\"2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39\"],\"default\":\"2027.cde813e2-a829-4401-8930-9ef9d87a3ed2\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":662,\"y\":195,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.818bf28b-f2ae-4ac8-804f-4746a15bf907\"},{\"targetRef\":\"2025.b8b40c99-c378-4565-82c7-e0b203559dc5\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ea7e2c87-16ed-433e-812a-2d3bd3eaf548\",\"sourceRef\":\"2025.818bf28b-f2ae-4ac8-804f-4746a15bf907\"},{\"incoming\":[\"2027.cde813e2-a829-4401-8930-9ef9d87a3ed2\",\"2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":663,\"y\":272,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.f4849f60-5988-4446-846c-6292a77e6235\"},{\"targetRef\":\"2025.f4849f60-5988-4446-846c-6292a77e6235\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.cde813e2-a829-4401-8930-9ef9d87a3ed2\",\"sourceRef\":\"2025.818bf28b-f2ae-4ac8-804f-4746a15bf907\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7be4e13d-730d-47ef-8257-b1f5959b8629\"},{\"startQuantity\":1,\"outgoing\":[\"2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39\"],\"incoming\":[\"2027.09dd92bb-0e55-4eb2-8dce-821414e97a2e\"],\"default\":\"2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":524,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.e83abf20-ae23-429e-8767-21c270da66f3\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/ lctest\\r\\n\\r\\n\"]}},{\"targetRef\":\"2025.818bf28b-f2ae-4ac8-804f-4746a15bf907\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39\",\"sourceRef\":\"2025.e83abf20-ae23-429e-8767-21c270da66f3\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorPanelVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.73f2a521-d0df-4aa5-882a-a5b6c5514edb\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.screenName = \\\"\\\";\\nautoObject.userRole = \\\"\\\";\\nautoObject.complianceApproval = false;\\nautoObject.lastStepAction = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"documentation\":[{\"content\":[\"&quot;screenName,userRole,complianceApproval,lastAction&quot;\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ca89426f-2d9c-4c9a-89c3-6bdd5b9e0cff\"},{\"startQuantity\":1,\"outgoing\":[\"2027.45fdb8cc-de8e-4616-8a0d-836266666d25\"],\"incoming\":[\"2027.8966d7f5-3695-48e6-afd8-0bb99a19cadf\"],\"default\":\"2027.45fdb8cc-de8e-4616-8a0d-836266666d25\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":220,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Init\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.0e26d665-90eb-44c1-8abc-bc08cb0f1382\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.errorMessage=\\\"\\\";\\r\\ntw.local.errorPanelVIS=\\\"NONE\\\";\\r\\ntw.local.todayDate = new Date();\\r\\n\\r\\ntw.local.odcRequest.stepLog={};\\r\\ntw.local.odcRequest.stepLog.startTime =new Date();\\r\\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT03;\\r\\n\\r\\ntw.local.actionConditions = {};\\r\\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\\r\\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT03;\\r\\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\\r\\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;\\r\\n\\r\\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\\r\\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\\r\\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\\r\\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\\r\\n\\/*app log*\\/\\r\\ntw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT03;\\r\\n\\/*Visibilty Conditions*\\/\\r\\n\\/*Basic Details CV Visibility*\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nif(tw.local.odcRequest.requestNature.value == \\\"update\\\"){\\r\\n\\ttw.local.parentRequestNoVIS = \\\"READONLY\\\";\\r\\n}\\t\\r\\nelse{\\r\\n\\ttw.local.parentRequestNoVIS = \\\"None\\\";\\r\\n}\\t\\r\\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\\r\\n\\ttw.local.contractStageVIS = \\\"READONLY\\\";\\r\\n}\\r\\nelse{\\r\\ntw.local.contractStageVIS = \\\"NONE\\\";\\r\\n}\\r\\n\\/*Document Generation Section*\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\\r\\n\\ttw.local.requestTypeVIS = \\\"EDITABLE\\\";\\r\\n}\\r\\nelse{\\r\\n\\ttw.local.requestTypeVIS = \\\"NONE\\\";\\t\\r\\n}\\r\\n\\/*Importer Details Visibility*\\/\\r\\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\\r\\n\\ttw.local.bankRefVIS = \\\"EDITABLE\\\";\\r\\n}\\r\\nelse{\\r\\n\\ttw.local.bankRefVIS = \\\"NONE\\\";\\t\\r\\n}\\r\\n\\/* Financial Trade Fo CV*\\/\\r\\nif(tw.local.odcRequest.BasicDetails.paymentTerms.name == \\\"001\\\")\\r\\n{\\r\\n\\ttw.local.multiTenorVIS = \\\"None\\\";\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\ttw.local.multiTenorVIS = \\\"Editable\\\";\\r\\n}\\r\\n\\r\\ntw.local.errorPanelVIS =\\\"NONE\\\";\"]}},{\"targetRef\":\"2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To ACT03\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.45fdb8cc-de8e-4616-8a0d-836266666d25\",\"sourceRef\":\"2025.0e26d665-90eb-44c1-8abc-bc08cb0f1382\"},{\"targetRef\":\"2025.b0fbc0da-a09c-4440-ac8b-42fd4b101471\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"aad39d1c-e4ae-466b-8af6-90895eb69761\",\"coachEventPath\":\"ODC_Templete\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topLeft\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Stay on page\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7364bbd5-7c2d-4838-8f0b-04a07bad88b1\",\"sourceRef\":\"2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6\"},{\"targetRef\":\"2025.e83abf20-ae23-429e-8767-21c270da66f3\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"36feda5f-00be-4e45-80df-c58d12662fc4\",\"coachEventPath\":\"ODC_Templete\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Validation\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.09dd92bb-0e55-4eb2-8dce-821414e97a2e\",\"sourceRef\":\"2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"paymentTerms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.fc2d8a3f-c3da-46eb-8fb5-03e050dad8b9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"deliveryTerms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1c49391c-f218-4695-8dff-82c7d1c7cd5c\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"specialInstructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.60d73a01-3809-45e5-8fba-d9ae4d3eeff5\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"instructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6e026a99-79b3-4333-80a2-f2c8cc8443a6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"bankRefVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a5a07bae-507c-4d8f-8dfd-81f0f918b749\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.959f4b0b-d341-4f08-82d7-2a08de825c9e\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ca3e8cea-6c8f-49c9-82a8-7fc8977df572\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNoVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.32eeeee9-250e-490d-8ea5-4b3207a67158\"},{\"startQuantity\":1,\"outgoing\":[\"2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221\"],\"incoming\":[\"2027.ea7e2c87-16ed-433e-812a-2d3bd3eaf548\"],\"default\":\"2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":734,\"y\":174,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"setting status and substatus\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.b8b40c99-c378-4565-82c7-e0b203559dc5\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.obtainApprovals){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"In Approval\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Trade Compliance Review\\\";\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"In Execution\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Execution Hub Processing\\\";\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\" Initiated\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\" Returned to Initiator\\\";\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"Terminated\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Terminated\\\";\\r\\n}\\r\\n\\r\\ntw.local.isTradeFo = true;\\r\\ntw.local.complianceApprovalInit = tw.epv.userRole.CACT03;\"]}},{\"targetRef\":\"2025.de22d752-6311-442f-8c63-c262f8b43e2f\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221\",\"sourceRef\":\"2025.b8b40c99-c378-4565-82c7-e0b203559dc5\"},{\"outgoing\":[\"2027.e2bdd977-118b-4958-8541-ff8cc78749c6\"],\"incoming\":[\"2027.3ac8c43a-7bd5-423a-8742-9c4cda2db78d\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":1113,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.e2bdd977-118b-4958-8541-ff8cc78749c6\",\"name\":\"Audit Request History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.userRole.CACT03\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.9184a73b-f996-43a3-8ed6-03ad856ded84\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"multiTenorVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ace7b4f7-f88a-4d67-8262-95acf9aba106\"},{\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"todayDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.fa2b8453-fb03-4fb9-8894-6e93458fa1ec\"},{\"outgoing\":[\"2027.3ac8c43a-7bd5-423a-8742-9c4cda2db78d\",\"2027.58d243ec-961a-4a01-8294-b778e78e1606\"],\"incoming\":[\"2027.ed752262-7bf6-4518-846f-f84544221317\"],\"default\":\"2027.58d243ec-961a-4a01-8294-b778e78e1606\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1014,\"y\":194,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9\"},{\"targetRef\":\"2025.9184a73b-f996-43a3-8ed6-03ad856ded84\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3ac8c43a-7bd5-423a-8742-9c4cda2db78d\",\"sourceRef\":\"2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9\"},{\"incoming\":[\"2027.58d243ec-961a-4a01-8294-b778e78e1606\",\"2027.e3d83667-75be-401d-851f-eec6708f35e0\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1117,\"y\":261,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 2\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.14ba3c70-b807-4f43-81ff-58dae08ea932\"},{\"targetRef\":\"2025.14ba3c70-b807-4f43-81ff-58dae08ea932\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.58d243ec-961a-4a01-8294-b778e78e1606\",\"sourceRef\":\"2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9\"},{\"outgoing\":[\"2027.ed752262-7bf6-4518-846f-f84544221317\"],\"incoming\":[\"2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221\"],\"extensionElements\":{\"postAssignmentScript\":[\"\\/\\/console.log(\\\"audit odc request- after\\\");\\r\\n\\/\\/\\r\\n\\/\\/console.log(\\\"tw.local.reqID=  \\\"+ tw.local.reqID); \\r\\n\\/\\/console.log(\\\"tw.local.sql=  \\\"+ tw.local.sqlOut); \\r\\n\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":845,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\/\\/console.log(\\\"audit odc request- before\\\");\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.ed752262-7bf6-4518-846f-f84544221317\",\"name\":\"Audit ODC Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.de22d752-6311-442f-8c63-c262f8b43e2f\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.254cf8eb-2743-4c53-8c52-e51c8c22884e\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.reqID\"]}}],\"sourceRef\":[\"2055.59dc474f-3e47-40ee-8737-ad21d25eb436\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlOut\"]}}],\"sourceRef\":[\"2055.0d27ff21-d378-41ac-801e-065cf08cc7a7\"]}],\"calledElement\":\"1.7ee96dd0-834b-44cb-af41-b21585627e49\"},{\"outgoing\":[\"2027.1fabcc9f-905c-4b1a-8100-4c9dd4743d0a\",\"2027.e3d83667-75be-401d-851f-eec6708f35e0\"],\"incoming\":[\"2027.e2bdd977-118b-4958-8541-ff8cc78749c6\"],\"default\":\"2027.e3d83667-75be-401d-851f-eec6708f35e0\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1224,\"y\":194,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.5fb79679-5296-4f7e-82b0-bebf028b1805\"},{\"targetRef\":\"2025.4f73ec24-8706-4d4d-8516-ae3a0527faae\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1fabcc9f-905c-4b1a-8100-4c9dd4743d0a\",\"sourceRef\":\"2025.5fb79679-5296-4f7e-82b0-bebf028b1805\"},{\"targetRef\":\"2025.14ba3c70-b807-4f43-81ff-58dae08ea932\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e3d83667-75be-401d-851f-eec6708f35e0\",\"sourceRef\":\"2025.5fb79679-5296-4f7e-82b0-bebf028b1805\"},{\"targetRef\":\"2025.5fb79679-5296-4f7e-82b0-bebf028b1805\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e2bdd977-118b-4958-8541-ff8cc78749c6\",\"sourceRef\":\"2025.9184a73b-f996-43a3-8ed6-03ad856ded84\"},{\"targetRef\":\"2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightBottom\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ed752262-7bf6-4518-846f-f84544221317\",\"sourceRef\":\"2025.de22d752-6311-442f-8c63-c262f8b43e2f\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"reqID\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.e42f00d5-947b-443a-8834-9c104b72614e\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sqlOut\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.64443cae-13ab-4279-808b-f62e850cefec\"},{\"outgoing\":[\"2027.45b9bb2b-daf3-4734-8f12-91207ffb5354\",\"2027.269226ea-58bc-4c21-8656-0b70d03b8a9d\"],\"incoming\":[\"2027.1fabcc9f-905c-4b1a-8100-4c9dd4743d0a\"],\"default\":\"2027.269226ea-58bc-4c21-8656-0b70d03b8a9d\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1292,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"terminate?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.4f73ec24-8706-4d4d-8516-ae3a0527faae\"},{\"targetRef\":\"2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.terminateRequest\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Cancel and Delete Transactions\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.45b9bb2b-daf3-4734-8f12-91207ffb5354\",\"sourceRef\":\"2025.4f73ec24-8706-4d4d-8516-ae3a0527faae\"},{\"startQuantity\":1,\"outgoing\":[\"2027.e81ad409-403d-4c95-8931-6942fdab5769\"],\"incoming\":[\"2027.45b9bb2b-daf3-4734-8f12-91207ffb5354\"],\"default\":\"2027.e81ad409-403d-4c95-8931-6942fdab5769\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1398,\"y\":223,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.requestIdStr = tw.local.odcRequest.requestID+\\\"\\\";\"]},\"name\":\"Cancel and Delete Transactions\",\"dataInputAssociation\":[{\"targetRef\":\"2055.8c25b7d2-65fe-4ffa-83d3-f054a2ad4209\",\"assignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestIdStr\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1\",\"calledElement\":\"1.812db3ff-6589-474c-bcc5-21fde39e4d25\"},{\"targetRef\":\"2025.800694b0-0ed1-4b77-8191-203e5997c78d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e81ad409-403d-4c95-8931-6942fdab5769\",\"sourceRef\":\"2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1\"},{\"targetRef\":\"0a02d88c-8873-4302-bec6-1c1de85d6bcb\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.269226ea-58bc-4c21-8656-0b70d03b8a9d\",\"sourceRef\":\"2025.4f73ec24-8706-4d4d-8516-ae3a0527faae\"},{\"outgoing\":[\"2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b\"],\"incoming\":[\"2027.e81ad409-403d-4c95-8931-6942fdab5769\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":1573,\"y\":223,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b\",\"name\":\"cancel request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.55a76aa1-e513-4fd3-835f-7fe160120af7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.77d2b3d6-2a2a-4520-ad08-31686157d431\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderID\"]}}]},{\"targetRef\":\"2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.800694b0-0ed1-4b77-8191-203e5997c78d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a\"]}],\"calledElement\":\"1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc\"},{\"targetRef\":\"2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b\",\"sourceRef\":\"2025.800694b0-0ed1-4b77-8191-203e5997c78d\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.83ca9e93-201f-4a63-84e5-6eae400e2529\"},{\"incoming\":[\"2027.e57900ea-c688-4f8d-8a1f-8493318c353a\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1695,\"y\":394,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 3\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.c3ee5235-6560-41cb-8e1d-a211a809dac6\"},{\"outgoing\":[\"2027.2aec9ad6-a7f6-44f4-8521-b5f0e5230e6f\",\"2027.e57900ea-c688-4f8d-8a1f-8493318c353a\"],\"incoming\":[\"2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b\"],\"default\":\"2027.e57900ea-c688-4f8d-8a1f-8493318c353a\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1710,\"y\":249,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc\"},{\"targetRef\":\"0a02d88c-8873-4302-bec6-1c1de85d6bcb\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Copy of To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2aec9ad6-a7f6-44f4-8521-b5f0e5230e6f\",\"sourceRef\":\"2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc\"},{\"targetRef\":\"2025.c3ee5235-6560-41cb-8e1d-a211a809dac6\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Copy of To Stay on page 3\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e57900ea-c688-4f8d-8a1f-8493318c353a\",\"sourceRef\":\"2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc\"},{\"outgoing\":[\"2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba\"],\"incoming\":[\"2027.c56dfaba-c959-4ca0-87a1-f0370eb0e1a0\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":525,\"y\":264,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba\",\"name\":\"Get accounts\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0bc98ce8-10aa-460a-8857-8a8600dafc90\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.FcCollections.currency.value\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.e9e88217-d06d-4cc0-8294-3396855e634f\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.FcCollections.listOfAccounts\"]}}],\"sourceRef\":[\"2055.208f75bb-611b-4393-83d6-d60470c4a6a4\"]}],\"calledElement\":\"1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f\"},{\"targetRef\":\"2025.f4849f60-5988-4446-846c-6292a77e6235\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Stay on page 1\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba\",\"sourceRef\":\"2025.e9e88217-d06d-4cc0-8294-3396855e634f\"},{\"targetRef\":\"2025.e9e88217-d06d-4cc0-8294-3396855e634f\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"ee6ece3a-f788-4872-80e7-2b4a61bed235\",\"coachEventPath\":\"FC_Collections_CV1\\/getAccountNumber\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get accounts\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c56dfaba-c959-4ca0-87a1-f0370eb0e1a0\",\"sourceRef\":\"2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestIdStr\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6383974a-e9d0-4540-8a06-43807b690fb1\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"invalidTabs\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.8b4dd351-a8c0-4772-8d15-40bd9222d88f\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"7c97aaaf-287d-40cc-993d-d92795ac761b\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"6d189eaa-003f-4a1b-a0de-1959a75645d9\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Act03 -  Review ODC Request by Trade Front Office\",\"declaredType\":\"globalUserTask\",\"id\":\"1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.ddf3e65a-a820-4c08-9ab8-7d8c06e7eb7d\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isTradeFo\",\"isCollection\":false,\"id\":\"2055.b65aba1a-dd56-4a0c-8bd9-ecc16e9d6d70\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"complianceApprovalInit\",\"isCollection\":false,\"id\":\"2055.2c437293-db72-4278-8707-cc40bf84c167\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.f2f62331-fbf9-44a4-80d7-ee53dbd42401\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"6fb2a5bc-ae02-4cb1-80af-aae250f6a49c\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"b2e50612-a0c2-4bfa-809b-3085f3d09e07\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.769dc134-1d15-4dd4-a967-c5f61cf352dc\",\"epvProcessLinkId\":\"7570be27-beb4-45fc-89a9-e5846b3f13b8\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"f4f5f0bd-65e4-4758-8d59-cad8423ef3b5\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"98da4046-3ffc-4541-88b0-02dfc76f7260\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"4c50366c-4032-4176-bcae-593ce9835c54\"}],\"outputSet\":[{\"id\":\"03153c72-456e-46aa-889e-4fcc06fb0edb\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = {};\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = {};\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = [];\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = [];\\nautoObject.FcCollections.listOfAccounts[0] = {};\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = [];\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = {};\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.Drawer = {};\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = {};\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = {};\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = {};\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = {};\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.c3e60b22-5baf-4042-9000-ca67a0ed85e2\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"regeneratedRemittanceLetterTitleVIS\",\"isCollection\":false,\"id\":\"2055.9a7f935d-4a17-4085-8d13-ccfa64144506\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.2371dd53-f0ea-4b5e-8cf9-155e1386a13f\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"1a4ecc38-7df3-4d31-ae67-0a2abed4be6d\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c3e60b22-5baf-4042-9000-ca67a0ed85e2", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "c723f4ab-1553-4435-9b20-23639719d051", "versionId": "1e02c492-7780-4536-95b6-a1b631fd527d"}, {"name": "regeneratedRemittanceLetterTitleVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.9a7f935d-4a17-4085-8d13-ccfa64144506", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "dc4a21d7-8d60-4718-b68b-2a6ac82ae3f3", "versionId": "ea88a9f2-f9fa-4c67-b657-fef2ac3fc251"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2371dd53-f0ea-4b5e-8cf9-155e1386a13f", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "22362971-ea9e-4e83-80be-8d7cfdee561e", "versionId": "2b63c3cc-8f91-494e-a02c-e67bc01c977a"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ddf3e65a-a820-4c08-9ab8-7d8c06e7eb7d", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4bc06ecd-6b17-4268-90c1-df29ad10018b", "versionId": "f78195d1-d17c-4a8c-8e98-d1cb105a9c3f"}, {"name": "isTradeFo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b65aba1a-dd56-4a0c-8bd9-ecc16e9d6d70", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6f6eb8cd-7154-4683-8e13-241ea5b4730a", "versionId": "9720332a-**************-4d4d3d078bc3"}, {"name": "complianceApprovalInit", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2c437293-db72-4278-8707-cc40bf84c167", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "35682900-6a9e-4755-8b04-a547e23aeacc", "versionId": "bd111ab6-6aaa-4198-a27e-6ccda5ea5995"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7be4e13d-730d-47ef-8257-b1f5959b8629", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "01096d89-7897-48a5-92a9-33cfd5bcb13e", "versionId": "dd7e63e7-4e7e-403f-ac54-35a9d7cd63ee"}, {"name": "errorPanelVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.73f2a521-d0df-4aa5-882a-a5b6c5514edb", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a6487a59-b0a1-4b60-8d24-453f2ea672d8", "versionId": "6567e41f-e481-4eff-b19a-************"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ca89426f-2d9c-4c9a-89c3-6bdd5b9e0cff", "description": "&quot;screenName,userRole,complianceApproval,lastAction&quot;", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ee533519-46df-40b2-9203-11a2cf924030", "versionId": "65ad7585-c8c8-4a3a-9d5e-531dc3c765be"}, {"name": "paymentTerms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.fc2d8a3f-c3da-46eb-8fb5-03e050dad8b9", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "94664437-f73c-4b29-a493-914d402a7d1d", "versionId": "f5292cbb-185d-4d47-86b3-e0a1dead1585"}, {"name": "deliveryTerms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1c49391c-f218-4695-8dff-82c7d1c7cd5c", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "756048ad-26dd-4114-b9a0-af7cc27a8152", "versionId": "4325c773-2e3e-4ddc-b464-bcb7559f42e7"}, {"name": "specialInstructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.60d73a01-3809-45e5-8fba-d9ae4d3eeff5", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c824f1d4-915a-4925-b59c-550690655a0b", "versionId": "7c22f71b-5cc0-49f9-a833-48a60f68e1d3"}, {"name": "instructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6e026a99-79b3-4333-80a2-f2c8cc8443a6", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3d8eab84-009f-41e3-bd52-151d257ed308", "versionId": "fea5ed9a-6cb5-46e7-9616-dd8b6eed30ab"}, {"name": "bankRefVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a5a07bae-507c-4d8f-8dfd-81f0f918b749", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c0e277a8-7ceb-4587-b87e-4caccf975c21", "versionId": "1ff9ce7d-d590-4222-96c4-163bbbfdef53"}, {"name": "requestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.959f4b0b-d341-4f08-82d7-2a08de825c9e", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "cd445c97-1966-4e93-9580-481054fc6e78", "versionId": "dda6eda0-ce65-4b31-b54e-3de390eb098c"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ca3e8cea-6c8f-49c9-82a8-7fc8977df572", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4ceea244-fa8e-4c54-b28c-b103de7eecbb", "versionId": "b4e26143-088a-4a2c-bfad-c72cbfe8bba2"}, {"name": "parentRequestNoVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.32eeeee9-250e-490d-8ea5-4b3207a67158", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "2863317b-a400-452a-a691-c4a91981e124", "versionId": "26e9d152-58ee-46bf-b080-a11ab7d6a2a5"}, {"name": "multiTenorVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ace7b4f7-f88a-4d67-8262-95acf9aba106", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "12", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "58c805c3-0ea4-4658-8a71-842ba0ac8d6a", "versionId": "8440ec27-ad5b-429f-947b-75034f846cd1"}, {"name": "todayDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.fa2b8453-fb03-4fb9-8894-6e93458fa1ec", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "13", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "33134e99-96b8-4e1d-9a4b-2e79f65765ed", "versionId": "e9cc5103-2eab-490e-acf0-86d7b3441368"}, {"name": "reqID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.e42f00d5-947b-443a-8834-9c104b72614e", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "14", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "8f96888f-1a07-4cf7-b38e-7a97d2320de8", "versionId": "6a57ae34-d417-412e-af24-a49132170bdc"}, {"name": "sqlOut", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.64443cae-13ab-4279-808b-f62e850cefec", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "15", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "14cda69f-71f9-4ebc-97a2-2c6163eb8f0f", "versionId": "3d40b4e6-945f-49fa-8616-727ddfccb50a"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.83ca9e93-201f-4a63-84e5-6eae400e2529", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "16", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "eb23be44-24e4-49e2-881a-36774e99a1fa", "versionId": "16ca6a63-b2c0-4106-9e24-3fe053198170"}, {"name": "requestIdStr", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6383974a-e9d0-4540-8a06-43807b690fb1", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "17", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "460369fe-7ff0-41dc-b8c3-3798149e7562", "versionId": "d483ba04-4f67-42d7-b0c7-110196d07c67"}, {"name": "invalidTabs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8b4dd351-a8c0-4772-8d15-40bd9222d88f", "description": {"isNull": "true"}, "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "namespace": "2", "seq": "18", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a5bb4882-3ebd-40f5-94e8-d8bef6d94610", "versionId": "1bc125d2-e9c2-4744-9fb5-655c4ce4a30b"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.781599a7-8906-4384-a1b9-1e732d81a61a", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.485b6e12-1c9e-4bab-8cf1-443e64db8a12", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ef0d56db9ddf1554:-58d13970:189ac07c2bb:2d7c", "versionId": "08985715-3686-4094-b708-fb4c078b8058", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.dbf05597-219e-4f03-a2be-be0eeace86d1", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.6d779f87-cc1c-4d5b-8a51-a6e1d7bb8f53", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ef0d56db9ddf1554:-58d13970:189ac07c2bb:2d7d", "versionId": "2ac5dbe6-fd0a-4aae-993f-1fa6cd178f68", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.6d779f87-cc1c-4d5b-8a51-a6e1d7bb8f53", "haltProcess": "false", "guid": "7a31fb67-5998-432d-9625-da5917b45b43", "versionId": "793ca837-67b1-4fed-90a8-0282c95f01de"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9184a73b-f996-43a3-8ed6-03ad856ded84", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "Audit Request History", "tWComponentName": "SubProcess", "tWComponentId": "3012.a5fe5fc9-28cb-4644-9a29-a424ba9ab06a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:415d794a2c221205:3dfd662b:18a3676f5a2:-18f7", "versionId": "392dcfbb-ae28-4046-aade-08fdbd3d7026", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.a5fe5fc9-28cb-4644-9a29-a424ba9ab06a", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "9b1af1c8-3ef0-490b-a508-719b7e65bfcc", "versionId": "9be00c5a-f63f-4e0a-a929-2fe75a6277ab"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "Cancel and Delete Transactions", "tWComponentName": "SubProcess", "tWComponentId": "3012.c2c309a1-1963-42b4-b1c0-ab293058a88e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:31a7", "versionId": "65c80e14-05bf-49e8-accd-38364630588f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.c2c309a1-1963-42b4-b1c0-ab293058a88e", "attachedProcessRef": "/1.812db3ff-6589-474c-bcc5-21fde39e4d25", "guid": "bc4eb164-ff85-4af3-b0a2-96516307df55", "versionId": "dd2a6c44-0043-4e40-b852-895a738cd46c"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e9e88217-d06d-4cc0-8294-3396855e634f", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "Get accounts", "tWComponentName": "SubProcess", "tWComponentId": "3012.d57b1106-474b-4b3c-8d12-43aa25b8be17", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:2b1528dec8afbe86:-********:18be81b4797:-7543", "versionId": "9eaa4794-8090-4a81-ab15-19b36074a406", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.d57b1106-474b-4b3c-8d12-43aa25b8be17", "attachedProcessRef": "/1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f", "guid": "2214d15f-91c6-4cf2-ad86-27b2889a7648", "versionId": "3e1c9385-7ef7-42ba-bf17-a5c55901b208"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.800694b0-0ed1-4b77-8191-203e5997c78d", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "cancel request", "tWComponentName": "SubProcess", "tWComponentId": "3012.0ffd8595-6143-4a0f-8be2-3b8d5930a0e0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:7d37", "versionId": "d287dc16-2987-460f-847c-5ca8959a5705", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.0ffd8595-6143-4a0f-8be2-3b8d5930a0e0", "attachedProcessRef": "/1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "guid": "51034d4d-5447-4d9c-82a6-ac08e6652ef9", "versionId": "e555498b-6a4f-498f-b743-317ee41d47f6"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.de22d752-6311-442f-8c63-c262f8b43e2f", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "name": "Audit ODC Request", "tWComponentName": "SubProcess", "tWComponentId": "3012.0fb8b680-fbd7-4d3b-859a-faf7f9a39740", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:3287", "versionId": "f0af46fb-d8fc-40f9-9e84-a022e071b022", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.0fb8b680-fbd7-4d3b-859a-faf7f9a39740", "attachedProcessRef": "/1.7ee96dd0-834b-44cb-af41-b21585627e49", "guid": "0858dcba-cfca-4ec7-9926-245f124242ab", "versionId": "5b901b33-7588-42c1-a36d-1ba446403462"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.bb826cb2-4e1a-4e12-88fa-95ebaf5d4c3b", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "guid": "ba4807a4-c041-4331-a558-1813af420bea", "versionId": "48ca38c2-9d37-4782-a81c-f28e661598a5"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.5b818026-a382-4c74-96c0-5e07e4619d41", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "guid": "cd35f7e7-ce38-47b0-ba15-777e7b033184", "versionId": "a508b471-c9a4-4f43-a412-601eef46d93c"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.034e0a53-2b90-46c2-a94f-4906c3fd4393", "epvId": "/21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "guid": "507214a1-1eff-4c3a-8adc-044b67d63bcd", "versionId": "a7b24a37-9886-44cd-a685-09ee74e2c1a8"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.d7d38195-89ad-4f41-89f5-147d5e4071be", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "guid": "1c3cd975-6edb-4461-92b9-70c0c471e124", "versionId": "ad6eeace-1552-4afa-a89b-6026cdeee6ab"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.208b32e8-2719-45f8-9220-db4eae6da028", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "guid": "9c37568d-**************-384afbb3a38b", "versionId": "f937d0d2-7e53-44e4-ac1e-ba562f6a3a74"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.f4e3a0f2-4fdd-48ca-93d8-7c02341a4b14", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "guid": "66f1defc-2dfa-49e8-90d5-0291e972a5e2", "versionId": "fc93fe10-ef8e-4622-a63a-e6d6543639d5"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "1a4ecc38-7df3-4d31-ae67-0a2abed4be6d", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "Act03 -  Review ODC Request by Trade Front Office", "id": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "6d189eaa-003f-4a1b-a0de-1959a75645d9", "ns16:startEvent": {"name": "Start", "id": "d8a94184-f412-41d7-a709-32777632c7f0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "134", "y": "200", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.8966d7f5-3695-48e6-afd8-0bb99a19cadf"}, "ns3:formTask": {"name": "ACT03", "id": "2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6", "ns16:extensionElements": {"ns3:validationStayOnPagePaths": "okbutton", "ns13:nodeVisualInfo": {"x": "350", "y": "177", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "\r\r\n"}, "ns16:incoming": ["2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee", "2027.45fdb8cc-de8e-4616-8a0d-836266666d25"], "ns16:outgoing": ["2027.7364bbd5-7c2d-4838-8f0b-04a07bad88b1", "2027.09dd92bb-0e55-4eb2-8dce-821414e97a2e", "2027.c56dfaba-c959-4ca0-87a1-f0370eb0e1a0"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "4329d93d-28af-4675-8b80-5ee05652e2ca", "ns19:layoutItemId": "ODC_Templete", "ns19:configData": [{"ns19:id": "181464ab-fb2d-416a-81cc-ecc576909a18", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "142337c6-2374-4a83-8447-c556028e0e09", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "c890191c-653a-4b5e-8503-6e1133efe5e7", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "366b9b64-0ed4-43c6-88a6-5d2039676b3e", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "48937872-ef9b-4a24-8922-9ffcf4bf8c84", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "d26f50e7-e132-4f11-82b0-3da304589c0f", "ns19:optionName": "buttonName", "ns19:value": "Submit"}, {"ns19:id": "6ced93af-e593-48f3-87e2-313d6a562130", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "7dc8b8c7-0732-4d7d-82c2-6450dc15b713", "ns19:optionName": "approvals", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "4de1b979-652e-4708-8395-ccec614834a4", "ns19:optionName": "complianceApprovalVis", "ns19:value": ""}, {"ns19:id": "d0cd978a-f34a-4fd7-8364-2441474fe56e", "ns19:optionName": "complianceApproval", "ns19:value": "tw.local.odcRequest.complianceApproval", "ns19:valueType": "dynamic"}, {"ns19:id": "07b2709f-2a5e-4ce6-8338-36f0d2712514", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "55448eb2-51a4-4c07-85f2-188208e65039", "ns19:optionName": "terminateReasonVIS", "ns19:value": "Editable"}, {"ns19:id": "ca00f5ca-9853-4159-8e3f-e63ef850afc0", "ns19:optionName": "errorPanelVIS", "ns19:value": "tw.local.errorPanelVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "806fb3bf-2688-4be1-8575-c047ba072833", "ns19:optionName": "data", "ns19:value": "tw.local.data", "ns19:valueType": "dynamic"}, {"ns19:id": "b986abec-db9f-4b82-8aa9-c01113bfa345", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "4643fde4-547c-41e0-8699-b3c97b07c3b3", "ns19:optionName": "approvalCommentVIS", "ns19:value": "Editable"}, {"ns19:id": "2d090fec-94cf-4f52-8f6e-480de100a595", "ns19:optionName": "approvalComment", "ns19:value": "tw.local.odcRequest.approvalComment", "ns19:valueType": "dynamic"}, {"ns19:id": "6f385165-1e71-4ceb-8edb-a81f46f4084c", "ns19:optionName": "returnReasonVIS", "ns19:value": "Editable"}, {"ns19:id": "c97079e2-54a3-47b6-8b18-520d773d5b71", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "Editable"}, {"ns19:id": "55b14373-0c36-405d-82c1-28759f704dee", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "2ffac703-9aa3-4a25-8833-26ba994865d7", "ns19:optionName": "tradeFoComment", "ns19:value": "tw.local.odcRequest.tradeFoComment", "ns19:valueType": "dynamic"}, {"ns19:id": "43f8c445-ed27-49f8-80ad-08fc3e514007", "ns19:optionName": "exeHubMkrComment", "ns19:value": "tw.local.odcRequest.exeHubMkrComment", "ns19:valueType": "dynamic"}, {"ns19:id": "ebf9c974-c56b-4d74-8981-154e3504b6f3", "ns19:optionName": "compcheckerCommentVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "76e221a2-d0a6-41b1-8c05-7474aedf17c4", "ns19:optionName": "compcheckerComment", "ns19:value": "tw.local.odcRequest.compcheckerComment", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "9c799fe1-8ec9-407f-842e-c0edaeb25c88", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f883482a-d226-4e45-8ef1-1188a8ded128", "ns19:layoutItemId": "Tab_Section1", "ns19:configData": [{"ns19:id": "2f8fccd3-9879-4e3e-8e48-17d3c6e63e01", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "29c98562-024e-47da-845a-3abfd51a7b46", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "462b95ce-b660-4c4a-8bfc-70f72c026223", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "5601885e-a6bf-4bc1-8bec-2a7630731cba", "ns19:optionName": "colorStyle", "ns19:value": "P"}, {"ns19:id": "bccc37a3-b72d-43c8-8c95-cd4e3290ee52", "ns19:optionName": "sizeStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"}, {"ns19:id": "cac524a9-787a-4f00-86e2-7e4893f304fa", "ns19:optionName": "tabsStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "4f59508d-9e4b-4845-8f49-4b3cbefe2edf", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "7f199f4d-c7a9-497a-89b4-15228c4256c4", "ns19:layoutItemId": "0", "ns19:configData": [{"ns19:id": "f138f6a9-e27a-413e-86bc-ddeb6d979ad2", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "569dfbbb-7e7c-408a-85bd-ad9a4c1499af", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "cffdb4cc-b87d-4e77-8a7d-191bf3c42b63", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "2e3ef5c6-9725-4a66-83f1-30f509e8faba", "ns19:optionName": "parentRequestNoVis", "ns19:value": "tw.local.parentRequestNoVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "6ff35c29-f501-4bae-8e42-5be94b38bfa0", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "NONE"}, {"ns19:id": "d3a1bbed-3613-4d5d-86c0-4d4df0cbc285", "ns19:optionName": "contractStageVIS", "ns19:value": "tw.local.contractStageVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "d6288c3f-4203-4cf2-8f23-71767b6c1a2c", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "Editable"}, {"ns19:id": "ca6c7267-ce79-40ae-8996-3cfb89db987c", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "tw.local.multiTenorVIS", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "bf13348a-57c9-46f5-8b74-cba703b6aade", "ns19:layoutItemId": "1", "ns19:configData": [{"ns19:id": "346cb219-0124-426d-8fc0-9cf5e483c354", "ns19:optionName": "@label", "ns19:value": "Letter Generation"}, {"ns19:id": "e9cd7085-34e9-4873-8116-6eb83f5b1115", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "8836366c-f207-4a8a-8551-ba9e6a36955a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "a493f4c4-b68e-43e6-8696-b5697a7cf0d4", "ns19:optionName": "instructions", "ns19:value": "tw.local.instructions", "ns19:valueType": "dynamic"}, {"ns19:id": "fd7b418e-9e08-46e9-85b7-560d79075d05", "ns19:optionName": "specialInstructions", "ns19:value": "tw.local.specialInstructions", "ns19:valueType": "dynamic"}, {"ns19:id": "0c3420e8-8315-4c79-8a6f-cb0a78abf3e3", "ns19:optionName": "paymentTerms", "ns19:value": "tw.local.paymentTerms", "ns19:valueType": "dynamic"}, {"ns19:id": "6f2a42b2-3358-4bd5-8e03-bdc4744d97f2", "ns19:optionName": "deliveryTerms", "ns19:value": "tw.local.deliveryTerms", "ns19:valueType": "dynamic"}, {"ns19:id": "02e9ddec-84a4-44d6-85d9-d787655fe348", "ns19:optionName": "regeneratedRemittanceLetterTitleVIS", "ns19:value": "tw.local.regeneratedRemittanceLetterTitleVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "88647bb4-2f96-449b-8979-bd7d21358e2f", "ns19:optionName": "DocumentGenerationVIS", "ns19:value": "\"Editable\""}, {"ns19:id": "ac5a54e1-77c7-4437-83a1-0e70ce5648b4", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "8f0ee7af-370d-486d-8f9c-3c4f94188079", "ns19:optionName": "requestTypeVIS", "ns19:value": "tw.local.requestTypeVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "65126971-a36b-4259-8949-e69e6c39188c", "ns19:optionName": "remittanceLetter<PERSON><PERSON>on", "ns19:value": "None"}, {"ns19:id": "3b14afe9-87d6-42dc-8be6-bfae5f4fbf66", "ns19:optionName": "documentGenerationVIS", "ns19:value": "Editable"}], "ns19:viewUUID": "64.1d99aba8-195f-4eee-ba8c-a47926bc21e2", "ns19:binding": "tw.local.odcRequest.GeneratedDocumentInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "a1bb5a88-33a1-4a0c-8264-48202758152d", "ns19:layoutItemId": "2", "ns19:configData": [{"ns19:id": "4b7335dc-565a-4874-87fb-d6b5faf147d6", "ns19:optionName": "@label", "ns19:value": "Customer Info"}, {"ns19:id": "12f7655f-8be9-42c7-82d8-fed7984dc3c2", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "1c4ab8ed-1743-4726-8da8-92ad4490ee1c", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "7975da5f-cc23-467f-8936-3cb87a9fb4bf", "ns19:optionName": "customerInfoVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "7021f345-19f1-4c0c-8103-01315252d575", "ns19:layoutItemId": "4", "ns19:configData": [{"ns19:id": "53c207e1-4e34-4e9f-826f-313f99a55eab", "ns19:optionName": "@label", "ns19:value": "Financial Details - Branch"}, {"ns19:id": "ffcc6dc8-d25a-4f68-8c9e-7cc29724f213", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "69ba7164-e143-463f-87cb-765e8ed61190", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "a0802725-4c63-4a8b-891d-a5cb1d5604b0", "ns19:optionName": "requestTradeFoVis", "ns19:value": "\"READONLY\""}, {"ns19:id": "be85e188-326e-4c9b-8632-f5559cf76111", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "223910ad-acc4-4fa9-8355-84dbb81e27ef", "ns19:layoutItemId": "5", "ns19:configData": [{"ns19:id": "783cb246-2d9d-4ddb-87cc-33e490a2fc35", "ns19:optionName": "@label", "ns19:value": "Fc Collection"}, {"ns19:id": "4e21faf2-ce42-4a21-864e-80ccdda2e9eb", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "8ed0576a-89e4-4fb5-82b5-206b1af02b43", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "bd9a438f-896a-4f54-8c40-155179ca2cd9", "ns19:optionName": "customerCif", "ns19:value": "tw.local.odcRequest.CustomerInfo.cif", "ns19:valueType": "dynamic"}, {"ns19:id": "fa5c3743-bd71-47a0-8a19-72baea19669a", "ns19:optionName": "activityType", "ns19:value": "write"}, {"ns19:id": "a874402b-f961-46fd-8b68-4e14770085aa", "ns19:optionName": "FCVIS", "ns19:value": "EDITABLE"}, {"ns19:id": "9f8124fc-2f29-4867-8fb0-ae3eb6068c6d", "ns19:optionName": "addBtnVIS", "ns19:value": "EDITABLE"}, {"ns19:id": "5e70b11e-e185-4efc-8673-47b3fd4066f5", "ns19:optionName": "negotiatedExchangeRateVIS", "ns19:value": "EDITABLE"}, {"ns19:id": "714193d4-39b3-4d6b-8b38-642787c113ca", "ns19:optionName": "collectionCurrencyVIS", "ns19:value": "EDITABLE"}], "ns19:viewUUID": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "ns19:binding": "tw.local.odcRequest.FcCollections"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "e5f3d5cb-112c-4aee-86b9-c504e06f5d0f", "ns19:layoutItemId": "6", "ns19:configData": [{"ns19:id": "5f436f93-934f-4a58-8ea1-85651ffb6de4", "ns19:optionName": "@label", "ns19:value": "Financial Details Trade FO"}, {"ns19:id": "88372dd2-08ae-433e-8cd1-7358fda2cdb3", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "843ce6aa-9fb6-49cc-8f11-30491f52957f", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "9083f0fd-5e15-4c85-84bf-7d94ff4f974b", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "ee7ac591-231a-41fa-8479-eaf2d847f09e", "ns19:optionName": "act3VIS", "ns19:value": "EDITABLE"}, {"ns19:id": "25027f58-e250-4d5b-83fc-6cc975b57268", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "tw.local.multiTenorVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "0835b4d7-eecb-44bb-88c8-aedc155524e6", "ns19:optionName": "todayDate", "ns19:value": "tw.local.todayDate", "ns19:valueType": "dynamic"}, {"ns19:id": "21358ad2-d5ea-4a84-8c13-ab31d3f60cd4", "ns19:optionName": "financialDetailsFOVis", "ns19:value": "EDITABLE"}, {"ns19:id": "c9188f17-0100-4f17-8067-a2d014ee9f6b", "ns19:optionName": "documentAmount", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.documentAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "861e2e65-73e5-43c2-8e54-c2b7ca68882f", "ns19:optionName": "amountAdvanced", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.amountAdvanced", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1", "ns19:binding": "tw.local.odcRequest.FinancialDetailsFO"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "a8221971-e5c4-4206-80f1-daba253145a0", "ns19:layoutItemId": "7", "ns19:configData": [{"ns19:id": "b1af5108-2f0c-4577-8a18-d5a9546c3c62", "ns19:optionName": "@label", "ns19:value": "Importer Details"}, {"ns19:id": "63acc8fb-2ebc-4e32-8298-667fc98cad2b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "3c58150e-97f2-48c4-8bed-6f765743d111", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "cbe01ade-bf5e-4893-86b9-9964ba611787", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "2fc7759a-1a33-4425-80c8-fa2a2ee55828", "ns19:optionName": "importerDetailsCVVIS", "ns19:value": "EDITABLE"}, {"ns19:id": "d0b6409f-964d-4788-83cd-e44a45b87f60", "ns19:optionName": "bankRefVIS", "ns19:value": "tw.local.bankRefVIS", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.0ff96fd8-0740-4d17-887e-a56c8ef7921b", "ns19:binding": "tw.local.odcRequest.ImporterDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "53cbc386-3cb6-45a7-8054-d82635f3fcf9", "ns19:layoutItemId": "8", "ns19:configData": [{"ns19:id": "aabac55e-4de5-49a5-8598-0745307fde70", "ns19:optionName": "@label", "ns19:value": "Products/Shipment"}, {"ns19:id": "65f1c620-138c-488c-889c-50fd2bae4e61", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "d043bbe6-77d7-4118-8d39-b0f516f89978", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "9918b730-99b8-48ef-845e-6429f43a76f3", "ns19:optionName": "productsVis", "ns19:value": "EDITABLE"}, {"ns19:id": "0776602a-d237-43f7-8c7b-dc9ce4877bf7", "ns19:optionName": "shipmentVis", "ns19:value": "EDITABLE"}], "ns19:viewUUID": "64.5c068fcf-**************-6bb898b38609", "ns19:binding": "tw.local.odcRequest.ProductShipmentDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f5bd2e4d-**************-3776041b1aca", "ns19:layoutItemId": "Attachment_Comments_View1", "ns19:configData": [{"ns19:id": "96edc205-324f-4941-8ed6-d3141ae5980c", "ns19:optionName": "@label", "ns19:value": "Attachment "}, {"ns19:id": "deb225d5-aae7-42dd-8f40-cc96b236244f", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "97a68835-5a68-4cff-8473-7353d6e553d1", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "8b66b932-805d-44b0-8e0d-5a8a8d0ea686", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "7ae2bc8d-4459-4888-802e-719d979d6e47", "ns19:optionName": "remittanceLetter<PERSON>ath", "ns19:value": "tw.local.odcRequest.folderPath", "ns19:valueType": "dynamic"}, {"ns19:id": "1269ec1b-5fe8-4378-84ee-7838e73a60ea", "ns19:optionName": "canUpdate", "ns19:value": "true"}, {"ns19:id": "d7518069-ce29-4bfc-81ba-e8f19b15d269", "ns19:optionName": "canCreate", "ns19:value": "true"}, {"ns19:id": "ba270e81-f60f-45ae-8ac5-948dabb6bcae", "ns19:optionName": "canDelete", "ns19:value": "true"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "3d5d75ec-**************-134856b1ad9f", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "dfa2acb0-dd29-40a8-8344-df43cfbe5163", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "4339aa5f-ab64-404f-858f-fe1bac3cadc5", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "fd3cbd69-be5a-45de-8b15-74ed20c3397b", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:endEvent": {"name": "End", "id": "0a02d88c-8873-4302-bec6-1c1de85d6bcb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1825", "y": "246", "width": "24", "height": "44", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.269226ea-58bc-4c21-8656-0b70d03b8a9d", "2027.2aec9ad6-a7f6-44f4-8521-b5f0e5230e6f"]}, "ns16:sequenceFlow": [{"sourceRef": "d8a94184-f412-41d7-a709-32777632c7f0", "targetRef": "2025.0e26d665-90eb-44c1-8abc-bc08cb0f1382", "name": "To Coach", "id": "2027.8966d7f5-3695-48e6-afd8-0bb99a19cadf", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.b0fbc0da-a09c-4440-ac8b-42fd4b101471", "targetRef": "2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6", "name": "To ACT03", "id": "2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.818bf28b-f2ae-4ac8-804f-4746a15bf907", "targetRef": "2025.b8b40c99-c378-4565-82c7-e0b203559dc5", "name": "Yes", "id": "2027.ea7e2c87-16ed-433e-812a-2d3bd3eaf548", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.818bf28b-f2ae-4ac8-804f-4746a15bf907", "targetRef": "2025.f4849f60-5988-4446-846c-6292a77e6235", "name": "No", "id": "2027.cde813e2-a829-4401-8930-9ef9d87a3ed2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}}, {"sourceRef": "2025.e83abf20-ae23-429e-8767-21c270da66f3", "targetRef": "2025.818bf28b-f2ae-4ac8-804f-4746a15bf907", "name": "To Valid?", "id": "2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}}, {"sourceRef": "2025.0e26d665-90eb-44c1-8abc-bc08cb0f1382", "targetRef": "2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6", "name": "To ACT03", "id": "2027.45fdb8cc-de8e-4616-8a0d-836266666d25", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6", "targetRef": "2025.b0fbc0da-a09c-4440-ac8b-42fd4b101471", "name": "To Stay on page", "id": "2027.7364bbd5-7c2d-4838-8f0b-04a07bad88b1", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topLeft", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "aad39d1c-e4ae-466b-8af6-90895eb69761", "ns3:coachEventPath": "ODC_Templete/saveState"}}}, {"sourceRef": "2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6", "targetRef": "2025.e83abf20-ae23-429e-8767-21c270da66f3", "name": "To Validation", "id": "2027.09dd92bb-0e55-4eb2-8dce-821414e97a2e", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "36feda5f-00be-4e45-80df-c58d12662fc4", "ns3:coachEventPath": "ODC_Templete/submit"}}}, {"sourceRef": "2025.b8b40c99-c378-4565-82c7-e0b203559dc5", "targetRef": "2025.de22d752-6311-442f-8c63-c262f8b43e2f", "name": "To End", "id": "2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9", "targetRef": "2025.9184a73b-f996-43a3-8ed6-03ad856ded84", "name": "Yes", "id": "2027.3ac8c43a-7bd5-423a-8742-9c4cda2db78d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9", "targetRef": "2025.14ba3c70-b807-4f43-81ff-58dae08ea932", "name": "No", "id": "2027.58d243ec-961a-4a01-8294-b778e78e1606", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.5fb79679-5296-4f7e-82b0-bebf028b1805", "targetRef": "2025.4f73ec24-8706-4d4d-8516-ae3a0527faae", "name": "", "id": "2027.1fabcc9f-905c-4b1a-8100-4c9dd4743d0a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.5fb79679-5296-4f7e-82b0-bebf028b1805", "targetRef": "2025.14ba3c70-b807-4f43-81ff-58dae08ea932", "name": "No", "id": "2027.e3d83667-75be-401d-851f-eec6708f35e0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.9184a73b-f996-43a3-8ed6-03ad856ded84", "targetRef": "2025.5fb79679-5296-4f7e-82b0-bebf028b1805", "name": "To Audited?", "id": "2027.e2bdd977-118b-4958-8541-ff8cc78749c6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.de22d752-6311-442f-8c63-c262f8b43e2f", "targetRef": "2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9", "name": "To Audited?", "id": "2027.ed752262-7bf6-4518-846f-f84544221317", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightBottom", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.4f73ec24-8706-4d4d-8516-ae3a0527faae", "targetRef": "2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1", "name": "To Cancel and Delete Transactions", "id": "2027.45b9bb2b-daf3-4734-8f12-91207ffb5354", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.terminateRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1", "targetRef": "2025.800694b0-0ed1-4b77-8191-203e5997c78d", "name": "Yes", "id": "2027.e81ad409-403d-4c95-8931-6942fdab5769", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.4f73ec24-8706-4d4d-8516-ae3a0527faae", "targetRef": "0a02d88c-8873-4302-bec6-1c1de85d6bcb", "name": "To End", "id": "2027.269226ea-58bc-4c21-8656-0b70d03b8a9d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.800694b0-0ed1-4b77-8191-203e5997c78d", "targetRef": "2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc", "name": "To End", "id": "2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc", "targetRef": "0a02d88c-8873-4302-bec6-1c1de85d6bcb", "name": "<PERSON><PERSON> of To End", "id": "2027.2aec9ad6-a7f6-44f4-8521-b5f0e5230e6f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc", "targetRef": "2025.c3ee5235-6560-41cb-8e1d-a211a809dac6", "name": "Co<PERSON> of To Stay on page 3", "id": "2027.e57900ea-c688-4f8d-8a1f-8493318c353a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.e9e88217-d06d-4cc0-8294-3396855e634f", "targetRef": "2025.f4849f60-5988-4446-846c-6292a77e6235", "name": "To Stay on page 1", "id": "2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.9efa5933-76b1-42d0-b7e6-8fda3a740ff6", "targetRef": "2025.e9e88217-d06d-4cc0-8294-3396855e634f", "name": "To Get accounts", "id": "2027.c56dfaba-c959-4ca0-87a1-f0370eb0e1a0", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "ee6ece3a-f788-4872-80e7-2b4a61bed235", "ns3:coachEventPath": "FC_Collections_CV1/getAccountNumber"}}}], "ns16:intermediateThrowEvent": [{"name": "Stay on page", "id": "2025.b0fbc0da-a09c-4440-ac8b-42fd4b101471", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "338", "y": "73", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee"}, "ns16:incoming": "2027.7364bbd5-7c2d-4838-8f0b-04a07bad88b1", "ns16:outgoing": "2027.5c96e79a-0575-4e8d-8a08-a1d19b8c96ee", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.f4849f60-5988-4446-846c-6292a77e6235", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "663", "y": "272", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.cde813e2-a829-4401-8930-9ef9d87a3ed2", "2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba"], "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 2", "id": "2025.14ba3c70-b807-4f43-81ff-58dae08ea932", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1117", "y": "261", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.58d243ec-961a-4a01-8294-b778e78e1606", "2027.e3d83667-75be-401d-851f-eec6708f35e0"], "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 3", "id": "2025.c3ee5235-6560-41cb-8e1d-a211a809dac6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1695", "y": "394", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.e57900ea-c688-4f8d-8a1f-8493318c353a", "ns3:stayOnPageEventDefinition": ""}], "ns16:exclusiveGateway": [{"default": "2027.cde813e2-a829-4401-8930-9ef9d87a3ed2", "name": "Valid?", "id": "2025.818bf28b-f2ae-4ac8-804f-4746a15bf907", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "662", "y": "195", "width": "32", "height": "32"}}, "ns16:incoming": "2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39", "ns16:outgoing": ["2027.ea7e2c87-16ed-433e-812a-2d3bd3eaf548", "2027.cde813e2-a829-4401-8930-9ef9d87a3ed2"]}, {"default": "2027.58d243ec-961a-4a01-8294-b778e78e1606", "name": "Audited?", "id": "2025.1f3078c8-7378-40a7-8bca-c42f5c1032a9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1014", "y": "194", "width": "32", "height": "32"}}, "ns16:incoming": "2027.ed752262-7bf6-4518-846f-f84544221317", "ns16:outgoing": ["2027.3ac8c43a-7bd5-423a-8742-9c4cda2db78d", "2027.58d243ec-961a-4a01-8294-b778e78e1606"]}, {"default": "2027.e3d83667-75be-401d-851f-eec6708f35e0", "name": "Audited?", "id": "2025.5fb79679-5296-4f7e-82b0-bebf028b1805", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1224", "y": "194", "width": "32", "height": "32"}}, "ns16:incoming": "2027.e2bdd977-118b-4958-8541-ff8cc78749c6", "ns16:outgoing": ["2027.1fabcc9f-905c-4b1a-8100-4c9dd4743d0a", "2027.e3d83667-75be-401d-851f-eec6708f35e0"]}, {"default": "2027.269226ea-58bc-4c21-8656-0b70d03b8a9d", "name": "terminate?", "id": "2025.4f73ec24-8706-4d4d-8516-ae3a0527faae", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1292", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.1fabcc9f-905c-4b1a-8100-4c9dd4743d0a", "ns16:outgoing": ["2027.45b9bb2b-daf3-4734-8f12-91207ffb5354", "2027.269226ea-58bc-4c21-8656-0b70d03b8a9d"]}, {"default": "2027.e57900ea-c688-4f8d-8a1f-8493318c353a", "name": "Exclusive Gateway", "id": "2025.6f1f0ca4-9fff-4a9a-8681-488cf71d11bc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1710", "y": "249", "width": "32", "height": "32"}}, "ns16:incoming": "2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b", "ns16:outgoing": ["2027.2aec9ad6-a7f6-44f4-8521-b5f0e5230e6f", "2027.e57900ea-c688-4f8d-8a1f-8493318c353a"]}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.7be4e13d-730d-47ef-8257-b1f5959b8629"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorPanelVIS", "id": "2056.73f2a521-d0df-4aa5-882a-a5b6c5514edb"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.ca89426f-2d9c-4c9a-89c3-6bdd5b9e0cff", "ns16:documentation": {"_": "&quot;screenName,userRole,complianceApproval,lastAction&quot;", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.screenName = \"\";\r\nautoObject.userRole = \"\";\r\nautoObject.complianceApproval = false;\r\nautoObject.lastStepAction = \"\";\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "paymentTerms", "id": "2056.fc2d8a3f-c3da-46eb-8fb5-03e050dad8b9"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "deliveryTerms", "id": "2056.1c49391c-f218-4695-8dff-82c7d1c7cd5c"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "specialInstructions", "id": "2056.60d73a01-3809-45e5-8fba-d9ae4d3eeff5"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "instructions", "id": "2056.6e026a99-79b3-4333-80a2-f2c8cc8443a6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "bankRefVIS", "id": "2056.a5a07bae-507c-4d8f-8dfd-81f0f918b749"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestTypeVIS", "id": "2056.959f4b0b-d341-4f08-82d7-2a08de825c9e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.ca3e8cea-6c8f-49c9-82a8-7fc8977df572"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNoVIS", "id": "2056.32eeeee9-250e-490d-8ea5-4b3207a67158"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "multiTenorVIS", "id": "2056.ace7b4f7-f88a-4d67-8262-95acf9aba106"}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "todayDate", "id": "2056.fa2b8453-fb03-4fb9-8894-6e93458fa1ec"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "reqID", "id": "2056.e42f00d5-947b-443a-8834-9c104b72614e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sqlOut", "id": "2056.64443cae-13ab-4279-808b-f62e850cefec"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.83ca9e93-201f-4a63-84e5-6eae400e2529"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestIdStr", "id": "2056.6383974a-e9d0-4540-8a06-43807b690fb1"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "true", "name": "invalidTabs", "id": "2056.8b4dd351-a8c0-4772-8d15-40bd9222d88f"}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "default": "2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39", "name": "Validation", "id": "2025.e83abf20-ae23-429e-8767-21c270da66f3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "524", "y": "176", "width": "95", "height": "70", "color": "#95D087"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.09dd92bb-0e55-4eb2-8dce-821414e97a2e", "ns16:outgoing": "2027.1ce33e57-f1c2-4b0a-83c3-2d7dbe990c39", "ns16:script": "// lctest\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "default": "2027.45fdb8cc-de8e-4616-8a0d-836266666d25", "name": "Init", "id": "2025.0e26d665-90eb-44c1-8abc-bc08cb0f1382", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "220", "y": "177", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.8966d7f5-3695-48e6-afd8-0bb99a19cadf", "ns16:outgoing": "2027.45fdb8cc-de8e-4616-8a0d-836266666d25", "ns16:script": "tw.local.errorMessage=\"\";\r\r\ntw.local.errorPanelVIS=\"NONE\";\r\r\ntw.local.todayDate = new Date();\r\r\n\r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime =new Date();\r\r\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT03;\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT03;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;\r\r\n\r\r\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\r\r\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\r\r\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\r\r\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\r\r\n/*app log*/\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT03;\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\r\n\ttw.local.parentRequestNoVIS = \"READONLY\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\n/*Document Generation Section*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.requestTypeVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.requestTypeVIS = \"NONE\";\t\r\r\n}\r\r\n/*Importer Details Visibility*/\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.bankRefVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.bankRefVIS = \"NONE\";\t\r\r\n}\r\r\n/* Financial Trade Fo CV*/\r\r\nif(tw.local.odcRequest.BasicDetails.paymentTerms.name == \"001\")\r\r\n{\r\r\n\ttw.local.multiTenorVIS = \"None\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.multiTenorVIS = \"Editable\";\r\r\n}\r\r\n\r\r\ntw.local.errorPanelVIS =\"NONE\";"}, {"scriptFormat": "text/x-javascript", "default": "2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221", "name": "setting status and substatus", "id": "2025.b8b40c99-c378-4565-82c7-e0b203559dc5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "734", "y": "174", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.ea7e2c87-16ed-433e-812a-2d3bd3eaf548", "ns16:outgoing": "2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221", "ns16:script": "if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.obtainApprovals){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Trade Compliance Review\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator){\r\r\n\ttw.local.odcRequest.appInfo.status    = \" Initiated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \" Returned to Initiator\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"Terminated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Terminated\";\r\r\n}\r\r\n\r\r\ntw.local.isTradeFo = true;\r\r\ntw.local.complianceApprovalInit = tw.epv.userRole.CACT03;"}], "ns16:callActivity": [{"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "default": "2027.e2bdd977-118b-4958-8541-ff8cc78749c6", "name": "Audit Request History", "id": "2025.9184a73b-f996-43a3-8ed6-03ad856ded84", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1113", "y": "175", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": ""}, "ns16:incoming": "2027.3ac8c43a-7bd5-423a-8742-9c4cda2db78d", "ns16:outgoing": "2027.e2bdd977-118b-4958-8541-ff8cc78749c6", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.epv.userRole.CACT03", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "default": "2027.ed752262-7bf6-4518-846f-f84544221317", "name": "Audit ODC Request", "id": "2025.de22d752-6311-442f-8c63-c262f8b43e2f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "845", "y": "175", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "//console.log(\"audit odc request- before\");", "ns3:postAssignmentScript": "//console.log(\"audit odc request- after\");\r\r\n//\r\r\n//console.log(\"tw.local.reqID=  \"+ tw.local.reqID); \r\r\n//console.log(\"tw.local.sql=  \"+ tw.local.sqlOut); \r\r\n"}, "ns16:incoming": "2027.12dcdcb6-0678-4f5e-82a4-2c6c3426c221", "ns16:outgoing": "2027.ed752262-7bf6-4518-846f-f84544221317", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.afad40c5-a38b-475c-8154-b4dabd94b6fe", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.254cf8eb-2743-4c53-8c52-e51c8c22884e", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.59dc474f-3e47-40ee-8737-ad21d25eb436", "ns16:assignment": {"ns16:to": {"_": "tw.local.reqID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:sourceRef": "2055.0d27ff21-d378-41ac-801e-065cf08cc7a7", "ns16:assignment": {"ns16:to": {"_": "tw.local.sqlOut", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.812db3ff-6589-474c-bcc5-21fde39e4d25", "default": "2027.e81ad409-403d-4c95-8931-6942fdab5769", "name": "Cancel and Delete Transactions", "id": "2025.0dea6c40-1c28-435b-8e3f-1a3fe2fba7e1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1398", "y": "223", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.requestIdStr = tw.local.odcRequest.requestID+\"\";"}, "ns16:incoming": "2027.45b9bb2b-daf3-4734-8f12-91207ffb5354", "ns16:outgoing": "2027.e81ad409-403d-4c95-8931-6942fdab5769", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.8c25b7d2-65fe-4ffa-83d3-f054a2ad4209", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestIdStr", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}}}, {"calledElement": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b", "name": "cancel request", "id": "2025.800694b0-0ed1-4b77-8191-203e5997c78d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1573", "y": "223", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.e81ad409-403d-4c95-8931-6942fdab5769", "ns16:outgoing": "2027.e2cf69b4-9dbf-4eb2-8411-2d162479fa9b", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.55a76aa1-e513-4fd3-835f-7fe160120af7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.77d2b3d6-2a2a-4520-ad08-31686157d431", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:targetRef": "2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f", "default": "2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba", "name": "Get accounts", "id": "2025.e9e88217-d06d-4cc0-8294-3396855e634f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "525", "y": "264", "width": "95", "height": "70"}}, "ns16:incoming": "2027.c56dfaba-c959-4ca0-87a1-f0370eb0e1a0", "ns16:outgoing": "2027.c7cc87b7-10f6-45c7-8ca4-4f77214bd8ba", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0bc98ce8-10aa-460a-8857-8a8600dafc90", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.FcCollections.currency.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.208f75bb-611b-4393-83d6-d60470c4a6a4", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.FcCollections.listOfAccounts", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.d2e5a15a-ea53-4793-9e93-29af5bd80b13"}}}}], "ns3:htmlHeaderTag": {"id": "7c97aaaf-287d-40cc-993d-d92795ac761b", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:mobileReady": "true", "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "6fb2a5bc-ae02-4cb1-80af-aae250f6a49c"}, {"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "b2e50612-a0c2-4bfa-809b-3085f3d09e07"}, {"epvId": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "epvProcessLinkId": "7570be27-beb4-45fc-89a9-e5846b3f13b8"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "f4f5f0bd-65e4-4758-8d59-cad8423ef3b5"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "98da4046-3ffc-4541-88b0-02dfc76f7260"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.f2f62331-fbf9-44a4-80d7-ee53dbd42401"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.c3e60b22-5baf-4042-9000-ca67a0ed85e2", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = {};\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = [];\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = [];\r\nautoObject.FcCollections.listOfAccounts[0] = {};\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = [];\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = {};\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.Drawer = {};\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = {};\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = {};\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = {};\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = {};\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "useDefault": "false"}}}, {"name": "regeneratedRemittanceLetterTitleVIS", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.9a7f935d-4a17-4085-8d13-ccfa64144506"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.2371dd53-f0ea-4b5e-8cf9-155e1386a13f"}], "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.ddf3e65a-a820-4c08-9ab8-7d8c06e7eb7d"}, {"name": "isTradeFo", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.b65aba1a-dd56-4a0c-8bd9-ecc16e9d6d70"}, {"name": "complianceApprovalInit", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.2c437293-db72-4278-8707-cc40bf84c167"}], "ns16:inputSet": {"id": "4c50366c-4032-4176-bcae-593ce9835c54"}, "ns16:outputSet": {"id": "03153c72-456e-46aa-889e-4fcc06fb0edb"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.0b0f420d-7321-4a36-8504-5b0769f80722", "processId": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.781599a7-8906-4384-a1b9-1e732d81a61a", "2025.781599a7-8906-4384-a1b9-1e732d81a61a"], "endStateId": "Out", "toProcessItemId": ["2025.dbf05597-219e-4f03-a2be-be0eeace86d1", "2025.dbf05597-219e-4f03-a2be-be0eeace86d1"], "guid": "e5b6476a-dcb4-4a12-86f9-c133f7046f26", "versionId": "2a8d9399-ad02-4341-bf45-d35f4c80557d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}