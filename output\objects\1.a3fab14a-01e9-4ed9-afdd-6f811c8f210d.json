{"id": "1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "versionId": "82bbc204-9432-4081-b8a0-104b2219474c", "name": "BoxUtils", "type": "process", "typeName": "Process", "details": {"processType": "11"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "name": "BoxUtils", "lastModified": "1691583171045", "lastModifiedBy": "abdelrahman.saleh", "processId": "1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": [{"isNull": "true"}, {"isNull": "true"}], "isRootProcess": "false", "processType": "11", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": {"isNull": "true"}, "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "true", "externalId": "_406b38c7-7d45-41e9-97b0-619e439685fb", "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4b79", "versionId": "82bbc204-9432-4081-b8a0-104b2219474c", "dependencySummary": {"isNull": "true"}, "jsonData": {"isNull": "true"}, "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns14:definitions": {"xmlns:ns14": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns16": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns17": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/uitheme", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "id": "_0d994755-ce24-419d-b573-9bffc5f80b5e", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns14:interface": {"name": "BoxUtils", "id": "_e8190576-61dd-477e-ba84-2a511a8ac276", "ns14:documentation": "", "ns14:operation": [{"name": "main", "id": "_24757661-f847-4705-83dc-8ad8f2ef5ffc", "ns14:documentation": "", "ns14:extensionElements": {"ns17:businessSignature": {"ns17:input": {"id": "_b581b93d-d58f-4ee5-af11-5c75f5c8d21d", "ns17:name": "Parameter 1", "ns17:description": "Parameter 1", "ns17:typeRef": "{http://www.w3.org/2001/XMLSchema}string", "ns17:typeRefPO": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "ns17:path": "Parameter 1", "ns17:isCollection": "true", "ns17:isOptional": "false"}}}}, {"name": "generateBarcode", "id": "_d9bb1876-6c68-4dd3-911c-d2550bb89434", "ns14:documentation": "", "ns14:extensionElements": {"ns17:businessSignature": {"ns17:input": {"id": "_a181d5f4-514a-4499-93d8-0d824eea2d5f", "ns17:name": "Parameter 1", "ns17:description": "Parameter 1", "ns17:typeRef": "{http://www.w3.org/2001/XMLSchema}string", "ns17:typeRefPO": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "ns17:path": "Parameter 1", "ns17:isCollection": "false", "ns17:isOptional": "false"}, "ns17:output": {"id": "_1ad9bd31-bbe2-48ec-9d09-d250b18c8f7b", "ns17:name": "Return Value", "ns17:typeRef": "{http://www.w3.org/2001/XMLSchema}string", "ns17:typeRefPO": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "ns17:path": "Return Value", "ns17:isCollection": "false", "ns17:isOptional": "false"}}}}]}, "ns14:endPoint": {"ns14:extensionElements": {"ns15:externalService": {"interfaceRef": "_e8190576-61dd-477e-ba84-2a511a8ac276", "name": "BoxUtils", "id": "_406b38c7-7d45-41e9-97b0-619e439685fb", "ns15:javaBinding": {"className": "BoxUtils", "ns15:methodBindings": [{"name": "main", "ns15:inputParameter": {"name": "Parameter 1", "type": "[Ljava.lang.String;"}}, {"name": "generateBarcode", "ns15:inputParameter": {"name": "Parameter 1", "type": "java.lang.String"}, "ns15:outputParameter": {"name": "Return Value", "type": "java.lang.String"}}], "ns15:endpoints": {"ns15:jarFileRef": "61.59e818ca-0ece-415d-8e13-aa7a74c0d527"}}, "ns15:cachingType": "false"}}}}}}}}, "subType": "11", "hasDetails": false}