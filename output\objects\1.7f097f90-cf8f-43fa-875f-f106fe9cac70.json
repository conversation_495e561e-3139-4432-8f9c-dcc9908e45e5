{"id": "1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "versionId": "f6012b23-364c-4dcc-9b2f-62a0cad72961", "name": "Document Generation", "type": "process", "typeName": "Process", "details": {"processType": "11"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "name": "Document Generation", "lastModified": "1693480732407", "lastModifiedBy": "abdelrahman.saleh", "processId": "1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": [{"isNull": "true"}, {"isNull": "true"}], "isRootProcess": "false", "processType": "11", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": {"isNull": "true"}, "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "true", "externalId": "{http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}_4f57dabd-7522-45c1-a6cb-ae30e4578c97", "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-176d", "versionId": "f6012b23-364c-4dcc-9b2f-62a0cad72961", "dependencySummary": "<dependencySummary id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1762\">\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1761\">\r\n    <refId>/6023.3c0ee9b7-a4a7-4812-9e58-f2cc9dd2fd92</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1760\">\r\n      <name>externalId</name>\r\n      <value>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-175f\">\r\n      <name>mimeType</name>\r\n      <value>swagger</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-175e\">\r\n    <refId>/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-175d\">\r\n      <name>externalId</name>\r\n      <value>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-175c\">\r\n      <name>mimeType</name>\r\n      <value>xsd</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-175b\">\r\n    <refId>/12.a894d8aa-2563-4e74-8c8a-8b87c2aba05b</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-175a\">\r\n      <name>externalId</name>\r\n      <value>{http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}generateDocumentUsingPOSTRequestMsg</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1759\">\r\n      <name>mimeType</name>\r\n      <value>msg</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1758\">\r\n    <refId>/12.5ab4dfaa-bb18-4ceb-b653-8f1a20041d49</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1757\">\r\n      <name>externalId</name>\r\n      <value>{http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}generateDocumentUsingPOSTResponseMsg</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1756\">\r\n      <name>mimeType</name>\r\n      <value>msg</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n</dependencySummary>", "jsonData": {"isNull": "true"}, "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns14:definitions": {"xmlns:ns14": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns16": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns17": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/uitheme", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "id": "_efb47504-03b0-46c5-bbcc-b43e63715d08", "targetNamespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns14:interface": {"name": "IFace.4f93f3e8-36a3-4f29-ad1c-14db25a15369", "id": "_22fbd660-76cf-4cf3-9684-d5bbc960ac9d", "ns14:documentation": "Document Generation Service is a service that generate word document based on word template.\r\n The service can be work through different application with different configuration per each application like template path returned response ..etc .\r\n Service will fill document with data from request and data configured at Database. \r\n  The Word Document template that will be used should be in docx format .The variable pattern that defined at template is \\$\\{variable name\\}", "ns14:operation": {"name": "generateDocumentUsingPOST", "id": "_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed", "ns14:documentation": "", "ns14:extensionElements": {"ns3:inMessageRefPO": "12.a894d8aa-2563-4e74-8c8a-8b87c2aba05b", "ns3:outMessageRefPO": "12.5ab4dfaa-bb18-4ceb-b653-8f1a20041d49", "ns17:businessSignature": {"ns17:input": {"id": "_5f31e534-f82c-4bed-8b97-f4b50bb07fb8", "ns17:name": "request", "ns17:description": "request", "ns17:typeRef": "{http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd}DocumentGenerationRequest", "ns17:typeRefPO": "itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "ns17:path": "request", "ns17:isCollection": "false", "ns17:isOptional": "false"}, "ns17:output": {"id": "_66fa2129-ae2c-47d6-aa59-b4f60f4a6c96", "ns17:name": "generateDocumentUsingPOST_200", "ns17:description": "Document Generated Successfully", "ns17:typeRef": "{http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd}DocumentGenerationResponse", "ns17:typeRefPO": "itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc", "ns17:path": "generateDocumentUsingPOST_200", "ns17:isCollection": "false", "ns17:isOptional": "true", "ns17:httpResponseCode": "200"}}}, "ns14:inMessageRef": {"_": "ns21:generateDocumentUsingPOSTRequestMsg", "xmlns:ns21": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service"}, "ns14:outMessageRef": {"_": "ns21:generateDocumentUsingPOSTResponseMsg", "xmlns:ns21": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service"}}}, "ns14:endPoint": {"ns14:extensionElements": {"ns15:externalService": {"interfaceRef": "_22fbd660-76cf-4cf3-9684-d5bbc960ac9d", "name": "Document Generation", "id": "_4f57dabd-7522-45c1-a6cb-ae30e4578c97", "ns15:restBinding": {"xmlns:ns21": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service", "swaggerRef": "ns21:6023.3c0ee9b7-a4a7-4812-9e58-f2cc9dd2fd92", "openAPIVersion": "v2", "descriptorSrcLocation": "http://localhost:8080/v2/api-docs", "bindingRef": "2094.a093316c-cc7c-40df-85b3-6f9266360c1f", "bindingRefV2": "62.baa265b6-9a89-4025-a3ea-79bc6384c47d"}, "ns15:cachingType": "false"}}}}}}}}, "subType": "11", "hasDetails": false}