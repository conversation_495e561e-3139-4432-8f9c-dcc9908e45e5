{"id": "12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "versionId": "9621acdb-733d-40ec-8438-385f61755704", "name": "DocumentGenerationRequest", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "name": "DocumentGenerationRequest", "lastModified": "1693480732436", "lastModifiedBy": "abdelrahman.saleh", "classId": "12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "true", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "dependencySummary": "<dependencySummary id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1751\">\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1750\">\r\n    <refId>/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-174f\">\r\n      <name>externalId</name>\r\n      <value>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-174e\">\r\n      <name>mimeType</name>\r\n      <value>xsd</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n</dependencySummary>", "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"complexType\":[{\"annotation\":{\"documentation\":[{\"content\":[\"Document Generation Request\"]}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{\"namespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"typeName\":\"DocumentGenerationRequest\"}],\"shadow\":[true]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"appName\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"string\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":1}]}]},\"name\":\"appName\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"paramsValues\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"anyType\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":2}]}]},\"name\":\"paramsValues\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}ANY\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.c09c9b6e-aabd-4897-bef2-ed61db106297\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"programCode\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"string\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":3}]}]},\"name\":\"programCode\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"purposeCode\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"string\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":2147483647,\"order\":4}]}]},\"name\":\"purposeCode\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"secure\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"boolean\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":5}]}]},\"name\":\"secure\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Boolean\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"templates\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"_minOccurs\":0,\"typeName\":\"RequestTemplatePojo\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":2147483647,\"order\":6}]}]},\"name\":\"templates\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/NBEODCR}BrokenReference\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.8cf8193c-ddf5-44ec-8584-1443729deace\"}}]},\"name\":\"DocumentGenerationRequest\"}],\"id\":\"_12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063\"}", "description": "Document Generation Request", "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-176b", "versionId": "9621acdb-733d-40ec-8438-385f61755704", "definition": {"property": [{"name": "appName", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "string", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "1", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "paramsV<PERSON>ues", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "anyType", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "2", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "programCode", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "string", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "3", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "purposeCode", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "string", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "unbounded", "nillable": "false", "order": "4", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "secure", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "boolean", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "5", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "templates", "description": {"isNull": "true"}, "classRef": "/12.8cf8193c-ddf5-44ec-8584-1443729deace", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "RequestTemplatePojo", "typeNamespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "minOccurs": "0", "maxOccurs": "unbounded", "nillable": "false", "order": "6", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}], "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "DocumentGenerationRequest", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": "DocumentGenerationRequest", "namespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}