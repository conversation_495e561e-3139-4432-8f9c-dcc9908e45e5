{"id": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "versionId": "91cf47a3-59bb-4063-9c71-0c4efd9afc23", "name": "retrieve multiTenorDates", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "retrieve multiTenorDates", "lastModified": "1696416450418", "lastModifiedBy": "heba", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.5ace31fd-290d-4248-ae4a-e2e417307686", "2025.5ace31fd-290d-4248-ae4a-e2e417307686"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "c2609e3c-b32b-431e-94ec-f4bbe1891ee4", "versionId": "91cf47a3-59bb-4063-9c71-0c4efd9afc23", "dependencySummary": "<dependencySummary id=\"bpdid:cdc758d910e20d2d:-607ed7:18af686ab01:4518\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.a11d1524-8529-4374-a445-ae67a19556ad\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"48f71abd-d3b1-4f42-9c66-7df573e49c82\"},{\"incoming\":[\"e2218007-84bb-42c2-83d5-41fb56875da3\",\"4ecef72b-08ef-4508-8e6d-f4502607b681\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":759,\"y\":81,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f3f\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"7f40324c-91c8-47fb-b764-afe24f11250a\"},{\"targetRef\":\"5ace31fd-290d-4248-ae4a-e2e417307686\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.a11d1524-8529-4374-a445-ae67a19556ad\",\"sourceRef\":\"48f71abd-d3b1-4f42-9c66-7df573e49c82\"},{\"startQuantity\":1,\"outgoing\":[\"d92dbc26-e6fc-4871-90a2-df0e17ec9799\"],\"incoming\":[\"2027.a11d1524-8529-4374-a445-ae67a19556ad\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":107,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Script Task\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"5ace31fd-290d-4248-ae4a-e2e417307686\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sql = \\\"select * from odc_fctransaction where requesrid = '\\\"+tw.local.requestId+\\\"' \\\";\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"e2218007-84bb-42c2-83d5-41fb56875da3\"],\"incoming\":[\"775dde5a-6a82-401c-91fd-05a70cf6b6b8\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":477,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Mapping output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"3d471e16-34cb-459c-89a2-eaca0a0017c0\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/tw.local.odcRequest = {};\\r\\n\\/\\/tw.local.odcRequest.FinancialDetailsFO ={};\\r\\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\r\\n\\r\\nif(tw.local.sqlResults.listLength > 0)\\r\\n{\\r\\n\\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\\r\\n\\t{\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i]  = new tw.object.MultiTenorDates();\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date     = tw.local.sqlResults[0].rows[i].data[1];\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount       = tw.local.sqlResults[0].rows[i].data[0];\\r\\n\\t}\\r\\n\\r\\n}\"]}},{\"startQuantity\":1,\"outgoing\":[\"775dde5a-6a82-401c-91fd-05a70cf6b6b8\"],\"incoming\":[\"d92dbc26-e6fc-4871-90a2-df0e17ec9799\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":291,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Linked Service Flow\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"126a28c8-b816-4d86-85b9-674b302baae4\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlResults\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"126a28c8-b816-4d86-85b9-674b302baae4\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Linked Service Flow\",\"declaredType\":\"sequenceFlow\",\"id\":\"d92dbc26-e6fc-4871-90a2-df0e17ec9799\",\"sourceRef\":\"5ace31fd-290d-4248-ae4a-e2e417307686\"},{\"targetRef\":\"3d471e16-34cb-459c-89a2-eaca0a0017c0\",\"extensionElements\":{\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Mapping output\",\"declaredType\":\"sequenceFlow\",\"id\":\"775dde5a-6a82-401c-91fd-05a70cf6b6b8\",\"sourceRef\":\"126a28c8-b816-4d86-85b9-674b302baae4\"},{\"targetRef\":\"7f40324c-91c8-47fb-b764-afe24f11250a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e2218007-84bb-42c2-83d5-41fb56875da3\",\"sourceRef\":\"3d471e16-34cb-459c-89a2-eaca0a0017c0\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.69a1cdc1-cf18-47e8-9ac6-9062110b48a5\"},{\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"sqlParameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.236ce309-0585-4e6c-9a24-68cfecb46707\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"sqlResults\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.7c9ed672-7643-45b9-a119-2e65485a8540\"},{\"startQuantity\":1,\"outgoing\":[\"4ecef72b-08ef-4508-8e6d-f4502607b681\"],\"incoming\":[\"d1bf18e0-2918-4462-8b3b-c5c75bc518d3\",\"62bf6444-9225-4956-8c20-80f9e8d319ed\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":477,\"y\":188,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Retrieve Multi Tenor Dates Data\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"c8e37476-36c4-496d-844f-026adb86880a\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"parallelMultiple\":false,\"outgoing\":[\"62bf6444-9225-4956-8c20-80f9e8d319ed\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"4bb6dc7b-5ae5-427a-8335-a6e4435e7ef5\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"a1d598d7-b1da-4150-869d-4ccda902b2dc\",\"otherAttributes\":{\"eventImplId\":\"43ccbb38-754f-493f-81af-5bce754191a3\"}}],\"attachedToRef\":\"3d471e16-34cb-459c-89a2-eaca0a0017c0\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":512,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"9c09c871-45ee-4159-8a1f-79072c751467\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"d1bf18e0-2918-4462-8b3b-c5c75bc518d3\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"1bd249bd-c0ce-44c1-83c9-ce51555652cc\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"c3b7b5bc-cf1d-4a96-87cb-69b677808cde\",\"otherAttributes\":{\"eventImplId\":\"6246c384-67c2-40e4-8ce2-98d2fabc9de3\"}}],\"attachedToRef\":\"126a28c8-b816-4d86-85b9-674b302baae4\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":326,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"cc214cb4-c8ab-4bfc-8e9c-b8f4bbeec01b\",\"outputSet\":{}},{\"targetRef\":\"7f40324c-91c8-47fb-b764-afe24f11250a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"4ecef72b-08ef-4508-8e6d-f4502607b681\",\"sourceRef\":\"c8e37476-36c4-496d-844f-026adb86880a\"},{\"targetRef\":\"c8e37476-36c4-496d-844f-026adb86880a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"d1bf18e0-2918-4462-8b3b-c5c75bc518d3\",\"sourceRef\":\"cc214cb4-c8ab-4bfc-8e9c-b8f4bbeec01b\"},{\"targetRef\":\"c8e37476-36c4-496d-844f-026adb86880a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"62bf6444-9225-4956-8c20-80f9e8d319ed\",\"sourceRef\":\"9c09c871-45ee-4159-8a1f-79072c751467\"}],\"laneSet\":[{\"id\":\"ee2b2ad7-e88a-44b2-8b2b-fbc35cc71659\",\"lane\":[{\"flowNodeRef\":[\"48f71abd-d3b1-4f42-9c66-7df573e49c82\",\"7f40324c-91c8-47fb-b764-afe24f11250a\",\"5ace31fd-290d-4248-ae4a-e2e417307686\",\"3d471e16-34cb-459c-89a2-eaca0a0017c0\",\"126a28c8-b816-4d86-85b9-674b302baae4\",\"c8e37476-36c4-496d-844f-026adb86880a\",\"9c09c871-45ee-4159-8a1f-79072c751467\",\"cc214cb4-c8ab-4bfc-8e9c-b8f4bbeec01b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"8950c197-1458-430b-98a3-bed44512912c\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"retrieve multiTenorDates\",\"declaredType\":\"process\",\"id\":\"1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.597c3250-25af-42c6-b65b-7d280ac3a896\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.2631c1a1-529d-4221-9643-ef723e7bb772\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.597c3250-25af-42c6-b65b-7d280ac3a896\",\"2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"0\"}]},\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"requestId\",\"isCollection\":false,\"id\":\"2055.2631c1a1-529d-4221-9643-ef723e7bb772\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2631c1a1-529d-4221-9643-ef723e7bb772", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "b92fe04b-6b14-40ce-b275-156c3f50b859", "versionId": "ff5140c2-ad63-45f2-9a95-b3c760381a0d"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.597c3250-25af-42c6-b65b-7d280ac3a896", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "742a8845-20a4-4110-b605-468aac99aa33", "versionId": "af1f9657-dc5c-4009-bdf7-3c02461e0fdd"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "104f57fd-9583-4541-aa60-58ee34875f4e", "versionId": "8d1117d3-386d-4bbe-a85c-d11cb2a5ddea"}], "processVariable": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.69a1cdc1-cf18-47e8-9ac6-9062110b48a5", "description": {"isNull": "true"}, "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "cddcdec7-b74e-46a3-a76a-46b9e2a9046b", "versionId": "d7950005-1f3c-433f-ab2b-ba002ecfded9"}, {"name": "sqlParameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.236ce309-0585-4e6c-9a24-68cfecb46707", "description": {"isNull": "true"}, "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3696ff76-84dd-44f1-8c40-1a97793533fe", "versionId": "cbf5fe89-8854-427c-bf45-ca9fadab6583"}, {"name": "sqlResults", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7c9ed672-7643-45b9-a119-2e65485a8540", "description": {"isNull": "true"}, "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7dd52ff7-7377-4093-b976-907f3b446232", "versionId": "86315bf5-077b-47a6-8040-606f06d1fadf"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.126a28c8-b816-4d86-85b9-674b302baae4", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "Linked Service Flow", "tWComponentName": "SubProcess", "tWComponentId": "3012.480b5e4d-7854-418a-80cc-a546732474bf", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.c8e37476-36c4-496d-844f-026adb86880a", "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f41", "versionId": "294df514-4ae0-44e7-ae3e-24c96809aaae", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "291", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:3688", "errorHandlerItemId": "2025.c8e37476-36c4-496d-844f-026adb86880a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.480b5e4d-7854-418a-80cc-a546732474bf", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "f82b0cac-c31f-49cd-983e-cf89139bafb3", "versionId": "6a839a46-50bd-4f9f-b7ee-45f7576200d4", "parameterMapping": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d20f5e69-d786-4af9-9d63-128231c1ea0b", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.480b5e4d-7854-418a-80cc-a546732474bf", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "260c1d9d-ea49-4c66-b409-d8f42dd7f710", "versionId": "2f744aab-0ecc-4350-b72b-1f27d645c9af", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f8c89b6c-9cb8-4f45-9207-61731b41d0cf", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.480b5e4d-7854-418a-80cc-a546732474bf", "useDefault": "false", "value": "tw.local.sqlResults", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "a79e1ee4-0601-413a-9716-dbec766f2069", "versionId": "93f51676-bbc3-45cb-a5a0-356e466bd70c", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0b9fb830-4738-4775-acbd-ce5697f4b2dc", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.480b5e4d-7854-418a-80cc-a546732474bf", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "fa1adda5-72f1-4c39-b397-cf8280fa0e7d", "versionId": "ce40220e-9b38-414d-a239-21d947ab2cd9", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5e168553-0333-4ac3-a9db-8632242941bf", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.480b5e4d-7854-418a-80cc-a546732474bf", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "b2b1c213-1bd3-47e6-848f-ddd10987ad05", "versionId": "e90a37b8-2c7c-4126-8ab7-4974d7e20cd4", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7ed02502-a53c-4164-bbe0-ac8796ec18db", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.480b5e4d-7854-418a-80cc-a546732474bf", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "e49ae85b-63f3-4220-950e-53c1aeea553d", "versionId": "f8f68ba2-e70f-407d-b20e-2cacf883fa97", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7f40324c-91c8-47fb-b764-afe24f11250a", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.9bfbf46e-46a3-437f-84ce-ef15f52694bf", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f3f", "versionId": "620b6bbe-b59c-4c4d-952d-96619356ee37", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "759", "y": "81", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.9bfbf46e-46a3-437f-84ce-ef15f52694bf", "haltProcess": "false", "guid": "19a5b5ea-1833-4628-a04c-fb05a6b05b5a", "versionId": "68b62175-5c5d-4672-bfce-c1cb60c8f1e0"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3d471e16-34cb-459c-89a2-eaca0a0017c0", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "Mapping output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.ef82626c-1007-4cab-af89-fd709ad7d33a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.c8e37476-36c4-496d-844f-026adb86880a", "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f42", "versionId": "aee6dea9-0e43-4e9a-a834-fd9f1fb3f4d4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "477", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:3688", "errorHandlerItemId": "2025.c8e37476-36c4-496d-844f-026adb86880a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.ef82626c-1007-4cab-af89-fd709ad7d33a", "scriptTypeId": "2", "isActive": "true", "script": "//tw.local.odcRequest = {};\r\r\n//tw.local.odcRequest.FinancialDetailsFO ={};\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\n\r\r\nif(tw.local.sqlResults.listLength > 0)\r\r\n{\r\r\n\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i]  = new tw.object.MultiTenorDates();\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date     = tw.local.sqlResults[0].rows[i].data[1];\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount       = tw.local.sqlResults[0].rows[i].data[0];\r\r\n\t}\r\r\n\r\r\n}", "isRule": "false", "guid": "9ca7b465-28f9-4521-b92c-3b5592b7e4b1", "versionId": "ddde614d-9e3f-4dc2-97b6-8ef61a2e6db2"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.5ace31fd-290d-4248-ae4a-e2e417307686", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "Script Task", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.2ad9f910-a5bc-4b51-9aad-b2ce27dea6cf", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f40", "versionId": "bbb577ae-57d2-43a0-9101-f7b21215bfaf", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "107", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.2ad9f910-a5bc-4b51-9aad-b2ce27dea6cf", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sql = \"select * from odc_fctransaction where requesrid = '\"+tw.local.requestId+\"' \";\r\r\n", "isRule": "false", "guid": "1ecc0fe2-0f97-46e8-a480-7529d0002379", "versionId": "8c597e4f-a907-49af-94d8-db49c86af517"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c8e37476-36c4-496d-844f-026adb86880a", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.2c3301e1-e9c9-454d-b9ae-b95ccd6149e5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:3688", "versionId": "e878e8d3-664f-442e-8900-ec490037915d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "477", "y": "188", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.2c3301e1-e9c9-454d-b9ae-b95ccd6149e5", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "97bd5402-f678-4043-aea0-0df8b3a46d62", "versionId": "53cb3593-9e3e-4c36-b068-1d463bcff1e3", "parameterMapping": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3a86800e-6f2e-40e5-a602-64f2e6d5685b", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.2c3301e1-e9c9-454d-b9ae-b95ccd6149e5", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "9a7ec23e-0d6f-4104-ac46-b66c614c50f8", "versionId": "07edb5d6-fbbf-4fa6-9e77-268a4ff5700b", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f47e1eea-b493-4513-b1ca-1b1eeca1b16f", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.2c3301e1-e9c9-454d-b9ae-b95ccd6149e5", "useDefault": "false", "value": "\"Retrieve Multi Tenor Dates Data\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "30b96df9-9a19-4260-9c38-188a09a98384", "versionId": "631571e7-c7a2-4aef-bb85-8dbf9ac5600d", "description": {"isNull": "true"}}]}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "retrieve multiTenorDates", "id": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "requestId", "itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "id": "2055.2631c1a1-529d-4221-9643-ef723e7bb772", "ns16:extensionElements": {"ns3:defaultValue": {"_": "0", "useDefault": "false"}}}, "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.597c3250-25af-42c6-b65b-7d280ac3a896"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.2631c1a1-529d-4221-9643-ef723e7bb772"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.597c3250-25af-42c6-b65b-7d280ac3a896", "2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce"]}}, "ns16:laneSet": {"id": "ee2b2ad7-e88a-44b2-8b2b-fbc35cc71659", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "8950c197-1458-430b-98a3-bed44512912c", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["48f71abd-d3b1-4f42-9c66-7df573e49c82", "7f40324c-91c8-47fb-b764-afe24f11250a", "5ace31fd-290d-4248-ae4a-e2e417307686", "3d471e16-34cb-459c-89a2-eaca0a0017c0", "126a28c8-b816-4d86-85b9-674b302baae4", "c8e37476-36c4-496d-844f-026adb86880a", "9c09c871-45ee-4159-8a1f-79072c751467", "cc214cb4-c8ab-4bfc-8e9c-b8f4bbeec01b"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "48f71abd-d3b1-4f42-9c66-7df573e49c82", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.a11d1524-8529-4374-a445-ae67a19556ad"}, "ns16:endEvent": {"name": "End", "id": "7f40324c-91c8-47fb-b764-afe24f11250a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "759", "y": "81", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f3f"}, "ns16:incoming": ["e2218007-84bb-42c2-83d5-41fb56875da3", "4ecef72b-08ef-4508-8e6d-f4502607b681"]}, "ns16:sequenceFlow": [{"sourceRef": "48f71abd-d3b1-4f42-9c66-7df573e49c82", "targetRef": "5ace31fd-290d-4248-ae4a-e2e417307686", "name": "To End", "id": "2027.a11d1524-8529-4374-a445-ae67a19556ad", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "5ace31fd-290d-4248-ae4a-e2e417307686", "targetRef": "126a28c8-b816-4d86-85b9-674b302baae4", "name": "To Linked Service Flow", "id": "d92dbc26-e6fc-4871-90a2-df0e17ec9799", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "126a28c8-b816-4d86-85b9-674b302baae4", "targetRef": "3d471e16-34cb-459c-89a2-eaca0a0017c0", "name": "To Mapping output", "id": "775dde5a-6a82-401c-91fd-05a70cf6b6b8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "3d471e16-34cb-459c-89a2-eaca0a0017c0", "targetRef": "7f40324c-91c8-47fb-b764-afe24f11250a", "name": "To End", "id": "e2218007-84bb-42c2-83d5-41fb56875da3", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "c8e37476-36c4-496d-844f-026adb86880a", "targetRef": "7f40324c-91c8-47fb-b764-afe24f11250a", "name": "To End", "id": "4ecef72b-08ef-4508-8e6d-f4502607b681", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}, {"sourceRef": "cc214cb4-c8ab-4bfc-8e9c-b8f4bbeec01b", "targetRef": "c8e37476-36c4-496d-844f-026adb86880a", "name": "To Exception Handling", "id": "d1bf18e0-2918-4462-8b3b-c5c75bc518d3", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "9c09c871-45ee-4159-8a1f-79072c751467", "targetRef": "c8e37476-36c4-496d-844f-026adb86880a", "name": "To Exception Handling", "id": "62bf6444-9225-4956-8c20-80f9e8d319ed", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Script Task", "id": "5ace31fd-290d-4248-ae4a-e2e417307686", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "107", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.a11d1524-8529-4374-a445-ae67a19556ad", "ns16:outgoing": "d92dbc26-e6fc-4871-90a2-df0e17ec9799", "ns16:script": "tw.local.sql = \"select * from odc_fctransaction where requesrid = '\"+tw.local.requestId+\"' \";\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Mapping output", "id": "3d471e16-34cb-459c-89a2-eaca0a0017c0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "477", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "775dde5a-6a82-401c-91fd-05a70cf6b6b8", "ns16:outgoing": "e2218007-84bb-42c2-83d5-41fb56875da3", "ns16:script": "//tw.local.odcRequest = {};\r\r\n//tw.local.odcRequest.FinancialDetailsFO ={};\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\n\r\r\nif(tw.local.sqlResults.listLength > 0)\r\r\n{\r\r\n\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i]  = new tw.object.MultiTenorDates();\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date     = tw.local.sqlResults[0].rows[i].data[1];\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount       = tw.local.sqlResults[0].rows[i].data[0];\r\r\n\t}\r\r\n\r\r\n}"}], "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "Linked Service Flow", "id": "126a28c8-b816-4d86-85b9-674b302baae4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "291", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "d92dbc26-e6fc-4871-90a2-df0e17ec9799", "ns16:outgoing": "775dde5a-6a82-401c-91fd-05a70cf6b6b8", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.sqlResults", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exception Handling", "id": "c8e37476-36c4-496d-844f-026adb86880a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "477", "y": "188", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["d1bf18e0-2918-4462-8b3b-c5c75bc518d3", "62bf6444-9225-4956-8c20-80f9e8d319ed"], "ns16:outgoing": "4ecef72b-08ef-4508-8e6d-f4502607b681", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Retrieve Multi Tenor Dates Data\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.69a1cdc1-cf18-47e8-9ac6-9062110b48a5"}, {"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "sqlParameters", "id": "2056.236ce309-0585-4e6c-9a24-68cfecb46707"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "sqlResults", "id": "2056.7c9ed672-7643-45b9-a119-2e65485a8540"}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "3d471e16-34cb-459c-89a2-eaca0a0017c0", "parallelMultiple": "false", "name": "Error", "id": "9c09c871-45ee-4159-8a1f-79072c751467", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "512", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "62bf6444-9225-4956-8c20-80f9e8d319ed", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "4bb6dc7b-5ae5-427a-8335-a6e4435e7ef5"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "a1d598d7-b1da-4150-869d-4ccda902b2dc", "eventImplId": "43ccbb38-754f-493f-81af-5bce754191a3", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "126a28c8-b816-4d86-85b9-674b302baae4", "parallelMultiple": "false", "name": "Error1", "id": "cc214cb4-c8ab-4bfc-8e9c-b8f4bbeec01b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "326", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "d1bf18e0-2918-4462-8b3b-c5c75bc518d3", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "1bd249bd-c0ce-44c1-83c9-ce51555652cc"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "c3b7b5bc-cf1d-4a96-87cb-69b677808cde", "eventImplId": "6246c384-67c2-40e4-8ce2-98d2fabc9de3", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To Mapping output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.775dde5a-6a82-401c-91fd-05a70cf6b6b8", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.126a28c8-b816-4d86-85b9-674b302baae4", "2025.126a28c8-b816-4d86-85b9-674b302baae4"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.3d471e16-34cb-459c-89a2-eaca0a0017c0", "2025.3d471e16-34cb-459c-89a2-eaca0a0017c0"], "guid": "bb39193f-3a8c-4b69-8424-a0b1afba877b", "versionId": "00cb0978-ee9b-46bd-9fcd-da5fa22aa48a", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Linked Service Flow", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d92dbc26-e6fc-4871-90a2-df0e17ec9799", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.5ace31fd-290d-4248-ae4a-e2e417307686", "2025.5ace31fd-290d-4248-ae4a-e2e417307686"], "endStateId": "Out", "toProcessItemId": ["2025.126a28c8-b816-4d86-85b9-674b302baae4", "2025.126a28c8-b816-4d86-85b9-674b302baae4"], "guid": "0d71c35a-8f7b-4708-a773-295490a1fea8", "versionId": "22e0f9ac-aa80-4113-8ebf-c96b9ac7c458", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4ecef72b-08ef-4508-8e6d-f4502607b681", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c8e37476-36c4-496d-844f-026adb86880a", "2025.c8e37476-36c4-496d-844f-026adb86880a"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.7f40324c-91c8-47fb-b764-afe24f11250a", "2025.7f40324c-91c8-47fb-b764-afe24f11250a"], "guid": "634ff033-69fe-4f0c-93ee-6adfde6ad9a5", "versionId": "78f406a3-f55a-4086-bd40-775c7905aeba", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e2218007-84bb-42c2-83d5-41fb56875da3", "processId": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.3d471e16-34cb-459c-89a2-eaca0a0017c0", "2025.3d471e16-34cb-459c-89a2-eaca0a0017c0"], "endStateId": "Out", "toProcessItemId": ["2025.7f40324c-91c8-47fb-b764-afe24f11250a", "2025.7f40324c-91c8-47fb-b764-afe24f11250a"], "guid": "c04f89b3-243c-4772-9c93-d5c829dc54ac", "versionId": "9b489f47-b8a2-42f4-b117-1a7d3d66daf8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}