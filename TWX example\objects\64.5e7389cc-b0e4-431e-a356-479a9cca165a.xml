<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.5e7389cc-b0e4-431e-a356-479a9cca165a" name="DC start request template">
        <lastModified>1692013288444</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
        <isTemplate>true</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;bd85072d-7c74-4d39-8ce1-037a06009e0d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Header_View1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b5f4bc3-8427-4832-852a-c8f48faf98dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Header View&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;de5c6869-6947-4e71-8962-b6bcabb8da57&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;716e5467-6ca2-436c-8542-6799aa48ff5f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.55afafe5-9321-40fd-9b9a-cbbd556a8005&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.odcRequest.appInfo&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:GridLayoutContainer" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;fe6673cd-c9c8-4352-8f2b-2a116a7db739&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1cf5e062-c534-4392-8835-84a350a1a43b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;e3ed836d-8431-481b-80ec-fdae3f7cc017&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae7c2394-eca4-4d90-8d79-8cff10979f82&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;124b173b-a571-4d10-8953-301c7d6cd4c2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;049efd81-5559-4859-843d-93cc8c9f55b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":1},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;396d58d9-b954-46d4-81c7-c0432739407f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e0aad31-6390-44b5-8568-10911f4e9a93&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":11},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;dfddd41d-4593-4320-8d92-e12f42d4d815&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;211242dc-bb7f-49b1-8f6a-8aea00b17ca9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":11},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;51501f53-9c8b-4682-8ad8-c8eedf9afbf3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c7706d44-f248-41d5-882a-d79a986a3324&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;161b0099-bd57-42f5-8043-8a969826af8a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Start_New_Request_CV1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f05daee-c33b-42a8-8774-71d6bbcf38bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Start New Request CV&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee8a90e4-72af-4e4f-843b-517041691c2f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;43c7cc4d-0195-4a06-880c-3a34bafb47c6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;11be9154-754d-4230-858f-6f9a946ebbe7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;errorMessgae&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.errorMsg&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7c4993bc-f7d5-4689-96c6-89c5b908583f&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.odcRequest&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;e2a54f1e-e72f-4b2b-853f-dca14fed87fb&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff28bdcb-b795-4c0e-8a03-d43b9fcbff05&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":1}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;2a21989c-ae42-4e84-80b2-3d4922dcd758&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer9&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;31c90a13-3856-4bd3-8b69-c46503d60e01&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":11}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;70b9b0e5-6d16-447e-8bbe-b80c337ea08b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell9&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c738c7df-b6ef-4885-8835-3f53e009d91d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":11}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9f05068d-582f-43be-865d-11020b1ca62a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6ac9f238-57e3-4bb6-8d08-9a17436a7a1d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Error Description / تفاصيل الاخطاء&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9bb5c3cb-cf1c-422f-80b1-d84877163191&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;00782e0f-a99b-4bbd-84fd-6bfa8832aeea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7d358143-c247-426e-8e72-2c821cbbc041&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.errorPanelVIS&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7e2b0d39-4bc8-4652-8e5b-4bb7467a83c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e57dd69a-5202-41e2-8556-2fe48150692c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;lightColor&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;ab66abcc-349c-4529-864a-4e5335bf3b6e&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;12c7ff22-ffb3-4fb1-8e15-edc6491f1d71&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;109b94af-b770-41dd-8be2-11780eb43d44&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;29046878-a72b-4501-8a9e-88098d46a741&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b7c68760-c70f-49fb-8a7a-a135753d0ab0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c252a8ba-7fb3-4791-8ad5-968ecc777c92&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@className&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a029092d-88ed-4bc9-887f-60e199e6bc92&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@htmlOverrides&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b34d04e5-e7d1-499a-8506-86e37f144d7b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;G&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f9dce2b-4568-48eb-809f-0134e6070267&lt;/ns2:id&gt;&lt;ns2:optionName&gt;weightStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af1bdedc-678d-451a-8216-fcb54a76fb7f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowHTML&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.errorMsg&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction>//updateActionList();</changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>0485aa05-30c1-4749-b165-d52b4847ff13</guid>
        <versionId>3f5595f6-740a-432d-af7f-278d37e4845c</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.7b175874-654d-4450-9f43-72be0cc60235</coachViewBindingTypeId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>f80c3437-d86b-4c27-9174-688c826a3c89</guid>
            <versionId>0f4d0a5b-eeab-4456-bb11-812e3e14fd93</versionId>
        </bindingType>
        <configOption name="stepLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.aad4aba7-f9cd-4932-a6c5-db390dbbf9f9</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.e4654440-58a7-47b2-8f98-3eaa9cccad49</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>dfcf1802-28a1-437a-95f7-44ea15263455</guid>
            <versionId>1cb69c96-b9ce-4564-abc2-5c1749c2d814</versionId>
        </configOption>
        <configOption name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.cd9d3c00-04a5-4a76-ab3f-6c336587cab2</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>0c33c1a2-ba4b-4141-a9a7-e25aee618bc2</guid>
            <versionId>bd71973a-22c9-4ba8-8600-109c526664dd</versionId>
        </configOption>
        <configOption name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.c43b4184-720a-434d-93db-fa5c377ff9ae</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>7d68fe26-f8a2-4c57-8980-16d107e17811</guid>
            <versionId>9d72c78a-4e0d-43d7-a8f2-56b253727d2a</versionId>
        </configOption>
        <configOption name="complianceApproval">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.cf1864b2-f290-4bde-a3bd-3f6354444a9f</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>75740912-16d0-4d5b-9239-6c26fc75d321</guid>
            <versionId>fef2add3-acaa-4329-abf6-ccedd107a842</versionId>
        </configOption>
        <configOption name="complianceApprovalVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.da5da78b-53db-4dbd-8796-43dd6c1f97d3</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>6e818d0b-26d8-470b-b7e5-0d4782bb7f7e</guid>
            <versionId>321ced09-caa5-4338-87e8-26f75c49ed78</versionId>
        </configOption>
        <configOption name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e10bccc3-fae0-43f5-9c3c-0a7e2afa56f8</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>9d6c57de-fc3e-44c7-b908-370556cdaf4d</guid>
            <versionId>ec9d79d7-3b9d-41cf-b039-ad759f58a71c</versionId>
        </configOption>
        <configOption name="terminateReasonVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.a36ea4d8-24d4-4da0-8f43-935c51ee39a6</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>6590ef4c-e3ac-4954-a5d6-41be8322da47</guid>
            <versionId>9521412a-f9b7-4dbb-827f-2c03389154e9</versionId>
        </configOption>
        <configOption name="errorPanelVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.540a1d3e-99e0-414b-a186-a88cd3326906</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>058b9e04-8955-461d-98c2-5e8f7616e5fd</guid>
            <versionId>5397771f-9f74-45b3-8248-af4879f89bf8</versionId>
        </configOption>
        <configOption name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.bcc933d2-844c-40e1-8931-569c3576a782</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>ff1dc183-a5c7-488a-a137-1c748441a607</guid>
            <versionId>496be749-6e28-4b3b-b1bb-c7e39b2b500a</versionId>
        </configOption>
        <configOption name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e4a90aa7-93ca-45f8-9408-dc15978e4614</coachViewConfigOptionId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>adfe79ad-ab9a-4cdc-98a2-5e5152f0d5da</guid>
            <versionId>05c622e1-f6c5-4987-96e5-98c49450d463</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.030bc6b3-3f91-4b53-825b-aee460895474</coachViewInlineScriptId>
            <coachViewId>64.5e7389cc-b0e4-431e-a356-479a9cca165a</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>//this.showApprovals = function  () {&#xD;
//	if (this.context.options.hasApprovals.get("value")) {&#xD;
//		this.ui.get("approvalsLayout").setVisible(true,true);&#xD;
//		if (this.context.options.approvalsReadOnly.get("value")) {&#xD;
//			this.ui.get("approvalsLayout").setEnabled(false);&#xD;
//		}else{&#xD;
//			this.ui.get("approvalsLayout").setEnabled(true);&#xD;
//		}&#xD;
//&#xD;
//	}else{&#xD;
//&#xD;
//		this.ui.get("approvalsLayout").setVisible(false,true);&#xD;
//	}&#xD;
//	&#xD;
//}&#xD;
//&#xD;
//this.showReturnReason = function  () {&#xD;
//	if (this.context.options.hasReturnReason.get("value") &amp;&amp; ((this.context.options.selectedAction.get("value") == "Return To Trade FO") || (this.context.options.selectedAction.get("value") == "Return to Initiator") || (this.context.options.selectedAction.get("value") == "Return To Maker"))) {&#xD;
//		this.ui.get("ReturnReason").setVisible(true,true);&#xD;
//	}else{&#xD;
//		this.ui.get("ReturnReason").setVisible(false,true);&#xD;
//	}&#xD;
//}&#xD;
//&#xD;
//this.showAction = function  () {&#xD;
//	if (this.context.binding.get("value").get("subStatus") == "") {&#xD;
//		this.ui.get("Single_Select1").setVisible(false,true);&#xD;
//		this.ui.get("Text1").setVisible(false,true);&#xD;
//	}&#xD;
//}&#xD;
&#xD;
//updateActionList = function  () {&#xD;
//&#xD;
//&#xD;
//console.log(this.context.options.errorPanelVIS.get("value"));&#xD;
//&#xD;
////var compApp = ;&#xD;
//&#xD;
////	if(this.context.options.complianceApproval.get("value"))&#xD;
////	{&#xD;
////		this.context.options.action.set("value", ["123","34"]);&#xD;
////	}&#xD;
//}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>4825a2c9-3017-4b62-b396-676c20bf093c</guid>
            <versionId>d69232f8-d775-4df2-9a5d-00dc17199a26</versionId>
        </inlineScript>
    </coachView>
</teamworks>

