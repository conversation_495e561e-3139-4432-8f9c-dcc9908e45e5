<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.da205dca-ccab-4203-9f89-719cb8957cc5" name="ClosureACT04 - Review ODC Closure Request by Trade FO Checker">
        <lastModified>1700641113390</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.c79d867b-7518-4c8f-8e02-1753c70c39bd</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-39ab</guid>
        <versionId>f7a78373-5e71-41d8-8e11-d6c9c7e9ef57</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f1071759-c94a-463e-8b4c-3dc68e57e338"],"isInterrupting":true,"extensionElements":{"default":["2027.f1071759-c94a-463e-8b4c-3dc68e57e338"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"933077b0-504d-428c-88af-fc58d26b5ce8"},{"startQuantity":1,"outgoing":["2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260"],"incoming":["2027.f1071759-c94a-463e-8b4c-3dc68e57e338"],"default":"2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260","extensionElements":{"nodeVisualInfo":[{"width":95,"x":191,"y":178,"declaredType":"TNodeVisualInfo","height":70}]},"name":"init Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.8b524a90-be9e-4c8d-8b14-44dd96321ee7","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.stepLog ={};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step      =  tw.epv.ScreenNames.closureACT04;\r\n\r\n\r\ntw.local.actionConditions = {};\r\ntw.local.actionConditions.screenName     = tw.epv.ScreenNames.closureACT04;\r\ntw.local.actionConditions.lastStepAction = tw.local.lastAction;\r\nif(tw.local.lastAction == tw.epv.CreationActions.terminateRequest)\r\n{\r\n\ttw.local.terminationReasonVIS = \"Editable\";\r\n}\r\nelse\r\n{\r\n\ttw.local.terminationReasonVIS = \"None\";\r\n}\r\n\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/*Initializing Request header*\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nvar date = new Date();\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\/' +(date.getMonth() + 1) + '\/' + date.getFullYear();\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT04;\r\n\ttw.local.odcRequest.appInfo.branch.name = \"HUB \"   + tw.local.odcRequest.ReversalReason.executionHub.value;\r\n\ttw.local.odcRequest.appInfo.branch.value =  \"HUB \" +  tw.local.odcRequest.ReversalReason.executionHub.value;\r\n"]}},{"outgoing":["2027.a0a2959d-0b6c-4a3f-848b-855e39ca546c","2027.9cdde7a0-e193-46b4-88e1-3f4b6a1ad8a6"],"incoming":["2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260","2027.60ef462c-96b2-48f1-8f52-a652587bcc23"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":357,"y":179,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Reversal_Closure_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1bd5af4f-4adf-4084-8dfd-dc5940d414ef","optionName":"@label","value":"Closure"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6da490a1-7ca4-4ea3-8795-8675a6ce5b04","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"985026b2-1057-465d-8257-dbf4ffba713b","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0db44100-c852-4e74-8dbd-6f70ee4a08b7","optionName":"closureReasonVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2b579f71-c114-4408-83e4-aaf335093ecf","optionName":"reversalReasonVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9f449808-39f8-4f46-861c-85c147c72470","optionName":"executionHubVIS","value":"Readonly"}],"viewUUID":"64.f0c268ac-0772-4735-af5b-5fc6caec30a1","binding":"tw.local.odcRequest.ReversalReason","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"50a83c61-852d-46c9-8f4c-a8f51327bc72","version":"8550"},{"layoutItemId":"Basic_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2263b419-150f-4a05-8b0b-82f81a465888","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"acbed5fb-589c-4ddc-826e-854c527eceb1","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2ce78e59-d8b8-434c-8f4f-db36e00bbeaf","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9acf442b-**************-bf761c14687b","optionName":"parentRequestNoVis","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ccfa2540-0320-49a7-8544-3855ea3ee418","optionName":"basicDetailsCVVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"39c2e06d-04bd-4dae-8787-8e1511b93c45","optionName":"multiTenorDatesVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bda78f37-baa0-482d-872b-33601f34e825","optionName":"contractStageVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"02ec6355-a6e0-4776-87a1-810704b7e50b","optionName":"flexCubeContractNoVIS","value":"None"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f3c38caa-562d-4d21-8b2a-a37d35c826f1","version":"8550"},{"layoutItemId":"Customer_Information_cv1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bfe785c6-8a1c-4999-8376-a4adfe07502e","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c379aabc-b356-4f1c-8cce-cf1959dd0a2c","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"207ce30a-df7b-4f4d-8e11-d9341591d2ec","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5c6821af-11dc-44b0-8792-9d94a0c5946d","optionName":"customerInfoVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0d16ae54-7d10-4fa1-8370-f106fa24e6e8","optionName":"listsVIS","value":"Readonly"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"08abfc57-7679-4ef4-85de-19b6f9359655","version":"8550"},{"layoutItemId":"Financial_Details_Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f298c50b-e0c2-4fe6-87ad-f10b3e7a3292","optionName":"@label","value":"Financial Details Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0344cb55-d312-43dc-81b7-a5e349c91376","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b56207ba-d44f-4e51-8434-3b6a29019852","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"00f92bfb-05df-4c58-855a-dd80700c5361","optionName":"financialDetailsCVVis","value":"READONLY"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"45d7d1e1-3e4a-4a17-865c-bf7bfc615008","optionName":"currencyDocAmountVIS","value":"READONLY"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"507266b2-8001-4a54-8678-688ce13767ee","version":"8550"},{"layoutItemId":"FC_Collections_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"20724731-14b4-4309-8d84-52b26d88a829","optionName":"@label","value":"FC Collections"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e27507c4-b284-4a7a-82b9-1f2471edba03","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3d74221-7922-4556-8d93-d8aea6813aa4","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d78155f9-**************-1b29851a018c","optionName":"FCVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d38eacc9-84da-43a4-8481-cf6702da2ec5","optionName":"retrieveBtnVis","value":"Hidden"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7eb7ad31-22cd-42bd-853d-d772b547a6e5","optionName":"addBtnVIS","value":"Hidden"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1e7e8a9c-4351-43d0-8c22-8e8e63267a0a","optionName":"customerCif","value":"tw.local.odcRequest.cif"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"67623170-b428-4760-8bd1-1b31214a15bd","optionName":"collectionCurrencyVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b387b9b2-3e69-4759-80b1-bc7a36957570","optionName":"negotiatedExchangeRateVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"67b743d5-4f8d-418d-8a34-30248cd69ff2","optionName":"activityType","value":"read"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","binding":"tw.local.odcRequest.FcCollections","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"4b554ce2-6618-41a2-8898-044bb03c81bd","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0b6e395c-b301-4ec1-8e03-918c92622ebe","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ebb4e80f-f426-491b-81f6-459c3d139831","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"176841f7-b8c0-4725-8844-f02f02ad0a04","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"79820d86-1402-4ede-8146-1b39ef1b1014","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e968f1ee-1669-4020-8842-658e0653ea50","optionName":"canCreate","value":"false"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"de7d6cb0-492f-4f43-8db8-d7d5d8f135d8","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fafe017a-0e8f-49f2-8539-796219a586a4","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"30ef9ad8-059c-4659-81cb-5749786bf9d8","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6f8c810c-dc58-4597-8646-318bd1a93ef7","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7cc7a7b6-77b0-45b5-8c31-e32e9c4d1eef","optionName":"@visibility.script","value":"{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"85f22b77-2c4f-435f-8aba-0fc60d40d2d7","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"5b5fb51f-d279-4ef7-8168-10b0b136c964"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"81a7e103-12d7-421e-82e8-5b43901ce360","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6cd3319c-df9f-4846-881e-b3b65413abc7","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f984338b-fd8a-48a0-869b-a70ef1ee22ab","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6a4282f6-86cc-4ad5-8672-af95f2b27aa4","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"36c1c22a-cade-4684-885f-8e084ddff0fe"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8c238e47-30d1-49d2-8571-e2d4dc98612d","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d8493d23-2335-4e4f-89f2-5b85b5aa7f5e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"14196930-3d8e-4b38-8e42-cecf1b44f0d1","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"11f65889-db64-47a3-812b-8977e7e6c89b","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"060b5f21-73a0-4924-8014-6f0b7b9f65f0","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b6ea2a7a-e2ca-4c5f-8360-1ccef3a00c44","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c0f4a000-721f-4282-8ccc-0adcb817b82d","optionName":"complianceApprovalVis","value":"NONE"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f2728d2d-1f77-48aa-891a-17a815ac7c67","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"64bf4290-2252-48a6-8df6-8db6be29e803","optionName":"actionConditions","value":"tw.local.actionConditions"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c88c9e63-8c12-4a0b-8d9c-7df25e46b0df","optionName":"terminateReasonVIS","value":"tw.local.terminationReasonVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0ff6a145-f8c7-4333-8e51-ab92f6891860","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"efa8c346-c4f9-4566-8c34-6d0e9f1adfef","optionName":"tradeFoCommentVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e51959ac-adb3-4faf-8f48-92efd989de96","optionName":"exeHubMkrCommentVis","value":"None"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"d9cba029-2d0e-44c5-8a76-7c1d55ab6505","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Review ODC Closure Request","isForCompensation":false,"completionQuantity":1,"id":"2025.6007ff73-2d72-4023-80e7-851b9f8cbae4"},{"startQuantity":1,"outgoing":["2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c"],"incoming":["2027.a0a2959d-0b6c-4a3f-848b-855e39ca546c"],"default":"2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":541,"y":182,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.22648431-de61-46b9-8a83-630d7273e7ad","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false;\r\n \r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\n{\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason , \"tw.local.odcRequest.stepLog.returnReason\");\r\n}\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\n{\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason , \"tw.local.odcRequest.stepLog.returnReason\");\r\n}\t\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)\r\n{\r\n\tmandatory(tw.local.odcRequest.stepLog.terminateReason , \"tw.local.odcRequest.stepLog.terminateReason\");\r\n}\t\r\n\r\n\/\/if(tw.local.odcRequest.ReversalReason != null &amp;&amp; (tw.local.odcRequest.ReversalReason.executionHub == null || tw.local.odcRequest.ReversalReason.executionHub.value == \"\"))\r\n\/\/{\r\n\/\/\tmandatory(tw.local.odcRequest.ReversalReason.executionHub , \"tw.local.odcRequest.ReversalReason.executionHub\");\r\n\/\/}\r\n\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\t"]}},{"startQuantity":1,"outgoing":["2027.f14dc606-9c19-484f-8411-7612d768f25e"],"incoming":["2027.ceae1a9a-0161-4e8f-8a5f-564b589e1367"],"default":"2027.f14dc606-9c19-484f-8411-7612d768f25e","extensionElements":{"nodeVisualInfo":[{"width":95,"x":800,"y":181,"declaredType":"TNodeVisualInfo","height":70}]},"name":"setting status and substatus","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.6d3942cb-7615-46d2-8157-79f48c6ef836","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"In Execution\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending Execution Hub Closure\";\r\n\t\r\n}\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"Terminated\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Terminated\";\r\n\t\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Final\";\r\n\t\r\n}\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"Initiated\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Returned to Initiator\";\r\n\r\n}\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Awaiting Trade Fo Approval\";\r\n\r\n}"]}},{"outgoing":["2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8"],"incoming":["2027.f14dc606-9c19-484f-8411-7612d768f25e"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":945,"y":180,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8","name":"History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.epv.userRole.tradeFOChkr"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.8a860bcf-0fa1-4a12-8ec8-6977cdf421f9","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"targetRef":"2025.8b524a90-be9e-4c8d-8b14-44dd96321ee7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To init Script","declaredType":"sequenceFlow","id":"2027.f1071759-c94a-463e-8b4c-3dc68e57e338","sourceRef":"933077b0-504d-428c-88af-fc58d26b5ce8"},{"targetRef":"2025.6007ff73-2d72-4023-80e7-851b9f8cbae4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review ODC Closure Request","declaredType":"sequenceFlow","id":"2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260","sourceRef":"2025.8b524a90-be9e-4c8d-8b14-44dd96321ee7"},{"targetRef":"2025.22648431-de61-46b9-8a83-630d7273e7ad","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"ce4371e6-39de-4d9c-8849-df1fe98e8750","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validation script","declaredType":"sequenceFlow","id":"2027.a0a2959d-0b6c-4a3f-848b-855e39ca546c","sourceRef":"2025.6007ff73-2d72-4023-80e7-851b9f8cbae4"},{"incoming":["2027.7543b7ee-15ce-4969-886f-8711a48038b2","2027.f3357bff-ff39-4669-87b5-423723897796"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1462,"y":202,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"2025.b07ec843-3a7f-46c7-883e-7a048d6e7e7e"},{"targetRef":"2025.41d65a57-61e6-4656-8889-43d17a257ba6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Audited?","declaredType":"sequenceFlow","id":"2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8","sourceRef":"2025.8a860bcf-0fa1-4a12-8ec8-6977cdf421f9"},{"outgoing":["2027.ceae1a9a-0161-4e8f-8a5f-564b589e1367","2027.ca54056b-f833-45f5-86fe-4b2977f9b052"],"incoming":["2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c"],"default":"2027.ca54056b-f833-45f5-86fe-4b2977f9b052","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":678,"y":201,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.83120517-3073-4ee7-8a70-6b72e7b321bb"},{"targetRef":"2025.83120517-3073-4ee7-8a70-6b72e7b321bb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c","sourceRef":"2025.22648431-de61-46b9-8a83-630d7273e7ad"},{"targetRef":"2025.6d3942cb-7615-46d2-8157-79f48c6ef836","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.ceae1a9a-0161-4e8f-8a5f-564b589e1367","sourceRef":"2025.83120517-3073-4ee7-8a70-6b72e7b321bb"},{"targetRef":"2025.8a860bcf-0fa1-4a12-8ec8-6977cdf421f9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To History","declaredType":"sequenceFlow","id":"2027.f14dc606-9c19-484f-8411-7612d768f25e","sourceRef":"2025.6d3942cb-7615-46d2-8157-79f48c6ef836"},{"incoming":["2027.ca54056b-f833-45f5-86fe-4b2977f9b052"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":660,"y":94,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.f33636b0-3732-48b6-80cd-ab69293abcdd"},{"targetRef":"2025.f33636b0-3732-48b6-80cd-ab69293abcdd","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.ca54056b-f833-45f5-86fe-4b2977f9b052","sourceRef":"2025.83120517-3073-4ee7-8a70-6b72e7b321bb"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.387ab45f-7452-4a56-8a82-190d7303659b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.b1cd524f-a73a-4122-8a70-e1b907dfb8d5"},{"outgoing":["2027.60ef462c-96b2-48f1-8f52-a652587bcc23"],"incoming":["2027.9cdde7a0-e193-46b4-88e1-3f4b6a1ad8a6"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.60ef462c-96b2-48f1-8f52-a652587bcc23"],"nodeVisualInfo":[{"width":24,"x":376,"y":82,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.bf671400-1f64-4ca9-89e2-b8b31949f9ff"},{"targetRef":"2025.bf671400-1f64-4ca9-89e2-b8b31949f9ff","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"ac525a3d-8383-468b-887f-7faf8ee5c1ea","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.9cdde7a0-e193-46b4-88e1-3f4b6a1ad8a6","sourceRef":"2025.6007ff73-2d72-4023-80e7-851b9f8cbae4"},{"targetRef":"2025.6007ff73-2d72-4023-80e7-851b9f8cbae4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review ODC Closure Request","declaredType":"sequenceFlow","id":"2027.60ef462c-96b2-48f1-8f52-a652587bcc23","sourceRef":"2025.bf671400-1f64-4ca9-89e2-b8b31949f9ff"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"terminationReasonVIS","isCollection":false,"declaredType":"dataObject","id":"2056.51757684-d605-43dd-87e2-cf01df6ce61d"},{"outgoing":["2027.c6b3a4e8-5966-47cb-8c98-ccc6540a35e3","2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5"],"incoming":["2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8"],"default":"2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1045,"y":199,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Audited?","declaredType":"exclusiveGateway","id":"2025.41d65a57-61e6-4656-8889-43d17a257ba6"},{"targetRef":"2025.edce93fc-daba-4de8-8f48-62b0adc9f630","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.c6b3a4e8-5966-47cb-8c98-ccc6540a35e3","sourceRef":"2025.41d65a57-61e6-4656-8889-43d17a257ba6"},{"incoming":["2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1028,"y":296,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.bdaa0cb3-e9cd-4a14-81bc-0a2b50c5e1f1"},{"targetRef":"2025.bdaa0cb3-e9cd-4a14-81bc-0a2b50c5e1f1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5","sourceRef":"2025.41d65a57-61e6-4656-8889-43d17a257ba6"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.37fbd3e1-61cd-48e9-83f0-750bca72aaf0"},{"outgoing":["2027.7543b7ee-15ce-4969-886f-8711a48038b2","2027.6e8e2044-b41a-42fc-8023-76d5517618aa"],"incoming":["2027.c6b3a4e8-5966-47cb-8c98-ccc6540a35e3"],"default":"2027.7543b7ee-15ce-4969-886f-8711a48038b2","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1151,"y":199,"declaredType":"TNodeVisualInfo","height":32}]},"name":"cancel","declaredType":"exclusiveGateway","id":"2025.edce93fc-daba-4de8-8f48-62b0adc9f630"},{"outgoing":["2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b"],"incoming":["2027.6e8e2044-b41a-42fc-8023-76d5517618aa"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1261,"y":62,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b","name":"cancel request","dataInputAssociation":[{"targetRef":"2055.55a76aa1-e513-4fd3-835f-7fe160120af7","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.77d2b3d6-2a2a-4520-ad08-31686157d431","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderID"]}}]},{"targetRef":"2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.8ca3460e-549f-4af5-8659-6f1cd40ecbad","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a"]}],"calledElement":"1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc"},{"targetRef":"2025.b07ec843-3a7f-46c7-883e-7a048d6e7e7e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.7543b7ee-15ce-4969-886f-8711a48038b2","sourceRef":"2025.edce93fc-daba-4de8-8f48-62b0adc9f630"},{"targetRef":"2025.8ca3460e-549f-4af5-8659-6f1cd40ecbad","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.terminateRequest"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of To cancel request","declaredType":"sequenceFlow","id":"2027.6e8e2044-b41a-42fc-8023-76d5517618aa","sourceRef":"2025.edce93fc-daba-4de8-8f48-62b0adc9f630"},{"targetRef":"2025.23e99b04-afd4-419a-846b-137038c32946","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b","sourceRef":"2025.8ca3460e-549f-4af5-8659-6f1cd40ecbad"},{"incoming":["2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1362,"y":17,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 3","declaredType":"intermediateThrowEvent","id":"2025.24e71289-df86-482f-81f1-9c75334a865a"},{"outgoing":["2027.f3357bff-ff39-4669-87b5-423723897796","2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a"],"incoming":["2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b"],"default":"2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1380,"y":136,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"2025.23e99b04-afd4-419a-846b-137038c32946"},{"targetRef":"2025.b07ec843-3a7f-46c7-883e-7a048d6e7e7e","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Copy of To End","declaredType":"sequenceFlow","id":"2027.f3357bff-ff39-4669-87b5-423723897796","sourceRef":"2025.23e99b04-afd4-419a-846b-137038c32946"},{"targetRef":"2025.24e71289-df86-482f-81f1-9c75334a865a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Copy of To Stay on page 3","declaredType":"sequenceFlow","id":"2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a","sourceRef":"2025.23e99b04-afd4-419a-846b-137038c32946"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"85749197-2bc9-477f-8ad5-68ab6f79a0ca","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"049fa0f1-f1d1-4b12-8cb6-c973e84c4016","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"ClosureACT04 - Review ODC Closure Request by Trade FO Checker","declaredType":"globalUserTask","id":"1.da205dca-ccab-4203-9f89-719cb8957cc5","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.c20f4aee-748a-4a36-8508-67ebf5f69f81"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.8545d9a7-9f35-4c3d-8ea4-13e90a5896f7"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"abf5c8b2-d5f4-4bbc-882a-3a6cb624e857","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"2de4cfe8-56e9-4e38-879d-3f198475144a","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"46777eea-4ea6-4905-8efa-312c5413df08","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"_1741b41e-da04-421a-8dd6-7dcbd65e6d5c"}],"outputSet":[{"id":"_57b1fc3a-afc0-49a6-ae0f-6ce7eed098d5"}],"dataInput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.b22dee7e-b212-48ff-8e2e-95bc9f0371e6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.7e77ef12-1a3f-42ec-86be-d745ee08a665"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.ddfa07cf-291a-4338-89e0-3e7a37c265ae"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"bc540f32-8d03-493c-82e3-ab57df7254a8"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b22dee7e-b212-48ff-8e2e-95bc9f0371e6</processParameterId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e95665f4-1a0f-457b-9ac4-4882ae302400</guid>
            <versionId>5f23c313-e976-4ee8-8a94-f39bfc1bef7f</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7e77ef12-1a3f-42ec-86be-d745ee08a665</processParameterId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>91854313-48b3-4ab4-bd6e-965586287eef</guid>
            <versionId>c30a80a5-3063-41ba-a7a9-7c845ed62b10</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ddfa07cf-291a-4338-89e0-3e7a37c265ae</processParameterId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1fbe29c7-3326-4133-8a7b-e1dadf2c1928</guid>
            <versionId>03757004-428c-4270-8c4d-f9a0834edf69</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c20f4aee-748a-4a36-8508-67ebf5f69f81</processParameterId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4bd86730-5bd7-4256-bedc-42b950618d30</guid>
            <versionId>1ddedaef-1308-42a7-9fb3-a9e67c9dadaf</versionId>
        </processParameter>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.387ab45f-7452-4a56-8a82-190d7303659b</processVariableId>
            <description isNull="true" />
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4bd3a87f-5491-40f1-9ce8-da27f699871d</guid>
            <versionId>ee32cb54-5e67-41e4-924f-75917d3f8f2d</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b1cd524f-a73a-4122-8a70-e1b907dfb8d5</processVariableId>
            <description isNull="true" />
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e0bcb762-7c7e-49e3-9291-8b1eb938cf68</guid>
            <versionId>3074da8f-fdbb-4d3b-8f38-830597c58ae6</versionId>
        </processVariable>
        <processVariable name="terminationReasonVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.51757684-d605-43dd-87e2-cf01df6ce61d</processVariableId>
            <description isNull="true" />
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e1f1c200-002b-417d-a83f-09e24b28298a</guid>
            <versionId>5f54909a-50ec-48e8-a8c4-69612fb02f93</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.37fbd3e1-61cd-48e9-83f0-750bca72aaf0</processVariableId>
            <description isNull="true" />
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>38b44dfc-a64a-43bb-af39-2474b6f5d47a</guid>
            <versionId>18dae2c0-7cfd-4135-8cf3-13a5e6ab1476</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8ca3460e-549f-4af5-8659-6f1cd40ecbad</processItemId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <name>cancel request</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8e317327-c358-4a7f-8c23-339123e76ed6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7b9c</guid>
            <versionId>1329ef48-6733-4bdb-aa1d-5d501fd94d8d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8e317327-c358-4a7f-8c23-339123e76ed6</subProcessId>
                <attachedProcessRef>/1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc</attachedProcessRef>
                <guid>75f2cc29-3226-4363-874c-15426c364e0e</guid>
                <versionId>43357c6d-57c1-4933-81db-c2232caae507</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c79d867b-7518-4c8f-8e02-1753c70c39bd</processItemId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.81d1a2d2-39a1-4d65-82bb-38f10caccf5d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-39a9</guid>
            <versionId>596b0218-8683-4dcb-a773-976e4eb5d24c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.64c04a25-a8d2-4471-89f8-fed4b26ea6ea</processItemId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.6af747bc-285a-42c3-b5bb-81b19f065b17</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-39aa</guid>
            <versionId>9dc0bba3-b912-4675-9c2c-b1cf0ea590c2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.6af747bc-285a-42c3-b5bb-81b19f065b17</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ccc98a73-13d2-4b79-a26c-0129451aa157</guid>
                <versionId>aca1c2cb-9ef1-47ea-823b-baf3fd2b2f4b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8a860bcf-0fa1-4a12-8ec8-6977cdf421f9</processItemId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <name>History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.cf2f3f6d-0b6e-4354-9533-369926254c3f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:-350</guid>
            <versionId>b5bb1fe9-2c1c-4e40-b79e-0f0aeccc2a88</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.cf2f3f6d-0b6e-4354-9533-369926254c3f</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>6dd1f2f0-ea9d-42fc-be3f-233d226a3d6d</guid>
                <versionId>0174ae76-cdbe-4443-b030-1941cf2bf1d1</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.9d5e84e0-ab67-4f1d-b151-7492e235707e</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <guid>f82390c2-6c1e-486f-afd9-56b5471dd8ac</guid>
            <versionId>1851e4cd-182b-49fa-87d7-60f17b05691e</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.0be11eaa-5dfe-49d0-bfba-1b0926a4832a</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <guid>e1a6e56f-b6ce-41dc-86b2-6de66cedfee2</guid>
            <versionId>2553a934-0a3c-4cbb-87c0-b26237519327</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.b0c74182-fe62-43bf-9193-b17b4ff4ff5e</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <guid>bb05ee54-086c-41ad-be84-81bddd50a342</guid>
            <versionId>d59e9ebf-f173-4852-ab69-3285691b1123</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.77753158-3b63-4a05-b3e3-90a92f308f7b</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <guid>b62810b7-560c-427e-802c-57c3a577cc6d</guid>
            <versionId>fb088c49-3b20-4ce1-b9c9-bcdf7ac2bfaa</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.c79d867b-7518-4c8f-8e02-1753c70c39bd</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="bc540f32-8d03-493c-82e3-ab57df7254a8" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="ClosureACT04 - Review ODC Closure Request by Trade FO Checker" id="1.da205dca-ccab-4203-9f89-719cb8957cc5">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="049fa0f1-f1d1-4b12-8cb6-c973e84c4016">
                            
                            
                            <ns16:startEvent name="Start" id="933077b0-504d-428c-88af-fc58d26b5ce8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:default>2027.f1071759-c94a-463e-8b4c-3dc68e57e338</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.f1071759-c94a-463e-8b4c-3dc68e57e338</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260" name="init Script" id="2025.8b524a90-be9e-4c8d-8b14-44dd96321ee7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="191" y="178" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f1071759-c94a-463e-8b4c-3dc68e57e338</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.odcRequest.stepLog ={};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step      =  tw.epv.ScreenNames.closureACT04;&#xD;
&#xD;
&#xD;
tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.screenName     = tw.epv.ScreenNames.closureACT04;&#xD;
tw.local.actionConditions.lastStepAction = tw.local.lastAction;&#xD;
if(tw.local.lastAction == tw.epv.CreationActions.terminateRequest)&#xD;
{&#xD;
	tw.local.terminationReasonVIS = "Editable";&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.terminationReasonVIS = "None";&#xD;
}&#xD;
&#xD;
///////////////*Initializing Request header*//////////////////////////&#xD;
//////////////////////////////////////////////////////////////////////&#xD;
var date = new Date();&#xD;
tw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();&#xD;
tw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+"( "+ tw.system.user.name+")";&#xD;
tw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT04;&#xD;
	tw.local.odcRequest.appInfo.branch.name = "HUB "   + tw.local.odcRequest.ReversalReason.executionHub.value;&#xD;
	tw.local.odcRequest.appInfo.branch.value =  "HUB " +  tw.local.odcRequest.ReversalReason.executionHub.value;&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Review ODC Closure Request" id="2025.6007ff73-2d72-4023-80e7-851b9f8cbae4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="357" y="179" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.60ef462c-96b2-48f1-8f52-a652587bcc23</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.a0a2959d-0b6c-4a3f-848b-855e39ca546c</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.9cdde7a0-e193-46b4-88e1-3f4b6a1ad8a6</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>d9cba029-2d0e-44c5-8a76-7c1d55ab6505</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8c238e47-30d1-49d2-8571-e2d4dc98612d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d8493d23-2335-4e4f-89f2-5b85b5aa7f5e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>14196930-3d8e-4b38-8e42-cecf1b44f0d1</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>11f65889-db64-47a3-812b-8977e7e6c89b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>060b5f21-73a0-4924-8014-6f0b7b9f65f0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b6ea2a7a-e2ca-4c5f-8360-1ccef3a00c44</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c0f4a000-721f-4282-8ccc-0adcb817b82d</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f2728d2d-1f77-48aa-891a-17a815ac7c67</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>64bf4290-2252-48a6-8df6-8db6be29e803</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c88c9e63-8c12-4a0b-8d9c-7df25e46b0df</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.terminationReasonVIS</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>0ff6a145-f8c7-4333-8e51-ab92f6891860</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>efa8c346-c4f9-4566-8c34-6d0e9f1adfef</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e51959ac-adb3-4faf-8f48-92efd989de96</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>36c1c22a-cade-4684-885f-8e084ddff0fe</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>6a4282f6-86cc-4ad5-8672-af95f2b27aa4</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>81a7e103-12d7-421e-82e8-5b43901ce360</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>6cd3319c-df9f-4846-881e-b3b65413abc7</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f984338b-fd8a-48a0-869b-a70ef1ee22ab</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>5b5fb51f-d279-4ef7-8168-10b0b136c964</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>50a83c61-852d-46c9-8f4c-a8f51327bc72</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Reversal_Closure_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1bd5af4f-4adf-4084-8dfd-dc5940d414ef</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Closure</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6da490a1-7ca4-4ea3-8795-8675a6ce5b04</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>985026b2-1057-465d-8257-dbf4ffba713b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0db44100-c852-4e74-8dbd-6f70ee4a08b7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>closureReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2b579f71-c114-4408-83e4-aaf335093ecf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>reversalReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9f449808-39f8-4f46-861c-85c147c72470</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>executionHubVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f0c268ac-0772-4735-af5b-5fc6caec30a1</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.ReversalReason</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>f3c38caa-562d-4d21-8b2a-a37d35c826f1</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2263b419-150f-4a05-8b0b-82f81a465888</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>acbed5fb-589c-4ddc-826e-854c527eceb1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2ce78e59-d8b8-434c-8f4f-db36e00bbeaf</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9acf442b-**************-bf761c14687b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ccfa2540-0320-49a7-8544-3855ea3ee418</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>39c2e06d-04bd-4dae-8787-8e1511b93c45</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bda78f37-baa0-482d-872b-33601f34e825</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>02ec6355-a6e0-4776-87a1-810704b7e50b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>08abfc57-7679-4ef4-85de-19b6f9359655</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information_cv1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bfe785c6-8a1c-4999-8376-a4adfe07502e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c379aabc-b356-4f1c-8cce-cf1959dd0a2c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>207ce30a-df7b-4f4d-8e11-d9341591d2ec</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>5c6821af-11dc-44b0-8792-9d94a0c5946d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0d16ae54-7d10-4fa1-8370-f106fa24e6e8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>507266b2-8001-4a54-8678-688ce13767ee</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details_Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f298c50b-e0c2-4fe6-87ad-f10b3e7a3292</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0344cb55-d312-43dc-81b7-a5e349c91376</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b56207ba-d44f-4e51-8434-3b6a29019852</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>00f92bfb-05df-4c58-855a-dd80700c5361</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>45d7d1e1-3e4a-4a17-865c-bf7bfc615008</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>4b554ce2-6618-41a2-8898-044bb03c81bd</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>FC_Collections_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>20724731-14b4-4309-8d84-52b26d88a829</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>FC Collections</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e27507c4-b284-4a7a-82b9-1f2471edba03</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a3d74221-7922-4556-8d93-d8aea6813aa4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d78155f9-**************-1b29851a018c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>FCVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d38eacc9-84da-43a4-8481-cf6702da2ec5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>retrieveBtnVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Hidden</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7eb7ad31-22cd-42bd-853d-d772b547a6e5</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addBtnVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Hidden</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1e7e8a9c-4351-43d0-8c22-8e8e63267a0a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerCif</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.cif</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>67623170-b428-4760-8bd1-1b31214a15bd</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>collectionCurrencyVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b387b9b2-3e69-4759-80b1-bc7a36957570</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>negotiatedExchangeRateVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>67b743d5-4f8d-418d-8a34-30248cd69ff2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>activityType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>read</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FcCollections</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>de7d6cb0-492f-4f43-8db8-d7d5d8f135d8</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0b6e395c-b301-4ec1-8e03-918c92622ebe</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ebb4e80f-f426-491b-81f6-459c3d139831</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>176841f7-b8c0-4725-8844-f02f02ad0a04</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>79820d86-1402-4ede-8146-1b39ef1b1014</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e968f1ee-1669-4020-8842-658e0653ea50</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>85f22b77-2c4f-435f-8aba-0fc60d40d2d7</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>fafe017a-0e8f-49f2-8539-796219a586a4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>30ef9ad8-059c-4659-81cb-5749786bf9d8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6f8c810c-dc58-4597-8646-318bd1a93ef7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7cc7a7b6-77b0-45b5-8c31-e32e9c4d1eef</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"@class":"com.ibm.bpm.coachNG.visibility.VisibilityRules","rules":[{"@class":"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule","var_conditions":[{"timeInMs":null,"var":"tw.local.isFirstTime","operand":true,"operator":0}],"action":"NONE"}],"defaultAction":"DEFAULT"}</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c" name="validation script" id="2025.22648431-de61-46b9-8a83-630d7273e7ad">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="541" y="182" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a0a2959d-0b6c-4a3f-848b-855e39ca546c</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c</ns16:outgoing>
                                
                                
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false;&#xD;
 &#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.stepLog.returnReason , "tw.local.odcRequest.stepLog.returnReason");&#xD;
}&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.stepLog.returnReason , "tw.local.odcRequest.stepLog.returnReason");&#xD;
}	&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.stepLog.terminateReason , "tw.local.odcRequest.stepLog.terminateReason");&#xD;
}	&#xD;
&#xD;
//if(tw.local.odcRequest.ReversalReason != null &amp;&amp; (tw.local.odcRequest.ReversalReason.executionHub == null || tw.local.odcRequest.ReversalReason.executionHub.value == ""))&#xD;
//{&#xD;
//	mandatory(tw.local.odcRequest.ReversalReason.executionHub , "tw.local.odcRequest.ReversalReason.executionHub");&#xD;
//}&#xD;
&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
	</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f14dc606-9c19-484f-8411-7612d768f25e" name="setting status and substatus" id="2025.6d3942cb-7615-46d2-8157-79f48c6ef836">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="800" y="181" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ceae1a9a-0161-4e8f-8a5f-564b589e1367</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f14dc606-9c19-484f-8411-7612d768f25e</ns16:outgoing>
                                
                                
                                <ns16:script>if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="In Execution";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Pending Execution Hub Closure";&#xD;
	&#xD;
}&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="Terminated";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Terminated";&#xD;
	&#xD;
	tw.local.odcRequest.BasicDetails.requestState = "Final";&#xD;
	&#xD;
}&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="Initiated";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Returned to Initiator";&#xD;
&#xD;
}&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="In Approval";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Awaiting Trade Fo Approval";&#xD;
&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8" name="History" id="2025.8a860bcf-0fa1-4a12-8ec8-6977cdf421f9">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="945" y="180" width="95" height="70" />
                                    
                                    
                                    <ns3:mode>InvokeService</ns3:mode>
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f14dc606-9c19-484f-8411-7612d768f25e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.epv.userRole.tradeFOChkr</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="933077b0-504d-428c-88af-fc58d26b5ce8" targetRef="2025.8b524a90-be9e-4c8d-8b14-44dd96321ee7" name="To init Script" id="2027.f1071759-c94a-463e-8b4c-3dc68e57e338">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8b524a90-be9e-4c8d-8b14-44dd96321ee7" targetRef="2025.6007ff73-2d72-4023-80e7-851b9f8cbae4" name="To Review ODC Closure Request" id="2027.f74a3d44-5e28-4e4e-8a1f-a2e6b4564260">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6007ff73-2d72-4023-80e7-851b9f8cbae4" targetRef="2025.22648431-de61-46b9-8a83-630d7273e7ad" name="To validation script" id="2027.a0a2959d-0b6c-4a3f-848b-855e39ca546c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="ce4371e6-39de-4d9c-8849-df1fe98e8750">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:endEvent name="End" id="2025.b07ec843-3a7f-46c7-883e-7a048d6e7e7e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1462" y="202" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.7543b7ee-15ce-4969-886f-8711a48038b2</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.f3357bff-ff39-4669-87b5-423723897796</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8a860bcf-0fa1-4a12-8ec8-6977cdf421f9" targetRef="2025.41d65a57-61e6-4656-8889-43d17a257ba6" name="To Audited?" id="2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.ca54056b-f833-45f5-86fe-4b2977f9b052" name="Valid?" id="2025.83120517-3073-4ee7-8a70-6b72e7b321bb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="678" y="201" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.ceae1a9a-0161-4e8f-8a5f-564b589e1367</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.ca54056b-f833-45f5-86fe-4b2977f9b052</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.22648431-de61-46b9-8a83-630d7273e7ad" targetRef="2025.83120517-3073-4ee7-8a70-6b72e7b321bb" name="To Valid?" id="2027.d8daef8d-a5bd-4471-8be3-50ed8876fe4c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.83120517-3073-4ee7-8a70-6b72e7b321bb" targetRef="2025.6d3942cb-7615-46d2-8157-79f48c6ef836" name="yes" id="2027.ceae1a9a-0161-4e8f-8a5f-564b589e1367">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6d3942cb-7615-46d2-8157-79f48c6ef836" targetRef="2025.8a860bcf-0fa1-4a12-8ec8-6977cdf421f9" name="To History" id="2027.f14dc606-9c19-484f-8411-7612d768f25e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.f33636b0-3732-48b6-80cd-ab69293abcdd">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="660" y="94" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ca54056b-f833-45f5-86fe-4b2977f9b052</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.83120517-3073-4ee7-8a70-6b72e7b321bb" targetRef="2025.f33636b0-3732-48b6-80cd-ab69293abcdd" name="No" id="2027.ca54056b-f833-45f5-86fe-4b2977f9b052">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.387ab45f-7452-4a56-8a82-190d7303659b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.b1cd524f-a73a-4122-8a70-e1b907dfb8d5" />
                            
                            
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.bf671400-1f64-4ca9-89e2-b8b31949f9ff">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="376" y="82" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.60ef462c-96b2-48f1-8f52-a652587bcc23</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.9cdde7a0-e193-46b4-88e1-3f4b6a1ad8a6</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.60ef462c-96b2-48f1-8f52-a652587bcc23</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6007ff73-2d72-4023-80e7-851b9f8cbae4" targetRef="2025.bf671400-1f64-4ca9-89e2-b8b31949f9ff" name="To Postpone" id="2027.9cdde7a0-e193-46b4-88e1-3f4b6a1ad8a6">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="ac525a3d-8383-468b-887f-7faf8ee5c1ea">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.bf671400-1f64-4ca9-89e2-b8b31949f9ff" targetRef="2025.6007ff73-2d72-4023-80e7-851b9f8cbae4" name="To Review ODC Closure Request" id="2027.60ef462c-96b2-48f1-8f52-a652587bcc23">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="terminationReasonVIS" id="2056.51757684-d605-43dd-87e2-cf01df6ce61d" />
                            
                            
                            <ns16:exclusiveGateway default="2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5" name="Audited?" id="2025.41d65a57-61e6-4656-8889-43d17a257ba6">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1045" y="199" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.eeaabe59-64fa-498a-8826-2c84c0fa02d8</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.c6b3a4e8-5966-47cb-8c98-ccc6540a35e3</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.41d65a57-61e6-4656-8889-43d17a257ba6" targetRef="2025.edce93fc-daba-4de8-8f48-62b0adc9f630" name="Yes" id="2027.c6b3a4e8-5966-47cb-8c98-ccc6540a35e3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.bdaa0cb3-e9cd-4a14-81bc-0a2b50c5e1f1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1028" y="296" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.41d65a57-61e6-4656-8889-43d17a257ba6" targetRef="2025.bdaa0cb3-e9cd-4a14-81bc-0a2b50c5e1f1" name="No" id="2027.0dd5da03-b8f0-48a6-80cb-b20b729a06f5">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.37fbd3e1-61cd-48e9-83f0-750bca72aaf0" />
                            
                            
                            <ns16:exclusiveGateway default="2027.7543b7ee-15ce-4969-886f-8711a48038b2" gatewayDirection="Unspecified" name="cancel" id="2025.edce93fc-daba-4de8-8f48-62b0adc9f630">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1151" y="199" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.c6b3a4e8-5966-47cb-8c98-ccc6540a35e3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.7543b7ee-15ce-4969-886f-8711a48038b2</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.6e8e2044-b41a-42fc-8023-76d5517618aa</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:callActivity calledElement="1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b" name="cancel request" id="2025.8ca3460e-549f-4af5-8659-6f1cd40ecbad">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1261" y="62" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6e8e2044-b41a-42fc-8023-76d5517618aa</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.55a76aa1-e513-4fd3-835f-7fe160120af7</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.77d2b3d6-2a2a-4520-ad08-31686157d431</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.folderID</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.edce93fc-daba-4de8-8f48-62b0adc9f630" targetRef="2025.b07ec843-3a7f-46c7-883e-7a048d6e7e7e" name="No" id="2027.7543b7ee-15ce-4969-886f-8711a48038b2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.edce93fc-daba-4de8-8f48-62b0adc9f630" targetRef="2025.8ca3460e-549f-4af5-8659-6f1cd40ecbad" name="Copy of To cancel request" id="2027.6e8e2044-b41a-42fc-8023-76d5517618aa">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.terminateRequest</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8ca3460e-549f-4af5-8659-6f1cd40ecbad" targetRef="2025.23e99b04-afd4-419a-846b-137038c32946" name="To End" id="2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 3" id="2025.24e71289-df86-482f-81f1-9c75334a865a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1362" y="17" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:exclusiveGateway default="2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a" name="Exclusive Gateway" id="2025.23e99b04-afd4-419a-846b-137038c32946">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1380" y="136" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f3c33a25-e0dd-4484-88a7-0db71db3de6b</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f3357bff-ff39-4669-87b5-423723897796</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.23e99b04-afd4-419a-846b-137038c32946" targetRef="2025.b07ec843-3a7f-46c7-883e-7a048d6e7e7e" name="Copy of To End" id="2027.f3357bff-ff39-4669-87b5-423723897796">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.23e99b04-afd4-419a-846b-137038c32946" targetRef="2025.24e71289-df86-482f-81f1-9c75334a865a" name="Copy of To Stay on page 3" id="2027.6eae12b5-93ae-41bd-8c9b-2fa3726f079a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="85749197-2bc9-477f-8ad5-68ab6f79a0ca">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.8545d9a7-9f35-4c3d-8ea4-13e90a5896f7</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="abf5c8b2-d5f4-4bbc-882a-3a6cb624e857" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="2de4cfe8-56e9-4e38-879d-3f198475144a" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="46777eea-4ea6-4905-8efa-312c5413df08" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.b22dee7e-b212-48ff-8e2e-95bc9f0371e6" />
                        
                        
                        <ns16:dataInput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.7e77ef12-1a3f-42ec-86be-d745ee08a665" />
                        
                        
                        <ns16:dataInput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.ddfa07cf-291a-4338-89e0-3e7a37c265ae" />
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.c20f4aee-748a-4a36-8508-67ebf5f69f81" />
                        
                        
                        <ns16:inputSet id="_1741b41e-da04-421a-8dd6-7dcbd65e6d5c" />
                        
                        
                        <ns16:outputSet id="_57b1fc3a-afc0-49a6-ae0f-6ce7eed098d5" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.31190a74-2ca4-4f8a-ac20-672a021ecd21</processLinkId>
            <processId>1.da205dca-ccab-4203-9f89-719cb8957cc5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c79d867b-7518-4c8f-8e02-1753c70c39bd</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.64c04a25-a8d2-4471-89f8-fed4b26ea6ea</toProcessItemId>
            <guid>d10e0ff5-c99a-4446-a0c2-f604d8bf09db</guid>
            <versionId>8f4b1133-5b1c-4b43-85d3-44b7576cdb99</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.c79d867b-7518-4c8f-8e02-1753c70c39bd</fromProcessItemId>
            <toProcessItemId>2025.64c04a25-a8d2-4471-89f8-fed4b26ea6ea</toProcessItemId>
        </link>
    </process>
</teamworks>

