<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.545f4988-f748-4797-90c9-d2c7c8a98082" name="Check Parent Request">
        <lastModified>1698558555458</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>8265d9e1-33ae-42f3-a4b2-1df7c7bfba0c</guid>
        <versionId>c1d19b2a-90ea-4231-9cca-065d226aa8e1</versionId>
        <dependencySummary>&lt;dependencySummary id="5820306d-14ec-4a6f-ac6b-b0d0efb94743" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="requestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.52711b62-7df7-40b3-aa2d-0338fa7284dd</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"07702230001069"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d2b6ba47-c492-4f12-9f0d-41ee4294ea86</guid>
            <versionId>12fdf79c-95cd-4cf1-a71f-a1e557e16432</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.82d72677-767a-4f2c-bc85-8e8dcdebf086</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.IDCContract();
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();
autoObject.settlementAccounts[0] = new tw.object.SettlementAccount();
autoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = new tw.object.DBLookup();
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = new tw.object.listOf.Parties();
autoObject.party[0] = new tw.object.Parties();
autoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.party[0].cifVis = "";
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = new tw.object.LiquidationSummary();
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new TWDate();
autoObject.liquidationSummary.creditValueDate = new TWDate();
autoObject.IDCProduct = new tw.object.DBLookup();
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new TWDate();
autoObject.transactionMaturityDate = new TWDate();
autoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.commissionsAndCharges[0].defaultPercentage = 0.0;
autoObject.commissionsAndCharges[0].changePercentage = 0.0;
autoObject.commissionsAndCharges[0].description = "";
autoObject.commissionsAndCharges[0].rateType = "";
autoObject.commissionsAndCharges[0].percentageVis = "";
autoObject.commissionsAndCharges[0].changeAmountVis = "";
autoObject.transactionBaseDate = new TWDate();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = new tw.object.DBLookup();
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = new tw.object.SwiftMessageData();
autoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = new tw.object.listOf.ContractAdvice();
autoObject.advices[0] = new tw.object.ContractAdvice();
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();
autoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new TWDate();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();
autoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();
autoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new TWDate();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0].facilityCode = "";
autoObject.facilities[0].overallLimit = 0.0;
autoObject.facilities[0].limitAmount = 0.0;
autoObject.facilities[0].effectiveLimitAmount = 0.0;
autoObject.facilities[0].availableAmount = 0.0;
autoObject.facilities[0].expiryDate = new TWDate();
autoObject.facilities[0].availableFlag = false;
autoObject.facilities[0].authorizedFlag = false;
autoObject.facilities[0].Utilization = 0.0;
autoObject.facilities[0].returnCode = "";
autoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0].lineCode = "";
autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();
autoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
autoObject.facilities[0].facilityLines[0].availableFlag = false;
autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
autoObject.facilities[0].facilityLines[0].internalRemarks = "";
autoObject.facilities[0].facilityLines[0].purpose = "";
autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = "";
autoObject.facilities[0].facilityLines[0].LCDef = "";
autoObject.facilities[0].facilityLines[0].LCCashCover = "";
autoObject.facilities[0].facilityLines[0].IDCCommission = "";
autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = "";
autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = "";
autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
autoObject.facilities[0].facilityLines[0].lineCurrency = "";
autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
autoObject.facilities[0].facilityLines[0].returnCode = "";
autoObject.facilities[0].facilityLines[0].LGCommission = "";
autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = "";
autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = "";
autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject.facilities[0].facilityLines[0].CIF = "";
autoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].partyType.name = "";
autoObject.facilities[0].facilityLines[0].partyType.value = "";
autoObject.facilities[0].facilityLines[0].facilityID = "";
autoObject.facilities[0].status = "";
autoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityCurrency.name = "";
autoObject.facilities[0].facilityCurrency.value = "";
autoObject.facilities[0].facilityID = "";
autoObject.limitsTrackingVIs = "";
autoObject.interestVisibility = "";
autoObject.adviceVis = "";
autoObject.outstandingAmount = 0.0;
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0c23bd51-310e-4373-9164-1acb3d189b9c</guid>
            <versionId>c46082f9-a80a-4d90-965e-61caeeb39396</versionId>
        </processParameter>
        <processParameter name="IDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.88e74584-565a-40c2-a722-ade695030bbd</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>3</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "1234";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "00102230001070";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;

autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c077a444-2ce2-4f8c-b6fd-da04dccee6e7</guid>
            <versionId>f68829f8-5b87-4567-b6ed-457006ce98b7</versionId>
        </processParameter>
        <processParameter name="parentIDC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.455bc6b1-7ba6-4720-94d5-a300a02c504f</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>4</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "00102230001070";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "123";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f8316a3e-42c9-4d9f-8a2e-9cfc972e745e</guid>
            <versionId>3b8e9b6e-5ad2-4762-b0cd-187abea988d9</versionId>
        </processParameter>
        <processParameter name="isvalid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2df15530-52f0-41a9-9c25-bae06fa34398</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7b223b02-48e8-44e8-be27-150a1c53743f</guid>
            <versionId>b1d26cd7-3d40-4e37-a497-2ac38a6928c0</versionId>
        </processParameter>
        <processParameter name="massege">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0d917db9-6dd3-40cd-963c-f56294d058e5</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7413d1c0-f338-472f-93fa-5639b497b6ee</guid>
            <versionId>33fedca2-d6c0-412f-95f3-7bbe16b45098</versionId>
        </processParameter>
        <processParameter name="DBID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2164b222-615a-4a75-a5d5-98b99470a6ff</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>84264581-9d3e-4b18-a6eb-b63d11d70128</guid>
            <versionId>bb2d2412-937c-4b61-aa92-fee019479bfc</versionId>
        </processParameter>
        <processParameter name="idcContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.60d0706b-a7ec-4ef7-93af-f527d8da9f48</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>373f5c96-0a4d-4447-826a-44b0c2bcc455</guid>
            <versionId>c3af8c21-8f1b-4037-984d-e4d1be20c3de</versionId>
        </processParameter>
        <processParameter name="parentIDCContract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ef212b69-feda-46ce-97d9-c2a5b4fe91f3</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5011e813-e938-4e0b-bef3-f396136491f8</guid>
            <versionId>91f8dc18-4074-4547-b306-da8f23766204</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5612afbe-e447-4c02-8202-fd20453fd03f</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1d04bca1-a7dd-482e-a403-f833ca6a7215</guid>
            <versionId>5317fa36-6037-4178-bb3c-cc40f10076e6</versionId>
        </processParameter>
        <processParameter name="IDCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9a3d0a85-297e-4663-93c4-721d815eb332</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classId>
            <seq>11</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7a071b20-e0d4-4f1b-80b8-2050de62e30a</guid>
            <versionId>0346d27f-303b-4f7d-9bb5-e9330e85a08d</versionId>
        </processParameter>
        <processParameter name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9bd55f86-fcc3-485e-b9f0-c45dede585e9</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>12</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4756dd56-95c9-4ae6-8b93-494d7c6d4d2a</guid>
            <versionId>462dbeb2-3138-4437-a2b5-e5588a676632</versionId>
        </processParameter>
        <processParameter name="fullPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1f4f4e36-97e7-43ae-b14a-21ca79480c5f</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>13</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b7c606a3-b14e-4dbb-a4c4-76674ed7e0d4</guid>
            <versionId>970d8d42-94f1-4cf6-9f90-a2357b8ab68f</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0d63f311-de13-4c02-9731-3cae95646591</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>14</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a69fcb30-fc54-42af-a685-c48e974a47da</guid>
            <versionId>6f20a5aa-2757-431c-ad97-2a0dc4d5567e</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6db4acfa-0ea2-4e89-ba02-4abf1ab6a431</processParameterId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>15</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7d892549-cc81-4c34-88e0-d3448d2dda21</guid>
            <versionId>786b82c9-ecfc-4447-8fa1-c64095c218c3</versionId>
        </processParameter>
        <processVariable name="parameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ff8aa7a3-bc58-45dc-a662-d32c344a8f11</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b0136a31-56e2-47f4-af73-ecb68abba192</guid>
            <versionId>83a00039-523f-4b36-932d-31edce4df529</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.736a5ca8-5151-467e-9d77-576f7c5e0200</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d3b81230-b933-463a-885e-34ef1c5fb1db</guid>
            <versionId>bdd7e978-731d-42aa-b5e7-6f8335d583ae</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3455980e-a47c-48bb-b1cc-3073d098fa0b</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e6c53d94-5ef3-48a0-8f3d-c39c98c7f2b2</guid>
            <versionId>48b71595-f479-4e8d-8b7f-0fc1bb9db122</versionId>
        </processVariable>
        <processVariable name="haveRunningRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0bf11c4f-4221-4ab4-8f45-da3ecef66b36</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>969f2002-cf3c-4de6-8a53-e992ff31cdd9</guid>
            <versionId>fbd73a32-9173-472a-b9d9-03293eaa6f1d</versionId>
        </processVariable>
        <processVariable name="isTotalyPaid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.07190f21-b578-4bde-a51e-36031244b366</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d16f7875-ee25-4541-b27c-************</guid>
            <versionId>933bbe41-d7a3-4d8b-9f4a-f7a2f756acb7</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.baa7b86f-9ab2-4e31-91aa-68fe1d638c3e</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>83a599d4-458c-42bc-985f-b98db4c73647</guid>
            <versionId>3a63b6de-422e-4bfe-b034-3c62506b0f12</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b8aad2ab-921e-4505-bbd0-aa2a6d94e28e</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>92d6c766-f2cd-46e7-bf29-3d99f99216ac</guid>
            <versionId>dd3fd7cd-ec76-4ca3-8f2b-42ae73083633</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fda30d62-86a6-461e-85ac-************</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>87ccf7ec-e7ff-4c9a-89b7-49799ce05136</guid>
            <versionId>cbdff536-c842-4cec-b9de-44662761403d</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0ccabf04-2650-48f4-a1ca-b18f255675c7</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>511e6517-fcc6-4606-8d48-d7f3f5f482c0</guid>
            <versionId>865bce3c-50a9-4496-bd2d-12cc1d94fa35</versionId>
        </processVariable>
        <processVariable name="SCQueryBCContractRes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d5d0cbde-a3d5-4a67-99e3-0b43e353f916</processVariableId>
            <description isNull="true" />
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9229332e-1708-4f3c-9a14-de75fd6fff8c</guid>
            <versionId>7f4218ae-c31d-4080-90d8-26f6a494add7</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>has running request</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.967f4214-6e41-4ee0-bcb6-6c84724105b3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:468</guid>
            <versionId>09f742eb-c147-4d20-8862-5b306ac64478</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1302" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.967f4214-6e41-4ee0-bcb6-6c84724105b3</switchId>
                <guid>27d03987-d9aa-429f-b953-8681296c26ee</guid>
                <versionId>5fd9aec7-19b8-4a4c-9a88-aca995d6c32c</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.fdcc59c0-e79a-4853-979e-de103323605d</switchConditionId>
                    <switchId>3013.967f4214-6e41-4ee0-bcb6-6c84724105b3</switchId>
                    <seq>1</seq>
                    <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709f</endStateId>
                    <condition>tw.local.haveRunningRequest	  ==	  false</condition>
                    <guid>a1814963-f09e-40f5-8239-fe12b46f5839</guid>
                    <versionId>7f705e25-eb9c-4873-b412-bed2b3900283</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9f0a234f-f85c-4b82-90d7-323282f28c0e</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.41c59d17-8ff0-4089-b883-2f51695f394a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:462</guid>
            <versionId>11ba0ef7-ea27-40f9-9092-2054af6a3d79</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1858" y="221">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.41c59d17-8ff0-4089-b883-2f51695f394a</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>b8556a7d-ef9d-4aeb-8b04-4a95a1e5213d</guid>
                <versionId>56421de7-1cf5-4e2c-9a78-c3508fcca4a1</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ecf0e07d-07e7-4f78-9357-d8c0e8ea579f</parameterMappingId>
                    <processParameterId>2055.6db4acfa-0ea2-4e89-ba02-4abf1ab6a431</processParameterId>
                    <parameterMappingParentId>3007.41c59d17-8ff0-4089-b883-2f51695f394a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3f10f1c0-6a8f-43ad-83a8-63848c8a23f4</guid>
                    <versionId>64208d4c-b0a6-4d17-9440-936fd0cd3725</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.21bf961b-7097-4d5b-b1b0-25a6ab444787</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Get Request Number And CBE</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:464</guid>
            <versionId>1cf80d2e-12ef-4f97-be1f-c6121af629ca</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1425" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error8</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</subProcessId>
                <attachedProcessRef>/1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13</attachedProcessRef>
                <guid>5f134062-5fb6-43ad-aa5f-aa65894b6141</guid>
                <versionId>d2993d87-e8bb-4269-bdc0-591bcd27112a</versionId>
                <parameterMapping name="result">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.048f91a0-73fe-4835-b363-3741f10f0fc5</parameterMappingId>
                    <processParameterId>2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.IDCRequest.customerInformation.isCustomeSanctionedbyCBE</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1a0b4f58-12c0-4495-909e-a78a4a8b8854</guid>
                    <versionId>24a22901-4170-4e72-ba44-c7b7e2885d0f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0a747e30-0a66-470f-9f51-9afae31225fb</parameterMappingId>
                    <processParameterId>2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c8e450c8-7c2f-4b3a-9df4-79829a018f99</guid>
                    <versionId>2db0b1df-515e-4d57-b4a0-61f1d0d6500c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="BPM_Request_Number">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.33f65645-5b9f-49fa-8947-08728adcf039</parameterMappingId>
                    <processParameterId>2055.d22b4a18-865c-4b84-b879-26ee9e3f322f</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.IDCRequest.appInfo.instanceID</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>549663a3-cf40-4534-9cf9-b0f4dc9d6e8b</guid>
                    <versionId>494b9831-f2c9-4a1b-8d7f-f497cbcd0391</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="idcRequest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.174046cb-2eaa-4183-9cba-13096ea8b597</parameterMappingId>
                    <processParameterId>2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.IDCRequest</value>
                    <classRef>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e1118f3d-1f19-479b-9c6e-82ff49b52f48</guid>
                    <versionId>8e3c152a-19fe-4f86-89c9-0326e24b442a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="folderID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3d39c62c-1dcb-46df-8830-e35b8611024b</parameterMappingId>
                    <processParameterId>2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.folderID</value>
                    <classRef>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>370e20eb-6c44-48c7-8fbe-9a5728871982</guid>
                    <versionId>c33656ff-aff0-4b93-af4f-047ef5d18b82</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parentPath">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b2ab6cfb-348e-4f41-bc7f-42a765e1e411</parameterMappingId>
                    <processParameterId>2055.31e2d85c-18d8-4756-82c5-efad7879177e</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentPath</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e8849b45-045a-4c64-99b5-580e08366839</guid>
                    <versionId>eda5a1b7-1f25-42ed-91ef-01c08949d00a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="fullPath">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ad46f971-ba6a-4d52-b8a5-49f5f8af58d3</parameterMappingId>
                    <processParameterId>2055.b304617e-98f3-4070-b457-59e470497a2f</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.fullPath</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ee2fee33-f616-446b-ac35-5f8b49d9d35c</guid>
                    <versionId>efcdcc52-3dde-4e29-9609-2a26677a5463</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parentIDC">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.acb7af5d-2399-4ce6-8cba-a86554ccf1ab</parameterMappingId>
                    <processParameterId>2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441</processParameterId>
                    <parameterMappingParentId>3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentIDC</value>
                    <classRef>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4d91c8bb-f81f-4648-824e-e68bfcd781b9</guid>
                    <versionId>f16fc945-a7ce-45a3-b718-a326a85ae6ac</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f1ee4e76-20d9-4426-a039-72de129ca5a0</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Insert IDC Request</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.480e0f91-d28e-45da-ba32-47d7705f0718</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:460</guid>
            <versionId>20195e9f-3002-40db-9cb9-db6e1dc3790a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1590" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error6</name>
                <locationId>bottomLeft</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.480e0f91-d28e-45da-ba32-47d7705f0718</subProcessId>
                <attachedProcessRef>/1.06eaabde-33db-480a-9d65-982fa27c2eac</attachedProcessRef>
                <guid>035e1b0a-d994-464a-86ed-************</guid>
                <versionId>de345546-be3f-413c-8bc2-2750e6ec4765</versionId>
                <parameterMapping name="DBID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.606dba56-b0c0-4055-8539-339e7615e2a4</parameterMappingId>
                    <processParameterId>2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb</processParameterId>
                    <parameterMappingParentId>3012.480e0f91-d28e-45da-ba32-47d7705f0718</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.DBID</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5952b563-9679-4c20-8429-4a695bdb7c16</guid>
                    <versionId>23f30b63-6d3d-49a4-b85e-702547979f71</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="error">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f3a5bef2-6085-48c5-ae63-74a5e350d631</parameterMappingId>
                    <processParameterId>2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01</processParameterId>
                    <parameterMappingParentId>3012.480e0f91-d28e-45da-ba32-47d7705f0718</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error</value>
                    <classRef>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1c470618-db2a-42a7-80ae-f340f68b3dd8</guid>
                    <versionId>9484b5ba-5f59-4c6f-9a08-89b652f95385</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="IDCRequest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f353e9e8-564b-435a-81a1-d224fecf6410</parameterMappingId>
                    <processParameterId>2055.6287bfa2-c4da-4ec3-842c-a164e045a461</processParameterId>
                    <parameterMappingParentId>3012.480e0f91-d28e-45da-ba32-47d7705f0718</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.IDCRequest</value>
                    <classRef>/12.7f4fdce8-dfe9-472a-a501-530e9a76af67</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c2c1bf50-**************-e8a00fb7f821</guid>
                    <versionId>976b55f0-f556-4c39-9a79-854f92414265</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="idcContract">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8d62b427-ae2c-4d1a-af15-36a47412819f</parameterMappingId>
                    <processParameterId>2055.e4ea1176-3273-4117-87c0-b882c7df46c4</processParameterId>
                    <parameterMappingParentId>3012.480e0f91-d28e-45da-ba32-47d7705f0718</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcContract</value>
                    <classRef>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ba174035-7844-4db7-b39d-2ef9dc9319a4</guid>
                    <versionId>b80dd2d3-0ab7-4c88-bdb9-4a2a5831321b</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a6129d38-34a3-4623-9a9e-d8e404c126d8</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>is totally paid</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.d58b5f22-6762-44f2-90c8-7849d5ffab31</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:46c</guid>
            <versionId>20ce5d64-b05c-4b7c-a751-7f9df8a6bc59</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="871" y="79">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.d58b5f22-6762-44f2-90c8-7849d5ffab31</switchId>
                <guid>a499ee9b-a15e-44dc-977d-4254dac8edd1</guid>
                <versionId>4cca14d1-dd0b-428a-986b-827aab9528b1</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.777d977d-5594-4aaf-ad1a-69ec1d0e12e7</switchConditionId>
                    <switchId>3013.d58b5f22-6762-44f2-90c8-7849d5ffab31</switchId>
                    <seq>1</seq>
                    <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-70a0</endStateId>
                    <condition>tw.local.isTotalyPaid	  ==	  false</condition>
                    <guid>19286535-359b-4952-8de3-bd2ca6544862</guid>
                    <versionId>f5da11ba-dba5-4a8c-8bd3-60d9e008a26b</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>MW_FC Query DC Contract</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:45e</guid>
            <versionId>3c82e144-f586-408e-a55b-06e0badc2830</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.8d2636e8-46d1-44b1-b0ef-59eb2e54de63</processItemPrePostId>
                <processItemId>2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>e5f18774-c9d0-43db-acfa-392d312c46d9</guid>
                <versionId>bb1a50a1-29ad-4e68-8d53-536536e05da0</versionId>
            </processPrePosts>
            <layoutData x="369" y="58">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error7</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.3875c748-15fc-40ef-bef1-ea4d905d7f75</attachedProcessRef>
                <guid>5d7274f8-5917-47d2-8383-4b7248b3bd1c</guid>
                <versionId>de45752c-4816-4201-aec5-deabd8ba3ace</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.574f439a-6edd-4df4-ad12-0d9ff97dd1cb</parameterMappingId>
                    <processParameterId>2055.95835270-515f-4794-a207-a5e2aa301c0e</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8c70e41c-5135-4e9e-b0e8-1587f9deeb57</guid>
                    <versionId>0bfb5ba9-19fd-47f2-9cb6-f5c7e0d2f36b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c83d1875-9333-465c-9dc4-ce6c517d674a</parameterMappingId>
                    <processParameterId>2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>cedc58c4-f553-462c-9c19-101e0ffebdd4</guid>
                    <versionId>1c9e0689-0223-48f7-ad25-34a4d0e8b404</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.738ebb65-2c32-4a12-af51-42d6e9c87163</parameterMappingId>
                    <processParameterId>2055.42effea6-8d8b-437c-958a-da5cf9119674</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7d84d764-4684-46ec-b2b0-9997113ea376</guid>
                    <versionId>33b91614-25eb-4d0c-b023-56fb544ce2f9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e64631ef-f052-47bf-8945-4864a57c4f7e</parameterMappingId>
                    <processParameterId>2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>506640df-7ea7-4bbc-b1c6-f522d217edb2</guid>
                    <versionId>3775bdf1-6af3-4fa5-ab7a-2cf82f3157ea</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="contractNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b60a85ad-8b83-452b-9721-62113969b2ef</parameterMappingId>
                    <processParameterId>2055.8f786b77-ae66-4547-a444-d6ccb8969c42</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentIDC.FCContractNumber</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6ea50abc-d5ee-4b9c-856f-f7e92fdca057</guid>
                    <versionId>4d9f03a9-cf05-4c4b-bcdc-f0249a65e45c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cfb4af85-1aa3-4615-b107-afa462b7598e</parameterMappingId>
                    <processParameterId>2055.4ff12b5f-6b69-4504-a730-046ee5c2000a</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fb135835-6db9-44c7-9af0-96a3385776b2</guid>
                    <versionId>5f0543d1-a741-4920-8a14-5719bb875d1d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6013b4fb-95d8-426d-9016-a4a87431ca78</parameterMappingId>
                    <processParameterId>2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5cb2590e-a5e8-4104-9bd5-565a18414d6a</guid>
                    <versionId>9b0adfd2-0c5e-4cb2-8c96-02ecb3ec85d7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="SCQueryBCContractRes">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8de140ca-2d4d-4f9e-a551-ce14f232d16e</parameterMappingId>
                    <processParameterId>2055.004a9996-2e6b-4d60-821e-5db8e4ba2271</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.SCQueryBCContractRes</value>
                    <classRef>/12.*************-4cbb-a781-44d233d577c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>717780f6-6ec2-4762-9057-d7682496fc86</guid>
                    <versionId>bb3062bf-a53c-42f5-a253-4128359866ba</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1f377485-115b-4527-8294-034273c42c45</parameterMappingId>
                    <processParameterId>2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b7a9b573-f445-4b68-a480-d6b3eee52cd9</guid>
                    <versionId>c4ad8e5b-ee29-4e07-8614-477d63e2eea1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.056fa3d4-ffe7-4906-80e2-7f8d024a98df</parameterMappingId>
                    <processParameterId>2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e376fd1f-0b52-4452-aa51-973f310be611</guid>
                    <versionId>c79947c4-663a-40fe-88cd-c04da7ebb3bf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.26b77f4d-b5b5-4183-b4ff-1b54a76b6541</parameterMappingId>
                    <processParameterId>2055.ee7811d9-22b1-4727-9148-bcac74c306af</processParameterId>
                    <parameterMappingParentId>3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>7f3ca078-883e-43d8-bc41-2e292d8227a7</guid>
                    <versionId>ece0718d-a600-41c4-b6a3-707f4c8a158d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>check has running request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.b05e3bee-734e-4167-9b85-05f73d545d90</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:461</guid>
            <versionId>714a3801-70f0-49a6-a385-309ea0f1cf55</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1179" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error5</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.b05e3bee-734e-4167-9b85-05f73d545d90</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.results[0].rows[0].data[0] == null) {&#xD;
	tw.local.haveRunningRequest = false;&#xD;
	tw.local.isvalid = true;&#xD;
	&#xD;
}&#xD;
else{&#xD;
	tw.local.haveRunningRequest = true;&#xD;
	tw.local.isvalid = false;&#xD;
	tw.local.massege = "this request number has Running Request";&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>230e600b-e7fd-4169-9748-6ef019df0c97</guid>
                <versionId>c702172f-c060-4e29-a155-dfdb5b4f5842</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>check idc type</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.55bc5585-b15d-4c7f-9b12-c9031438532b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:45f</guid>
            <versionId>727bdb25-df18-4f74-ba8a-dd047b6cff6b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="59" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.55bc5585-b15d-4c7f-9b12-c9031438532b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.IDCRequest.IDCRequestType.englishdescription =="IDC Completion" ) {&#xD;
&#xD;
	if (tw.local.parentIDC.IDCRequestStage =="Final") {&#xD;
		tw.local.massege = "can not make IDC Completion request for request its stage Final";&#xD;
		tw.local.isvalid = false;&#xD;
	}&#xD;
	else{&#xD;
		tw.local.isvalid = true;&#xD;
	}&#xD;
	&#xD;
}else if (tw.local.IDCRequest.IDCRequestType.englishdescription =="IDC Payment" ) {&#xD;
&#xD;
	if (tw.local.parentIDC.IDCRequestStage != "Final") {&#xD;
		tw.local.massege = "can not make IDC Payment request for request its stage initial";&#xD;
		tw.local.isvalid = false;&#xD;
	}&#xD;
	else if(tw.local.parentIDC.paymentTerms.englishdescription == "Sight"){&#xD;
		tw.local.massege = "can not make IDC Payment request for request its payment Terms Sight";&#xD;
		tw.local.isvalid = false;&#xD;
	}else if(tw.local.parentIDC.financialDetails.amtSight &gt; 0 ){&#xD;
		tw.local.massege = "can not make IDC Payment request for request its sight amount more than 0";&#xD;
		tw.local.isvalid = false;&#xD;
	}&#xD;
	else{&#xD;
		tw.local.isvalid = true;&#xD;
	}&#xD;
}&#xD;
else{&#xD;
	tw.local.isvalid = true;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>2c354606-cc3e-4791-9efd-b0ef3ec38b1a</guid>
                <versionId>2962045e-8976-4932-a4db-f5372e4238cb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.ae759aeb-1b3e-43b7-8854-f030d4a7b11f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:466</guid>
            <versionId>94746ce8-75fb-487b-a4d4-502839e83406</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="161" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.ae759aeb-1b3e-43b7-8854-f030d4a7b11f</switchId>
                <guid>31637303-56cb-496a-8907-68970f8abb55</guid>
                <versionId>84f94434-9193-4c51-8574-aba9845aaddb</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.f4871456-120d-4758-a266-e103f6cbd58c</switchConditionId>
                    <switchId>3013.ae759aeb-1b3e-43b7-8854-f030d4a7b11f</switchId>
                    <seq>1</seq>
                    <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709e</endStateId>
                    <condition>tw.local.isvalid	  ==	  false</condition>
                    <guid>b74e6f87-937e-485c-971e-1c89b6c5091e</guid>
                    <versionId>c61d957d-17bc-4f9a-99ab-d7774ea9d2e8</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e363cb85-8a6a-4690-a741-9dfb5644a68c</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Init SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bdcd45c3-a548-427c-a22a-f92e0fb95d3e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:46e</guid>
            <versionId>9e4d8bb1-1f04-4bf6-9158-36108d07faf0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="945" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomRight</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bdcd45c3-a548-427c-a22a-f92e0fb95d3e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql = "SELECT max(ID) FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER like ? AND REQUEST_STATUS != 'Completed' AND REQUEST_STATUS != 'Terminated' AND REQUEST_STATUS != 'Canceled'";&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]  = new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value = tw.local.requestNumber+"%";</script>
                <isRule>false</isRule>
                <guid>5210f982-2e5d-4062-aed5-9755594493a1</guid>
                <versionId>2daf86ec-7646-458c-99ae-73d255ba99f7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.11362e14-55ce-442b-9b59-474f682d1754</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:469</guid>
            <versionId>9fdaa578-1439-4bd5-bec5-f089f2bc2078</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1724" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.11362e14-55ce-442b-9b59-474f682d1754</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>752905ce-a912-4423-b531-ab25848a9a4b</guid>
                <versionId>86a4b4fc-f9b9-4e48-bc97-fbfa18a292d2</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Parse IDC Request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bd2fd443-cc6c-43f5-a7b2-92f90c48f314</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:465</guid>
            <versionId>a615f1bb-0c50-4f29-bf05-82b9655de2ee</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="669" y="58">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bd2fd443-cc6c-43f5-a7b2-92f90c48f314</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
tw.local.isTotalyPaid = false;&#xD;
if (tw.local.SCQueryBCContractRes.OutstandingAmount != "" &amp;&amp; tw.local.SCQueryBCContractRes.OutstandingAmount!=null &amp;&amp; tw.local.SCQueryBCContractRes.OutstandingAmount != undefined) {&#xD;
	tw.local.idcContract.outstandingAmount = parseFloat(tw.local.SCQueryBCContractRes.OutstandingAmount);&#xD;
}&#xD;
&#xD;
if (tw.local.idcContract.outstandingAmount == 0) {&#xD;
	tw.local.isvalid = false;&#xD;
	tw.local.isTotalyPaid = true;&#xD;
	tw.local.massege = "this request is totally paid";&#xD;
}else{&#xD;
	tw.local.isvalid = true;&#xD;
&#xD;
 	tw.local.idcContract.IDCProduct.code = tw.local.SCQueryBCContractRes.ProductCode;&#xD;
 	tw.local.idcContract.IDCProduct.englishdescription = tw.local.SCQueryBCContractRes.ProductDesc;&#xD;
	 &#xD;
//	tw.local.idcContract.IDCRequestStage = tw.local.SCQueryBCContractRes.Stag;&#xD;
	tw.local.idcContract.userReference = tw.local.SCQueryBCContractRes.UseRefNum;&#xD;
	if (tw.local.SCQueryBCContractRes.Amount != "" &amp;&amp; tw.local.SCQueryBCContractRes.Amount != null &amp;&amp; tw.local.SCQueryBCContractRes.Amount != undefined) {&#xD;
		tw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);&#xD;
	}&#xD;
	&#xD;
//	if (tw.local.SCQueryBCContractRes.Amount != "" &amp;&amp; tw.local.SCQueryBCContractRes.Amount != null &amp;&amp; tw.local.SCQueryBCContractRes.Amount != undefined) {&#xD;
//		tw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);&#xD;
//	}&#xD;
&#xD;
	tw.local.idcContract.billCurrency.code = tw.local.SCQueryBCContractRes.Currency;&#xD;
&#xD;
	if (tw.local.SCQueryBCContractRes.BaseDate != "" &amp;&amp; tw.local.SCQueryBCContractRes.BaseDate != null &amp;&amp; tw.local.SCQueryBCContractRes.BaseDate != undefined) {&#xD;
		tw.local.idcContract.transactionBaseDate = new Date();&#xD;
		tw.local.idcContract.transactionBaseDate.parse(tw.local.SCQueryBCContractRes.BaseDate, "yyyy-MM-dd");&#xD;
	}&#xD;
	&#xD;
	if (tw.local.SCQueryBCContractRes.ValueDate != "" &amp;&amp; tw.local.SCQueryBCContractRes.ValueDate != null &amp;&amp; tw.local.SCQueryBCContractRes.ValueDate != undefined) {&#xD;
		tw.local.idcContract.transactionValueDate = new Date();&#xD;
		tw.local.idcContract.transactionValueDate.parse(tw.local.SCQueryBCContractRes.ValueDate, "yyyy-MM-dd");&#xD;
	}&#xD;
	&#xD;
	if (tw.local.SCQueryBCContractRes.TenorDays != "" &amp;&amp; tw.local.SCQueryBCContractRes.TenorDays != null &amp;&amp; tw.local.SCQueryBCContractRes.TenorDays != undefined) {&#xD;
		tw.local.idcContract.transactionTenorDays = parseInt(tw.local.SCQueryBCContractRes.TenorDays);&#xD;
	}&#xD;
&#xD;
	if (tw.local.SCQueryBCContractRes.TransitDays != "" &amp;&amp; tw.local.SCQueryBCContractRes.TransitDays != null &amp;&amp; tw.local.SCQueryBCContractRes.TransitDays != undefined) {&#xD;
		tw.local.idcContract.transactionTransitDays = parseInt(tw.local.SCQueryBCContractRes.TransitDays);&#xD;
	}&#xD;
	&#xD;
	if (tw.local.SCQueryBCContractRes.MaturityDate != "" &amp;&amp; tw.local.SCQueryBCContractRes.MaturityDate != null &amp;&amp; tw.local.SCQueryBCContractRes.MaturityDate != undefined) {&#xD;
		tw.local.idcContract.transactionMaturityDate = new Date();&#xD;
		tw.local.idcContract.transactionMaturityDate.parse(tw.local.SCQueryBCContractRes.MaturityDate, "yyyy-MM-dd");&#xD;
	}&#xD;
&#xD;
&#xD;
	for (var i=0; i&lt;tw.local.SCQueryBCContractRes.Contract_Parties.listLength; i++) {&#xD;
		tw.local.idcContract.party[i] = new tw.object.Parties();&#xD;
		tw.local.idcContract.party[i].partyCIF = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;&#xD;
		tw.local.idcContract.party[i].partyId = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;&#xD;
		tw.local.idcContract.party[i].partyType = new tw.object.NameValuePair();&#xD;
		tw.local.idcContract.party[i].partyType.name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;&#xD;
		tw.local.idcContract.party[i].partyType.value = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;&#xD;
		tw.local.idcContract.party[i].name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyName;&#xD;
		tw.local.idcContract.party[i].country = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyCountryCode;&#xD;
		tw.local.idcContract.party[i].language = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyLang;&#xD;
		tw.local.idcContract.party[i].reference = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyRefNum;&#xD;
		tw.local.idcContract.party[i].address1 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress1;&#xD;
		tw.local.idcContract.party[i].address2 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress2;&#xD;
		tw.local.idcContract.party[i].address3 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress3;&#xD;
		tw.local.idcContract.party[i].address4 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress4;&#xD;
		if(tw.local.idcContract.party[i].partyType.name == "REMITTING BANK" &amp;&amp; tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr!=null &amp;&amp; tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0] !=null )		 &#xD;
		{&#xD;
		   tw.local.idcContract.party[i].media = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].MEDIA;&#xD;
		   tw.local.idcContract.party[i].address = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].ADDR;&#xD;
		}&#xD;
		if(tw.local.idcContract.party[i].partyType.name == "REMITTING BANK")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Remitting Bank";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Remitting Bank";&#xD;
		}&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "DRAWEE")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Drawee";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Drawee";&#xD;
		}&#xD;
		&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "DRAWER")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Drawer";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Drawer";&#xD;
		}&#xD;
		&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "ACCOUNTEE")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Accountee";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Accountee";&#xD;
		}&#xD;
		&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "CASE IN NEED")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Case in Need";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Case in Need";&#xD;
		}&#xD;
	}&#xD;
	&#xD;
	if(tw.local.SCQueryBCContractRes.FromDate != null &amp;&amp; tw.local.SCQueryBCContractRes.FromDate != "" )&#xD;
	{&#xD;
	    tw.local.idcContract.interestFromDate = new Date();&#xD;
	    tw.local.idcContract.interestFromDate.parse(tw.local.SCQueryBCContractRes.FromDate, "yyyy-MM-dd");&#xD;
	}&#xD;
	if(tw.local.SCQueryBCContractRes.Todate != null &amp;&amp; tw.local.SCQueryBCContractRes.Todate != "" )&#xD;
	{&#xD;
	     tw.local.idcContract.interestToDate = new Date();&#xD;
	     tw.local.idcContract.interestToDate.parse(tw.local.SCQueryBCContractRes.Todate, "yyyy-MM-dd");&#xD;
	}&#xD;
	&#xD;
//	if (tw.local.SCQueryBCContractRes.Collateral_details != null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0] !=null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral !=null) {&#xD;
//	      &#xD;
//	   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount != null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount !="" )&#xD;
//	      tw.local.idcContract.collateralAmount = parseFloat(tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount);&#xD;
//	   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY != null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY !="" )&#xD;
//	      tw.local.idcContract.collateralCurrency.code = tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY;&#xD;
//		&#xD;
//	}&#xD;
	&#xD;
	tw.local.idcContract.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();&#xD;
	tw.local.idcContract.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountBranchCode ="";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountClass = "Customer Account";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountCurrency ="";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountNumber = new tw.object.NameValuePair();&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountNumber.name ="";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountNumber.value = ""; &#xD;
	&#xD;
	&#xD;
	tw.local.parentIDCContract = new tw.object.IDCContract();&#xD;
	tw.local.parentIDCContract = tw.local.idcContract;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>213e1bc9-44b9-4b44-b6e6-4728ed0a74de</guid>
                <versionId>e852b106-fa1f-4a41-94dd-22e32b8873f8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.668a7876-1d21-4b1f-b557-61caca4c591d</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.30ead00c-943e-4266-ad67-1aad972f8049</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:46a</guid>
            <versionId>ceca3c33-ed45-42d3-9371-6d746220e56c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="245" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.30ead00c-943e-4266-ad67-1aad972f8049</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.idcContract= new tw.object.IDCContract();&#xD;
&#xD;
//tw.local.idcContract.collateralAmount = 0;&#xD;
&#xD;
tw.local.idcContract.settlementAccounts = new tw.object.listOf.SettlementAccount();&#xD;
tw.local.idcContract.settlementAccounts[0] = new tw.object.SettlementAccount();&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = "Customer Account";&#xD;
tw.local.idcContract.settlementAccounts[0].accountNumberList = new tw.object.listOf.NameValuePair();&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();&#xD;
&#xD;
tw.local.idcContract.swiftMessageData = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediary = {};&#xD;
tw.local.idcContract.swiftMessageData.accountWithInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.receiverCorrespondent = {};&#xD;
tw.local.idcContract.swiftMessageData.detailsOfPayment = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.orderingInstitution = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};&#xD;
tw.local.idcContract.swiftMessageData.orderingCustomer = {};&#xD;
tw.local.idcContract.swiftMessageData.senderToReciever = {};&#xD;
 &#xD;
tw.local.idcContract.IDCProduct= new tw.object.DBLookup();&#xD;
//tw.local.idcContract.IDCProduct.code = "IAVC";&#xD;
//tw.local.idcContract.IDCProduct.englishdescription = "INCOMING AVALIZED BILLS UNDER COLLECTION";&#xD;
//tw.local.idcContract.userReference = "*********";&#xD;
//tw.local.idcContract.billAmount = 100;&#xD;
tw.local.idcContract.billCurrency = new tw.object.DBLookup();&#xD;
//tw.local.idcContract.billCurrency.code = "EGP";&#xD;
//tw.local.idcContract.sourceReference = "1111111111111111";&#xD;
//tw.local.idcContract.isLimitsTrackingRequired = false;&#xD;
tw.local.idcContract.interestToDate = new TWDate();&#xD;
tw.local.idcContract.transactionMaturityDate = new TWDate();&#xD;
tw.local.idcContract.transactionBaseDate = new TWDate();&#xD;
//tw.local.idcContract.tradeFinanceApprovalNumber = "12345";&#xD;
tw.local.idcContract.FCContractNumber = tw.local.IDCRequest.FCContractNumber;&#xD;
tw.local.idcContract.collateralCurrency = new tw.object.DBLookup();&#xD;
//tw.local.idcContract.collateralCurrency.code = "";&#xD;
//tw.local.idcContract.interestRate = 44.0;&#xD;
//tw.local.idcContract.transactionTransitDays = 44;&#xD;
&#xD;
tw.local.idcContract.advices = new tw.object.listOf.ContractAdvice();&#xD;
//tw.local.idcContract.advices[0] = new tw.object.ContractAdvice();&#xD;
//tw.local.idcContract.advices[0].adviceCode = "FCEWNKMC";&#xD;
//tw.local.idcContract.advices[0].suppressed = true;&#xD;
//tw.local.idcContract.advices[0].advicelines = new tw.object.SwiftMessagePart();&#xD;
//tw.local.idcContract.advices[0].advicelines.line1 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line2 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line3 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line4 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line5 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line6 = "test";&#xD;
&#xD;
&#xD;
tw.local.idcContract.party = new tw.object.listOf.Parties();&#xD;
tw.local.idcContract.party[0] = new tw.object.Parties();&#xD;
tw.local.idcContract.party[0].partyType = {};&#xD;
tw.local.idcContract.party[0].partyType.name = "Drawee";&#xD;
tw.local.idcContract.party[0].partyType.value = "Drawee";&#xD;
tw.local.idcContract.party[0].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
tw.local.idcContract.party[0].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
tw.local.idcContract.party[0].name = tw.local.IDCRequest.customerInformation.customerName;&#xD;
tw.local.idcContract.party[0].country = "EG";&#xD;
tw.local.idcContract.party[0].reference = "NO REF";&#xD;
tw.local.idcContract.party[0].address1 = tw.local.IDCRequest.customerInformation.addressLine1;&#xD;
tw.local.idcContract.party[0].address2 =tw.local.IDCRequest.customerInformation.addressLine2;&#xD;
tw.local.idcContract.party[0].address3 = tw.local.IDCRequest.customerInformation.addressLine3;&#xD;
tw.local.idcContract.party[0].address4 = "";&#xD;
tw.local.idcContract.party[0].media = "";&#xD;
&#xD;
tw.local.idcContract.party[0].phone = "";&#xD;
tw.local.idcContract.party[0].fax = "";&#xD;
tw.local.idcContract.party[0].email = "";&#xD;
tw.local.idcContract.party[0].contactPersonName = "";&#xD;
tw.local.idcContract.party[0].mobile = "";&#xD;
tw.local.idcContract.party[0].branch = {}&#xD;
tw.local.idcContract.party[0].branch.name = "";&#xD;
tw.local.idcContract.party[0].branch.value = "";&#xD;
tw.local.idcContract.party[0].language = "";&#xD;
&#xD;
tw.local.idcContract.party[0].isNbeCustomer = true;&#xD;
tw.local.idcContract.party[1] = new tw.object.Parties();&#xD;
tw.local.idcContract.party[1].partyType = {};&#xD;
tw.local.idcContract.party[1].partyType.name = "Drawer";&#xD;
tw.local.idcContract.party[1].partyType.value = "Drawer";&#xD;
//tw.local.idcContract.party[1].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
//tw.local.idcContract.party[1].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
//tw.local.idcContract.party[1].name = tw.local.IDCRequest.customerInformation.customerName;&#xD;
//tw.local.idcContract.party[1].country = "EG";&#xD;
//tw.local.idcContract.party[1].reference = "NO REF";&#xD;
//tw.local.idcContract.party[1].address1 = tw.local.IDCRequest.customerInformation.addressLine1;&#xD;
//tw.local.idcContract.party[1].address2 =tw.local.IDCRequest.customerInformation.addressLine2;&#xD;
//tw.local.idcContract.party[1].address3 = "";&#xD;
//tw.local.idcContract.party[1].address4 = "";&#xD;
//tw.local.idcContract.party[1].media = "";&#xD;
&#xD;
//tw.local.idcContract.party[1].phone = "";&#xD;
//tw.local.idcContract.party[1].fax = "";&#xD;
//tw.local.idcContract.party[1].email = "";&#xD;
//tw.local.idcContract.party[1].contactPersonName = "";&#xD;
//tw.local.idcContract.party[1].mobile = "";&#xD;
//tw.local.idcContract.party[1].branch = {}&#xD;
//tw.local.idcContract.party[1].branch.name = "";&#xD;
//tw.local.idcContract.party[1].branch.value = "";&#xD;
//tw.local.idcContract.party[1].language = "";&#xD;
//tw.local.idcContract.party[1].isNbeCustomer = true;&#xD;
&#xD;
//tw.local.idcContract.IDCRequestStage = "Finial";&#xD;
tw.local.idcContract.transactionValueDate = new TWDate();&#xD;
//tw.local.idcContract.transactionTenorDays = 5;&#xD;
&#xD;
tw.local.idcContract.interestFromDate = new TWDate();&#xD;
//tw.local.idcContract.interestAmount = 100;&#xD;
//tw.local.idcContract.haveInterest = true;&#xD;
tw.local.idcContract.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.accountNumberList[0].name = "12345";&#xD;
//tw.local.idcContract.accountNumberList[0].value = "12345";&#xD;
tw.local.idcContract.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();&#xD;
//tw.local.idcContract.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();&#xD;
//tw.local.idcContract.facilities[0].facilityCode = "MMNUFY";&#xD;
//tw.local.idcContract.facilities[0].overallLimit = 11.0;&#xD;
//tw.local.idcContract.facilities[0].limitAmount = 110;&#xD;
//tw.local.idcContract.facilities[0].effectiveLimitAmount = 11.0;&#xD;
//tw.local.idcContract.facilities[0].availableAmount = 13.0;&#xD;
//tw.local.idcContract.facilities[0].expiryDate = new TWDate();&#xD;
//tw.local.idcContract.facilities[0].availableFlag = true;&#xD;
//tw.local.idcContract.facilities[0].authorizedFlag = true;&#xD;
//tw.local.idcContract.facilities[0].Utilization = 0.0;&#xD;
//tw.local.idcContract.facilities[0].returnCode = "12345";&#xD;
//tw.local.idcContract.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineCode = "fdewa";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineAmount = 3452.0;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].availableAmount = 876.0;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].effectiveLineAmount = 234560.0;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].expiryDate = new TWDate();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.name = "001";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.value = "001";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].availableFlag = true;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].authorizedFlag = true;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityPercentageToBook = 100;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].internalRemarks = "gwd";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].purpose = "ekwsaa";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LCCommissionPercentage = "112";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LCDef = "eell";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LCCashCover = "mcdms";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].IDCCommission = "dkmc";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].IDCAvalCommPercentage = "elm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].IDCCashCoverPercentage = "csa";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].debitAccountNumber = "dlm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineCurrency = "EGP";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineSerialNumber = "eele";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].returnCode = "4343";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCommission = "433";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_BidBond = "434";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_Performance = "434";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = "1234";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "ewdew";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "ewd";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "kmk";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "dkkd,";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].name = "ked";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].code = "kee";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].source = "iinkl";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].status = "eoeo";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].name = "mdmdm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].code = "ieii";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].source = "nsamls";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].status = "wodmow,";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].name = "wmd;wmq";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].code = "lkwmdw";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].source = "alwmda;lm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].status = "aswdcnk";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].name = "iuyt";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].code = "lkjh";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].source = ",mnbv";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].status = "ujmtgv";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].CIF = "12345678";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].partyType.name = "ddddmm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].partyType.value = "ujm tg";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityID = "ertyuio";&#xD;
//tw.local.idcContract.facilities[0].status = "rururur";&#xD;
//tw.local.idcContract.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.facilities[0].facilityCurrency.name = "rjrjrj";&#xD;
//tw.local.idcContract.facilities[0].facilityCurrency.value = "enene";&#xD;
//tw.local.idcContract.facilities[0].facilityID = "11111111";&#xD;
//tw.local.idcContract.limitsTrackingVIs = "DEFAULT";&#xD;
//tw.local.idcContract.interestVisibility = "DEFAULT";&#xD;
//tw.local.idcContract.adviceVis = "DEFAULT";&#xD;
//tw.local.idcContract.outstandingAmount = 100.0;</script>
                <isRule>false</isRule>
                <guid>c991f97f-71da-4fb6-ada9-297effc7dcd8</guid>
                <versionId>cdb77e74-4ae3-4020-a8f8-def355dbfede</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0187f9c3-bf28-41ae-a7e6-b3c354c869d3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:463</guid>
            <versionId>e1b7eb61-f334-4d06-a4ca-ab5b5567b5c0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="1690" y="267">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0187f9c3-bf28-41ae-a7e6-b3c354c869d3</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.error != undefined &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;&#xD;
</script>
                <isRule>false</isRule>
                <guid>6310493e-2bac-4414-9c09-77185f37c8f4</guid>
                <versionId>b021ad52-ae08-4859-b57f-1586c9c1ec4f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.83b89cca-8a42-4643-a747-1ac33270f64f</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>Get IDC Contract Facilities</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.7539663d-c24b-42cf-b3b6-add6e6f675d4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:46d</guid>
            <versionId>e3d1d202-2928-4e68-9815-0b0e2280ac0b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="779" y="60">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error9</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.7539663d-c24b-42cf-b3b6-add6e6f675d4</subProcessId>
                <attachedProcessRef>/1.42f20f4f-289f-432d-9bd8-3837be1dc61d</attachedProcessRef>
                <guid>3b90266e-cfa6-4dad-a396-d0d01285bdd3</guid>
                <versionId>eb609455-04a5-4000-bd28-43ac36669952</versionId>
                <parameterMapping name="SCQueryBCContractRes">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.256c0a40-3e73-4b9a-b4aa-79a7919727e7</parameterMappingId>
                    <processParameterId>2055.5ee29ebe-48c3-4dc5-8fda-5433c9d3151d</processParameterId>
                    <parameterMappingParentId>3012.7539663d-c24b-42cf-b3b6-add6e6f675d4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.SCQueryBCContractRes</value>
                    <classRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>18f91d3e-79db-4f7f-b8a2-17173b2c6815</guid>
                    <versionId>364221c3-ca41-48a4-b78c-454fe0b3d161</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="idcContract">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7ecb4a57-647e-4819-9ff7-f4d3053ce8c2</parameterMappingId>
                    <processParameterId>2055.e36b1711-d92f-4f79-8134-45f6ebe54fe7</processParameterId>
                    <parameterMappingParentId>3012.7539663d-c24b-42cf-b3b6-add6e6f675d4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcContract</value>
                    <classRef>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>404af59e-de91-47d8-b5fa-8fd578b45a96</guid>
                    <versionId>36533d2d-4518-47ae-a8c0-213e30a3f620</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="idcContract">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a3ca88a0-5d18-4ee5-8b24-857f81a9bb07</parameterMappingId>
                    <processParameterId>2055.10751aed-d9b5-4b76-804f-3a33b2a7eb76</processParameterId>
                    <parameterMappingParentId>3012.7539663d-c24b-42cf-b3b6-add6e6f675d4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.idcContract</value>
                    <classRef>/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>01b7cb3f-fbc7-48f8-8283-893dad259dcc</guid>
                    <versionId>d9a36bc9-96bc-4a6b-a92c-8079735fef08</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b493bed2-487f-4a47-abb3-d51787b640ad</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>SQL Execute Statement (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.5b45e4b7-705e-4862-8053-8302de42427c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:467</guid>
            <versionId>ec05dfa1-cfe0-48c0-963d-504ffadb4eda</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1060" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error4</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b6c1db471:463</errorHandlerItem>
                <errorHandlerItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.5b45e4b7-705e-4862-8053-8302de42427c</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>aef03b02-3781-4cd8-8d17-af8aa49beadd</guid>
                <versionId>a165a8e9-749b-4bea-bd35-b06b35383ade</versionId>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.349cc1e5-fb6b-4228-a37e-e8a047653171</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.5b45e4b7-705e-4862-8053-8302de42427c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>a22b0e80-2488-44c5-a9bb-5f7242402ddd</guid>
                    <versionId>301050e4-2c3b-42df-9a18-0127db2375f4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5451706f-294e-44db-a324-df7ed0ceb918</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.5b45e4b7-705e-4862-8053-8302de42427c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6c895a9f-a1c0-4cbe-953d-c8899bc6bd01</guid>
                    <versionId>99c1e5ad-**************-da4faff50e78</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.52c10f11-6ebb-44a6-bf82-2670731c0caf</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.5b45e4b7-705e-4862-8053-8302de42427c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>a1885bd2-afa0-4791-8b48-eeffff68fbeb</guid>
                    <versionId>9e1c7e67-f064-4c2e-930a-86aef92e51a3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bf2fdc9d-b624-4250-a37f-fe1cd646e42b</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.5b45e4b7-705e-4862-8053-8302de42427c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cd8dcfb1-5b5b-4e10-9809-333ba97feb89</guid>
                    <versionId>b761b87a-0fe6-4c79-805d-a104201d5458</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.32a47ce2-ec5b-417e-8edf-13ea248717bf</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.5b45e4b7-705e-4862-8053-8302de42427c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>84c91326-2205-4a05-976d-c695aa4e6d37</guid>
                    <versionId>d8457a08-86fc-4bb8-9692-7b291a203c1a</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a054c73c-8d25-4717-b21d-e82248d07389</processItemId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <name>isSuccess?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.52021517-60b7-42af-9ca5-9932c15def1f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:46b</guid>
            <versionId>fee674f7-fd27-4ea3-b662-e865ca78e43f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="511" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.52021517-60b7-42af-9ca5-9932c15def1f</switchId>
                <guid>d07f0568-d8cc-480e-8cc4-e5c189757166</guid>
                <versionId>c9c81ac0-90d7-4377-9440-4e746ecc192a</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.7c46ab86-5230-4e18-b528-c68bd49d9b6f</switchConditionId>
                    <switchId>3013.52021517-60b7-42af-9ca5-9932c15def1f</switchId>
                    <seq>1</seq>
                    <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709d</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>2990acbc-3882-419e-a2bb-cb86a47777a7</guid>
                    <versionId>f236f6a4-2dfb-4b45-8ad9-035bfba4f88a</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.0c9cea64-512a-42fa-83d6-c989ea8cc75f</epvProcessLinkId>
            <epvId>/21.0bb89e09-258a-4bd1-b3a7-137a3219209f</epvId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <guid>d4c947a5-ae29-469d-b693-924fdead9925</guid>
            <versionId>5d81f3bf-7fb9-44ef-94de-ead1656aa346</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="5" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check Parent Request" id="1.545f4988-f748-4797-90c9-d2c7c8a98082" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.0bb89e09-258a-4bd1-b3a7-137a3219209f" epvProcessLinkId="0e8c06c1-98e7-4683-823d-ff68ddde78ce" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="requestNumber" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.52711b62-7df7-40b3-aa2d-0338fa7284dd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"07702230001069"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.82d72677-767a-4f2c-bc85-8e8dcdebf086">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.IDCContract();
autoObject.collateralAmount = 0.0;
autoObject.userReference = "";
autoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();
autoObject.settlementAccounts[0] = new tw.object.SettlementAccount();
autoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.settlementAccounts[0].debitedAccount.balanceSign = "";
autoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.settlementAccounts[0].debitedAccount.accountBranchCode = "";
autoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;
autoObject.settlementAccounts[0].debitedAccount.accountNumber = "";
autoObject.settlementAccounts[0].debitedAccount.accountClass = "";
autoObject.settlementAccounts[0].debitedAccount.ownerAccounts = "";
autoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].debitedAccount.currency.name = "";
autoObject.settlementAccounts[0].debitedAccount.currency.value = "";
autoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;
autoObject.settlementAccounts[0].debitedAccount.commCIF = "";
autoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;
autoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;
autoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;
autoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.settlementAccounts[0].accountNumberList[0].name = "";
autoObject.settlementAccounts[0].accountNumberList[0].value = "";
autoObject.settlementAccounts[0].settCIF = "";
autoObject.billAmount = 0.0;
autoObject.billCurrency = new tw.object.DBLookup();
autoObject.billCurrency.id = 0;
autoObject.billCurrency.code = "";
autoObject.billCurrency.arabicdescription = "";
autoObject.billCurrency.englishdescription = "";
autoObject.party = new tw.object.listOf.Parties();
autoObject.party[0] = new tw.object.Parties();
autoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].partyType.name = "";
autoObject.party[0].partyType.value = "";
autoObject.party[0].partyId = "";
autoObject.party[0].name = "";
autoObject.party[0].country = "";
autoObject.party[0].reference = "";
autoObject.party[0].address1 = "";
autoObject.party[0].address2 = "";
autoObject.party[0].address3 = "";
autoObject.party[0].address4 = "";
autoObject.party[0].media = "";
autoObject.party[0].address = "";
autoObject.party[0].phone = "";
autoObject.party[0].fax = "";
autoObject.party[0].email = "";
autoObject.party[0].contactPersonName = "";
autoObject.party[0].mobile = "";
autoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.party[0].branch.name = "";
autoObject.party[0].branch.value = "";
autoObject.party[0].language = "";
autoObject.party[0].partyCIF = "";
autoObject.party[0].isNbeCustomer = false;
autoObject.party[0].isRetrived = false;
autoObject.party[0].cifVis = "";
autoObject.sourceReference = "";
autoObject.isLimitsTrackingRequired = false;
autoObject.liquidationSummary = new tw.object.LiquidationSummary();
autoObject.liquidationSummary.liquidationCurrency = "";
autoObject.liquidationSummary.debitBasisby = "";
autoObject.liquidationSummary.liquidationAmt = 0.0;
autoObject.liquidationSummary.debitValueDate = new TWDate();
autoObject.liquidationSummary.creditValueDate = new TWDate();
autoObject.IDCProduct = new tw.object.DBLookup();
autoObject.IDCProduct.id = 0;
autoObject.IDCProduct.code = "";
autoObject.IDCProduct.arabicdescription = "";
autoObject.IDCProduct.englishdescription = "";
autoObject.interestToDate = new TWDate();
autoObject.transactionMaturityDate = new TWDate();
autoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();
autoObject.commissionsAndCharges[0].component = "";
autoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();
autoObject.commissionsAndCharges[0].debitedAccount.balanceSign = "";
autoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;
autoObject.commissionsAndCharges[0].debitedAccount.accountNumber = "";
autoObject.commissionsAndCharges[0].debitedAccount.accountClass = "";
autoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].debitedAccount.currency.name = "";
autoObject.commissionsAndCharges[0].debitedAccount.currency.value = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;
autoObject.commissionsAndCharges[0].debitedAccount.commCIF = "";
autoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;
autoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;
autoObject.commissionsAndCharges[0].chargeAmount = 0.0;
autoObject.commissionsAndCharges[0].waiver = false;
autoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;
autoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;
autoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();
autoObject.commissionsAndCharges[0].defaultCurrency.id = 0;
autoObject.commissionsAndCharges[0].defaultCurrency.code = "";
autoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = "";
autoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = "";
autoObject.commissionsAndCharges[0].defaultAmount = 0.0;
autoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.commissionsAndCharges[0].commAccountList[0].name = "";
autoObject.commissionsAndCharges[0].commAccountList[0].value = "";
autoObject.commissionsAndCharges[0].defaultPercentage = 0.0;
autoObject.commissionsAndCharges[0].changePercentage = 0.0;
autoObject.commissionsAndCharges[0].description = "";
autoObject.commissionsAndCharges[0].rateType = "";
autoObject.commissionsAndCharges[0].percentageVis = "";
autoObject.commissionsAndCharges[0].changeAmountVis = "";
autoObject.transactionBaseDate = new TWDate();
autoObject.tradeFinanceApprovalNumber = "";
autoObject.FCContractNumber = "";
autoObject.collateralCurrency = new tw.object.DBLookup();
autoObject.collateralCurrency.id = 0;
autoObject.collateralCurrency.code = "";
autoObject.collateralCurrency.arabicdescription = "";
autoObject.collateralCurrency.englishdescription = "";
autoObject.interestRate = 0.0;
autoObject.transactionTransitDays = 0;
autoObject.swiftMessageData = new tw.object.SwiftMessageData();
autoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediary.line1 = "";
autoObject.swiftMessageData.intermediary.line2 = "";
autoObject.swiftMessageData.intermediary.line3 = "";
autoObject.swiftMessageData.intermediary.line4 = "";
autoObject.swiftMessageData.intermediary.line5 = "";
autoObject.swiftMessageData.intermediary.line6 = "";
autoObject.swiftMessageData.detailsOfCharge = "";
autoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.accountWithInstitution.line1 = "";
autoObject.swiftMessageData.accountWithInstitution.line2 = "";
autoObject.swiftMessageData.accountWithInstitution.line3 = "";
autoObject.swiftMessageData.accountWithInstitution.line4 = "";
autoObject.swiftMessageData.accountWithInstitution.line5 = "";
autoObject.swiftMessageData.accountWithInstitution.line6 = "";
autoObject.swiftMessageData.receiver = "";
autoObject.swiftMessageData.swiftMessageOption = "";
autoObject.swiftMessageData.coverRequired = "";
autoObject.swiftMessageData.transferType = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = "";
autoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = "";
autoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.receiverCorrespondent.line1 = "";
autoObject.swiftMessageData.receiverCorrespondent.line2 = "";
autoObject.swiftMessageData.receiverCorrespondent.line3 = "";
autoObject.swiftMessageData.receiverCorrespondent.line4 = "";
autoObject.swiftMessageData.receiverCorrespondent.line5 = "";
autoObject.swiftMessageData.receiverCorrespondent.line6 = "";
autoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.detailsOfPayment.line1 = "";
autoObject.swiftMessageData.detailsOfPayment.line2 = "";
autoObject.swiftMessageData.detailsOfPayment.line3 = "";
autoObject.swiftMessageData.detailsOfPayment.line4 = "";
autoObject.swiftMessageData.detailsOfPayment.line5 = "";
autoObject.swiftMessageData.detailsOfPayment.line6 = "";
autoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingInstitution.line1 = "";
autoObject.swiftMessageData.orderingInstitution.line2 = "";
autoObject.swiftMessageData.orderingInstitution.line3 = "";
autoObject.swiftMessageData.orderingInstitution.line4 = "";
autoObject.swiftMessageData.orderingInstitution.line5 = "";
autoObject.swiftMessageData.orderingInstitution.line6 = "";
autoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.beneficiaryInstitution.line1 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line2 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line3 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line4 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line5 = "";
autoObject.swiftMessageData.beneficiaryInstitution.line6 = "";
autoObject.swiftMessageData.receiverOfCover = "";
autoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.ultimateBeneficiary.line1 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line2 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line3 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line4 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line5 = "";
autoObject.swiftMessageData.ultimateBeneficiary.line6 = "";
autoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.orderingCustomer.line1 = "";
autoObject.swiftMessageData.orderingCustomer.line2 = "";
autoObject.swiftMessageData.orderingCustomer.line3 = "";
autoObject.swiftMessageData.orderingCustomer.line4 = "";
autoObject.swiftMessageData.orderingCustomer.line5 = "";
autoObject.swiftMessageData.orderingCustomer.line6 = "";
autoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();
autoObject.swiftMessageData.senderToReciever.line1 = "";
autoObject.swiftMessageData.senderToReciever.line2 = "";
autoObject.swiftMessageData.senderToReciever.line3 = "";
autoObject.swiftMessageData.senderToReciever.line4 = "";
autoObject.swiftMessageData.senderToReciever.line5 = "";
autoObject.swiftMessageData.senderToReciever.line6 = "";
autoObject.swiftMessageData.RTGS = "";
autoObject.swiftMessageData.RTGSNetworkType = "";
autoObject.advices = new tw.object.listOf.ContractAdvice();
autoObject.advices[0] = new tw.object.ContractAdvice();
autoObject.advices[0].adviceCode = "";
autoObject.advices[0].suppressed = false;
autoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();
autoObject.advices[0].advicelines.line1 = "";
autoObject.advices[0].advicelines.line2 = "";
autoObject.advices[0].advicelines.line3 = "";
autoObject.advices[0].advicelines.line4 = "";
autoObject.advices[0].advicelines.line5 = "";
autoObject.advices[0].advicelines.line6 = "";
autoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();
autoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();
autoObject.cashCollateralAccounts[0].accountCurrency = "";
autoObject.cashCollateralAccounts[0].accountClass = "";
autoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;
autoObject.cashCollateralAccounts[0].GLAccountNumber = "";
autoObject.cashCollateralAccounts[0].accountBranchCode = "";
autoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.cashCollateralAccounts[0].accountNumber.name = "";
autoObject.cashCollateralAccounts[0].accountNumber.value = "";
autoObject.cashCollateralAccounts[0].isGLFound = false;
autoObject.cashCollateralAccounts[0].isGLVerified = false;
autoObject.IDCRequestStage = "";
autoObject.transactionValueDate = new TWDate();
autoObject.transactionTenorDays = 0;
autoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();
autoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();
autoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.contractLimitsTracking[0].partyType.name = "";
autoObject.contractLimitsTracking[0].partyType.value = "";
autoObject.contractLimitsTracking[0].type = "";
autoObject.contractLimitsTracking[0].jointVentureParent = "";
autoObject.contractLimitsTracking[0].customerNo = "";
autoObject.contractLimitsTracking[0].linkageRefNum = "";
autoObject.contractLimitsTracking[0].amountTag = "";
autoObject.contractLimitsTracking[0].contributionPercentage = 0.0;
autoObject.contractLimitsTracking[0].isCIFfound = false;
autoObject.interestFromDate = new TWDate();
autoObject.interestAmount = 0.0;
autoObject.haveInterest = false;
autoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.accountNumberList[0].name = "";
autoObject.accountNumberList[0].value = "";
autoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();
autoObject.facilities[0].facilityCode = "";
autoObject.facilities[0].overallLimit = 0.0;
autoObject.facilities[0].limitAmount = 0.0;
autoObject.facilities[0].effectiveLimitAmount = 0.0;
autoObject.facilities[0].availableAmount = 0.0;
autoObject.facilities[0].expiryDate = new TWDate();
autoObject.facilities[0].availableFlag = false;
autoObject.facilities[0].authorizedFlag = false;
autoObject.facilities[0].Utilization = 0.0;
autoObject.facilities[0].returnCode = "";
autoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();
autoObject.facilities[0].facilityLines[0].lineCode = "";
autoObject.facilities[0].facilityLines[0].lineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].availableAmount = 0.0;
autoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;
autoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();
autoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].facilityBranch.name = "";
autoObject.facilities[0].facilityLines[0].facilityBranch.value = "";
autoObject.facilities[0].facilityLines[0].availableFlag = false;
autoObject.facilities[0].facilityLines[0].authorizedFlag = false;
autoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;
autoObject.facilities[0].facilityLines[0].internalRemarks = "";
autoObject.facilities[0].facilityLines[0].purpose = "";
autoObject.facilities[0].facilityLines[0].LCCommissionPercentage = "";
autoObject.facilities[0].facilityLines[0].LCDef = "";
autoObject.facilities[0].facilityLines[0].LCCashCover = "";
autoObject.facilities[0].facilityLines[0].IDCCommission = "";
autoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = "";
autoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = "";
autoObject.facilities[0].facilityLines[0].debitAccountNumber = "";
autoObject.facilities[0].facilityLines[0].lineCurrency = "";
autoObject.facilities[0].facilityLines[0].lineSerialNumber = "";
autoObject.facilities[0].facilityLines[0].returnCode = "";
autoObject.facilities[0].facilityLines[0].LGCommission = "";
autoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = "";
autoObject.facilities[0].facilityLines[0].LGCashCover_Performance = "";
autoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = "";
autoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = "";
autoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = "";
autoObject.facilities[0].facilityLines[0].CIF = "";
autoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityLines[0].partyType.name = "";
autoObject.facilities[0].facilityLines[0].partyType.value = "";
autoObject.facilities[0].facilityLines[0].facilityID = "";
autoObject.facilities[0].status = "";
autoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilities[0].facilityCurrency.name = "";
autoObject.facilities[0].facilityCurrency.value = "";
autoObject.facilities[0].facilityID = "";
autoObject.limitsTrackingVIs = "";
autoObject.interestVisibility = "";
autoObject.adviceVis = "";
autoObject.outstandingAmount = 0.0;
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="IDCRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.88e74584-565a-40c2-a722-ade695030bbd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "1234";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "00102230001070";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;

autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="parentIDC" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.455bc6b1-7ba6-4720-94d5-a300a02c504f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.IDCRequest();
autoObject.IDCRequestState = "";
autoObject.commodityDescription = "";
autoObject.countryOfOrigin = new tw.object.DBLookup();
autoObject.countryOfOrigin.id = 0;
autoObject.countryOfOrigin.code = "";
autoObject.countryOfOrigin.arabicdescription = "";
autoObject.countryOfOrigin.englishdescription = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "00102230001070";
autoObject.productsDetails = new tw.object.ProductsDetails();
autoObject.productsDetails.destinationPort = "";
autoObject.productsDetails.shippingDate = new TWDate();
autoObject.productsDetails.HSProduct = new tw.object.DBLookup();
autoObject.productsDetails.HSProduct.id = 0;
autoObject.productsDetails.HSProduct.code = "";
autoObject.productsDetails.HSProduct.arabicdescription = "";
autoObject.productsDetails.HSProduct.englishdescription = "";
autoObject.productsDetails.incoterms = new tw.object.DBLookup();
autoObject.productsDetails.incoterms.id = 0;
autoObject.productsDetails.incoterms.code = "";
autoObject.productsDetails.incoterms.arabicdescription = "";
autoObject.productsDetails.incoterms.englishdescription = "";
autoObject.productsDetails.ACID = "";
autoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();
autoObject.productsDetails.CBECommodityClassification.id = 0;
autoObject.productsDetails.CBECommodityClassification.code = "";
autoObject.productsDetails.CBECommodityClassification.arabicdescription = "";
autoObject.productsDetails.CBECommodityClassification.englishdescription = "";
autoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();
autoObject.productsDetails.shipmentMethod.id = 0;
autoObject.productsDetails.shipmentMethod.code = "";
autoObject.productsDetails.shipmentMethod.arabicdescription = "";
autoObject.productsDetails.shipmentMethod.englishdescription = "";
autoObject.financialDetails = new tw.object.FinancialDetails();
autoObject.financialDetails.isAdvancePaymentsUsed = false;
autoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();
autoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();
autoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;
autoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();
autoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();
autoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = "";
autoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = "";
autoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = "";
autoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;
autoObject.financialDetails.usedAdvancePayment[0].DBID = 0;
autoObject.financialDetails.discountAmt = 0.0;
autoObject.financialDetails.firstInstallementMaturityDate = new TWDate();
autoObject.financialDetails.facilityAmtInDocCurrency = 0.0;
autoObject.financialDetails.amtSight = 0.0;
autoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();
autoObject.financialDetails.beneficiaryDetails.account = "";
autoObject.financialDetails.beneficiaryDetails.bank = "";
autoObject.financialDetails.beneficiaryDetails.correspondentRefNum = "";
autoObject.financialDetails.beneficiaryDetails.name = "";
autoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();
autoObject.financialDetails.beneficiaryDetails.country.id = 0;
autoObject.financialDetails.beneficiaryDetails.country.code = "";
autoObject.financialDetails.beneficiaryDetails.country.arabicdescription = "";
autoObject.financialDetails.beneficiaryDetails.country.englishdescription = "";
autoObject.financialDetails.amtDeferredNoAvalized = 0.0;
autoObject.financialDetails.amtPayableByNBE = 0.0;
autoObject.financialDetails.executionHub = new tw.object.DBLookup();
autoObject.financialDetails.executionHub.id = 0;
autoObject.financialDetails.executionHub.code = "";
autoObject.financialDetails.executionHub.arabicdescription = "";
autoObject.financialDetails.executionHub.englishdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfForeignCurrency.id = 0;
autoObject.financialDetails.sourceOfForeignCurrency.code = "";
autoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = "";
autoObject.financialDetails.sourceOfForeignCurrency.englishdescription = "";
autoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;
autoObject.financialDetails.documentCurrency = new tw.object.DBLookup();
autoObject.financialDetails.documentCurrency.id = 0;
autoObject.financialDetails.documentCurrency.code = "";
autoObject.financialDetails.documentCurrency.arabicdescription = "";
autoObject.financialDetails.documentCurrency.englishdescription = "";
autoObject.financialDetails.daysTillMaturity = 0;
autoObject.financialDetails.tradeFinanceApprovalNumber = "";
autoObject.financialDetails.amountAdvanced = 0.0;
autoObject.financialDetails.paymentAccount = "";
autoObject.financialDetails.tradeFOReferenceNumber = "";
autoObject.financialDetails.documentAmount = 0.0;
autoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();
autoObject.financialDetails.sourceOfFunds.id = 0;
autoObject.financialDetails.sourceOfFunds.code = "";
autoObject.financialDetails.sourceOfFunds.arabicdescription = "";
autoObject.financialDetails.sourceOfFunds.englishdescription = "";
autoObject.financialDetails.chargesAccount = "";
autoObject.financialDetails.CashAmtWithNoCurrency = 0.0;
autoObject.financialDetails.amtDeferredAvalized = 0.0;
autoObject.financialDetails.amtPaidbyOtherBanks = 0.0;
autoObject.financialDetails.cashAmtInDocCurrency = 0.0;
autoObject.IDCRequestType = new tw.object.DBLookup();
autoObject.IDCRequestType.id = 0;
autoObject.IDCRequestType.code = "";
autoObject.IDCRequestType.arabicdescription = "";
autoObject.IDCRequestType.englishdescription = "";
autoObject.isIDCWithdrawn = false;
autoObject.IDCRequestStage = "";
autoObject.FCContractNumber = "123";
autoObject.billOfLading = new tw.object.listOf.Invoice();
autoObject.billOfLading[0] = new tw.object.Invoice();
autoObject.billOfLading[0].date = new TWDate();
autoObject.billOfLading[0].number = "";
autoObject.importPurpose = new tw.object.DBLookup();
autoObject.importPurpose.id = 0;
autoObject.importPurpose.code = "";
autoObject.importPurpose.arabicdescription = "";
autoObject.importPurpose.englishdescription = "";
autoObject.IDCRequestNature = new tw.object.DBLookup();
autoObject.IDCRequestNature.id = 0;
autoObject.IDCRequestNature.code = "";
autoObject.IDCRequestNature.arabicdescription = "";
autoObject.IDCRequestNature.englishdescription = "";
autoObject.customerInformation = new tw.object.CustomerInformation();
autoObject.customerInformation.CIFNumber = "";
autoObject.customerInformation.importCardNumber = "";
autoObject.customerInformation.commercialRegistrationNumber = "";
autoObject.customerInformation.customerName = "";
autoObject.customerInformation.isCustomeSanctionedbyCBE = "";
autoObject.customerInformation.CBENumber = "";
autoObject.customerInformation.customerSector = "";
autoObject.customerInformation.facilityType = new tw.object.DBLookup();
autoObject.customerInformation.facilityType.id = 0;
autoObject.customerInformation.facilityType.code = "";
autoObject.customerInformation.facilityType.arabicdescription = "";
autoObject.customerInformation.facilityType.englishdescription = "";
autoObject.customerInformation.commercialRegistrationOffice = "";
autoObject.customerInformation.taxCardNumber = "";
autoObject.customerInformation.customerType = "";
autoObject.customerInformation.addressLine1 = "";
autoObject.customerInformation.addressLine2 = "";
autoObject.invoices = new tw.object.listOf.Invoice();
autoObject.invoices[0] = new tw.object.Invoice();
autoObject.invoices[0].date = new TWDate();
autoObject.invoices[0].number = "";
autoObject.productCategory = new tw.object.DBLookup();
autoObject.productCategory.id = 0;
autoObject.productCategory.code = "";
autoObject.productCategory.arabicdescription = "";
autoObject.productCategory.englishdescription = "";
autoObject.documentsSource = new tw.object.DBLookup();
autoObject.documentsSource.id = 0;
autoObject.documentsSource.code = "";
autoObject.documentsSource.arabicdescription = "";
autoObject.documentsSource.englishdescription = "";
autoObject.ParentIDCRequestNumber = "";
autoObject.paymentTerms = new tw.object.DBLookup();
autoObject.paymentTerms.id = 0;
autoObject.paymentTerms.code = "";
autoObject.paymentTerms.arabicdescription = "";
autoObject.paymentTerms.englishdescription = "";
autoObject.approvals = new tw.object.Approvals();
autoObject.approvals.CAD = false;
autoObject.approvals.treasury = false;
autoObject.approvals.compliance = false;
autoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();
autoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();
autoObject.appLog[0].startTime = new TWDate();
autoObject.appLog[0].endTime = new TWDate();
autoObject.appLog[0].userName = "";
autoObject.appLog[0].role = "";
autoObject.appLog[0].step = "";
autoObject.appLog[0].action = "";
autoObject.appLog[0].comment = "";
autoObject.appLog[0].terminateReason = "";
autoObject.appLog[0].returnReason = "";
autoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.DBID = 0;
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="isvalid" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.2df15530-52f0-41a9-9c25-bae06fa34398" />
                        
                        
                        <ns16:dataOutput name="massege" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0d917db9-6dd3-40cd-963c-f56294d058e5" />
                        
                        
                        <ns16:dataOutput name="DBID" itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" id="2055.2164b222-615a-4a75-a5d5-98b99470a6ff" />
                        
                        
                        <ns16:dataOutput name="idcContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.60d0706b-a7ec-4ef7-93af-f527d8da9f48" />
                        
                        
                        <ns16:dataOutput name="parentIDCContract" itemSubjectRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1" isCollection="false" id="2055.ef212b69-feda-46ce-97d9-c2a5b4fe91f3" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.5612afbe-e447-4c02-8202-fd20453fd03f" />
                        
                        
                        <ns16:dataOutput name="IDCRequest" itemSubjectRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67" isCollection="false" id="2055.9a3d0a85-297e-4663-93c4-721d815eb332" />
                        
                        
                        <ns16:dataOutput name="folderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.9bd55f86-fcc3-485e-b9f0-c45dede585e9" />
                        
                        
                        <ns16:dataOutput name="fullPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1f4f4e36-97e7-43ae-b14a-21ca79480c5f" />
                        
                        
                        <ns16:dataOutput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0d63f311-de13-4c02-9731-3cae95646591" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.52711b62-7df7-40b3-aa2d-0338fa7284dd</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.82d72677-767a-4f2c-bc85-8e8dcdebf086</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.88e74584-565a-40c2-a722-ade695030bbd</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.455bc6b1-7ba6-4720-94d5-a300a02c504f</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.2df15530-52f0-41a9-9c25-bae06fa34398</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.0d917db9-6dd3-40cd-963c-f56294d058e5</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.2164b222-615a-4a75-a5d5-98b99470a6ff</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.60d0706b-a7ec-4ef7-93af-f527d8da9f48</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.ef212b69-feda-46ce-97d9-c2a5b4fe91f3</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.5612afbe-e447-4c02-8202-fd20453fd03f</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.9a3d0a85-297e-4663-93c4-721d815eb332</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.9bd55f86-fcc3-485e-b9f0-c45dede585e9</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.1f4f4e36-97e7-43ae-b14a-21ca79480c5f</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.0d63f311-de13-4c02-9731-3cae95646591</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c356b96d-9bea-4510-846d-7550da16deaa">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="71e12162-71a9-477f-bd67-96f18c746db8" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>fdaa298b-f679-42de-a255-0af145d71dc0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c2d05175-f6ad-4008-ae10-591e4ee7d02f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8e8fc976-8cfe-45cb-ba5e-ab164ab7313e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a6129d38-34a3-4623-9a9e-d8e404c126d8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f1ee4e76-20d9-4426-a039-72de129ca5a0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e363cb85-8a6a-4690-a741-9dfb5644a68c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b493bed2-487f-4a47-abb3-d51787b640ad</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>36856bd5-8a6d-4350-8553-79c8ef9bd4b6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d08974c7-c6bd-4c5f-b01a-19033aea0dbc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>668a7876-1d21-4b1f-b557-61caca4c591d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b5e33c56-0336-49a0-a352-58d4bc39d4b7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ae1216ae-bd08-45fb-aef8-bce65e2f6426</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0f5e586d-0779-4485-b42a-d58b9bbe59f8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>699b9c4a-04db-420d-87b3-e8cb1c286a0d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e212cedc-cf7c-49ca-9c81-7484a80f478c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>086277b6-1233-4e1f-8b7d-f4d962b10be5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>99072e7b-f2b1-40e9-82b2-c038ce2bcc02</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>59a87880-1c45-4052-ba87-8e54a3b76b24</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1460dc3d-355d-44a6-8b93-3fb0763acfcc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6553d6c2-8d1e-4b49-818c-8a3ee5d79a93</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>21bf961b-7097-4d5b-b1b0-25a6ab444787</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4e8a7062-5cbe-4975-850b-bc22276b37c8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>83b89cca-8a42-4643-a747-1ac33270f64f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f0d946e2-e9b4-4ea8-97f4-478c1d077ee4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a054c73c-8d25-4717-b21d-e82248d07389</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9f0a234f-f85c-4b82-90d7-323282f28c0e</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="fdaa298b-f679-42de-a255-0af145d71dc0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="5" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>194ecb4f-5819-4668-94d2-e221b974d994</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c2d05175-f6ad-4008-ae10-591e4ee7d02f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1724" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:056f27db08707381:36b20ea0:18b6c1db471:469</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>63acd7d4-c71d-4752-80cf-e5876026bcc9</ns16:incoming>
                        
                        
                        <ns16:incoming>fbaa7d14-34b1-42fc-bf90-a11f92481e01</ns16:incoming>
                        
                        
                        <ns16:incoming>ed712f21-360d-4f13-b6c4-65bf051fe108</ns16:incoming>
                        
                        
                        <ns16:incoming>08d3d573-ea3c-4f3b-b713-45fcb07bc0f5</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Parse IDC Request" id="8e8fc976-8cfe-45cb-ba5e-ab164ab7313e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="669" y="58" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ba83e2db-be94-49cc-9b97-4f9ff3e479ee</ns16:incoming>
                        
                        
                        <ns16:outgoing>8ea32b17-d31c-45fc-992d-2e54348e8c9c</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
tw.local.isTotalyPaid = false;&#xD;
if (tw.local.SCQueryBCContractRes.OutstandingAmount != "" &amp;&amp; tw.local.SCQueryBCContractRes.OutstandingAmount!=null &amp;&amp; tw.local.SCQueryBCContractRes.OutstandingAmount != undefined) {&#xD;
	tw.local.idcContract.outstandingAmount = parseFloat(tw.local.SCQueryBCContractRes.OutstandingAmount);&#xD;
}&#xD;
&#xD;
if (tw.local.idcContract.outstandingAmount == 0) {&#xD;
	tw.local.isvalid = false;&#xD;
	tw.local.isTotalyPaid = true;&#xD;
	tw.local.massege = "this request is totally paid";&#xD;
}else{&#xD;
	tw.local.isvalid = true;&#xD;
&#xD;
 	tw.local.idcContract.IDCProduct.code = tw.local.SCQueryBCContractRes.ProductCode;&#xD;
 	tw.local.idcContract.IDCProduct.englishdescription = tw.local.SCQueryBCContractRes.ProductDesc;&#xD;
	 &#xD;
//	tw.local.idcContract.IDCRequestStage = tw.local.SCQueryBCContractRes.Stag;&#xD;
	tw.local.idcContract.userReference = tw.local.SCQueryBCContractRes.UseRefNum;&#xD;
	if (tw.local.SCQueryBCContractRes.Amount != "" &amp;&amp; tw.local.SCQueryBCContractRes.Amount != null &amp;&amp; tw.local.SCQueryBCContractRes.Amount != undefined) {&#xD;
		tw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);&#xD;
	}&#xD;
	&#xD;
//	if (tw.local.SCQueryBCContractRes.Amount != "" &amp;&amp; tw.local.SCQueryBCContractRes.Amount != null &amp;&amp; tw.local.SCQueryBCContractRes.Amount != undefined) {&#xD;
//		tw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);&#xD;
//	}&#xD;
&#xD;
	tw.local.idcContract.billCurrency.code = tw.local.SCQueryBCContractRes.Currency;&#xD;
&#xD;
	if (tw.local.SCQueryBCContractRes.BaseDate != "" &amp;&amp; tw.local.SCQueryBCContractRes.BaseDate != null &amp;&amp; tw.local.SCQueryBCContractRes.BaseDate != undefined) {&#xD;
		tw.local.idcContract.transactionBaseDate = new Date();&#xD;
		tw.local.idcContract.transactionBaseDate.parse(tw.local.SCQueryBCContractRes.BaseDate, "yyyy-MM-dd");&#xD;
	}&#xD;
	&#xD;
	if (tw.local.SCQueryBCContractRes.ValueDate != "" &amp;&amp; tw.local.SCQueryBCContractRes.ValueDate != null &amp;&amp; tw.local.SCQueryBCContractRes.ValueDate != undefined) {&#xD;
		tw.local.idcContract.transactionValueDate = new Date();&#xD;
		tw.local.idcContract.transactionValueDate.parse(tw.local.SCQueryBCContractRes.ValueDate, "yyyy-MM-dd");&#xD;
	}&#xD;
	&#xD;
	if (tw.local.SCQueryBCContractRes.TenorDays != "" &amp;&amp; tw.local.SCQueryBCContractRes.TenorDays != null &amp;&amp; tw.local.SCQueryBCContractRes.TenorDays != undefined) {&#xD;
		tw.local.idcContract.transactionTenorDays = parseInt(tw.local.SCQueryBCContractRes.TenorDays);&#xD;
	}&#xD;
&#xD;
	if (tw.local.SCQueryBCContractRes.TransitDays != "" &amp;&amp; tw.local.SCQueryBCContractRes.TransitDays != null &amp;&amp; tw.local.SCQueryBCContractRes.TransitDays != undefined) {&#xD;
		tw.local.idcContract.transactionTransitDays = parseInt(tw.local.SCQueryBCContractRes.TransitDays);&#xD;
	}&#xD;
	&#xD;
	if (tw.local.SCQueryBCContractRes.MaturityDate != "" &amp;&amp; tw.local.SCQueryBCContractRes.MaturityDate != null &amp;&amp; tw.local.SCQueryBCContractRes.MaturityDate != undefined) {&#xD;
		tw.local.idcContract.transactionMaturityDate = new Date();&#xD;
		tw.local.idcContract.transactionMaturityDate.parse(tw.local.SCQueryBCContractRes.MaturityDate, "yyyy-MM-dd");&#xD;
	}&#xD;
&#xD;
&#xD;
	for (var i=0; i&lt;tw.local.SCQueryBCContractRes.Contract_Parties.listLength; i++) {&#xD;
		tw.local.idcContract.party[i] = new tw.object.Parties();&#xD;
		tw.local.idcContract.party[i].partyCIF = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;&#xD;
		tw.local.idcContract.party[i].partyId = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;&#xD;
		tw.local.idcContract.party[i].partyType = new tw.object.NameValuePair();&#xD;
		tw.local.idcContract.party[i].partyType.name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;&#xD;
		tw.local.idcContract.party[i].partyType.value = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;&#xD;
		tw.local.idcContract.party[i].name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyName;&#xD;
		tw.local.idcContract.party[i].country = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyCountryCode;&#xD;
		tw.local.idcContract.party[i].language = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyLang;&#xD;
		tw.local.idcContract.party[i].reference = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyRefNum;&#xD;
		tw.local.idcContract.party[i].address1 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress1;&#xD;
		tw.local.idcContract.party[i].address2 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress2;&#xD;
		tw.local.idcContract.party[i].address3 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress3;&#xD;
		tw.local.idcContract.party[i].address4 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress4;&#xD;
		if(tw.local.idcContract.party[i].partyType.name == "REMITTING BANK" &amp;&amp; tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr!=null &amp;&amp; tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0] !=null )		 &#xD;
		{&#xD;
		   tw.local.idcContract.party[i].media = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].MEDIA;&#xD;
		   tw.local.idcContract.party[i].address = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].ADDR;&#xD;
		}&#xD;
		if(tw.local.idcContract.party[i].partyType.name == "REMITTING BANK")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Remitting Bank";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Remitting Bank";&#xD;
		}&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "DRAWEE")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Drawee";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Drawee";&#xD;
		}&#xD;
		&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "DRAWER")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Drawer";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Drawer";&#xD;
		}&#xD;
		&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "ACCOUNTEE")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Accountee";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Accountee";&#xD;
		}&#xD;
		&#xD;
		else if(tw.local.idcContract.party[i].partyType.name == "CASE IN NEED")		 &#xD;
		{&#xD;
			tw.local.idcContract.party[i].partyType.name = "Case in Need";&#xD;
			tw.local.idcContract.party[i].partyType.value = "Case in Need";&#xD;
		}&#xD;
	}&#xD;
	&#xD;
	if(tw.local.SCQueryBCContractRes.FromDate != null &amp;&amp; tw.local.SCQueryBCContractRes.FromDate != "" )&#xD;
	{&#xD;
	    tw.local.idcContract.interestFromDate = new Date();&#xD;
	    tw.local.idcContract.interestFromDate.parse(tw.local.SCQueryBCContractRes.FromDate, "yyyy-MM-dd");&#xD;
	}&#xD;
	if(tw.local.SCQueryBCContractRes.Todate != null &amp;&amp; tw.local.SCQueryBCContractRes.Todate != "" )&#xD;
	{&#xD;
	     tw.local.idcContract.interestToDate = new Date();&#xD;
	     tw.local.idcContract.interestToDate.parse(tw.local.SCQueryBCContractRes.Todate, "yyyy-MM-dd");&#xD;
	}&#xD;
	&#xD;
//	if (tw.local.SCQueryBCContractRes.Collateral_details != null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0] !=null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral !=null) {&#xD;
//	      &#xD;
//	   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount != null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount !="" )&#xD;
//	      tw.local.idcContract.collateralAmount = parseFloat(tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount);&#xD;
//	   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY != null &amp;&amp; tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY !="" )&#xD;
//	      tw.local.idcContract.collateralCurrency.code = tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY;&#xD;
//		&#xD;
//	}&#xD;
	&#xD;
	tw.local.idcContract.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();&#xD;
	tw.local.idcContract.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountBranchCode ="";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountClass = "Customer Account";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountCurrency ="";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountNumber = new tw.object.NameValuePair();&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountNumber.name ="";&#xD;
	tw.local.idcContract.cashCollateralAccounts[0].accountNumber.value = ""; &#xD;
	&#xD;
	&#xD;
	tw.local.parentIDCContract = new tw.object.IDCContract();&#xD;
	tw.local.parentIDCContract = tw.local.idcContract;&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8e8fc976-8cfe-45cb-ba5e-ab164ab7313e" targetRef="83b89cca-8a42-4643-a747-1ac33270f64f" name="To Get IDC Contract Facilities" id="8ea32b17-d31c-45fc-992d-2e54348e8c9c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="63acd7d4-c71d-4752-80cf-e5876026bcc9" name="is totally paid" id="a6129d38-34a3-4623-9a9e-d8e404c126d8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="871" y="79" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b4cb8b21-b895-4651-9bcd-b3ca74a232c6</ns16:incoming>
                        
                        
                        <ns16:outgoing>63acd7d4-c71d-4752-80cf-e5876026bcc9</ns16:outgoing>
                        
                        
                        <ns16:outgoing>943a0fa8-bd2e-4ade-a9af-6a5e2e825fb2</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="a6129d38-34a3-4623-9a9e-d8e404c126d8" targetRef="c2d05175-f6ad-4008-ae10-591e4ee7d02f" name="To End" id="63acd7d4-c71d-4752-80cf-e5876026bcc9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="971" y="23" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isTotalyPaid	  ==	  true</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.06eaabde-33db-480a-9d65-982fa27c2eac" name="Insert IDC Request" id="f1ee4e76-20d9-4426-a039-72de129ca5a0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1590" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a75df045-58b4-48cd-8b18-d73f73b65378</ns16:incoming>
                        
                        
                        <ns16:outgoing>fbaa7d14-34b1-42fc-bf90-a11f92481e01</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6287bfa2-c4da-4ec3-842c-a164e045a461</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.IDCRequest</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e4ea1176-3273-4117-87c0-b882c7df46c4</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">tw.local.DBID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="a6129d38-34a3-4623-9a9e-d8e404c126d8" targetRef="e363cb85-8a6a-4690-a741-9dfb5644a68c" name="To Init SQL" id="943a0fa8-bd2e-4ade-a9af-6a5e2e825fb2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isTotalyPaid	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f1ee4e76-20d9-4426-a039-72de129ca5a0" targetRef="c2d05175-f6ad-4008-ae10-591e4ee7d02f" name="To End" id="fbaa7d14-34b1-42fc-bf90-a11f92481e01">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac1</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init SQL" id="e363cb85-8a6a-4690-a741-9dfb5644a68c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="945" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>943a0fa8-bd2e-4ade-a9af-6a5e2e825fb2</ns16:incoming>
                        
                        
                        <ns16:outgoing>d3709240-2966-4eb0-b216-690f7e146630</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql = "SELECT max(ID) FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER like ? AND REQUEST_STATUS != 'Completed' AND REQUEST_STATUS != 'Terminated' AND REQUEST_STATUS != 'Canceled'";&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0]  = new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value = tw.local.requestNumber+"%";</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e363cb85-8a6a-4690-a741-9dfb5644a68c" targetRef="b493bed2-487f-4a47-abb3-d51787b640ad" name="To SQL Execute Statement (SQLResult)" id="d3709240-2966-4eb0-b216-690f7e146630">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" name="SQL Execute Statement (SQLResult)" id="b493bed2-487f-4a47-abb3-d51787b640ad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1060" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d3709240-2966-4eb0-b216-690f7e146630</ns16:incoming>
                        
                        
                        <ns16:outgoing>06eb160f-9e72-4e88-b5a4-92ece00e13b3</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="b493bed2-487f-4a47-abb3-d51787b640ad" targetRef="d08974c7-c6bd-4c5f-b01a-19033aea0dbc" name="To check has running request" id="06eb160f-9e72-4e88-b5a4-92ece00e13b3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="ed712f21-360d-4f13-b6c4-65bf051fe108" name="has running request" id="36856bd5-8a6d-4350-8553-79c8ef9bd4b6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1302" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b05c7e80-32fd-48b7-93d0-3aa671ac2cf7</ns16:incoming>
                        
                        
                        <ns16:outgoing>80521704-3860-4c36-b0eb-ee75ff33da57</ns16:outgoing>
                        
                        
                        <ns16:outgoing>ed712f21-360d-4f13-b6c4-65bf051fe108</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="36856bd5-8a6d-4350-8553-79c8ef9bd4b6" targetRef="21bf961b-7097-4d5b-b1b0-25a6ab444787" name="To Get Request Number And CBE" id="80521704-3860-4c36-b0eb-ee75ff33da57">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.haveRunningRequest	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="36856bd5-8a6d-4350-8553-79c8ef9bd4b6" targetRef="c2d05175-f6ad-4008-ae10-591e4ee7d02f" name="To End" id="ed712f21-360d-4f13-b6c4-65bf051fe108">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="1432" y="163" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="check has running request" id="d08974c7-c6bd-4c5f-b01a-19033aea0dbc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1179" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>06eb160f-9e72-4e88-b5a4-92ece00e13b3</ns16:incoming>
                        
                        
                        <ns16:outgoing>b05c7e80-32fd-48b7-93d0-3aa671ac2cf7</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.results[0].rows[0].data[0] == null) {&#xD;
	tw.local.haveRunningRequest = false;&#xD;
	tw.local.isvalid = true;&#xD;
	&#xD;
}&#xD;
else{&#xD;
	tw.local.haveRunningRequest = true;&#xD;
	tw.local.isvalid = false;&#xD;
	tw.local.massege = "this request number has Running Request";&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="d08974c7-c6bd-4c5f-b01a-19033aea0dbc" targetRef="36856bd5-8a6d-4350-8553-79c8ef9bd4b6" name="To has running request" id="b05c7e80-32fd-48b7-93d0-3aa671ac2cf7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameters" id="2056.ff8aa7a3-bc58-45dc-a662-d32c344a8f11" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.736a5ca8-5151-467e-9d77-576f7c5e0200" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.3455980e-a47c-48bb-b1cc-3073d098fa0b" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="haveRunningRequest" id="2056.0bf11c4f-4221-4ab4-8f45-da3ecef66b36" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isTotalyPaid" id="2056.07190f21-b578-4bde-a51e-36031244b366" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="668a7876-1d21-4b1f-b557-61caca4c591d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="245" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>22b076c2-6552-454c-b4bc-39a71ac38b45</ns16:incoming>
                        
                        
                        <ns16:outgoing>55b7edaf-f016-482d-aece-0a99ea7f9422</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.idcContract= new tw.object.IDCContract();&#xD;
&#xD;
//tw.local.idcContract.collateralAmount = 0;&#xD;
&#xD;
tw.local.idcContract.settlementAccounts = new tw.object.listOf.SettlementAccount();&#xD;
tw.local.idcContract.settlementAccounts[0] = new tw.object.SettlementAccount();&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = "Customer Account";&#xD;
tw.local.idcContract.settlementAccounts[0].accountNumberList = new tw.object.listOf.NameValuePair();&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
&#xD;
tw.local.idcContract.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();&#xD;
&#xD;
tw.local.idcContract.swiftMessageData = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediary = {};&#xD;
tw.local.idcContract.swiftMessageData.accountWithInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.receiverCorrespondent = {};&#xD;
tw.local.idcContract.swiftMessageData.detailsOfPayment = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.orderingInstitution = {};&#xD;
&#xD;
tw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};&#xD;
tw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};&#xD;
tw.local.idcContract.swiftMessageData.orderingCustomer = {};&#xD;
tw.local.idcContract.swiftMessageData.senderToReciever = {};&#xD;
 &#xD;
tw.local.idcContract.IDCProduct= new tw.object.DBLookup();&#xD;
//tw.local.idcContract.IDCProduct.code = "IAVC";&#xD;
//tw.local.idcContract.IDCProduct.englishdescription = "INCOMING AVALIZED BILLS UNDER COLLECTION";&#xD;
//tw.local.idcContract.userReference = "*********";&#xD;
//tw.local.idcContract.billAmount = 100;&#xD;
tw.local.idcContract.billCurrency = new tw.object.DBLookup();&#xD;
//tw.local.idcContract.billCurrency.code = "EGP";&#xD;
//tw.local.idcContract.sourceReference = "1111111111111111";&#xD;
//tw.local.idcContract.isLimitsTrackingRequired = false;&#xD;
tw.local.idcContract.interestToDate = new TWDate();&#xD;
tw.local.idcContract.transactionMaturityDate = new TWDate();&#xD;
tw.local.idcContract.transactionBaseDate = new TWDate();&#xD;
//tw.local.idcContract.tradeFinanceApprovalNumber = "12345";&#xD;
tw.local.idcContract.FCContractNumber = tw.local.IDCRequest.FCContractNumber;&#xD;
tw.local.idcContract.collateralCurrency = new tw.object.DBLookup();&#xD;
//tw.local.idcContract.collateralCurrency.code = "";&#xD;
//tw.local.idcContract.interestRate = 44.0;&#xD;
//tw.local.idcContract.transactionTransitDays = 44;&#xD;
&#xD;
tw.local.idcContract.advices = new tw.object.listOf.ContractAdvice();&#xD;
//tw.local.idcContract.advices[0] = new tw.object.ContractAdvice();&#xD;
//tw.local.idcContract.advices[0].adviceCode = "FCEWNKMC";&#xD;
//tw.local.idcContract.advices[0].suppressed = true;&#xD;
//tw.local.idcContract.advices[0].advicelines = new tw.object.SwiftMessagePart();&#xD;
//tw.local.idcContract.advices[0].advicelines.line1 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line2 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line3 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line4 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line5 = "test";&#xD;
//tw.local.idcContract.advices[0].advicelines.line6 = "test";&#xD;
&#xD;
&#xD;
tw.local.idcContract.party = new tw.object.listOf.Parties();&#xD;
tw.local.idcContract.party[0] = new tw.object.Parties();&#xD;
tw.local.idcContract.party[0].partyType = {};&#xD;
tw.local.idcContract.party[0].partyType.name = "Drawee";&#xD;
tw.local.idcContract.party[0].partyType.value = "Drawee";&#xD;
tw.local.idcContract.party[0].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
tw.local.idcContract.party[0].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
tw.local.idcContract.party[0].name = tw.local.IDCRequest.customerInformation.customerName;&#xD;
tw.local.idcContract.party[0].country = "EG";&#xD;
tw.local.idcContract.party[0].reference = "NO REF";&#xD;
tw.local.idcContract.party[0].address1 = tw.local.IDCRequest.customerInformation.addressLine1;&#xD;
tw.local.idcContract.party[0].address2 =tw.local.IDCRequest.customerInformation.addressLine2;&#xD;
tw.local.idcContract.party[0].address3 = tw.local.IDCRequest.customerInformation.addressLine3;&#xD;
tw.local.idcContract.party[0].address4 = "";&#xD;
tw.local.idcContract.party[0].media = "";&#xD;
&#xD;
tw.local.idcContract.party[0].phone = "";&#xD;
tw.local.idcContract.party[0].fax = "";&#xD;
tw.local.idcContract.party[0].email = "";&#xD;
tw.local.idcContract.party[0].contactPersonName = "";&#xD;
tw.local.idcContract.party[0].mobile = "";&#xD;
tw.local.idcContract.party[0].branch = {}&#xD;
tw.local.idcContract.party[0].branch.name = "";&#xD;
tw.local.idcContract.party[0].branch.value = "";&#xD;
tw.local.idcContract.party[0].language = "";&#xD;
&#xD;
tw.local.idcContract.party[0].isNbeCustomer = true;&#xD;
tw.local.idcContract.party[1] = new tw.object.Parties();&#xD;
tw.local.idcContract.party[1].partyType = {};&#xD;
tw.local.idcContract.party[1].partyType.name = "Drawer";&#xD;
tw.local.idcContract.party[1].partyType.value = "Drawer";&#xD;
//tw.local.idcContract.party[1].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
//tw.local.idcContract.party[1].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;&#xD;
//tw.local.idcContract.party[1].name = tw.local.IDCRequest.customerInformation.customerName;&#xD;
//tw.local.idcContract.party[1].country = "EG";&#xD;
//tw.local.idcContract.party[1].reference = "NO REF";&#xD;
//tw.local.idcContract.party[1].address1 = tw.local.IDCRequest.customerInformation.addressLine1;&#xD;
//tw.local.idcContract.party[1].address2 =tw.local.IDCRequest.customerInformation.addressLine2;&#xD;
//tw.local.idcContract.party[1].address3 = "";&#xD;
//tw.local.idcContract.party[1].address4 = "";&#xD;
//tw.local.idcContract.party[1].media = "";&#xD;
&#xD;
//tw.local.idcContract.party[1].phone = "";&#xD;
//tw.local.idcContract.party[1].fax = "";&#xD;
//tw.local.idcContract.party[1].email = "";&#xD;
//tw.local.idcContract.party[1].contactPersonName = "";&#xD;
//tw.local.idcContract.party[1].mobile = "";&#xD;
//tw.local.idcContract.party[1].branch = {}&#xD;
//tw.local.idcContract.party[1].branch.name = "";&#xD;
//tw.local.idcContract.party[1].branch.value = "";&#xD;
//tw.local.idcContract.party[1].language = "";&#xD;
//tw.local.idcContract.party[1].isNbeCustomer = true;&#xD;
&#xD;
//tw.local.idcContract.IDCRequestStage = "Finial";&#xD;
tw.local.idcContract.transactionValueDate = new TWDate();&#xD;
//tw.local.idcContract.transactionTenorDays = 5;&#xD;
&#xD;
tw.local.idcContract.interestFromDate = new TWDate();&#xD;
//tw.local.idcContract.interestAmount = 100;&#xD;
//tw.local.idcContract.haveInterest = true;&#xD;
tw.local.idcContract.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.accountNumberList[0].name = "12345";&#xD;
//tw.local.idcContract.accountNumberList[0].value = "12345";&#xD;
tw.local.idcContract.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();&#xD;
//tw.local.idcContract.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();&#xD;
//tw.local.idcContract.facilities[0].facilityCode = "MMNUFY";&#xD;
//tw.local.idcContract.facilities[0].overallLimit = 11.0;&#xD;
//tw.local.idcContract.facilities[0].limitAmount = 110;&#xD;
//tw.local.idcContract.facilities[0].effectiveLimitAmount = 11.0;&#xD;
//tw.local.idcContract.facilities[0].availableAmount = 13.0;&#xD;
//tw.local.idcContract.facilities[0].expiryDate = new TWDate();&#xD;
//tw.local.idcContract.facilities[0].availableFlag = true;&#xD;
//tw.local.idcContract.facilities[0].authorizedFlag = true;&#xD;
//tw.local.idcContract.facilities[0].Utilization = 0.0;&#xD;
//tw.local.idcContract.facilities[0].returnCode = "12345";&#xD;
//tw.local.idcContract.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineCode = "fdewa";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineAmount = 3452.0;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].availableAmount = 876.0;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].effectiveLineAmount = 234560.0;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].expiryDate = new TWDate();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.name = "001";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.value = "001";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].availableFlag = true;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].authorizedFlag = true;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityPercentageToBook = 100;&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].internalRemarks = "gwd";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].purpose = "ekwsaa";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LCCommissionPercentage = "112";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LCDef = "eell";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LCCashCover = "mcdms";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].IDCCommission = "dkmc";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].IDCAvalCommPercentage = "elm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].IDCCashCoverPercentage = "csa";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].debitAccountNumber = "dlm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineCurrency = "EGP";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].lineSerialNumber = "eele";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].returnCode = "4343";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCommission = "433";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_BidBond = "434";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_Performance = "434";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = "1234";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].name = "ewdew";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].code = "ewd";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].source = "kmk";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].status = "dkkd,";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].name = "ked";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].code = "kee";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].source = "iinkl";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].status = "eoeo";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].name = "mdmdm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].code = "ieii";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].source = "nsamls";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].status = "wodmow,";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].name = "wmd;wmq";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].code = "lkwmdw";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].source = "alwmda;lm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].status = "aswdcnk";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].name = "iuyt";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].code = "lkjh";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].source = ",mnbv";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].status = "ujmtgv";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].CIF = "12345678";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].partyType.name = "ddddmm";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].partyType.value = "ujm tg";&#xD;
//tw.local.idcContract.facilities[0].facilityLines[0].facilityID = "ertyuio";&#xD;
//tw.local.idcContract.facilities[0].status = "rururur";&#xD;
//tw.local.idcContract.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
//tw.local.idcContract.facilities[0].facilityCurrency.name = "rjrjrj";&#xD;
//tw.local.idcContract.facilities[0].facilityCurrency.value = "enene";&#xD;
//tw.local.idcContract.facilities[0].facilityID = "11111111";&#xD;
//tw.local.idcContract.limitsTrackingVIs = "DEFAULT";&#xD;
//tw.local.idcContract.interestVisibility = "DEFAULT";&#xD;
//tw.local.idcContract.adviceVis = "DEFAULT";&#xD;
//tw.local.idcContract.outstandingAmount = 100.0;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="668a7876-1d21-4b1f-b557-61caca4c591d" targetRef="1460dc3d-355d-44a6-8b93-3fb0763acfcc" name="To MW_FC Query DC Contract" id="55b7edaf-f016-482d-aece-0a99ea7f9422">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="check idc type" id="b5e33c56-0336-49a0-a352-58d4bc39d4b7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="59" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>194ecb4f-5819-4668-94d2-e221b974d994</ns16:incoming>
                        
                        
                        <ns16:outgoing>8ce516ac-97b8-4121-9aa4-da3463ee2923</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.IDCRequest.IDCRequestType.englishdescription =="IDC Completion" ) {&#xD;
&#xD;
	if (tw.local.parentIDC.IDCRequestStage =="Final") {&#xD;
		tw.local.massege = "can not make IDC Completion request for request its stage Final";&#xD;
		tw.local.isvalid = false;&#xD;
	}&#xD;
	else{&#xD;
		tw.local.isvalid = true;&#xD;
	}&#xD;
	&#xD;
}else if (tw.local.IDCRequest.IDCRequestType.englishdescription =="IDC Payment" ) {&#xD;
&#xD;
	if (tw.local.parentIDC.IDCRequestStage != "Final") {&#xD;
		tw.local.massege = "can not make IDC Payment request for request its stage initial";&#xD;
		tw.local.isvalid = false;&#xD;
	}&#xD;
	else if(tw.local.parentIDC.paymentTerms.englishdescription == "Sight"){&#xD;
		tw.local.massege = "can not make IDC Payment request for request its payment Terms Sight";&#xD;
		tw.local.isvalid = false;&#xD;
	}else if(tw.local.parentIDC.financialDetails.amtSight &gt; 0 ){&#xD;
		tw.local.massege = "can not make IDC Payment request for request its sight amount more than 0";&#xD;
		tw.local.isvalid = false;&#xD;
	}&#xD;
	else{&#xD;
		tw.local.isvalid = true;&#xD;
	}&#xD;
}&#xD;
else{&#xD;
	tw.local.isvalid = true;&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b5e33c56-0336-49a0-a352-58d4bc39d4b7" targetRef="f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0" name="To Exclusive Gateway" id="8ce516ac-97b8-4121-9aa4-da3463ee2923">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="22b076c2-6552-454c-b4bc-39a71ac38b45" name="Exclusive Gateway" id="f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="161" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8ce516ac-97b8-4121-9aa4-da3463ee2923</ns16:incoming>
                        
                        
                        <ns16:outgoing>22b076c2-6552-454c-b4bc-39a71ac38b45</ns16:outgoing>
                        
                        
                        <ns16:outgoing>08d3d573-ea3c-4f3b-b713-45fcb07bc0f5</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0" targetRef="668a7876-1d21-4b1f-b557-61caca4c591d" name="To Script Task" id="22b076c2-6552-454c-b4bc-39a71ac38b45">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0" targetRef="c2d05175-f6ad-4008-ae10-591e4ee7d02f" name="To End" id="08d3d573-ea3c-4f3b-b713-45fcb07bc0f5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="1252" y="216" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isvalid	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Catch Errors" id="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1690" y="267" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ff5d24ef-a747-483b-9309-b526fc5720fc</ns16:incoming>
                        
                        
                        <ns16:incoming>f3a9e7e6-6200-4285-a465-6ec73a529420</ns16:incoming>
                        
                        
                        <ns16:incoming>83d7717c-7f4e-45f3-9afa-d8bd30cc257c</ns16:incoming>
                        
                        
                        <ns16:incoming>d4f2c5e2-5ce5-4ad7-8e5f-df0260ca6dfa</ns16:incoming>
                        
                        
                        <ns16:incoming>1aa354ec-c58b-4e68-bf75-0a755f429f2f</ns16:incoming>
                        
                        
                        <ns16:incoming>b498b616-8aa5-4c6a-9469-61d6731f9a3d</ns16:incoming>
                        
                        
                        <ns16:incoming>9fbee0ac-15d3-4584-a7cf-0ef52e5fe901</ns16:incoming>
                        
                        
                        <ns16:incoming>8e964d0e-172a-4924-bc9b-0ee9272df726</ns16:incoming>
                        
                        
                        <ns16:incoming>351e7b05-e245-4427-b61b-e1c5a43ca3a3</ns16:incoming>
                        
                        
                        <ns16:incoming>c4af2a6b-38bf-4b1e-b32b-8de6b2045d93</ns16:incoming>
                        
                        
                        <ns16:incoming>3f074fdf-69e4-47d6-8ec3-e4c7cfa1a995</ns16:incoming>
                        
                        
                        <ns16:outgoing>6ade1896-2e98-499d-a764-59458e2c352a</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.error != undefined &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.baa7b86f-9ab2-4e31-91aa-68fe1d638c3e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.b8aad2ab-921e-4505-bbd0-aa2a6d94e28e" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b5e33c56-0336-49a0-a352-58d4bc39d4b7" parallelMultiple="false" name="Error" id="ae1216ae-bd08-45fb-aef8-bce65e2f6426">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="94" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ff5d24ef-a747-483b-9309-b526fc5720fc</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="07ab6351-264a-47c0-a5ea-c647666862b7" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="52c6f7ed-9e5f-441f-85f1-2181fc1bfd58" eventImplId="ef1e110f-6772-47d2-8bf3-ad47dcc32920">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="668a7876-1d21-4b1f-b557-61caca4c591d" parallelMultiple="false" name="Error1" id="0f5e586d-0779-4485-b42a-d58b9bbe59f8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="280" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f3a9e7e6-6200-4285-a465-6ec73a529420</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ed9458ee-5205-4c56-9801-1fe9288050ca" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="456ff78e-d003-44e1-b96e-8397f14ad851" eventImplId="72e804b8-5444-4d02-89cf-001e15608cd1">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8e8fc976-8cfe-45cb-ba5e-ab164ab7313e" parallelMultiple="false" name="Error2" id="699b9c4a-04db-420d-87b3-e8cb1c286a0d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="704" y="116" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>83d7717c-7f4e-45f3-9afa-d8bd30cc257c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="27a58723-eedf-4fd7-96c0-eff8c28ccff1" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="dab45dea-d869-4b33-97a2-c166f0b40105" eventImplId="eda24b30-bfc1-45b4-8b15-a412c4053e41">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e363cb85-8a6a-4690-a741-9dfb5644a68c" parallelMultiple="false" name="Error3" id="e212cedc-cf7c-49ca-9c81-7484a80f478c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1006" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>d4f2c5e2-5ce5-4ad7-8e5f-df0260ca6dfa</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="25bc3dc6-28b9-4ea5-92ec-e2dd62b6c6c3" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="9033a645-c427-4f10-bcb0-48860fb9203b" eventImplId="0a8c35e9-6abe-46d2-84aa-47895034dd3c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="b493bed2-487f-4a47-abb3-d51787b640ad" parallelMultiple="false" name="Error4" id="086277b6-1233-4e1f-8b7d-f4d962b10be5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1095" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1aa354ec-c58b-4e68-bf75-0a755f429f2f</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="17ff3a7c-f2d5-4718-9738-2ed44f8f46a9" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="e21c5d6c-626e-445a-b490-9ebeeaa1b4b9" eventImplId="c0a3f1f0-261d-4893-85e2-99260bbd60b0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="d08974c7-c6bd-4c5f-b01a-19033aea0dbc" parallelMultiple="false" name="Error5" id="99072e7b-f2b1-40e9-82b2-c038ce2bcc02">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1214" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b498b616-8aa5-4c6a-9469-61d6731f9a3d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="641abe04-3021-4703-973b-c2eb1f174e92" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="16041c58-26df-4b6b-a765-bd9cf8a6c0be" eventImplId="fc527377-1c49-4407-81f1-9fc30f1f4625">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="f1ee4e76-20d9-4426-a039-72de129ca5a0" parallelMultiple="false" name="Error6" id="59a87880-1c45-4052-ba87-8e54a3b76b24">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1599" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>9fbee0ac-15d3-4584-a7cf-0ef52e5fe901</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="c9dd502d-5ac0-4af4-b801-fceaec0e802a" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="80f48fb7-5d4c-4655-8d8a-bae3880f9163" eventImplId="37ace88b-a60c-4ed0-8437-fb6b4d48699e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="ae1216ae-bd08-45fb-aef8-bce65e2f6426" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="ff5d24ef-a747-483b-9309-b526fc5720fc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="0f5e586d-0779-4485-b42a-d58b9bbe59f8" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="f3a9e7e6-6200-4285-a465-6ec73a529420">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="699b9c4a-04db-420d-87b3-e8cb1c286a0d" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="83d7717c-7f4e-45f3-9afa-d8bd30cc257c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e212cedc-cf7c-49ca-9c81-7484a80f478c" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="d4f2c5e2-5ce5-4ad7-8e5f-df0260ca6dfa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="086277b6-1233-4e1f-8b7d-f4d962b10be5" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="1aa354ec-c58b-4e68-bf75-0a755f429f2f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="99072e7b-f2b1-40e9-82b2-c038ce2bcc02" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="b498b616-8aa5-4c6a-9469-61d6731f9a3d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="59a87880-1c45-4052-ba87-8e54a3b76b24" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="9fbee0ac-15d3-4584-a7cf-0ef52e5fe901">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" targetRef="9f0a234f-f85c-4b82-90d7-323282f28c0e" name="To End" id="6ade1896-2e98-499d-a764-59458e2c352a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.3875c748-15fc-40ef-bef1-ea4d905d7f75" name="MW_FC Query DC Contract" id="1460dc3d-355d-44a6-8b93-3fb0763acfcc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="369" y="58" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>55b7edaf-f016-482d-aece-0a99ea7f9422</ns16:incoming>
                        
                        
                        <ns16:outgoing>9427dfdf-0b8a-4476-9315-19c753730904</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8f786b77-ae66-4547-a444-d6ccb8969c42</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentIDC.FCContractNumber</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4ff12b5f-6b69-4504-a730-046ee5c2000a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.42effea6-8d8b-437c-958a-da5cf9119674</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.95835270-515f-4794-a207-a5e2aa301c0e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.004a9996-2e6b-4d60-821e-5db8e4ba2271</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.*************-4cbb-a781-44d233d577c6">tw.local.SCQueryBCContractRes</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ee7811d9-22b1-4727-9148-bcac74c306af</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="1460dc3d-355d-44a6-8b93-3fb0763acfcc" targetRef="a054c73c-8d25-4717-b21d-e82248d07389" name="To isSuccess?" id="9427dfdf-0b8a-4476-9315-19c753730904">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.fda30d62-86a6-461e-85ac-************" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.0ccabf04-2650-48f4-a1ca-b18f255675c7" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.*************-4cbb-a781-44d233d577c6" isCollection="false" name="SCQueryBCContractRes" id="2056.d5d0cbde-a3d5-4a67-99e3-0b43e353f916" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1460dc3d-355d-44a6-8b93-3fb0763acfcc" parallelMultiple="false" name="Error7" id="6553d6c2-8d1e-4b49-818c-8a3ee5d79a93">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="404" y="116" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8e964d0e-172a-4924-bc9b-0ee9272df726</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e0f92768-0b1d-4714-8693-1a9cae9bed20" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8893d422-4952-4bcc-aab7-0aff49921d7a" eventImplId="d5b91c3b-d4d9-45b0-89ae-47d11ba0b04d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:callActivity calledElement="1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13" name="Get Request Number And CBE" id="21bf961b-7097-4d5b-b1b0-25a6ab444787">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1425" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>80521704-3860-4c36-b0eb-ee75ff33da57</ns16:incoming>
                        
                        
                        <ns16:outgoing>a75df045-58b4-48cd-8b18-d73f73b65378</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.IDCRequest</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67">tw.local.parentIDC</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.IDCRequest.customerInformation.isCustomeSanctionedbyCBE</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.d22b4a18-865c-4b84-b879-26ee9e3f322f</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.IDCRequest.appInfo.instanceID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.b304617e-98f3-4070-b457-59e470497a2f</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fullPath</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderID</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.31e2d85c-18d8-4756-82c5-efad7879177e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="21bf961b-7097-4d5b-b1b0-25a6ab444787" targetRef="f1ee4e76-20d9-4426-a039-72de129ca5a0" name="To Insert IDC Request" id="a75df045-58b4-48cd-8b18-d73f73b65378">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c7</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6553d6c2-8d1e-4b49-818c-8a3ee5d79a93" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="8e964d0e-172a-4924-bc9b-0ee9272df726">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="21bf961b-7097-4d5b-b1b0-25a6ab444787" parallelMultiple="false" name="Error8" id="4e8a7062-5cbe-4975-850b-bc22276b37c8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1460" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>351e7b05-e245-4427-b61b-e1c5a43ca3a3</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="0636619d-2cc1-4226-8280-8b7fe62157a0" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="30b08339-3d7b-4934-87c3-dce2a94daf85" eventImplId="f105d181-50df-4a4e-87b5-96ec91b70c2d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="4e8a7062-5cbe-4975-850b-bc22276b37c8" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="351e7b05-e245-4427-b61b-e1c5a43ca3a3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.42f20f4f-289f-432d-9bd8-3837be1dc61d" name="Get IDC Contract Facilities" id="83b89cca-8a42-4643-a747-1ac33270f64f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="779" y="60" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8ea32b17-d31c-45fc-992d-2e54348e8c9c</ns16:incoming>
                        
                        
                        <ns16:outgoing>b4cb8b21-b895-4651-9bcd-b3ca74a232c6</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e36b1711-d92f-4f79-8134-45f6ebe54fe7</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ee29ebe-48c3-4dc5-8fda-5433c9d3151d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.*************-4cbb-a781-44d233d577c6">tw.local.SCQueryBCContractRes</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.10751aed-d9b5-4b76-804f-3a33b2a7eb76</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1">tw.local.idcContract</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="83b89cca-8a42-4643-a747-1ac33270f64f" targetRef="a6129d38-34a3-4623-9a9e-d8e404c126d8" name="To is totally paid" id="b4cb8b21-b895-4651-9bcd-b3ca74a232c6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:7c3a</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="fdaa298b-f679-42de-a255-0af145d71dc0" targetRef="b5e33c56-0336-49a0-a352-58d4bc39d4b7" name="To check idc type" id="194ecb4f-5819-4668-94d2-e221b974d994">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="83b89cca-8a42-4643-a747-1ac33270f64f" parallelMultiple="false" name="Error9" id="f0d946e2-e9b4-4ea8-97f4-478c1d077ee4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="814" y="118" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c4af2a6b-38bf-4b1e-b32b-8de6b2045d93</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="9ced3e41-258a-483f-9995-7b256ae6a65e" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="05c95ef5-5538-47a6-aa04-00ede8ddf598" eventImplId="ef9add1b-96a4-41cd-83bb-65f53dc41c51">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f0d946e2-e9b4-4ea8-97f4-478c1d077ee4" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="To Catch Errors" id="c4af2a6b-38bf-4b1e-b32b-8de6b2045d93">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="ba83e2db-be94-49cc-9b97-4f9ff3e479ee" name="isSuccess?" id="a054c73c-8d25-4717-b21d-e82248d07389">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="511" y="77" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9427dfdf-0b8a-4476-9315-19c753730904</ns16:incoming>
                        
                        
                        <ns16:outgoing>ba83e2db-be94-49cc-9b97-4f9ff3e479ee</ns16:outgoing>
                        
                        
                        <ns16:outgoing>3f074fdf-69e4-47d6-8ec3-e4c7cfa1a995</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="a054c73c-8d25-4717-b21d-e82248d07389" targetRef="8e8fc976-8cfe-45cb-ba5e-ab164ab7313e" name="Yes" id="ba83e2db-be94-49cc-9b97-4f9ff3e479ee">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="a054c73c-8d25-4717-b21d-e82248d07389" targetRef="dd0c11e8-8975-4abd-9d0a-a7d03a5534b5" name="No" id="3f074fdf-69e4-47d6-8ec3-e4c7cfa1a995">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="9f0a234f-f85c-4b82-90d7-323282f28c0e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1858" y="221" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6ade1896-2e98-499d-a764-59458e2c352a</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="dd839a48-10ff-4cdd-b868-fe7d4b73835d" eventImplId="c553fdc0-a824-4d92-84fb-b3d31cc2bc60">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To isSuccess?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.05331bbb-5dd1-4f07-8757-5d1ef080c629</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc</fromProcessItemId>
            <endStateId>guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754</endStateId>
            <toProcessItemId>2025.a054c73c-8d25-4717-b21d-e82248d07389</toProcessItemId>
            <guid>3f014205-1fe5-484c-9d75-878232df77bb</guid>
            <versionId>0d06c039-b197-421e-8a0e-665060f2c3f2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc</fromProcessItemId>
            <toProcessItemId>2025.a054c73c-8d25-4717-b21d-e82248d07389</toProcessItemId>
        </link>
        <link name="To Get IDC Contract Facilities">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7914af6f-eefd-408a-8b28-3dcbf187b684</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.83b89cca-8a42-4643-a747-1ac33270f64f</toProcessItemId>
            <guid>954b4857-9e2e-4240-8991-41361ba80bcd</guid>
            <versionId>2de9ca47-9199-4950-bdff-d54aad822799</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e</fromProcessItemId>
            <toProcessItemId>2025.83b89cca-8a42-4643-a747-1ac33270f64f</toProcessItemId>
        </link>
        <link name="To Exclusive Gateway">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b42a8a97-0fbc-4e9e-b3aa-fa5fa94964a5</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</toProcessItemId>
            <guid>b0b0bc70-c8a0-4300-9af3-47b67105b4ce</guid>
            <versionId>36eb5f90-e726-4682-a0af-51fd4f4ea92f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7</fromProcessItemId>
            <toProcessItemId>2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</toProcessItemId>
        </link>
        <link name="To Insert IDC Request">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.856de0e2-987a-4b3e-ba5b-3172df5ff33e</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.21bf961b-7097-4d5b-b1b0-25a6ab444787</fromProcessItemId>
            <endStateId>guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c7</endStateId>
            <toProcessItemId>2025.f1ee4e76-20d9-4426-a039-72de129ca5a0</toProcessItemId>
            <guid>339c77a5-6c7d-41f2-8a1a-df2dbc8ed8d1</guid>
            <versionId>3ad0d2a3-99de-4097-bc14-f9d70c00dd26</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.21bf961b-7097-4d5b-b1b0-25a6ab444787</fromProcessItemId>
            <toProcessItemId>2025.f1ee4e76-20d9-4426-a039-72de129ca5a0</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b7d497d1-7083-442e-9a0e-0048b81fd316</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f1ee4e76-20d9-4426-a039-72de129ca5a0</fromProcessItemId>
            <endStateId>guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac1</endStateId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
            <guid>51935698-444c-4608-8c17-166f7cb7f0e1</guid>
            <versionId>4257034c-5b06-4999-aea8-2fee9bfcb47e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f1ee4e76-20d9-4426-a039-72de129ca5a0</fromProcessItemId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.cbcc71bc-ad42-4696-a1e4-1b0d15f17f2c</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a6129d38-34a3-4623-9a9e-d8e404c126d8</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
            <guid>89d65f15-1a0e-46ab-b0f5-50b0718acbbe</guid>
            <versionId>57375eae-5f0a-482b-8a61-4b2ccf73c0e0</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="971" y="23" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.a6129d38-34a3-4623-9a9e-d8e404c126d8</fromProcessItemId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1de82572-a47e-49a0-ae09-87293b8ab224</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9f0a234f-f85c-4b82-90d7-323282f28c0e</toProcessItemId>
            <guid>1ed3b8e1-09a8-454e-ae58-526acd8e8894</guid>
            <versionId>590c1751-aa6d-40a0-acef-bfd2f37285b1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</fromProcessItemId>
            <toProcessItemId>2025.9f0a234f-f85c-4b82-90d7-323282f28c0e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5f92f1eb-0911-45a3-a710-db8036768738</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</fromProcessItemId>
            <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709e</endStateId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
            <guid>eca200b9-f093-455c-a514-f2d8337bb92f</guid>
            <versionId>5ad0e383-dd49-4ebd-af6b-c4b316a9e113</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="1252" y="216" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</fromProcessItemId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
        </link>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7133d5ec-1b05-4fef-9bc9-1d234aebedaf</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.668a7876-1d21-4b1f-b557-61caca4c591d</toProcessItemId>
            <guid>59f7c263-4614-449e-9a89-e9bc8bafb001</guid>
            <versionId>7e213ffc-8b4e-4bcc-9be0-5392b1acea1f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0</fromProcessItemId>
            <toProcessItemId>2025.668a7876-1d21-4b1f-b557-61caca4c591d</toProcessItemId>
        </link>
        <link name="To MW_FC Query DC Contract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fce978f8-1812-4233-b4f7-1a57738f9411</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.668a7876-1d21-4b1f-b557-61caca4c591d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc</toProcessItemId>
            <guid>ec8980f6-1d23-4a85-91e0-e5c389693be2</guid>
            <versionId>806f2dd5-842b-4130-9e71-56614aa1adfd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.668a7876-1d21-4b1f-b557-61caca4c591d</fromProcessItemId>
            <toProcessItemId>2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0e508126-287c-4818-a4d5-9ed719aff795</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a054c73c-8d25-4717-b21d-e82248d07389</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e</toProcessItemId>
            <guid>*************-41a3-ab22-897784d01617</guid>
            <versionId>87a13b2c-f4b9-4584-8d8c-6e51ec405750</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a054c73c-8d25-4717-b21d-e82248d07389</fromProcessItemId>
            <toProcessItemId>2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e</toProcessItemId>
        </link>
        <link name="To Init SQL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.579663fd-3375-4b37-b504-6a9b1c6b4a0d</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a6129d38-34a3-4623-9a9e-d8e404c126d8</fromProcessItemId>
            <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-70a0</endStateId>
            <toProcessItemId>2025.e363cb85-8a6a-4690-a741-9dfb5644a68c</toProcessItemId>
            <guid>ae069443-4028-417b-aa8a-e746e317bd33</guid>
            <versionId>8fd2aa5b-8db8-4b8a-ac11-ff526027055c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a6129d38-34a3-4623-9a9e-d8e404c126d8</fromProcessItemId>
            <toProcessItemId>2025.e363cb85-8a6a-4690-a741-9dfb5644a68c</toProcessItemId>
        </link>
        <link name="To is totally paid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e5b594bb-1c9d-43af-869b-a5c91f34841a</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.83b89cca-8a42-4643-a747-1ac33270f64f</fromProcessItemId>
            <endStateId>guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:7c3a</endStateId>
            <toProcessItemId>2025.a6129d38-34a3-4623-9a9e-d8e404c126d8</toProcessItemId>
            <guid>537f8c65-d814-408a-b162-422c67747fd6</guid>
            <versionId>91a43ad9-1083-47fa-8ce4-10a8377c6a61</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.83b89cca-8a42-4643-a747-1ac33270f64f</fromProcessItemId>
            <toProcessItemId>2025.a6129d38-34a3-4623-9a9e-d8e404c126d8</toProcessItemId>
        </link>
        <link name="To has running request">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.275c1f3e-9dfd-4b82-9fe4-bb0bd920b35c</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6</toProcessItemId>
            <guid>ae22bd8d-4ea7-476d-8268-a605a891ee96</guid>
            <versionId>a7c64e21-4af0-45bf-86e3-80b1f4fc967d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc</fromProcessItemId>
            <toProcessItemId>2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6</toProcessItemId>
        </link>
        <link name="To Get Request Number And CBE">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.71d3970a-26ec-4593-9335-1668563981c9</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6</fromProcessItemId>
            <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709f</endStateId>
            <toProcessItemId>2025.21bf961b-7097-4d5b-b1b0-25a6ab444787</toProcessItemId>
            <guid>60a9cf11-347f-4210-a065-ae4438331b7a</guid>
            <versionId>b7f9c566-4896-4a82-ae0a-02c0ed56cde3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6</fromProcessItemId>
            <toProcessItemId>2025.21bf961b-7097-4d5b-b1b0-25a6ab444787</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.677b0d10-7d5a-47fe-9d7e-d1a82ee93bb5</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a054c73c-8d25-4717-b21d-e82248d07389</fromProcessItemId>
            <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709d</endStateId>
            <toProcessItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</toProcessItemId>
            <guid>8813801b-390f-4554-98c8-4c23b783279a</guid>
            <versionId>c656d09e-0b00-4f48-ac6c-c600485901ba</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a054c73c-8d25-4717-b21d-e82248d07389</fromProcessItemId>
            <toProcessItemId>2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5</toProcessItemId>
        </link>
        <link name="To SQL Execute Statement (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.58fe3ee2-f994-474d-b7fa-d4d109f8f618</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e363cb85-8a6a-4690-a741-9dfb5644a68c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b493bed2-487f-4a47-abb3-d51787b640ad</toProcessItemId>
            <guid>85d4020a-eeb3-433d-999e-b2a163eb69b5</guid>
            <versionId>cd8c92c0-ec47-422b-9815-21a97b3bdf17</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e363cb85-8a6a-4690-a741-9dfb5644a68c</fromProcessItemId>
            <toProcessItemId>2025.b493bed2-487f-4a47-abb3-d51787b640ad</toProcessItemId>
        </link>
        <link name="To check has running request">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0988eeb5-ca4e-483f-9ca9-db7465042886</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b493bed2-487f-4a47-abb3-d51787b640ad</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc</toProcessItemId>
            <guid>38dcdabf-978a-4c28-962f-0028a71423be</guid>
            <versionId>e6a17a52-3c0f-4134-b6c0-ba90954a40f1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b493bed2-487f-4a47-abb3-d51787b640ad</fromProcessItemId>
            <toProcessItemId>2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ae512094-37f9-40cd-b0af-aad4c8973892</processLinkId>
            <processId>1.545f4988-f748-4797-90c9-d2c7c8a98082</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
            <guid>ae879bdf-734e-48fa-b8b6-192d53657bd6</guid>
            <versionId>efb56890-001f-4108-97ef-ac1a4f2ac88e</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="1432" y="163" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6</fromProcessItemId>
            <toProcessItemId>2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f</toProcessItemId>
        </link>
    </process>
</teamworks>

