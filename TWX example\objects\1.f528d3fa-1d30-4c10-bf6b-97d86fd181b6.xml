<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6" name="UT TF">
        <lastModified>1692206519546</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.2ead4e93-5a6d-425e-8ca7-f130d20b2ec9</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:25fd907f15501a6a:510e674:189fee9ec23:-440e</guid>
        <versionId>*************-4d6b-a6c7-e9ec794dddf0</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:25fd907f15501a6a:510e674:189fee9ec23:-391f" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.758ac9e5-4feb-40c2-874e-d61ad2673555"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"fc512d8b-2fc9-4044-8e46-4a3b1bc183f4"},{"incoming":["533102db-d593-4ea9-8727-9323283505d5"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:25fd907f15501a6a:510e674:189fee9ec23:-440c"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"89dba6b8-c23b-4ca0-8c49-232f5b5f588a"},{"targetRef":"2ead4e93-5a6d-425e-8ca7-f130d20b2ec9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.758ac9e5-4feb-40c2-874e-d61ad2673555","sourceRef":"fc512d8b-2fc9-4044-8e46-4a3b1bc183f4"},{"startQuantity":1,"outgoing":["533102db-d593-4ea9-8727-9323283505d5"],"incoming":["2027.758ac9e5-4feb-40c2-874e-d61ad2673555"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":199,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2ead4e93-5a6d-425e-8ca7-f130d20b2ec9","scriptFormat":"text\/x-javascript","script":{"content":["\/\/tw.local.filteredTeam \t\t\t= new tw.object.Team();\r\n\/\/tw.local.filteredTeam.name \t\t= tw.local.originalTeam.name;\r\n\/\/tw.local.filteredTeam.managerTeam \t= tw.local.originalTeam.managerTeam;\r\n\/\/\r\n\/\/tw.local.filteredTeam.members = new tw.object.listOf.String();\r\n\/\/tw.local.filteredTeam.members[0] = tw.local.initiator;\r\n\/\/\/\/for( var i=0; i&lt;tw.local.originalTeam.members.listLength; i++) \r\n\/\/\/\/{\r\n\/\/\/\/\tif( tw.local.originalTeam.members[i] == tw.local.initiator )\r\n\/\/\/\/          tw.local.filteredTeam.members[tw.local.filteredTeam.members.listLength]  = tw.local.originalTeam.members[i];\r\n\/\/\/\/}"]}},{"targetRef":"89dba6b8-c23b-4ca0-8c49-232f5b5f588a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"533102db-d593-4ea9-8727-9323283505d5","sourceRef":"2ead4e93-5a6d-425e-8ca7-f130d20b2ec9"}],"laneSet":[{"id":"7468e066-232c-43d4-85bf-7f85e6e8bea0","lane":[{"flowNodeRef":["fc512d8b-2fc9-4044-8e46-4a3b1bc183f4","89dba6b8-c23b-4ca0-8c49-232f5b5f588a","2ead4e93-5a6d-425e-8ca7-f130d20b2ec9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":203}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"2a3aecb2-c45b-4811-880b-3e4cf1fe77fb","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"UT TF","declaredType":"process","id":"1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"filteredTeam","isCollection":false,"id":"2055.e2248c8b-32f2-4c9d-808e-0f003e630ab8","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}],"inputSet":[{"dataInputRefs":["2055.3c1a50cb-2e12-4f14-89dd-d7f59e506d67","2055.a76b6fae-3a5c-469c-85df-3d71dc308f87"]}],"outputSet":[{}],"otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnlyOutputs":"true"},"dataInput":[{"itemSubjectRef":"itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0","name":"originalTeam","isCollection":false,"id":"2055.3c1a50cb-2e12-4f14-89dd-d7f59e506d67","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"initiator","isCollection":false,"id":"2055.a76b6fae-3a5c-469c-85df-3d71dc308f87"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="originalTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3c1a50cb-2e12-4f14-89dd-d7f59e506d67</processParameterId>
            <processId>1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>699d0b14-c446-45d4-806c-14c83d5e983e</guid>
            <versionId>39168f1e-0433-4fb2-bcf4-09fb8f3a3b38</versionId>
        </processParameter>
        <processParameter name="initiator">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a76b6fae-3a5c-469c-85df-3d71dc308f87</processParameterId>
            <processId>1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f4a83711-d983-4976-b1d1-e78d963643f6</guid>
            <versionId>45ae443b-91c4-491a-9662-7e3808636942</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e2248c8b-32f2-4c9d-808e-0f003e630ab8</processParameterId>
            <processId>1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f40ec63b-b865-46f6-aba4-96e9ba36676f</guid>
            <versionId>22b65634-bd62-4d31-8aab-430b3e39cb42</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.89dba6b8-c23b-4ca0-8c49-232f5b5f588a</processItemId>
            <processId>1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ebdea2b9-9219-49fc-bcf5-c98d47e2ca4f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:25fd907f15501a6a:510e674:189fee9ec23:-440c</guid>
            <versionId>15cb5007-2050-4f83-a836-6ac8373d9760</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ebdea2b9-9219-49fc-bcf5-c98d47e2ca4f</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>250ab6c6-4be7-4819-aafb-c93e96fd3c8c</guid>
                <versionId>e647162d-7737-4564-8f69-eab6f75252fe</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2ead4e93-5a6d-425e-8ca7-f130d20b2ec9</processItemId>
            <processId>1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ab3fa30f-c4c0-4165-86df-0f2be7d5f0d7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:25fd907f15501a6a:510e674:189fee9ec23:-3a9c</guid>
            <versionId>a9631d62-7618-4037-9f96-16a67c61c0ca</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="199" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ab3fa30f-c4c0-4165-86df-0f2be7d5f0d7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//tw.local.filteredTeam 			= new tw.object.Team();&#xD;
//tw.local.filteredTeam.name 		= tw.local.originalTeam.name;&#xD;
//tw.local.filteredTeam.managerTeam 	= tw.local.originalTeam.managerTeam;&#xD;
//&#xD;
//tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
//tw.local.filteredTeam.members[0] = tw.local.initiator;&#xD;
////for( var i=0; i&lt;tw.local.originalTeam.members.listLength; i++) &#xD;
////{&#xD;
////	if( tw.local.originalTeam.members[i] == tw.local.initiator )&#xD;
////          tw.local.filteredTeam.members[tw.local.filteredTeam.members.listLength]  = tw.local.originalTeam.members[i];&#xD;
////}</script>
                <isRule>false</isRule>
                <guid>a6ad3827-19e6-4202-a622-4fac22c38290</guid>
                <versionId>8222de0d-d393-42c7-a80a-d35242067b44</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.2ead4e93-5a6d-425e-8ca7-f130d20b2ec9</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="UT TF" id="1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:dataInput name="originalTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.3c1a50cb-2e12-4f14-89dd-d7f59e506d67" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="initiator" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a76b6fae-3a5c-469c-85df-3d71dc308f87" />
                        
                        
                        <ns16:dataOutput name="filteredTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.e2248c8b-32f2-4c9d-808e-0f003e630ab8" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.3c1a50cb-2e12-4f14-89dd-d7f59e506d67</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.a76b6fae-3a5c-469c-85df-3d71dc308f87</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="7468e066-232c-43d4-85bf-7f85e6e8bea0">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="2a3aecb2-c45b-4811-880b-3e4cf1fe77fb" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="203" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>fc512d8b-2fc9-4044-8e46-4a3b1bc183f4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>89dba6b8-c23b-4ca0-8c49-232f5b5f588a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2ead4e93-5a6d-425e-8ca7-f130d20b2ec9</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="fc512d8b-2fc9-4044-8e46-4a3b1bc183f4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.758ac9e5-4feb-40c2-874e-d61ad2673555</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="89dba6b8-c23b-4ca0-8c49-232f5b5f588a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:25fd907f15501a6a:510e674:189fee9ec23:-440c</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>533102db-d593-4ea9-8727-9323283505d5</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="fc512d8b-2fc9-4044-8e46-4a3b1bc183f4" targetRef="2ead4e93-5a6d-425e-8ca7-f130d20b2ec9" name="To Script Task" id="2027.758ac9e5-4feb-40c2-874e-d61ad2673555">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="2ead4e93-5a6d-425e-8ca7-f130d20b2ec9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="199" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.758ac9e5-4feb-40c2-874e-d61ad2673555</ns16:incoming>
                        
                        
                        <ns16:outgoing>533102db-d593-4ea9-8727-9323283505d5</ns16:outgoing>
                        
                        
                        <ns16:script>//tw.local.filteredTeam 			= new tw.object.Team();&#xD;
//tw.local.filteredTeam.name 		= tw.local.originalTeam.name;&#xD;
//tw.local.filteredTeam.managerTeam 	= tw.local.originalTeam.managerTeam;&#xD;
//&#xD;
//tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
//tw.local.filteredTeam.members[0] = tw.local.initiator;&#xD;
////for( var i=0; i&lt;tw.local.originalTeam.members.listLength; i++) &#xD;
////{&#xD;
////	if( tw.local.originalTeam.members[i] == tw.local.initiator )&#xD;
////          tw.local.filteredTeam.members[tw.local.filteredTeam.members.listLength]  = tw.local.originalTeam.members[i];&#xD;
////}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2ead4e93-5a6d-425e-8ca7-f130d20b2ec9" targetRef="89dba6b8-c23b-4ca0-8c49-232f5b5f588a" name="To End" id="533102db-d593-4ea9-8727-9323283505d5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.533102db-d593-4ea9-8727-9323283505d5</processLinkId>
            <processId>1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2ead4e93-5a6d-425e-8ca7-f130d20b2ec9</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.89dba6b8-c23b-4ca0-8c49-232f5b5f588a</toProcessItemId>
            <guid>5d0b9e47-b416-497e-a73d-0b378f74df5a</guid>
            <versionId>d2f6b07f-a5a3-4fda-9743-c148968a39e5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2ead4e93-5a6d-425e-8ca7-f130d20b2ec9</fromProcessItemId>
            <toProcessItemId>2025.89dba6b8-c23b-4ca0-8c49-232f5b5f588a</toProcessItemId>
        </link>
    </process>
</teamworks>

