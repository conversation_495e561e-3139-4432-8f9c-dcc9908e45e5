{"id": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "versionId": "f8eed9d0-81b2-4e47-8fa4-d6d5eebd1940", "name": "Validate Required Documents 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "name": "Validate Required Documents 2", "lastModified": "1699443521757", "lastModifiedBy": "so<PERSON>ia", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.b73973ae-cf61-48f7-a7d7-58462330ed5d", "2025.b73973ae-cf61-48f7-a7d7-58462330ed5d"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "f5f35ded-a8f3-447f-b553-5ed91276b059", "versionId": "f8eed9d0-81b2-4e47-8fa4-d6d5eebd1940", "dependencySummary": "<dependencySummary id=\"bpdid:651a1a6abf396537:64776e00:18baeba64af:-4076\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.fbd093a1-004b-4316-870b-898610f44822\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"a6e87649-2ef6-44c9-a228-bf00528a2b8d\"},{\"incoming\":[\"e75637ab-2efc-4947-a2bf-2a4e34c8dd4f\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d2\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"1b5a020e-5857-4a63-8df4-727ea9014b25\"},{\"targetRef\":\"b73973ae-cf61-48f7-a7d7-58462330ed5d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Documents Table\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fbd093a1-004b-4316-870b-898610f44822\",\"sourceRef\":\"a6e87649-2ef6-44c9-a228-bf00528a2b8d\"},{\"startQuantity\":1,\"outgoing\":[\"3247959b-80c1-4c86-8ef6-0073e8dbcb6e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":129,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"ruleSet\":[{\"rules\":[{\"name\":\"rule 1\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TRule\",\"ruleId\":\"7dabda14-1430-445d-850f-0b1907e63ded\",\"type\":\"DECISION_TABLE\",\"locale\":\"en\",\"decisionTableHash\":\"KdbHXPfaz2BRnCbX42wK1xPSEGQTyI7I8PpVfcdI9AE=\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TRuleSet\",\"locale\":\"en\"}]},\"implementation\":\"##unspecified\",\"name\":\"Documents Table\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"businessRuleTask\",\"id\":\"20354183-1ad8-4916-8709-13af38dc69ae\"},{\"targetRef\":\"b73973ae-cf61-48f7-a7d7-58462330ed5d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Set Documents List\",\"declaredType\":\"sequenceFlow\",\"id\":\"3247959b-80c1-4c86-8ef6-0073e8dbcb6e\",\"sourceRef\":\"20354183-1ad8-4916-8709-13af38dc69ae\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"result\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.3a40c528-feef-4005-919f-0a729c2cfb56\"},{\"startQuantity\":1,\"outgoing\":[\"e75637ab-2efc-4947-a2bf-2a4e34c8dd4f\"],\"incoming\":[\"3247959b-80c1-4c86-8ef6-0073e8dbcb6e\",\"2027.fbd093a1-004b-4316-870b-898610f44822\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":280,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Documents List\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"b73973ae-cf61-48f7-a7d7-58462330ed5d\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.requiredDocuments = new tw.object.listOf.String();\\r\\nif (tw.local.result.length > 0) {\\r\\n\\ttw.local.requiredDocuments = tw.local.result.split(\\\",\\\")\\r\\n}\"]}},{\"targetRef\":\"1b5a020e-5857-4a63-8df4-727ea9014b25\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e75637ab-2efc-4947-a2bf-2a4e34c8dd4f\",\"sourceRef\":\"b73973ae-cf61-48f7-a7d7-58462330ed5d\"}],\"laneSet\":[{\"id\":\"e96dd6c0-c048-476e-a8ec-153dee277950\",\"lane\":[{\"flowNodeRef\":[\"a6e87649-2ef6-44c9-a228-bf00528a2b8d\",\"1b5a020e-5857-4a63-8df4-727ea9014b25\",\"20354183-1ad8-4916-8709-13af38dc69ae\",\"b73973ae-cf61-48f7-a7d7-58462330ed5d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"5d75c2da-0aca-4142-844a-9742baa46b85\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Validate Required Documents 2\",\"declaredType\":\"process\",\"id\":\"1.46ffdf85-029f-4715-857e-d78bf56f8839\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requiredDocuments\",\"isCollection\":true,\"id\":\"2055.26839f0b-31e7-4eea-8bdf-1df39400f19e\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.9507669b-8a1e-4e66-93da-f6ab79643307\",\"2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.26839f0b-31e7-4eea-8bdf-1df39400f19e\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"Advance Payment\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestType\",\"isCollection\":false,\"id\":\"2055.9507669b-8a1e-4e66-93da-f6ab79643307\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"Correspondent\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"documentSource\",\"isCollection\":false,\"id\":\"2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.9507669b-8a1e-4e66-93da-f6ab79643307", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "\"Advance Payment\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "c814dfdb-2a68-4bdd-8b91-fba43fbf4dac", "versionId": "30367342-18c9-4217-a2de-da657f461795"}, {"name": "documentSource", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"Correspondent\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "7e8688ca-2ae4-44ce-9805-9ddde95d857c", "versionId": "887ae2c6-4211-4cfd-9e34-537b79a710dc"}, {"name": "requiredDocuments", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.26839f0b-31e7-4eea-8bdf-1df39400f19e", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "parameterType": "2", "isArrayOf": "true", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "0978950b-78ac-490b-8a8c-54e481ebd02e", "versionId": "ee984397-4b41-4f71-83b1-cd2467bda1fa"}], "processVariable": {"name": "result", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3a40c528-feef-4005-919f-0a729c2cfb56", "description": {"isNull": "true"}, "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "true", "defaultValue": "\"\"", "guid": "5799ba59-e581-4b5b-b6b4-57ee570647cf", "versionId": "4d416314-5f34-4387-803e-edc9ac174fb6"}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1b5a020e-5857-4a63-8df4-727ea9014b25", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.599539cc-4c7c-48c1-be0f-e2ec3e710fe1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d2", "versionId": "0ee236d5-0a9b-40b3-ae4b-cf836b8364f3", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.599539cc-4c7c-48c1-be0f-e2ec3e710fe1", "haltProcess": "false", "guid": "079011e0-3dc1-411f-8bd9-2fee24f74059", "versionId": "e680dac2-be51-4b52-99fa-12ed3894e658"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.20354183-1ad8-4916-8709-13af38dc69ae", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "name": "Documents Table", "tWComponentName": "ILOGDecision", "tWComponentId": "3026.90ceac86-32ff-4bff-8a0b-a6ee2b5c2453", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d1", "versionId": "a3fce480-8743-4799-b83f-b24372e31143", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "129", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "iLogDecisionId": "3026.90ceac86-32ff-4bff-8a0b-a6ee2b5c2453", "definition": "<iLogDecision>\r\n  <rule>\r\n    <name>rule 1</name>\r\n    <ruleId>7dabda14-1430-445d-850f-0b1907e63ded</ruleId>\r\n    <DT xmlns=\"http://schemas.ilog.com/Rules/7.0/DecisionTable\" Version=\"7.0\">\r\n      <Body>\r\n        <Properties>\r\n          <Property Name=\"UI.MediaType\"><![CDATA[Web]]></Property>\r\n        </Properties>\r\n        <Structure>\r\n          <ConditionDefinitions>\r\n            <ConditionDefinition Id=\"C0\">\r\n              <ExpressionDefinition>\r\n                <Text><![CDATA[requestType contains <a string>]]></Text>\r\n              </ExpressionDefinition>\r\n            </ConditionDefinition>\r\n            <ConditionDefinition Id=\"C2\">\r\n              <ExpressionDefinition>\r\n                <Text><![CDATA[documentSource is one of <strings>]]></Text>\r\n              </ExpressionDefinition>\r\n            </ConditionDefinition>\r\n          </ConditionDefinitions>\r\n          <ActionDefinitions>\r\n            <ActionDefinition Id=\"A0\">\r\n              <ExpressionDefinition>\r\n                <Text><![CDATA[set result to <a string>]]></Text>\r\n              </ExpressionDefinition>\r\n            </ActionDefinition>\r\n          </ActionDefinitions>\r\n        </Structure>\r\n        <Contents>\r\n          <Partition DefId=\"C0\">\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"Advance Payment\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Customer Request,Invoice\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"ICAP\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Customer Request,Invoice,Transport document\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"IDC Acknowledgement\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Correspondent cover letter,Invoice,Transport document\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"IDC Execution\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Correspondent\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Customer Request,Correspondent cover letter,Invoice,Transport document\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"IDC Execution Completion\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Correspondent\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Customer Request,Correspondent cover letter,Invoice,Transport document\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"IDC Execution\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Exporter\", \"Importer\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Customer Request,Invoice,Transport document\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"IDC Completion\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Exporter\", \"Importer\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Customer Request,Invoice,Transport document\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n            <Condition>\r\n              <Expression>\r\n                <Param><![CDATA[\"IDC Payment\"]]></Param>\r\n              </Expression>\r\n              <Partition DefId=\"C2\">\r\n                <Condition>\r\n                  <Expression>\r\n                    <Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param>\r\n                  </Expression>\r\n                  <ActionSet>\r\n                    <Action DefId=\"A0\">\r\n                      <Expression>\r\n                        <Param><![CDATA[\"Customer Request\"]]></Param>\r\n                      </Expression>\r\n                    </Action>\r\n                  </ActionSet>\r\n                </Condition>\r\n              </Partition>\r\n            </Condition>\r\n          </Partition>\r\n        </Contents>\r\n      </Body>\r\n      <Resources DefaultLocale=\"en_US\">\r\n        <ResourceSet Locale=\"en\">\r\n          <Data Name=\"Definitions(C0)#HeaderText\"><![CDATA[Request Type]]></Data>\r\n          <Data Name=\"Definitions(A0)#Width\"><![CDATA[80]]></Data>\r\n          <Data Name=\"Definitions(C2)#HeaderText\"><![CDATA[Document Source]]></Data>\r\n          <Data Name=\"Definitions(C0)#Width\"><![CDATA[80]]></Data>\r\n          <Data Name=\"Definitions(C2)#Width\"><![CDATA[227]]></Data>\r\n        </ResourceSet>\r\n      </Resources>\r\n    </DT>\r\n    <locale>en</locale>\r\n    <type>DECISION_TABLE</type>\r\n  </rule>\r\n  <locale>en</locale>\r\n</iLogDecision>", "guid": "5406c40b-7338-44f4-b676-5e3d3a3ab7d9", "versionId": "f798b150-f60c-472b-bf3f-939a3cee11cb"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b73973ae-cf61-48f7-a7d7-58462330ed5d", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "name": "Set Documents List", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.e8dd8c25-6327-42d8-b9d0-cf2774b499bd", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d3", "versionId": "dfa33204-196e-4ea2-9c5b-37acd160892d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "280", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.e8dd8c25-6327-42d8-b9d0-cf2774b499bd", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.requiredDocuments = new tw.object.listOf.String();\r\r\nif (tw.local.result.length > 0) {\r\r\n\ttw.local.requiredDocuments = tw.local.result.split(\",\")\r\r\n}", "isRule": "false", "guid": "bf2f6feb-6715-49d9-8370-020208c79208", "versionId": "3e88b3f9-3b1f-4936-9d61-b3110de6824a"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Validate Required Documents 2", "id": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "requestType", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.9507669b-8a1e-4e66-93da-f6ab79643307", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"Advance Payment\"", "useDefault": "true"}}}, {"name": "documentSource", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"Correspondent\"", "useDefault": "true"}}}], "ns16:dataOutput": {"name": "requiredDocuments", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "id": "2055.26839f0b-31e7-4eea-8bdf-1df39400f19e"}, "ns16:inputSet": {"ns16:dataInputRefs": ["2055.9507669b-8a1e-4e66-93da-f6ab79643307", "2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c"]}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.26839f0b-31e7-4eea-8bdf-1df39400f19e"}}, "ns16:laneSet": {"id": "e96dd6c0-c048-476e-a8ec-153dee277950", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "5d75c2da-0aca-4142-844a-9742baa46b85", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["a6e87649-2ef6-44c9-a228-bf00528a2b8d", "1b5a020e-5857-4a63-8df4-727ea9014b25", "20354183-1ad8-4916-8709-13af38dc69ae", "b73973ae-cf61-48f7-a7d7-58462330ed5d"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "a6e87649-2ef6-44c9-a228-bf00528a2b8d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.fbd093a1-004b-4316-870b-898610f44822"}, "ns16:endEvent": {"name": "End", "id": "1b5a020e-5857-4a63-8df4-727ea9014b25", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d2"}, "ns16:incoming": "e75637ab-2efc-4947-a2bf-2a4e34c8dd4f"}, "ns16:sequenceFlow": [{"sourceRef": "a6e87649-2ef6-44c9-a228-bf00528a2b8d", "targetRef": "b73973ae-cf61-48f7-a7d7-58462330ed5d", "name": "To Documents Table", "id": "2027.fbd093a1-004b-4316-870b-898610f44822", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "20354183-1ad8-4916-8709-13af38dc69ae", "targetRef": "b73973ae-cf61-48f7-a7d7-58462330ed5d", "name": "To Set Documents List", "id": "3247959b-80c1-4c86-8ef6-0073e8dbcb6e", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "b73973ae-cf61-48f7-a7d7-58462330ed5d", "targetRef": "1b5a020e-5857-4a63-8df4-727ea9014b25", "name": "To End", "id": "e75637ab-2efc-4947-a2bf-2a4e34c8dd4f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:businessRuleTask": {"implementation": "##unspecified", "name": "Documents Table", "id": "20354183-1ad8-4916-8709-13af38dc69ae", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "129", "y": "57", "width": "95", "height": "70"}, "ns3:ruleSet": {"ns3:locale": "en", "ns3:rules": {"ns3:ruleId": "7dabda14-1430-445d-850f-0b1907e63ded", "ns3:name": "rule 1", "ns3:type": "DECISION_TABLE", "ns3:decisionTableDefinition": "<?xml version=\"1.0\" encoding=\"UTF-8\"?><DT xmlns=\"http://schemas.ilog.com/Rules/7.0/DecisionTable\" Version=\"7.0\"><Body><Properties><Property Name=\"UI.MediaType\"><![CDATA[Web]]></Property></Properties><Structure><ConditionDefinitions><ConditionDefinition Id=\"C0\"><ExpressionDefinition><Text><![CDATA[requestType contains <a string> ]]></Text></ExpressionDefinition></ConditionDefinition><ConditionDefinition Id=\"C2\"><ExpressionDefinition><Text><![CDATA[documentSource is one of <strings> ]]></Text></ExpressionDefinition></ConditionDefinition></ConditionDefinitions><ActionDefinitions><ActionDefinition Id=\"A0\"><ExpressionDefinition><Text><![CDATA[set result to <a string> \r\n]]></Text></ExpressionDefinition></ActionDefinition></ActionDefinitions></Structure><Contents><Partition DefId=\"C0\"><Condition><Expression><Param><![CDATA[\"Advance Payment\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Customer Request,Invoice\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition><Condition><Expression><Param><![CDATA[\"ICAP\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Customer Request,Invoice,Transport document\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition><Condition><Expression><Param><![CDATA[\"IDC Acknowledgement\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Correspondent cover letter,Invoice,Transport document\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition><Condition><Expression><Param><![CDATA[\"IDC Execution\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Correspondent\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Customer Request,Correspondent cover letter,Invoice,Transport document\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition><Condition><Expression><Param><![CDATA[\"IDC Execution Completion\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Correspondent\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Customer Request,Correspondent cover letter,Invoice,Transport document\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition><Condition><Expression><Param><![CDATA[\"IDC Execution\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Exporter\", \"Importer\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Customer Request,Invoice,Transport document\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition><Condition><Expression><Param><![CDATA[\"IDC Completion\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Exporter\", \"Importer\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Customer Request,Invoice,Transport document\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition><Condition><Expression><Param><![CDATA[\"IDC Payment\"]]></Param></Expression><Partition DefId=\"C2\"><Condition><Expression><Param><![CDATA[{ \"Correspondent\",\"Exporter\",\"Importer\" }]]></Param></Expression><ActionSet><Action DefId=\"A0\"><Expression><Param><![CDATA[\"Customer Request\"]]></Param></Expression></Action></ActionSet></Condition></Partition></Condition></Partition></Contents></Body><Resources DefaultLocale=\"en_US\"><ResourceSet Locale=\"en\"><Data Name=\"Definitions(C0)#HeaderText\"><![CDATA[Request Type]]></Data><Data Name=\"Definitions(A0)#Width\"><![CDATA[80]]></Data><Data Name=\"Definitions(C2)#HeaderText\"><![CDATA[Document Source]]></Data><Data Name=\"Definitions(C0)#Width\"><![CDATA[80]]></Data><Data Name=\"Definitions(C2)#Width\"><![CDATA[227]]></Data></ResourceSet></Resources></DT>", "ns3:decisionTableHash": "KdbHXPfaz2BRnCbX42wK1xPSEGQTyI7I8PpVfcdI9AE=", "ns3:locale": "en"}}}, "ns16:outgoing": "3247959b-80c1-4c86-8ef6-0073e8dbcb6e"}, "ns16:dataObject": {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "result", "id": "2056.3a40c528-feef-4005-919f-0a729c2cfb56", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"\"", "useDefault": "true"}}}, "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Set Documents List", "id": "b73973ae-cf61-48f7-a7d7-58462330ed5d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "280", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": ["3247959b-80c1-4c86-8ef6-0073e8dbcb6e", "2027.fbd093a1-004b-4316-870b-898610f44822"], "ns16:outgoing": "e75637ab-2efc-4947-a2bf-2a4e34c8dd4f", "ns16:script": "tw.local.requiredDocuments = new tw.object.listOf.String();\r\r\nif (tw.local.result.length > 0) {\r\r\n\ttw.local.requiredDocuments = tw.local.result.split(\",\")\r\r\n}"}}}}, "link": [{"name": "To Set Documents List", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.3247959b-80c1-4c86-8ef6-0073e8dbcb6e", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.20354183-1ad8-4916-8709-13af38dc69ae", "2025.20354183-1ad8-4916-8709-13af38dc69ae"], "endStateId": "Out", "toProcessItemId": ["2025.b73973ae-cf61-48f7-a7d7-58462330ed5d", "2025.b73973ae-cf61-48f7-a7d7-58462330ed5d"], "guid": "4d3f994a-0a43-4586-a84b-76011cb9a2f7", "versionId": "874258be-af41-47f1-a7ec-4b0778743f35", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e75637ab-2efc-4947-a2bf-2a4e34c8dd4f", "processId": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b73973ae-cf61-48f7-a7d7-58462330ed5d", "2025.b73973ae-cf61-48f7-a7d7-58462330ed5d"], "endStateId": "Out", "toProcessItemId": ["2025.1b5a020e-5857-4a63-8df4-727ea9014b25", "2025.1b5a020e-5857-4a63-8df4-727ea9014b25"], "guid": "cf38aaeb-f981-40f6-b665-57902c4c21f1", "versionId": "b0e9d7b2-6eb3-4aa5-a944-aa244c6aaedf", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}