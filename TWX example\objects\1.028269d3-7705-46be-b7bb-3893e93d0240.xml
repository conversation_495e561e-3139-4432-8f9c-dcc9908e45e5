<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.028269d3-7705-46be-b7bb-3893e93d0240" name="Query BC contract 2">
        <lastModified>1698580479651</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.67a25a6f-df62-498d-8f38-f30fd6faf86d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:53c3</guid>
        <versionId>1b81c58c-4961-4b95-a8fa-3422edd86cbe</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:056f27db08707381:36b20ea0:18b7b4cc468:1be1" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.03b9bef5-df02-48df-8f6b-224718613a70"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":0,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"44c8a0b9-35fe-41fd-83f9-c045f3e28d2b"},{"incoming":["e9d84465-adca-41d0-85fb-62634dbca12b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":750,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:53c5"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"308a7d21-6566-4d4c-8a21-813fb30b6438"},{"targetRef":"67a25a6f-df62-498d-8f38-f30fd6faf86d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Map to private variables","declaredType":"sequenceFlow","id":"2027.03b9bef5-df02-48df-8f6b-224718613a70","sourceRef":"44c8a0b9-35fe-41fd-83f9-c045f3e28d2b"},{"startQuantity":1,"outgoing":["10296623-cbf6-490c-804a-f35705185f7d"],"incoming":["49c94340-3e71-4b42-8cf4-35f65cadeddc"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":169,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Query BC contract","dataInputAssociation":[{"targetRef":"2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.42effea6-8d8b-437c-958a-da5cf9119674","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.8f786b77-ae66-4547-a444-d6ccb8969c42","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.ContractDetails.parentRequestNo"]}}]},{"targetRef":"2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC Creation \/ Amendment Process Details\""]}}]},{"targetRef":"2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\/\/tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.4ff12b5f-6b69-4504-a730-046ee5c2000a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\/\/tw.system.user_id"]}}]},{"targetRef":"2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\/\/tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"988c413d-535c-4478-8c1e-a72f5de9d8a5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.7c34490a-c8bf-41cd-b6e8-f39738896a16","declaredType":"TFormalExpression","content":["tw.local.queryBCContractResults"]}}],"sourceRef":["2055.004a9996-2e6b-4d60-821e-5db8e4ba2271"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.95835270-515f-4794-a207-a5e2aa301c0e"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.ee7811d9-22b1-4727-9148-bcac74c306af"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4"]}],"calledElement":"1.3875c748-15fc-40ef-bef1-ea4d905d7f75"},{"targetRef":"67606be1-367f-47e3-8707-e69eac6ccdcd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To initialize odc object","declaredType":"sequenceFlow","id":"10296623-cbf6-490c-804a-f35705185f7d","sourceRef":"988c413d-535c-4478-8c1e-a72f5de9d8a5"},{"startQuantity":1,"outgoing":["e9d84465-adca-41d0-85fb-62634dbca12b"],"incoming":["b1f1f049-a91b-4d4f-8789-ab5e8f012323","10296623-cbf6-490c-804a-f35705185f7d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":521,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"67606be1-367f-47e3-8707-e69eac6ccdcd","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.queryBCContractResults != null)\r\n{\r\n\ttw.local.queryBCContractResults.BaseDate;\r\n\ttw.local.ContractDetails.requestDate= new TWDate();\/\/tw.local.queryBCContractResults.BaseDate;\r\n\ttw.local.ContractDetails.ImporterName= \"Webex\";\/\/\r\n\t\r\n\ttw.local.ContractDetails.requestType = tw.local.requestType;\r\n\ttw.local.ContractDetails.requestNature = tw.local.requestNature;\r\n\ttw.local.ContractDetails.parentRequestNo = tw.local.parentRequestNumber;\r\n\ttw.local.ContractDetails.CustomerInfo = tw.local.customerInfo;\r\n\t\r\n\ttw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();\r\n\ttw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;\r\n\ttw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;\r\n\ttw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;\r\n\ttw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;\r\n\ttw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);\r\n\ttw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);\r\n\ttw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);\r\n\ttw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;\r\n\ttw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;\r\n\ttw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);\r\n\ttw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);\r\n\ttw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;\r\n\ttw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);\r\n\t\r\n\ttw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;\r\n\t\r\n\tfor(var i=0;i&lt;tw.local.queryBCContractResults.Contract_Parties.listLength;i++)\r\n\t{\r\n\t\tif(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"COLLECTING BANK\")\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\n\r\n\t\t}\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWEE\")\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\n\r\n\t\t}\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWER\")\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\n\r\n\t\t}\r\n\r\n\t}\r\n\t\r\n\tif(tw.local.queryBCContractResults.Contract_Multitnr != null)\r\n\t{\r\n\t\tfor(i=0;i&lt;tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();\r\n\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);\r\n\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);\r\n\t\t}\r\n\r\n\t}\r\n\t\r\n\tif(tw.local.queryBCContractResults.Charges != null &amp;&amp; tw.local.queryBCContractResults.Charges[0] != null)\r\n\t{\r\n\t\tfor(i=0;i&lt;tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;\r\n\t\t\tif(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == \"y\")\r\n\t\t\t{\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;\t\t\t\r\n\t\t\t}\r\n\t\t\t\t\t\r\n\t\t}\r\n\t}\r\n\tif (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency==\"\")\r\n\t\ttw.local.queryBCContractResults.Currency= \"USD\";\r\n\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;\r\n\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;\t\t\r\n}\r\n\ttw.local.ContractDetails.FinancialDetailsBR.maxCollectionDate =new TWDate();\r\n\ttw.local.ContractDetails.FinancialDetailsBR.amountAdvanced = 100.6;\r\n\ttw.local.ContractDetails.FinancialDetailsBR.documentAmount = 400.9;\r\n\t\r\n\ttw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount= new tw.object.toolkit.TWSYS.NameValuePair(); \r\n\ttw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"176\";\r\n\ttw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount.value =\"1762460873245900084\";\r\n\t\r\n\ttw.local.ContractDetails.FinancialDetailsBR.collectionAccount= new tw.object.toolkit.TWSYS.NameValuePair(); \r\n\ttw.local.ContractDetails.FinancialDetailsBR.collectionAccount.name = \"176\"\r\n\ttw.local.ContractDetails.FinancialDetailsBR.collectionAccount.value = \"1762460873245900073\";\r\n\t\t\r\n\ttw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();\r\n\ttw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;\r\n\ttw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;\r\n\t\r\n\ttw.local.ContractDetails.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair(); \r\n\ttw.local.ContractDetails.BasicDetails.paymentTerms.name =\"003\";\r\n\ttw.local.ContractDetails.BasicDetails.paymentTerms.value = \"\u0623\u062c\u0644 \u0628\u062f\u0648\u0646 \u0627\u0644\u062a\u0632\u0627\u0645\";\r\n\t\r\n\ttw.local.ContractDetails.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair(); \r\n\ttw.local.ContractDetails.BasicDetails.exportPurpose.name =\"001\";\r\n\ttw.local.ContractDetails.BasicDetails.exportPurpose.value = \"\u062a\u0635\u062f\u064a\u0631 \u0633\u0644\u0639\u064a\";\r\n\t\r\n\ttw.local.ContractDetails.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\n\ttw.local.ContractDetails.BasicDetails.productCategory.name = \"INSB\";\r\n\ttw.local.ContractDetails.BasicDetails.productCategory.value = \"Incoming Standby\";\r\n\t\r\n\ttw.local.ContractDetails.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\n\ttw.local.ContractDetails.BasicDetails.CountryOfOrigin.name =\"MQ\";\r\n\ttw.local.ContractDetails.BasicDetails.CountryOfOrigin.value =\"\u0645\u0627\u0631\u062a\u064a\u0646\u0643\u0648\";\r\n\ttw.local.ContractDetails.BasicDetails.commodityDescription =\"the product will expire in may 2025\";\r\n\t\t\r\n\ttw.local.ContractDetails.BasicDetails.Bills =  new tw.object.listOf.Bills();\r\n\ttw.local.ContractDetails.BasicDetails.Bills[0] = new tw.object.Bills();\r\n\ttw.local.ContractDetails.BasicDetails.Bills[0].billOfLadingDate= new TWDate();\r\n\ttw.local.ContractDetails.BasicDetails.Bills[0].billOfLadingRef= \"7654321234564\";\r\n\r\n\ttw.local.ContractDetails.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\n\ttw.local.ContractDetails.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\n\ttw.local.ContractDetails.BasicDetails.Invoice[0].invoiceNo= \"34562324372\";\r\n\ttw.local.ContractDetails.BasicDetails.Invoice[0].invoiceDate=new TWDate();\r\n\t\r\n\t"]}},{"targetRef":"308a7d21-6566-4d4c-8a21-813fb30b6438","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e9d84465-adca-41d0-85fb-62634dbca12b","sourceRef":"67606be1-367f-47e3-8707-e69eac6ccdcd"},{"incoming":["8e08a19f-0ac3-436a-8cda-d5307a9798d9"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ecac6e93-3945-4f7c-8711-7ec517147f32","otherAttributes":{"eventImplId":"868d5e4d-f403-40cb-8097-1e04592f09bb"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":375,"y":167,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["\r\ntw.local.ajaxError = new tw.object.AjaxError();\r\nlog.info(\"*====================== ODC =========================*\");\r\nlog.info(\"[Query BC contract -&gt; Log Error ]- START\");\r\nlog.info(\"Process Instance ID:\" +tw.system.currentProcessInstanceID + \" Error Message: \" + tw.system.error.toString(true));\r\n\r\nlog.info(\"[Query BC contract -&gt; Log Error ]- END\");\r\nlog.info(\"*=======================================================*\");\r\n\r\ntw.local.ajaxError.errorText = tw.local.errorMessage;"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.ajaxError.errorText"]}}]}],"declaredType":"endEvent","id":"c46353db-e72b-46f4-859f-426d9e5070d9"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();\nautoObject.errorText = \"\";\nautoObject.errorCode = \"\";\nautoObject.serviceInError = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"ajaxError","isCollection":false,"declaredType":"dataObject","id":"2056.0f98651a-c511-4e57-86de-8a9856405a2b"},{"itemSubjectRef":"itm.12.*************-4cbb-a781-44d233d577c6","name":"queryBCContractResults","isCollection":false,"declaredType":"dataObject","id":"2056.17e73a67-5d7b-4d08-898c-ca06564eb485"},{"startQuantity":1,"outgoing":["b1f1f049-a91b-4d4f-8789-ab5e8f012323"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":358,"y":59,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"initialize odc object","dataInputAssociation":[{"targetRef":"2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ContractDetails"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"13ef34bf-9380-426f-8a20-a154bf30620d","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ContractDetails"]}}],"sourceRef":["2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de"]}],"calledElement":"1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e"},{"targetRef":"67606be1-367f-47e3-8707-e69eac6ccdcd","extensionElements":{"endStateId":["guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Map output","declaredType":"sequenceFlow","id":"b1f1f049-a91b-4d4f-8789-ab5e8f012323","sourceRef":"13ef34bf-9380-426f-8a20-a154bf30620d"},{"parallelMultiple":false,"outgoing":["8e08a19f-0ac3-436a-8cda-d5307a9798d9"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"74c8969c-bdd3-424f-86d9-5adf3527fe5c"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"5ac03e45-8f34-4096-89e3-9d38fcd92e74","otherAttributes":{"eventImplId":"a3bc2e2d-810b-4dbc-8e52-967979f8cbcd"}}],"attachedToRef":"13ef34bf-9380-426f-8a20-a154bf30620d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":393,"y":117,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"84e493c6-0082-4ffb-842e-04a0eeeabc74","outputSet":{}},{"targetRef":"c46353db-e72b-46f4-859f-426d9e5070d9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"8e08a19f-0ac3-436a-8cda-d5307a9798d9","sourceRef":"84e493c6-0082-4ffb-842e-04a0eeeabc74"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.13893f90-ef42-4ab9-862e-e8a66297a134"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.71eb30f4-df91-427d-8f8f-b1834299cfe5"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.2f253943-a7c0-48f5-8441-a6f931ab70c4"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"requestType","isCollection":false,"declaredType":"dataObject","id":"2056.938e0150-a0fd-42bb-869d-1aab4f8a6913"},{"startQuantity":1,"outgoing":["49c94340-3e71-4b42-8cf4-35f65cadeddc"],"incoming":["2027.03b9bef5-df02-48df-8f6b-224718613a70"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":45,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map to private variables","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"67a25a6f-df62-498d-8f38-f30fd6faf86d","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.requestType = new tw.object.NameValuePair();\r\ntw.local.requestType.name = \"\";\r\ntw.local.requestType.value = \"\";\r\ntw.local.requestNature = new tw.object.NameValuePair();\r\ntw.local.requestNature.name = \"\";\r\ntw.local.requestNature.value = \"\";\r\ntw.local.parentRequestNumber =\"\";\r\ntw.local.customerInfo = new tw.object.CustomerInfo();\r\ntw.local.customerInfo.customerSector = new tw.object.NameValuePair();\r\ntw.local.customerInfo.customerSector.name = \"\";\r\ntw.local.customerInfo.customerSector.value = \"\";\r\ntw.local.customerInfo.facilityType = new tw.object.NameValuePair();\r\ntw.local.customerInfo.facilityType.name = \"\";\r\ntw.local.customerInfo.facilityType.value = \"\";\r\n\r\n\r\ntw.local.requestType = tw.local.ContractDetails.requestType;\r\ntw.local.requestNature =tw.local.ContractDetails.requestNature;\r\ntw.local.parentRequestNumber= tw.local.ContractDetails.parentRequestNo;\r\ntw.local.customerInfo= tw.local.ContractDetails.CustomerInfo;"]}},{"targetRef":"988c413d-535c-4478-8c1e-a72f5de9d8a5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Query BC contract","declaredType":"sequenceFlow","id":"49c94340-3e71-4b42-8cf4-35f65cadeddc","sourceRef":"67a25a6f-df62-498d-8f38-f30fd6faf86d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.name = \"\";\nautoObject.value = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"requestNature","isCollection":false,"declaredType":"dataObject","id":"2056.b15697d9-b1e3-48ea-81ed-24a29e67a4fc"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNumber","isCollection":false,"declaredType":"dataObject","id":"2056.cc7d4757-6ae3-4d36-8d80-0d16dd763534"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.CustomerInfo();\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.addressLine1 = \"\";\nautoObject.addressLine2 = \"\";\nautoObject.addressLine3 = \"\";\nautoObject.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.customerSector.name = \"\";\nautoObject.customerSector.value = \"\";\nautoObject.customerType = \"\";\nautoObject.customerNoCBE = \"\";\nautoObject.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.facilityType.name = \"\";\nautoObject.facilityType.value = \"\";\nautoObject.commercialRegistrationNo = \"\";\nautoObject.commercialRegistrationOffice = \"\";\nautoObject.taxCardNo = \"\";\nautoObject.importCardNo = \"\";\nautoObject.initiationHub = \"\";\nautoObject.country = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a","name":"customerInfo","isCollection":false,"declaredType":"dataObject","id":"2056.c9ab6d6f-f87b-47a5-843c-29ebb445a910"},{"parallelMultiple":false,"outgoing":["aa83d8cd-bb28-414a-88e3-e4b293c2f8c0"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5deb5993-2f31-488c-8fbc-91da2ba8f736"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4f9ffccd-657f-43a5-817e-37d7650bed82","otherAttributes":{"eventImplId":"99853e7b-29dd-41aa-83b1-ba27e140ccd6"}}],"attachedToRef":"988c413d-535c-4478-8c1e-a72f5de9d8a5","extensionElements":{"nodeVisualInfo":[{"width":24,"x":204,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"b39dcde2-62c1-4460-80df-24e334d595ed","outputSet":{}},{"startQuantity":1,"incoming":["aa83d8cd-bb28-414a-88e3-e4b293c2f8c0","e150512f-46f3-4b50-8896-92be7044e79c"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":360,"y":220,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Query BC contract\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"9aa3d732-7854-4b98-88a8-97f484e035c8","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"9aa3d732-7854-4b98-88a8-97f484e035c8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"aa83d8cd-bb28-414a-88e3-e4b293c2f8c0","sourceRef":"b39dcde2-62c1-4460-80df-24e334d595ed"},{"parallelMultiple":false,"outgoing":["e150512f-46f3-4b50-8896-92be7044e79c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"506bc477-4dcc-4ef6-81b1-3ad877307496"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0e76fdd7-195b-4514-887a-51be30f0dbc3","otherAttributes":{"eventImplId":"448f2578-87ba-41e7-88fd-d817e4529556"}}],"attachedToRef":"67606be1-367f-47e3-8707-e69eac6ccdcd","extensionElements":{"nodeVisualInfo":[{"width":24,"x":556,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"0c41f441-a380-4897-8d44-814566986590","outputSet":{}},{"targetRef":"9aa3d732-7854-4b98-88a8-97f484e035c8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"e150512f-46f3-4b50-8896-92be7044e79c","sourceRef":"0c41f441-a380-4897-8d44-814566986590"}],"laneSet":[{"id":"5adc3756-ff32-42d2-8c84-b1322afc45d2","lane":[{"flowNodeRef":["44c8a0b9-35fe-41fd-83f9-c045f3e28d2b","308a7d21-6566-4d4c-8a21-813fb30b6438","988c413d-535c-4478-8c1e-a72f5de9d8a5","67606be1-367f-47e3-8707-e69eac6ccdcd","c46353db-e72b-46f4-859f-426d9e5070d9","13ef34bf-9380-426f-8a20-a154bf30620d","84e493c6-0082-4ffb-842e-04a0eeeabc74","67a25a6f-df62-498d-8f38-f30fd6faf86d","b39dcde2-62c1-4460-80df-24e334d595ed","9aa3d732-7854-4b98-88a8-97f484e035c8","0c41f441-a380-4897-8d44-814566986590"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"bc18c354-9af5-4818-87ce-17628f8da105","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Query BC contract 2","declaredType":"process","id":"1.028269d3-7705-46be-b7bb-3893e93d0240","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"ContractDetails","isCollection":false,"id":"2055.c29e9950-**************-a265c9a11073"}],"inputSet":[{"dataInputRefs":["2055.27bd0649-625a-4578-880a-7a3b02e51071"]}],"outputSet":[{"dataOutputRefs":["2055.c29e9950-**************-a265c9a11073"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"02366014\";\r\nautoObject.customerName = \"FULL-NAME-02366014\";\r\nautoObject.parentRequestNo = \"100OMBC222160001\";\r\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","documentation":[{"content":["parentRequestNo\u00a0&lt;div&gt;&lt;div&gt;\/\/&amp;quot;07704230001024&amp;quot;&lt;\/div&gt;&lt;div&gt;&amp;quot;100OMBC222160001&amp;quot;&lt;\/div&gt;&lt;\/div&gt;"],"textFormat":"text\/plain"}],"name":"ContractDetails","isCollection":false,"id":"2055.27bd0649-625a-4578-880a-7a3b02e51071"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="ContractDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.27bd0649-625a-4578-880a-7a3b02e51071</processParameterId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description>parentRequestNo &lt;div&gt;&lt;div&gt;//&amp;quot;07704230001024&amp;quot;&lt;/div&gt;&lt;div&gt;&amp;quot;100OMBC222160001&amp;quot;&lt;/div&gt;&lt;/div&gt;</description>
            <guid>878691ac-5d52-4365-ac67-294364d7ab9a</guid>
            <versionId>67c75dcc-b319-4916-919a-2c99818cf921</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.41950919-12e4-46db-a7ad-c9f222111938</processParameterId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>584f6179-361f-407b-b322-837a6757cbbe</guid>
            <versionId>94924164-444d-4ba3-8445-04a93e56ffb8</versionId>
        </processParameter>
        <processParameter name="ContractDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c29e9950-**************-a265c9a11073</processParameterId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5b12e5f0-0950-4bb5-8556-e3efcaa54e32</guid>
            <versionId>29c29df1-dddc-4ef6-a7c8-1bc9273e971e</versionId>
        </processParameter>
        <processVariable name="ajaxError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0f98651a-c511-4e57-86de-8a9856405a2b</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c81d3460-c16d-4990-b156-3f0adebefea0</guid>
            <versionId>1d33341a-6bf5-4ef9-bc72-397069ce5dff</versionId>
        </processVariable>
        <processVariable name="queryBCContractResults">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.17e73a67-5d7b-4d08-898c-ca06564eb485</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8e5fb499-7ed0-44c1-9166-d43a74a24a8b</guid>
            <versionId>7bc4fc69-b2a3-43a6-9b33-f42581cc8668</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.13893f90-ef42-4ab9-862e-e8a66297a134</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6f1472c0-9437-43e5-8716-116022d65d57</guid>
            <versionId>bb74cabb-9381-4ad8-bce6-c1a2eb277372</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.71eb30f4-df91-427d-8f8f-b1834299cfe5</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2779c25a-4647-4c71-8154-f3df123ed74b</guid>
            <versionId>8077a7a8-41b0-4ef3-84c0-4ce07c349ed4</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2f253943-a7c0-48f5-8441-a6f931ab70c4</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2c7101dc-bc2f-4bf9-8705-84db5ace458e</guid>
            <versionId>beddd887-b2ea-48df-bd56-2a8184590d20</versionId>
        </processVariable>
        <processVariable name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.938e0150-a0fd-42bb-869d-1aab4f8a6913</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3d5138f3-2ca0-4e55-816b-abdad472137a</guid>
            <versionId>fc49ffdb-5fb6-4e57-8be3-1ff7e00b67ba</versionId>
        </processVariable>
        <processVariable name="requestNature">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b15697d9-b1e3-48ea-81ed-24a29e67a4fc</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fca93982-7acc-47ca-9a66-0d5f6d2852a3</guid>
            <versionId>e4d9bfbe-c23d-4849-b24d-06c24c5466f0</versionId>
        </processVariable>
        <processVariable name="parentRequestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cc7d4757-6ae3-4d36-8d80-0d16dd763534</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>c1674d32-2f14-4c1e-8d24-3ed33e68a8a1</guid>
            <versionId>38fde4cf-d16f-430e-91c8-373ef781b3a4</versionId>
        </processVariable>
        <processVariable name="customerInfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c9ab6d6f-f87b-47a5-843c-29ebb445a910</processVariableId>
            <description isNull="true" />
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.CustomerInfo();
autoObject.cif = "";
autoObject.customerName = "";
autoObject.addressLine1 = "";
autoObject.addressLine2 = "";
autoObject.addressLine3 = "";
autoObject.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.customerSector.name = "";
autoObject.customerSector.value = "";
autoObject.customerType = "";
autoObject.customerNoCBE = "";
autoObject.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilityType.name = "";
autoObject.facilityType.value = "";
autoObject.commercialRegistrationNo = "";
autoObject.commercialRegistrationOffice = "";
autoObject.taxCardNo = "";
autoObject.importCardNo = "";
autoObject.initiationHub = "";
autoObject.country = "";
autoObject</defaultValue>
            <guid>03383c22-4f3f-4048-b8f9-113df76e6c45</guid>
            <versionId>691006d0-52e7-4574-8866-8aa76bb996c3</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9aa3d732-7854-4b98-88a8-97f484e035c8</processItemId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.266aeeb8-354f-45c6-9027-17ea87e25c73</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a051bcdb927c971:-1aedb8c3:18b485b51eb:-6b97</guid>
            <versionId>142b14e2-7441-455d-aad2-8b7110e8a26c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="360" y="220">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.266aeeb8-354f-45c6-9027-17ea87e25c73</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>82d7650d-68b4-4559-8af4-e38f759fd8f2</guid>
                <versionId>9c864c97-92ac-4f33-9763-81ed8d151f5c</versionId>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b27f742e-f160-4daa-a576-a0b3f3f29436</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.266aeeb8-354f-45c6-9027-17ea87e25c73</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8b05e270-1b38-4061-bb7f-268df7340e7d</guid>
                    <versionId>5dfdcef1-8e0b-492b-977e-3957cc392ac1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.54913732-666c-4c2a-878c-f8ade8c17866</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.266aeeb8-354f-45c6-9027-17ea87e25c73</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Query BC contract"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f8c75760-4dd3-4984-9a7b-59fd5ad53089</guid>
                    <versionId>6c23445a-5ee6-4616-95be-dce8d915e57d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.67606be1-367f-47e3-8707-e69eac6ccdcd</processItemId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.78a44b63-6e54-4b76-9271-34b76f0651b7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.9aa3d732-7854-4b98-88a8-97f484e035c8</errorHandlerItemId>
            <guid>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:5425</guid>
            <versionId>7291c423-203c-4626-9fd5-439389a68ec7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="521" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:4a051bcdb927c971:-1aedb8c3:18b485b51eb:-6b97</errorHandlerItem>
                <errorHandlerItemId>2025.9aa3d732-7854-4b98-88a8-97f484e035c8</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.78a44b63-6e54-4b76-9271-34b76f0651b7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.queryBCContractResults != null)&#xD;
{&#xD;
	tw.local.queryBCContractResults.BaseDate;&#xD;
	tw.local.ContractDetails.requestDate= new TWDate();//tw.local.queryBCContractResults.BaseDate;&#xD;
	tw.local.ContractDetails.ImporterName= "Webex";//&#xD;
	&#xD;
	tw.local.ContractDetails.requestType = tw.local.requestType;&#xD;
	tw.local.ContractDetails.requestNature = tw.local.requestNature;&#xD;
	tw.local.ContractDetails.parentRequestNo = tw.local.parentRequestNumber;&#xD;
	tw.local.ContractDetails.CustomerInfo = tw.local.customerInfo;&#xD;
	&#xD;
	tw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();&#xD;
	tw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;&#xD;
	tw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;&#xD;
	tw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;&#xD;
	tw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;&#xD;
	tw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);&#xD;
	tw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);&#xD;
	tw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);&#xD;
	tw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;&#xD;
	tw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;&#xD;
	tw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);&#xD;
	tw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);&#xD;
	tw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;&#xD;
	tw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);&#xD;
	&#xD;
	tw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;&#xD;
	&#xD;
	for(var i=0;i&lt;tw.local.queryBCContractResults.Contract_Parties.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "COLLECTING BANK")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
			tw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWEE")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();&#xD;
			tw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWER")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();&#xD;
			tw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
		}&#xD;
		else&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
&#xD;
	}&#xD;
	&#xD;
	if(tw.local.queryBCContractResults.Contract_Multitnr != null)&#xD;
	{&#xD;
		for(i=0;i&lt;tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)&#xD;
		{&#xD;
			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();&#xD;
			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);&#xD;
			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);&#xD;
		}&#xD;
&#xD;
	}&#xD;
	&#xD;
	if(tw.local.queryBCContractResults.Charges != null &amp;&amp; tw.local.queryBCContractResults.Charges[0] != null)&#xD;
	{&#xD;
		for(i=0;i&lt;tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)&#xD;
		{&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;&#xD;
			if(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;			&#xD;
			}&#xD;
					&#xD;
		}&#xD;
	}&#xD;
	if (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency=="")&#xD;
		tw.local.queryBCContractResults.Currency= "USD";&#xD;
		tw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;&#xD;
		tw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;		&#xD;
}&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.maxCollectionDate =new TWDate();&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.amountAdvanced = 100.6;&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.documentAmount = 400.9;&#xD;
	&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount= new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount.name = "176";&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount.value ="1762460873245900084";&#xD;
	&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.collectionAccount= new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.FinancialDetailsBR.collectionAccount.name = "176"&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.collectionAccount.value = "1762460873245900073";&#xD;
		&#xD;
	tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();&#xD;
	tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;&#xD;
	tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.BasicDetails.paymentTerms.name ="003";&#xD;
	tw.local.ContractDetails.BasicDetails.paymentTerms.value = "أجل بدون التزام";&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.BasicDetails.exportPurpose.name ="001";&#xD;
	tw.local.ContractDetails.BasicDetails.exportPurpose.value = "تصدير سلعي";&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
	tw.local.ContractDetails.BasicDetails.productCategory.name = "INSB";&#xD;
	tw.local.ContractDetails.BasicDetails.productCategory.value = "Incoming Standby";&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
	tw.local.ContractDetails.BasicDetails.CountryOfOrigin.name ="MQ";&#xD;
	tw.local.ContractDetails.BasicDetails.CountryOfOrigin.value ="مارتينكو";&#xD;
	tw.local.ContractDetails.BasicDetails.commodityDescription ="the product will expire in may 2025";&#xD;
		&#xD;
	tw.local.ContractDetails.BasicDetails.Bills =  new tw.object.listOf.Bills();&#xD;
	tw.local.ContractDetails.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
	tw.local.ContractDetails.BasicDetails.Bills[0].billOfLadingDate= new TWDate();&#xD;
	tw.local.ContractDetails.BasicDetails.Bills[0].billOfLadingRef= "7654321234564";&#xD;
&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice[0].invoiceNo= "34562324372";&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice[0].invoiceDate=new TWDate();&#xD;
	&#xD;
	</script>
                <isRule>false</isRule>
                <guid>b714b90e-697e-475d-ac11-097a9490a8dd</guid>
                <versionId>d61f971a-3c4a-4316-ab72-a4a08e835413</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.308a7d21-6566-4d4c-8a21-813fb30b6438</processItemId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.972da452-e2fa-4e61-a0e8-ef2fb8fd2170</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:53c5</guid>
            <versionId>b8d23da6-6610-4c10-a8f9-c458913bf1f9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="750" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.972da452-e2fa-4e61-a0e8-ef2fb8fd2170</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>68eb4081-cc38-4c34-bf9a-7abbff79cc20</guid>
                <versionId>ffe1e6e2-a183-4dec-aa05-ae224c7e3784</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.67a25a6f-df62-498d-8f38-f30fd6faf86d</processItemId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <name>Map to private variables</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9030d439-cabb-4ce3-8d41-e3237819ef8a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:989374ae3827db3a:f13aa6c:18a98f4fe63:5d5a</guid>
            <versionId>c01e6e97-ff1e-458f-b7d0-41c7fdd9b469</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="45" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9030d439-cabb-4ce3-8d41-e3237819ef8a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.requestType = new tw.object.NameValuePair();&#xD;
tw.local.requestType.name = "";&#xD;
tw.local.requestType.value = "";&#xD;
tw.local.requestNature = new tw.object.NameValuePair();&#xD;
tw.local.requestNature.name = "";&#xD;
tw.local.requestNature.value = "";&#xD;
tw.local.parentRequestNumber ="";&#xD;
tw.local.customerInfo = new tw.object.CustomerInfo();&#xD;
tw.local.customerInfo.customerSector = new tw.object.NameValuePair();&#xD;
tw.local.customerInfo.customerSector.name = "";&#xD;
tw.local.customerInfo.customerSector.value = "";&#xD;
tw.local.customerInfo.facilityType = new tw.object.NameValuePair();&#xD;
tw.local.customerInfo.facilityType.name = "";&#xD;
tw.local.customerInfo.facilityType.value = "";&#xD;
&#xD;
&#xD;
tw.local.requestType = tw.local.ContractDetails.requestType;&#xD;
tw.local.requestNature =tw.local.ContractDetails.requestNature;&#xD;
tw.local.parentRequestNumber= tw.local.ContractDetails.parentRequestNo;&#xD;
tw.local.customerInfo= tw.local.ContractDetails.CustomerInfo;</script>
                <isRule>false</isRule>
                <guid>7bedcfa5-d51f-412d-9eea-85a3a2c25a32</guid>
                <versionId>8f76c59e-d9a0-46ae-b8f6-d65f29c88930</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.13ef34bf-9380-426f-8a20-a154bf30620d</processItemId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <name>initialize odc object</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e6b71879-9494-4e42-a3b4-860099e457c4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.c46353db-e72b-46f4-859f-426d9e5070d9</errorHandlerItemId>
            <guid>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:6041</guid>
            <versionId>df0c4415-8dc1-4d5d-a18b-ac22b0202514</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="358" y="59">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:543b</errorHandlerItem>
                <errorHandlerItemId>2025.c46353db-e72b-46f4-859f-426d9e5070d9</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e6b71879-9494-4e42-a3b4-860099e457c4</subProcessId>
                <attachedProcessRef>/1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</attachedProcessRef>
                <guid>ff5691cc-07fc-4856-9774-feaa1c1b44f2</guid>
                <versionId>00cff88f-6197-4e83-aa15-db93680f178b</versionId>
                <parameterMapping name="odcRequest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b2fdd843-0fe5-4cfc-8877-a4ada78dc111</parameterMappingId>
                    <processParameterId>2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5</processParameterId>
                    <parameterMappingParentId>3012.e6b71879-9494-4e42-a3b4-860099e457c4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ContractDetails</value>
                    <classRef>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>70c9510e-ea76-4c3d-a03c-6dbe35471296</guid>
                    <versionId>375a74cc-0da1-4774-8bdf-770098d11bff</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="odcRequest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.55ebe5cc-edd0-4fe2-bcdb-09af69356dd4</parameterMappingId>
                    <processParameterId>2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de</processParameterId>
                    <parameterMappingParentId>3012.e6b71879-9494-4e42-a3b4-860099e457c4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ContractDetails</value>
                    <classRef>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>57229830-00f3-4425-abc6-9115a2a6ce49</guid>
                    <versionId>c2ecbfb2-0978-47a1-9063-89635f9d363d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2f0863ad-dfb7-46c9-ab13-4c981204dfd1</parameterMappingId>
                    <processParameterId>2055.247d12a8-cf76-495c-8cd4-700f32286023</processParameterId>
                    <parameterMappingParentId>3012.e6b71879-9494-4e42-a3b4-860099e457c4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>77458298-a776-4137-9bcf-a9438f374f4f</guid>
                    <versionId>df345352-d8d3-4ae1-a7db-6333647ddb99</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c46353db-e72b-46f4-859f-426d9e5070d9</processItemId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.7af71236-4c6f-443a-91c4-97bd1b466813</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:543b</guid>
            <versionId>e474e839-3a11-4603-918d-56b7e51704d4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.cb48b053-576b-43b2-94e1-362439e9a3a6</processItemPrePostId>
                <processItemId>2025.c46353db-e72b-46f4-859f-426d9e5070d9</processItemId>
                <location>1</location>
                <script>&#xD;
tw.local.ajaxError = new tw.object.AjaxError();&#xD;
log.info("*====================== ODC =========================*");&#xD;
log.info("[Query BC contract -&gt; Log Error ]- START");&#xD;
log.info("Process Instance ID:" +tw.system.currentProcessInstanceID + " Error Message: " + tw.system.error.toString(true));&#xD;
&#xD;
log.info("[Query BC contract -&gt; Log Error ]- END");&#xD;
log.info("*=======================================================*");&#xD;
&#xD;
tw.local.ajaxError.errorText = tw.local.errorMessage;</script>
                <guid>42750f92-ff3e-4a12-a283-a3f9019f3a13</guid>
                <versionId>171e21fd-7e7a-45e5-b778-959f9d8c8384</versionId>
            </processPrePosts>
            <layoutData x="375" y="167">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.7af71236-4c6f-443a-91c4-97bd1b466813</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>fa9fabf0-33e9-42c4-b0a4-b12806811646</guid>
                <versionId>138626c6-2fc8-41a8-8121-de4a7f24de5a</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.*************-4099-9cc9-2b986cdf3606</parameterMappingId>
                    <processParameterId>2055.41950919-12e4-46db-a7ad-c9f222111938</processParameterId>
                    <parameterMappingParentId>3007.7af71236-4c6f-443a-91c4-97bd1b466813</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ajaxError.errorText</value>
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>852ea651-4a9e-4a05-9fbe-2baf4906bbdb</guid>
                    <versionId>bc3496d8-56f9-43e5-95cc-6d8cf264c8a3</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.988c413d-535c-4478-8c1e-a72f5de9d8a5</processItemId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <name>Query BC contract</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.9aa3d732-7854-4b98-88a8-97f484e035c8</errorHandlerItemId>
            <guid>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:5426</guid>
            <versionId>f143f2aa-c80c-4858-9c86-50933e7261d2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.6546ffce-929b-4272-8b71-8b72301f84e5</processItemPrePostId>
                <processItemId>2025.988c413d-535c-4478-8c1e-a72f5de9d8a5</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>4981d8e7-e061-4865-81ad-e918785f19da</guid>
                <versionId>0db27d7a-14ca-43c9-a0d0-a3fcf4ce52d5</versionId>
            </processPrePosts>
            <layoutData x="169" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:4a051bcdb927c971:-1aedb8c3:18b485b51eb:-6b97</errorHandlerItem>
                <errorHandlerItemId>2025.9aa3d732-7854-4b98-88a8-97f484e035c8</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.3875c748-15fc-40ef-bef1-ea4d905d7f75</attachedProcessRef>
                <guid>7f452b82-65c3-480a-bb70-353b7f8848ca</guid>
                <versionId>10b2e56e-221f-4d4c-8efe-85aa8732403b</versionId>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fc8dbfbe-a996-41fa-8e2f-0f466267d936</parameterMappingId>
                    <processParameterId>2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC Creation / Amendment Process Details"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c547be74-fe79-4f05-aed4-fae56ca46b92</guid>
                    <versionId>3cb38063-1413-425b-9b10-e05c53c7ec58</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5a273f9a-3563-4d4f-ae43-d72380946064</parameterMappingId>
                    <processParameterId>2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>//tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3228b611-ca9f-446d-8a6d-3921e8703cec</guid>
                    <versionId>42c0b961-2c0e-4308-9dd2-920703d017bb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8af4c4ad-9f97-4f60-8f48-187645b4010f</parameterMappingId>
                    <processParameterId>2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>//tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5769cda3-38db-4b11-bb7f-20f72730cdf8</guid>
                    <versionId>49c5814a-49d6-449b-b9f6-6a33f2f1b85a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="SCQueryBCContractRes">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.595aa6d8-fa64-4033-a296-e57ab93870b0</parameterMappingId>
                    <processParameterId>2055.004a9996-2e6b-4d60-821e-5db8e4ba2271</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.queryBCContractResults</value>
                    <classRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5db4de1f-3eac-4608-ab7f-dd07e1478893</guid>
                    <versionId>5df18f72-49d8-4c61-aea7-9a320bfe5100</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4629c1be-3e96-4b6e-ad5f-6559e6e3660d</parameterMappingId>
                    <processParameterId>2055.42effea6-8d8b-437c-958a-da5cf9119674</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>dd6bb6bf-f1bc-4959-ae03-6adb6756515d</guid>
                    <versionId>6e1de751-d588-4e6e-94f0-8092ea2f5f57</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.586fb7ca-471c-4cb1-909d-062b9d53ea01</parameterMappingId>
                    <processParameterId>2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6acfe5fe-0887-4474-b69e-2afe6fbb5b48</guid>
                    <versionId>8819f857-b17a-4652-bfe9-2981233be0b6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="contractNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ee3df07b-284a-4e16-8c7e-c05519df3803</parameterMappingId>
                    <processParameterId>2055.8f786b77-ae66-4547-a444-d6ccb8969c42</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ContractDetails.parentRequestNo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6cd9fd0f-8a25-4263-963e-654a2f47844b</guid>
                    <versionId>96f3c900-eb0f-4e64-99b6-293243018be2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.27323edb-671a-46c5-8d35-6dd197c921f0</parameterMappingId>
                    <processParameterId>2055.4ff12b5f-6b69-4504-a730-046ee5c2000a</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>//tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b5db1bbf-5ed2-49f5-a1cf-0f604ccac2aa</guid>
                    <versionId>a890fac1-d093-4596-99df-b600d3915683</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6a32b398-4aac-49d9-9568-e71db0e2d610</parameterMappingId>
                    <processParameterId>2055.ee7811d9-22b1-4727-9148-bcac74c306af</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>2fcf6e39-b5d2-4ae3-b0d4-5cf6fb813530</guid>
                    <versionId>bc235a70-1f97-4cf5-bcc2-6d133fb94ce5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.15245a60-d4df-4fc2-9827-489c42898b42</parameterMappingId>
                    <processParameterId>2055.95835270-515f-4794-a207-a5e2aa301c0e</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1294a15c-6e16-4c5e-8c6f-f30054db0d0c</guid>
                    <versionId>c88398cb-847d-4e14-8baa-87197ac98b1c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e1720896-d97d-4905-a9a8-c854b5553b3a</parameterMappingId>
                    <processParameterId>2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753</processParameterId>
                    <parameterMappingParentId>3012.edfdf5ab-4cf1-4285-b10f-ac74019985ea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7a706d89-bdc8-4ce3-ad49-0c339b9d57cb</guid>
                    <versionId>c96a4c5f-0c1b-49b0-a54d-410d319ab19e</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.67a25a6f-df62-498d-8f38-f30fd6faf86d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Query BC contract 2" id="1.028269d3-7705-46be-b7bb-3893e93d0240" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="ContractDetails" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.27bd0649-625a-4578-880a-7a3b02e51071">
                            
                            
                            <ns16:documentation textFormat="text/plain">parentRequestNo &lt;div&gt;&lt;div&gt;//&amp;quot;07704230001024&amp;quot;&lt;/div&gt;&lt;div&gt;&amp;quot;100OMBC222160001&amp;quot;&lt;/div&gt;&lt;/div&gt;</ns16:documentation>
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "02366014";&#xD;
autoObject.customerName = "FULL-NAME-02366014";&#xD;
autoObject.parentRequestNo = "100OMBC222160001";&#xD;
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="ContractDetails" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.c29e9950-**************-a265c9a11073" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.27bd0649-625a-4578-880a-7a3b02e51071</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.c29e9950-**************-a265c9a11073</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="5adc3756-ff32-42d2-8c84-b1322afc45d2">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="bc18c354-9af5-4818-87ce-17628f8da105" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>44c8a0b9-35fe-41fd-83f9-c045f3e28d2b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>308a7d21-6566-4d4c-8a21-813fb30b6438</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>988c413d-535c-4478-8c1e-a72f5de9d8a5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>67606be1-367f-47e3-8707-e69eac6ccdcd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c46353db-e72b-46f4-859f-426d9e5070d9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>13ef34bf-9380-426f-8a20-a154bf30620d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>84e493c6-0082-4ffb-842e-04a0eeeabc74</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>67a25a6f-df62-498d-8f38-f30fd6faf86d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b39dcde2-62c1-4460-80df-24e334d595ed</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9aa3d732-7854-4b98-88a8-97f484e035c8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0c41f441-a380-4897-8d44-814566986590</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="44c8a0b9-35fe-41fd-83f9-c045f3e28d2b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="0" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.03b9bef5-df02-48df-8f6b-224718613a70</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="308a7d21-6566-4d4c-8a21-813fb30b6438">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="750" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:b86efb8caf3cdf8f:7a8905be:18a41c3c83f:53c5</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e9d84465-adca-41d0-85fb-62634dbca12b</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="44c8a0b9-35fe-41fd-83f9-c045f3e28d2b" targetRef="67a25a6f-df62-498d-8f38-f30fd6faf86d" name="To Map to private variables" id="2027.03b9bef5-df02-48df-8f6b-224718613a70">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.3875c748-15fc-40ef-bef1-ea4d905d7f75" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Query BC contract" id="988c413d-535c-4478-8c1e-a72f5de9d8a5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="169" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>49c94340-3e71-4b42-8cf4-35f65cadeddc</ns16:incoming>
                        
                        
                        <ns16:outgoing>10296623-cbf6-490c-804a-f35705185f7d</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.42effea6-8d8b-437c-958a-da5cf9119674</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8f786b77-ae66-4547-a444-d6ccb8969c42</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.ContractDetails.parentRequestNo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC Creation / Amendment Process Details"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">//tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4ff12b5f-6b69-4504-a730-046ee5c2000a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">//tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">//tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.004a9996-2e6b-4d60-821e-5db8e4ba2271</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.7c34490a-c8bf-41cd-b6e8-f39738896a16">tw.local.queryBCContractResults</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.95835270-515f-4794-a207-a5e2aa301c0e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ee7811d9-22b1-4727-9148-bcac74c306af</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="988c413d-535c-4478-8c1e-a72f5de9d8a5" targetRef="67606be1-367f-47e3-8707-e69eac6ccdcd" name="To initialize odc object" id="10296623-cbf6-490c-804a-f35705185f7d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="67606be1-367f-47e3-8707-e69eac6ccdcd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="521" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b1f1f049-a91b-4d4f-8789-ab5e8f012323</ns16:incoming>
                        
                        
                        <ns16:incoming>10296623-cbf6-490c-804a-f35705185f7d</ns16:incoming>
                        
                        
                        <ns16:outgoing>e9d84465-adca-41d0-85fb-62634dbca12b</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.queryBCContractResults != null)&#xD;
{&#xD;
	tw.local.queryBCContractResults.BaseDate;&#xD;
	tw.local.ContractDetails.requestDate= new TWDate();//tw.local.queryBCContractResults.BaseDate;&#xD;
	tw.local.ContractDetails.ImporterName= "Webex";//&#xD;
	&#xD;
	tw.local.ContractDetails.requestType = tw.local.requestType;&#xD;
	tw.local.ContractDetails.requestNature = tw.local.requestNature;&#xD;
	tw.local.ContractDetails.parentRequestNo = tw.local.parentRequestNumber;&#xD;
	tw.local.ContractDetails.CustomerInfo = tw.local.customerInfo;&#xD;
	&#xD;
	tw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();&#xD;
	tw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;&#xD;
	tw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;&#xD;
	tw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;&#xD;
	tw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;&#xD;
	tw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);&#xD;
	tw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);&#xD;
	tw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);&#xD;
	tw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;&#xD;
	tw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;&#xD;
	tw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);&#xD;
	tw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);&#xD;
	tw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;&#xD;
	tw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);&#xD;
	&#xD;
	tw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;&#xD;
	&#xD;
	for(var i=0;i&lt;tw.local.queryBCContractResults.Contract_Parties.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "COLLECTING BANK")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
			tw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWEE")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();&#xD;
			tw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWER")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();&#xD;
			tw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
		}&#xD;
		else&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
&#xD;
	}&#xD;
	&#xD;
	if(tw.local.queryBCContractResults.Contract_Multitnr != null)&#xD;
	{&#xD;
		for(i=0;i&lt;tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)&#xD;
		{&#xD;
			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();&#xD;
			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);&#xD;
			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);&#xD;
		}&#xD;
&#xD;
	}&#xD;
	&#xD;
	if(tw.local.queryBCContractResults.Charges != null &amp;&amp; tw.local.queryBCContractResults.Charges[0] != null)&#xD;
	{&#xD;
		for(i=0;i&lt;tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)&#xD;
		{&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;&#xD;
			if(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;			&#xD;
			}&#xD;
					&#xD;
		}&#xD;
	}&#xD;
	if (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency=="")&#xD;
		tw.local.queryBCContractResults.Currency= "USD";&#xD;
		tw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;&#xD;
		tw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;		&#xD;
}&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.maxCollectionDate =new TWDate();&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.amountAdvanced = 100.6;&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.documentAmount = 400.9;&#xD;
	&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount= new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount.name = "176";&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.chargesAndCommissionsAccount.value ="1762460873245900084";&#xD;
	&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.collectionAccount= new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.FinancialDetailsBR.collectionAccount.name = "176"&#xD;
	tw.local.ContractDetails.FinancialDetailsBR.collectionAccount.value = "1762460873245900073";&#xD;
		&#xD;
	tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();&#xD;
	tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;&#xD;
	tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.BasicDetails.paymentTerms.name ="003";&#xD;
	tw.local.ContractDetails.BasicDetails.paymentTerms.value = "أجل بدون التزام";&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair(); &#xD;
	tw.local.ContractDetails.BasicDetails.exportPurpose.name ="001";&#xD;
	tw.local.ContractDetails.BasicDetails.exportPurpose.value = "تصدير سلعي";&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
	tw.local.ContractDetails.BasicDetails.productCategory.name = "INSB";&#xD;
	tw.local.ContractDetails.BasicDetails.productCategory.value = "Incoming Standby";&#xD;
	&#xD;
	tw.local.ContractDetails.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
	tw.local.ContractDetails.BasicDetails.CountryOfOrigin.name ="MQ";&#xD;
	tw.local.ContractDetails.BasicDetails.CountryOfOrigin.value ="مارتينكو";&#xD;
	tw.local.ContractDetails.BasicDetails.commodityDescription ="the product will expire in may 2025";&#xD;
		&#xD;
	tw.local.ContractDetails.BasicDetails.Bills =  new tw.object.listOf.Bills();&#xD;
	tw.local.ContractDetails.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
	tw.local.ContractDetails.BasicDetails.Bills[0].billOfLadingDate= new TWDate();&#xD;
	tw.local.ContractDetails.BasicDetails.Bills[0].billOfLadingRef= "7654321234564";&#xD;
&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice[0].invoiceNo= "34562324372";&#xD;
	tw.local.ContractDetails.BasicDetails.Invoice[0].invoiceDate=new TWDate();&#xD;
	&#xD;
	</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="67606be1-367f-47e3-8707-e69eac6ccdcd" targetRef="308a7d21-6566-4d4c-8a21-813fb30b6438" name="To End" id="e9d84465-adca-41d0-85fb-62634dbca12b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="c46353db-e72b-46f4-859f-426d9e5070d9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="375" y="167" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>&#xD;
tw.local.ajaxError = new tw.object.AjaxError();&#xD;
log.info("*====================== ODC =========================*");&#xD;
log.info("[Query BC contract -&gt; Log Error ]- START");&#xD;
log.info("Process Instance ID:" +tw.system.currentProcessInstanceID + " Error Message: " + tw.system.error.toString(true));&#xD;
&#xD;
log.info("[Query BC contract -&gt; Log Error ]- END");&#xD;
log.info("*=======================================================*");&#xD;
&#xD;
tw.local.ajaxError.errorText = tw.local.errorMessage;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8e08a19f-0ac3-436a-8cda-d5307a9798d9</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.ajaxError.errorText</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="ecac6e93-3945-4f7c-8711-7ec517147f32" eventImplId="868d5e4d-f403-40cb-8097-1e04592f09bb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="ajaxError" id="2056.0f98651a-c511-4e57-86de-8a9856405a2b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.*************-4cbb-a781-44d233d577c6" isCollection="false" name="queryBCContractResults" id="2056.17e73a67-5d7b-4d08-898c-ca06564eb485" />
                    
                    
                    <ns16:callActivity calledElement="1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e" isForCompensation="false" startQuantity="1" completionQuantity="1" name="initialize odc object" id="13ef34bf-9380-426f-8a20-a154bf30620d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="358" y="59" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b1f1f049-a91b-4d4f-8789-ab5e8f012323</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.ContractDetails</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.ContractDetails</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="13ef34bf-9380-426f-8a20-a154bf30620d" targetRef="67606be1-367f-47e3-8707-e69eac6ccdcd" name="To Map output" id="b1f1f049-a91b-4d4f-8789-ab5e8f012323">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="13ef34bf-9380-426f-8a20-a154bf30620d" parallelMultiple="false" name="Error2" id="84e493c6-0082-4ffb-842e-04a0eeeabc74">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="393" y="117" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8e08a19f-0ac3-436a-8cda-d5307a9798d9</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="74c8969c-bdd3-424f-86d9-5adf3527fe5c" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="5ac03e45-8f34-4096-89e3-9d38fcd92e74" eventImplId="a3bc2e2d-810b-4dbc-8e52-967979f8cbcd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="84e493c6-0082-4ffb-842e-04a0eeeabc74" targetRef="c46353db-e72b-46f4-859f-426d9e5070d9" name="To End Event" id="8e08a19f-0ac3-436a-8cda-d5307a9798d9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.13893f90-ef42-4ab9-862e-e8a66297a134" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.71eb30f4-df91-427d-8f8f-b1834299cfe5" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.2f253943-a7c0-48f5-8441-a6f931ab70c4" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" name="requestType" id="2056.938e0150-a0fd-42bb-869d-1aab4f8a6913" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map to private variables" id="67a25a6f-df62-498d-8f38-f30fd6faf86d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="45" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.03b9bef5-df02-48df-8f6b-224718613a70</ns16:incoming>
                        
                        
                        <ns16:outgoing>49c94340-3e71-4b42-8cf4-35f65cadeddc</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.requestType = new tw.object.NameValuePair();&#xD;
tw.local.requestType.name = "";&#xD;
tw.local.requestType.value = "";&#xD;
tw.local.requestNature = new tw.object.NameValuePair();&#xD;
tw.local.requestNature.name = "";&#xD;
tw.local.requestNature.value = "";&#xD;
tw.local.parentRequestNumber ="";&#xD;
tw.local.customerInfo = new tw.object.CustomerInfo();&#xD;
tw.local.customerInfo.customerSector = new tw.object.NameValuePair();&#xD;
tw.local.customerInfo.customerSector.name = "";&#xD;
tw.local.customerInfo.customerSector.value = "";&#xD;
tw.local.customerInfo.facilityType = new tw.object.NameValuePair();&#xD;
tw.local.customerInfo.facilityType.name = "";&#xD;
tw.local.customerInfo.facilityType.value = "";&#xD;
&#xD;
&#xD;
tw.local.requestType = tw.local.ContractDetails.requestType;&#xD;
tw.local.requestNature =tw.local.ContractDetails.requestNature;&#xD;
tw.local.parentRequestNumber= tw.local.ContractDetails.parentRequestNo;&#xD;
tw.local.customerInfo= tw.local.ContractDetails.CustomerInfo;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="67a25a6f-df62-498d-8f38-f30fd6faf86d" targetRef="988c413d-535c-4478-8c1e-a72f5de9d8a5" name="To Query BC contract" id="49c94340-3e71-4b42-8cf4-35f65cadeddc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" name="requestNature" id="2056.b15697d9-b1e3-48ea-81ed-24a29e67a4fc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">var autoObject = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.name = "";
autoObject.value = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentRequestNumber" id="2056.cc7d4757-6ae3-4d36-8d80-0d16dd763534">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a" isCollection="false" name="customerInfo" id="2056.c9ab6d6f-f87b-47a5-843c-29ebb445a910">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.CustomerInfo();
autoObject.cif = "";
autoObject.customerName = "";
autoObject.addressLine1 = "";
autoObject.addressLine2 = "";
autoObject.addressLine3 = "";
autoObject.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.customerSector.name = "";
autoObject.customerSector.value = "";
autoObject.customerType = "";
autoObject.customerNoCBE = "";
autoObject.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.facilityType.name = "";
autoObject.facilityType.value = "";
autoObject.commercialRegistrationNo = "";
autoObject.commercialRegistrationOffice = "";
autoObject.taxCardNo = "";
autoObject.importCardNo = "";
autoObject.initiationHub = "";
autoObject.country = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="988c413d-535c-4478-8c1e-a72f5de9d8a5" parallelMultiple="false" name="Error" id="b39dcde2-62c1-4460-80df-24e334d595ed">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="204" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>aa83d8cd-bb28-414a-88e3-e4b293c2f8c0</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5deb5993-2f31-488c-8fbc-91da2ba8f736" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="4f9ffccd-657f-43a5-817e-37d7650bed82" eventImplId="99853e7b-29dd-41aa-83b1-ba27e140ccd6">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exception Handling" id="9aa3d732-7854-4b98-88a8-97f484e035c8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="360" y="220" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>aa83d8cd-bb28-414a-88e3-e4b293c2f8c0</ns16:incoming>
                        
                        
                        <ns16:incoming>e150512f-46f3-4b50-8896-92be7044e79c</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Query BC contract"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="b39dcde2-62c1-4460-80df-24e334d595ed" targetRef="9aa3d732-7854-4b98-88a8-97f484e035c8" name="To Exception Handling" id="aa83d8cd-bb28-414a-88e3-e4b293c2f8c0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="67606be1-367f-47e3-8707-e69eac6ccdcd" parallelMultiple="false" name="Error1" id="0c41f441-a380-4897-8d44-814566986590">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="556" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e150512f-46f3-4b50-8896-92be7044e79c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="506bc477-4dcc-4ef6-81b1-3ad877307496" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="0e76fdd7-195b-4514-887a-51be30f0dbc3" eventImplId="448f2578-87ba-41e7-88fd-d817e4529556">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="0c41f441-a380-4897-8d44-814566986590" targetRef="9aa3d732-7854-4b98-88a8-97f484e035c8" name="To Exception Handling" id="e150512f-46f3-4b50-8896-92be7044e79c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e9d84465-adca-41d0-85fb-62634dbca12b</processLinkId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.67606be1-367f-47e3-8707-e69eac6ccdcd</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.308a7d21-6566-4d4c-8a21-813fb30b6438</toProcessItemId>
            <guid>74126837-b08f-4e88-89c3-4a4d63e3cbe6</guid>
            <versionId>2fe98c50-28b7-4eda-ba8e-db2808d36af1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.67606be1-367f-47e3-8707-e69eac6ccdcd</fromProcessItemId>
            <toProcessItemId>2025.308a7d21-6566-4d4c-8a21-813fb30b6438</toProcessItemId>
        </link>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b1f1f049-a91b-4d4f-8789-ab5e8f012323</processLinkId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.13ef34bf-9380-426f-8a20-a154bf30620d</fromProcessItemId>
            <endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940</endStateId>
            <toProcessItemId>2025.67606be1-367f-47e3-8707-e69eac6ccdcd</toProcessItemId>
            <guid>197f7eda-cfa2-4afe-992f-429b987dd17d</guid>
            <versionId>a7afb607-c006-4a08-ba41-ed013d07d0f6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.13ef34bf-9380-426f-8a20-a154bf30620d</fromProcessItemId>
            <toProcessItemId>2025.67606be1-367f-47e3-8707-e69eac6ccdcd</toProcessItemId>
        </link>
        <link name="To initialize odc object">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.10296623-cbf6-490c-804a-f35705185f7d</processLinkId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.988c413d-535c-4478-8c1e-a72f5de9d8a5</fromProcessItemId>
            <endStateId>guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754</endStateId>
            <toProcessItemId>2025.67606be1-367f-47e3-8707-e69eac6ccdcd</toProcessItemId>
            <guid>c8c8d050-423a-4d20-b6f8-55cc0911ebbd</guid>
            <versionId>a86a0e65-84af-44dc-8f51-eb079f0031e5</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.988c413d-535c-4478-8c1e-a72f5de9d8a5</fromProcessItemId>
            <toProcessItemId>2025.67606be1-367f-47e3-8707-e69eac6ccdcd</toProcessItemId>
        </link>
        <link name="To Query BC contract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.49c94340-3e71-4b42-8cf4-35f65cadeddc</processLinkId>
            <processId>1.028269d3-7705-46be-b7bb-3893e93d0240</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.67a25a6f-df62-498d-8f38-f30fd6faf86d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.988c413d-535c-4478-8c1e-a72f5de9d8a5</toProcessItemId>
            <guid>9031799d-2314-4443-ae6a-ad484169ec65</guid>
            <versionId>c56f59cb-98cc-423b-a3d1-c162aa822950</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.67a25a6f-df62-498d-8f38-f30fd6faf86d</fromProcessItemId>
            <toProcessItemId>2025.988c413d-535c-4478-8c1e-a72f5de9d8a5</toProcessItemId>
        </link>
    </process>
</teamworks>

