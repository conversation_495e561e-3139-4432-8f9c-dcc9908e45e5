{"id": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "versionId": "a4863016-bca6-415f-9407-7cd45040cbde", "name": "DC Templete", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "appinfo", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "returnReasonVIS", "tradeFoComment", "tradeFoCommentVis", "exeHubMkrComment", "exeHubMkrCommentVis", "compcheckerComment", "compcheckerCommentVIS", "disableSubmit"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": "/* Main container for tabs */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n    display: flex;\r\r\n    flex-wrap: wrap; /* Allow tabs to wrap onto multiple lines */\r\r\n    align-items: center;\r\r\n    padding: 5px;\r\r\n    border-bottom: 1px solid #ddd;\r\r\n    background-color: #f8f9fa;\r\r\n    list-style-type: none; /* Remove default list styling */\r\r\n    margin: 0;\r\r\n}\r\r\n\r\r\n/* Style each tab */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n    /*margin-right: 5px;*/\r\r\n    margin-bottom: 2px;\r\r\n    list-style-type: none; /* Ensure no bullets */\r\r\n    white-space: nowrap; /* Prevent text wrapping inside each tab */\r\r\n}\r\r\n\r\r\n/* Tab anchor styling */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n    display: block;\r\r\n    padding: 5px 5px;\r\r\n    background-color: #ffffff;\r\r\n    border: 1px solid #ddd;\r\r\n    border-radius: 3px;\r\r\n    color: #007bff;\r\r\n    text-decoration: none;\r\r\n    transition: background-color 0.3s ease, color 0.3s ease;\r\r\n    height:26px;\r\r\n}\r\r\n\r\r\n/* Active tab and hover state */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.active > a,\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a:hover {\r\r\n    background-color: #007bff;\r\r\n    color: #ffffff;\r\r\n}\r\r\n\r\r\n/* Hide the secondary dropdown menu if it exists */\r\r\nul.nav.nav-tabs-mnu.nav-tabs-simple.tabs-primary {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Make sure the first <ul> doesn't get altered by dropdown behavior */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-toggle {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-menu {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Responsive adjustments to ensure tabs wrap correctly */\r\r\n@media (max-width: 768px) {\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n        flex-direction: row;\r\r\n        flex-wrap: wrap; /* Ensure tabs wrap on small screens */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n        margin-right: 0;\r\r\n        white-space: normal; /* Allow text to wrap within tabs */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n        text-align: center; /* Center text within tabs */\r\r\n    }\r\r\n}\r\r\n/* Ensure tabs don't leave white space when removed */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.hidden {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Ensure all tabs are displayed */\r\r\n.nav-tabs > li {\r\r\n    display: block !important; /* Override any display:none */\r\r\n    /*visibility: visible !important; /* Ensure visibility */\r\r\n}"}]}, "hasDetails": true}