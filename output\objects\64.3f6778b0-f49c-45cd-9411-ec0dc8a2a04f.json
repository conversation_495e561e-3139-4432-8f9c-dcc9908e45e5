{"id": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "versionId": "a4863016-bca6-415f-9407-7cd45040cbde", "name": "DC Templete", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "appinfo", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "returnReasonVIS", "tradeFoComment", "tradeFoCommentVis", "exeHubMkrComment", "exeHubMkrCommentVis", "compcheckerComment", "compcheckerCommentVIS", "disableSubmit"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": "/* Main container for tabs */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n    display: flex;\r\r\n    flex-wrap: wrap; /* Allow tabs to wrap onto multiple lines */\r\r\n    align-items: center;\r\r\n    padding: 5px;\r\r\n    border-bottom: 1px solid #ddd;\r\r\n    background-color: #f8f9fa;\r\r\n    list-style-type: none; /* Remove default list styling */\r\r\n    margin: 0;\r\r\n}\r\r\n\r\r\n/* Style each tab */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n    /*margin-right: 5px;*/\r\r\n    margin-bottom: 2px;\r\r\n    list-style-type: none; /* Ensure no bullets */\r\r\n    white-space: nowrap; /* Prevent text wrapping inside each tab */\r\r\n}\r\r\n\r\r\n/* Tab anchor styling */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n    display: block;\r\r\n    padding: 5px 5px;\r\r\n    background-color: #ffffff;\r\r\n    border: 1px solid #ddd;\r\r\n    border-radius: 3px;\r\r\n    color: #007bff;\r\r\n    text-decoration: none;\r\r\n    transition: background-color 0.3s ease, color 0.3s ease;\r\r\n    height:26px;\r\r\n}\r\r\n\r\r\n/* Active tab and hover state */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.active > a,\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a:hover {\r\r\n    background-color: #007bff;\r\r\n    color: #ffffff;\r\r\n}\r\r\n\r\r\n/* Hide the secondary dropdown menu if it exists */\r\r\nul.nav.nav-tabs-mnu.nav-tabs-simple.tabs-primary {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Make sure the first <ul> doesn't get altered by dropdown behavior */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-toggle {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-menu {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Responsive adjustments to ensure tabs wrap correctly */\r\r\n@media (max-width: 768px) {\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n        flex-direction: row;\r\r\n        flex-wrap: wrap; /* Ensure tabs wrap on small screens */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n        margin-right: 0;\r\r\n        white-space: normal; /* Allow text to wrap within tabs */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n        text-align: center; /* Center text within tabs */\r\r\n    }\r\r\n}\r\r\n/* Ensure tabs don't leave white space when removed */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.hidden {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Ensure all tabs are displayed */\r\r\n.nav-tabs > li {\r\r\n    display: block !important; /* Override any display:none */\r\r\n    /*visibility: visible !important; /* Ensure visibility */\r\r\n}"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "name": "DC Templete", "lastModified": "1748954143619", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isTemplate": "true", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>bd85072d-7c74-4d39-8ce1-037a06009e0d</ns2:id><ns2:layoutItemId>Header_View1</ns2:layoutItemId><ns2:configData><ns2:id>9b5f4bc3-8427-4832-852a-c8f48faf98dd</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Header View</ns2:value></ns2:configData><ns2:configData><ns2:id>de5c6869-6947-4e71-8962-b6bcabb8da57</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>716e5467-6ca2-436c-8542-6799aa48ff5f</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>73596bc1-8b1a-4b42-869a-bb3dbecc84a6</ns2:id><ns2:optionName>subStatusVisibility</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.55afafe5-9321-40fd-9b9a-cbbd556a8005</ns2:viewUUID><ns2:binding>tw.businessData.appinfo</ns2:binding></ns2:layoutItem><ns2:layoutItem xsi:type=\"ns2:CustomHTML\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>d580be2c-2d87-4dc1-8759-bc34a6932204</ns2:id><ns2:layoutItemId>CustomHTML1</ns2:layoutItemId><ns2:configData><ns2:id>ebc01f54-8cf9-401c-83ee-b3a93ae88a0b</ns2:id><ns2:optionName>@customHTML.contentType</ns2:optionName><ns2:value>TEXT</ns2:value></ns2:configData></ns2:layoutItem><ns2:layoutItem xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>fe6673cd-c9c8-4352-8f2b-2a116a7db739</ns2:id><ns2:layoutItemId>GridLayoutContainer4</ns2:layoutItemId><ns2:configData><ns2:id>1cf5e062-c534-4392-8835-84a350a1a43b</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>e3ed836d-8431-481b-80ec-fdae3f7cc017</ns2:id><ns2:layoutItemId>GridLayoutContainer5</ns2:layoutItemId><ns2:configData><ns2:id>ae7c2394-eca4-4d90-8d79-8cff10979f82</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>HORIZONTAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>124b173b-a571-4d10-8953-301c7d6cd4c2</ns2:id><ns2:layoutItemId>GridLayoutCell4</ns2:layoutItemId><ns2:configData><ns2:id>049efd81-5559-4859-843d-93cc8c9f55b1</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":1},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>396d58d9-b954-46d4-81c7-c0432739407f</ns2:id><ns2:layoutItemId>GridLayoutContainer1</ns2:layoutItemId><ns2:configData><ns2:id>1e0aad31-6390-44b5-8568-10911f4e9a93</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":11},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>dfddd41d-4593-4320-8d92-e12f42d4d815</ns2:id><ns2:layoutItemId>GridLayoutContainer3</ns2:layoutItemId><ns2:configData><ns2:id>211242dc-bb7f-49b1-8f6a-8aea00b17ca9</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":11},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>HORIZONTAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>51501f53-9c8b-4682-8ad8-c8eedf9afbf3</ns2:id><ns2:layoutItemId>GridLayoutCell5</ns2:layoutItemId><ns2:configData><ns2:id>c7706d44-f248-41d5-882a-d79a986a3324</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":10},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridCellLayoutItems xsi:type=\"ns2:ContentBox\" version=\"8550\"><ns2:id>65288874-0c0e-4639-8826-1e82d0ad9be9</ns2:id><ns2:layoutItemId>ContentBox1</ns2:layoutItemId></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>e584eb66-137c-4387-803e-90cdb7def669</ns2:id><ns2:layoutItemId>GridLayoutContainer2</ns2:layoutItemId><ns2:configData><ns2:id>2bd85218-7b45-4dce-813a-3b438158dc16</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":10}]}</ns2:value></ns2:configData><ns2:direction>HORIZONTAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>ba612e02-7ff7-4851-893f-e7f9cc3daf15</ns2:id><ns2:layoutItemId>GridLayoutContainer7</ns2:layoutItemId><ns2:configData><ns2:id>e73d3a2a-3de5-40d0-8f5b-3dab06b58c68</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":5}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>aa2dd0be-9193-45ef-879c-f94824eab375</ns2:id><ns2:layoutItemId>GridLayoutContainer8</ns2:layoutItemId><ns2:configData><ns2:id>1cd96d41-f88d-46a7-80c3-a349b2a79c15</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":5}]}</ns2:value></ns2:configData><ns2:direction>HORIZONTAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>22504682-5fa4-4eb2-868e-0d762e7a556e</ns2:id><ns2:layoutItemId>GridLayoutCell8</ns2:layoutItemId><ns2:configData><ns2:id>63a11d16-c87c-450b-8cdb-27cec691a484</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":4},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>edd64fc6-86f0-4442-81d9-e0b5fe752092</ns2:id><ns2:layoutItemId>actionList</ns2:layoutItemId><ns2:configData><ns2:id>4fe9f100-6e4a-4dbf-8288-e8a6b5ba7f52</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Action</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>76d3fb63-3d19-47fc-8351-d791142f7354</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>819aac2d-543d-4f83-80bf-67cac93a235b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>a9998b2c-779a-4b6d-8336-981a8b8fa65e</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>db9f7924-823a-4005-8074-0973556d946e</ns2:id><ns2:optionName>itemList</ns2:optionName><ns2:value>tw.options.action[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>8d4f54b6-80a2-42f7-8915-2e8e3de139d4</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>569361a0-4aea-4262-8ce0-dc46166e89d2</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>3e4dc718-89b8-4d53-847e-dd0767c8cdb4</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>c19ff324-8fe2-4853-8006-327ce52d2835</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>bbf6f090-1e88-4e9c-8e2f-f8b63fbc01be</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>e823edfc-3731-4ecf-8100-3c7c1ca26395</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.options.actionConditions</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.options.selectedAction</ns2:binding></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>13fadc07-8b84-4822-8639-c33cebcff6cc</ns2:id><ns2:layoutItemId>ReturnReason</ns2:layoutItemId><ns2:configData><ns2:id>6b20c52d-822e-494a-a71a-53f096c8626f</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>2a09518f-8ada-4e96-8f5a-41bf217d07a9</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Return Reason</ns2:value></ns2:configData><ns2:configData><ns2:id>5b22360a-ac69-41ac-8257-a40a6eeb3a61</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>86ed6434-1b34-4fe6-8472-e173bd22d304</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>0c67a33c-f7de-44d4-891d-fc058ebaf700</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>me.ui.getAbsoluteName()</ns2:value></ns2:configData><ns2:configData><ns2:id>bdeefa5b-041b-422d-8907-25d65e052aec</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.returnReasonVIS</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.options.stepLog.returnReason</ns2:binding></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d2acd7f6-53dc-46f2-8926-4090a2d7f870</ns2:id><ns2:layoutItemId>Text2</ns2:layoutItemId><ns2:configData><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>56cd9c7a-629e-4b2d-8a1a-9ffcfde64caa</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Terminate Reason</ns2:value></ns2:configData><ns2:configData><ns2:id>17347a41-14b2-43fc-8d11-507d5c5bb2eb</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>10e6b0e3-925c-41bd-8977-acf77b0c7c20</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>7dffd307-1956-4f7a-8e35-27b5fe7738b2</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.terminateReasonVIS</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.options.stepLog.terminateReason</ns2:binding></ns2:gridCellLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>3ac17695-7e95-4f8a-8841-ed7806fa5b00</ns2:id><ns2:layoutItemId>GridLayoutCell2</ns2:layoutItemId><ns2:configData><ns2:id>2352c7bb-92fe-4b6e-8bf6-9d34987e7042</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":5}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>868f927b-42bc-4aba-8737-6106f0ae83ea</ns2:id><ns2:layoutItemId>complianceApproval</ns2:layoutItemId><ns2:configData><ns2:id>37b85f00-5952-433c-8ae2-30884468c26f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Need Compliance Approval</ns2:value></ns2:configData><ns2:configData><ns2:id>05b322d6-59d3-4e30-83e9-261f76708af8</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>f7916876-1f3f-474f-8e58-64edf8531af2</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.complianceApprovalVis</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>dca9bf79-fd06-4115-8e7b-539f46257eaa</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"30px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>90f35fff-5605-4a1d-8db2-2b7691571b75</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>${actionList}.setAction(); </ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>a53e9310-4afb-4fe6-8afd-1b6a19974c13</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.fffd1628-baee-44e8-b7ca-5ae48644b0be</ns2:viewUUID><ns2:binding>tw.options.complianceApproval</ns2:binding></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>2e8e65d6-3296-455d-8ff0-25479ec37027</ns2:id><ns2:layoutItemId>Text1</ns2:layoutItemId><ns2:configData><ns2:id>294b0b85-655f-4b3a-ad05-d48c1287713c</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>aba44115-a7ad-4b25-85da-5c5395beac2f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Trade Fo Comment</ns2:value></ns2:configData><ns2:configData><ns2:id>3445cd08-f5df-42a8-8378-5b0535b0c506</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>17d5d4c2-5bf3-40f3-8f6b-2ea6258972c2</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>a5d8a98e-5074-4881-8f35-99556f29c6c0</ns2:id><ns2:optionName>printOverflow</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>808b4d61-24fd-4b96-88c8-39f9c18a097b</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>7ee911cf-0aad-4b6b-8c3d-8950b5836cc6</ns2:id><ns2:optionName>height</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:configData><ns2:id>756b7cd3-4336-4562-8ef7-4c111f0f2cc5</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.tradeFoCommentVis</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.0e61869e-73fd-4401-b156-8c11adaec3f8</ns2:viewUUID><ns2:binding>tw.options.tradeFoComment</ns2:binding></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>2819829d-5e73-4524-839e-784b8d470101</ns2:id><ns2:layoutItemId>Text4</ns2:layoutItemId><ns2:configData><ns2:id>4ec24920-75c5-496c-8b1d-ebc06597c396</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>65520db7-cbbf-4596-8a37-19e9425d21a7</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Execution Hub Maker Comment</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>86c4f93d-f1a6-406a-8d05-c1947d16d663</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2b0a27d6-a766-43e2-8815-b1e8ead08a6b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>2c1ed17d-817e-488f-80d3-cd9b8a6188d7</ns2:id><ns2:optionName>printOverflow</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>69c8f306-312f-4da5-8d3f-a32e6d69e478</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>67fb8e8f-eb38-47c3-827a-adbd134bab54</ns2:id><ns2:optionName>height</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:configData><ns2:id>f0173162-e6e0-4e0b-882a-079d9444a155</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.exeHubMkrCommentVis</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.0e61869e-73fd-4401-b156-8c11adaec3f8</ns2:viewUUID><ns2:binding>tw.options.exeHubMkrComment</ns2:binding></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>b16bda84-8d03-40b0-8ffb-18c753269b03</ns2:id><ns2:layoutItemId>Text5</ns2:layoutItemId><ns2:configData><ns2:id>cc528f81-818d-483d-81b1-97b8a2ee7f65</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>27de7629-7345-4dec-834f-d44f3348ce66</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Compliance Checker Comment</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>7abe3875-b416-4dfb-8542-b5f7e9790cd0</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>2bcbe5ef-61be-4946-860e-1bb82b6f29b9</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>5b4ff5cc-9bc5-4fd7-854c-874b14b4a1d4</ns2:id><ns2:optionName>printOverflow</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>165d58cf-ae5d-4959-8d45-be6a60d1e487</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>bb25eead-4bc2-4520-8722-ce0f3c481b01</ns2:id><ns2:optionName>height</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:configData><ns2:id>734ccd70-4aeb-400b-8902-992135296fad</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.compcheckerCommentVIS</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.0e61869e-73fd-4401-b156-8c11adaec3f8</ns2:viewUUID><ns2:binding>tw.options.compcheckerComment</ns2:binding></ns2:gridCellLayoutItems></ns2:gridContainerLayoutItems></ns2:gridCellLayoutItems></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>e2a54f1e-e72f-4b2b-853f-dca14fed87fb</ns2:id><ns2:layoutItemId>GridLayoutCell3</ns2:layoutItemId><ns2:configData><ns2:id>ff28bdcb-b795-4c0e-8a03-d43b9fcbff05</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":1}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutContainer\" version=\"8550\"><ns2:id>2a21989c-ae42-4e84-80b2-3d4922dcd758</ns2:id><ns2:layoutItemId>GridLayoutContainer9</ns2:layoutItemId><ns2:configData><ns2:id>31c90a13-3856-4bd3-8b69-c46503d60e01</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":11}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>70b9b0e5-6d16-447e-8bbe-b80c337ea08b</ns2:id><ns2:layoutItemId>GridLayoutCell9</ns2:layoutItemId><ns2:configData><ns2:id>c738c7df-b6ef-4885-8835-3f53e009d91d</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":10}]}</ns2:value></ns2:configData><ns2:direction>VERTICAL</ns2:direction><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>5fa0a34b-f1dc-4906-800c-a97c603a9503</ns2:id><ns2:layoutItemId>Text3</ns2:layoutItemId><ns2:configData><ns2:id>4a039874-6963-4c86-886a-e2167007a662</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>5fe1840f-81e3-435c-8101-4a1ec3f9fd26</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Comment</ns2:value></ns2:configData><ns2:configData><ns2:id>4e5d22b9-abd7-4a4c-892b-7a19dfd6c93a</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>c2cf53e8-097f-4628-8fc1-37a8e654bfe1</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>54270f5b-2948-4336-84ca-05d31c94d542</ns2:id><ns2:optionName>printOverflow</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>81b5da13-1eaf-4107-8674-9772f4414f99</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>7bbc6d53-0b49-4b09-87cf-d362e433a3dd</ns2:id><ns2:optionName>height</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:viewUUID>64.0e61869e-73fd-4401-b156-8c11adaec3f8</ns2:viewUUID><ns2:binding>tw.options.stepLog.comment</ns2:binding></ns2:gridCellLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems><ns2:gridContainerLayoutItems xsi:type=\"ns2:GridLayoutCell\" version=\"8550\"><ns2:id>be2cafb6-c223-4fc7-809e-fd7938dc0e74</ns2:id><ns2:layoutItemId>GridLayoutCell1</ns2:layoutItemId><ns2:configData><ns2:id>91f558d3-ef2c-468c-8781-b6b2369b19fc</ns2:id><ns2:optionName>@horizontalSpan</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":10},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>a2839375-fe5c-4188-8888-5b88dc366d75</ns2:id><ns2:optionName>@horizontalAlignment</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"RIGHT\"}]}</ns2:value></ns2:configData><ns2:direction>HORIZONTAL</ns2:direction><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>1a15b3a1-**************-b90aafb5c861</ns2:id><ns2:layoutItemId>isLiquidated</ns2:layoutItemId><ns2:configData><ns2:id>6345cfb5-b1b5-48fa-8674-1aa9f5383385</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Liquidation Created</ns2:value></ns2:configData><ns2:configData><ns2:id>b5fe358d-72e7-4e64-868b-e631334e842d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>8614dbee-5e7c-4c37-877d-30de42293956</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"HIDDEN\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.fffd1628-baee-44e8-b7ca-5ae48644b0be</ns2:viewUUID><ns2:binding>tw.options.actionConditions.isLiquidated</ns2:binding></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>623252a0-d638-4d98-8cb3-40d132d9ae70</ns2:id><ns2:layoutItemId>saveState</ns2:layoutItemId><ns2:configData><ns2:id>09b4a9cf-d33f-4bd0-86fa-5818b1067b69</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Save</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>6566ca05-0e69-43b4-8d60-9b4cfd4b067c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>a6d1fb2d-845e-43bf-8e71-b99980d9f9d5</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>cb790568-a5b5-4a3d-82ad-c4385dd41715</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>B</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>95aba975-8cb9-4e89-84dd-ae1b5a5a6184</ns2:id><ns2:optionName>shapeStyle</ns2:optionName><ns2:value>R</ns2:value></ns2:configData><ns2:configData><ns2:id>55cc9e36-a88d-4b4e-850f-7bd0f62ef497</ns2:id><ns2:optionName>iconLocation</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value></ns2:configData><ns2:configData><ns2:id>068aa8c7-137f-4a31-8978-2d79d1f87cd7</ns2:id><ns2:optionName>icon</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[]}</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>1e318248-2bb5-4c28-8b97-e39cd86ccc10</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>760c5652-c984-46ce-89d3-bb743819613e</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>alert( me.ui.getAbsoluteName() )</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID></ns2:gridCellLayoutItems><ns2:gridCellLayoutItems xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>351de0b7-6c37-4db3-88cf-cd2393baae6e</ns2:id><ns2:layoutItemId>submit</ns2:layoutItemId><ns2:configData><ns2:id>b8165fc6-2bef-4600-8d17-cbbd14a62ae4</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Submit</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>7dde2648-1619-40fe-8eb1-a86ab6de6ffc</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>54a08579-6fcb-451b-8857-3c06eac16d7a</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>8846fc24-88ff-470c-88f7-d9102a70ec54</ns2:id><ns2:optionName>shapeStyle</ns2:optionName><ns2:value>R</ns2:value></ns2:configData><ns2:configData><ns2:id>0c1af7d4-71cb-46f6-8a29-1729f3979264</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>a0537c1a-6bf4-4e22-8b43-24c1b67b92ed</ns2:id><ns2:optionName>outline</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>3b870918-00ef-45a2-8f58-5a68bd769365</ns2:id><ns2:optionName>ghostMode</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>71d8c93f-ecd5-4ffe-8d1e-9b40040ab7ab</ns2:id><ns2:optionName>iconLocation</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"R\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>38404cbc-eb83-496f-89e4-4914602bbfa4</ns2:id><ns2:optionName>icon</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>cc104e69-5831-4c95-8451-1eae4fec1e35</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>7daf668e-9881-440b-8f3d-6934c183ae4c</ns2:id><ns2:optionName>eventON_CLICK</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>6deefe61-b56f-4eb9-8377-c17f27ef33bb</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>//alert( me.ui.getAbsoluteName() )</ns2:value></ns2:configData><ns2:configData><ns2:id>4269b418-05e0-4ac4-806f-9c0faf0497d5</ns2:id><ns2:optionName>eventON_BOUNDARYEVT</ns2:optionName><ns2:value>${actionList}.setAction(); </ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID><ns2:binding></ns2:binding></ns2:gridCellLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems></ns2:gridContainerLayoutItems></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "true", "loadJsFunction": "", "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": {"isNull": "true"}, "changeJsFunction": "", "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "1de9ae94-e96e-4b0b-a9dc-8cc3e2f59b9d", "versionId": "a4863016-bca6-415f-9407-7cd45040cbde", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "bindingType": {"name": "appinfo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewBindingTypeId": "65.a278377a-9724-4710-984a-b787d6179397", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "classId": "341e5684-f12d-4cf6-aac8-d2dc77d648ab/12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6", "seq": "0", "description": {"isNull": "true"}, "guid": "af964fa3-adcb-4238-b3b8-0fc64b6c9811", "versionId": "2e29cc27-c95d-49e4-b8c4-76f6d5c8229e"}, "configOption": [{"name": "stepLog", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.44af1cd5-d132-47c5-ba78-11e19d7d7edf", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "0", "description": "", "groupName": "", "guid": "9372713b-8958-43af-ad6b-5157f4271873", "versionId": "7a526bc8-bca0-4541-aca0-2982cf0a7b06"}, {"name": "action", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.5fed2b8c-68dd-471b-81cd-113c1d580c3e", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "1", "description": "", "groupName": "", "guid": "68e17316-da1d-4b1f-8892-44ab55783b7e", "versionId": "11630f56-4e67-4e5b-a388-8ba5ab9bce35"}, {"name": "selectedAction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.211201fc-be1c-48cf-a7e7-ba0e08097527", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "2", "description": "", "groupName": "", "guid": "c29aa599-7591-470f-aa3e-713617ef8e82", "versionId": "67e81d09-7072-4374-9c94-c6656a496c9f"}, {"name": "complianceApproval", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.edd6ef26-d4d6-45f8-9e98-118c23c65974", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "3", "description": "", "groupName": "", "guid": "76379fce-2cfb-47c7-bece-4b9bfdb16821", "versionId": "b133e2f6-fadf-44a1-8e32-3ccc66245b9e"}, {"name": "complianceApprovalVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.19090976-0ac1-4ce7-b322-edca722ada00", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "4", "description": "", "groupName": "", "guid": "27ccd8ad-824e-417f-a00f-d320784c866e", "versionId": "22b0c3d9-02cf-47cf-9aa4-1029c03c4ef1"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.89030d45-dd39-4cad-b6a7-d749e38d3cdc", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "5", "description": "", "groupName": "", "guid": "65197f99-f612-45cf-9f63-8a6d9556e694", "versionId": "c2def6a8-f329-4fbf-b00c-33f98c8f1895"}, {"name": "terminateReasonVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.aeacaa5d-0c93-4747-972e-09a49025de74", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "6", "description": "", "groupName": "", "guid": "1e642dbd-e85a-40c0-bf3a-ee131dc18cae", "versionId": "5483f728-2bc8-4991-a9e5-3eb6f70bd690"}, {"name": "errorPanelVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.f5ef53b0-b6df-4db2-9f7b-2b44db251720", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "7", "description": "", "groupName": "", "guid": "8809e0da-9a1d-4d03-9d37-3de3c5987153", "versionId": "7fec6e17-b938-4042-98c4-0016ec21695a"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.5df9b584-9924-4086-83ee-c0b9cbda73db", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "8", "description": "", "groupName": "", "guid": "9cb2ef47-adbd-4fda-9a87-9f33f94c3cad", "versionId": "45898804-9ed3-482b-88af-a35427d80095"}, {"name": "returnReasonVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.3fecbbf2-a3f7-48a8-b8f5-711f9c6142b9", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "9", "description": "", "groupName": "", "guid": "5f30f90e-4595-4a5e-81ae-4bb1bd1092cc", "versionId": "a9a31d6e-8b1a-4712-a342-ea2325914ef4"}, {"name": "tradeFoComment", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.26c6c110-dfc0-428e-85f7-e894ba21d52e", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "10", "description": "", "groupName": "", "guid": "a49a45e6-cb22-4a23-a61f-cda8e03531b0", "versionId": "04bf0794-a191-4018-a225-52efbc7e144f"}, {"name": "tradeFoCommentVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.44aba9c0-2938-484f-ba24-f789f2fe2b30", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "11", "description": "", "groupName": "", "guid": "fbf2af97-b9fc-472a-be78-f1b775b4cbc0", "versionId": "c66b06aa-d4a1-4ea7-a0b9-4152ce4c0f37"}, {"name": "exeHubMkrComment", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.8ae3669b-087b-47c1-8b15-9c323204f216", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "12", "description": "", "groupName": "", "guid": "8c6e6789-6f71-4e2e-9974-f479ee441141", "versionId": "cf7552a0-5fd4-4eed-983b-344a6245d43f"}, {"name": "exeHubMkrCommentVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.afc9e1a8-fa7d-418f-8a52-54f74520bcf3", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "13", "description": "", "groupName": "", "guid": "cf1277b4-1eb1-4c64-92a5-86108e99356e", "versionId": "dc3cdecc-bb27-430d-8269-bcaba225db44"}, {"name": "compcheckerComment", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.9b45db2b-2f0a-4e53-96db-9ba48ba77d3e", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "14", "description": "", "groupName": "", "guid": "f7769732-e305-45a3-ab9b-755bd2dffac0", "versionId": "2f5263e2-9b1a-4c2b-b538-f95d6983d0ca"}, {"name": "compcheckerCommentVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.47e3416e-eecf-4d15-8383-5044a4a36fd5", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "15", "description": "", "groupName": "", "guid": "5d06a419-da2d-4e67-a119-6dd1a6dbe44f", "versionId": "8c8da9ca-ef2b-4f06-8d93-a016f481ae96"}, {"name": "disableSubmit", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.5c07b7b6-67b1-4d35-b48f-766870404a4f", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "16", "description": "", "groupName": "", "guid": "c2a89daa-af8b-4a3d-9cc6-8757e3c3b410", "versionId": "21f14305-2ceb-4ec6-81cd-d5d5330ee5f2"}], "inlineScript": [{"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.1ec7b036-dcea-4c98-b033-c4807635d0cd", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "scriptType": "JS", "scriptBlock": "\r\r\n//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};\r\r\n", "seq": "0", "description": "", "guid": "ec33e843-6f2d-498b-af37-058eff9105e5", "versionId": "2255547a-c51f-457c-a503-f7d184b46389"}, {"name": "Inline CSS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.151e60af-454b-4f0e-a95b-d2c7cf2ca5b5", "coachViewId": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "scriptType": "CSS", "scriptBlock": "/* Main container for tabs */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n    display: flex;\r\r\n    flex-wrap: wrap; /* Allow tabs to wrap onto multiple lines */\r\r\n    align-items: center;\r\r\n    padding: 5px;\r\r\n    border-bottom: 1px solid #ddd;\r\r\n    background-color: #f8f9fa;\r\r\n    list-style-type: none; /* Remove default list styling */\r\r\n    margin: 0;\r\r\n}\r\r\n\r\r\n/* Style each tab */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n    /*margin-right: 5px;*/\r\r\n    margin-bottom: 2px;\r\r\n    list-style-type: none; /* Ensure no bullets */\r\r\n    white-space: nowrap; /* Prevent text wrapping inside each tab */\r\r\n}\r\r\n\r\r\n/* Tab anchor styling */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n    display: block;\r\r\n    padding: 5px 5px;\r\r\n    background-color: #ffffff;\r\r\n    border: 1px solid #ddd;\r\r\n    border-radius: 3px;\r\r\n    color: #007bff;\r\r\n    text-decoration: none;\r\r\n    transition: background-color 0.3s ease, color 0.3s ease;\r\r\n    height:26px;\r\r\n}\r\r\n\r\r\n/* Active tab and hover state */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.active > a,\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a:hover {\r\r\n    background-color: #007bff;\r\r\n    color: #ffffff;\r\r\n}\r\r\n\r\r\n/* Hide the secondary dropdown menu if it exists */\r\r\nul.nav.nav-tabs-mnu.nav-tabs-simple.tabs-primary {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Make sure the first <ul> doesn't get altered by dropdown behavior */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-toggle {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-menu {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Responsive adjustments to ensure tabs wrap correctly */\r\r\n@media (max-width: 768px) {\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n        flex-direction: row;\r\r\n        flex-wrap: wrap; /* Ensure tabs wrap on small screens */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n        margin-right: 0;\r\r\n        white-space: normal; /* Allow text to wrap within tabs */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n        text-align: center; /* Center text within tabs */\r\r\n    }\r\r\n}\r\r\n/* Ensure tabs don't leave white space when removed */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.hidden {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Ensure all tabs are displayed */\r\r\n.nav-tabs > li {\r\r\n    display: block !important; /* Override any display:none */\r\r\n    /*visibility: visible !important; /* Ensure visibility */\r\r\n}", "seq": "1", "description": "", "guid": "0c64f21f-f918-44a3-81c0-f6c84af1c872", "versionId": "d97115a5-6d89-49a8-a457-29e148b67631"}]}}}, "hasDetails": true}