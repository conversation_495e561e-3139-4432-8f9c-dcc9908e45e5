{"id": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "versionId": "3f6df8cc-626f-4428-a80d-46cc65930974", "name": "Get terms and conditions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "name": "Get terms and conditions", "lastModified": "1698234095930", "lastModifiedBy": "fatma", "processId": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.ad0eda60-3875-409a-887e-ee7a077e432e", "2025.ad0eda60-3875-409a-887e-ee7a077e432e"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f3a", "versionId": "3f6df8cc-626f-4428-a80d-46cc65930974", "dependencySummary": "<dependencySummary id=\"bpdid:266f6f4955d8489f:7b0b9b81:18b66504d45:-5e7c\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.2f066134-1b26-4c7e-881d-df58956dc62c\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"e835a1e4-8674-4163-88cb-1c0d3661f9b1\"},{\"incoming\":[\"b6239c32-c26f-40e6-8174-47ea729ca063\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f38\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"358814d6-186a-4437-8e31-aa400b3d1f71\"},{\"targetRef\":\"ad0eda60-3875-409a-887e-ee7a077e432e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set payment instructions list\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2f066134-1b26-4c7e-881d-df58956dc62c\",\"sourceRef\":\"e835a1e4-8674-4163-88cb-1c0d3661f9b1\"},{\"startQuantity\":1,\"outgoing\":[\"b6239c32-c26f-40e6-8174-47ea729ca063\"],\"incoming\":[\"2027.2f066134-1b26-4c7e-881d-df58956dc62c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":288,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set payment instructions list\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"ad0eda60-3875-409a-887e-ee7a077e432e\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.results = new tw.object.listOf.String();\\r\\n\\r\\nif(tw.local.data == tw.epv.TermsAndConditions.paymentTerms)\\r\\n{\\r\\n\\ttw.local.results[0] = \\\"PLEASE CREDIT SAID AMOUNT TO OUR A\\/C NO. 544-7-12954HELD WITH (CHASUS33)UNDER TESTED SWIFT MESSAGE TO US(NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\\\";\\r\\n\\ttw.local.results[1] = \\\"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A\\/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\\\";\\r\\n}\\r\\nelse if(tw.local.data == tw.epv.TermsAndConditions.instructions)\\r\\n{\\r\\n\\ttw.local.results[0] = \\\"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\\\";\\r\\n\\ttw.local.results[1] = \\\"PLEASE ADVISE FATE OF DOCUMENTS AT YOUR EARLIEST CONVENIENCE UNDER TESTED SWIFT MSG.\\\";\\r\\n}\\r\\nelse if(tw.local.data == tw.epv.TermsAndConditions.specialInstructions)\\r\\n{\\r\\n\\ttw.local.results[0] = \\\"ALL YOUR CHARGES & EXPENSES IF ANY AND ALL CHARGES OUTSIDE EGYPT TO BE BORNE BY THE DRAWEE. DO NOT WAIVE. IF THE DRAWEE REFUSED TO PAY CHARGES THE DOCUMENTS MUST NOT BE DELIVERED\\\";\\r\\n\\ttw.local.results[1] = \\\"IF YOU ARE NOT IN A POSITION ALLOWING YOU TO EXECUTE OUR INSTRUCTIONS PLEASE DO NOT DELIVER DOCUMENTS TO THE DRAWEE AND INFORM US PROMPTLY, PENDING RECEIPT OUR WRITTEN INSTRUCTIONS.\\\";\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\ttw.local.results[0] = \\\"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A\\/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF.\\\";\\r\\n\\ttw.local.results[1] = \\\"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\\\";\\t\\r\\n}\\r\\n\"]}},{\"targetRef\":\"358814d6-186a-4437-8e31-aa400b3d1f71\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"b6239c32-c26f-40e6-8174-47ea729ca063\",\"sourceRef\":\"ad0eda60-3875-409a-887e-ee7a077e432e\"}],\"laneSet\":[{\"id\":\"179d730f-8cf9-48e1-8b10-ace0f96bc93d\",\"lane\":[{\"flowNodeRef\":[\"e835a1e4-8674-4163-88cb-1c0d3661f9b1\",\"358814d6-186a-4437-8e31-aa400b3d1f71\",\"ad0eda60-3875-409a-887e-ee7a077e432e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"1821e614-0bda-45d5-85a2-2dacdf950a23\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get terms and conditions\",\"declaredType\":\"process\",\"id\":\"1.ce54fd48-8513-44df-9dc5-530fe191c83f\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"results\",\"isCollection\":true,\"id\":\"2055.38336c6e-f954-46a3-8007-33a22f69718c\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.769dc134-1d15-4dd4-a967-c5f61cf352dc\",\"epvProcessLinkId\":\"c27477af-95f6-437a-83f1-eca8efa12af3\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.38336c6e-f954-46a3-8007-33a22f69718c\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\"}]},\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"data\",\"isCollection\":false,\"id\":\"2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2", "processId": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "b68a625b-058f-4619-8802-34cdeb820669", "versionId": "60af74aa-fdd2-4f78-91ca-24fa1fb73913"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.38336c6e-f954-46a3-8007-33a22f69718c", "processId": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "parameterType": "2", "isArrayOf": "true", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "476f32be-43cf-43f1-9213-0bd151f09c9a", "versionId": "e6ccdc52-2f5b-4f84-8943-528b1b568ea9"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ad0eda60-3875-409a-887e-ee7a077e432e", "processId": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "name": "Set payment instructions list", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.2cec8a59-1090-4c6c-8250-ffb6a35be5d6", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f31", "versionId": "0f03ef4c-c889-42a6-9ed6-e76521e1b8f7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "288", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.2cec8a59-1090-4c6c-8250-ffb6a35be5d6", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.results = new tw.object.listOf.String();\r\r\n\r\r\nif(tw.local.data == tw.epv.TermsAndConditions.paymentTerms)\r\r\n{\r\r\n\ttw.local.results[0] = \"PLEASE CREDIT SAID AMOUNT TO OUR A/C NO. 544-7-12954HELD WITH (CHASUS33)UNDER TESTED SWIFT MESSAGE TO US(NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\";\r\r\n\ttw.local.results[1] = \"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\";\r\r\n}\r\r\nelse if(tw.local.data == tw.epv.TermsAndConditions.instructions)\r\r\n{\r\r\n\ttw.local.results[0] = \"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\";\r\r\n\ttw.local.results[1] = \"PLEASE ADVISE FATE OF DOCUMENTS AT YOUR EARLIEST CONVENIENCE UNDER TESTED SWIFT MSG.\";\r\r\n}\r\r\nelse if(tw.local.data == tw.epv.TermsAndConditions.specialInstructions)\r\r\n{\r\r\n\ttw.local.results[0] = \"ALL YOUR CHARGES & EXPENSES IF ANY AND ALL CHARGES OUTSIDE EGYPT TO BE BORNE BY THE DRAWEE. DO NOT WAIVE. IF THE DRAWEE REFUSED TO PAY CHARGES THE DOCUMENTS MUST NOT BE DELIVERED\";\r\r\n\ttw.local.results[1] = \"IF YOU ARE NOT IN A POSITION ALLOWING YOU TO EXECUTE OUR INSTRUCTIONS PLEASE DO NOT DELIVER DOCUMENTS TO THE DRAWEE AND INFORM US PROMPTLY, PENDING RECEIPT OUR WRITTEN INSTRUCTIONS.\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.results[0] = \"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF.\";\r\r\n\ttw.local.results[1] = \"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\";\t\r\r\n}\r\r\n", "isRule": "false", "guid": "fbab90df-a978-4c73-80a1-3a1e8a43db3c", "versionId": "fd22750a-4dc5-444e-94e6-b20eb0da5902"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.358814d6-186a-4437-8e31-aa400b3d1f71", "processId": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.f5fa2d0b-1f8f-4ce8-bc82-0e1226a9203d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f38", "versionId": "8a42cde2-250f-42e9-b131-60a2bd0291ae", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.f5fa2d0b-1f8f-4ce8-bc82-0e1226a9203d", "haltProcess": "false", "guid": "534493eb-60ac-40f9-b1f5-ec705a09934e", "versionId": "592141e4-190d-4481-8312-8c898a852796"}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.5f63d5a1-3503-426b-90fa-a19663baab61", "epvId": "/21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "processId": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "guid": "499d3dd2-d193-4f74-96d7-6efad5963238", "versionId": "6e5637cd-f34a-45b3-a3cd-963fadc72f84"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get terms and conditions", "id": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:localizationResourceLinks": "", "ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "epvProcessLinkId": "c27477af-95f6-437a-83f1-eca8efa12af3"}}}, "ns16:dataInput": {"name": "data", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, "ns16:dataOutput": {"name": "results", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "true", "id": "2055.38336c6e-f954-46a3-8007-33a22f69718c"}, "ns16:inputSet": {"ns16:dataInputRefs": "2055.c7e3d08f-2e98-4735-8228-2e8bfafa7dc2"}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.38336c6e-f954-46a3-8007-33a22f69718c"}}, "ns16:laneSet": {"id": "179d730f-8cf9-48e1-8b10-ace0f96bc93d", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "1821e614-0bda-45d5-85a2-2dacdf950a23", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["e835a1e4-8674-4163-88cb-1c0d3661f9b1", "358814d6-186a-4437-8e31-aa400b3d1f71", "ad0eda60-3875-409a-887e-ee7a077e432e"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "e835a1e4-8674-4163-88cb-1c0d3661f9b1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.2f066134-1b26-4c7e-881d-df58956dc62c"}, "ns16:endEvent": {"name": "End", "id": "358814d6-186a-4437-8e31-aa400b3d1f71", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-4f38"}, "ns16:incoming": "b6239c32-c26f-40e6-8174-47ea729ca063"}, "ns16:sequenceFlow": [{"sourceRef": "e835a1e4-8674-4163-88cb-1c0d3661f9b1", "targetRef": "ad0eda60-3875-409a-887e-ee7a077e432e", "name": "To Set payment instructions list", "id": "2027.2f066134-1b26-4c7e-881d-df58956dc62c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "ad0eda60-3875-409a-887e-ee7a077e432e", "targetRef": "358814d6-186a-4437-8e31-aa400b3d1f71", "name": "To End", "id": "b6239c32-c26f-40e6-8174-47ea729ca063", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Set payment instructions list", "id": "ad0eda60-3875-409a-887e-ee7a077e432e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "288", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.2f066134-1b26-4c7e-881d-df58956dc62c", "ns16:outgoing": "b6239c32-c26f-40e6-8174-47ea729ca063", "ns16:script": "tw.local.results = new tw.object.listOf.String();\r\r\n\r\r\nif(tw.local.data == tw.epv.TermsAndConditions.paymentTerms)\r\r\n{\r\r\n\ttw.local.results[0] = \"PLEASE CREDIT SAID AMOUNT TO OUR A/C NO. 544-7-12954HELD WITH (CHASUS33)UNDER TESTED SWIFT MESSAGE TO US(NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\";\r\r\n\ttw.local.results[1] = \"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF. NO.\";\r\r\n}\r\r\nelse if(tw.local.data == tw.epv.TermsAndConditions.instructions)\r\r\n{\r\r\n\ttw.local.results[0] = \"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\";\r\r\n\ttw.local.results[1] = \"PLEASE ADVISE FATE OF DOCUMENTS AT YOUR EARLIEST CONVENIENCE UNDER TESTED SWIFT MSG.\";\r\r\n}\r\r\nelse if(tw.local.data == tw.epv.TermsAndConditions.specialInstructions)\r\r\n{\r\r\n\ttw.local.results[0] = \"ALL YOUR CHARGES & EXPENSES IF ANY AND ALL CHARGES OUTSIDE EGYPT TO BE BORNE BY THE DRAWEE. DO NOT WAIVE. IF THE DRAWEE REFUSED TO PAY CHARGES THE DOCUMENTS MUST NOT BE DELIVERED\";\r\r\n\ttw.local.results[1] = \"IF YOU ARE NOT IN A POSITION ALLOWING YOU TO EXECUTE OUR INSTRUCTIONS PLEASE DO NOT DELIVER DOCUMENTS TO THE DRAWEE AND INFORM US PROMPTLY, PENDING RECEIPT OUR WRITTEN INSTRUCTIONS.\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.results[0] = \"PLEASE REMIT PROCEEDS AS FOLLOWS ( BY USING SWIFT MT S202 )PLS. CREDIT OUR CAIRO H.O. A/C NO NO 544-7-12954 HELD WITH CHASUS33XXX UNDER SWIFT MESSAGE TO US( NBEGEGCX599) QUOTING OUR ABOVE MENTIONED REF.\";\r\r\n\ttw.local.results[1] = \"PRESENT DOCUMENTS IMMEDIATELY ON RECEIPT, UNDER SWIFT ACKNOWLEDGEMENT ADVICE TO US.\";\t\r\r\n}\r\r\n"}}}}, "link": {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.b6239c32-c26f-40e6-8174-47ea729ca063", "processId": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ad0eda60-3875-409a-887e-ee7a077e432e", "2025.ad0eda60-3875-409a-887e-ee7a077e432e"], "endStateId": "Out", "toProcessItemId": ["2025.358814d6-186a-4437-8e31-aa400b3d1f71", "2025.358814d6-186a-4437-8e31-aa400b3d1f71"], "guid": "c993fafc-0884-4c0e-9ed5-768e76ad2c8f", "versionId": "07cdbab1-fdca-4884-82d2-4f08ab478c5e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}}}}, "subType": "12", "hasDetails": false}