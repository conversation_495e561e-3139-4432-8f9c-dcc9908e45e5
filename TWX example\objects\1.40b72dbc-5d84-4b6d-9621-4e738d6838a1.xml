<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.40b72dbc-5d84-4b6d-9621-4e738d6838a1" name="Audit ODC Update Request">
        <lastModified>1699959135179</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.85507d45-497f-4e10-882d-1ec8e596ca09</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
        <errorHandlerItemId>2025.a0b81870-2a9b-41e4-8bf8-790372e330a7</errorHandlerItemId>
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-63e9</guid>
        <versionId>53c70d0a-2e2e-4c55-9973-e0c4eacf2f52</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:a6be0630e884ccca:-427a0b52:18bcdaa728b:-7fde" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.b571257f-a45f-4951-8b8c-03951d4d4ac5"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":65,"y":140,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"9a18130b-2f01-4175-863a-8c8674865b5b"},{"incoming":["e6129011-e4a8-4461-8c30-2e5dd272e435","4056e234-3962-4f2d-8ca6-55ebdbff22dc","8e89186d-9538-4022-81e4-f8d4365fd5a5"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":747,"y":140,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-63e7"],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.requestNo +\" - ServiceName : Audit ODC Update Request : END\");"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"*************-4bfe-831a-7271b9ba6410"},{"targetRef":"85507d45-497f-4e10-882d-1ec8e596ca09","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Test Data","declaredType":"sequenceFlow","id":"2027.b571257f-a45f-4951-8b8c-03951d4d4ac5","sourceRef":"9a18130b-2f01-4175-863a-8c8674865b5b"},{"startQuantity":1,"outgoing":["405c2d3d-6745-403b-8372-8008b6d137e5"],"incoming":["d646517b-cff4-4bd9-8142-edf109d1d04e"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":519,"y":117,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.requestNo +\" - ServiceName : Audit ODC Update Request 'Insertion Path' : START\");\r\n\r\n"]},"name":"Insert ODC Collection Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"870eed50-5519-45dd-8458-a26c3ae7b317","scriptFormat":"text\/x-javascript","script":{"content":["\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\ntw.local.sql = \"INSERT INTO \"+ tw.env.DBSchema + \".ODC_RequestInfo\"+\r\n               \"(liqAmount, liqCurrency, isLiquidated ,requestNo, requestNature , requestType, requestDate, requestState,requestStatus, parentRequestNo, REVERSALREASON,CLOSUREREASON,SUBSTATUS,BPMINSTANCENUMBER,\"\r\n             +\"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT\"\r\n             +\")\"\r\n \t\t +  \" VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)\";\r\n\r\n\r\n\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqAmount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqCurrency));\r\n\r\nvar liquidated= \"0\";\r\nif(tw.local.odcRequest.isLiquidated)\r\n\tliquidated=\"1\";\r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(liquidated));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.reversalReason));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.closureReason));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \r\n \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitValueDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditValueDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitedAccountNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balanceSign)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balance)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.standardExRate));  \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.negotiatedExRate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount));       \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));  "]}},{"targetRef":"f70b0f32-cf82-4eef-8cad-bbc832079442","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Insert SQL Statement","declaredType":"sequenceFlow","id":"405c2d3d-6745-403b-8372-8008b6d137e5","sourceRef":"870eed50-5519-45dd-8458-a26c3ae7b317"},{"startQuantity":1,"outgoing":["e6129011-e4a8-4461-8c30-2e5dd272e435"],"incoming":["405c2d3d-6745-403b-8372-8008b6d137e5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":639,"y":117,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Insert SQL Statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"f70b0f32-cf82-4eef-8cad-bbc832079442","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"*************-4bfe-831a-7271b9ba6410","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e6129011-e4a8-4461-8c30-2e5dd272e435","sourceRef":"f70b0f32-cf82-4eef-8cad-bbc832079442"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"sqlParameters","isCollection":true,"declaredType":"dataObject","id":"2056.9c30e79e-456d-4079-89de-46a1e3d0ee5e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.e2c9b311-b10a-4560-8720-9c4982e46a89"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.cb7e8e85-f5c8-4a57-886d-bb4d2aadf88e"},{"startQuantity":1,"outgoing":["4056e234-3962-4f2d-8ca6-55ebdbff22dc"],"incoming":["cf660fe3-32de-4be2-876e-fa15a45770c3"],"extensionElements":{"postAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.requestNo +\" - ServiceName : Audit ODC Update Request : EXP Handling - Post\");\r\n"],"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":637,"y":296,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exp Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Audit ODC Update Request\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a0b81870-2a9b-41e4-8bf8-790372e330a7","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"parallelMultiple":false,"outgoing":["cf660fe3-32de-4be2-876e-fa15a45770c3"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"1c6e9379-89a2-406d-83e6-c18c2b56dde2","otherAttributes":{"eventImplId":"9acb1dc4-b9c0-4410-86de-282ba90a4c4c"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":557,"y":319,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error Event","declaredType":"intermediateCatchEvent","id":"a2810c7a-4596-429c-82ef-ceab756af304"},{"targetRef":"a0b81870-2a9b-41e4-8bf8-790372e330a7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exp Handling","declaredType":"sequenceFlow","id":"cf660fe3-32de-4be2-876e-fa15a45770c3","sourceRef":"a2810c7a-4596-429c-82ef-ceab756af304"},{"targetRef":"*************-4bfe-831a-7271b9ba6410","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"4056e234-3962-4f2d-8ca6-55ebdbff22dc","sourceRef":"a0b81870-2a9b-41e4-8bf8-790372e330a7"},{"startQuantity":1,"outgoing":["4e4f5a5a-4db6-4b8e-8bd3-90309289d189"],"incoming":["2027.b571257f-a45f-4951-8b8c-03951d4d4ac5"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":128,"y":117,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.results=[];\r\nlog.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.requestNo +\" - ServiceName : Audit ODC Update Request : START\");\r\n\r\n"]},"name":"Retrieve ODC Collection Request","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"85507d45-497f-4e10-882d-1ec8e596ca09","scriptFormat":"text\/x-javascript","script":{"content":["\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\ntw.local.sql = \"SELECT ID FROM \"+ tw.env.DBSchema + \".ODC_RequestInfo WHERE requestNo=?\";\r\n\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\n"]}},{"startQuantity":1,"outgoing":["4735712f-4d39-4b20-8342-3472d01c235c"],"incoming":["4e4f5a5a-4db6-4b8e-8bd3-90309289d189"],"extensionElements":{"postAssignmentScript":["\r\nif (!!tw.local.results &amp;&amp; tw.local.results.listLength &gt; 0 )\r\n\ttw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;\r\n\t\r\n\t "],"nodeVisualInfo":[{"width":95,"x":274,"y":117,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Execute SQL Statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["1"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"d1fd8340-7511-4f82-8791-5f26a7f70fca","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"d1fd8340-7511-4f82-8791-5f26a7f70fca","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Execute SQL Statement","declaredType":"sequenceFlow","id":"4e4f5a5a-4db6-4b8e-8bd3-90309289d189","sourceRef":"85507d45-497f-4e10-882d-1ec8e596ca09"},{"targetRef":"07344f9a-89b3-4f0d-8f16-78631dbd2c56","extensionElements":{"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set SQL Statement","declaredType":"sequenceFlow","id":"4735712f-4d39-4b20-8342-3472d01c235c","sourceRef":"d1fd8340-7511-4f82-8791-5f26a7f70fca"},{"outgoing":["d646517b-cff4-4bd9-8142-edf109d1d04e","78eed385-2a19-45eb-8328-fc838f00c36c"],"incoming":["4735712f-4d39-4b20-8342-3472d01c235c"],"default":"d646517b-cff4-4bd9-8142-edf109d1d04e","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":392,"y":136,"declaredType":"TNodeVisualInfo","height":32}]},"name":"exist record?","declaredType":"exclusiveGateway","id":"07344f9a-89b3-4f0d-8f16-78631dbd2c56"},{"targetRef":"870eed50-5519-45dd-8458-a26c3ae7b317","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"d646517b-cff4-4bd9-8142-edf109d1d04e","sourceRef":"07344f9a-89b3-4f0d-8f16-78631dbd2c56"},{"targetRef":"2cd89407-e68c-4975-86f1-7f995694896d","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestID\t  &gt;\t  0"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"78eed385-2a19-45eb-8328-fc838f00c36c","sourceRef":"07344f9a-89b3-4f0d-8f16-78631dbd2c56"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"requestID","isCollection":false,"declaredType":"dataObject","id":"2056.7177a8d0-c523-4b3a-8ab9-839d0f9a6560"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.0b5ff817-323c-47d6-8fa0-758de07a705b"},{"startQuantity":1,"outgoing":["ead7e3d2-b956-440b-82f7-95c20ac74602"],"incoming":["78eed385-2a19-45eb-8328-fc838f00c36c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":510,"y":204,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.requestNo +\" - ServiceName : Audit ODC Update Request 'Update Path' : START\");\r\n\r\n"]},"name":"Update Odc Collection Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2cd89407-e68c-4975-86f1-7f995694896d","scriptFormat":"text\/x-javascript","script":{"content":["\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\ntw.local.sql= \"UPDATE \"+ tw.env.DBSchema +  \".ODC_RequestInfo SET \"\r\n\t\t+ \"(liqAmount, liqCurrency, isLiquidated , requestNature , requestType, requestDate, requestState,requestStatus, parentRequestNo, REVERSALREASON,CLOSUREREASON,SUBSTATUS,BPMINSTANCENUMBER,\"\r\n            +\"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT\"\r\n            + \" ) \"\r\n\t\t+\" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)\"\r\n\t\t+\" WHERE requestNo = ? \";\r\n\r\n\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqAmount));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqCurrency));\r\n\r\nvar liquidated= \"0\";\r\nif(tw.local.odcRequest.isLiquidated)\r\n\tliquidated=\"1\";\r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(liquidated));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.reversalReason));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.closureReason));\r\n  \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID));  \r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitValueDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditValueDate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitedAccountNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balanceSign)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.name));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balance)); \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.standardExRate));  \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.negotiatedExRate));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount));       \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));  \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));"]}},{"startQuantity":1,"outgoing":["8e89186d-9538-4022-81e4-f8d4365fd5a5"],"incoming":["ead7e3d2-b956-440b-82f7-95c20ac74602"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":643,"y":204,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Update Execute SQL Statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1997bd77-d790-4d6c-8231-8b78e5fdc080","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"1997bd77-d790-4d6c-8231-8b78e5fdc080","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Update Execute SQL Statement","declaredType":"sequenceFlow","id":"ead7e3d2-b956-440b-82f7-95c20ac74602","sourceRef":"2cd89407-e68c-4975-86f1-7f995694896d"},{"targetRef":"*************-4bfe-831a-7271b9ba6410","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8e89186d-9538-4022-81e4-f8d4365fd5a5","sourceRef":"1997bd77-d790-4d6c-8231-8b78e5fdc080"}],"laneSet":[{"id":"7ad61c3f-d4b0-43bb-8d4e-36a6b1034b31","lane":[{"flowNodeRef":["9a18130b-2f01-4175-863a-8c8674865b5b","*************-4bfe-831a-7271b9ba6410","870eed50-5519-45dd-8458-a26c3ae7b317","f70b0f32-cf82-4eef-8cad-bbc832079442","a0b81870-2a9b-41e4-8bf8-790372e330a7","a2810c7a-4596-429c-82ef-ceab756af304","85507d45-497f-4e10-882d-1ec8e596ca09","d1fd8340-7511-4f82-8791-5f26a7f70fca","07344f9a-89b3-4f0d-8f16-78631dbd2c56","2cd89407-e68c-4975-86f1-7f995694896d","1997bd77-d790-4d6c-8231-8b78e5fdc080"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"fd510162-c543-4d12-803d-da500fb9c3b5","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Audit ODC Update Request","declaredType":"process","id":"1.40b72dbc-5d84-4b6d-9621-4e738d6838a1","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.8d379594-e94f-4a21-8222-396c4ba9b2e1"}],"inputSet":[{"dataInputRefs":["2055.debd9766-ed8e-45c7-8bbb-c471a2567088","2055.b0be0c94-0742-4365-875f-1b01b63caf0c","2055.27a871f0-6893-4366-80d9-133f55bffddb"]}],"outputSet":[{"dataOutputRefs":["2055.8d379594-e94f-4a21-8222-396c4ba9b2e1"]}],"dataInput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.debd9766-ed8e-45c7-8bbb-c471a2567088"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"reversalReason","isCollection":false,"id":"2055.b0be0c94-0742-4365-875f-1b01b63caf0c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"closureReason","isCollection":false,"id":"2055.27a871f0-6893-4366-80d9-133f55bffddb"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.debd9766-ed8e-45c7-8bbb-c471a2567088</processParameterId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f267b0cd-3bb0-477e-8b03-************</guid>
            <versionId>3c06fe24-6d5e-4528-96a0-f24bbf649a16</versionId>
        </processParameter>
        <processParameter name="reversalReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b0be0c94-0742-4365-875f-1b01b63caf0c</processParameterId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f62f0a0f-146b-46c1-9a9d-7ce3960f887f</guid>
            <versionId>def5b971-9d13-4bc2-8f05-658aa60342e6</versionId>
        </processParameter>
        <processParameter name="closureReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.27a871f0-6893-4366-80d9-133f55bffddb</processParameterId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>97e8382d-b867-4561-bf25-81d804933022</guid>
            <versionId>c45ba7ea-91b6-40c2-8dbe-80c09493f648</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8d379594-e94f-4a21-8222-396c4ba9b2e1</processParameterId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>14e023e1-8a51-4b30-a867-e346497d0af7</guid>
            <versionId>15c4101e-d750-4276-a2af-6fcb322e6959</versionId>
        </processParameter>
        <processVariable name="sqlParameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9c30e79e-456d-4079-89de-46a1e3d0ee5e</processVariableId>
            <description isNull="true" />
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cd67b199-6105-40ae-be2b-b72fd5d95046</guid>
            <versionId>addc8188-b8c2-4aae-8c17-8ddd0b989222</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e2c9b311-b10a-4560-8720-9c4982e46a89</processVariableId>
            <description isNull="true" />
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7a60ebcd-0d36-47fe-af89-d9f88bb6c37c</guid>
            <versionId>ba1cafe4-33d1-4826-ba3b-e3899a58ed0f</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cb7e8e85-f5c8-4a57-886d-bb4d2aadf88e</processVariableId>
            <description isNull="true" />
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b868932e-64ce-4f60-908f-f00d6a305584</guid>
            <versionId>932305aa-54d5-46bd-bbc6-c62c768d1227</versionId>
        </processVariable>
        <processVariable name="requestID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7177a8d0-c523-4b3a-8ab9-839d0f9a6560</processVariableId>
            <description isNull="true" />
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7a3cfad0-34e5-410b-b65f-ed0ab4541c98</guid>
            <versionId>6a5190bd-bb23-4f1d-8075-115aef5cf748</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0b5ff817-323c-47d6-8fa0-758de07a705b</processVariableId>
            <description isNull="true" />
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c329ad90-dcbd-401a-8389-c6b00134f883</guid>
            <versionId>4e3d9873-2d62-44cc-a30d-2cd889046fa3</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2cd89407-e68c-4975-86f1-7f995694896d</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>Update Odc Collection Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9db57580-14c3-4757-8ee4-128a69ab968c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:-4f21</guid>
            <versionId>295a0d30-ca63-4420-b836-36a90a3c082a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.5ed0d572-bab8-4b05-8b95-7121afb92824</processItemPrePostId>
                <processItemId>2025.2cd89407-e68c-4975-86f1-7f995694896d</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request 'Update Path' : START");&#xD;
&#xD;
</script>
                <guid>f328f201-4f2f-47be-ad95-ef0492e67693</guid>
                <versionId>12a5938f-4190-4a80-8d24-90fb08e930c3</versionId>
            </processPrePosts>
            <layoutData x="510" y="204">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9db57580-14c3-4757-8ee4-128a69ab968c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql= "UPDATE "+ tw.env.DBSchema +  ".ODC_RequestInfo SET "&#xD;
		+ "(liqAmount, liqCurrency, isLiquidated , requestNature , requestType, requestDate, requestState,requestStatus, parentRequestNo, REVERSALREASON,CLOSUREREASON,SUBSTATUS,BPMINSTANCENUMBER,"&#xD;
            +"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT"&#xD;
            + " ) "&#xD;
		+" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"&#xD;
		+" WHERE requestNo = ? ";&#xD;
&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqCurrency));&#xD;
&#xD;
var liquidated= "0";&#xD;
if(tw.local.odcRequest.isLiquidated)&#xD;
	liquidated="1";&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(liquidated));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.reversalReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.closureReason));&#xD;
  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID));  &#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitedAccountNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balanceSign)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balance)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.standardExRate));  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.negotiatedExRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount));       &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));</script>
                <isRule>false</isRule>
                <guid>e28e99b2-41c5-46bf-b27f-103b56594ff4</guid>
                <versionId>8d87e0d3-154c-4f72-8629-043ffa182c50</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a0b81870-2a9b-41e4-8bf8-790372e330a7</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>Exp Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e07dbd5a-d75e-420e-8b78-d9b2ae59ec59</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f57c3a34823ee126:-91461cc:18af0bf5eab:-5d7e</guid>
            <versionId>56a1fb6b-0ee3-4a29-90fb-9a43fcd959c4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.94d6de1a-b4e5-4349-af73-679c2d37cb4e</processItemPrePostId>
                <processItemId>2025.a0b81870-2a9b-41e4-8bf8-790372e330a7</processItemId>
                <location>2</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request : EXP Handling - Post");&#xD;
</script>
                <guid>5fa59bb8-d0da-4994-b2b9-8e189a85e034</guid>
                <versionId>6d2dc294-5c94-4b26-b849-805e8a799d24</versionId>
            </processPrePosts>
            <layoutData x="637" y="296">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e07dbd5a-d75e-420e-8b78-d9b2ae59ec59</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>e136c631-c899-48bf-9f65-c9e973c27d86</guid>
                <versionId>225fac88-d76a-4b90-adee-42f9062065ac</versionId>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.62fca347-1e93-4fe6-8bae-79b01ed15f48</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.e07dbd5a-d75e-420e-8b78-d9b2ae59ec59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d1ae9d74-898a-466c-a50b-94ce22cd8137</guid>
                    <versionId>5f0898e8-9f92-41bb-a903-cb041c7cc0a2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d952c062-f597-445b-968a-ac97478350cd</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.e07dbd5a-d75e-420e-8b78-d9b2ae59ec59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Audit ODC Update Request"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>209e0de8-549b-4500-b6ac-c7c45f15dc55</guid>
                    <versionId>d6f0aa81-b95a-4d13-943d-2bb5effb953f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c0a647da-db78-4290-a7f6-032a4e5bffbb</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.e07dbd5a-d75e-420e-8b78-d9b2ae59ec59</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f73c035e-c693-407a-804b-0b06f73a8366</guid>
                    <versionId>fe760691-**************-1bbf0c4b9a7b</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.870eed50-5519-45dd-8458-a26c3ae7b317</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>Insert ODC Collection Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bec654dc-d951-4834-860e-3a53a679bc9f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-6343</guid>
            <versionId>6cf8703d-ef9f-49db-acb7-b4965d78ec02</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.a9404be6-21fc-4474-8f93-770e972cc549</processItemPrePostId>
                <processItemId>2025.870eed50-5519-45dd-8458-a26c3ae7b317</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>fa876672-8f10-48d0-93c5-2978bc06a06b</guid>
                <versionId>17e7216e-477f-4fa0-b02a-52a363672a9c</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2742d2e5-c0f4-4ca3-9747-4cab513a1eee</processItemPrePostId>
                <processItemId>2025.870eed50-5519-45dd-8458-a26c3ae7b317</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request 'Insertion Path' : START");&#xD;
&#xD;
</script>
                <guid>d3663a4f-1f1b-4764-abfa-267151f90224</guid>
                <versionId>dd9b210f-c153-4ddd-9a7c-36bf99a368a9</versionId>
            </processPrePosts>
            <layoutData x="519" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bec654dc-d951-4834-860e-3a53a679bc9f</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql = "INSERT INTO "+ tw.env.DBSchema + ".ODC_RequestInfo"+&#xD;
               "(liqAmount, liqCurrency, isLiquidated ,requestNo, requestNature , requestType, requestDate, requestState,requestStatus, parentRequestNo, REVERSALREASON,CLOSUREREASON,SUBSTATUS,BPMINSTANCENUMBER,"&#xD;
             +"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT"&#xD;
             +")"&#xD;
 		 +  " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";&#xD;
&#xD;
&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqCurrency));&#xD;
&#xD;
var liquidated= "0";&#xD;
if(tw.local.odcRequest.isLiquidated)&#xD;
	liquidated="1";&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(liquidated));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.reversalReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.closureReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); &#xD;
 &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitedAccountNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balanceSign)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balance)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.standardExRate));  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.negotiatedExRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount));       &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));  </script>
                <isRule>false</isRule>
                <guid>c432ee32-8672-4978-8d15-75745de71f78</guid>
                <versionId>a6129c19-9f91-448c-9142-33d99c01d5fa</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d1fd8340-7511-4f82-8791-5f26a7f70fca</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>Execute SQL Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.58dfe14b-6258-4a40-81cd-0e5f83ba4259</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-219</guid>
            <versionId>709f8ce2-186c-4dfd-9b5a-073aae06ea41</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.dba70c2a-cb17-46ba-ba38-58820bafac0a</processItemPrePostId>
                <processItemId>2025.d1fd8340-7511-4f82-8791-5f26a7f70fca</processItemId>
                <location>2</location>
                <script>&#xD;
if (!!tw.local.results &amp;&amp; tw.local.results.listLength &gt; 0 )&#xD;
	tw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;&#xD;
	&#xD;
	 </script>
                <guid>f769ae59-5862-4cbb-be93-10bfe33ce23f</guid>
                <versionId>3fa7d50d-342e-49f6-bb26-20feee5fb970</versionId>
            </processPrePosts>
            <layoutData x="274" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.58dfe14b-6258-4a40-81cd-0e5f83ba4259</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>c8990ec3-ec31-4ae8-909a-e18030f658dd</guid>
                <versionId>526b06b6-683b-4fa5-b635-2e97465fa681</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.666a06db-608c-4853-a332-429a85193d8e</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.58dfe14b-6258-4a40-81cd-0e5f83ba4259</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>962921f0-70e0-4f97-8c7b-38237739fc03</guid>
                    <versionId>1aa8ce8f-266a-40f4-a922-98bd8de87546</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.24900308-1bf6-4cd1-8566-8398c12cef75</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.58dfe14b-6258-4a40-81cd-0e5f83ba4259</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>77d632d6-867c-4d9c-936a-1f3776a3cfe2</guid>
                    <versionId>26e368d0-139a-4b36-8f7e-5a0764c48ab8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5a377323-71f6-43eb-88c1-741958dae628</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.58dfe14b-6258-4a40-81cd-0e5f83ba4259</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>a462b973-efc5-474a-8585-81d10012cd6c</guid>
                    <versionId>44415088-4ce2-4ba9-aade-8554e326d9e3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1fef08b9-752c-46f4-90b8-3590df2271de</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.58dfe14b-6258-4a40-81cd-0e5f83ba4259</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>1c5f3afd-d72f-4398-932e-ada96d664215</guid>
                    <versionId>5146ac29-9134-450d-b5ba-84377b70ad5a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0edb0cb0-e3f8-40c0-a048-ad82dc801957</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.58dfe14b-6258-4a40-81cd-0e5f83ba4259</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e014115b-2a60-4f6b-bfff-3086e5fa217a</guid>
                    <versionId>52a8b5eb-16e9-4ba9-832d-65ee15c66b28</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.07344f9a-89b3-4f0d-8f16-78631dbd2c56</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>exist record?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.5675d0b2-f886-430e-adaf-119e101443f4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-1ec</guid>
            <versionId>7925b337-9a87-428e-968f-adaa684839ab</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="392" y="136">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.5675d0b2-f886-430e-adaf-119e101443f4</switchId>
                <guid>05920f31-982d-4a16-a50e-b111c4505de1</guid>
                <versionId>9b749923-e10e-4a7c-9d52-026e567f4dd0</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.cd3f598c-9844-47d0-91db-6fd4887d9ceb</switchConditionId>
                    <switchId>3013.5675d0b2-f886-430e-adaf-119e101443f4</switchId>
                    <seq>1</seq>
                    <endStateId>guid:a6be0630e884ccca:-427a0b52:18bcdaa728b:-7fdf</endStateId>
                    <condition>tw.local.requestID	  &gt;	  0</condition>
                    <guid>16a1a69c-5f46-46d2-941e-a9b4244787cc</guid>
                    <versionId>f6f5140a-b8d6-4dbf-bad1-ac8d5dcc34b8</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.*************-4bfe-831a-7271b9ba6410</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.bfd175f3-900d-4d55-8fdf-897ddd06efbb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-63e7</guid>
            <versionId>9aff0b2f-443e-4a63-922d-56f0cc7cf256</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.b8102c6b-855f-4ea1-b6f6-7d68649f8a25</processItemPrePostId>
                <processItemId>2025.*************-4bfe-831a-7271b9ba6410</processItemId>
                <location>1</location>
                <script>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request : END");</script>
                <guid>a66980dd-9d82-4827-9f92-b41590ab22d5</guid>
                <versionId>802464ad-70d5-49b6-b6a7-6ec03f2a871f</versionId>
            </processPrePosts>
            <layoutData x="747" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.bfd175f3-900d-4d55-8fdf-897ddd06efbb</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>afa29592-d9e9-457e-968c-4a9928fe02a7</guid>
                <versionId>55f11756-3150-405e-b44a-acc5980e9e0d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f70b0f32-cf82-4eef-8cad-bbc832079442</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>Insert SQL Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.7f752bf3-fb3f-4f97-a768-8fd862120491</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-6342</guid>
            <versionId>b9ad292f-3f64-4e86-bd81-5bd773f56f36</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="639" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.7f752bf3-fb3f-4f97-a768-8fd862120491</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>c972a5ae-01c8-4f3c-ae22-e5e79f3f7c60</guid>
                <versionId>e0900b47-09c1-41ec-90b5-8d48c4a7d8c7</versionId>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.96a7fdf9-d801-4434-a370-63ef39808ea7</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.7f752bf3-fb3f-4f97-a768-8fd862120491</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>317bc7cd-de02-4b10-9297-bd83d5afbd4d</guid>
                    <versionId>24777726-b29e-4170-a22a-4ee78019323a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4d902483-d34a-4c06-98a8-e44da9429399</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.7f752bf3-fb3f-4f97-a768-8fd862120491</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>4df2da2d-57d6-4387-9147-8008e0ed7190</guid>
                    <versionId>61297c65-ecd7-4893-8f2e-e625cdcf91a4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8132448f-10ab-4c22-ac9f-01aea198a573</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.7f752bf3-fb3f-4f97-a768-8fd862120491</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4f07991e-3885-4bb4-9054-ddc596a84088</guid>
                    <versionId>99111321-3d5a-4dc1-b190-19f9eacb744d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.885c3ded-e1ba-4c20-a270-0e8694ffe3e2</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.7f752bf3-fb3f-4f97-a768-8fd862120491</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>15073432-df10-4696-ad67-9a52926e91de</guid>
                    <versionId>a7ed1aa4-ae6a-4de6-9ad9-3c7674153745</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fcbac0be-1861-445f-bc91-9ab876472e9c</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.7f752bf3-fb3f-4f97-a768-8fd862120491</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>1fd914ad-c58d-4628-9d44-ffa7ff9f0d65</guid>
                    <versionId>c966645b-31a9-4862-80e7-f0d45168d12d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1997bd77-d790-4d6c-8231-8b78e5fdc080</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>Update Execute SQL Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.5ce51eb6-fecf-4914-8b24-21be132ea947</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:-4d09</guid>
            <versionId>c2a25ee2-113a-4b67-a937-e08a8df94092</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="643" y="204">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.5ce51eb6-fecf-4914-8b24-21be132ea947</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>77743507-08b9-4cb9-bfe4-7c5fa467551c</guid>
                <versionId>b16e1830-18d6-40ed-b4a1-7ce0cf30c23c</versionId>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.12fa82e1-c1cd-41bc-9c11-80e3ffae2abb</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.5ce51eb6-fecf-4914-8b24-21be132ea947</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7512c01e-71eb-44a4-9097-52aa01b6cb82</guid>
                    <versionId>057f987e-d585-43c3-afc9-188982be7534</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3e794886-0f93-46a8-add9-8b7912bed366</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.5ce51eb6-fecf-4914-8b24-21be132ea947</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1ac5f63f-d003-4942-9514-8b2f15b6e82b</guid>
                    <versionId>1dd5f1ed-de91-41cf-a14b-cf0c2f1e5a44</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b8654909-4e52-4ec7-ab42-fc9a765473ed</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.5ce51eb6-fecf-4914-8b24-21be132ea947</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>30c8266e-81f7-450d-9f16-120e6bec63dc</guid>
                    <versionId>378be348-8042-4eea-a47b-8928510f54ff</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ccf3ccde-aef1-45ec-8f7b-616bdf02a43e</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.5ce51eb6-fecf-4914-8b24-21be132ea947</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>7b8b5e15-7dc6-4e43-a19c-5eb0ddd8838b</guid>
                    <versionId>41beb2ef-9966-4aa4-acf6-5b345cc4f0ae</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.695dde07-623b-450f-b545-2f63a2ff878f</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.5ce51eb6-fecf-4914-8b24-21be132ea947</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>50237bd6-fdb9-4175-b145-b05cd88bd3dc</guid>
                    <versionId>be7862bd-ff9a-4beb-b9e3-f50979db060c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.85507d45-497f-4e10-882d-1ec8e596ca09</processItemId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <name>Retrieve ODC Collection Request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.db0d5bc5-73e3-4335-8079-5a8beeb91e9c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-25c</guid>
            <versionId>c33ea0be-9571-487d-a6e2-ea0debc9e8e5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.c5638577-4869-42fd-8b0e-042277a9320f</processItemPrePostId>
                <processItemId>2025.85507d45-497f-4e10-882d-1ec8e596ca09</processItemId>
                <location>1</location>
                <script>tw.local.results=[];&#xD;
log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request : START");&#xD;
&#xD;
</script>
                <guid>90d83b4d-6505-4f33-90ef-0f143d04e180</guid>
                <versionId>d1138eba-9a0b-4bc7-a7e1-a38571ffdb0f</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.54470cc4-4571-4ef8-95f7-09d22e2c1bb6</processItemPrePostId>
                <processItemId>2025.85507d45-497f-4e10-882d-1ec8e596ca09</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>e1872607-05c1-4f9c-b34c-7da7c9a0bbed</guid>
                <versionId>df5d145f-7b8f-433a-b161-1ba2c49dda27</versionId>
            </processPrePosts>
            <layoutData x="128" y="117">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.db0d5bc5-73e3-4335-8079-5a8beeb91e9c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql = "SELECT ID FROM "+ tw.env.DBSchema + ".ODC_RequestInfo WHERE requestNo=?";&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
</script>
                <isRule>false</isRule>
                <guid>2079e5a3-c539-41ed-9177-69041d28f64c</guid>
                <versionId>d84029ae-84a4-43f3-b5fd-9bf69bd33afb</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.85507d45-497f-4e10-882d-1ec8e596ca09</startingProcessItemId>
        <errorHandlerItemId>2025.a0b81870-2a9b-41e4-8bf8-790372e330a7</errorHandlerItemId>
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="65" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <globalExceptionHandler>
            <layoutData x="557" y="319">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </globalExceptionHandler>
        <globalExceptionLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </globalExceptionLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Audit ODC Update Request" id="1.40b72dbc-5d84-4b6d-9621-4e738d6838a1" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.debd9766-ed8e-45c7-8bbb-c471a2567088" />
                        
                        
                        <ns16:dataInput name="reversalReason" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b0be0c94-0742-4365-875f-1b01b63caf0c" />
                        
                        
                        <ns16:dataInput name="closureReason" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.27a871f0-6893-4366-80d9-133f55bffddb" />
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.8d379594-e94f-4a21-8222-396c4ba9b2e1" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.debd9766-ed8e-45c7-8bbb-c471a2567088</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.b0be0c94-0742-4365-875f-1b01b63caf0c</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.27a871f0-6893-4366-80d9-133f55bffddb</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.8d379594-e94f-4a21-8222-396c4ba9b2e1</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="7ad61c3f-d4b0-43bb-8d4e-36a6b1034b31">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="fd510162-c543-4d12-803d-da500fb9c3b5" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>9a18130b-2f01-4175-863a-8c8674865b5b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>*************-4bfe-831a-7271b9ba6410</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>870eed50-5519-45dd-8458-a26c3ae7b317</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f70b0f32-cf82-4eef-8cad-bbc832079442</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a0b81870-2a9b-41e4-8bf8-790372e330a7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a2810c7a-4596-429c-82ef-ceab756af304</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>85507d45-497f-4e10-882d-1ec8e596ca09</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d1fd8340-7511-4f82-8791-5f26a7f70fca</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>07344f9a-89b3-4f0d-8f16-78631dbd2c56</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2cd89407-e68c-4975-86f1-7f995694896d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1997bd77-d790-4d6c-8231-8b78e5fdc080</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="9a18130b-2f01-4175-863a-8c8674865b5b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="65" y="140" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.b571257f-a45f-4951-8b8c-03951d4d4ac5</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="*************-4bfe-831a-7271b9ba6410">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="747" y="140" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-63e7</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request : END");</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e6129011-e4a8-4461-8c30-2e5dd272e435</ns16:incoming>
                        
                        
                        <ns16:incoming>4056e234-3962-4f2d-8ca6-55ebdbff22dc</ns16:incoming>
                        
                        
                        <ns16:incoming>8e89186d-9538-4022-81e4-f8d4365fd5a5</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9a18130b-2f01-4175-863a-8c8674865b5b" targetRef="85507d45-497f-4e10-882d-1ec8e596ca09" name="To Test Data" id="2027.b571257f-a45f-4951-8b8c-03951d4d4ac5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert ODC Collection Data" id="870eed50-5519-45dd-8458-a26c3ae7b317">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="519" y="117" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request 'Insertion Path' : START");&#xD;
&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d646517b-cff4-4bd9-8142-edf109d1d04e</ns16:incoming>
                        
                        
                        <ns16:outgoing>405c2d3d-6745-403b-8372-8008b6d137e5</ns16:outgoing>
                        
                        
                        <ns16:script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql = "INSERT INTO "+ tw.env.DBSchema + ".ODC_RequestInfo"+&#xD;
               "(liqAmount, liqCurrency, isLiquidated ,requestNo, requestNature , requestType, requestDate, requestState,requestStatus, parentRequestNo, REVERSALREASON,CLOSUREREASON,SUBSTATUS,BPMINSTANCENUMBER,"&#xD;
             +"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT"&#xD;
             +")"&#xD;
 		 +  " VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";&#xD;
&#xD;
&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqCurrency));&#xD;
&#xD;
var liquidated= "0";&#xD;
if(tw.local.odcRequest.isLiquidated)&#xD;
	liquidated="1";&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(liquidated));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.reversalReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.closureReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); &#xD;
 &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitedAccountNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balanceSign)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balance)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.standardExRate));  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.negotiatedExRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount));       &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));  </ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="870eed50-5519-45dd-8458-a26c3ae7b317" targetRef="f70b0f32-cf82-4eef-8cad-bbc832079442" name="To Insert SQL Statement" id="405c2d3d-6745-403b-8372-8008b6d137e5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" name="Insert SQL Statement" id="f70b0f32-cf82-4eef-8cad-bbc832079442">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="639" y="117" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>405c2d3d-6745-403b-8372-8008b6d137e5</ns16:incoming>
                        
                        
                        <ns16:outgoing>e6129011-e4a8-4461-8c30-2e5dd272e435</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="f70b0f32-cf82-4eef-8cad-bbc832079442" targetRef="*************-4bfe-831a-7271b9ba6410" name="To End" id="e6129011-e4a8-4461-8c30-2e5dd272e435">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="sqlParameters" id="2056.9c30e79e-456d-4079-89de-46a1e3d0ee5e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.e2c9b311-b10a-4560-8720-9c4982e46a89" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.cb7e8e85-f5c8-4a57-886d-bb4d2aadf88e" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exp Handling" id="a0b81870-2a9b-41e4-8bf8-790372e330a7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="637" y="296" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request : EXP Handling - Post");&#xD;
</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>cf660fe3-32de-4be2-876e-fa15a45770c3</ns16:incoming>
                        
                        
                        <ns16:outgoing>4056e234-3962-4f2d-8ca6-55ebdbff22dc</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Audit ODC Update Request"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:intermediateCatchEvent name="Error Event" id="a2810c7a-4596-429c-82ef-ceab756af304">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="557" y="319" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>cf660fe3-32de-4be2-876e-fa15a45770c3</ns16:outgoing>
                        
                        
                        <ns16:errorEventDefinition id="1c6e9379-89a2-406d-83e6-c18c2b56dde2" eventImplId="9acb1dc4-b9c0-4410-86de-282ba90a4c4c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:intermediateCatchEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a2810c7a-4596-429c-82ef-ceab756af304" targetRef="a0b81870-2a9b-41e4-8bf8-790372e330a7" name="To Exp Handling" id="cf660fe3-32de-4be2-876e-fa15a45770c3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="a0b81870-2a9b-41e4-8bf8-790372e330a7" targetRef="*************-4bfe-831a-7271b9ba6410" name="To End" id="4056e234-3962-4f2d-8ca6-55ebdbff22dc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Retrieve ODC Collection Request" id="85507d45-497f-4e10-882d-1ec8e596ca09">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="128" y="117" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                            
                            <ns3:preAssignmentScript>tw.local.results=[];&#xD;
log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request : START");&#xD;
&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.b571257f-a45f-4951-8b8c-03951d4d4ac5</ns16:incoming>
                        
                        
                        <ns16:outgoing>4e4f5a5a-4db6-4b8e-8bd3-90309289d189</ns16:outgoing>
                        
                        
                        <ns16:script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql = "SELECT ID FROM "+ tw.env.DBSchema + ".ODC_RequestInfo WHERE requestNo=?";&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Execute SQL Statement" id="d1fd8340-7511-4f82-8791-5f26a7f70fca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="274" y="117" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>&#xD;
if (!!tw.local.results &amp;&amp; tw.local.results.listLength &gt; 0 )&#xD;
	tw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;&#xD;
	&#xD;
	 </ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4e4f5a5a-4db6-4b8e-8bd3-90309289d189</ns16:incoming>
                        
                        
                        <ns16:outgoing>4735712f-4d39-4b20-8342-3472d01c235c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="85507d45-497f-4e10-882d-1ec8e596ca09" targetRef="d1fd8340-7511-4f82-8791-5f26a7f70fca" name="To Execute SQL Statement" id="4e4f5a5a-4db6-4b8e-8bd3-90309289d189">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d1fd8340-7511-4f82-8791-5f26a7f70fca" targetRef="07344f9a-89b3-4f0d-8f16-78631dbd2c56" name="To Set SQL Statement" id="4735712f-4d39-4b20-8342-3472d01c235c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="d646517b-cff4-4bd9-8142-edf109d1d04e" name="exist record?" id="07344f9a-89b3-4f0d-8f16-78631dbd2c56">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="392" y="136" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4735712f-4d39-4b20-8342-3472d01c235c</ns16:incoming>
                        
                        
                        <ns16:outgoing>d646517b-cff4-4bd9-8142-edf109d1d04e</ns16:outgoing>
                        
                        
                        <ns16:outgoing>78eed385-2a19-45eb-8328-fc838f00c36c</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="07344f9a-89b3-4f0d-8f16-78631dbd2c56" targetRef="870eed50-5519-45dd-8458-a26c3ae7b317" name="No" id="d646517b-cff4-4bd9-8142-edf109d1d04e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="07344f9a-89b3-4f0d-8f16-78631dbd2c56" targetRef="2cd89407-e68c-4975-86f1-7f995694896d" name="Yes" id="78eed385-2a19-45eb-8328-fc838f00c36c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestID	  &gt;	  0</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="requestID" id="2056.7177a8d0-c523-4b3a-8ab9-839d0f9a6560" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.0b5ff817-323c-47d6-8fa0-758de07a705b" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Update Odc Collection Data" id="2cd89407-e68c-4975-86f1-7f995694896d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="510" y="204" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>log.info("ODC -- ProcessInstance : "+tw.local.odcRequest.requestNo +" - ServiceName : Audit ODC Update Request 'Update Path' : START");&#xD;
&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>78eed385-2a19-45eb-8328-fc838f00c36c</ns16:incoming>
                        
                        
                        <ns16:outgoing>ead7e3d2-b956-440b-82f7-95c20ac74602</ns16:outgoing>
                        
                        
                        <ns16:script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql= "UPDATE "+ tw.env.DBSchema +  ".ODC_RequestInfo SET "&#xD;
		+ "(liqAmount, liqCurrency, isLiquidated , requestNature , requestType, requestDate, requestState,requestStatus, parentRequestNo, REVERSALREASON,CLOSUREREASON,SUBSTATUS,BPMINSTANCENUMBER,"&#xD;
            +"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT"&#xD;
            + " ) "&#xD;
		+" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)"&#xD;
		+" WHERE requestNo = ? ";&#xD;
&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqAmount));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.liqCurrency));&#xD;
&#xD;
var liquidated= "0";&#xD;
if(tw.local.odcRequest.isLiquidated)&#xD;
	liquidated="1";&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(liquidated));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.reversalReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.closureReason));&#xD;
  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID));  &#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditValueDate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.debitedAccountNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balanceSign)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.name));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAccount.balance)); &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.standardExRate));  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.negotiatedExRate));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount));       &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));  &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Update Execute SQL Statement" id="1997bd77-d790-4d6c-8231-8b78e5fdc080">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="643" y="204" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ead7e3d2-b956-440b-82f7-95c20ac74602</ns16:incoming>
                        
                        
                        <ns16:outgoing>8e89186d-9538-4022-81e4-f8d4365fd5a5</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="2cd89407-e68c-4975-86f1-7f995694896d" targetRef="1997bd77-d790-4d6c-8231-8b78e5fdc080" name="To Update Execute SQL Statement" id="ead7e3d2-b956-440b-82f7-95c20ac74602">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="1997bd77-d790-4d6c-8231-8b78e5fdc080" targetRef="*************-4bfe-831a-7271b9ba6410" name="To End" id="8e89186d-9538-4022-81e4-f8d4365fd5a5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Execute SQL Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4e4f5a5a-4db6-4b8e-8bd3-90309289d189</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.85507d45-497f-4e10-882d-1ec8e596ca09</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.d1fd8340-7511-4f82-8791-5f26a7f70fca</toProcessItemId>
            <guid>9955d8b7-d2dd-4156-be43-44bdb756ef06</guid>
            <versionId>03c5e7e9-f16a-4632-ac1b-7c1901325146</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.85507d45-497f-4e10-882d-1ec8e596ca09</fromProcessItemId>
            <toProcessItemId>2025.d1fd8340-7511-4f82-8791-5f26a7f70fca</toProcessItemId>
        </link>
        <link name="To Insert SQL Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.405c2d3d-6745-403b-8372-8008b6d137e5</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.870eed50-5519-45dd-8458-a26c3ae7b317</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.f70b0f32-cf82-4eef-8cad-bbc832079442</toProcessItemId>
            <guid>96147c78-5f4d-4817-99dc-310fb9bf44be</guid>
            <versionId>074fecd2-3af1-49f1-81a8-da8e8336426a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.870eed50-5519-45dd-8458-a26c3ae7b317</fromProcessItemId>
            <toProcessItemId>2025.f70b0f32-cf82-4eef-8cad-bbc832079442</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.78eed385-2a19-45eb-8328-fc838f00c36c</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.07344f9a-89b3-4f0d-8f16-78631dbd2c56</fromProcessItemId>
            <endStateId>guid:a6be0630e884ccca:-427a0b52:18bcdaa728b:-7fdf</endStateId>
            <toProcessItemId>2025.2cd89407-e68c-4975-86f1-7f995694896d</toProcessItemId>
            <guid>786f0e06-3310-48bb-99fd-ccb6130ec0f0</guid>
            <versionId>0b521ceb-86bc-4b1c-956c-0fd8577ca09f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.07344f9a-89b3-4f0d-8f16-78631dbd2c56</fromProcessItemId>
            <toProcessItemId>2025.2cd89407-e68c-4975-86f1-7f995694896d</toProcessItemId>
        </link>
        <link name="To Set SQL Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4735712f-4d39-4b20-8342-3472d01c235c</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d1fd8340-7511-4f82-8791-5f26a7f70fca</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.07344f9a-89b3-4f0d-8f16-78631dbd2c56</toProcessItemId>
            <guid>a0071ffd-bbcc-4563-a614-a190176dea9c</guid>
            <versionId>2773f999-cdf5-4897-83df-1c5453e98d4e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d1fd8340-7511-4f82-8791-5f26a7f70fca</fromProcessItemId>
            <toProcessItemId>2025.07344f9a-89b3-4f0d-8f16-78631dbd2c56</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8e89186d-9538-4022-81e4-f8d4365fd5a5</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1997bd77-d790-4d6c-8231-8b78e5fdc080</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.*************-4bfe-831a-7271b9ba6410</toProcessItemId>
            <guid>3fbc139e-e572-41db-b1a0-17256204a924</guid>
            <versionId>39e8ca75-0bf9-4459-9d2e-8c918a9fcf48</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.1997bd77-d790-4d6c-8231-8b78e5fdc080</fromProcessItemId>
            <toProcessItemId>2025.*************-4bfe-831a-7271b9ba6410</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4056e234-3962-4f2d-8ca6-55ebdbff22dc</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a0b81870-2a9b-41e4-8bf8-790372e330a7</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.*************-4bfe-831a-7271b9ba6410</toProcessItemId>
            <guid>5c213499-9540-40a4-b292-617f725e55d2</guid>
            <versionId>7d1e4365-ba08-4bd8-b3a0-fe8e7a9dc816</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.a0b81870-2a9b-41e4-8bf8-790372e330a7</fromProcessItemId>
            <toProcessItemId>2025.*************-4bfe-831a-7271b9ba6410</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d646517b-cff4-4bd9-8142-edf109d1d04e</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.07344f9a-89b3-4f0d-8f16-78631dbd2c56</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.870eed50-5519-45dd-8458-a26c3ae7b317</toProcessItemId>
            <guid>55965a89-d48b-4f6e-aa30-8203925460da</guid>
            <versionId>7db59a2c-eeb1-4513-aa3f-6f6bc63ccbc4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.07344f9a-89b3-4f0d-8f16-78631dbd2c56</fromProcessItemId>
            <toProcessItemId>2025.870eed50-5519-45dd-8458-a26c3ae7b317</toProcessItemId>
        </link>
        <link name="To Update Execute SQL Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ead7e3d2-b956-440b-82f7-95c20ac74602</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2cd89407-e68c-4975-86f1-7f995694896d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1997bd77-d790-4d6c-8231-8b78e5fdc080</toProcessItemId>
            <guid>*************-4f02-9408-be3606331b91</guid>
            <versionId>8f09de3f-bf07-4f0c-bb79-1f6b43a69acf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2cd89407-e68c-4975-86f1-7f995694896d</fromProcessItemId>
            <toProcessItemId>2025.1997bd77-d790-4d6c-8231-8b78e5fdc080</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e6129011-e4a8-4461-8c30-2e5dd272e435</processLinkId>
            <processId>1.40b72dbc-5d84-4b6d-9621-4e738d6838a1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f70b0f32-cf82-4eef-8cad-bbc832079442</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.*************-4bfe-831a-7271b9ba6410</toProcessItemId>
            <guid>3d36b52e-961c-4210-a326-3b27a7a726c4</guid>
            <versionId>bfd96b3f-9138-4cb9-8ddd-9e0a1a9db296</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f70b0f32-cf82-4eef-8cad-bbc832079442</fromProcessItemId>
            <toProcessItemId>2025.*************-4bfe-831a-7271b9ba6410</toProcessItemId>
        </link>
    </process>
</teamworks>

