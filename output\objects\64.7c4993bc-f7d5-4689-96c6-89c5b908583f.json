{"id": "64.7c4993bc-f7d5-4689-96c6-89c5b908583f", "versionId": "3e728fac-930b-4161-9fb2-c7fcac0ddd33", "name": "Start New Request CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "ODCRequest", "configOptions": ["retrieveRequestBtn", "createRequestBtn", "errorMessgae", "retrieveCustomer", "errorPnlVis", "parentrequestTypeVIS", "userConditions", "role"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "///show parent request number if request nature is update request\r\r\nthis.showParentRequestNo = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"update\"){\r\r\n\t\t//this.context.options.parentrequestTypeVIS.set(\"Editable\",  \"NONE\");\r\r\n\t\tthis.ui.get(\"updateRequestTypeSection\").setVisible(true,true);\r\r\n\t}\r\r\n\telse{\r\r\n\t\t//this.context.options.parentrequestTypeVIS.set(\"value\",  \"NONE\");\r\r\n\t\tthis.ui.get(\"updateRequestTypeSection\").setVisible(false,true);\r\r\n\t}\r\r\n}\r\r\n// hide create contract if request nature , request type , cif no and parent request no is empty\r\r\nthis.showCreateBtn = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"\" ||this.context.binding.get(\"value\").get(\"requestType\").get(\"value\") == \"\" ||this.context.binding.get(\"value\").get(\"cif\") == \"\" ||\r\r\n\t(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"update\" && this.context.binding.get(\"value\").get(\"parentRequestNo\") == \"\" ))\r\r\n\t{\r\r\n\t \tthis.ui.get(\"createRequestBtn\").setVisible(false,true);\r\r\n\t}\r\r\n\telse{\r\r\n\t\tthis.ui.get(\"createRequestBtn\").setVisible(true,true);\t\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}