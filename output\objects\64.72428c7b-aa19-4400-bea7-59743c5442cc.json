{"id": "64.72428c7b-aa19-4400-bea7-59743c5442cc", "versionId": "c79eb96d-5560-467c-84c7-7135acc836e4", "name": "Validation Helper", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"configOptions": ["runTimeValid", "stop"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "_this = this;\r\r\n\r\r\nvar displayText = \"Display_Text1\";\r\r\nconst generalTab = \"GENERAL\";\r\r\nvar tabSectionId = \"\";\r\r\nvar tabSection = \"\";\r\r\nconst errorSectionId = \"ErrorSection_Panel1\";\r\r\nconst stopVU = this.context.options.stop.get(\"value\") || false;\r\r\nconst runTime = this.context.options.runTimeValid.get(\"value\") || false;\r\r\nconst initExpanded = this.context.options.initExpanded?.get(\"value\") || true;\r\r\n\r\r\n_this.startVU = function () {\r\r\n\ttabSection = document.querySelector('[role=\"tablist\"]');\r\r\n\ttabSectionId = _this.getTabSectionId(tabSection);\r\r\n\tconst redCircles = document.querySelectorAll(\".red-circle\");\r\r\n\r\r\n\tvar viewErrorList = [];\r\r\n\tviewErrorList = bpmext.ui.getInvalidViews();\r\r\n\r\r\n\tif (viewErrorList.length == 0) {\r\r\n\t\tthis.ui.get(displayText).setText(\"\");\r\r\n\t\t_this.ui.get(errorSectionId).setVisible(false, true);\r\r\n\t\t_this.resetRedCircle(redCircles);\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tconst errMapList = viewErrorList\r\r\n\t\t.map((view) => _this.constructErrorMap(view))\r\r\n\t\t.filter(function (obj) {\r\r\n\t\t\treturn obj != null;\r\r\n\t\t});\r\r\n\r\r\n\tvar viewMapList = [];\r\r\n\t_this.resetRedCircle(redCircles);\r\r\n\tviewMapList = _this.organizeErrorsByTab(errMapList);\r\r\n\tconsole.dir(viewMapList);\r\r\n\t// Add counter red circle\r\r\n\t_this.resetRedCircle(redCircles);\r\r\n\tviewMapList.forEach((viewMap) => _this.addRedCircleToTab(viewMap));\r\r\n\r\r\n\t//Add panel with tabs and messages\r\r\n\t_this.ui.get(errorSectionId).setVisible(true, true);\r\r\n\tsetTimeout(() => {\r\r\n\t\t_this.constructValidPanel(viewMapList);\r\r\n\t}, 200);\r\r\n};\r\r\n\r\r\n_this.getTabInfoFirst = function (tabSection) {\r\r\n\tconst tabElementList = tabSection?.children;\r\r\n\tvar tabsInfo = {};\r\r\n\tfor (var i = 0; i < tabElementList.length; i++) {\r\r\n\t\tvar tabElement = tabElementList[i];\r\r\n\r\r\n\t\tvar tabInnerText = tabElement.innerText.split(\"\\n\")[0].replaceAll(\" \", \"\");\r\r\n\t\tif (!tabInnerText || tabElement.getAttribute(\"role\") !== \"tab\") continue;\r\r\n\r\r\n\t\ttabsInfo[tabInnerText] = {\r\r\n\t\t\ttabDomID: tabElement.id,\r\r\n\t\t\ttabPathId: i,\r\r\n\t\t};\r\r\n\t}\r\r\n\r\r\n\treturn tabsInfo;\r\r\n};\r\r\n\r\r\n_this.resetRedCircle = function (redCircles) {\r\r\n\t// const redCircles = document.querySelectorAll(\".red-circle\");\r\r\n\tif (!redCircles) return;\r\r\n\tredCircles.forEach((circle) => circle.remove());\r\r\n};\r\r\n\r\r\n_this.getTabSectionId = function (tabSection) {\r\r\n\tif (!tabSection) return;\r\r\n\tvar currentElement = tabSection;\r\r\n\r\r\n\twhile (currentElement && currentElement !== document.body) {\r\r\n\t\tif (currentElement.classList.contains(\"Tab_Section\")) {\r\r\n\t\t\ttabSectionId = currentElement.getAttribute(\"control-name\");\r\r\n\t\t\tbreak;\r\r\n\t\t}\r\r\n\r\r\n\t\tcurrentElement = currentElement.parentElement;\r\r\n\t}\r\r\n\r\r\n\treturn tabSectionId;\r\r\n};\r\r\n\r\r\n_this.constructValidPanel = function (viewMapList) {\r\r\n\tif (!viewMapList || viewMapList.length == 0) return;\r\r\n\tvar tabNameListHTML = ``;\r\r\n\r\r\n\tfor (var i = 0; i < viewMapList.length; i++) {\r\r\n\t\tvar tabData = viewMapList[i].tab;\r\r\n\t\tvar messageList = viewMapList[i].messages;\r\r\n\r\r\n\t\tif (!tabData) continue;\r\r\n\r\r\n\t\tvar tabDomId = tabData.domId;\r\r\n\t\tvar tabName = tabData.name || generalTab;\r\r\n\t\tvar errorListId = `error-list-${tabDomId}`;\r\r\n\t\tvar tabIndex = tabData.pathId;\r\r\n\t\tvar errorListHTML = _this.generateErrorListHTML(messageList, tabName, tabIndex);\r\r\n\r\r\n\t\ttabNameListHTML += _this.generateTabItemHTML(tabName, tabDomId, errorListId, tabIndex, errorListHTML);\r\r\n\t}\r\r\n\r\r\n\ttabNameListHTML = `<ul class=\"tab-list\">${tabNameListHTML}</ul>`;\r\r\n\t_this.ui.get(displayText).setText(tabNameListHTML);\r\r\n};\r\r\n\r\r\n_this.activateTab = function (tabDomId, tabName, tabIndex) {\r\r\n\tif (!tabName || !tabIndex) return;\r\r\n\r\r\n\tif (tabName !== generalTab) {\r\r\n\t\tpage.ui.get(tabSectionId).setCurrentPane(tabIndex);\r\r\n\t\tif (tabDomId) {\r\r\n\t\t\tvar tabElement = document.getElementById(tabDomId);\r\r\n\t\t\t_this.highLighElement(tabElement);\r\r\n\t\t}\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.activateField = function (tabName, panelString, tabIndex, fieldPathId) {\r\r\n\tconst panelList = panelString.split(\"@@\").filter((e) => e !== \"\");\r\r\n\r\r\n\tif (tabIndex && tabSectionId && tabName !== generalTab) {\r\r\n\t\tpage.ui.get(tabSectionId).setCurrentPane(tabIndex);\r\r\n\t}\r\r\n\tif (panelList && panelList.length > 0) {\r\r\n\t\tfor (let i = 0; i < panelList.length; i++) {\r\r\n\t\t\tpage.ui.get(panelList[i]).expand();\r\r\n\t\t}\r\r\n\t}\r\r\n\tif (!fieldPathId) return;\r\r\n\r\r\n\tsetTimeout(() => {\r\r\n\t\t_this.focusOnElement(fieldPathId);\r\r\n\t}, 300);\r\r\n};\r\r\n\r\r\n_this.focusOnElement = function (fieldPathId) {\r\r\n\tvar fieldElement = page.ui.get(fieldPathId).context.element;\r\r\n\t_this.highLighElement(fieldElement);\r\r\n\r\r\n\tpage.ui.get(fieldPathId).focus();\r\r\n};\r\r\n\r\r\n_this.highLighElement = function (fieldElement) {\r\r\n\tif (!fieldElement) return;\r\r\n\r\r\n\tfieldElement.classList.add(\"highlighted-field\");\r\r\n\tsetTimeout(function () {\r\r\n\t\tfieldElement.classList.remove(\"highlighted-field\");\r\r\n\t}, 1500);\r\r\n};\r\r\n\r\r\n_this.addRedCircleToTab = function (viewMap) {\r\r\n\tif (!viewMap.tab.domId) return;\r\r\n\r\r\n\tconst messagesCount = viewMap.messages.length;\r\r\n\tconst tabDomId = viewMap.tab.domId;\r\r\n\tconst tabElement = document.getElementById(tabDomId);\r\r\n\tif (!tabElement) return;\r\r\n\r\r\n\t// Combine DOM reads\r\r\n\tconst existingCircle = tabElement.querySelector(\".red-circle\");\r\r\n\tconst newCircleContent = `<div class=\"red-circle\">${messagesCount}</div>`;\r\r\n\r\r\n\t// Combine DOM writes\r\r\n\tif (!existingCircle) {\r\r\n\t\ttabElement.insertAdjacentHTML(\"beforeend\", newCircleContent);\r\r\n\t} else {\r\r\n\t\texistingCircle.innerText = messagesCount;\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.constructErrorMap = function (fieldElement) {\r\r\n\tif (!fieldElement || !fieldElement.context.element || !fieldElement._bpmextViewNode) return null;\r\r\n\r\r\n\tvar fieldDomId = fieldElement.context.element.id;\r\r\n\tvar fieldParents = _this.getFieldParents(fieldDomId);\r\r\n      // var isField = Object.keys(fieldElement._bpmextViewNode._data.context.subview).length === 0;\r\r\n      var isField = true;\r\r\n\tif (isField) {\r\r\n\t\terrorMap = {\r\r\n\t\t\tfield: {\r\r\n\t\t\t\tmessage: fieldElement._bpmextVE?.errors?.[0]?.message || \"\",\r\r\n\t\t\t\tdomId: fieldDomId,\r\r\n\t\t\t\tpathId: fieldElement.context.element.getAttribute(\"control-name\"),\r\r\n\t\t\t\tviewId: fieldElement.context.element.getAttribute(\"data-viewid\"),\r\r\n\t\t\t\tlabel: fieldElement.getLabel(),\r\r\n\t\t\t},\r\r\n\r\r\n\t\t\tpanels: fieldParents.cPanelList /*[list of \"SPARKCPanel\"]*/,\r\r\n\r\r\n\t\t\tview: fieldParents.viewObj,\r\r\n\t\t};\r\r\n\t\treturn errorMap;\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.getFieldParents = function (elementId) {\r\r\n\tvar fieldParents = {\r\r\n\t\tviewObj: {\r\r\n\t\t\tname: \"\",\r\r\n\t\t\tdomId: \"\",\r\r\n\t\t\tpathId: \"\",\r\r\n\t\t},\r\r\n\t\tcPanelList: [],\r\r\n\t};\r\r\n\tconst cPanelClass = \"Collapsible_Panel\";\r\r\n\tconst tabClass = \"tab-pane\";\r\r\n\r\r\n\tvar currentElement = document.getElementById(elementId);\r\r\n\r\r\n\twhile (currentElement && currentElement !== document.body) {\r\r\n\t\tif (currentElement.classList.contains(tabClass)) {\r\r\n\t\t\tfieldParents.viewObj.name = currentElement.getAttribute(\"aria-label\");\r\r\n\t\t\tfieldParents.viewObj.domId = currentElement.id;\r\r\n\t\t\tfieldParents.viewObj.pathId = currentElement.getAttribute(\"control-name\");\r\r\n\t\t} else if (currentElement.classList.contains(cPanelClass)) {\r\r\n\t\t\tfieldParents.cPanelList.unshift(currentElement.getAttribute(\"control-name\"));\r\r\n\t\t}\r\r\n\r\r\n\t\tcurrentElement = currentElement.parentNode;\r\r\n\t}\r\r\n\tconsole.dir(fieldParents);\r\r\n\treturn fieldParents;\r\r\n};\r\r\n\r\r\n_this.organizeErrorsByTab = function (errorList) {\r\r\n\tconst viewMap = new Map();\r\r\n\tlet tabsInfo = {};\r\r\n\r\r\n\tif (tabSection) {\r\r\n\t\ttabsInfo = _this.getTabInfoFirst(tabSection);\r\r\n\t}\r\r\n\r\r\n\terrorList.forEach((error) => {\r\r\n\t\tif (error) {\r\r\n\t\t\tconst viewName = error.view.name;\r\r\n\t\t\tconst sanitizedViewName = viewName?.replaceAll(\" \", \"\");\r\r\n\r\r\n\t\t\tif (!viewMap.has(viewName)) {\r\r\n\t\t\t\tviewMap.set(viewName, {\r\r\n\t\t\t\t\tview: {\r\r\n\t\t\t\t\t\tname: viewName,\r\r\n\t\t\t\t\t\tdomId: error.view.domId,\r\r\n\t\t\t\t\t\tpathId: error.view.pathId,\r\r\n\t\t\t\t\t},\r\r\n\t\t\t\t\tmessages: [],\r\r\n\t\t\t\t\ttab: {\r\r\n\t\t\t\t\t\tname: viewName,\r\r\n\t\t\t\t\t\tdomId: tabsInfo[sanitizedViewName]?.tabDomID,\r\r\n\t\t\t\t\t\tpathId: tabsInfo[sanitizedViewName]?.tabPathId,\r\r\n\t\t\t\t\t},\r\r\n\t\t\t\t});\r\r\n\t\t\t}\r\r\n\t\t\t// Add the error message to the corresponding tab entry\r\r\n\t\t\tconst viewEntry = viewMap.get(viewName);\r\r\n\t\t\tviewEntry.messages.push({\r\r\n\t\t\t\tmessage: error.field.message,\r\r\n\t\t\t\tfield: {\r\r\n\t\t\t\t\tdomId: error.field.domId,\r\r\n\t\t\t\t\tpathId: error.field.pathId,\r\r\n\t\t\t\t\tviewId: error.field.viewId,\r\r\n\t\t\t\t\tlabel: error.field.label,\r\r\n\t\t\t\t\tpanels: [...error.panels],\r\r\n\t\t\t\t},\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\t// Convert the map values to an array of tab objects\r\r\n\treturn [...viewMap.values()];\r\r\n};\r\r\n\r\r\n_this.generateTabItemHTML = function (tabName, tabDomId, errorListId, tabIndex, errorListHTML) {\r\r\n\tconst initialButtonText = initExpanded ? \"Hide\" : \"Show\";\r\r\n\tconst initialDisplayStyle = initExpanded ? \"block\" : \"none\";\r\r\n\treturn `<li class=\"tab-item\">\r\r\n            <div class=\"tab-container\">\r\r\n                <div class=\"tab-header\">\r\r\n                    <div class=\"gradient-box\">\r\r\n                        <a href=\"#${tabDomId}\" class=\"tab-name\"\r\r\n                            onclick=\"_this.toggleErrorList('${errorListId}'); event.preventDefault(); event.stopPropagation();\">${tabName}</a>\r\r\n                    </div>\r\r\n                </div>\r\r\n                <ul id=\"${errorListId}\" class=\"error-list\" style=\"display: ${initialDisplayStyle};\">${errorListHTML}</ul>\r\r\n            </div>\r\r\n        </li>`;\r\r\n};\r\r\n\r\r\n_this.generateErrorListHTML = function (listOfErrors, tabName, tabIndex) {\r\r\n\treturn listOfErrors\r\r\n\t\t.map(function (error) {\r\r\n\t\t\tconst fieldDomId = error.field.domId;\r\r\n\t\t\tconst fieldPathId = error.field.pathId;\r\r\n\t\t\tconst label = error.field.label;\r\r\n\t\t\tconst targetMessage = `<b>${label}</b> : ${error.message}`;\r\r\n\t\t\tconst panelString = error.field.panels.join(\"@@\");\r\r\n\r\r\n\t\t\treturn `<li><span class=\"bullet\">&#8226;</span> <a href=\"#${fieldDomId}\" class=\"message-link\" message-id=\"${fieldDomId}\" onclick=\"_this.activateField('${tabName}','${panelString}','${tabIndex}','${fieldPathId}');\">${targetMessage}</a></li>`;\r\r\n\t\t})\r\r\n\t\t.join(\"\");\r\r\n};\r\r\n\r\r\n_this.toggleErrorList = function (errorListId) {\r\r\n\tconst errorList = document.getElementById(errorListId);\r\r\n\tif (errorList) {\r\r\n\t\tconst isCollapsed = errorList.style.display === \"none\" || !errorList.style.display;\r\r\n\t\terrorList.style.display = isCollapsed ? \"block\" : \"none\";\r\r\n\t\t//   if (button) {\r\r\n\t\t//       button.textContent = isCollapsed ? \"Hide\" : \"Show\";\r\r\n\t\t//   }\r\r\n\t}\r\r\n};\r\r\n//=======================================REQUIRED===============================================//\r\r\nrequire([\"com.ibm.bpm.coach/engine\"], function (engine) {\r\r\n\tvar dve = engine._deliverValidationEvents;\r\r\n\tengine._deliverValidationEvents = function (event, viewMap, isClear) {\r\r\n\t\tdve(event, viewMap, isClear); // original processing first\r\r\n\t\t// console.log(\"_deliverValidationEvents\", event, viewMap, isClear);\r\r\n\t}.bind(engine);\r\r\n\tvar hve = engine.handleValidationEvent;\r\r\n\tengine.handleValidationEvent = function (event) {\r\r\n\t\thve(event);\r\r\n\t\t// console.log(\"handleValidationEvent\", event);\r\r\n\t\tif (!stopVU) {\r\r\n\t\t\t_this.startVU();\r\r\n\t\t}\r\r\n\t}.bind(engine);\r\r\n});\r\r\n\r\r\nvar uvvs = bpmext && bpmext.ui && bpmext.ui.updateViewValidationState;\r\r\nif (uvvs) {\r\r\n\tbpmext.ui.updateViewValidationState = function (view, event) {\r\r\n\t\tuvvs(view, event); //call original handler\r\r\n\t\t// console.log(\"updateViewValidationState\", view, event);\r\r\n\t\tif (!stopVU && runTime == true) {\r\r\n\t\t\t_this.startVU();\r\r\n\t\t}\r\r\n\t};\r\r\n}"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": "/* Style for the red circle counter */\r\r\n.red-circle {\r\r\n\tposition: absolute;\r\r\n\ttop: 0;\r\r\n\tright: 0;\r\r\n\twidth: 17px;\r\r\n\theight: 17px;\r\r\n\tbackground-color: red;\r\r\n\tborder-radius: 50%;\r\r\n\tdisplay: flex;\r\r\n\tjustify-content: center;\r\r\n\talign-items: center;\r\r\n\tcolor: white;\r\r\n\tfont-weight: bold;\r\r\n}\r\r\n\r\r\n.tab-link {\r\r\n\tfont-size: medium;\r\r\n}\r\r\n\r\r\n/* Style for the tab list */\r\r\n.tab-list {\r\r\n\tlist-style-type: none;\r\r\n\tpadding: 0;\r\r\n\tmargin: 0; /* Remove default margin */\r\r\n}\r\r\n\r\r\n/* Style for each tab item */\r\r\n.tab-item {\r\r\n\tmargin-bottom: 5px; /* Reduce space between tabs */\r\r\n\tborder: none;\r\r\n\tpadding: 5px;\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\tlist-style: none;\r\r\n}\r\r\n\r\r\n/* Style for the tab name */\r\r\n.tab-name {\r\r\n\tfont-size: 16px;\r\r\n\tmargin: 0;\r\r\n\tcolor: #8a1412; /* Replaced red with a shade of orange */\r\r\n\ttext-decoration: none;\r\r\n\tfont-weight: bold;\r\r\n}\r\r\n\r\r\n.tab-name:hover {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400;\r\r\n}\r\r\n\r\r\n.tab-name:focus {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400;\r\r\n}\r\r\n\r\r\n.tab-container {\r\r\n\tdisplay: flex;\r\r\n\tflex-direction: column;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n.tab-header {\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n/* Style for the gradient box */\r\r\n.gradient-box {\r\r\n\tflex: 1;\r\r\n\tpadding: 10px 20px;\r\r\n\tbackground: linear-gradient(45deg, rgba(255, 255, 255, 0.8), rgba(240, 240, 240, 0.8));\r\r\n\tborder-radius: 4px;\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\tcursor: auto; /* Change cursor to auto to indicate non-clickable */\r\r\n\ttransition: none;\r\r\n}\r\r\n\r\r\n/* .gradient-box:hover {\r\r\n\ttransform: scale(1.05);\r\r\n\tbackground: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(240, 240, 240, 1));\r\r\n} */\r\r\n\r\r\n.tab {\r\r\n\tposition: relative;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n.tab::after {\r\r\n\tcontent: attr(error-count);\r\r\n\tcolor: red;\r\r\n\tfont-size: 10px;\r\r\n\tposition: absolute;\r\r\n\tright: 5px;\r\r\n\ttop: 5px;\r\r\n}\r\r\n\r\r\n/* Add animation for the highlighted field */\r\r\n.highlighted-field {\r\r\n\tanimation-name: highlight;\r\r\n\tanimation-duration: 1.5s;\r\r\n}\r\r\n\r\r\n@keyframes highlight {\r\r\n\tfrom {\r\r\n\t\tbackground-color: yellow;\r\r\n\t}\r\r\n\tto {\r\r\n\t\tbackground-color: initial;\r\r\n\t}\r\r\n}\r\r\n\r\r\n.error-list {\r\r\n\tdisplay: none;\r\r\n\tmargin-left: 20px;\r\r\n\tpadding: 0;\r\r\n\tlist-style-type: none;\r\r\n\ttransition: max-height 0.2s ease-out;\r\r\n\toverflow: hidden;\r\r\n}\r\r\n\r\r\n.error-list.collapsed {\r\r\n\tmax-height: 0;\r\r\n\ttransition: max-height 0.3s ease-out;\r\r\n}\r\r\n\r\r\n.error-list.expanded {\r\r\n\tmax-height: 500px; /* Adjust this value as needed */\r\r\n\ttransition: max-height 0.3s ease-in;\r\r\n}\r\r\n\r\r\n/* Style for the toggle button */\r\r\n.toggle-button {\r\r\n\tmargin-left: 10px;\r\r\n\tbackground-color: #f7f7f7; /* Off-white background */\r\r\n\tcolor: #8a1412; /* Darker red for text */\r\r\n\tborder: 1px solid #e0e0e0; /* Off-white border */\r\r\n\tborder-radius: 4px;\r\r\n\tpadding: 5px 10px;\r\r\n\tcursor: pointer;\r\r\n\ttransition: background-color 0.3s ease, transform 0.3s ease;\r\r\n}\r\r\n\r\r\n.toggle-button:hover {\r\r\n\tbackground-color: #e8e8e8; /* Slightly darker off-white on hover */\r\r\n\ttransform: scale(1.05);\r\r\n}\r\r\n\r\r\n.bullet {\r\r\n\tcolor: #ff8c00; /* Replaced red with a shade of orange */\r\r\n\tmargin-right: 5px; /* Adjust spacing between bullet and link */\r\r\n}\r\r\n\r\r\n.message-link {\r\r\n\tcursor: pointer;\r\r\n\tcolor: #8a1412; /* Replaced red with a shade of orange */\r\r\n\ttext-decoration: none;\r\r\n\tpadding: 5px; /* Add padding for better hover area */\r\r\n\ttransition: color 0.2s ease, background-color 0.2s ease; /* Add transition for smooth animation */\r\r\n\tposition: relative;\r\r\n}\r\r\n\r\r\n.message-link:hover {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400; /* Darker shade of orange on hover */\r\r\n\t/* background-color: rgba(255, 140, 0, 0.1); */\r\r\n}"}]}, "hasDetails": true}