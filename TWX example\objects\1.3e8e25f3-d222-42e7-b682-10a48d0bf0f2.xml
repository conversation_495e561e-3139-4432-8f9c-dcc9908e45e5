<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2" name="Generate Remittance Letter">
        <lastModified>1700020717717</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-16a0</guid>
        <versionId>0f8b5d23-2ad9-4e92-a134-ff82524258aa</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f15"&gt;
  &lt;artifactReference id="bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f14"&gt;
    &lt;refId&gt;1.7f097f90-cf8f-43fa-875f-f106fe9cac70&lt;/refId&gt;
    &lt;refType&gt;1&lt;/refType&gt;
    &lt;nameValuePair id="bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f13"&gt;
      &lt;name&gt;operationId&lt;/name&gt;
      &lt;value&gt;{http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed&lt;/value&gt;
    &lt;/nameValuePair&gt;
  &lt;/artifactReference&gt;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.2521cec4-3685-4925-848f-52ab82439a5d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":209,"y":77,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"98eaadf2-a3e9-4432-8cc8-70ef8c5970c6"},{"incoming":["6f0096bd-6183-4c60-81fb-1d5ef2324a64"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1017,"y":77,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-169e"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"3a8ae9f4-a4b5-4024-863f-a0b0817b39c7"},{"targetRef":"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.2521cec4-3685-4925-848f-52ab82439a5d","sourceRef":"98eaadf2-a3e9-4432-8cc8-70ef8c5970c6"},{"outgoing":["4c44551e-24bb-4ce9-8c6c-b0e99527615b"],"incoming":["078c569a-3fac-4dfa-8c82-6d494c69d3de","e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6"],"matchAllSearchCriteria":true,"extensionElements":{"postAssignmentScript":["log.info(\" ServiceName : Get Doc Gen Temp Folder: END\");"],"nodeVisualInfo":[{"width":95,"x":505,"y":53,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\"ProcessInstance : \"+tw.system.currentProcessInstanceID +\" - ServiceName : Contract Document Generation : START\");\r\nlog.info(\" ServiceName : Get Doc Gen Temp Folder: START\");"],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_FOLDER_BY_PATH","implementation":"##WebService","serverName":"useMappingServer","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Doc Gen Temp Folder","dataInputAssociation":[{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"FileNet\""]}}]},{"targetRef":"PATH","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderPath"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"b4ca5615-841a-4b96-8f71-17df1c09d08e","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","declaredType":"TFormalExpression","content":["tw.local.docGenTempFolder"]}}],"sourceRef":["FOLDER"]}],"orderOverride":false},{"targetRef":"df1ded4c-8bfd-44d8-8f0f-b837239503ce","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Script Task1","declaredType":"sequenceFlow","id":"4c44551e-24bb-4ce9-8c6c-b0e99527615b","sourceRef":"b4ca5615-841a-4b96-8f71-17df1c09d08e"},{"startQuantity":1,"outgoing":["456da453-0ff0-48d2-8993-02260083c2ad"],"incoming":["4c44551e-24bb-4ce9-8c6c-b0e99527615b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":649,"y":54,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Prepare request","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"df1ded4c-8bfd-44d8-8f0f-b837239503ce","scriptFormat":"text\/x-javascript","script":{"content":["function stringToList(string) {\r\n\tvar stringList = new tw.object.listOf.String();\r\n\tstringList[0] = string;\r\n\treturn stringList;\r\n}\r\n\r\nfunction floatToStringList(float) {\r\n\tvar stringList = new tw.object.listOf.String();\r\n\tstringList[0] = \"\" + float;\r\n\treturn stringList;\r\n}\r\n\r\nfunction booleanToStringList(boolean) {\r\n\tvar stringList = new tw.object.listOf.String();\r\n\tif (boolean)\r\n\t\tstringList[0] = \"\u0646\u0639\u0645\"\r\n\telse\r\n\t\tstringList[0] = \"\u0644\u0627\"\r\n\treturn stringList;\r\n}\r\n\r\nfunction formatDate(date) {\r\n\tdate.setHours(date.getHours() + 2);\r\n\tvar d = new Date(date),\r\n\t\tday = '' + (d.getDate()),\r\n\t\tmonth = '' + (d.getMonth() + 1),\r\n\t\tyear = d.getFullYear();\r\n\r\n\tif (month.length &lt; 2)\r\n\t\tmonth = '0' + month;\r\n\tif (day.length &lt; 2)\r\n\t\tday = '0' + day;\r\n\r\n\treturn [day, month, year].join('\/');\r\n}\r\n\r\ntw.local.request = new tw.object.DocumentGenerationRequest();\r\ntw.local.request.templates = new tw.object.listOf.RequestTemplatePojo();\r\n\/\/tw.local.request.templates[0] = new tw.object.RequestTemplatePojo();\r\n\r\ntw.local.request.appName = \"ODC create\/Amend\";\r\ntw.local.request.purposeCode = new tw.object.listOf.String();\r\ntw.local.request.purposeCode[0] = \"PUR1\";\r\n\r\ntw.local.request.programCode = \"PROG1\";\r\n\/\/\"PROG1\";\r\n\r\ntw.local.request.secure = true;\r\n\r\ntw.local.paramsValues = new tw.object.Map();\r\n\r\n\r\n\/\/---------------------------------------------------\r\nvar ecmInfo = new tw.object.EcmDocumentInformation();\r\necmInfo.docClassName = \"Document\";\r\necmInfo.docProperties = \"\";\r\necmInfo.folderId = tw.local.docGenTempFolder.path;\r\n\r\nif (tw.local.generationStatus == \"Generate\") {\r\n\tvar title = \"\";\r\n}\r\nelse {\r\n\tif (tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value == \"other\") {\r\n\t\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\r\n\t}\r\n\telse {\r\n\t\tvar title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value;\r\n\t}\r\n}\r\n\/\/var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value + tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;\r\ntw.local.paramsValues.put(\"Title001\", stringToList(title));\r\nvar date = new tw.object.Date();\r\ntw.local.paramsValues.put(\"Date001\", stringToList(formatDate(date)));\r\nvar swiftCode = \"NBEGEGCX\" + tw.local.odcRequest.FinancialDetailsFO.executionHub.value;\r\n\r\ntw.local.paramsValues.put(\"Swift001\", stringToList(swiftCode));\r\ntw.local.paramsValues.put(\"Int001\", stringToList(tw.local.odcRequest.ImporterDetails.bank));\r\ntw.local.paramsValues.put(\"Int002\", stringToList(tw.local.odcRequest.ImporterDetails.bankAddress));\r\ntw.local.paramsValues.put(\"Int003\", stringToList(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\ntw.local.paramsValues.put(\"Int004\", stringToList(tw.local.odcRequest.ImporterDetails.BICCode));\r\ntw.local.paramsValues.put(\"Int005\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerName));\r\ntw.local.paramsValues.put(\"Int006\", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerAddress));\r\ntw.local.paramsValues.put(\"Int007\", stringToList(tw.local.odcRequest.ImporterDetails.importerName));\r\ntw.local.paramsValues.put(\"Int008\", stringToList(tw.local.odcRequest.ImporterDetails.importerAddress));\r\ntw.local.paramsValues.put(\"Int009\", stringToList(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\ntw.local.paramsValues.put(\"Int010\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\ntw.local.paramsValues.put(\"Int011\", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\nvar claimedAmount = tw.local.odcRequest.FinancialDetailsFO.collectableAmount + tw.local.odcRequest.FinancialDetailsFO.ourCharges;\r\ntw.local.paramsValues.put(\"Int012\", floatToStringList(claimedAmount));\r\ntw.local.paramsValues.put(\"Int013\", stringToList(tw.local.odcRequest.FinancialDetailsBR.currency.name));\r\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name == \"001\") {\r\n\ttw.local.paramsValues.put(\"Int014\", stringToList(\"Sight\"));\r\n}\r\nelse {\r\n\ttw.local.paramsValues.put(\"Int014\", stringToList(formatDate(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date)));\r\n}\r\ntw.local.paramsValues.put(\"Int015\", stringToList(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\n\r\nvar deliveryTerms = new tw.object.listOf.String();\r\nfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms.listLength; i++) {\r\n\tdeliveryTerms[i] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[i];\r\n}\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength != 0) {\r\n\tfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength; i++) {\r\n\t\tdeliveryTerms[deliveryTerms.length] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra[i];\r\n\t}\r\n}\r\n\r\ntw.local.paramsValues.put(\"Int016\", deliveryTerms);\r\n\r\nvar paymentInstructions = new tw.object.listOf.String();\r\nfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions.listLength; i++) {\r\n\tpaymentInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[i];\r\n}\r\nvar length = paymentInstructions.length;\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength != 0) {\r\n\tfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength; i++) {\r\n\t\tpaymentInstructions[paymentInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra[i];\r\n\t}\r\n}\r\n\r\ntw.local.paramsValues.put(\"Int017\", paymentInstructions);\r\n\r\nvar instructions = new tw.object.listOf.String();\r\nfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.Instructions.listLength; i++) {\r\n\tinstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.Instructions[i];\r\n}\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength != 0) {\r\n\tfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength; i++) {\r\n\t\tinstructions[instructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra[i];\r\n\t}\r\n}\r\ntw.local.paramsValues.put(\"Int018\", instructions);\r\n\r\nvar specialInstructions = new tw.object.listOf.String();\r\nfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions.listLength; i++) {\r\n\tspecialInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[i];\r\n}\r\nif (tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength != 0) {\r\n\tfor (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength; i++) {\r\n\t\tspecialInstructions[specialInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra[i];\r\n\t}\r\n}\r\n\r\ntw.local.paramsValues.put(\"Int019\", specialInstructions);\r\n\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nvar documentNames = new tw.object.listOf.String();\r\nvar noOfCopies = new tw.object.listOf.String();\r\nvar noOfOriginals = new tw.object.listOf.String();\r\nvar docsCount =0;\r\nfor (var i = 0; i &lt; tw.local.odcRequest.attachmentDetails.attachment.listLength; i++) {\r\n\tif (!!tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals || !!tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies) {\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].name == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].name == null) {\r\n\t\t\tdocumentNames[docsCount] = \"\";\r\n\t\t}\r\n\t\telse {\r\n\t\t\tdocumentNames[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].name;\r\n\t\t}\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == null) {\r\n\t\t\tnoOfOriginals[docsCount] = \"\";\r\n\t\t}\r\n\t\telse {\r\n\t\t\tnoOfOriginals[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals + \"\";\r\n\t\t}\r\n\t\tif (tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == \"\" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == null) {\r\n\t\t\tnoOfCopies[docsCount] = \"\";\r\n\t\t}\r\n\t\telse {\r\n\t\t\tnoOfCopies[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies + \"\";\r\n\t\t}\r\n\t\tdocsCount++;\r\n\r\n\t}\r\n\/\/\telse{\r\n\/\/\t\tcontinue;\r\n\/\/\t}\r\n}\r\nif(docsCount&gt;0){\r\n\ttw.local.paramsValues.put(\"intr001\", documentNames);\r\n\ttw.local.paramsValues.put(\"intr002\", noOfOriginals);\r\n\ttw.local.paramsValues.put(\"intr003\", noOfCopies);\r\n}\r\n\r\n\r\n\r\n\/\/tw.local.paramsValues.put(\"intr001\",specialInstructions);\r\n\/\/tw.local.paramsValues.put(\"intr002\",specialInstructions);\r\n\/\/tw.local.paramsValues.put(\"intr003\",specialInstructions);\r\n\r\ntw.local.request.paramsValues = tw.local.paramsValues;\r\n\/\/--------------------------Generate Docs-------------------\r\nvar template = new tw.object.RequestTemplatePojo();\r\ntemplate.secure = true;\r\ntemplate.ecmDocInfo = ecmInfo;\r\ntemplate.templateCode = \"ODCRemittanceLetter\";\r\ntemplate.generatedDocName = \"remittance Letter\";\r\ntw.local.request.templates.insertIntoList(tw.local.request.templates.listLength, template);\r\n"]}},{"targetRef":"7044d967-7568-4a9e-8f73-a0143e928089","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get template names","declaredType":"sequenceFlow","id":"456da453-0ff0-48d2-8993-02260083c2ad","sourceRef":"df1ded4c-8bfd-44d8-8f0f-b837239503ce"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject[0].objectTypeId = \"\";\nautoObject[0].value = null;\nautoObject"}]},"itemSubjectRef":"itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9","name":"properties","isCollection":true,"declaredType":"dataObject","id":"2056.79ad9803-626f-4181-8c3c-770f087a21ec"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"templateName","isCollection":true,"declaredType":"dataObject","id":"2056.d77cfee1-b5f8-48c8-8413-9f4deccdd93a"},{"itemSubjectRef":"itm.12.90c5b1d3-3fa1-4b3b-ab27-220b0652dc55","name":"paramsValues","isCollection":false,"declaredType":"dataObject","id":"2056.88fc3cd0-f6cb-465e-8f19-9802902cea33"},{"itemSubjectRef":"itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063","name":"request","isCollection":false,"declaredType":"dataObject","id":"2056.1d5f3808-737b-48d5-8993-0aef345100bd"},{"outgoing":["6f0096bd-6183-4c60-81fb-1d5ef2324a64"],"incoming":["456da453-0ff0-48d2-8993-02260083c2ad"],"extensionElements":{"postAssignmentScript":["log.info(\" ServiceName : Document Generation: END\");\r\ntw.local.docID = tw.local.response.templatesResponse[0].ecmDocName;"],"nodeVisualInfo":[{"width":95,"x":859,"y":54,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["log.info(\" ServiceName : Document Generation: START\");"],"activityType":["ServiceTask"],"externalServiceRefPO":["1.7f097f90-cf8f-43fa-875f-f106fe9cac70"],"externalServiceRef":["{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}_4f57dabd-7522-45c1-a6cb-ae30e4578c97"]},"operationRef":"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service}_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed","implementation":"##WebService","declaredType":"serviceTask","startQuantity":1,"name":"Generate Document","dataInputAssociation":[{"targetRef":"_5f31e534-f82c-4bed-8b97-f4b50bb07fb8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063","declaredType":"TFormalExpression","content":["tw.local.request"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"7044d967-7568-4a9e-8f73-a0143e928089","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc","declaredType":"TFormalExpression","content":["tw.local.response"]}}],"sourceRef":["_66fa2129-ae2c-47d6-aa59-b4f60f4a6c96"]}]},{"targetRef":"3a8ae9f4-a4b5-4024-863f-a0b0817b39c7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"6f0096bd-6183-4c60-81fb-1d5ef2324a64","sourceRef":"7044d967-7568-4a9e-8f73-a0143e928089"},{"itemSubjectRef":"itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc","name":"response","isCollection":false,"declaredType":"dataObject","id":"2056.8194bbbc-8a6e-43a4-865f-51db9a4c83ec"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"declaredType":"dataObject","id":"2056.47435375-900d-463a-8170-bb2c5ac59b17"},{"startQuantity":1,"outgoing":["4a81ac30-2f20-4653-83e0-0b0c4aa45d0c"],"incoming":["6945970e-2c78-4a60-8fcd-6be760d2a3f6"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":712,"y":311,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Generate Remittance Letter\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"56d069f7-8b98-48b1-800b-dd3a134083b1","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"bc8b682b-6442-40c7-88cc-2da045c87a52","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"4a81ac30-2f20-4653-83e0-0b0c4aa45d0c","sourceRef":"56d069f7-8b98-48b1-800b-dd3a134083b1"},{"outgoing":["078c569a-3fac-4dfa-8c82-6d494c69d3de","0b7e9890-ebed-4616-8d5c-037bbf967812"],"incoming":["2027.2521cec4-3685-4925-848f-52ab82439a5d"],"default":"078c569a-3fac-4dfa-8c82-6d494c69d3de","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":314,"y":73,"declaredType":"TNodeVisualInfo","height":32}]},"name":"regenerate?","declaredType":"exclusiveGateway","id":"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc"},{"targetRef":"b4ca5615-841a-4b96-8f71-17df1c09d08e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"078c569a-3fac-4dfa-8c82-6d494c69d3de","sourceRef":"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc"},{"targetRef":"13c3f7c0-c045-4e71-88f4-4a56be3e590d","conditionExpression":{"declaredType":"TFormalExpression","content":["!!tw.local.odcRequest.templateDocID"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"0b7e9890-ebed-4616-8d5c-037bbf967812","sourceRef":"c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc"},{"itemSubjectRef":"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a","name":"docs","isCollection":true,"declaredType":"dataObject","id":"2056.56f0e5cd-36e3-4171-8118-3182c75106a1"},{"outgoing":["e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6"],"incoming":["0b7e9890-ebed-4616-8d5c-037bbf967812"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":401,"y":185,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["ContentTask"]},"operationRef":"DOC_OP_DELETE_DOCUMENT","implementation":"##WebService","serverName":"useMappingServer","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Delete Doc","dataInputAssociation":[{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"FileNet\""]}}]},{"targetRef":"ALL_VERSIONS","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["true"]}}]},{"targetRef":"DOCUMENT_ID","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.templateDocID"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"13c3f7c0-c045-4e71-88f4-4a56be3e590d","orderOverride":false},{"targetRef":"b4ca5615-841a-4b96-8f71-17df1c09d08e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Doc Gen Temp Folder","declaredType":"sequenceFlow","id":"e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6","sourceRef":"13c3f7c0-c045-4e71-88f4-4a56be3e590d"},{"parallelMultiple":false,"outgoing":["6945970e-2c78-4a60-8fcd-6be760d2a3f6"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"98eb20b4-0d3e-401c-8b7f-d01d8b88083b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"f1a42aaa-61ef-46c9-88ea-255111151e90","otherAttributes":{"eventImplId":"4e7012cf-723f-425a-8175-9f1a6178fd44"}}],"attachedToRef":"13c3f7c0-c045-4e71-88f4-4a56be3e590d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":436,"y":243,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"21c155e3-195c-40b3-891c-9ec39b9f93b7","outputSet":{}},{"targetRef":"56d069f7-8b98-48b1-800b-dd3a134083b1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"6945970e-2c78-4a60-8fcd-6be760d2a3f6","sourceRef":"21c155e3-195c-40b3-891c-9ec39b9f93b7"},{"incoming":["4a81ac30-2f20-4653-83e0-0b0c4aa45d0c"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e75eccda-e8f4-41d4-8c9b-d3c6e4538dc0","otherAttributes":{"eventImplId":"e1f379d2-8f3b-4915-826a-0cf9bb01de4c"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":935,"y":408,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"bc8b682b-6442-40c7-88cc-2da045c87a52"}],"laneSet":[{"id":"9213a0a3-4f14-498b-870a-bdd484aa7ff9","lane":[{"flowNodeRef":["98eaadf2-a3e9-4432-8cc8-70ef8c5970c6","3a8ae9f4-a4b5-4024-863f-a0b0817b39c7","b4ca5615-841a-4b96-8f71-17df1c09d08e","df1ded4c-8bfd-44d8-8f0f-b837239503ce","7044d967-7568-4a9e-8f73-a0143e928089","56d069f7-8b98-48b1-800b-dd3a134083b1","c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc","13c3f7c0-c045-4e71-88f4-4a56be3e590d","21c155e3-195c-40b3-891c-9ec39b9f93b7","bc8b682b-6442-40c7-88cc-2da045c87a52"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c4d72eee-6884-4051-8394-1ddf22790a12","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Generate Remittance Letter","declaredType":"process","id":"1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183","name":"docGenTempFolder","isCollection":false,"id":"2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.a37faedf-a8f5-421e-8773-5c59cb9d3888"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"id":"2055.ad3e5165-9152-45e2-8bef-7be47d669999"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"docID","isCollection":false,"id":"2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939"}],"inputSet":[{"dataInputRefs":["2055.4996fad4-3fac-48ef-8f5b-05525969b773","2055.cb493271-2446-49c9-80fd-c8bfca9646bc"]}],"outputSet":[{"dataOutputRefs":["2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417","2055.a37faedf-a8f5-421e-8773-5c59cb9d3888","2055.ad3e5165-9152-45e2-8bef-7be47d669999","2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"{7015CC8B-0000-CF4A-AB45-A92089CC7DB7}\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject.folderPath = \"\/\u0623\u0631\u0634\u064a\u0641 \u0639\u0645\u0644\u0627\u0621 \u0627\u0644\u0628\u0646\u0643 \u0627\u0644\u0627\u0647\u0644\u064a\/\u0639\u0645\u0644\u0627\u0621 \u0627\u0644\u0628\u0646\u0643\/03024659\/DC Outward\/00104230000209ODC\/Issuance\";\nautoObject.templateDocID = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.4996fad4-3fac-48ef-8f5b-05525969b773"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"generationStatus","isCollection":false,"id":"2055.cb493271-2446-49c9-80fd-c8bfca9646bc"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4996fad4-3fac-48ef-8f5b-05525969b773</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "{7015CC8B-0000-CF4A-AB45-A92089CC7DB7}";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject.folderPath = "/أرشيف عملاء البنك الاهلي/عملاء البنك/03024659/DC Outward/00104230000209ODC/Issuance";
autoObject.templateDocID = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e9f2d199-df16-45fc-879c-6b639e2eae0b</guid>
            <versionId>ac8236eb-70cb-4e7d-a460-90b104ed95c2</versionId>
        </processParameter>
        <processParameter name="generationStatus">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cb493271-2446-49c9-80fd-c8bfca9646bc</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>35fa516f-769e-4e3e-b493-f354e5b6e991</guid>
            <versionId>8c06cb3f-ca43-4488-aee3-08e53dbac21a</versionId>
        </processParameter>
        <processParameter name="docGenTempFolder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>299c1c92-057a-4469-b9be-22ccfa8bbf91</guid>
            <versionId>059b42c7-26de-4e3c-824d-339079664b74</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a37faedf-a8f5-421e-8773-5c59cb9d3888</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>13c073a7-d802-42ce-91a5-e356b7ee5e2e</guid>
            <versionId>606d86a1-f7ec-4dd5-8815-5645c0faf768</versionId>
        </processParameter>
        <processParameter name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ad3e5165-9152-45e2-8bef-7be47d669999</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6965740b-203c-4fa1-9e9d-e9ca395d15e3</guid>
            <versionId>b3b75ed0-eec7-42cc-bbfc-be28287670a5</versionId>
        </processParameter>
        <processParameter name="docID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>805055e6-be6a-4379-860d-d0713b65b490</guid>
            <versionId>e985f59a-a1cd-40a3-b19a-10e461af8cc8</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c3371967-77c0-4272-901f-9f67262b643e</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>205</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6ed4f6a8-b00b-4fcf-8358-389c76d9fc9f</guid>
            <versionId>d02622c9-141b-4846-91c2-e2bd53bb2f9d</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.98097b94-2b12-4bea-a2ab-a62c610d2510</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>222</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8d79654b-0c8c-4c6d-8327-9032f39d0a8d</guid>
            <versionId>6bcd9905-12ec-454d-8a24-a4e0015c92ac</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7474d81e-18d3-4732-8a36-07e6f72c583f</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>223</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4c5d56f2-3ae4-4963-8cab-bb415de47161</guid>
            <versionId>c8a5f67f-23de-4de8-afbf-3c04bd77772f</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0b414eaa-a71d-4cd9-965e-efcefc32c027</processParameterId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>224</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>628e1f44-73e0-4e5e-9f57-97d574c0580d</guid>
            <versionId>e3eed660-c619-4336-a353-5acdd2372470</versionId>
        </processParameter>
        <processVariable name="properties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.79ad9803-626f-4181-8c3c-770f087a21ec</processVariableId>
            <description isNull="true" />
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.a4275847-1a37-4d3c-9289-0462d5afbca9</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject[0].objectTypeId = "";
autoObject[0].value = null;
autoObject</defaultValue>
            <guid>86cbedf5-6a3e-4804-a9ef-fd603204341e</guid>
            <versionId>4a08d7dd-0d71-43ef-9c31-90e2c7433bd8</versionId>
        </processVariable>
        <processVariable name="templateName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d77cfee1-b5f8-48c8-8413-9f4deccdd93a</processVariableId>
            <description isNull="true" />
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6a31c723-8462-42da-b2e0-d6bca8548769</guid>
            <versionId>3fa0074b-9a22-4b7f-906d-a6e056e79dd5</versionId>
        </processVariable>
        <processVariable name="paramsValues">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.88fc3cd0-f6cb-465e-8f19-9802902cea33</processVariableId>
            <description isNull="true" />
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.90c5b1d3-3fa1-4b3b-ab27-220b0652dc55</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>15f23f1a-c095-4346-8397-c9c2cbeb370f</guid>
            <versionId>e295226a-d7bb-4475-8ab6-58edeb313cfa</versionId>
        </processVariable>
        <processVariable name="request">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1d5f3808-737b-48d5-8993-0aef345100bd</processVariableId>
            <description isNull="true" />
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d26d18ae-9d5f-4a50-b7d0-4f32d147f272</guid>
            <versionId>b4898c83-6cc5-48cb-8a2f-41bf0f3155a1</versionId>
        </processVariable>
        <processVariable name="response">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8194bbbc-8a6e-43a4-865f-51db9a4c83ec</processVariableId>
            <description isNull="true" />
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a0722085-9246-4af1-b86e-8a21be7e94cc</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>61a9c9b1-5a5a-4d25-891d-8b7008b15333</guid>
            <versionId>c44a6938-a31e-48dc-b8c0-5d10e6a5b521</versionId>
        </processVariable>
        <processVariable name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.47435375-900d-463a-8170-bb2c5ac59b17</processVariableId>
            <description isNull="true" />
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e0a67c09-de75-4872-9099-e004c3b2e16a</guid>
            <versionId>13b3ade4-9d14-4300-a444-a7cc8c01fbad</versionId>
        </processVariable>
        <processVariable name="docs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.56f0e5cd-36e3-4171-8118-3182c75106a1</processVariableId>
            <description isNull="true" />
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.490f939c-3c6d-4ef7-9707-33b5b618877a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f706efb9-1b4b-4eb6-acf7-6ec6b60b19ab</guid>
            <versionId>03272da3-6800-487e-97c2-44c7243bc713</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>regenerate?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.1d9387be-9862-4023-8f8a-595ae3d9949f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:16d6</guid>
            <versionId>0000af5f-782a-40c5-bf01-4183b15866af</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="314" y="73">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.1d9387be-9862-4023-8f8a-595ae3d9949f</switchId>
                <guid>9d3f597d-0c65-4b22-9265-bc84e4d75eaf</guid>
                <versionId>ab23816e-bcf4-4eba-9f58-25e3c04ad2c6</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.aee81b16-c29b-4e02-87d5-04555dfed37c</switchConditionId>
                    <switchId>3013.1d9387be-9862-4023-8f8a-595ae3d9949f</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f12</endStateId>
                    <condition>!!tw.local.odcRequest.templateDocID</condition>
                    <guid>45bfb906-9b64-47ce-bd87-7cda9e313da2</guid>
                    <versionId>afc54b01-ca27-46e8-b722-e699fa0e6ea7</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7044d967-7568-4a9e-8f73-a0143e928089</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>Generate Document</name>
            <tWComponentName>SKELConnector</tWComponentName>
            <tWComponentId>3034.cb0ddfa4-5b82-4494-b28f-6b8f5a03d6fb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1403</guid>
            <versionId>1551904e-059a-4101-b614-e107411612ea</versionId>
            <externalServiceRef>/1.7f097f90-cf8f-43fa-875f-f106fe9cac70</externalServiceRef>
            <externalServiceOp>_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed</externalServiceOp>
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.581b2560-24b1-43cf-85cf-33b780778bc7</processItemPrePostId>
                <processItemId>2025.7044d967-7568-4a9e-8f73-a0143e928089</processItemId>
                <location>1</location>
                <script>log.info(" ServiceName : Document Generation: START");</script>
                <guid>a2d28ffa-e2a3-49c6-9409-5557c963008e</guid>
                <versionId>92634e61-cdb0-42a1-8cc0-868d62ffc3b3</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.a1b62852-ee8c-4c29-bbbd-c927ce68aea4</processItemPrePostId>
                <processItemId>2025.7044d967-7568-4a9e-8f73-a0143e928089</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Document Generation: END");&#xD;
tw.local.docID = tw.local.response.templatesResponse[0].ecmDocName;</script>
                <guid>b7591f42-669a-4681-afae-eef31856c8a8</guid>
                <versionId>948f6540-986d-4a67-97ba-6b39fbeb340c</versionId>
            </processPrePosts>
            <layoutData x="859" y="54">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <skelConnectorId>3034.cb0ddfa4-5b82-4494-b28f-6b8f5a03d6fb</skelConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.SKELConnectorConfiguration"&gt;
  &lt;inputParameters&gt;
    &lt;parameter&gt;
      &lt;name&gt;_5f31e534-f82c-4bed-8b97-f4b50bb07fb8&lt;/name&gt;
      &lt;type&gt;itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063&lt;/type&gt;
      &lt;description /&gt;
      &lt;defaultValue /&gt;
      &lt;argumentVariable&gt;tw.local.request&lt;/argumentVariable&gt;
      &lt;isArray&gt;&lt;/isArray&gt;
    &lt;/parameter&gt;
  &lt;/inputParameters&gt;
  &lt;outputParameters&gt;
    &lt;parameter&gt;
      &lt;name&gt;_66fa2129-ae2c-47d6-aa59-b4f60f4a6c96&lt;/name&gt;
      &lt;type&gt;itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc&lt;/type&gt;
      &lt;description /&gt;
      &lt;defaultValue /&gt;
      &lt;argumentVariable&gt;tw.local.response&lt;/argumentVariable&gt;
      &lt;isArray&gt;&lt;/isArray&gt;
    &lt;/parameter&gt;
  &lt;/outputParameters&gt;
  &lt;extendedProperties /&gt;
&lt;/config&gt;</definition>
                <guid>7199283f-9cf0-47dc-899c-bde63961a94e</guid>
                <versionId>b57ccadb-fc52-4e6f-9fe7-1e9ea4b05634</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bc8b682b-6442-40c7-88cc-2da045c87a52</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.1da008ca-96d9-47f7-8d3c-df19828c13ef</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:539c</guid>
            <versionId>3d1d9c3e-e537-4fa1-9387-03500c0e2590</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="935" y="408">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.1da008ca-96d9-47f7-8d3c-df19828c13ef</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>79b6794d-f53a-4279-ade5-53b8d7841641</guid>
                <versionId>c8ccdac6-4cff-4ffc-83c9-3e7ba39725b0</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.88153111-9dcf-4b75-a50c-43eaea4ba621</parameterMappingId>
                    <processParameterId>2055.c3371967-77c0-4272-901f-9f67262b643e</processParameterId>
                    <parameterMappingParentId>3007.1da008ca-96d9-47f7-8d3c-df19828c13ef</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>beb23170-7158-439c-a38c-dc367bd62411</guid>
                    <versionId>15336b51-c71a-439f-80ad-8e4695a45117</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>Delete Doc</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.37a71276-8dc4-4f99-824c-7e3ab2603c87</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.56d069f7-8b98-48b1-800b-dd3a134083b1</errorHandlerItemId>
            <guid>guid:a6be0630e884ccca:-427a0b52:18bc0b330f6:178d</guid>
            <versionId>4787c012-14f1-4748-97b8-f1a474bec666</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="401" y="185">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3298</errorHandlerItem>
                <errorHandlerItemId>2025.56d069f7-8b98-48b1-800b-dd3a134083b1</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.37a71276-8dc4-4f99-824c-7e3ab2603c87</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;documentId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.odcRequest.templateDocID&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;allVersions&lt;/name&gt;&#xD;
      &lt;type&gt;Boolean&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;true&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"FileNet"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters /&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;DOC_OP_DELETE_DOCUMENT&lt;/operationType&gt;&#xD;
  &lt;server&gt;useMappingServer&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.0b414eaa-a71d-4cd9-965e-efcefc32c027&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>1d339d7e-2c26-4429-933c-4d1419c97a04</guid>
                <versionId>ad42daff-99d1-41b8-8ec3-0504db179185</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3a8ae9f4-a4b5-4024-863f-a0b0817b39c7</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.580aca1a-03d5-408a-9599-8e18babd5f43</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-169e</guid>
            <versionId>6f045014-0ce1-45da-a638-992e9b8da9c3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1017" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.580aca1a-03d5-408a-9599-8e18babd5f43</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>9e973ace-4170-412f-8523-7ba669b4196e</guid>
                <versionId>bad0d1ad-c521-4451-974b-4a3b47437cbf</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>Prepare request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.81651db4-f4a5-4632-a003-1b7b6fdbd044</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-160e</guid>
            <versionId>bc64a280-d1da-491e-9d90-64dda27a0fa3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="649" y="54">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.81651db4-f4a5-4632-a003-1b7b6fdbd044</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>function stringToList(string) {&#xD;
	var stringList = new tw.object.listOf.String();&#xD;
	stringList[0] = string;&#xD;
	return stringList;&#xD;
}&#xD;
&#xD;
function floatToStringList(float) {&#xD;
	var stringList = new tw.object.listOf.String();&#xD;
	stringList[0] = "" + float;&#xD;
	return stringList;&#xD;
}&#xD;
&#xD;
function booleanToStringList(boolean) {&#xD;
	var stringList = new tw.object.listOf.String();&#xD;
	if (boolean)&#xD;
		stringList[0] = "نعم"&#xD;
	else&#xD;
		stringList[0] = "لا"&#xD;
	return stringList;&#xD;
}&#xD;
&#xD;
function formatDate(date) {&#xD;
	date.setHours(date.getHours() + 2);&#xD;
	var d = new Date(date),&#xD;
		day = '' + (d.getDate()),&#xD;
		month = '' + (d.getMonth() + 1),&#xD;
		year = d.getFullYear();&#xD;
&#xD;
	if (month.length &lt; 2)&#xD;
		month = '0' + month;&#xD;
	if (day.length &lt; 2)&#xD;
		day = '0' + day;&#xD;
&#xD;
	return [day, month, year].join('/');&#xD;
}&#xD;
&#xD;
tw.local.request = new tw.object.DocumentGenerationRequest();&#xD;
tw.local.request.templates = new tw.object.listOf.RequestTemplatePojo();&#xD;
//tw.local.request.templates[0] = new tw.object.RequestTemplatePojo();&#xD;
&#xD;
tw.local.request.appName = "ODC create/Amend";&#xD;
tw.local.request.purposeCode = new tw.object.listOf.String();&#xD;
tw.local.request.purposeCode[0] = "PUR1";&#xD;
&#xD;
tw.local.request.programCode = "PROG1";&#xD;
//"PROG1";&#xD;
&#xD;
tw.local.request.secure = true;&#xD;
&#xD;
tw.local.paramsValues = new tw.object.Map();&#xD;
&#xD;
&#xD;
//---------------------------------------------------&#xD;
var ecmInfo = new tw.object.EcmDocumentInformation();&#xD;
ecmInfo.docClassName = "Document";&#xD;
ecmInfo.docProperties = "";&#xD;
ecmInfo.folderId = tw.local.docGenTempFolder.path;&#xD;
&#xD;
if (tw.local.generationStatus == "Generate") {&#xD;
	var title = "";&#xD;
}&#xD;
else {&#xD;
	if (tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value == "other") {&#xD;
		var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;&#xD;
	}&#xD;
	else {&#xD;
		var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value;&#xD;
	}&#xD;
}&#xD;
//var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value + tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;&#xD;
tw.local.paramsValues.put("Title001", stringToList(title));&#xD;
var date = new tw.object.Date();&#xD;
tw.local.paramsValues.put("Date001", stringToList(formatDate(date)));&#xD;
var swiftCode = "NBEGEGCX" + tw.local.odcRequest.FinancialDetailsFO.executionHub.value;&#xD;
&#xD;
tw.local.paramsValues.put("Swift001", stringToList(swiftCode));&#xD;
tw.local.paramsValues.put("Int001", stringToList(tw.local.odcRequest.ImporterDetails.bank));&#xD;
tw.local.paramsValues.put("Int002", stringToList(tw.local.odcRequest.ImporterDetails.bankAddress));&#xD;
tw.local.paramsValues.put("Int003", stringToList(tw.local.odcRequest.ImporterDetails.bankPhoneNo));&#xD;
tw.local.paramsValues.put("Int004", stringToList(tw.local.odcRequest.ImporterDetails.BICCode));&#xD;
tw.local.paramsValues.put("Int005", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerName));&#xD;
tw.local.paramsValues.put("Int006", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerAddress));&#xD;
tw.local.paramsValues.put("Int007", stringToList(tw.local.odcRequest.ImporterDetails.importerName));&#xD;
tw.local.paramsValues.put("Int008", stringToList(tw.local.odcRequest.ImporterDetails.importerAddress));&#xD;
tw.local.paramsValues.put("Int009", stringToList(tw.local.odcRequest.ImporterDetails.importerPhoneNo));&#xD;
tw.local.paramsValues.put("Int010", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));&#xD;
tw.local.paramsValues.put("Int011", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.ourCharges));&#xD;
var claimedAmount = tw.local.odcRequest.FinancialDetailsFO.collectableAmount + tw.local.odcRequest.FinancialDetailsFO.ourCharges;&#xD;
tw.local.paramsValues.put("Int012", floatToStringList(claimedAmount));&#xD;
tw.local.paramsValues.put("Int013", stringToList(tw.local.odcRequest.FinancialDetailsBR.currency.name));&#xD;
if (tw.local.odcRequest.BasicDetails.paymentTerms.name == "001") {&#xD;
	tw.local.paramsValues.put("Int014", stringToList("Sight"));&#xD;
}&#xD;
else {&#xD;
	tw.local.paramsValues.put("Int014", stringToList(formatDate(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date)));&#xD;
}&#xD;
tw.local.paramsValues.put("Int015", stringToList(tw.local.odcRequest.FinancialDetailsFO.referenceNo));&#xD;
&#xD;
var deliveryTerms = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms.listLength; i++) {&#xD;
	deliveryTerms[i] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[i];&#xD;
}&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength; i++) {&#xD;
		deliveryTerms[deliveryTerms.length] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra[i];&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.paramsValues.put("Int016", deliveryTerms);&#xD;
&#xD;
var paymentInstructions = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions.listLength; i++) {&#xD;
	paymentInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[i];&#xD;
}&#xD;
var length = paymentInstructions.length;&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength; i++) {&#xD;
		paymentInstructions[paymentInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra[i];&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.paramsValues.put("Int017", paymentInstructions);&#xD;
&#xD;
var instructions = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.Instructions.listLength; i++) {&#xD;
	instructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.Instructions[i];&#xD;
}&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength; i++) {&#xD;
		instructions[instructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra[i];&#xD;
	}&#xD;
}&#xD;
tw.local.paramsValues.put("Int018", instructions);&#xD;
&#xD;
var specialInstructions = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions.listLength; i++) {&#xD;
	specialInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[i];&#xD;
}&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength; i++) {&#xD;
		specialInstructions[specialInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra[i];&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.paramsValues.put("Int019", specialInstructions);&#xD;
&#xD;
////////////////////////////////////////////////////////////////////////////////&#xD;
var documentNames = new tw.object.listOf.String();&#xD;
var noOfCopies = new tw.object.listOf.String();&#xD;
var noOfOriginals = new tw.object.listOf.String();&#xD;
var docsCount =0;&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.attachmentDetails.attachment.listLength; i++) {&#xD;
	if (!!tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals || !!tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies) {&#xD;
		if (tw.local.odcRequest.attachmentDetails.attachment[i].name == "" || tw.local.odcRequest.attachmentDetails.attachment[i].name == null) {&#xD;
			documentNames[docsCount] = "";&#xD;
		}&#xD;
		else {&#xD;
			documentNames[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].name;&#xD;
		}&#xD;
		if (tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == "" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == null) {&#xD;
			noOfOriginals[docsCount] = "";&#xD;
		}&#xD;
		else {&#xD;
			noOfOriginals[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals + "";&#xD;
		}&#xD;
		if (tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == "" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == null) {&#xD;
			noOfCopies[docsCount] = "";&#xD;
		}&#xD;
		else {&#xD;
			noOfCopies[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies + "";&#xD;
		}&#xD;
		docsCount++;&#xD;
&#xD;
	}&#xD;
//	else{&#xD;
//		continue;&#xD;
//	}&#xD;
}&#xD;
if(docsCount&gt;0){&#xD;
	tw.local.paramsValues.put("intr001", documentNames);&#xD;
	tw.local.paramsValues.put("intr002", noOfOriginals);&#xD;
	tw.local.paramsValues.put("intr003", noOfCopies);&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
//tw.local.paramsValues.put("intr001",specialInstructions);&#xD;
//tw.local.paramsValues.put("intr002",specialInstructions);&#xD;
//tw.local.paramsValues.put("intr003",specialInstructions);&#xD;
&#xD;
tw.local.request.paramsValues = tw.local.paramsValues;&#xD;
//--------------------------Generate Docs-------------------&#xD;
var template = new tw.object.RequestTemplatePojo();&#xD;
template.secure = true;&#xD;
template.ecmDocInfo = ecmInfo;&#xD;
template.templateCode = "ODCRemittanceLetter";&#xD;
template.generatedDocName = "remittance Letter";&#xD;
tw.local.request.templates.insertIntoList(tw.local.request.templates.listLength, template);&#xD;
</script>
                <isRule>false</isRule>
                <guid>90118122-6e47-440f-98c6-201a45c6cf42</guid>
                <versionId>f3760ef8-189e-47ea-a969-6d9807131a6d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.56d069f7-8b98-48b1-800b-dd3a134083b1</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:-3298</guid>
            <versionId>bdc99aad-385e-4458-9228-8ef1c6c9dfde</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="712" y="311">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>2d3847d4-bc06-4292-bb2f-094f0b39f872</guid>
                <versionId>2ba7eeac-b0dc-47f6-b3cc-57d60c77116b</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.93ffc81f-1a26-4877-978b-c43a5709e4f4</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1c9955c5-e285-4bae-8bd4-84dfee4b310c</guid>
                    <versionId>03051c67-0d12-42a7-a53d-bb4b158e46bf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dbe92ea4-a9c8-421b-b337-9226e9c9d5b4</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b0d6fd7b-6488-4ee0-832d-120aed7f8864</guid>
                    <versionId>96401f41-703d-437c-8a68-2bf2b200dfb3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b2d9bcc4-aaf1-40f7-b211-f02604a0ab09</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.6ba17e7b-d169-485d-80f7-1adc19d6bea2</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Generate Remittance Letter"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ebc49c71-b1bf-4ead-8fc0-eaf08387a19e</guid>
                    <versionId>d484534f-6873-4a9c-a326-bda186e07e7c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</processItemId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <name>Get Doc Gen Temp Folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.3ce9a66d-a3ed-4ed8-90b2-13c6b0680d40</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1610</guid>
            <versionId>f00a9250-57b5-46c8-beba-74a535360fe1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.08d7a07a-a4be-41bd-948e-365b1e0e4902</processItemPrePostId>
                <processItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</processItemId>
                <location>1</location>
                <script>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Contract Document Generation : START");&#xD;
log.info(" ServiceName : Get Doc Gen Temp Folder: START");</script>
                <guid>afdfd841-3482-43b7-b053-7375fec1a76e</guid>
                <versionId>26e5d969-582f-4fd8-a8d6-36956e8e2b7f</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.00041e72-5de7-4308-87c6-178cbe9b5701</processItemPrePostId>
                <processItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</processItemId>
                <location>2</location>
                <script>log.info(" ServiceName : Get Doc Gen Temp Folder: END");</script>
                <guid>4cacff6e-8f7c-4d35-a039-c8571c1ff103</guid>
                <versionId>622ce84d-c8f9-4a16-8eb9-608b367d5a1a</versionId>
            </processPrePosts>
            <layoutData x="505" y="53">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.3ce9a66d-a3ed-4ed8-90b2-13c6b0680d40</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;path&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.odcRequest.folderPath&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;"FileNet"&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folder&lt;/name&gt;&#xD;
      &lt;type&gt;ECMFolder&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.docGenTempFolder&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_FOLDER_BY_PATH&lt;/operationType&gt;&#xD;
  &lt;server&gt;useMappingServer&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.7474d81e-18d3-4732-8a36-07e6f72c583f&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>7297e89a-1727-428e-88de-bb998fc89468</guid>
                <versionId>23b63278-55a6-4a80-b888-8998763f6400</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="209" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Generate Remittance Letter" id="1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.4996fad4-3fac-48ef-8f5b-05525969b773">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "{7015CC8B-0000-CF4A-AB45-A92089CC7DB7}";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject.folderPath = "/أرشيف عملاء البنك الاهلي/عملاء البنك/03024659/DC Outward/00104230000209ODC/Issuance";
autoObject.templateDocID = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="generationStatus" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.cb493271-2446-49c9-80fd-c8bfca9646bc" />
                        
                        
                        <ns16:dataOutput name="docGenTempFolder" itemSubjectRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183" isCollection="false" id="2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a37faedf-a8f5-421e-8773-5c59cb9d3888" />
                        
                        
                        <ns16:dataOutput name="isSuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.ad3e5165-9152-45e2-8bef-7be47d669999" />
                        
                        
                        <ns16:dataOutput name="docID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.4996fad4-3fac-48ef-8f5b-05525969b773</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.cb493271-2446-49c9-80fd-c8bfca9646bc</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.37daabcc-a1ec-4de9-852b-2de6cf6a3417</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.a37faedf-a8f5-421e-8773-5c59cb9d3888</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.ad3e5165-9152-45e2-8bef-7be47d669999</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.cc64b3d8-5b30-44dc-80bc-5a2f47738939</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="9213a0a3-4f14-498b-870a-bdd484aa7ff9">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c4d72eee-6884-4051-8394-1ddf22790a12" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>98eaadf2-a3e9-4432-8cc8-70ef8c5970c6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3a8ae9f4-a4b5-4024-863f-a0b0817b39c7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b4ca5615-841a-4b96-8f71-17df1c09d08e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>df1ded4c-8bfd-44d8-8f0f-b837239503ce</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7044d967-7568-4a9e-8f73-a0143e928089</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>56d069f7-8b98-48b1-800b-dd3a134083b1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>13c3f7c0-c045-4e71-88f4-4a56be3e590d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>21c155e3-195c-40b3-891c-9ec39b9f93b7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bc8b682b-6442-40c7-88cc-2da045c87a52</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="98eaadf2-a3e9-4432-8cc8-70ef8c5970c6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="209" y="77" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.2521cec4-3685-4925-848f-52ab82439a5d</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="3a8ae9f4-a4b5-4024-863f-a0b0817b39c7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1017" y="77" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-169e</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6f0096bd-6183-4c60-81fb-1d5ef2324a64</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="98eaadf2-a3e9-4432-8cc8-70ef8c5970c6" targetRef="c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc" name="To Exclusive Gateway" id="2027.2521cec4-3685-4925-848f-52ab82439a5d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns4:contentTask serverName="useMappingServer" operationRef="FOLDER_OP_GET_FOLDER_BY_PATH" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get Doc Gen Temp Folder" id="b4ca5615-841a-4b96-8f71-17df1c09d08e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="505" y="53" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Get Doc Gen Temp Folder: END");</ns3:postAssignmentScript>
                            
                            
                            <ns3:preAssignmentScript>log.info("ProcessInstance : "+tw.system.currentProcessInstanceID +" - ServiceName : Contract Document Generation : START");&#xD;
log.info(" ServiceName : Get Doc Gen Temp Folder: START");</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>078c569a-3fac-4dfa-8c82-6d494c69d3de</ns16:incoming>
                        
                        
                        <ns16:incoming>e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6</ns16:incoming>
                        
                        
                        <ns16:outgoing>4c44551e-24bb-4ce9-8c6c-b0e99527615b</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"FileNet"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>PATH</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.folderPath</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>FOLDER</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.240930df-e9da-40a6-a4e8-4b41b42bb183">tw.local.docGenTempFolder</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b4ca5615-841a-4b96-8f71-17df1c09d08e" targetRef="df1ded4c-8bfd-44d8-8f0f-b837239503ce" name="To Script Task1" id="4c44551e-24bb-4ce9-8c6c-b0e99527615b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Prepare request" id="df1ded4c-8bfd-44d8-8f0f-b837239503ce">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="649" y="54" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4c44551e-24bb-4ce9-8c6c-b0e99527615b</ns16:incoming>
                        
                        
                        <ns16:outgoing>456da453-0ff0-48d2-8993-02260083c2ad</ns16:outgoing>
                        
                        
                        <ns16:script>function stringToList(string) {&#xD;
	var stringList = new tw.object.listOf.String();&#xD;
	stringList[0] = string;&#xD;
	return stringList;&#xD;
}&#xD;
&#xD;
function floatToStringList(float) {&#xD;
	var stringList = new tw.object.listOf.String();&#xD;
	stringList[0] = "" + float;&#xD;
	return stringList;&#xD;
}&#xD;
&#xD;
function booleanToStringList(boolean) {&#xD;
	var stringList = new tw.object.listOf.String();&#xD;
	if (boolean)&#xD;
		stringList[0] = "نعم"&#xD;
	else&#xD;
		stringList[0] = "لا"&#xD;
	return stringList;&#xD;
}&#xD;
&#xD;
function formatDate(date) {&#xD;
	date.setHours(date.getHours() + 2);&#xD;
	var d = new Date(date),&#xD;
		day = '' + (d.getDate()),&#xD;
		month = '' + (d.getMonth() + 1),&#xD;
		year = d.getFullYear();&#xD;
&#xD;
	if (month.length &lt; 2)&#xD;
		month = '0' + month;&#xD;
	if (day.length &lt; 2)&#xD;
		day = '0' + day;&#xD;
&#xD;
	return [day, month, year].join('/');&#xD;
}&#xD;
&#xD;
tw.local.request = new tw.object.DocumentGenerationRequest();&#xD;
tw.local.request.templates = new tw.object.listOf.RequestTemplatePojo();&#xD;
//tw.local.request.templates[0] = new tw.object.RequestTemplatePojo();&#xD;
&#xD;
tw.local.request.appName = "ODC create/Amend";&#xD;
tw.local.request.purposeCode = new tw.object.listOf.String();&#xD;
tw.local.request.purposeCode[0] = "PUR1";&#xD;
&#xD;
tw.local.request.programCode = "PROG1";&#xD;
//"PROG1";&#xD;
&#xD;
tw.local.request.secure = true;&#xD;
&#xD;
tw.local.paramsValues = new tw.object.Map();&#xD;
&#xD;
&#xD;
//---------------------------------------------------&#xD;
var ecmInfo = new tw.object.EcmDocumentInformation();&#xD;
ecmInfo.docClassName = "Document";&#xD;
ecmInfo.docProperties = "";&#xD;
ecmInfo.folderId = tw.local.docGenTempFolder.path;&#xD;
&#xD;
if (tw.local.generationStatus == "Generate") {&#xD;
	var title = "";&#xD;
}&#xD;
else {&#xD;
	if (tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value == "other") {&#xD;
		var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;&#xD;
	}&#xD;
	else {&#xD;
		var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value;&#xD;
	}&#xD;
}&#xD;
//var title = tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value + tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle;&#xD;
tw.local.paramsValues.put("Title001", stringToList(title));&#xD;
var date = new tw.object.Date();&#xD;
tw.local.paramsValues.put("Date001", stringToList(formatDate(date)));&#xD;
var swiftCode = "NBEGEGCX" + tw.local.odcRequest.FinancialDetailsFO.executionHub.value;&#xD;
&#xD;
tw.local.paramsValues.put("Swift001", stringToList(swiftCode));&#xD;
tw.local.paramsValues.put("Int001", stringToList(tw.local.odcRequest.ImporterDetails.bank));&#xD;
tw.local.paramsValues.put("Int002", stringToList(tw.local.odcRequest.ImporterDetails.bankAddress));&#xD;
tw.local.paramsValues.put("Int003", stringToList(tw.local.odcRequest.ImporterDetails.bankPhoneNo));&#xD;
tw.local.paramsValues.put("Int004", stringToList(tw.local.odcRequest.ImporterDetails.BICCode));&#xD;
tw.local.paramsValues.put("Int005", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerName));&#xD;
tw.local.paramsValues.put("Int006", stringToList(tw.local.odcRequest.GeneratedDocumentInfo.customerAddress));&#xD;
tw.local.paramsValues.put("Int007", stringToList(tw.local.odcRequest.ImporterDetails.importerName));&#xD;
tw.local.paramsValues.put("Int008", stringToList(tw.local.odcRequest.ImporterDetails.importerAddress));&#xD;
tw.local.paramsValues.put("Int009", stringToList(tw.local.odcRequest.ImporterDetails.importerPhoneNo));&#xD;
tw.local.paramsValues.put("Int010", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));&#xD;
tw.local.paramsValues.put("Int011", floatToStringList(tw.local.odcRequest.FinancialDetailsFO.ourCharges));&#xD;
var claimedAmount = tw.local.odcRequest.FinancialDetailsFO.collectableAmount + tw.local.odcRequest.FinancialDetailsFO.ourCharges;&#xD;
tw.local.paramsValues.put("Int012", floatToStringList(claimedAmount));&#xD;
tw.local.paramsValues.put("Int013", stringToList(tw.local.odcRequest.FinancialDetailsBR.currency.name));&#xD;
if (tw.local.odcRequest.BasicDetails.paymentTerms.name == "001") {&#xD;
	tw.local.paramsValues.put("Int014", stringToList("Sight"));&#xD;
}&#xD;
else {&#xD;
	tw.local.paramsValues.put("Int014", stringToList(formatDate(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date)));&#xD;
}&#xD;
tw.local.paramsValues.put("Int015", stringToList(tw.local.odcRequest.FinancialDetailsFO.referenceNo));&#xD;
&#xD;
var deliveryTerms = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms.listLength; i++) {&#xD;
	deliveryTerms[i] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[i];&#xD;
}&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra.listLength; i++) {&#xD;
		deliveryTerms[deliveryTerms.length] = tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra[i];&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.paramsValues.put("Int016", deliveryTerms);&#xD;
&#xD;
var paymentInstructions = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions.listLength; i++) {&#xD;
	paymentInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[i];&#xD;
}&#xD;
var length = paymentInstructions.length;&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra.listLength; i++) {&#xD;
		paymentInstructions[paymentInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra[i];&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.paramsValues.put("Int017", paymentInstructions);&#xD;
&#xD;
var instructions = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.Instructions.listLength; i++) {&#xD;
	instructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.Instructions[i];&#xD;
}&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra.listLength; i++) {&#xD;
		instructions[instructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra[i];&#xD;
	}&#xD;
}&#xD;
tw.local.paramsValues.put("Int018", instructions);&#xD;
&#xD;
var specialInstructions = new tw.object.listOf.String();&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions.listLength; i++) {&#xD;
	specialInstructions[i] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[i];&#xD;
}&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength != 0) {&#xD;
	for (var i = 0; i &lt; tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra.listLength; i++) {&#xD;
		specialInstructions[specialInstructions.length] = tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra[i];&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.paramsValues.put("Int019", specialInstructions);&#xD;
&#xD;
////////////////////////////////////////////////////////////////////////////////&#xD;
var documentNames = new tw.object.listOf.String();&#xD;
var noOfCopies = new tw.object.listOf.String();&#xD;
var noOfOriginals = new tw.object.listOf.String();&#xD;
var docsCount =0;&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.attachmentDetails.attachment.listLength; i++) {&#xD;
	if (!!tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals || !!tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies) {&#xD;
		if (tw.local.odcRequest.attachmentDetails.attachment[i].name == "" || tw.local.odcRequest.attachmentDetails.attachment[i].name == null) {&#xD;
			documentNames[docsCount] = "";&#xD;
		}&#xD;
		else {&#xD;
			documentNames[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].name;&#xD;
		}&#xD;
		if (tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == "" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals == null) {&#xD;
			noOfOriginals[docsCount] = "";&#xD;
		}&#xD;
		else {&#xD;
			noOfOriginals[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfOriginals + "";&#xD;
		}&#xD;
		if (tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == "" || tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies == null) {&#xD;
			noOfCopies[docsCount] = "";&#xD;
		}&#xD;
		else {&#xD;
			noOfCopies[docsCount] = tw.local.odcRequest.attachmentDetails.attachment[i].numOfCopies + "";&#xD;
		}&#xD;
		docsCount++;&#xD;
&#xD;
	}&#xD;
//	else{&#xD;
//		continue;&#xD;
//	}&#xD;
}&#xD;
if(docsCount&gt;0){&#xD;
	tw.local.paramsValues.put("intr001", documentNames);&#xD;
	tw.local.paramsValues.put("intr002", noOfOriginals);&#xD;
	tw.local.paramsValues.put("intr003", noOfCopies);&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
//tw.local.paramsValues.put("intr001",specialInstructions);&#xD;
//tw.local.paramsValues.put("intr002",specialInstructions);&#xD;
//tw.local.paramsValues.put("intr003",specialInstructions);&#xD;
&#xD;
tw.local.request.paramsValues = tw.local.paramsValues;&#xD;
//--------------------------Generate Docs-------------------&#xD;
var template = new tw.object.RequestTemplatePojo();&#xD;
template.secure = true;&#xD;
template.ecmDocInfo = ecmInfo;&#xD;
template.templateCode = "ODCRemittanceLetter";&#xD;
template.generatedDocName = "remittance Letter";&#xD;
tw.local.request.templates.insertIntoList(tw.local.request.templates.listLength, template);&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="df1ded4c-8bfd-44d8-8f0f-b837239503ce" targetRef="7044d967-7568-4a9e-8f73-a0143e928089" name="To Get template names" id="456da453-0ff0-48d2-8993-02260083c2ad">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a4275847-1a37-4d3c-9289-0462d5afbca9" isCollection="true" name="properties" id="2056.79ad9803-626f-4181-8c3c-770f087a21ec">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject[0].objectTypeId = "";
autoObject[0].value = null;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="templateName" id="2056.d77cfee1-b5f8-48c8-8413-9f4deccdd93a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.90c5b1d3-3fa1-4b3b-ab27-220b0652dc55" isCollection="false" name="paramsValues" id="2056.88fc3cd0-f6cb-465e-8f19-9802902cea33" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063" isCollection="false" name="request" id="2056.1d5f3808-737b-48d5-8993-0aef345100bd" />
                    
                    
                    <ns16:serviceTask xmlns:ns23="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/240e7a4d-19e1-4926-9cf2-a3e2de0ef705Service" operationRef="ns23:_e9ecfd4b-aab3-4f5c-bc29-ef180d409eed" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Generate Document" id="7044d967-7568-4a9e-8f73-a0143e928089">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="859" y="54" width="95" height="70" />
                            
                            
                            <ns4:activityType>ServiceTask</ns4:activityType>
                            
                            
                            <ns3:externalServiceRefPO>1.7f097f90-cf8f-43fa-875f-f106fe9cac70</ns3:externalServiceRefPO>
                            
                            
                            <ns3:externalServiceRef>ns23:_4f57dabd-7522-45c1-a6cb-ae30e4578c97</ns3:externalServiceRef>
                            
                            
                            <ns3:preAssignmentScript>log.info(" ServiceName : Document Generation: START");</ns3:preAssignmentScript>
                            
                            
                            <ns3:postAssignmentScript>log.info(" ServiceName : Document Generation: END");&#xD;
tw.local.docID = tw.local.response.templatesResponse[0].ecmDocName;</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>456da453-0ff0-48d2-8993-02260083c2ad</ns16:incoming>
                        
                        
                        <ns16:outgoing>6f0096bd-6183-4c60-81fb-1d5ef2324a64</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>_5f31e534-f82c-4bed-8b97-f4b50bb07fb8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063">tw.local.request</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>_66fa2129-ae2c-47d6-aa59-b4f60f4a6c96</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc">tw.local.response</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:serviceTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="7044d967-7568-4a9e-8f73-a0143e928089" targetRef="3a8ae9f4-a4b5-4024-863f-a0b0817b39c7" name="To End" id="6f0096bd-6183-4c60-81fb-1d5ef2324a64">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc" isCollection="false" name="response" id="2056.8194bbbc-8a6e-43a4-865f-51db9a4c83ec" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" name="folderID" id="2056.47435375-900d-463a-8170-bb2c5ac59b17" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exception Handling" id="56d069f7-8b98-48b1-800b-dd3a134083b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="712" y="311" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6945970e-2c78-4a60-8fcd-6be760d2a3f6</ns16:incoming>
                        
                        
                        <ns16:outgoing>4a81ac30-2f20-4653-83e0-0b0c4aa45d0c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Generate Remittance Letter"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="56d069f7-8b98-48b1-800b-dd3a134083b1" targetRef="bc8b682b-6442-40c7-88cc-2da045c87a52" name="To End" id="4a81ac30-2f20-4653-83e0-0b0c4aa45d0c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="078c569a-3fac-4dfa-8c82-6d494c69d3de" name="regenerate?" id="c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="314" y="73" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.2521cec4-3685-4925-848f-52ab82439a5d</ns16:incoming>
                        
                        
                        <ns16:outgoing>078c569a-3fac-4dfa-8c82-6d494c69d3de</ns16:outgoing>
                        
                        
                        <ns16:outgoing>0b7e9890-ebed-4616-8d5c-037bbf967812</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc" targetRef="b4ca5615-841a-4b96-8f71-17df1c09d08e" name="no" id="078c569a-3fac-4dfa-8c82-6d494c69d3de">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc" targetRef="13c3f7c0-c045-4e71-88f4-4a56be3e590d" name="yes" id="0b7e9890-ebed-4616-8d5c-037bbf967812">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">!!tw.local.odcRequest.templateDocID</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a" isCollection="true" name="docs" id="2056.56f0e5cd-36e3-4171-8118-3182c75106a1" />
                    
                    
                    <ns4:contentTask serverName="useMappingServer" operationRef="DOC_OP_DELETE_DOCUMENT" name="Delete Doc" id="13c3f7c0-c045-4e71-88f4-4a56be3e590d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="401" y="185" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0b7e9890-ebed-4616-8d5c-037bbf967812</ns16:incoming>
                        
                        
                        <ns16:outgoing>e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"FileNet"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>ALL_VERSIONS</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">true</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>DOCUMENT_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.templateDocID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="13c3f7c0-c045-4e71-88f4-4a56be3e590d" targetRef="b4ca5615-841a-4b96-8f71-17df1c09d08e" name="To Get Doc Gen Temp Folder" id="e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="13c3f7c0-c045-4e71-88f4-4a56be3e590d" parallelMultiple="false" name="Error" id="21c155e3-195c-40b3-891c-9ec39b9f93b7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="436" y="243" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>6945970e-2c78-4a60-8fcd-6be760d2a3f6</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="98eb20b4-0d3e-401c-8b7f-d01d8b88083b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="f1a42aaa-61ef-46c9-88ea-255111151e90" eventImplId="4e7012cf-723f-425a-8175-9f1a6178fd44">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="21c155e3-195c-40b3-891c-9ec39b9f93b7" targetRef="56d069f7-8b98-48b1-800b-dd3a134083b1" name="To Exception Handling" id="6945970e-2c78-4a60-8fcd-6be760d2a3f6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="bc8b682b-6442-40c7-88cc-2da045c87a52">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="935" y="408" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4a81ac30-2f20-4653-83e0-0b0c4aa45d0c</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="e75eccda-e8f4-41d4-8c9b-d3c6e4538dc0" eventImplId="e1f379d2-8f3b-4915-826a-0cf9bb01de4c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0b7e9890-ebed-4616-8d5c-037bbf967812</processLinkId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</fromProcessItemId>
            <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5f12</endStateId>
            <toProcessItemId>2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d</toProcessItemId>
            <guid>5c3a7dc0-72fa-4141-a258-58770550d98c</guid>
            <versionId>52e72bfa-196d-4634-987a-556fe858a01f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</fromProcessItemId>
            <toProcessItemId>2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d</toProcessItemId>
        </link>
        <link name="To Script Task1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4c44551e-24bb-4ce9-8c6c-b0e99527615b</processLinkId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce</toProcessItemId>
            <guid>0ff28d29-cbaf-4f7e-96f2-a16a4afdb98f</guid>
            <versionId>60acb62e-f508-40e4-930e-2674bbd4fedc</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</fromProcessItemId>
            <toProcessItemId>2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce</toProcessItemId>
        </link>
        <link name="no">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.078c569a-3fac-4dfa-8c82-6d494c69d3de</processLinkId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</toProcessItemId>
            <guid>0aa9dfe9-f931-495d-97f3-a71405cb53b0</guid>
            <versionId>7566527a-056d-4770-8350-467ddc617531</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c0a984ec-948f-4ad1-8fff-ebb7c51d4fbc</fromProcessItemId>
            <toProcessItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4a81ac30-2f20-4653-83e0-0b0c4aa45d0c</processLinkId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.56d069f7-8b98-48b1-800b-dd3a134083b1</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.bc8b682b-6442-40c7-88cc-2da045c87a52</toProcessItemId>
            <guid>8a867ed1-6d7f-409b-961d-9b7e78218b91</guid>
            <versionId>76d28167-6255-4a6c-9679-325898f9ecf8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.56d069f7-8b98-48b1-800b-dd3a134083b1</fromProcessItemId>
            <toProcessItemId>2025.bc8b682b-6442-40c7-88cc-2da045c87a52</toProcessItemId>
        </link>
        <link name="To Get template names">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.456da453-0ff0-48d2-8993-02260083c2ad</processLinkId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7044d967-7568-4a9e-8f73-a0143e928089</toProcessItemId>
            <guid>38d9cf2c-75a3-4dc1-a5a0-8604d9049b41</guid>
            <versionId>7935028e-efff-44b5-92dd-bbe643dd5bad</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.df1ded4c-8bfd-44d8-8f0f-b837239503ce</fromProcessItemId>
            <toProcessItemId>2025.7044d967-7568-4a9e-8f73-a0143e928089</toProcessItemId>
        </link>
        <link name="To Get Doc Gen Temp Folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e4f3eff1-0fc1-45f5-8f48-a4e863ef33e6</processLinkId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</toProcessItemId>
            <guid>a5a79dbf-df6e-4745-acf6-5d06afb365dc</guid>
            <versionId>c39fe154-b0b1-49b9-bf8b-5584860a018b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.13c3f7c0-c045-4e71-88f4-4a56be3e590d</fromProcessItemId>
            <toProcessItemId>2025.b4ca5615-841a-4b96-8f71-17df1c09d08e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6f0096bd-6183-4c60-81fb-1d5ef2324a64</processLinkId>
            <processId>1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7044d967-7568-4a9e-8f73-a0143e928089</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3a8ae9f4-a4b5-4024-863f-a0b0817b39c7</toProcessItemId>
            <guid>80cb1eed-dbea-4108-a901-3c6cedf9dd53</guid>
            <versionId>d1bc8998-1466-4ef1-bfcc-58ca62aa6e6b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7044d967-7568-4a9e-8f73-a0143e928089</fromProcessItemId>
            <toProcessItemId>2025.3a8ae9f4-a4b5-4024-863f-a0b0817b39c7</toProcessItemId>
        </link>
    </process>
</teamworks>

