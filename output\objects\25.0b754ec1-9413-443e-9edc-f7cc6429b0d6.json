{"id": "25.0b754ec1-9413-443e-9edc-f7cc6429b0d6", "versionId": "7af263f8-ec28-4d86-9715-bda0c6db59f3", "name": "ODC Collection تسجيل حصائل على مستند تحصيل تصدير", "type": "bpd", "typeName": "Business Process Definition", "details": {}, "_fullObjectData": {"teamworks": {"bpd": {"id": "25.0b754ec1-9413-443e-9edc-f7cc6429b0d6", "name": "ODC Collection تسجيل حصائل على مستند تحصيل تصدير", "bpdParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "bpdParameterId": "2007.c2d7852f-6525-4794-8231-2b0bfd65a374", "bpdId": "25.0b754ec1-9413-443e-9edc-f7cc6429b0d6", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "0", "documentation": "", "hasDefault": "false", "defaultValue": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject", "isReadOnly": "false", "guid": "7205fa59-a9c0-4a8e-a035-ffd5a6a3e118", "versionId": "abdf3c33-8c22-44c0-9179-73b8f5da8bf6"}, {"name": "routingDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "bpdParameterId": "2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d", "bpdId": "25.0b754ec1-9413-443e-9edc-f7cc6429b0d6", "parameterType": "1", "isArrayOf": "false", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "seq": "1", "documentation": "", "hasDefault": "false", "defaultValue": "var autoObject = new tw.object.odcRoutingDetails();\r\nautoObject.hubCode = \"\";\r\nautoObject.branchCode = \"\";\r\nautoObject.initiatorUser = \"\";\r\nautoObject.branchName = \"\";\r\nautoObject.hubName = \"\";\r\nautoObject.branchSeq = \"\";\r\nautoObject", "isReadOnly": "false", "guid": "ac65c700-c0c6-4fa7-a197-a566d88c934d", "versionId": "57309e69-e3ac-42ad-9748-96e3aa7a3806"}], "lastModified": "*************", "lastModifiedBy": "mohamed.reda", "bpdId": "25.0b754ec1-9413-443e-9edc-f7cc6429b0d6", "isTrackingEnabled": "true", "isSpcEnabled": "false", "restrictedName": {"isNull": "true"}, "isCriticalPathEnabled": "false", "participantRef": {"isNull": "true"}, "businessDataParticipantRef": {"isNull": "true"}, "perfMetricParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6", "ownerTeamParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e", "timeScheduleType": {"isNull": "true"}, "timeScheduleName": "NBEWork", "timeScheduleExpression": {"isNull": "true"}, "holidayScheduleType": {"isNull": "true"}, "holidayScheduleName": "NBEHoliday", "holidayScheduleExpression": {"isNull": "true"}, "timezoneType": {"isNull": "true"}, "timezone": "Africa/Cairo", "timezoneExpression": {"isNull": "true"}, "internalName": {"isNull": "true"}, "description": "<html><body>-<span style=\"white-space:pre\">\t</span>This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.</body></html>", "type": "1", "rootBpdId": {"isNull": "true"}, "parentBpdId": {"isNull": "true"}, "parentFlowObjectId": {"isNull": "true"}, "xmlData": {"isNull": "true"}, "bpmn2Data": "<ns15:definitions xmlns:ns15=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:ns2=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup\" xmlns:ns3=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process\" xmlns:ns4=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle\" xmlns:ns5=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case\" xmlns:ns6=\"http://www.ibm.com/bpm/Extensions\" xmlns:ns7=\"http://www.ibm.com/xmlns/prod/bpm/uca\" xmlns:ns8=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team\" xmlns:ns9=\"http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary\" xmlns:ns10=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:ns11=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:ns12=\"http://www.omg.org/spec/BPMN/20100524/BPMNDI\" xmlns:ns13=\"http://www.ibm.com/xmlns/prod/bpm/graph\" xmlns:ns14=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice\" xmlns:ns16=\"http://www.ibm.com/bpm/processappsettings\" xmlns:ns17=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth\" xmlns:ns18=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns19=\"http://www.ibm.com/xmlns/links\" xmlns:ns20=\"http://www.ibm.com/bpm/uitheme\" xmlns:ns21=\"http://www.ibm.com/bpm/coachview\" xmlns:ns22=\"http://www.ibm.com/xmlns/tagging\" id=\"e72e3272-05a7-40e4-90d3-3f1b77bcd85c\" targetNamespace=\"\" expressionLanguage=\"http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript\"><ns15:process name=\"ODC Collection تسجيل حصائل على مستند تحصيل تصدير\" id=\"25.0b754ec1-9413-443e-9edc-f7cc6429b0d6\" ns3:executionMode=\"longRunning\"><ns15:documentation textFormat=\"text/plain\">-&lt;span style=\"white-space:pre\"&gt;\t&lt;/span&gt;This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.</ns15:documentation><ns15:extensionElements><ns4:bpdExtension instanceName=\"&quot; Request Type: &quot;+ tw.local.odcRequest.requestType.value +&quot; CIF : &quot;+ tw.local.odcRequest.cif +&quot; Request Number: &quot;+ tw.system.process.instanceId\" dueDateEnabled=\"false\" atRiskCalcEnabled=\"false\" enableTracking=\"true\" allowProjectedPathManagement=\"false\" optimizeExecForLatency=\"false\" sBOSyncEnabled=\"true\" allowContentOperations=\"false\" autoTrackingEnabled=\"false\" autoTrackingName=\"at1691923319746\"><ns4:dueDateSettings type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">8</ns4:dueDate></ns4:dueDateSettings><ns4:workSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timeScheduleName>NBEWork</ns4:timeScheduleName><ns4:timezoneType>0</ns4:timezoneType><ns4:timezone>Africa/Cairo</ns4:timezone><ns4:holidayScheduleType>0</ns4:holidayScheduleType><ns4:holidayScheduleName>NBEHoliday</ns4:holidayScheduleName></ns4:workSchedule></ns4:bpdExtension><ns5:caseExtension><ns5:caseFolder id=\"9ead2f75-765e-4737-9f97-38a0bf8292d2\" /></ns5:caseExtension><ns5:isConvergedProcess>true</ns5:isConvergedProcess></ns15:extensionElements><ns15:ioSpecification><ns15:extensionElements><ns3:epvProcessLinks><ns3:epvProcessLinkRef epvId=\"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd\" epvProcessLinkId=\"cf5f9c3e-3504-4e3d-85f5-0c9fe51046cb\" /><ns3:epvProcessLinkRef epvId=\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\" epvProcessLinkId=\"09d9adeb-4ea2-421d-809b-290fce12ec80\" /><ns3:epvProcessLinkRef epvId=\"21.00a114f4-2b01-4c48-aad9-bd62580da24b\" epvProcessLinkId=\"0c9a0944-2ae5-405d-85d1-2f6711b8e55d\" /><ns3:epvProcessLinkRef epvId=\"21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c\" epvProcessLinkId=\"3f2fb007-9b49-4d8e-8271-feacc32ead54\" /></ns3:epvProcessLinks></ns15:extensionElements><ns15:dataInput name=\"odcRequest\" itemSubjectRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\" isCollection=\"false\" id=\"2007.c2d7852f-6525-4794-8231-2b0bfd65a374\"><ns15:extensionElements><ns4:searchableField id=\"5d556fee-ab55-426f-8fae-c633625c524d\" alias=\"CustomerCIF\" path=\".CustomerInfo.cif\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"8d013837-0e10-4860-8a09-3c720a4eff7c\" alias=\"CustomerName\" path=\".CustomerInfo.customerName\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"0df39e40-549e-4dfe-8620-b81e23db8442\" alias=\"ParentRequestNumber\" path=\".parentRequestNo\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"156d15c5-3e68-43b1-813f-d4342fd62a41\" alias=\"Initiation\" path=\".initiator\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"d1326d94-db9a-43ca-80f9-ed14f69e5b5b\" alias=\"RequestDate\" path=\".appInfo.requestDate\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"2bf36b4f-823f-4d6c-81bb-c850cb059591\" alias=\"RequestStatus\" path=\".appInfo.status\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"f5e084bc-df19-47f8-8e4e-a849582d1912\" alias=\"initiatorUsername\" path=\".appInfo.initiator\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns3:defaultValue useDefault=\"false\">var autoObject = new tw.object.ODCRequest();\r\r\nautoObject.initiator = \"\";\r\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.requestNature.name = \"\";\r\r\nautoObject.requestNature.value = \"\";\r\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.requestType.name = \"\";\r\r\nautoObject.requestType.value = \"\";\r\r\nautoObject.cif = \"\";\r\r\nautoObject.customerName = \"\";\r\r\nautoObject.parentRequestNo = \"\";\r\r\nautoObject.requestDate = new TWDate();\r\r\nautoObject.ImporterName = \"\";\r\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\r\nautoObject.appInfo.requestDate = \"\";\r\r\nautoObject.appInfo.status = \"\";\r\r\nautoObject.appInfo.subStatus = \"\";\r\r\nautoObject.appInfo.initiator = \"\";\r\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.appInfo.branch.name = \"\";\r\r\nautoObject.appInfo.branch.value = \"\";\r\r\nautoObject.appInfo.requestName = \"\";\r\r\nautoObject.appInfo.requestType = \"\";\r\r\nautoObject.appInfo.stepName = \"\";\r\r\nautoObject.appInfo.appRef = \"\";\r\r\nautoObject.appInfo.appID = \"\";\r\r\nautoObject.appInfo.instanceID = \"\";\r\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\r\nautoObject.CustomerInfo.cif = \"\";\r\r\nautoObject.CustomerInfo.customerName = \"\";\r\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\r\nautoObject.CustomerInfo.customerType = \"\";\r\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\r\nautoObject.CustomerInfo.country = \"\";\r\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\r\nautoObject.BasicDetails.requestNature = \"\";\r\r\nautoObject.BasicDetails.requestType = \"\";\r\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\r\nautoObject.BasicDetails.requestState = \"\";\r\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\r\nautoObject.BasicDetails.contractStage = \"\";\r\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.currency.name = \"\";\r\r\nautoObject.FcCollections.currency.value = \"\";\r\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\r\nautoObject.FcCollections.fromDate = new TWDate();\r\r\nautoObject.FcCollections.ToDate = new TWDate();\r\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.accountNo.name = \"\";\r\r\nautoObject.FcCollections.accountNo.value = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\nautoObject.FcCollections.isReversed = false;\r\r\nautoObject.FcCollections.usedAmount = 0.0;\r\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\r\nautoObject.ImporterDetails.importerName = \"\";\r\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\r\nautoObject.ImporterDetails.bank = \"\";\r\r\nautoObject.ImporterDetails.BICCode = \"\";\r\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\r\nautoObject.OdcCollection.amount = 0.0;\r\r\nautoObject.OdcCollection.currency = \"\";\r\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\r\nautoObject.ReversalReason.reversalReason = \"\";\r\r\nautoObject.ReversalReason.closureReason = \"\";\r\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ContractCreation.productCode.name = \"\";\r\r\nautoObject.ContractCreation.productCode.value = \"\";\r\r\nautoObject.ContractCreation.productDescription = \"\";\r\r\nautoObject.ContractCreation.Stage = \"\";\r\r\nautoObject.ContractCreation.userReference = \"\";\r\r\nautoObject.ContractCreation.sourceReference = \"\";\r\r\nautoObject.ContractCreation.currency = \"\";\r\r\nautoObject.ContractCreation.amount = 0.0;\r\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\r\nautoObject.ContractCreation.tenorDays = 0;\r\r\nautoObject.ContractCreation.transitDays = 0;\r\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\r\nautoObject.Parties = new tw.object.odcParties();\r\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\r\nautoObject.Parties.Drawer.partyId = \"\";\r\r\nautoObject.Parties.Drawer.partyName = \"\";\r\r\nautoObject.Parties.Drawer.country = \"\";\r\r\nautoObject.Parties.Drawer.Language = \"\";\r\r\nautoObject.Parties.Drawer.Reference = \"\";\r\r\nautoObject.Parties.Drawer.address1 = \"\";\r\r\nautoObject.Parties.Drawer.address2 = \"\";\r\r\nautoObject.Parties.Drawer.address3 = \"\";\r\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\r\nautoObject.Parties.Drawee.partyId = \"\";\r\r\nautoObject.Parties.Drawee.partyName = \"\";\r\r\nautoObject.Parties.Drawee.country = \"\";\r\r\nautoObject.Parties.Drawee.Language = \"\";\r\r\nautoObject.Parties.Drawee.Reference = \"\";\r\r\nautoObject.Parties.Drawee.address1 = \"\";\r\r\nautoObject.Parties.Drawee.address2 = \"\";\r\r\nautoObject.Parties.Drawee.address3 = \"\";\r\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\r\nautoObject.Parties.collectingBank.id = \"\";\r\r\nautoObject.Parties.collectingBank.name = \"\";\r\r\nautoObject.Parties.collectingBank.country = \"\";\r\r\nautoObject.Parties.collectingBank.language = \"\";\r\r\nautoObject.Parties.collectingBank.reference = \"\";\r\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\r\nautoObject.Parties.collectingBank.cif = \"\";\r\r\nautoObject.Parties.collectingBank.media = \"\";\r\r\nautoObject.Parties.collectingBank.address = \"\";\r\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\r\nautoObject.Parties.partyTypes.country = \"\";\r\r\nautoObject.Parties.partyTypes.language = \"\";\r\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\r\nautoObject.complianceApproval = false;\r\r\nautoObject.stepLog = new tw.object.StepLog();\r\r\nautoObject.stepLog.startTime = new TWDate();\r\r\nautoObject.stepLog.endTime = new TWDate();\r\r\nautoObject.stepLog.userName = \"\";\r\r\nautoObject.stepLog.role = \"\";\r\r\nautoObject.stepLog.step = \"\";\r\r\nautoObject.stepLog.action = \"\";\r\r\nautoObject.stepLog.comment = \"\";\r\r\nautoObject.stepLog.terminateReason = \"\";\r\r\nautoObject.stepLog.returnReason = \"\";\r\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.actions[0] = \"\";\r\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\r\nautoObject.attachmentDetails.folderID = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\r\nautoObject.complianceComments[0].userName = \"\";\r\r\nautoObject.complianceComments[0].role = \"\";\r\r\nautoObject.complianceComments[0].step = \"\";\r\r\nautoObject.complianceComments[0].action = \"\";\r\r\nautoObject.complianceComments[0].comment = \"\";\r\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\r\nautoObject.complianceComments[0].returnReason = \"\";\r\r\nautoObject.History = new tw.object.listOf.StepLog();\r\r\nautoObject.History[0] = new tw.object.StepLog();\r\r\nautoObject.History[0].startTime = new TWDate();\r\r\nautoObject.History[0].endTime = new TWDate();\r\r\nautoObject.History[0].userName = \"\";\r\r\nautoObject.History[0].role = \"\";\r\r\nautoObject.History[0].step = \"\";\r\r\nautoObject.History[0].action = \"\";\r\r\nautoObject.History[0].comment = \"\";\r\r\nautoObject.History[0].terminateReason = \"\";\r\r\nautoObject.History[0].returnReason = \"\";\r\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.documentSource.name = \"\";\r\r\nautoObject.documentSource.value = \"\";\r\r\nautoObject.folderID = \"\";\r\r\nautoObject.isLiquidated = false;\r\r\nautoObject</ns3:defaultValue></ns15:extensionElements></ns15:dataInput><ns15:dataInput name=\"routingDetails\" itemSubjectRef=\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\" isCollection=\"false\" id=\"2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d\"><ns15:extensionElements><ns3:defaultValue useDefault=\"false\">var autoObject = new tw.object.odcRoutingDetails();\r\r\nautoObject.hubCode = \"\";\r\r\nautoObject.branchCode = \"\";\r\r\nautoObject.initiatorUser = \"\";\r\r\nautoObject.branchName = \"\";\r\r\nautoObject.hubName = \"\";\r\r\nautoObject.branchSeq = \"\";\r\r\nautoObject</ns3:defaultValue></ns15:extensionElements></ns15:dataInput><ns15:inputSet /><ns15:inputSet id=\"_7a4d5a0f-1097-4489-bf6f-ea6a7a28d4a4\" /><ns15:outputSet /><ns15:outputSet id=\"_3d42305c-af64-476f-add2-628c418da958\" /></ns15:ioSpecification><ns15:laneSet id=\"fc92c62a-eb7e-4a04-b3bb-104f7fc11c37\"><ns15:lane name=\"Hub Maker\" partitionElementRef=\"24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce\" id=\"2f34dc88-0eda-48b1-82cb-03faf20f7fbd\" ns4:isSystemLane=\"false\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"0\" y=\"0\" width=\"1011\" height=\"200\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:flowNodeRef>2300d98d-7703-498f-a179-da81156caffd</ns15:flowNodeRef><ns15:flowNodeRef>6c34a494-03cd-45bc-aa32-0c51125d02b3</ns15:flowNodeRef><ns15:flowNodeRef>aec4fe46-5179-4bf0-830d-b2197bde09b3</ns15:flowNodeRef><ns15:flowNodeRef>65373fd5-a6e7-471c-89f3-9914a767f490</ns15:flowNodeRef><ns15:flowNodeRef>578cd29e-eb65-4611-887c-ca89e350bbe0</ns15:flowNodeRef></ns15:lane><ns15:lane name=\"Hub Checker\" partitionElementRef=\"24.8e005024-3fe0-4848-8c3c-f1e9483900c6\" id=\"cbfe586a-2d9a-4152-b545-72306e0bfe17\" ns4:isSystemLane=\"false\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"0\" y=\"201\" width=\"1011\" height=\"200\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:flowNodeRef>3bbe7551-846d-4a0d-88b3-cbeee15bc65b</ns15:flowNodeRef><ns15:flowNodeRef>8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f</ns15:flowNodeRef><ns15:flowNodeRef>4548b365-e059-4b60-88e6-963bd9bdb102</ns15:flowNodeRef><ns15:flowNodeRef>e3ff4aa9-775b-4509-9e93-4b2a929de6ad</ns15:flowNodeRef><ns15:flowNodeRef>6b353da2-4799-4357-8635-1d550dcb5798</ns15:flowNodeRef></ns15:lane><ns15:lane name=\"System\" partitionElementRef=\"24.da7e4d23-78cb-4483-98ed-b9c238308a03\" id=\"5cc9debc-a01c-46df-82f3-935c3a9ca390\" ns4:isSystemLane=\"false\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"0\" y=\"402\" width=\"1011\" height=\"262\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:flowNodeRef>b8ccb671-17d0-4597-8b78-b1c858672409</ns15:flowNodeRef><ns15:flowNodeRef>e3080c20-6d98-42f1-85ae-ccf62f692b6e</ns15:flowNodeRef><ns15:flowNodeRef>426ad1b2-fd3f-4430-81c1-9d1d0664c795</ns15:flowNodeRef><ns15:flowNodeRef>d6e38de3-cffd-4f49-8793-11fd93a0c977</ns15:flowNodeRef><ns15:flowNodeRef>31deeeb5-4f4b-450e-892b-519f89b6ba64</ns15:flowNodeRef></ns15:lane></ns15:laneSet><ns15:startEvent name=\"Start\" id=\"2300d98d-7703-498f-a179-da81156caffd\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"35\" y=\"100\" width=\"24\" height=\"24\" color=\"#F8F8F8\" /><ns3:default>40db59ea-ddf4-4f91-ad82-c5dc7ea27829</ns3:default></ns15:extensionElements><ns15:outgoing>40db59ea-ddf4-4f91-ad82-c5dc7ea27829</ns15:outgoing></ns15:startEvent><ns15:endEvent name=\"End Authorize\" id=\"e3ff4aa9-775b-4509-9e93-4b2a929de6ad\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"862\" y=\"45\" width=\"24\" height=\"24\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:incoming>55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c</ns15:incoming></ns15:endEvent><ns15:callActivity calledElement=\"1.6255f65f-d7ce-452d-9058-3f856ea792e0\" default=\"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527\" name=\"Create ODC Collection Request \" id=\"6c34a494-03cd-45bc-aa32-0c51125d02b3\"><ns15:extensionElements><ns4:activityExtension conditional=\"false\" transactionalBehavior=\"NotSet\"><ns4:conditionScript /></ns4:activityExtension><ns13:nodeVisualInfo x=\"236\" y=\"77\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:narrative>&lt;#= tw.epv.Col_ScreenNames.ODCCol01 #&gt;  &lt;#= tw.system.process.instanceId #&gt;</ns4:narrative><ns4:subject>Create ODC Collection Request – طلب تسجيل حصائل على مستند تحصيل تصدير&lt;#= tw.system.process.instanceId #&gt;</ns4:subject><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">parseFloat(tw.epv.Col_SLA.SLA_Act01)</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>Africa/Cairo</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timeScheduleName>NBEWork</ns4:timeScheduleName><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType><ns4:holidayScheduleName>NBEHoliday</ns4:holidayScheduleName></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>UserTask</ns4:activityType></ns15:extensionElements><ns15:incoming>e65b9e5f-0fff-4bfc-8899-7b8266e14e83</ns15:incoming><ns15:incoming>40db59ea-ddf4-4f91-ad82-c5dc7ea27829</ns15:incoming><ns15:outgoing>c3a3c0bd-8021-4f3b-8182-ffc4b24b2527</ns15:outgoing><ns15:dataInputAssociation><ns15:targetRef>2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.fb57a244-f263-444b-8dc2-734946370a33</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\">tw.local.routingDetails</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.parentPath</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.115324d0-8168-495d-8187-b6a3c2434105</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.0b50a9fc-1f55-4a3b-84b9-18de25fe1bac</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\">tw.local.customerAccounts</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.f0a5931f-96f0-4329-81d0-83ef90e6192f</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\">tw.local.customerAccounts</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.parentPath</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns4:activityPerformer distribution=\"LastUser\" name=\"Lane\"><ns4:teamFilterService serviceRef=\"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9\"><ns15:dataInputAssociation><ns15:targetRef>2055.a76b6fae-3a5c-469c-85df-3d71dc308f87</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.loggedInUser</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.b95f0910-f183-4dab-9065-28297a14ceef</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_MKR\"</ns15:from></ns15:assignment></ns15:dataInputAssociation></ns4:teamFilterService></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:sequenceFlow sourceRef=\"2300d98d-7703-498f-a179-da81156caffd\" targetRef=\"6c34a494-03cd-45bc-aa32-0c51125d02b3\" name=\"To Create ODC Collection Request \" id=\"40db59ea-ddf4-4f91-ad82-c5dc7ea27829\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"65373fd5-a6e7-471c-89f3-9914a767f490\" targetRef=\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\" name=\"No\" id=\"********-401c-4a45-9acf-2dc238bc3950\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" fireValidation=\"Never\" /><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:callActivity calledElement=\"1.429675b0-3048-4910-8b11-e3d3eb0cd480\" default=\"afb60f23-0dec-4ee4-8ef0-07590f68ca18\" name=\"Review ODC Collection Request\" id=\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\"><ns15:extensionElements><ns4:activityExtension conditional=\"false\" transactionalBehavior=\"NotSet\"><ns4:conditionScript /></ns4:activityExtension><ns13:nodeVisualInfo x=\"598\" y=\"48\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:narrative>&lt;#= tw.epv.Col_ScreenNames.ODCCol02 #&gt;  &lt;#= tw.system.process.instanceId #&gt;</ns4:narrative><ns4:subject>Review ODC Collection Request</ns4:subject><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">parseFloat(tw.epv.Col_SLA.SLA_Act02)</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>Africa/Cairo</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timeScheduleName>NBEWork</ns4:timeScheduleName><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType><ns4:holidayScheduleName>NBEHoliday</ns4:holidayScheduleName></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>UserTask</ns4:activityType></ns15:extensionElements><ns15:incoming>********-401c-4a45-9acf-2dc238bc3950</ns15:incoming><ns15:incoming>2d535ad4-ad69-4293-850f-e7ea81aeccb3</ns15:incoming><ns15:incoming>b6c3d9ed-93d3-4698-8888-547b82966e13</ns15:incoming><ns15:outgoing>afb60f23-0dec-4ee4-8ef0-07590f68ca18</ns15:outgoing><ns15:dataInputAssociation><ns15:targetRef>2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\">tw.local.customerAccounts</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.parentPath</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.568ffb88-c172-4fff-841d-2e47908795cb</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns4:activityPerformer distribution=\"None\" name=\"Lane\"><ns4:teamFilterService serviceRef=\"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9\"><ns15:dataInputAssociation><ns15:targetRef>2055.b95f0910-f183-4dab-9065-28297a14ceef</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_CHKR\"</ns15:from></ns15:assignment></ns15:dataInputAssociation></ns4:teamFilterService></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:exclusiveGateway default=\"5a8d21ed-0bb8-4366-824e-f6e661620491\" name=\"Check Action\" id=\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"703\" y=\"84\" width=\"32\" height=\"32\" /></ns15:extensionElements><ns15:incoming>afb60f23-0dec-4ee4-8ef0-07590f68ca18</ns15:incoming><ns15:outgoing>5a8d21ed-0bb8-4366-824e-f6e661620491</ns15:outgoing><ns15:outgoing>55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c</ns15:outgoing><ns15:outgoing>e65b9e5f-0fff-4bfc-8899-7b8266e14e83</ns15:outgoing></ns15:exclusiveGateway><ns15:sequenceFlow sourceRef=\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\" targetRef=\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\" name=\"To Check Action\" id=\"afb60f23-0dec-4ee4-8ef0-07590f68ca18\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>rightBottom</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:endEvent name=\"End Cancel\" id=\"4548b365-e059-4b60-88e6-963bd9bdb102\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"871\" y=\"101\" width=\"24\" height=\"24\" /></ns15:extensionElements><ns15:incoming>5a8d21ed-0bb8-4366-824e-f6e661620491</ns15:incoming></ns15:endEvent><ns15:sequenceFlow sourceRef=\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\" targetRef=\"4548b365-e059-4b60-88e6-963bd9bdb102\" name=\"Cancel\" id=\"5a8d21ed-0bb8-4366-824e-f6e661620491\"><ns15:extensionElements><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" /><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\" targetRef=\"e3ff4aa9-775b-4509-9e93-4b2a929de6ad\" name=\"Authorize\" id=\"55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c\"><ns15:extensionElements><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" /><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements><ns15:conditionExpression xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.authorize</ns15:conditionExpression></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\" targetRef=\"6c34a494-03cd-45bc-aa32-0c51125d02b3\" name=\"Return to initiator\" id=\"e65b9e5f-0fff-4bfc-8899-7b8266e14e83\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation><ns13:targetPortLocation>rightTop</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements><ns15:conditionExpression xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToMaker</ns15:conditionExpression></ns15:sequenceFlow><ns15:dataObject itemSubjectRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" isCollection=\"false\" name=\"taskId\" id=\"389ebfab-ad7c-45f7-80b1-7bcacb85f82c\" /><ns15:boundaryEvent cancelActivity=\"false\" attachedToRef=\"6c34a494-03cd-45bc-aa32-0c51125d02b3\" parallelMultiple=\"false\" name=\"Timer/Create ODC Collection Request \" id=\"aec4fe46-5179-4bf0-830d-b2197bde09b3\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"297\" y=\"135\" width=\"24\" height=\"24\" /><ns3:default>411f55c7-efea-401d-83fa-637a1a614dcc</ns3:default><ns3:preAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.taskId</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.system.step.task.id</ns15:to></ns3:preAssignment></ns15:extensionElements><ns15:outgoing>411f55c7-efea-401d-83fa-637a1a614dcc</ns15:outgoing><ns15:timerEventDefinition id=\"797540ef-74fd-40a7-830b-b21dd47dc747\" eventImplId=\"0c1429b6-50f2-4f11-8a12-4bad07e715ba\"><ns15:extensionElements><ns4:timerEventSettings><ns4:dateType>DueDate</ns4:dateType><ns4:relativeDirection>AfterDueDate</ns4:relativeDirection><ns4:relativeTime>0</ns4:relativeTime><ns4:relativeTimeResolution>Hours</ns4:relativeTimeResolution><ns4:toleranceInterval>0</ns4:toleranceInterval><ns4:toleranceIntervalResolution>Hours</ns4:toleranceIntervalResolution><ns4:useCalendar>false</ns4:useCalendar></ns4:timerEventSettings></ns15:extensionElements></ns15:timerEventDefinition></ns15:boundaryEvent><ns15:callActivity calledElement=\"1.d7acf968-6740-4e52-b037-2049466eeeb2\" default=\"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1\" name=\"Send Escalation Mail\" id=\"b8ccb671-17d0-4597-8b78-b1c858672409\"><ns15:extensionElements><ns4:deleteTaskOnCompletion>true</ns4:deleteTaskOnCompletion><ns13:nodeVisualInfo x=\"501\" y=\"112\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">1</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>(use default)</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>ServiceTask</ns4:activityType><ns4:activityExtension conditional=\"false\"><ns4:conditionScript /></ns4:activityExtension></ns15:extensionElements><ns15:incoming>411f55c7-efea-401d-83fa-637a1a614dcc</ns15:incoming><ns15:incoming>a45be2a3-5a13-4ad5-8503-1b96f538d134</ns15:incoming><ns15:outgoing>0ccb44e3-2a5f-45c4-885e-74c915e4b2a1</ns15:outgoing><ns15:dataInputAssociation><ns15:targetRef>2055.9771d7e8-ca59-430e-8b1a-194cf04c1182</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.mailTo</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.taskId</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns4:activityPerformer distribution=\"None\" name=\"Lane\"><ns4:teamFilterService /></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:sequenceFlow sourceRef=\"aec4fe46-5179-4bf0-830d-b2197bde09b3\" targetRef=\"b8ccb671-17d0-4597-8b78-b1c858672409\" name=\"To Send Escalation Mail\" id=\"411f55c7-efea-401d-83fa-637a1a614dcc\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftTop</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:boundaryEvent cancelActivity=\"false\" attachedToRef=\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\" parallelMultiple=\"false\" name=\"Timer/Review ODC Collection Request\" id=\"6b353da2-4799-4357-8635-1d550dcb5798\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"659\" y=\"106\" width=\"24\" height=\"24\" /><ns3:default>a45be2a3-5a13-4ad5-8503-1b96f538d134</ns3:default><ns3:preAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.taskId</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.system.step.task.id</ns15:to></ns3:preAssignment></ns15:extensionElements><ns15:outgoing>a45be2a3-5a13-4ad5-8503-1b96f538d134</ns15:outgoing><ns15:timerEventDefinition id=\"97cbc8f0-bf40-4c43-808f-09f05b5d6869\" eventImplId=\"730b13f1-e3ca-4b97-8d5e-c15b872f4ab3\"><ns15:extensionElements><ns4:timerEventSettings><ns4:dateType>DueDate</ns4:dateType><ns4:relativeDirection>AfterDueDate</ns4:relativeDirection><ns4:relativeTime>0</ns4:relativeTime><ns4:relativeTimeResolution>Hours</ns4:relativeTimeResolution><ns4:toleranceInterval>0</ns4:toleranceInterval><ns4:toleranceIntervalResolution>Hours</ns4:toleranceIntervalResolution><ns4:useCalendar>false</ns4:useCalendar></ns4:timerEventSettings></ns15:extensionElements></ns15:timerEventDefinition></ns15:boundaryEvent><ns15:sequenceFlow sourceRef=\"6b353da2-4799-4357-8635-1d550dcb5798\" targetRef=\"b8ccb671-17d0-4597-8b78-b1c858672409\" name=\"To System Task1\" id=\"a45be2a3-5a13-4ad5-8503-1b96f538d134\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>rightTop</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:endEvent name=\"End Event1\" id=\"e3080c20-6d98-42f1-85ae-ccf62f692b6e\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"718\" y=\"202\" width=\"24\" height=\"24\" /></ns15:extensionElements><ns15:incoming>528cd2ca-6545-4614-8638-a383622bd1e2</ns15:incoming><ns15:incoming>0ccb44e3-2a5f-45c4-885e-74c915e4b2a1</ns15:incoming></ns15:endEvent><ns15:dataObject itemSubjectRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" isCollection=\"false\" name=\"mailTo\" id=\"662a0040-1dd6-4b44-8213-26b31d166e45\" /><ns15:dataObject itemSubjectRef=\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\" isCollection=\"true\" name=\"customerAccounts\" id=\"47aab0e9-13a2-4912-8526-d7424de98556\" /><ns15:callActivity calledElement=\"1.a6205b87-57cc-47bb-abfc-edff0743b08e\" isForCompensation=\"false\" startQuantity=\"1\" completionQuantity=\"1\" default=\"2d535ad4-ad69-4293-850f-e7ea81aeccb3\" name=\"Send CAD Team Mail\" id=\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\"><ns15:extensionElements><ns4:deleteTaskOnCompletion>true</ns4:deleteTaskOnCompletion><ns13:nodeVisualInfo x=\"499\" y=\"22\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">1</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>(use default)</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>ServiceTask</ns4:activityType><ns4:activityExtension conditional=\"false\"><ns4:conditionScript /></ns4:activityExtension><ns3:preAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.mailTo</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.epv.Mails.CadTeam</ns15:to></ns3:preAssignment></ns15:extensionElements><ns15:incoming>407b35e3-4142-4a62-86b0-c68ffa25d1fb</ns15:incoming><ns15:outgoing>2d535ad4-ad69-4293-850f-e7ea81aeccb3</ns15:outgoing><ns15:dataInputAssociation><ns15:targetRef>2055.8510e459-4375-44f2-afb0-c0b98b03ee89</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.b4c53a07-42dc-4fe2-995e-23d8719e647e</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.taskId</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.mailTo</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns4:activityPerformer distribution=\"None\" name=\"Lane\"><ns4:teamFilterService /></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:exclusiveGateway default=\"********-401c-4a45-9acf-2dc238bc3950\" name=\"Inform CAD Team?\" id=\"65373fd5-a6e7-471c-89f3-9914a767f490\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"431\" y=\"114\" width=\"32\" height=\"32\" /></ns15:extensionElements><ns15:incoming>c3a3c0bd-8021-4f3b-8182-ffc4b24b2527</ns15:incoming><ns15:outgoing>********-401c-4a45-9acf-2dc238bc3950</ns15:outgoing><ns15:outgoing>407b35e3-4142-4a62-86b0-c68ffa25d1fb</ns15:outgoing></ns15:exclusiveGateway><ns15:sequenceFlow sourceRef=\"6c34a494-03cd-45bc-aa32-0c51125d02b3\" targetRef=\"65373fd5-a6e7-471c-89f3-9914a767f490\" name=\"To Inform CAD Team?\" id=\"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>rightBottom</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\" targetRef=\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\" name=\"To Review ODC Collection Request\" id=\"2d535ad4-ad69-4293-850f-e7ea81aeccb3\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"65373fd5-a6e7-471c-89f3-9914a767f490\" targetRef=\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\" name=\"Yes\" id=\"407b35e3-4142-4a62-86b0-c68ffa25d1fb\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftTop</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements><ns15:conditionExpression xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.odcRequest.OdcCollection.informCADAboutTheCollection</ns15:conditionExpression></ns15:sequenceFlow><ns15:boundaryEvent cancelActivity=\"true\" attachedToRef=\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\" parallelMultiple=\"false\" name=\"Error\" id=\"d6e38de3-cffd-4f49-8793-11fd93a0c977\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"534\" y=\"10\" width=\"24\" height=\"24\" /><ns3:default>b6c3d9ed-93d3-4698-8888-547b82966e13</ns3:default></ns15:extensionElements><ns15:outgoing>b6c3d9ed-93d3-4698-8888-547b82966e13</ns15:outgoing><ns15:dataOutput name=\"error\" itemSubjectRef=\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\" isCollection=\"false\" id=\"5852ca58-901b-44b7-84c8-3319037215af\" /><ns15:outputSet /><ns15:errorEventDefinition id=\"a2c6e895-55bc-4f40-877c-b4a06daf693e\" eventImplId=\"06abad98-ee10-430a-8af6-46e4bdda4792\"><ns15:extensionElements><ns4:errorEventSettings><ns4:catchAll>true</ns4:catchAll></ns4:errorEventSettings></ns15:extensionElements></ns15:errorEventDefinition></ns15:boundaryEvent><ns15:sequenceFlow sourceRef=\"d6e38de3-cffd-4f49-8793-11fd93a0c977\" targetRef=\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\" name=\"To Review ODC Collection Request\" id=\"b6c3d9ed-93d3-4698-8888-547b82966e13\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation><ns13:targetPortLocation>bottomLeft</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:boundaryEvent cancelActivity=\"true\" attachedToRef=\"b8ccb671-17d0-4597-8b78-b1c858672409\" parallelMultiple=\"false\" name=\"Error1\" id=\"31deeeb5-4f4b-450e-892b-519f89b6ba64\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"510\" y=\"170\" width=\"24\" height=\"24\" /><ns3:default>528cd2ca-6545-4614-8638-a383622bd1e2</ns3:default></ns15:extensionElements><ns15:outgoing>528cd2ca-6545-4614-8638-a383622bd1e2</ns15:outgoing><ns15:dataOutput name=\"error\" itemSubjectRef=\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\" isCollection=\"false\" id=\"ff3cb5ff-fef1-4f10-8f95-37fc667590ce\" /><ns15:outputSet /><ns15:errorEventDefinition id=\"42aecbb1-bc82-42f9-876d-17d2f2e842b2\" eventImplId=\"31536ba9-7809-4977-82eb-35b7f98a9826\"><ns15:extensionElements><ns4:errorEventSettings><ns4:catchAll>true</ns4:catchAll></ns4:errorEventSettings></ns15:extensionElements></ns15:errorEventDefinition></ns15:boundaryEvent><ns15:sequenceFlow sourceRef=\"31deeeb5-4f4b-450e-892b-519f89b6ba64\" targetRef=\"e3080c20-6d98-42f1-85ae-ccf62f692b6e\" name=\"To End Event1\" id=\"528cd2ca-6545-4614-8638-a383622bd1e2\"><ns15:extensionElements><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" /><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"b8ccb671-17d0-4597-8b78-b1c858672409\" targetRef=\"e3080c20-6d98-42f1-85ae-ccf62f692b6e\" name=\"To End Event1\" id=\"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1\"><ns15:extensionElements><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" /><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>topCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:dataObject itemSubjectRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" isCollection=\"false\" name=\"parentPath\" id=\"c8bac588-62b1-43ea-8239-3bb0bb495357\" /><ns15:callActivity calledElement=\"1.46cfd991-7ee5-4ce2-887a-8b21634e7e95\" name=\"User Task\" id=\"578cd29e-eb65-4611-887c-ca89e350bbe0\"><ns15:extensionElements><ns3:activityAdHocSettings repeatable=\"false\" hidden=\"false\" triggerType=\"Automatic\" option=\"Required\" /><ns13:nodeVisualInfo x=\"794\" y=\"77\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">1</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>(use default)</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>UserTask</ns4:activityType><ns3:activityPreconditions triggerType=\"NoPreconditions\" documentTriggerMode=\"External\" /></ns15:extensionElements><ns4:activityPerformer distribution=\"None\" name=\"Lane\"><ns4:teamFilterService /></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:resourceRole name=\"participantRef\" /><ns15:resourceRole name=\"businessDataParticipantRef\" /><ns15:resourceRole name=\"perfMetricParticipantRef\"><ns15:resourceRef>24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</ns15:resourceRef></ns15:resourceRole><ns15:resourceRole name=\"ownerTeamParticipantRef\"><ns15:resourceRef>24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ns15:resourceRef></ns15:resourceRole></ns15:process><ns15:interface name=\"ODC CollectionInterface\" id=\"_c6b2b3b4-6f3c-41d6-8c41-5ee7b96b4b86\" /></ns15:definitions>\r\r\n", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"40db59ea-ddf4-4f91-ad82-c5dc7ea27829\"],\"isInterrupting\":true,\"extensionElements\":{\"default\":[\"40db59ea-ddf4-4f91-ad82-c5dc7ea27829\"],\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":35,\"y\":100,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"2300d98d-7703-498f-a179-da81156caffd\"},{\"incoming\":[\"55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":862,\"y\":45,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Authorize\",\"declaredType\":\"endEvent\",\"id\":\"e3ff4aa9-775b-4509-9e93-4b2a929de6ad\"},{\"outgoing\":[\"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527\"],\"incoming\":[\"e65b9e5f-0fff-4bfc-8899-7b8266e14e83\",\"40db59ea-ddf4-4f91-ad82-c5dc7ea27829\"],\"extensionElements\":{\"activityExtension\":[{\"conditionScript\":\"\",\"conditional\":false,\"transactionalBehavior\":\"NotSet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension\"}],\"nodeVisualInfo\":[{\"width\":95,\"x\":236,\"y\":77,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"subject\":\"Create ODC Collection Request \\u2013 \\u0637\\u0644\\u0628 \\u062a\\u0633\\u062c\\u064a\\u0644 \\u062d\\u0635\\u0627\\u0626\\u0644 \\u0639\\u0644\\u0649 \\u0645\\u0633\\u062a\\u0646\\u062f \\u062a\\u062d\\u0635\\u064a\\u0644 \\u062a\\u0635\\u062f\\u064a\\u0631<#= tw.system.process.instanceId #>\",\"narrative\":\"<#= tw.epv.Col_ScreenNames.ODCCol01 #>  <#= tw.system.process.instanceId #>\",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"timeScheduleName\":\"NBEWork\",\"holidayScheduleName\":\"NBEHoliday\",\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"parseFloat(tw.epv.Col_SLA.SLA_Act01)\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"Africa\\/Cairo\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"UserTask\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"LastUser\",\"teamFilterService\":{\"dataInputAssociation\":[{\"targetRef\":\"2055.a76b6fae-3a5c-469c-85df-3d71dc308f87\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.loggedInUser\"]}}]},{\"targetRef\":\"2055.b95f0910-f183-4dab-9065-28297a14ceef\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"BPM_IDC_HUB_\\\"+tw.local.routingDetails.hubCode+\\\"_EXE_MKR\\\"\"]}}]}],\"serviceRef\":\"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9\"}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"default\":\"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527\",\"name\":\"Create ODC Collection Request \",\"dataInputAssociation\":[{\"targetRef\":\"2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.fb57a244-f263-444b-8dc2-734946370a33\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails\"]}}]},{\"targetRef\":\"2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"6c34a494-03cd-45bc-aa32-0c51125d02b3\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.115324d0-8168-495d-8187-b6a3c2434105\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerAccounts\"]}}],\"sourceRef\":[\"2055.0b50a9fc-1f55-4a3b-84b9-18de25fe1bac\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerAccounts\"]}}],\"sourceRef\":[\"2055.f0a5931f-96f0-4329-81d0-83ef90e6192f\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}],\"sourceRef\":[\"2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4\"]}],\"calledElement\":\"1.6255f65f-d7ce-452d-9058-3f856ea792e0\"},{\"targetRef\":\"6c34a494-03cd-45bc-aa32-0c51125d02b3\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Create ODC Collection Request \",\"declaredType\":\"sequenceFlow\",\"id\":\"40db59ea-ddf4-4f91-ad82-c5dc7ea27829\",\"sourceRef\":\"2300d98d-7703-498f-a179-da81156caffd\"},{\"targetRef\":\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"********-401c-4a45-9acf-2dc238bc3950\",\"sourceRef\":\"65373fd5-a6e7-471c-89f3-9914a767f490\"},{\"outgoing\":[\"afb60f23-0dec-4ee4-8ef0-07590f68ca18\"],\"incoming\":[\"********-401c-4a45-9acf-2dc238bc3950\",\"2d535ad4-ad69-4293-850f-e7ea81aeccb3\",\"b6c3d9ed-93d3-4698-8888-547b82966e13\"],\"extensionElements\":{\"activityExtension\":[{\"conditionScript\":\"\",\"conditional\":false,\"transactionalBehavior\":\"NotSet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension\"}],\"nodeVisualInfo\":[{\"width\":95,\"x\":598,\"y\":48,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"subject\":\"Review ODC Collection Request\",\"narrative\":\"<#= tw.epv.Col_ScreenNames.ODCCol02 #>  <#= tw.system.process.instanceId #>\",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"timeScheduleName\":\"NBEWork\",\"holidayScheduleName\":\"NBEHoliday\",\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"parseFloat(tw.epv.Col_SLA.SLA_Act02)\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"Africa\\/Cairo\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"UserTask\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{\"dataInputAssociation\":[{\"targetRef\":\"2055.b95f0910-f183-4dab-9065-28297a14ceef\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"BPM_IDC_HUB_\\\"+tw.local.routingDetails.hubCode+\\\"_EXE_CHKR\\\"\"]}}]}],\"serviceRef\":\"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9\"}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"default\":\"afb60f23-0dec-4ee4-8ef0-07590f68ca18\",\"name\":\"Review ODC Collection Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerAccounts\"]}}]},{\"targetRef\":\"2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.568ffb88-c172-4fff-841d-2e47908795cb\"]}],\"calledElement\":\"1.429675b0-3048-4910-8b11-e3d3eb0cd480\"},{\"outgoing\":[\"5a8d21ed-0bb8-4366-824e-f6e661620491\",\"55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c\",\"e65b9e5f-0fff-4bfc-8899-7b8266e14e83\"],\"incoming\":[\"afb60f23-0dec-4ee4-8ef0-07590f68ca18\"],\"default\":\"5a8d21ed-0bb8-4366-824e-f6e661620491\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":703,\"y\":84,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Check Action\",\"declaredType\":\"exclusiveGateway\",\"id\":\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\"},{\"targetRef\":\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightBottom\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Check Action\",\"declaredType\":\"sequenceFlow\",\"id\":\"afb60f23-0dec-4ee4-8ef0-07590f68ca18\",\"sourceRef\":\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\"},{\"incoming\":[\"5a8d21ed-0bb8-4366-824e-f6e661620491\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":871,\"y\":101,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Cancel\",\"declaredType\":\"endEvent\",\"id\":\"4548b365-e059-4b60-88e6-963bd9bdb102\"},{\"targetRef\":\"4548b365-e059-4b60-88e6-963bd9bdb102\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"Cancel\",\"declaredType\":\"sequenceFlow\",\"id\":\"5a8d21ed-0bb8-4366-824e-f6e661620491\",\"sourceRef\":\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\"},{\"targetRef\":\"e3ff4aa9-775b-4509-9e93-4b2a929de6ad\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.authorize\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"Authorize\",\"declaredType\":\"sequenceFlow\",\"id\":\"55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c\",\"sourceRef\":\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\"},{\"targetRef\":\"6c34a494-03cd-45bc-aa32-0c51125d02b3\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.returnToMaker\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"Return to initiator\",\"declaredType\":\"sequenceFlow\",\"id\":\"e65b9e5f-0fff-4bfc-8899-7b8266e14e83\",\"sourceRef\":\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"taskId\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"389ebfab-ad7c-45f7-80b1-7bcacb85f82c\"},{\"parallelMultiple\":false,\"outgoing\":[\"411f55c7-efea-401d-83fa-637a1a614dcc\"],\"eventDefinition\":[{\"extensionElements\":{\"timerEventSettings\":[{\"relativeTime\":\"0\",\"relativeTimeResolution\":\"Hours\",\"dateType\":\"DueDate\",\"toleranceInterval\":\"0\",\"useCalendar\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings\",\"toleranceIntervalResolution\":\"Hours\",\"relativeDirection\":\"AfterDueDate\"}]},\"declaredType\":\"timerEventDefinition\",\"id\":\"797540ef-74fd-40a7-830b-b21dd47dc747\",\"otherAttributes\":{\"eventImplId\":\"0c1429b6-50f2-4f11-8a12-4bad07e715ba\"}}],\"attachedToRef\":\"6c34a494-03cd-45bc-aa32-0c51125d02b3\",\"extensionElements\":{\"default\":[\"411f55c7-efea-401d-83fa-637a1a614dcc\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":297,\"y\":135,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"preAssignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.step.task.id\"]}}]},\"cancelActivity\":false,\"name\":\"Timer\\/Create ODC Collection Request \",\"declaredType\":\"boundaryEvent\",\"id\":\"aec4fe46-5179-4bf0-830d-b2197bde09b3\"},{\"outgoing\":[\"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1\"],\"incoming\":[\"411f55c7-efea-401d-83fa-637a1a614dcc\",\"a45be2a3-5a13-4ad5-8503-1b96f538d134\"],\"extensionElements\":{\"activityExtension\":[{\"conditionScript\":\"\",\"conditional\":false,\"transactionalBehavior\":\"NotSet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension\"}],\"deleteTaskOnCompletion\":[true],\"nodeVisualInfo\":[{\"width\":95,\"x\":501,\"y\":112,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"1\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"(use default)\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"ServiceTask\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"default\":\"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1\",\"name\":\"Send Escalation Mail\",\"dataInputAssociation\":[{\"targetRef\":\"2055.9771d7e8-ca59-430e-8b1a-194cf04c1182\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailTo\"]}}]},{\"targetRef\":\"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"b8ccb671-17d0-4597-8b78-b1c858672409\",\"calledElement\":\"1.d7acf968-6740-4e52-b037-2049466eeeb2\"},{\"targetRef\":\"b8ccb671-17d0-4597-8b78-b1c858672409\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Send Escalation Mail\",\"declaredType\":\"sequenceFlow\",\"id\":\"411f55c7-efea-401d-83fa-637a1a614dcc\",\"sourceRef\":\"aec4fe46-5179-4bf0-830d-b2197bde09b3\"},{\"parallelMultiple\":false,\"outgoing\":[\"a45be2a3-5a13-4ad5-8503-1b96f538d134\"],\"eventDefinition\":[{\"extensionElements\":{\"timerEventSettings\":[{\"relativeTime\":\"0\",\"relativeTimeResolution\":\"Hours\",\"dateType\":\"DueDate\",\"toleranceInterval\":\"0\",\"useCalendar\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings\",\"toleranceIntervalResolution\":\"Hours\",\"relativeDirection\":\"AfterDueDate\"}]},\"declaredType\":\"timerEventDefinition\",\"id\":\"97cbc8f0-bf40-4c43-808f-09f05b5d6869\",\"otherAttributes\":{\"eventImplId\":\"730b13f1-e3ca-4b97-8d5e-c15b872f4ab3\"}}],\"attachedToRef\":\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\",\"extensionElements\":{\"default\":[\"a45be2a3-5a13-4ad5-8503-1b96f538d134\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":659,\"y\":106,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"preAssignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.step.task.id\"]}}]},\"cancelActivity\":false,\"name\":\"Timer\\/Review ODC Collection Request\",\"declaredType\":\"boundaryEvent\",\"id\":\"6b353da2-4799-4357-8635-1d550dcb5798\"},{\"targetRef\":\"b8ccb671-17d0-4597-8b78-b1c858672409\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To System Task1\",\"declaredType\":\"sequenceFlow\",\"id\":\"a45be2a3-5a13-4ad5-8503-1b96f538d134\",\"sourceRef\":\"6b353da2-4799-4357-8635-1d550dcb5798\"},{\"incoming\":[\"528cd2ca-6545-4614-8638-a383622bd1e2\",\"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":718,\"y\":202,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event1\",\"declaredType\":\"endEvent\",\"id\":\"e3080c20-6d98-42f1-85ae-ccf62f692b6e\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"mailTo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"662a0040-1dd6-4b44-8213-26b31d166e45\"},{\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"customerAccounts\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"47aab0e9-13a2-4912-8526-d7424de98556\"},{\"outgoing\":[\"2d535ad4-ad69-4293-850f-e7ea81aeccb3\"],\"incoming\":[\"407b35e3-4142-4a62-86b0-c68ffa25d1fb\"],\"extensionElements\":{\"activityExtension\":[{\"conditionScript\":\"\",\"conditional\":false,\"transactionalBehavior\":\"NotSet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension\"}],\"deleteTaskOnCompletion\":[true],\"nodeVisualInfo\":[{\"width\":95,\"x\":499,\"y\":22,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailTo\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.Mails.CadTeam\"]}}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"1\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"(use default)\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"ServiceTask\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"default\":\"2d535ad4-ad69-4293-850f-e7ea81aeccb3\",\"name\":\"Send CAD Team Mail\",\"dataInputAssociation\":[{\"targetRef\":\"2055.8510e459-4375-44f2-afb0-c0b98b03ee89\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.b4c53a07-42dc-4fe2-995e-23d8719e647e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]}}]},{\"targetRef\":\"2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailTo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\",\"calledElement\":\"1.a6205b87-57cc-47bb-abfc-edff0743b08e\"},{\"outgoing\":[\"********-401c-4a45-9acf-2dc238bc3950\",\"407b35e3-4142-4a62-86b0-c68ffa25d1fb\"],\"incoming\":[\"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527\"],\"default\":\"********-401c-4a45-9acf-2dc238bc3950\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":431,\"y\":114,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Inform CAD Team?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"65373fd5-a6e7-471c-89f3-9914a767f490\"},{\"targetRef\":\"65373fd5-a6e7-471c-89f3-9914a767f490\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightBottom\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Inform CAD Team?\",\"declaredType\":\"sequenceFlow\",\"id\":\"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527\",\"sourceRef\":\"6c34a494-03cd-45bc-aa32-0c51125d02b3\"},{\"targetRef\":\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Review ODC Collection Request\",\"declaredType\":\"sequenceFlow\",\"id\":\"2d535ad4-ad69-4293-850f-e7ea81aeccb3\",\"sourceRef\":\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\"},{\"targetRef\":\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.OdcCollection.informCADAboutTheCollection\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"407b35e3-4142-4a62-86b0-c68ffa25d1fb\",\"sourceRef\":\"65373fd5-a6e7-471c-89f3-9914a767f490\"},{\"parallelMultiple\":false,\"outgoing\":[\"b6c3d9ed-93d3-4698-8888-547b82966e13\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"5852ca58-901b-44b7-84c8-3319037215af\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"a2c6e895-55bc-4f40-877c-b4a06daf693e\",\"otherAttributes\":{\"eventImplId\":\"06abad98-ee10-430a-8af6-46e4bdda4792\"}}],\"attachedToRef\":\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\",\"extensionElements\":{\"default\":[\"b6c3d9ed-93d3-4698-8888-547b82966e13\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":534,\"y\":10,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"d6e38de3-cffd-4f49-8793-11fd93a0c977\",\"outputSet\":{}},{\"targetRef\":\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Review ODC Collection Request\",\"declaredType\":\"sequenceFlow\",\"id\":\"b6c3d9ed-93d3-4698-8888-547b82966e13\",\"sourceRef\":\"d6e38de3-cffd-4f49-8793-11fd93a0c977\"},{\"parallelMultiple\":false,\"outgoing\":[\"528cd2ca-6545-4614-8638-a383622bd1e2\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"ff3cb5ff-fef1-4f10-8f95-37fc667590ce\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"42aecbb1-bc82-42f9-876d-17d2f2e842b2\",\"otherAttributes\":{\"eventImplId\":\"31536ba9-7809-4977-82eb-35b7f98a9826\"}}],\"attachedToRef\":\"b8ccb671-17d0-4597-8b78-b1c858672409\",\"extensionElements\":{\"default\":[\"528cd2ca-6545-4614-8638-a383622bd1e2\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":510,\"y\":170,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"31deeeb5-4f4b-450e-892b-519f89b6ba64\",\"outputSet\":{}},{\"targetRef\":\"e3080c20-6d98-42f1-85ae-ccf62f692b6e\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To End Event1\",\"declaredType\":\"sequenceFlow\",\"id\":\"528cd2ca-6545-4614-8638-a383622bd1e2\",\"sourceRef\":\"31deeeb5-4f4b-450e-892b-519f89b6ba64\"},{\"targetRef\":\"e3080c20-6d98-42f1-85ae-ccf62f692b6e\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To End Event1\",\"declaredType\":\"sequenceFlow\",\"id\":\"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1\",\"sourceRef\":\"b8ccb671-17d0-4597-8b78-b1c858672409\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"c8bac588-62b1-43ea-8239-3bb0bb495357\"},{\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"extensionElements\":{\"activityAdHocSettings\":[{\"hidden\":false,\"repeatable\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityAdHocSettings\",\"triggerType\":\"Automatic\",\"option\":\"Required\"}],\"nodeVisualInfo\":[{\"width\":95,\"x\":794,\"y\":77,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"1\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"(use default)\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"UserTask\"],\"activityPreconditions\":[{\"documentTriggerMode\":\"External\",\"sourceFolderReferenceType\":\"FolderId\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityPreconditions\",\"triggerType\":\"NoPreconditions\",\"matchAll\":true}]},\"name\":\"User Task\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"578cd29e-eb65-4611-887c-ca89e350bbe0\",\"calledElement\":\"1.46cfd991-7ee5-4ce2-887a-8b21634e7e95\"}],\"laneSet\":[{\"id\":\"fc92c62a-eb7e-4a04-b3bb-104f7fc11c37\",\"lane\":[{\"flowNodeRef\":[\"2300d98d-7703-498f-a179-da81156caffd\",\"6c34a494-03cd-45bc-aa32-0c51125d02b3\",\"aec4fe46-5179-4bf0-830d-b2197bde09b3\",\"65373fd5-a6e7-471c-89f3-9914a767f490\",\"578cd29e-eb65-4611-887c-ca89e350bbe0\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":1011,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":200}]},\"name\":\"Hub Maker\",\"partitionElementRef\":\"24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce\",\"declaredType\":\"lane\",\"id\":\"2f34dc88-0eda-48b1-82cb-03faf20f7fbd\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"false\"}},{\"flowNodeRef\":[\"3bbe7551-846d-4a0d-88b3-cbeee15bc65b\",\"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f\",\"4548b365-e059-4b60-88e6-963bd9bdb102\",\"e3ff4aa9-775b-4509-9e93-4b2a929de6ad\",\"6b353da2-4799-4357-8635-1d550dcb5798\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":1011,\"x\":0,\"y\":201,\"declaredType\":\"TNodeVisualInfo\",\"height\":200}]},\"name\":\"Hub Checker\",\"partitionElementRef\":\"24.8e005024-3fe0-4848-8c3c-f1e9483900c6\",\"declaredType\":\"lane\",\"id\":\"cbfe586a-2d9a-4152-b545-72306e0bfe17\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"false\"}},{\"flowNodeRef\":[\"b8ccb671-17d0-4597-8b78-b1c858672409\",\"e3080c20-6d98-42f1-85ae-ccf62f692b6e\",\"426ad1b2-fd3f-4430-81c1-9d1d0664c795\",\"d6e38de3-cffd-4f49-8793-11fd93a0c977\",\"31deeeb5-4f4b-450e-892b-519f89b6ba64\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":1011,\"x\":0,\"y\":402,\"declaredType\":\"TNodeVisualInfo\",\"height\":262}]},\"name\":\"System\",\"partitionElementRef\":\"24.da7e4d23-78cb-4483-98ed-b9c238308a03\",\"declaredType\":\"lane\",\"id\":\"5cc9debc-a01c-46df-82f3-935c3a9ca390\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"false\"}}]}],\"resourceRole\":[{\"name\":\"participantRef\",\"declaredType\":\"resourceRole\"},{\"name\":\"businessDataParticipantRef\",\"declaredType\":\"resourceRole\"},{\"resourceRef\":\"24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6\",\"name\":\"perfMetricParticipantRef\",\"declaredType\":\"resourceRole\"},{\"resourceRef\":\"24.48d9e8e1-2b51-4173-ac71-9a6c533d134e\",\"name\":\"ownerTeamParticipantRef\",\"declaredType\":\"resourceRole\"}],\"isClosed\":false,\"extensionElements\":{\"bpdExtension\":[{\"allowContentOperations\":false,\"enableTracking\":true,\"workSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"timezone\":\"Africa\\/Cairo\",\"timeScheduleName\":\"NBEWork\",\"holidayScheduleName\":\"NBEHoliday\",\"holidayScheduleType\":0},\"instanceName\":\"\\\" Request Type: \\\"+ tw.local.odcRequest.requestType.value +\\\" CIF : \\\"+ tw.local.odcRequest.cif +\\\" Request Number: \\\"+ tw.system.process.instanceId\",\"dueDateSettings\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"8\",\"timeOfDay\":\"00:00\"},\"type\":\"TimeCalculation\"},\"autoTrackingName\":\"at1691923319746\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension\",\"optimizeExecForLatency\":false,\"dueDateEnabled\":false,\"atRiskCalcEnabled\":false,\"allowProjectedPathManagement\":false,\"autoTrackingEnabled\":false,\"sboSyncEnabled\":true}],\"caseExtension\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension\",\"caseFolder\":{\"allowSubfoldersCreation\":false,\"allowLocalDoc\":false,\"id\":\"9ead2f75-765e-4737-9f97-38a0bf8292d2\",\"allowExternalFolder\":false,\"allowExternalDoc\":false}}],\"isConvergedProcess\":[true]},\"documentation\":[{\"content\":[\"-<span style=\\\"white-space:pre\\\">\\t<\\/span>This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"ODC Collection \\u062a\\u0633\\u062c\\u064a\\u0644 \\u062d\\u0635\\u0627\\u0626\\u0644 \\u0639\\u0644\\u0649 \\u0645\\u0633\\u062a\\u0646\\u062f \\u062a\\u062d\\u0635\\u064a\\u0644 \\u062a\\u0635\\u062f\\u064a\\u0631\",\"declaredType\":\"process\",\"id\":\"25.0b754ec1-9413-443e-9edc-f7cc6429b0d6\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"longRunning\"},\"ioSpecification\":{\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd\",\"epvProcessLinkId\":\"cf5f9c3e-3504-4e3d-85f5-0c9fe51046cb\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"09d9adeb-4ea2-421d-809b-290fce12ec80\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.00a114f4-2b01-4c48-aad9-bd62580da24b\",\"epvProcessLinkId\":\"0c9a0944-2ae5-405d-85d1-2f6711b8e55d\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c\",\"epvProcessLinkId\":\"3f2fb007-9b49-4d8e-8271-feacc32ead54\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{},{\"id\":\"_7a4d5a0f-1097-4489-bf6f-ea6a7a28d4a4\"}],\"outputSet\":[{},{\"id\":\"_3d42305c-af64-476f-add2-628c418da958\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject\"}],\"searchableField\":[{\"path\":\".CustomerInfo.cif\",\"alias\":\"CustomerCIF\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"5d556fee-ab55-426f-8fae-c633625c524d\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".CustomerInfo.customerName\",\"alias\":\"CustomerName\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"8d013837-0e10-4860-8a09-3c720a4eff7c\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".parentRequestNo\",\"alias\":\"ParentRequestNumber\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"0df39e40-549e-4dfe-8620-b81e23db8442\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".initiator\",\"alias\":\"Initiation\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"156d15c5-3e68-43b1-813f-d4342fd62a41\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".appInfo.requestDate\",\"alias\":\"RequestDate\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"d1326d94-db9a-43ca-80f9-ed14f69e5b5b\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".appInfo.status\",\"alias\":\"RequestStatus\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"2bf36b4f-823f-4d6c-81bb-c850cb059591\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".appInfo.initiator\",\"alias\":\"initiatorUsername\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"f5e084bc-df19-47f8-8e4e-a849582d1912\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2007.c2d7852f-6525-4794-8231-2b0bfd65a374\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.odcRoutingDetails();\\nautoObject.hubCode = \\\"\\\";\\nautoObject.branchCode = \\\"\\\";\\nautoObject.initiatorUser = \\\"\\\";\\nautoObject.branchName = \\\"\\\";\\nautoObject.hubName = \\\"\\\";\\nautoObject.branchSeq = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"name\":\"routingDetails\",\"isCollection\":false,\"id\":\"2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d\"}]}},{\"name\":\"ODC CollectionInterface\",\"declaredType\":\"interface\",\"id\":\"_c6b2b3b4-6f3c-41d6-8c41-5ee7b96b4b86\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"e72e3272-05a7-40e4-90d3-3f1b77bcd85c\"}", "migrationData": {"isNull": "true"}, "rwfData": {"isNull": "true"}, "rwfStatus": {"isNull": "true"}, "templateId": {"isNull": "true"}, "externalId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:374c", "versionId": "7af263f8-ec28-4d86-9715-bda0c6db59f3", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "BusinessProcessDiagram": {"id": "bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-195", "metadata": {"entry": {"key": "SAP_META.SHOULDRECOVER", "value": "no"}}, "name": ["ODC Collection تسجيل حصائل على مستند تحصيل تصدير", "ODC Collection تسجيل حصائل على مستند تحصيل تصدير"], "documentation": "<html><body>-<span style=\"white-space:pre\">\t</span>This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.</body></html>", "dimension": {"size": {"w": "600", "h": "150"}}, "author": "mohamed.reda", "isTrackingEnabled": "true", "isCriticalPathEnabled": "false", "isSpcEnabled": "false", "isDueDateEnabled": "false", "isAtRiskCalcEnabled": "false", "creationDate": "1691923319751", "modificationDate": "*************", "perfMetricParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6", "ownerTeamParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e", "metricSettings": {"itemType": "2"}, "instanceNameExpression": "\" Request Type: \"+ tw.local.odcRequest.requestType.value +\" CIF : \"+ tw.local.odcRequest.cif +\" Request Number: \"+ tw.system.process.instanceId", "dueDateType": "2", "dueDateTime": "8", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "dueDateCustom": "", "officeIntegration": {"sharePointParentSiteDisabled": "true", "sharePointParentSiteName": "<#= tw.system.process.name #>", "sharePointParentSiteTemplate": "ParentSiteTemplate.stp", "sharePointWorkspaceSiteName": "<#= tw.system.process.name #> <#= tw.system.process.instanceId #>", "sharePointWorkspaceSiteDescription": "This site has been automatically generated for managing collaborations and documents \r\nfor the Lombardi TeamWorks process instance: <#= tw.system.process.name #> <#= tw.system.process.instanceId #>\r\n\r\nTeamWorks Link:  http://NBEdevBAW:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=<#= tw.system.process.instanceId #>\r\n\r\n", "sharePointWorkspaceSiteTemplate": "WorkspaceSiteTemplate.stp", "sharePointLCID": "1033"}, "timeScheduleType": "0", "timeScheduleName": "NBEWork", "holidayScheduleName": "NBEHoliday", "holidayScheduleType": "0", "timezone": "Africa/Cairo", "timezoneType": "0", "executionProfile": "default", "isSBOSyncEnabled": "true", "allowContentOperations": "false", "isLegacyCaseMigrated": "false", "hasCaseObjectParams": "false", "defaultPool": {"BpmnObjectId": {"id": "fc92c62a-eb7e-4a04-b3bb-104f7fc11c37"}}, "defaultInstanceUI": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4619"}, "ownerTeamInstanceUI": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-461a"}, "simulationScenario": {"id": "bpdid:6ad7eb4224455a46:11f23e39:189eae0bd83:375f", "name": "<PERSON><PERSON><PERSON>", "simNumInstances": "100", "simMinutesBetween": "30", "maxInstances": "100", "useMaxInstances": "true", "continueFromReal": "false", "useParticipantCalendars": "false", "useDuration": "false", "duration": "86400", "startTime": "1691923320096"}, "flow": [{"id": "a45be2a3-5a13-4ad5-8503-1b96f538d134", "connectionType": "SequenceFlow", "name": "To System Task1", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bb"}}}, {"id": "0ccb44e3-2a5f-45c4-885e-74c915e4b2a1", "connectionType": "SequenceFlow", "name": "To End Event1", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ba"}}}, {"id": "e65b9e5f-0fff-4bfc-8899-7b8266e14e83", "connectionType": "SequenceFlow", "name": "Return to initiator", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cd", "expression": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToMaker"}}}, {"id": "55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c", "connectionType": "SequenceFlow", "name": "Authorize", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cf", "expression": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.authorize"}}}, {"id": "411f55c7-efea-401d-83fa-637a1a614dcc", "connectionType": "SequenceFlow", "name": "To Send Escalation Mail", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b9"}}}, {"id": "5a8d21ed-0bb8-4366-824e-f6e661620491", "connectionType": "SequenceFlow", "name": "Cancel", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b8"}}}, {"id": "afb60f23-0dec-4ee4-8ef0-07590f68ca18", "connectionType": "SequenceFlow", "name": "To Check Action", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b7"}}}, {"id": "b6c3d9ed-93d3-4698-8888-547b82966e13", "connectionType": "SequenceFlow", "name": "To Review ODC Collection Request", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b6"}}}, {"id": "********-401c-4a45-9acf-2dc238bc3950", "connectionType": "SequenceFlow", "name": "No", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b5"}}}, {"id": "c3a3c0bd-8021-4f3b-8182-ffc4b24b2527", "connectionType": "SequenceFlow", "name": "To Inform CAD Team?", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b4"}}}, {"id": "2d535ad4-ad69-4293-850f-e7ea81aeccb3", "connectionType": "SequenceFlow", "name": "To Review ODC Collection Request", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b3"}}}, {"id": "528cd2ca-6545-4614-8638-a383622bd1e2", "connectionType": "SequenceFlow", "name": "To End Event1", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b2"}}}, {"id": "40db59ea-ddf4-4f91-ad82-c5dc7ea27829", "connectionType": "SequenceFlow", "name": "To Create ODC Collection Request ", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b1"}}}, {"id": "407b35e3-4142-4a62-86b0-c68ffa25d1fb", "connectionType": "SequenceFlow", "name": "Yes", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c3", "expression": "tw.local.odcRequest.OdcCollection.informCADAboutTheCollection"}}}], "pool": {"id": "fc92c62a-eb7e-4a04-b3bb-104f7fc11c37", "name": "Pool", "documentation": "<html><body>-<span style=\"white-space:pre\">\t</span>This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.</body></html>", "restrictedName": "at1691923319746", "dimension": {"size": {"w": "3000", "h": "662"}}, "autoTrackingEnabled": "false", "lane": [{"id": "2f34dc88-0eda-48b1-82cb-03faf20f7fbd", "name": "<PERSON><PERSON>", "height": "200", "laneColor": "0", "systemLane": "false", "attachedParticipant": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce", "flowObject": [{"id": "6c34a494-03cd-45bc-aa32-0c51125d02b3", "componentType": "Activity", "name": "Create ODC Collection Request ", "position": {"location": {"x": "236", "y": "77"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "1", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "1", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "narrative": "<#= tw.epv.Col_ScreenNames.ODCCol01 #>  <#= tw.system.process.instanceId #>", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.6255f65f-d7ce-452d-9058-3f856ea792e0", "sendToType": "1", "taskRouting": "3", "dueDateType": "1", "dueDateTime": "parseFloat(tw.epv.Col_SLA.SLA_Act01)", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "subject": "Create ODC Collection Request – طلب تسجيل حصائل على مستند تحصيل تصدير<#= tw.system.process.instanceId #>", "narrative": "<#= tw.epv.Col_ScreenNames.ODCCol01 #>  <#= tw.system.process.instanceId #>", "forceSend": "true", "timeSchedule": "NBEWork", "timeScheduleType": "0", "timeZone": "Africa/Cairo", "timeZoneType": "0", "holidaySchedule": "NBEHoliday", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4605", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4604", "name": "routingDetails", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "input": "true", "value": "tw.local.routingDetails", "parameterId": "2055.fb57a244-f263-444b-8dc2-734946370a33"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4603", "name": "parentPath", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.parentPath", "parameterId": "2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95"}], "outputActivityParameterMapping": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4602", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.115324d0-8168-495d-8187-b6a3c2434105"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4601", "name": "customerAccounts", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "array": "true", "input": "true", "value": "tw.local.customerAccounts", "parameterId": "2055.f0a5931f-96f0-4329-81d0-83ef90e6192f"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4600", "name": "parentPath", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.parentPath", "parameterId": "2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4"}], "laneFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ff", "serviceType": "1", "teamRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce", "attachedActivityId": "/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "inputActivityParameterMapping": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fe", "name": "groupName", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "useDefault": "true", "value": "\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_MKR\"", "parameterId": "2055.b95f0910-f183-4dab-9065-28297a14ceef"}}, "teamFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fd", "serviceType": "1"}}}, "inputPort": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45da", "positionId": "leftCenter", "input": "true", "flow": {"ref": "40db59ea-ddf4-4f91-ad82-c5dc7ea27829"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ce", "positionId": "rightTop", "input": "true", "flow": {"ref": "e65b9e5f-0fff-4bfc-8899-7b8266e14e83"}}], "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c8", "positionId": "rightBottom", "flow": {"ref": "c3a3c0bd-8021-4f3b-8182-ffc4b24b2527"}}, "attachedEvent": {"id": "aec4fe46-5179-4bf0-830d-b2197bde09b3", "componentType": "Event", "name": "Timer/Create ODC Collection Request ", "documentation": "", "position": {"location": {"x": "0", "y": "0"}}, "positionId": "bottomRight", "dropIconUrl": "0", "colorInput": "Color", "component": {"nameVisible": "true", "eventType": "3", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "false", "EventAction": {"id": "797540ef-74fd-40a7-830b-b21dd47dc747", "actionType": "2", "actionSubType": "0", "EventActionImplementation": {"id": "0c1429b6-50f2-4f11-8a12-4bad07e715ba", "dateType": "1", "relativeDirection": "1", "relativeTime": "0", "relativeTimeResolution": "1", "toleranceInterval": "0", "toleranceIntervalResolution": "1", "UseCalendar": "false"}}}, "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cc", "positionId": "bottomCenter", "flow": {"ref": "411f55c7-efea-401d-83fa-637a1a614dcc"}}, "assignment": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e5", "assignTime": "1", "to": "tw.system.step.task.id", "from": "tw.local.taskId"}}}, {"id": "578cd29e-eb65-4611-887c-ca89e350bbe0", "componentType": "Activity", "name": "User Task", "position": {"location": {"x": "794", "y": "77"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "1", "isConditional": "false", "bpmnTaskType": "1", "activityOptionType": "REQUIRED", "activityExecutionType": "AUTOMATIC", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/1.46cfd991-7ee5-4ce2-887a-8b21634e7e95", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "1", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "forceSend": "true", "timeSchedule": "(use default)", "timeScheduleType": "0", "timeZone": "(use default)", "timeZoneType": "0", "holidaySchedule": "(use default)", "holidayScheduleType": "0", "laneFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e7", "serviceType": "1", "teamRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce"}, "teamFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e6", "serviceType": "1"}}, "preconditions": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e9", "triggerType": "0", "matchAll": "true", "documentTriggerMode": "1", "sourceFolderReferenceType": "0"}}}, {"id": "2300d98d-7703-498f-a179-da81156caffd", "componentType": "Event", "name": "Start", "documentation": "", "position": {"location": {"x": "35", "y": "100"}}, "dropIconUrl": "0", "colorInput": "#F8F8F8", "component": {"nameVisible": "true", "eventType": "1", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45db", "positionId": "rightCenter", "flow": {"ref": "40db59ea-ddf4-4f91-ad82-c5dc7ea27829"}}}, {"id": "65373fd5-a6e7-471c-89f3-9914a767f490", "componentType": "Gateway", "name": "Inform CAD Team?", "documentation": "", "position": {"location": {"x": "431", "y": "114"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "gatewayType": "1", "splitJoinType": "0"}, "inputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c7", "positionId": "leftCenter", "input": "true", "flow": {"ref": "c3a3c0bd-8021-4f3b-8182-ffc4b24b2527"}}, "outputPort": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d8", "positionId": "bottomCenter", "flow": {"ref": "407b35e3-4142-4a62-86b0-c68ffa25d1fb"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d9", "positionId": "rightCenter", "flow": {"ref": "********-401c-4a45-9acf-2dc238bc3950"}}]}]}, {"id": "cbfe586a-2d9a-4152-b545-72306e0bfe17", "name": "<PERSON><PERSON>", "height": "200", "laneColor": "0", "systemLane": "false", "attachedParticipant": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6", "flowObject": [{"id": "3bbe7551-846d-4a0d-88b3-cbeee15bc65b", "componentType": "Activity", "name": "Review ODC Collection Request", "position": {"location": {"x": "598", "y": "48"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "1", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "1", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "narrative": "<#= tw.epv.Col_ScreenNames.ODCCol02 #>  <#= tw.system.process.instanceId #>", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.429675b0-3048-4910-8b11-e3d3eb0cd480", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "parseFloat(tw.epv.Col_SLA.SLA_Act02)", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "subject": "Review ODC Collection Request", "narrative": "<#= tw.epv.Col_ScreenNames.ODCCol02 #>  <#= tw.system.process.instanceId #>", "forceSend": "true", "timeSchedule": "NBEWork", "timeScheduleType": "0", "timeZone": "Africa/Cairo", "timeZoneType": "0", "holidaySchedule": "NBEHoliday", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fb", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fa", "name": "customerAccounts", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "array": "true", "input": "true", "value": "tw.local.customerAccounts", "parameterId": "2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f9", "name": "parentPath", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.parentPath", "parameterId": "2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018"}], "outputActivityParameterMapping": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f8", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.568ffb88-c172-4fff-841d-2e47908795cb"}, "laneFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f7", "serviceType": "1", "teamRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6", "attachedActivityId": "/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "inputActivityParameterMapping": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f6", "name": "groupName", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "useDefault": "true", "value": "\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_CHKR\"", "parameterId": "2055.b95f0910-f183-4dab-9065-28297a14ceef"}}, "teamFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f5", "serviceType": "1"}}}, "inputPort": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d7", "positionId": "leftCenter", "input": "true", "flow": {"ref": "********-401c-4a45-9acf-2dc238bc3950"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c5", "positionId": "bottomLeft", "input": "true", "flow": {"ref": "2d535ad4-ad69-4293-850f-e7ea81aeccb3"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c1", "positionId": "bottomLeft", "input": "true", "flow": {"ref": "b6c3d9ed-93d3-4698-8888-547b82966e13"}}], "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d6", "positionId": "rightBottom", "flow": {"ref": "afb60f23-0dec-4ee4-8ef0-07590f68ca18"}}, "attachedEvent": {"id": "6b353da2-4799-4357-8635-1d550dcb5798", "componentType": "Event", "name": "Timer/Review ODC Collection Request", "documentation": "", "position": {"location": {"x": "0", "y": "0"}}, "positionId": "bottomRight", "dropIconUrl": "0", "colorInput": "Color", "component": {"nameVisible": "true", "eventType": "3", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "false", "EventAction": {"id": "97cbc8f0-bf40-4c43-808f-09f05b5d6869", "actionType": "2", "actionSubType": "0", "EventActionImplementation": {"id": "730b13f1-e3ca-4b97-8d5e-c15b872f4ab3", "dateType": "1", "relativeDirection": "1", "relativeTime": "0", "relativeTimeResolution": "1", "toleranceInterval": "0", "toleranceIntervalResolution": "1", "UseCalendar": "false"}}}, "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ca", "positionId": "bottomCenter", "flow": {"ref": "a45be2a3-5a13-4ad5-8503-1b96f538d134"}}, "assignment": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e2", "assignTime": "1", "to": "tw.system.step.task.id", "from": "tw.local.taskId"}}}, {"id": "e3ff4aa9-775b-4509-9e93-4b2a929de6ad", "componentType": "Event", "name": "End Authorize", "documentation": "", "position": {"location": {"x": "862", "y": "45"}}, "dropIconUrl": "0", "colorInput": "#F8F8F8", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d0", "positionId": "leftCenter", "input": "true", "flow": {"ref": "55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c"}}}, {"id": "4548b365-e059-4b60-88e6-963bd9bdb102", "componentType": "Event", "name": "End Cancel", "documentation": "", "position": {"location": {"x": "871", "y": "101"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d1", "positionId": "leftCenter", "input": "true", "flow": {"ref": "5a8d21ed-0bb8-4366-824e-f6e661620491"}}}, {"id": "8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f", "componentType": "Gateway", "name": "Check Action", "documentation": "", "position": {"location": {"x": "703", "y": "84"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "gatewayType": "1", "splitJoinType": "0"}, "inputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d5", "positionId": "leftCenter", "input": "true", "flow": {"ref": "afb60f23-0dec-4ee4-8ef0-07590f68ca18"}}, "outputPort": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d3", "positionId": "rightCenter", "flow": {"ref": "55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d2", "positionId": "topCenter", "flow": {"ref": "e65b9e5f-0fff-4bfc-8899-7b8266e14e83"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d4", "positionId": "rightCenter", "flow": {"ref": "5a8d21ed-0bb8-4366-824e-f6e661620491"}}]}]}, {"id": "5cc9debc-a01c-46df-82f3-935c3a9ca390", "name": "System", "height": "262", "laneColor": "0", "systemLane": "false", "attachedParticipant": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.da7e4d23-78cb-4483-98ed-b9c238308a03", "flowObject": [{"id": "b8ccb671-17d0-4597-8b78-b1c858672409", "componentType": "Activity", "name": "Send Escalation Mail", "documentation": "Escalation mail service: <div>   this service is running when the task has delay to be done </div>", "position": {"location": {"x": "501", "y": "112"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "4", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "3", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.d7acf968-6740-4e52-b037-2049466eeeb2", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "1", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "forceSend": "true", "noTask": "true", "timeSchedule": "(use default)", "timeScheduleType": "0", "timeZone": "(use default)", "timeZoneType": "0", "holidaySchedule": "(use default)", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f3", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "useDefault": "true", "value": "tw.local.odcRequest", "parameterId": "2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f2", "name": "taskId", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "useDefault": "true", "value": "tw.local.taskId", "parameterId": "2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1"}], "laneFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f1", "serviceType": "1", "teamRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.da7e4d23-78cb-4483-98ed-b9c238308a03"}, "teamFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f0", "serviceType": "1"}}}, "inputPort": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cb", "positionId": "leftTop", "input": "true", "flow": {"ref": "411f55c7-efea-401d-83fa-637a1a614dcc"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c9", "positionId": "rightTop", "input": "true", "flow": {"ref": "a45be2a3-5a13-4ad5-8503-1b96f538d134"}}], "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45be", "positionId": "rightCenter", "flow": {"ref": "0ccb44e3-2a5f-45c4-885e-74c915e4b2a1"}}, "attachedEvent": {"id": "31deeeb5-4f4b-450e-892b-519f89b6ba64", "componentType": "Event", "name": "Error1", "documentation": "", "position": {"location": {"x": "0", "y": "0"}}, "positionId": "bottomLeft", "dropIconUrl": "0", "colorInput": "Color", "component": {"nameVisible": "true", "eventType": "3", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true", "EventAction": {"id": "42aecbb1-bc82-42f9-876d-17d2f2e842b2", "actionType": "5", "actionSubType": "0", "EventActionImplementation": {"id": "31536ba9-7809-4977-82eb-35b7f98a9826", "faultStyle": "1", "isCatchAll": "true"}}}, "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c0", "positionId": "bottomCenter", "flow": {"ref": "528cd2ca-6545-4614-8638-a383622bd1e2"}}}}, {"id": "426ad1b2-fd3f-4430-81c1-9d1d0664c795", "componentType": "Activity", "name": "Send CAD Team Mail", "documentation": "Escalation mail service: <div>   this service is running when the task has delay to be done </div>", "position": {"location": {"x": "499", "y": "22"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "4", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "3", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.a6205b87-57cc-47bb-abfc-edff0743b08e", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "1", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "forceSend": "true", "noTask": "true", "timeSchedule": "(use default)", "timeScheduleType": "0", "timeZone": "(use default)", "timeZoneType": "0", "holidaySchedule": "(use default)", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ed", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.8510e459-4375-44f2-afb0-c0b98b03ee89"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ec", "name": "mailTo", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.mailTo", "parameterId": "2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1"}], "laneFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45eb", "serviceType": "1", "teamRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.da7e4d23-78cb-4483-98ed-b9c238308a03"}, "teamFilter": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ea", "serviceType": "1"}}}, "inputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c4", "positionId": "leftTop", "input": "true", "flow": {"ref": "407b35e3-4142-4a62-86b0-c68ffa25d1fb"}}, "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c6", "positionId": "rightCenter", "flow": {"ref": "2d535ad4-ad69-4293-850f-e7ea81aeccb3"}}, "assignment": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ef", "assignTime": "1", "to": "tw.epv.Mails.CadTeam", "from": "tw.local.mailTo"}, "attachedEvent": {"id": "d6e38de3-cffd-4f49-8793-11fd93a0c977", "componentType": "Event", "name": "Error", "documentation": "", "position": {"location": {"x": "0", "y": "0"}}, "positionId": "topCenter", "dropIconUrl": "0", "colorInput": "Color", "component": {"nameVisible": "true", "eventType": "3", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true", "EventAction": {"id": "a2c6e895-55bc-4f40-877c-b4a06daf693e", "actionType": "5", "actionSubType": "0", "EventActionImplementation": {"id": "06abad98-ee10-430a-8af6-46e4bdda4792", "faultStyle": "1", "isCatchAll": "true"}}}, "outputPort": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c2", "positionId": "topCenter", "flow": {"ref": "b6c3d9ed-93d3-4698-8888-547b82966e13"}}}}, {"id": "e3080c20-6d98-42f1-85ae-ccf62f692b6e", "componentType": "Event", "name": "End Event1", "documentation": "", "position": {"location": {"x": "718", "y": "202"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bf", "positionId": "leftCenter", "input": "true", "flow": {"ref": "528cd2ca-6545-4614-8638-a383622bd1e2"}}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bd", "positionId": "topCenter", "input": "true", "flow": {"ref": "0ccb44e3-2a5f-45c4-885e-74c915e4b2a1"}}]}]}], "inputParameter": [{"id": "c2d7852f-6525-4794-8231-2b0bfd65a374", "bpdParameterId": "2007.c2d7852f-6525-4794-8231-2b0bfd65a374", "isProcessInstanceCorrelator": "false"}, {"id": "e6db1bbe-7fd9-4e06-8f40-f78c1e68547d", "bpdParameterId": "2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d", "isProcessInstanceCorrelator": "false"}], "privateVariable": [{"id": "389ebfab-ad7c-45f7-80b1-7bcacb85f82c", "name": "taskId", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayOf": "false", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}, {"id": "662a0040-1dd6-4b44-8213-26b31d166e45", "name": "mailTo", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayOf": "false", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}, {"id": "47aab0e9-13a2-4912-8526-d7424de98556", "name": "customerAccounts", "description": "", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "arrayOf": "true", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}, {"id": "c8bac588-62b1-43ea-8239-3bb0bb495357", "name": "parentPath", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayOf": "false", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}], "epv": [{"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-460a", "epvId": "/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4609", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4608", "epvId": "/21.00a114f4-2b01-4c48-aad9-bd62580da24b"}, {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4607", "epvId": "/21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c"}], "searchableField": [{"id": "5d556fee-ab55-426f-8fae-c633625c524d", "name": "CustomerCIF", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4879", "expression": "tw.local.odcRequest.CustomerInfo.cif"}}, {"id": "8d013837-0e10-4860-8a09-3c720a4eff7c", "name": "CustomerName", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4878", "expression": "tw.local.odcRequest.CustomerInfo.customerName"}}, {"id": "0df39e40-549e-4dfe-8620-b81e23db8442", "name": "ParentRequestNumber", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4877", "expression": "tw.local.odcRequest.parentRequestNo"}}, {"id": "156d15c5-3e68-43b1-813f-d4342fd62a41", "name": "Initiation", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4876", "expression": "tw.local.odcRequest.initiator"}}, {"id": "d1326d94-db9a-43ca-80f9-ed14f69e5b5b", "name": "RequestDate", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4875", "expression": "tw.local.odcRequest.appInfo.requestDate"}}, {"id": "2bf36b4f-823f-4d6c-81bb-c850cb059591", "name": "RequestStatus", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4874", "expression": "tw.local.odcRequest.appInfo.status"}}, {"id": "f5e084bc-df19-47f8-8e4e-a849582d1912", "name": "initiatorUsername", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4873", "expression": "tw.local.odcRequest.appInfo.initiator"}}]}, "extension": {"id": "bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bc", "type": "CASE", "caseFolder": {"id": "9ead2f75-765e-4737-9f97-38a0bf8292d2", "allowLocalDoc": "false", "allowExternalDoc": "false", "allowSubfoldersCreation": "false", "allowExternalFolder": "false"}}}}}}}