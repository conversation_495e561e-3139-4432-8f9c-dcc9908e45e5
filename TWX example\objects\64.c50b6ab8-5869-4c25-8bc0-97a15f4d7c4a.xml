<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a" name="IDC Party">
        <lastModified>1735207941825</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;3fbac431-4890-42e3-8fc1-6291d6c19b43&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Collapsible_Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a780789d-00c7-44e3-86fc-94630f607332&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.party&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;770e91dd-c524-4f7a-8d4f-877e5ddc912b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dc6a1319-0626-470f-8c69-9b4cd23c5b50&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4fbcd04d-9fa5-4aa8-8d68-471aafc966da&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dbe35c55-cdb4-40de-8566-e1e672110cb5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81d79244-faed-4734-8b9a-5961415f4ee0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;var input = "partyName";
view.onloadTest("partyName");&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;325b69de-5c95-4a70-85cf-28b68b8929c5&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e4745733-b0fd-4842-8d26-6fb32cb34622&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ded2244e-640a-4e7b-adb6-7b1d6f091e44&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b399a024-990b-440c-8811-dd82fbb11fc9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;ID&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cee0d6fa-9ae7-455f-8fba-bcb7b38cd64d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;05f63d73-7bba-4b9b-8770-42b20c26eea1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;897fea8e-9247-4c36-8b3d-92a0589a9103&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;alert( me.ui.getAbsoluteName() )&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.input.partyId&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a6bb7c61-e79d-4dda-8566-6b48cbdcf992&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ec9f3b67-9c9d-473d-9ad6-688453d3725c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ccd8d3c4-2715-4240-8f99-980a3b32b29c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Party Name&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;169bdce4-891f-4949-84f4-c7f6f63b0837&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d22c6c81-bd7e-4332-87b9-d0b1c1cfba96&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.input.partyName&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8f093819-ab55-4274-8238-f2cce5ad9c19&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;73e705a8-d2ce-4089-8f6b-ea56c6d53cbd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3cd4dc4a-**************-c0eff665e17a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;11f185e9-7621-4523-868a-88e2cbe7a156&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49b22179-8ef2-4139-868f-4263c178d972&lt;/ns2:id&gt;&lt;ns2:optionName&gt;layoutFlow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"H"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4938cafd-4133-4d92-8914-47f6cb6993d0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;hAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;679fb1ce-cc86-4c0e-816a-31540c937582&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8802c138-7dab-4524-8d17-33511e03767d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;PartyTable&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2c6557e8-9b95-415b-84b2-b67849fc3baa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showAddButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.addParty&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;512b9878-2c23-4b2c-8548-2e4cc82a07b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showDeleteButton&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.deleteParty&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1e7bfc5-b51d-4215-8720-6cc7559d592f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Parties Table&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5d302aff-0b0d-4e07-8cf1-7cf5dc63a5f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c9adea63-4097-47a9-8ecb-d56fab9063ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2f153700-10c8-4bbe-8d86-8aa48df9b9e1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;expandableRows&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;34718a41-c834-4610-8caf-c281a60ec9a5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowColumnResize&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;693a3a6f-ea4d-40ec-809d-15e74826b7a5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;tableStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff39b7e1-4394-4276-8a0f-ec2dfd175029&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e95a2f9-b93f-46e8-8649-5ca3679bf2d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db3660a3-c2ae-42fb-8ae4-5ae6608d0991&lt;/ns2:id&gt;&lt;ns2:optionName&gt;columnSpecs&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.columns[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2861dbfb-0584-44da-8853-728b33a02abc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"200%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb6c0a70-d4c3-4786-8846-adcfbdacdf22&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_ADDREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//return view.addAccountee();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd1baf9e-893f-4c48-8cf2-cd51015cf429&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_DELREC&lt;/ns2:optionName&gt;&lt;ns2:value&gt;return view.onDeleteParty(me,item);
//if(item.partyType.name =="Drawer" || item.partyType.name =="Drawee"){
//	alert("You can not delete this row");
//	return false;
//}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dbb1067c-f504-4015-81da-affe9c695d71&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_ROWSLOADED&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;55e38c1f-25ae-4b39-8b24-be568892e1ec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f515b79f-fe61-4bd3-8e26-72f00155d139&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;7cc7fd18-3e3a-473d-8e0a-5179501d8120&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e0168522-1676-4bdb-8133-3a6eccfafa37&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;partytype&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92c77f30-1a44-48b4-b85a-a88cde39c380&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f00315fb-9596-4352-8dc4-944abab5f7f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.partytype&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;eafe285b-b6a7-41cd-8796-10577640b56e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e8f16404-7953-416b-8498-532ae7a48b34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae34a733-0283-4daa-81d8-f5ba30198e86&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;19de7c7a-7643-4cc0-8c21-692795ac203f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.ListNames.PartyType&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd8f69a8-0e16-4e4c-8d66-669d4ab32373&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0946557d-8346-4981-8ad5-6cc1b85ecba3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7eb25bb5-6c08-4a6b-8256-00a10ef89a41&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.partyTypeList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce31854d-b4d2-4003-8629-051997986baa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@validateParty(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c5e4b595-4a54-450a-8b4e-88a96e3335fe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.ui.getIndex() &amp;lt;2){
	me.setEnabled(false);
}

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d6748914-94c2-4dd3-875e-508a3778acbb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.4febbdb6-818a-4a1f-bbc5-2a880549ba9f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f5179e91-c386-4ccf-8b13-96fbfe64f2ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"new","value":"new"},{"name":"new1","value":"new1"},{"name":"new2","value":"new2"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7d91e1e0-6997-4742-8f8e-4d97faeb7e99&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_BLUR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.partyType&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;153e2e06-cd03-410f-807b-2af91921fbd8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;nbeCustomer&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;63ff4e55-cb86-4f62-8c4a-b8e8ec7227f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.FiledsNames.nbeCustomer&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e54afbaa-4407-4e9a-807d-1b6268e5e479&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97ce49de-0dc7-4fe5-88f6-7685afd76ef5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;28519375-2a40-4ad6-89e8-50578a437f13&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showValidationMarker&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":false}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92a47bbd-a249-45d8-8a12-be35e383d8d7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bca7de24-3fba-46ee-847b-1faec1753555&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@disableRetrieveBtn(me);


&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a338ee0a-1085-4bc8-838a-6680d95c3674&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@disableRetrieveBtn(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.isNbeCustomer&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c90046c1-a32c-4a68-8bc5-2c43c26eee55&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;partyCIF&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c4c91c68-dff7-4ee7-ad69-971806360b45&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;83abce14-f255-4d67-8a0c-2937cea873ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Party CIF&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a49eb100-44d6-445d-8a7f-83f4c4c0a334&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56567d11-3dc9-4f5e-8e08-386732de766e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ec00c580-05d3-4544-8949-e14b86319e34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(isNaN(Number(me.getData())) || me.getData().length &amp;lt; 8){
	me.setValid(false , "must be 8 digits");
//	me.setData("");
	return false;
}else
{
	me.setValid(true);
	return true;	
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9999b0e9-852c-44b2-81f5-629aad3290f3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 8){
	me.setValid(false , "must be 8 digits");
	me.setData("");
	return false;
}else{
	me.setValid(true);
	return true;
}
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba367efe-c593-4acf-825d-35db1c551c97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@addAccountee(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;41d8d86a-3011-4021-8ff0-7d1c042f8d97&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;46710c75-fe55-49dc-80ce-1c311f6dbc83&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.partyCIF&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c390859a-d31d-4138-82d4-b46426ae09ae&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;RetrieveBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5a6fdc3-ec08-485e-897b-eba1955a9f40&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Retrieve&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;22d8c8dd-8ebe-4c72-8d85-60c301dc6485&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b9f2faf-6902-44e6-8682-0cc83247609b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;210954a5-5ddf-47e8-8d53-b803e3051987&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a7c64ee4-d36e-4c56-8ada-361ca688901e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@clickEx(me);
//${GetPartyDetails1}.execute();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;99ad1cd8-17ce-417c-8fb3-5dc32dcfd2ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4b25f612-155c-44e7-8ac2-f3296d150765&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0efd7cb0-5cee-4dc4-8bd5-ea13632890c8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//me.setData(false);
//if(me.ui.getIndex() == 1){
//	me.setEnabled(false);
//}else{
//	me.setEnabled(true);
//}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9eaafb57-e3a1-4703-87de-f0d5905e30c8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.isRetrived&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;88d4ccfa-4fde-4543-82a5-c80b63a2b9dd&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CIF&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a87191e-ecc6-4b16-b924-4861fdd7d7e6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;36a8f52f-4931-4038-84b4-b2965e0b9c47&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Party ID&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;77d5663a-262d-4c1f-86f2-804e357e9ccb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2a9f5260-b84b-428f-8d6f-46ffd983ff19&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23711435-d7ab-4188-8246-690d3976e339&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@setPartyId(me);
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4eeeea28-8607-43c1-8945-2603cc215106&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 8)
{
	me.setValid(false , "max lenght is 8 digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d935a5d-dc75-4406-86d2-dd0e5b33ff23&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//@setOwnerCIF();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e7fb271-6d01-493a-8a31-11b28d9135b5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.partyId&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d1c48724-c648-4a45-8de7-b1d46ca1caae&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;partyname&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4af6b9bb-01b6-4da9-953b-60cbc58bbbf3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4f45a1bc-6b9e-4c90-86b2-8e24dc05e102&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.partyname&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b74b48bb-7189-4635-8cdd-af1460290cba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56f9cab0-42ba-485c-8184-e12b306ac9e6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.name&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;9c0e8436-0a67-4a2a-85ea-b3c78e45c9e3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;country&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ca6165c9-6862-4412-9ac6-2f02d2895e0c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0112f3f5-34ab-4777-863c-5687af43c1db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.country&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5fe3879-6ea1-4848-8a1c-bae2fe0e46d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;49f667c2-92e7-4f5c-89e3-9945ba68b0a2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5bff1cbc-e735-43dd-88fc-19684913c14d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;60361e13-bba2-4fbf-8da9-c68cf55e23a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.4febbdb6-818a-4a1f-bbc5-2a880549ba9f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;974197d1-9604-4725-8c93-023b722395f1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.ListNames.Country&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40cdb65c-dd45-4ddf-894e-2d29ffd0fc57&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;981c98dd-2620-4283-85d4-0fa9e2714520&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.country&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;16ae3cb7-413a-47dc-8c99-a3f684fcf4d2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;language&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;102b9238-3e1e-424e-85c0-ee8c8683a38b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb368526-879f-4170-83ab-1f9e9824ffd8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.language&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ebae7d00-1100-4ec5-837c-ab93524188e0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ebf10515-b989-4e00-8c05-8ddc9eb73d36&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.language&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;aa4fbb8e-1eff-409f-8faf-3136edfe37b9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;reference&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;873af441-0129-47c5-a3cd-cd4d7b28a7a7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;878b088b-15eb-40ff-87fb-d4eb243241ac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.reference&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d48ef1d-e32d-4714-88f5-87ade3c67af0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3cc70c73-e82d-4612-8b9d-109eee7fdb3b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.reference&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;460d0fba-**************-984bb38823a0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;91aa628e-5e98-4678-95e3-40a18aa5ece7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7df0e730-8beb-468b-8303-486acdba7d0a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address1&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9d5efb27-903f-4181-80a6-7c7b37ec332e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ff5445c-58da-4807-8b83-2379237d7220&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address1&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;92c4eb2b-4c1e-43d2-8704-5a93172ec198&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fcff24f4-5187-447c-aa34-8954b3b7ad5c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;48adad91-cbda-4328-831d-17009e5f68ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address2&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8455f632-335c-4c52-8698-12a3f1925048&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7555b621-d0f7-40b8-863d-07c795f8ba06&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address2&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;7789c0c1-8a7c-4018-86f9-a0760911988b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee92456e-3142-41f8-95c3-1e05cdc026af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d9026e2d-8800-4ab0-8328-f1c43a835203&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address3&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3711837d-e1dc-49d8-84bf-7804c5ce75e1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff0076e4-3b99-4918-8d0a-c999b894747a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address3&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a2a5e47f-5f7d-434d-87f3-cdb4d40e7359&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;media&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e66e1fd-7d8e-4e30-aedd-fe0bfdeaf191&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a5c1573-a457-4c74-8bc7-f66b597b1e4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.media&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ef8f92da-bb0d-401b-8e95-dd54d4d74922&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f11d80af-d487-4de1-819a-7728f03ff32a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff8ad4ee-710b-43a0-8bf2-028a3081a514&lt;/ns2:id&gt;&lt;ns2:optionName&gt;expression&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bfe63508-9518-4c8c-8bda-704ea9637721&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@setMediaVis(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.media&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;7a69b036-e2a2-4711-85bd-cf1aef36de30&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;address&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;43edfb72-3468-4931-8c43-4697be11f96c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.CommonLacale.address&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2f82835b-d033-4083-8ee7-8ce25d4b0a07&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b9a1eb0-3fdb-4bd2-8e50-e38c4f7ec672&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ea99a68-f819-4072-8b1c-32bb081a8ed6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aef02cd9-6d3a-411f-87d4-17681c97480c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b85e7dbe-31bb-4932-822c-f1d75a19e613&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@setBIC(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;904f9e06-db7d-4804-8bba-f521375da86f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;373481b7-1eaf-4e3d-8c08-23cbcde91a3e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"","optionDisplayProperty":""}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3cf95d0f-f366-40d1-8815-71ef0e87138b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.Party.currentItem.partyCIF&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f212838d-9b0a-457a-8350-a0b4c6e264dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9799d472-9fb7-4123-8927-4ce26bfb7f1a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.addressBICList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e2af431-985d-4580-8795-b383785b186f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;@setMediaVis(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;24d9f0fb-8eb5-4abd-8a58-3ac022ccbccc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.addressBICList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.Party.currentItem.address&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;131cc910-c2ed-4e36-8215-2cc7c1064f63&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;update&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4577426c-31eb-436b-8ffd-92764971586e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;update&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c97ae14-2ee6-42d6-8ce1-b6fdd2b0d941&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fdc744e7-325d-423a-8c7b-be2732c885b9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7040fd64-84c0-4b38-8d8b-5bf2f28566f7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f9440858-1b77-4e4c-8070-81248f283fc8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetPartyDetails1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;547ced56-317e-43d4-8120-c453aef0edbc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Party Details&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;df46e941-8d67-41f9-877b-17ea9816f764&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;15e0c455-4b66-418d-8ac5-1bc8cb240a75&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc69f1ac-bff8-43b4-88c6-e6f68d3de474&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.c3124e3b-0cdd-4710-a44b-6071a04e76dc&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b991536-7c8b-482e-84ef-9187fda0440b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.mapPartyData();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5779ce9e-afde-4738-8148-567315e86af6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6442679f-5dc6-449c-87c7-ef1c6912b906&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.customerFullDetails&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0ddfe2fe-e6a5-4145-8e6e-260ad2bafdb4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetAddressBIC&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5f29ccc7-fb4a-42ed-8908-b95ab2986081&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Address BIC&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e99180a4-bd54-4dcc-8470-f5016fef2a0d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e68a5eb-8ba3-4754-8e89-1ff6a6b81827&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;58a11cfe-5597-4790-80ec-449b467b68a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5d6b394c-c514-4c5c-8219-36944bd336b9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.addressBICList[]&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction>&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</loadJsFunction>
        <unloadJsFunction></unloadJsFunction>
        <viewJsFunction></viewJsFunction>
        <changeJsFunction isNull="true" />
        <collaborationJsFunction></collaborationJsFunction>
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>a58354e2-8a4c-4344-aab7-98d6b0518e60</guid>
        <versionId>50d524c6-1b7b-4b28-a77e-5a644b96a625</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="input">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.4bb5bd5f-5755-43dd-9012-f8e6de008e2e</coachViewBindingTypeId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <classId>/12.cdbb2c22-8516-45ab-925a-2933e6e1bed5</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>fa9336a8-be82-470d-bc8b-3aab37780a71</guid>
            <versionId>0e15342a-8f35-493c-9d70-e2bf4c91e4fc</versionId>
        </bindingType>
        <configOption name="partyTypeList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.ac4e91e2-c2c2-497e-a7d6-6c533d93b0de</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>9d89e10a-4d5c-4987-b752-55f27ec01748</guid>
            <versionId>c71ee089-88f0-4462-ae70-a0775b1b7857</versionId>
        </configOption>
        <configOption name="warningMess">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.eef77a75-c136-4bfb-a49a-a67b8200b8a3</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>417b8d79-a049-4ecb-9091-cf8a0c7ce8b9</guid>
            <versionId>2e29a97d-7680-4a46-ae47-bc605dd9cc98</versionId>
        </configOption>
        <configOption name="BeneficiaryDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.a8cced89-d62d-498a-8ac4-26ac30dddf0a</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.a025468c-38bc-4809-b337-57da9e95dacb</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>1cf59857-8609-42d0-a8c7-db32a4d373f2</guid>
            <versionId>ca66e698-a892-4e29-9ad3-5f8a165cb6d2</versionId>
        </configOption>
        <configOption name="partyIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.91cb6bdf-5b82-43b2-b50f-2a3f69c78c3e</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>08288aef-4b9c-4116-a0ad-37df9df343a1</guid>
            <versionId>8d91ad62-2c83-411c-9b11-0cd7ab2e632d</versionId>
        </configOption>
        <configOption name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.d6c10bf7-7ad4-4ed3-9a07-900a241d82c2</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>b5fe91f4-2827-49e2-8221-db09920cf038</guid>
            <versionId>75a230ce-230b-447d-9e2f-47f0b514a84a</versionId>
        </configOption>
        <configOption name="selectedBIC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.24b24d84-ef24-4616-ab87-eb4b94eb95a0</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>25d8dd28-65b5-4d01-9b2f-622c72d5d339</guid>
            <versionId>97545c3c-f9f2-4689-ba4b-eba0e86d890b</versionId>
        </configOption>
        <configOption name="customerCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.3289fde9-c029-49bc-96b3-5d3c396f4e06</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>c371a561-7efc-4fa3-9a62-aa02df425cc2</guid>
            <versionId>6b7fbb82-357f-4e00-8cd0-4458cfde1e39</versionId>
        </configOption>
        <configOption name="accounteeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.39aa05e8-c342-4fea-b1c0-bc7cfe7b90b5</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>84ee59bd-8be4-4136-a630-889d358a2b84</guid>
            <versionId>d8a5b711-098f-4308-8b0a-9d6a750abc99</versionId>
        </configOption>
        <configOption name="caseCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.*************-419a-a59e-29d0f145c613</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>b6681d92-2ce6-4408-911b-113fa7881c7d</guid>
            <versionId>c9abe58c-7b03-4241-9161-150652afa979</versionId>
        </configOption>
        <configOption name="addressBICList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.a45145ad-beb7-4836-b0d9-2e2f17164b64</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>6a19931f-a40d-4efa-8933-038e5d302da0</guid>
            <versionId>2ccd92c9-e7e3-47f0-a90f-47cf3ef37c66</versionId>
        </configOption>
        <configOption name="selectedCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.f1e2d8dd-f99a-40a4-9e4a-eb4f74abe5c0</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>10</seq>
            <description></description>
            <groupName></groupName>
            <guid>d90c1074-9520-4312-a1db-d75bd63772d9</guid>
            <versionId>24eb3f5f-2521-4068-8eed-739ea8def657</versionId>
        </configOption>
        <configOption name="alertMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.0be88f91-1b79-4b76-8fcb-c53066b635b2</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>11</seq>
            <description></description>
            <groupName></groupName>
            <guid>c78e67e7-b94f-4146-9ac2-094f2431517b</guid>
            <versionId>40eb45ad-58f5-4bf3-b348-b4a8872e3b2a</versionId>
        </configOption>
        <configOption name="errorVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.06b74dd0-4d12-4e5e-b166-61ba535cbf29</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>12</seq>
            <description></description>
            <groupName></groupName>
            <guid>515fd8d0-ebfe-4c08-a9b4-3613c17dc4af</guid>
            <versionId>132c5bf7-1d5d-4bc3-9528-5e9be50dcb60</versionId>
        </configOption>
        <configOption name="draweeCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.237c11da-d07c-42f8-a24f-6f1fc76b22e8</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>13</seq>
            <description></description>
            <groupName></groupName>
            <guid>f9dd791d-2773-4e5b-aee7-5007b064dd9d</guid>
            <versionId>0ef547de-0042-4ca0-b138-51ac77148d62</versionId>
        </configOption>
        <configOption name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.9b0d1a2f-9955-41a3-b3fd-b7d01ed51914</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>14</seq>
            <description></description>
            <groupName></groupName>
            <guid>6373316a-7443-4efd-866e-de9eb9728ab1</guid>
            <versionId>9abdd14d-0be1-4e71-a1c5-46003d317ff3</versionId>
        </configOption>
        <configOption name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.2aa7f3d1-a239-40b3-97c4-338a70dc05b1</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>15</seq>
            <description></description>
            <groupName></groupName>
            <guid>a35fac42-c07e-40e1-aeaf-cf39d280b3a8</guid>
            <versionId>667411a2-51f5-47a5-a96d-55a860ca7dc2</versionId>
        </configOption>
        <configOption name="requestState">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e14610e2-aa2f-4f74-a980-f59b7e872a84</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>16</seq>
            <description></description>
            <groupName></groupName>
            <guid>e68b0b41-9b93-48a4-a61d-0dfd2e68e1fc</guid>
            <versionId>70b2a4cd-c606-4d8d-ab93-d181dafd2df2</versionId>
        </configOption>
        <configOption name="columns">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.07e19288-958c-498f-98b4-4b31a396f446</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.55625d09-2e7f-4b8b-82aa-e01b1ba5bb57</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>17</seq>
            <description></description>
            <groupName></groupName>
            <guid>84fb8430-845b-45b9-9954-89cc8ba1755c</guid>
            <versionId>f9d787f8-7f81-461f-a031-7079e27ac6ad</versionId>
        </configOption>
        <configOption name="addParty">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.7f0b7e69-e793-49ae-a607-22bee5db8e7e</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>18</seq>
            <description></description>
            <groupName></groupName>
            <guid>f7838e6d-07ca-46ea-b274-3411140c1add</guid>
            <versionId>bf3a9ecd-1e12-462e-895b-4e7565e71f00</versionId>
        </configOption>
        <configOption name="deleteParty">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.165fe315-4bf9-4097-9a5b-3a7e56f4feb8</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>19</seq>
            <description></description>
            <groupName></groupName>
            <guid>4621fc7d-511d-4fc9-8daa-3e2e8a6ed214</guid>
            <versionId>8cbc485a-9b6b-4459-ba6e-cd14201b285d</versionId>
        </configOption>
        <configOption name="deletedCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.915cc37e-d8cf-424d-a00d-2ab2a7c878bc</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>20</seq>
            <description></description>
            <groupName></groupName>
            <guid>5210530c-a377-4fc9-8cbb-e9710ecac46f</guid>
            <versionId>3d5bd137-9c68-425c-abb5-ed9ce8467f60</versionId>
        </configOption>
        <configOption name="appID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.fb3296af-e7ce-463d-9c07-062acbeae704</coachViewConfigOptionId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>21</seq>
            <description></description>
            <groupName></groupName>
            <guid>225dcf11-7af3-428b-8da5-9906910dc3a0</guid>
            <versionId>82133490-5bb4-4d77-a748-4b1acd631e6e</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.5fce2ce4-ca63-44fb-b112-4645c0c7d121</coachViewInlineScriptId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
var isAmndFinal = false;&#xD;
var length = 0;&#xD;
this.setColumnsVis = function () {&#xD;
	var vis = "V";&#xD;
	if ((this.context.options.requestType.get("value") == "IDC Amendment" || this.context.options.requestType.get("value") == "IDC Completion") &amp;&amp; !this.context.options.isChecker.get("value")) {&#xD;
        this.context.options.addParty.set("value", false);&#xD;
        this.context.options.deleteParty.set("value", false);&#xD;
        if (this.context.options.requestState.get("value") == "Initial"){&#xD;
            vis = "R";&#xD;
            isAmndFinal = false;&#xD;
        }else if (this.context.options.requestState.get("value") == "Final"){&#xD;
        	vis = "R";&#xD;
            length = this.context.binding.get("value").length();&#xD;
            if (length &lt; 5) {&#xD;
                this.context.options.addParty.set("value", true);&#xD;
                isAmndFinal = true;&#xD;
            }    &#xD;
        }&#xD;
    }else{&#xD;
        vis = "V";&#xD;
        this.context.options.addParty.set("value", true);&#xD;
        this.context.options.deleteParty.set("value", true);&#xD;
        isAmndFinal = false;&#xD;
    }&#xD;
&#xD;
    var columns = [];&#xD;
    columns = [&#xD;
    {renderAs: "V", visibility: vis},&#xD;
    {renderAs: "V", visibility: vis},&#xD;
    {renderAs: "V", visibility: vis},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: vis},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"},&#xD;
    {renderAs: "V", visibility: "V"}&#xD;
    ];&#xD;
    this.context.options.columns.set("value", []);&#xD;
    this.context.options.columns.set("value", columns);&#xD;
}&#xD;
&#xD;
this.addAccountee = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
   	if (isAmndFinal &amp;&amp; record == length) {&#xD;
	    	value.setEnabled(true);&#xD;
	    	this.context.options.addParty.set("value", false);&#xD;
	    	this.context.options.deleteParty.set("value", true);&#xD;
	    	this.ui.get("PartyTable/partytype["+record+"]").setData({name:"Accountee",value:"Accountee"});&#xD;
    	}&#xD;
}&#xD;
&#xD;
this.setMediaVis = function (value) {&#xD;
	var r = value.ui.getIndex();&#xD;
	if (this.context.binding.get("value").get(r).get("partyType").get("name") == "Remitting Bank") {&#xD;
		value.setVisible(true,true);&#xD;
	}else{&#xD;
		value.hide();&#xD;
	}&#xD;
}&#xD;
&#xD;
this.onDeleteParty = function (value1,value2) {&#xD;
	var i = value1.getRecordIndex(value2);&#xD;
	var type = value2.partyType.name;&#xD;
&#xD;
	if((i == 0 || i == 1) || (isAmndFinal &amp;&amp; i != length)) {&#xD;
		alert("You can not delete this row");&#xD;
		return false;&#xD;
	}else{&#xD;
		this.context.options.addParty.set("value", true);&#xD;
&#xD;
		this.context.options.deletedCIF.set("value", value2.partyId);&#xD;
		if (type == "Accountee" || type == "Drawee") {&#xD;
        		this.ui.get("update").click();&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
&#xD;
//------------------------------------------------------------------&#xD;
this.validateOneType = function (type,value) {&#xD;
	var record = value.ui.getIndex();&#xD;
	var n = 0;&#xD;
	for (var i=0; i&lt; this.context.binding.get("value").length(); i++) {&#xD;
		if(this.context.binding.get("value").get(i).get("partyType").get("name") == type){&#xD;
			n+=1&#xD;
		}&#xD;
	}&#xD;
	if(value.getData().name == type &amp;&amp; n&gt;1){&#xD;
		this.context.options.warningMess.set("value", "Only one  is allowed of same Type");&#xD;
		this.context.binding.get("value").remove(record);&#xD;
	}else{&#xD;
		this.context.options.warningMess.set("value", "");&#xD;
	}&#xD;
}&#xD;
&#xD;
this.resetPartyItem = function (record) {&#xD;
	this.ui.get("PartyTable/partyCIF["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/CIF["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/partyname["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/country["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/language["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/reference["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/address1["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/address2["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/address3["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/media["+record+"]").setData("");&#xD;
	this.ui.get("PartyTable/address["+record+"]").setData("");&#xD;
}&#xD;
&#xD;
this.validateParty = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	this.context.binding.get("value").get(record).set("country", "EG");&#xD;
	this.context.binding.get("value").get(record).set("media", "");&#xD;
	this.ui.get("PartyTable/address["+record+"]").setData("");&#xD;
	this.context.binding.get("value").get(record).set("language", "ENG");&#xD;
	this.context.binding.get("value").get(record).set("reference", "NO REF")&#xD;
	this.ui.get("PartyTable/address["+record+"]").setEnabled(false);&#xD;
	this.ui.get("PartyTable/media["+record+"]").setEnabled(false);&#xD;
	//validateOneType&#xD;
	this.validateOneType("Accountee",value);&#xD;
	this.validateOneType("Drawee",value);&#xD;
	this.validateOneType("Case in Need",value);&#xD;
	this.validateOneType("Drawer",value);&#xD;
	this.validateOneType("Remitting Bank",value);&#xD;
	&#xD;
	//Reset Data&#xD;
	this.resetPartyItem(record);	&#xD;
    	//Default values&#xD;
&#xD;
	if (this.context.binding.get("value").get(record).get("partyType").get("name") == "Remitting Bank"){&#xD;
		this.context.binding.get("value").get(record).set("media", "SWIFT");&#xD;
		this.ui.get("PartyTable/address["+record+"]").setVisible(true,true);&#xD;
		this.ui.get("PartyTable/media["+record+"]").setVisible(true,true);&#xD;
	}else{&#xD;
		this.context.binding.get("value").get(record).set("media", "");&#xD;
		this.context.binding.get("value").get(record).set("address", "");&#xD;
//		this.ui.get("PartyTable/address["+record+"]").hide();&#xD;
//		this.ui.get("PartyTable/media["+record+"]").hide();&#xD;
	}&#xD;
	&#xD;
&#xD;
        	this.ui.get("update").click();&#xD;
&#xD;
}&#xD;
&#xD;
this.setOwnerCIF = function () {&#xD;
	for (var i=0; i&lt; this.context.binding.get("value").length(); i++) {&#xD;
		if(this.context.binding.get("value").get(i).get("partyType").get("name") == "Accountee"){&#xD;
			this.context.options.accounteeCIF.set("value", this.context.binding.get("value").get(i).get("CIF"));&#xD;
		}&#xD;
		if(this.context.binding.get("value").get(i).get("partyType").get("name") == "Drawee"){&#xD;
			this.context.options.draweeCIF.set("value", this.context.binding.get("value").get(i).get("CIF"));&#xD;
		}&#xD;
		if(this.context.binding.get("value").get(i).get("partyType").get("name") == "Case in Need"){&#xD;
			this.context.options.caseCIF.set("value", this.context.binding.get("value").get(i).get("CIF"));&#xD;
		}&#xD;
	}&#xD;
}&#xD;
this.setPartyId = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	var type = this.context.binding.get("value").get(record).get("partyType").get("name");&#xD;
	this.context.binding.get("value").get(record).set("isRetrived", false);&#xD;
	&#xD;
	//Set Owner CIF&#xD;
//	this.setOwnerCIF();&#xD;
	//Validation&#xD;
	if(isNaN(Number(value.getData())) || value.getData().length &lt; 8){&#xD;
        value.setValid(false , "must be 8 digits");&#xD;
		return false;&#xD;
	}else{&#xD;
        value.setValid(true);&#xD;
        if (type == "Accountee" || type == "Drawee") {&#xD;
        	this.ui.get("update").click();&#xD;
        }&#xD;
		return true;	&#xD;
	}&#xD;
}&#xD;
&#xD;
this.concatOnDelete = function (value){&#xD;
	var newString = "";&#xD;
	for (var i=0; i&lt;this.context.binding.get("value").length(); i++) {&#xD;
		if (i == value &amp;&amp; i &gt; 1) {&#xD;
			continue;&#xD;
		}&#xD;
		newString +=this.context.binding.get("value").get(i).get("partyType").name +",";&#xD;
	}&#xD;
	this.context.options.concatString.set("value", newString);&#xD;
}&#xD;
&#xD;
this.drawerNotNBE = function (){&#xD;
    if (this.context.binding.get("value").get(1).get("isNbeCustomer") == false){&#xD;
        this.context.binding.get("value").get(1).set("country", this.context.options.BeneficiaryDetails.get("value").get("country").get("englishdescription"));&#xD;
		this.context.binding.get("value").get(1).set("name", this.context.options.BeneficiaryDetails.get("value").get("name"));&#xD;
	}&#xD;
}&#xD;
&#xD;
this.mapPartyData = function (){&#xD;
      record = this.context.options.partyIndex.get("value");&#xD;
	if(this.context.binding.get("value").get(record).get("partyType").get("value") == "Remitting Bank" &amp;&amp; this.context.options.customerFullDetails.get("value").get("customerType") != "B"){&#xD;
&#xD;
		this.ui.get("PartyTable/partyCIF["+record+"]").setValid(false,"This CIF is not corresponding to a Bank");&#xD;
	}else{	&#xD;
		this.context.binding.get("value").get(record).set("partyId",  this.context.binding.get("value").get(record).get("partyCIF"));&#xD;
		this.context.binding.get("value").get(record).set("address1", this.context.options.customerFullDetails.get("value").get("customerAddress").get("addressLine1"));&#xD;
		this.context.binding.get("value").get(record).set("address2", this.context.options.customerFullDetails.get("value").get("customerAddress").get("addressLine2"));&#xD;
		this.context.binding.get("value").get(record).set("address3", this.context.options.customerFullDetails.get("value").get("customerAddress").get("addressLine3"));&#xD;
		this.context.binding.get("value").get(record).set("name", this.context.options.customerFullDetails.get("value").get("EnglishName"));&#xD;
		if (this.context.options.customerFullDetails.get("value").get("nationality") == "" || this.context.options.customerFullDetails.get("value").get("nationality") == null || this.context.options.customerFullDetails.get("value").get("nationality") == undefined ) {&#xD;
			this.context.binding.get("value").get(record).set("country","EG");&#xD;
		}&#xD;
		else{&#xD;
			this.context.binding.get("value").get(record).set("country",this.context.options.customerFullDetails.get("value").get("nationality"));&#xD;
		}&#xD;
		if (this.context.options.customerFullDetails.get("value").get("language") == "" || this.context.options.customerFullDetails.get("value").get("language") == null || this.context.options.customerFullDetails.get("value").get("language") == undefined ) {&#xD;
			this.context.binding.get("value").get(record).set("language", "ENG");&#xD;
		}else{&#xD;
			this.context.binding.get("value").get(record).set("language", this.context.options.customerFullDetails.get("value").get("language"));&#xD;
		}&#xD;
		this.context.binding.get("value").get(record).set("branch", {});&#xD;
		this.context.binding.get("value").get(record).get("branch").set("value", this.context.options.customerFullDetails.get("value").get("customerBranch"));&#xD;
		this.context.binding.get("value").get(record).get("branch").set("name", this.context.options.customerFullDetails.get("value").get("customerBranch"));&#xD;
		if(this.context.binding.get("value").get(record).get("partyType").get("value") === "Remitting Bank"){	&#xD;
			&#xD;
			var appID = this.context.options.appID.get("value");&#xD;
			var cif = this.context.binding.get("value").get(record).get("partyCIF");&#xD;
			var input = cif+"-"+appID;&#xD;
			this.ui.get("GetAddressBIC").execute(input);&#xD;
			if (!!this.context.options.BeneficiaryDetails.get("value").get("correspondentRefNum")) {	&#xD;
				&#xD;
				this.context.binding.get("value").get(record).set("reference", this.context.options.BeneficiaryDetails.get("value").get("correspondentRefNum"));&#xD;
			}else{&#xD;
				this.context.binding.get("value").get(record).set("reference","NO REF");&#xD;
			}&#xD;
			&#xD;
			this.ui.get("PartyTable/address["+record+"]").setEnabled(true);&#xD;
			this.ui.get("PartyTable/media["+record+"]").setEnabled(true);&#xD;
		}else{	&#xD;
			this.context.binding.get("value").get(record).set("reference", "NO REF");&#xD;
			this.ui.get("PartyTable/address["+record+"]").hide();&#xD;
			this.ui.get("PartyTable/media["+record+"]").hide();&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
this.clickEx = function (value){&#xD;
	record = value.ui.getIndex();&#xD;
	this.context.options.partyIndex.set("value", record);&#xD;
	value.setData(true);&#xD;
	if (!!this.context.binding.get("value").get(record).get("partyCIF")) {&#xD;
		var cif = this.context.binding.get("value").get(record).get("partyCIF");&#xD;
		var appID = this.context.options.appID.get("value");&#xD;
		var input = cif +"-"+appID;&#xD;
	    	this.ui.get("GetPartyDetails1").execute(input);&#xD;
//	    	this.context.binding.get("value").get(record).set("partyId",  this.context.binding.get("value").get(record).get("partyCIF"));&#xD;
	}	&#xD;
}&#xD;
&#xD;
this.setBIC = function (value){&#xD;
    this.context.options.selectedBIC.set("value", value.getData());&#xD;
}&#xD;
//---------------------------------------------------------------------------Drop_2--------------------------------------------------&#xD;
//function to view alert in case of get customer info error&#xD;
this.AjaxErrorHandling = function(errorMSG)&#xD;
{&#xD;
	this.context.options.alertMessage.set("value", errorMSG);&#xD;
	this.context.options.errorVis.set("value", "EDITABLE")&#xD;
}&#xD;
&#xD;
this.disableRetrieveBtn = function (value){&#xD;
	var record = value.ui.getIndex();&#xD;
	if (record != 1){&#xD;
//		value.setEnabled(false);&#xD;
		value.hide();&#xD;
	}else{&#xD;
		value.setEnabled(true);&#xD;
	}&#xD;
	if(record == 1 &amp;&amp; this.context.binding.get("value").get(record).get("isNbeCustomer") == true){&#xD;
		this.ui.get("PartyTable/RetrieveBtn["+record+"]").setEnabled(true);&#xD;
	}else if (record != 1){&#xD;
		this.ui.get("PartyTable/RetrieveBtn["+record+"]").setEnabled(true);&#xD;
	}else {&#xD;
		this.ui.get("PartyTable/RetrieveBtn["+record+"]").setEnabled(false);&#xD;
	}&#xD;
}&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>5990bcc7-0aab-4db5-9ef0-cf0798f6fde1</guid>
            <versionId>9b8b2597-2459-4d00-b9bd-135da5d53957</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.57aa66aa-753f-4cd3-9c72-b2be7b1e7c1d</coachViewLocalResId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <resourceBundleGroupId>341e5684-f12d-4cf6-aac8-d2dc77d648ab/50.babe04b6-56bb-4ff0-9723-638bba7612f2</resourceBundleGroupId>
            <seq>0</seq>
            <guid>e80c9779-918f-4f8a-a7a7-d408ed4db7f5</guid>
            <versionId>28e7802d-26d5-4c41-99e2-832ef9ea1bf4</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.17105772-5ce3-471d-bce7-2e382660c462</coachViewLocalResId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <resourceBundleGroupId>341e5684-f12d-4cf6-aac8-d2dc77d648ab/50.49a69c8c-61e1-426f-907a-e8ed80310ea5</resourceBundleGroupId>
            <seq>1</seq>
            <guid>7387fa18-e3fb-4d7c-bf1c-b12f861e226c</guid>
            <versionId>8a96d00d-2b85-427c-a60a-a4ee3836b1fb</versionId>
        </localization>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.af07db09-d19d-444a-815f-785756962f20</coachViewLocalResId>
            <coachViewId>64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a</coachViewId>
            <resourceBundleGroupId>/50.95ec9bf9-2d5a-4fc5-8dd1-e6d6a55a836c</resourceBundleGroupId>
            <seq>2</seq>
            <guid>f4e1f839-4722-473b-a5f3-cf427d102451</guid>
            <versionId>8d01affe-b0ed-4a4f-b9dc-2ca3f6c73b8b</versionId>
        </localization>
    </coachView>
</teamworks>

