{"id": "64.98459c6f-cb8f-462d-9fae-63d331db4606", "versionId": "9088f2b0-71c5-4a5b-991b-658253716d2c", "name": "test view", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "newtest", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.changeIt = function(me){\r\r\n\tvar v1 = me.getData();\r\r\n\tvar test2 = bpmext.ui.getView(\"/test_view_21\");\r\r\n\ttest2.setData(v1);\r\r\n\t\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};"}]}, "hasDetails": true}