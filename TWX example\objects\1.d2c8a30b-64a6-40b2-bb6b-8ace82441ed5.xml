<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5" name="Get Exchange Rate 2">
        <lastModified>1700560616314</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.75b15579-6cb9-4adf-8c49-a3f8cc00ed3c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>d5ac12a2-1b04-4789-b722-8b1aed3fb65d</guid>
        <versionId>b140fdd6-57d9-4bee-95fe-e4736776d96d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:4e45c66327642938:4e8a44d0:18bebf9775e:7652" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.07b122d6-c450-4a0b-9990-38e082114337"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":185,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"d7612756-b466-44b6-92d3-4308849f4cf5"},{"incoming":["bf3b270d-be40-444a-8ed6-e53f16a865e8","0e850827-be64-4f3a-8c72-c8d9e444a90c","ca205060-a380-40ec-926c-b3eb3bb16ea9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":800,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:5815664c4857eaa6:19ee65a1:18a60b4a587:-7cf"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"177bfc75-8b7e-4de4-8702-1d8a6f0377fe"},{"targetRef":"75b15579-6cb9-4adf-8c49-a3f8cc00ed3c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To TEST data","declaredType":"sequenceFlow","id":"2027.07b122d6-c450-4a0b-9990-38e082114337","sourceRef":"d7612756-b466-44b6-92d3-4308849f4cf5"},{"startQuantity":1,"outgoing":["bf3b270d-be40-444a-8ed6-e53f16a865e8"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":605,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"00d215b6-9a08-4dd9-ad56-110526506693","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.convertedAmount != null)\r\n{\r\n\ttw.local.results = tw.local.convertedAmount\/tw.local.initialAmount;\r\n}\r\n"]}},{"targetRef":"177bfc75-8b7e-4de4-8702-1d8a6f0377fe","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"bf3b270d-be40-444a-8ed6-e53f16a865e8","sourceRef":"00d215b6-9a08-4dd9-ad56-110526506693"},{"startQuantity":1,"outgoing":["ca205060-a380-40ec-926c-b3eb3bb16ea9"],"incoming":["dc104378-e8c3-47f1-8e1b-46b579ef0ffe"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":418,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Get equivilant amount","dataInputAssociation":[{"targetRef":"2055.f532fd4f-b514-4927-8cce-fd794f488e0d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.fromCurrency"]}}]},{"targetRef":"2055.282350b1-2727-4aa1-8118-3d5c3316136a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.toCurrency"]}}]},{"targetRef":"2055.293d7351-d3dc-48dc-896c-14e707fd7cba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC creation and amendment\""]}}]},{"targetRef":"2055.497359ed-14f4-4269-82b7-f09ee83dd3dc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.94fc6012-982f-4612-82c9-77bc1256e0b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.359d19b4-5948-47d2-814d-d3bbc031e650","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.6acacd41-404d-41cd-8b73-a6a3f53de59c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"TRANSFER\""]}}]},{"targetRef":"2055.*************-4e4c-8a23-3a8758392285","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"S\""]}}]},{"targetRef":"2055.0427603a-263e-49f1-8ebc-12396435a8f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"1\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"eeb517e6-435c-4280-9ad2-8d4047e3833a","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.error.errorText"]}}],"sourceRef":["2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.error.errorCode"]}}],"sourceRef":["2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.122b436b-8719-4a3f-81ab-c7897c8c2554"]}],"calledElement":"1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de"},{"targetRef":"177bfc75-8b7e-4de4-8702-1d8a6f0377fe","extensionElements":{"endStateId":["guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To get exchange rate","declaredType":"sequenceFlow","id":"ca205060-a380-40ec-926c-b3eb3bb16ea9","sourceRef":"eeb517e6-435c-4280-9ad2-8d4047e3833a"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"convertedAmount","isCollection":false,"declaredType":"dataObject","id":"2056.035c43eb-7d4e-47e7-b055-fda93ba85a02"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"TRANSFER\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateType","isCollection":false,"declaredType":"dataObject","id":"2056.57b3a568-83d2-4e49-ac1c-b1b3ce2606da"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"S\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateSubType","isCollection":false,"declaredType":"dataObject","id":"2056.ddb09da5-0007-4fb4-baa0-23b4bd4b777f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"100"}]},"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"initialAmount","isCollection":false,"declaredType":"dataObject","id":"2056.b65f124f-8eca-483f-a845-151e5f6ea465"},{"startQuantity":1,"outgoing":["dc104378-e8c3-47f1-8e1b-46b579ef0ffe"],"incoming":["2027.07b122d6-c450-4a0b-9990-38e082114337"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":257,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Init","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"75b15579-6cb9-4adf-8c49-a3f8cc00ed3c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.error={};\r\n\r\ntry{\r\n\tvar input = JSON.parse(tw.local.data);\r\n\r\n\ttw.local.fromCurrency = input.fromCurrency;\r\n\ttw.local.toCurrency = input.toCurrency;\r\n\r\n} catch (err) {\r\n\ttw.local.errorMessage= err.message;\r\n\tthrow new Error(tw.local.errorMessage);\r\n\t\r\n}"]}},{"targetRef":"eeb517e6-435c-4280-9ad2-8d4047e3833a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get equivilant amount","declaredType":"sequenceFlow","id":"dc104378-e8c3-47f1-8e1b-46b579ef0ffe","sourceRef":"75b15579-6cb9-4adf-8c49-a3f8cc00ed3c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fromCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.5d7197b9-678c-42d4-8107-b9374397330a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"toCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.1818bdff-97a3-4f0f-8a7c-c6eb546a7273"},{"parallelMultiple":false,"outgoing":["b977e738-5cd4-401b-8ff1-0194016012e4"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"861e3167-0d43-447c-8997-28458a08c65d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"fea5d8c0-6505-4a1a-8c09-328c1748aae0","otherAttributes":{"eventImplId":"318157d5-51da-4aee-8dc5-b87b975b8136"}}],"attachedToRef":"eeb517e6-435c-4280-9ad2-8d4047e3833a","extensionElements":{"nodeVisualInfo":[{"width":24,"x":453,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"42c78510-a902-4cf9-80c9-5cd6b510c739","outputSet":{}},{"parallelMultiple":false,"outgoing":["43f4f6da-0e84-4a98-80c0-9efb2da67027"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2f737605-efec-4612-8260-7585405fed5b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"56743de6-92bb-4926-8831-f1d9a6ebca38","otherAttributes":{"eventImplId":"a1caa45e-d0b4-49af-826f-6e46842db00d"}}],"attachedToRef":"00d215b6-9a08-4dd9-ad56-110526506693","extensionElements":{"nodeVisualInfo":[{"width":24,"x":640,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"d060bc0c-1440-4a80-8447-0240c9d5b473","outputSet":{}},{"startQuantity":1,"outgoing":["0e850827-be64-4f3a-8c72-c8d9e444a90c"],"incoming":["b977e738-5cd4-401b-8ff1-0194016012e4","43f4f6da-0e84-4a98-80c0-9efb2da67027"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":543,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exp Handking","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Get Exchange Rate 2\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"86324f3e-37b9-4815-8e46-4c3fc5e71ab8","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"86324f3e-37b9-4815-8e46-4c3fc5e71ab8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exp Handking","declaredType":"sequenceFlow","id":"b977e738-5cd4-401b-8ff1-0194016012e4","sourceRef":"42c78510-a902-4cf9-80c9-5cd6b510c739"},{"targetRef":"86324f3e-37b9-4815-8e46-4c3fc5e71ab8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exp Handking","declaredType":"sequenceFlow","id":"43f4f6da-0e84-4a98-80c0-9efb2da67027","sourceRef":"d060bc0c-1440-4a80-8447-0240c9d5b473"},{"targetRef":"177bfc75-8b7e-4de4-8702-1d8a6f0377fe","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"0e850827-be64-4f3a-8c72-c8d9e444a90c","sourceRef":"86324f3e-37b9-4815-8e46-4c3fc5e71ab8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.cc554807-3a9b-4d7f-8d45-7337955e5585"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.5eeef8bb-1912-4806-8ef2-4552e70b8021"}],"laneSet":[{"id":"c6484a78-2ca7-4c56-81a5-3787ef1688a7","lane":[{"flowNodeRef":["d7612756-b466-44b6-92d3-4308849f4cf5","177bfc75-8b7e-4de4-8702-1d8a6f0377fe","00d215b6-9a08-4dd9-ad56-110526506693","eeb517e6-435c-4280-9ad2-8d4047e3833a","75b15579-6cb9-4adf-8c49-a3f8cc00ed3c","42c78510-a902-4cf9-80c9-5cd6b510c739","d060bc0c-1440-4a80-8447-0240c9d5b473","86324f3e-37b9-4815-8e46-4c3fc5e71ab8"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"0a646edd-fb0d-4f8b-bacd-ca1d23391322","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[false]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Exchange Rate 2","declaredType":"process","id":"1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.9a79c75a-e7af-40f1-8850-0fabe5d13f54"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.d48ff027-7125-4a02-8a9b-8c7ef9ecae24"}],"inputSet":[{"dataInputRefs":["2055.7299e6d3-29bc-4902-83dd-5e114e595a68"]}],"outputSet":[{"dataOutputRefs":["2055.9a79c75a-e7af-40f1-8850-0fabe5d13f54","2055.d48ff027-7125-4a02-8a9b-8c7ef9ecae24"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"'{\"fromCurrency\":\"USD\",\"toCurrency\":\"EUR\"}'"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","documentation":[{"content":["from &amp;quot;USD&amp;quot;&lt;div&gt;to\u00a0&amp;quot;EGP&amp;quot;&lt;\/div&gt;&lt;div&gt;&lt;br \/&gt;&lt;\/div&gt;&lt;div&gt;\u00a0from the Liquidation Currency&lt;\/div&gt;&lt;div&gt;\u00a0to Account Currency&lt;br \/&gt;&lt;\/div&gt;"],"textFormat":"text\/plain"}],"name":"data","isCollection":false,"id":"2055.7299e6d3-29bc-4902-83dd-5e114e595a68"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7299e6d3-29bc-4902-83dd-5e114e595a68</processParameterId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>'{"fromCurrency":"USD","toCurrency":"EUR"}'</defaultValue>
            <isLocked>false</isLocked>
            <description>from &amp;quot;USD&amp;quot;&lt;div&gt;to &amp;quot;EGP&amp;quot;&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt; from the Liquidation Currency&lt;/div&gt;&lt;div&gt; to Account Currency&lt;br /&gt;&lt;/div&gt;</description>
            <guid>4ffd0818-4569-451d-b0d3-13631b2b2a83</guid>
            <versionId>896e221d-6c23-46bc-9d41-59e4e25b56d6</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9a79c75a-e7af-40f1-8850-0fabe5d13f54</processParameterId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ffa0abc4-7674-44ee-ad6f-5cf30eee77db</guid>
            <versionId>e8c6c85d-44f4-4ee7-8144-2720d3ecee59</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d48ff027-7125-4a02-8a9b-8c7ef9ecae24</processParameterId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ab1ba5b3-4882-4b09-9df7-f5c32007670f</guid>
            <versionId>e0a30b09-0a21-4e23-93a4-1127d39e3b92</versionId>
        </processParameter>
        <processVariable name="convertedAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.035c43eb-7d4e-47e7-b055-fda93ba85a02</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7628a2de-950e-455d-9c53-20f47fba982a</guid>
            <versionId>13287c02-d025-4357-be0a-44357669ef0f</versionId>
        </processVariable>
        <processVariable name="rateType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.57b3a568-83d2-4e49-ac1c-b1b3ce2606da</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>"TRANSFER"</defaultValue>
            <guid>747621ad-f652-4b9f-b737-3e3ce9bc3d6c</guid>
            <versionId>03423146-839f-41c3-bab7-18db1b6dd520</versionId>
        </processVariable>
        <processVariable name="rateSubType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ddb09da5-0007-4fb4-baa0-23b4bd4b777f</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>"S"</defaultValue>
            <guid>ac169b8a-0121-4647-b499-f8164f1086f7</guid>
            <versionId>3e8dfceb-7a4e-43e6-bf39-cc1a11f90311</versionId>
        </processVariable>
        <processVariable name="initialAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b65f124f-8eca-483f-a845-151e5f6ea465</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6013c478-91b2-407d-8d71-b75c26d61a32</guid>
            <versionId>98af3d46-78dd-4298-98af-dd8ff4a37d10</versionId>
        </processVariable>
        <processVariable name="fromCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5d7197b9-678c-42d4-8107-b9374397330a</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4a253c3c-6c0c-469c-8ba3-07fcc124721a</guid>
            <versionId>1af22f30-bea5-45bb-8c64-a613a8821e49</versionId>
        </processVariable>
        <processVariable name="toCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1818bdff-97a3-4f0f-8a7c-c6eb546a7273</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9f7885e7-0000-4131-9229-6dde60da1f5c</guid>
            <versionId>62e5c71c-264d-473f-aa8b-8412476a8d94</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cc554807-3a9b-4d7f-8d45-7337955e5585</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5d672f24-b0ad-4dfc-95e3-a700411e8051</guid>
            <versionId>31189479-19a8-466d-b03d-db42ac2da7a1</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5eeef8bb-1912-4806-8ef2-4552e70b8021</processVariableId>
            <description isNull="true" />
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a3024cc7-b466-4ed5-a8ab-5251a93052d1</guid>
            <versionId>13c63d34-9077-4d01-a3eb-30c493d3d8c6</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.86324f3e-37b9-4815-8e46-4c3fc5e71ab8</processItemId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <name>Exp Handking</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.*************-4906-8c16-4aa2c146c962</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:2027</guid>
            <versionId>42c40abc-eaad-4da5-9a8e-6721c58ae25b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="543" y="165">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.*************-4906-8c16-4aa2c146c962</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>38ebe94c-c4b9-4217-a9bf-8a63bad03097</guid>
                <versionId>eb59c58b-2d06-44e9-8e7f-1c45872871b2</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.080eef71-cc36-4fd7-a7ae-69644ab80412</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.*************-4906-8c16-4aa2c146c962</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0c9b65cb-ec10-45f3-85f6-a5950ddd5061</guid>
                    <versionId>096dd8ba-d893-43e7-b2c1-96f835b8d09e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5f9b3110-a3b4-4696-9750-cb8a4cd190cb</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.*************-4906-8c16-4aa2c146c962</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1aa98876-898b-4dd6-b433-03f70d61b92b</guid>
                    <versionId>2799edcf-9475-4037-a563-7f71ec986ba1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ba6afd3e-4b6d-4ace-b1c2-a30f985f68b0</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.*************-4906-8c16-4aa2c146c962</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Get Exchange Rate 2"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3641fa20-48a4-4e19-adef-f9d9cf213bd4</guid>
                    <versionId>e44803ab-2f2c-4ac7-b504-9f74a360b070</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.75b15579-6cb9-4adf-8c49-a3f8cc00ed3c</processItemId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <name>Init</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.01da1a51-0f10-48d9-ac88-9117fc9dd2cc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:33a</guid>
            <versionId>63effbd2-d104-422c-8fb7-91aa26803a73</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2238e42f-504e-4250-8576-ed0abaacc895</processItemPrePostId>
                <processItemId>2025.75b15579-6cb9-4adf-8c49-a3f8cc00ed3c</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>b534aae9-ce93-4afe-8863-a7a61f3d3517</guid>
                <versionId>1db7ec1f-4084-465b-9e39-15a133b6f0e6</versionId>
            </processPrePosts>
            <layoutData x="257" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.01da1a51-0f10-48d9-ac88-9117fc9dd2cc</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.error={};&#xD;
&#xD;
try{&#xD;
	var input = JSON.parse(tw.local.data);&#xD;
&#xD;
	tw.local.fromCurrency = input.fromCurrency;&#xD;
	tw.local.toCurrency = input.toCurrency;&#xD;
&#xD;
} catch (err) {&#xD;
	tw.local.errorMessage= err.message;&#xD;
	throw new Error(tw.local.errorMessage);&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>9dfb5bdc-9276-4f1a-b601-4d39b156c9c8</guid>
                <versionId>ad30b4da-3701-4c7f-8e26-a85bfda8cb62</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.eeb517e6-435c-4280-9ad2-8d4047e3833a</processItemId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <name>Get equivilant amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.86324f3e-37b9-4815-8e46-4c3fc5e71ab8</errorHandlerItemId>
            <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:-7ce</guid>
            <versionId>66bc8136-d02e-4780-90fa-952a46ec5cb9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.e53f3214-fef6-4c08-88a3-15a76be0aac8</processItemPrePostId>
                <processItemId>2025.eeb517e6-435c-4280-9ad2-8d4047e3833a</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>b9e18bd4-b021-4667-a722-e1ce457a8829</guid>
                <versionId>239a76ba-9e03-4230-a6ce-89cd202e748f</versionId>
            </processPrePosts>
            <layoutData x="418" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:2027</errorHandlerItem>
                <errorHandlerItemId>2025.86324f3e-37b9-4815-8e46-4c3fc5e71ab8</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3342da8c-694c-4d63-8509-c727db9eded1</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de</attachedProcessRef>
                <guid>a8c42d35-1bcb-4903-b239-1519222267d8</guid>
                <versionId>89304f50-bb74-4d5c-9451-ff9ae2f92d38</versionId>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4d5a2840-6132-4a87-9895-4e85457e49ae</parameterMappingId>
                    <processParameterId>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>05e070ac-cdb0-44be-b52a-fe5d48153f11</guid>
                    <versionId>0a65299f-0bbe-4488-a3f2-9efbc7f4585f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.969fa649-ac00-4c3d-9c3d-89e8b24d8ef0</parameterMappingId>
                    <processParameterId>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"TRANSFER"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5e23eff1-0f6a-48d9-84b3-6c7691d5368f</guid>
                    <versionId>2a909e1c-e3b0-4694-a4bf-72cd219d22e0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b5f08d81-00ea-420b-9df7-a3b2cc5ad050</parameterMappingId>
                    <processParameterId>2055.359d19b4-5948-47d2-814d-d3bbc031e650</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1315cf39-a028-4e59-8cad-ec207ab213d6</guid>
                    <versionId>4449cd2c-5831-4c8a-9f88-1f53b16fbfbe</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.12662d0f-3d4f-4a8d-8b05-b57965a09078</parameterMappingId>
                    <processParameterId>2055.0427603a-263e-49f1-8ebc-12396435a8f9</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"1"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>04434e3c-4f38-4f5e-a5e0-e9f29d11072d</guid>
                    <versionId>4f04179c-ea84-409f-9079-4f597262b6d7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency1">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.38c07366-7cd3-4d70-8709-ec2a9ae6700f</parameterMappingId>
                    <processParameterId>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.fromCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>84487420-431e-409d-8c54-ab39ef46f768</guid>
                    <versionId>8bdb43f8-9132-4443-afe7-a40a18039354</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8ae1a010-b14f-4cfc-960a-7eea9755cd93</parameterMappingId>
                    <processParameterId>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0e8e5f3d-a436-4441-9e7c-853680028aa3</guid>
                    <versionId>94ca3ee7-6989-4fb2-a137-e52fc0d39064</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amountResult">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.14b7debd-0c3f-44ba-a36c-6130be5ffae1</parameterMappingId>
                    <processParameterId>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e5180b43-b575-40fb-8dd9-eee09b7f68f1</guid>
                    <versionId>9acb1339-ad80-45c4-8b8f-d9a225861697</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.78bd6f85-8a1c-453a-99d1-de0a2466703e</parameterMappingId>
                    <processParameterId>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>164273d2-b12f-438d-b8a4-5187faa0ec2d</guid>
                    <versionId>9b747427-2572-4276-9b66-18b5fbf37a57</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateSubType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bb4229b2-bb49-4c10-afd8-afad8b8668e8</parameterMappingId>
                    <processParameterId>2055.*************-4e4c-8a23-3a8758392285</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"S"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d4a555ff-**************-e2066e630382</guid>
                    <versionId>9b93bca7-714e-4d3e-abb3-fe077556ff68</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7b23bf6c-262f-4bc9-bba2-f6a8688b00db</parameterMappingId>
                    <processParameterId>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error.errorText</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3478a94e-6263-4f34-878c-cefb24866016</guid>
                    <versionId>a4f4238a-2e3a-4102-b7ce-5dc076eee728</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.db1db358-147f-4803-8f5c-e8b151ae5f9c</parameterMappingId>
                    <processParameterId>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6136dd1e-28eb-4e20-9313-744ea0a6b259</guid>
                    <versionId>b6e329ae-7b8e-4789-971c-3dc41b2af91a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency2">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1d1802f9-2316-4525-b1fe-2d7d8780443f</parameterMappingId>
                    <processParameterId>2055.282350b1-2727-4aa1-8118-3d5c3316136a</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.toCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>46a491a9-0aee-4319-b723-cd9bd99e8e8e</guid>
                    <versionId>c215ca1f-9507-43e1-afa6-ee8dd158c295</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.95b581f2-c527-40ea-beb8-2780384e469e</parameterMappingId>
                    <processParameterId>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a39ba0f4-acfd-4a21-86ce-647effc311df</guid>
                    <versionId>c4990d0f-84f5-48d9-ba5a-50accd8bb046</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7be0b2ae-ecf3-4cf9-9e67-e2cebcb4d563</parameterMappingId>
                    <processParameterId>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4e793a52-8bfb-4603-9707-817ba5da5c8d</guid>
                    <versionId>c51ffbaf-4bdf-48e7-9e44-fb61eb716811</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5648159f-af71-488e-a4ce-900f871a382d</parameterMappingId>
                    <processParameterId>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</processParameterId>
                    <parameterMappingParentId>3012.3342da8c-694c-4d63-8509-c727db9eded1</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC creation and amendment"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>*************-4ca6-9720-82db9c5e15bc</guid>
                    <versionId>d6df1790-e99f-43c0-97e6-949eb7540f17</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.177bfc75-8b7e-4de4-8702-1d8a6f0377fe</processItemId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.fec2ee69-c596-4818-8f6d-430f99501199</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:-7cf</guid>
            <versionId>86d5741e-f429-4700-aa09-bafcd04b65aa</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="800" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.fec2ee69-c596-4818-8f6d-430f99501199</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>da78c80f-81b2-4bc6-9118-32b01d9a8d0d</guid>
                <versionId>71d3e5be-b8f0-4b20-a566-d7569a35f7ad</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.00d215b6-9a08-4dd9-ad56-110526506693</processItemId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.101bd406-49e4-4db5-97bc-1257e1cf084c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.86324f3e-37b9-4815-8e46-4c3fc5e71ab8</errorHandlerItemId>
            <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:-7cc</guid>
            <versionId>b7242ebe-b5c9-4335-82ba-246b5e464144</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="605" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:4a051bcdb927c971:-1aedb8c3:18b37c6d764:2027</errorHandlerItem>
                <errorHandlerItemId>2025.86324f3e-37b9-4815-8e46-4c3fc5e71ab8</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.101bd406-49e4-4db5-97bc-1257e1cf084c</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.convertedAmount != null)&#xD;
{&#xD;
	tw.local.results = tw.local.convertedAmount/tw.local.initialAmount;&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>6bb75595-da38-460f-8ac2-9dd7fdb62cd7</guid>
                <versionId>8fce69c0-b459-492d-a7dd-81a1cb859c4f</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.75b15579-6cb9-4adf-8c49-a3f8cc00ed3c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="185" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Exchange Rate 2" id="1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>false</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.7299e6d3-29bc-4902-83dd-5e114e595a68">
                            
                            
                            <ns16:documentation textFormat="text/plain">from &amp;quot;USD&amp;quot;&lt;div&gt;to &amp;quot;EGP&amp;quot;&lt;/div&gt;&lt;div&gt;&lt;br /&gt;&lt;/div&gt;&lt;div&gt; from the Liquidation Currency&lt;/div&gt;&lt;div&gt; to Account Currency&lt;br /&gt;&lt;/div&gt;</ns16:documentation>
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">'{"fromCurrency":"USD","toCurrency":"EUR"}'</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.9a79c75a-e7af-40f1-8850-0fabe5d13f54" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.d48ff027-7125-4a02-8a9b-8c7ef9ecae24" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.7299e6d3-29bc-4902-83dd-5e114e595a68</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.9a79c75a-e7af-40f1-8850-0fabe5d13f54</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.d48ff027-7125-4a02-8a9b-8c7ef9ecae24</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c6484a78-2ca7-4c56-81a5-3787ef1688a7">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="0a646edd-fb0d-4f8b-bacd-ca1d23391322" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>d7612756-b466-44b6-92d3-4308849f4cf5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>177bfc75-8b7e-4de4-8702-1d8a6f0377fe</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>00d215b6-9a08-4dd9-ad56-110526506693</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>eeb517e6-435c-4280-9ad2-8d4047e3833a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>75b15579-6cb9-4adf-8c49-a3f8cc00ed3c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>42c78510-a902-4cf9-80c9-5cd6b510c739</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d060bc0c-1440-4a80-8447-0240c9d5b473</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>86324f3e-37b9-4815-8e46-4c3fc5e71ab8</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="d7612756-b466-44b6-92d3-4308849f4cf5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="185" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.07b122d6-c450-4a0b-9990-38e082114337</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="177bfc75-8b7e-4de4-8702-1d8a6f0377fe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="800" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:-7cf</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bf3b270d-be40-444a-8ed6-e53f16a865e8</ns16:incoming>
                        
                        
                        <ns16:incoming>0e850827-be64-4f3a-8c72-c8d9e444a90c</ns16:incoming>
                        
                        
                        <ns16:incoming>ca205060-a380-40ec-926c-b3eb3bb16ea9</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="d7612756-b466-44b6-92d3-4308849f4cf5" targetRef="75b15579-6cb9-4adf-8c49-a3f8cc00ed3c" name="To TEST data" id="2027.07b122d6-c450-4a0b-9990-38e082114337">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="00d215b6-9a08-4dd9-ad56-110526506693">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="605" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>bf3b270d-be40-444a-8ed6-e53f16a865e8</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.convertedAmount != null)&#xD;
{&#xD;
	tw.local.results = tw.local.convertedAmount/tw.local.initialAmount;&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="00d215b6-9a08-4dd9-ad56-110526506693" targetRef="177bfc75-8b7e-4de4-8702-1d8a6f0377fe" name="To End" id="bf3b270d-be40-444a-8ed6-e53f16a865e8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get equivilant amount" id="eeb517e6-435c-4280-9ad2-8d4047e3833a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="418" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>dc104378-e8c3-47f1-8e1b-46b579ef0ffe</ns16:incoming>
                        
                        
                        <ns16:outgoing>ca205060-a380-40ec-926c-b3eb3bb16ea9</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fromCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.282350b1-2727-4aa1-8118-3d5c3316136a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.toCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC creation and amendment"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.359d19b4-5948-47d2-814d-d3bbc031e650</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"TRANSFER"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4e4c-8a23-3a8758392285</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"S"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0427603a-263e-49f1-8ebc-12396435a8f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"1"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.error.errorText</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.error.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="eeb517e6-435c-4280-9ad2-8d4047e3833a" targetRef="177bfc75-8b7e-4de4-8702-1d8a6f0377fe" name="To get exchange rate" id="ca205060-a380-40ec-926c-b3eb3bb16ea9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="convertedAmount" id="2056.035c43eb-7d4e-47e7-b055-fda93ba85a02" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateType" id="2056.57b3a568-83d2-4e49-ac1c-b1b3ce2606da">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">"TRANSFER"</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateSubType" id="2056.ddb09da5-0007-4fb4-baa0-23b4bd4b777f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">"S"</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="initialAmount" id="2056.b65f124f-8eca-483f-a845-151e5f6ea465">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">100</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init" id="75b15579-6cb9-4adf-8c49-a3f8cc00ed3c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="257" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.07b122d6-c450-4a0b-9990-38e082114337</ns16:incoming>
                        
                        
                        <ns16:outgoing>dc104378-e8c3-47f1-8e1b-46b579ef0ffe</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.error={};&#xD;
&#xD;
try{&#xD;
	var input = JSON.parse(tw.local.data);&#xD;
&#xD;
	tw.local.fromCurrency = input.fromCurrency;&#xD;
	tw.local.toCurrency = input.toCurrency;&#xD;
&#xD;
} catch (err) {&#xD;
	tw.local.errorMessage= err.message;&#xD;
	throw new Error(tw.local.errorMessage);&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="75b15579-6cb9-4adf-8c49-a3f8cc00ed3c" targetRef="eeb517e6-435c-4280-9ad2-8d4047e3833a" name="To Get equivilant amount" id="dc104378-e8c3-47f1-8e1b-46b579ef0ffe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="fromCurrency" id="2056.5d7197b9-678c-42d4-8107-b9374397330a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="toCurrency" id="2056.1818bdff-97a3-4f0f-8a7c-c6eb546a7273" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="eeb517e6-435c-4280-9ad2-8d4047e3833a" parallelMultiple="false" name="Error" id="42c78510-a902-4cf9-80c9-5cd6b510c739">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="453" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>b977e738-5cd4-401b-8ff1-0194016012e4</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="861e3167-0d43-447c-8997-28458a08c65d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="fea5d8c0-6505-4a1a-8c09-328c1748aae0" eventImplId="318157d5-51da-4aee-8dc5-b87b975b8136">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="00d215b6-9a08-4dd9-ad56-110526506693" parallelMultiple="false" name="Error1" id="d060bc0c-1440-4a80-8447-0240c9d5b473">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="640" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>43f4f6da-0e84-4a98-80c0-9efb2da67027</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2f737605-efec-4612-8260-7585405fed5b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="56743de6-92bb-4926-8831-f1d9a6ebca38" eventImplId="a1caa45e-d0b4-49af-826f-6e46842db00d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exp Handking" id="86324f3e-37b9-4815-8e46-4c3fc5e71ab8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="543" y="165" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>b977e738-5cd4-401b-8ff1-0194016012e4</ns16:incoming>
                        
                        
                        <ns16:incoming>43f4f6da-0e84-4a98-80c0-9efb2da67027</ns16:incoming>
                        
                        
                        <ns16:outgoing>0e850827-be64-4f3a-8c72-c8d9e444a90c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Get Exchange Rate 2"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="42c78510-a902-4cf9-80c9-5cd6b510c739" targetRef="86324f3e-37b9-4815-8e46-4c3fc5e71ab8" name="To Exp Handking" id="b977e738-5cd4-401b-8ff1-0194016012e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d060bc0c-1440-4a80-8447-0240c9d5b473" targetRef="86324f3e-37b9-4815-8e46-4c3fc5e71ab8" name="To Exp Handking" id="43f4f6da-0e84-4a98-80c0-9efb2da67027">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="86324f3e-37b9-4815-8e46-4c3fc5e71ab8" targetRef="177bfc75-8b7e-4de4-8702-1d8a6f0377fe" name="To End" id="0e850827-be64-4f3a-8c72-c8d9e444a90c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightBottom</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.cc554807-3a9b-4d7f-8d45-7337955e5585" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.5eeef8bb-1912-4806-8ef2-4552e70b8021" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0e850827-be64-4f3a-8c72-c8d9e444a90c</processLinkId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.86324f3e-37b9-4815-8e46-4c3fc5e71ab8</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.177bfc75-8b7e-4de4-8702-1d8a6f0377fe</toProcessItemId>
            <guid>d09d6e1c-db5b-4cf0-8e59-870b645c0b53</guid>
            <versionId>385f2232-54c5-4741-ad6e-7327aa7ed460</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightBottom" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.86324f3e-37b9-4815-8e46-4c3fc5e71ab8</fromProcessItemId>
            <toProcessItemId>2025.177bfc75-8b7e-4de4-8702-1d8a6f0377fe</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bf3b270d-be40-444a-8ed6-e53f16a865e8</processLinkId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.00d215b6-9a08-4dd9-ad56-110526506693</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.177bfc75-8b7e-4de4-8702-1d8a6f0377fe</toProcessItemId>
            <guid>96ccadb9-1e56-4850-b9d8-eb0612f66add</guid>
            <versionId>583af248-e1d9-418d-a540-b602d068f770</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.00d215b6-9a08-4dd9-ad56-110526506693</fromProcessItemId>
            <toProcessItemId>2025.177bfc75-8b7e-4de4-8702-1d8a6f0377fe</toProcessItemId>
        </link>
        <link name="To get exchange rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ca205060-a380-40ec-926c-b3eb3bb16ea9</processLinkId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.eeb517e6-435c-4280-9ad2-8d4047e3833a</fromProcessItemId>
            <endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</endStateId>
            <toProcessItemId>2025.177bfc75-8b7e-4de4-8702-1d8a6f0377fe</toProcessItemId>
            <guid>134f49fa-c2b8-4c3d-8d39-cf0edc24ed49</guid>
            <versionId>8204d64a-c345-40f9-8900-be4485d746eb</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.eeb517e6-435c-4280-9ad2-8d4047e3833a</fromProcessItemId>
            <toProcessItemId>2025.177bfc75-8b7e-4de4-8702-1d8a6f0377fe</toProcessItemId>
        </link>
        <link name="To Get equivilant amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.dc104378-e8c3-47f1-8e1b-46b579ef0ffe</processLinkId>
            <processId>1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.75b15579-6cb9-4adf-8c49-a3f8cc00ed3c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.eeb517e6-435c-4280-9ad2-8d4047e3833a</toProcessItemId>
            <guid>456cdda7-895d-4b0e-99fe-f4a42f9a9fc0</guid>
            <versionId>c48e3f22-250c-4ee0-bc31-b6aa9b421692</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.75b15579-6cb9-4adf-8c49-a3f8cc00ed3c</fromProcessItemId>
            <toProcessItemId>2025.eeb517e6-435c-4280-9ad2-8d4047e3833a</toProcessItemId>
        </link>
    </process>
</teamworks>

