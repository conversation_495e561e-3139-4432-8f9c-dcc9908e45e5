{"typeName": "Process", "count": 120, "objects": [{"id": "1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1", "versionId": "1bd76b05-5153-462d-993b-a53d8e782b07", "name": "Act01 - Create or Amend ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "versionId": "99d1d41f-38c8-4a04-b60c-56bdd6a0e721", "name": "ACT02 Review ODC Request By Compliance Rep", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "versionId": "b5c8a00c-4a4c-420f-95bd-6203311f9b9a", "name": "Act03 -  Review ODC Request by Trade Front Office", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "versionId": "69ae4197-2b05-43e5-b85f-c7987517a34b", "name": "ACT04 - ODC Execution Hub Initiation", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e", "versionId": "94c77e27-2519-45af-b545-d09e381df821", "name": "ACT04 - ODC Execution Hub Initiation 2", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.9db9271e-48cd-403e-accb-5dcc0e326e99", "versionId": "aabe6006-a38e-4f86-b9a0-8898fe3408db", "name": "ACT04 - ODC Execution Hub Initiation 3", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "versionId": "70fbc4e8-bbe0-438f-a2d2-24bceb75bd61", "name": "Act05 - ODC Execution Hub – Initiation Review", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9", "versionId": "d2ff0bc2-3f2f-40de-8897-6c929dcae36d", "name": "ACT06 -Print Remittance Letter", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285", "versionId": "bc13d0da-10ab-416c-b52c-f7897f3c5bf3", "name": "Ajax Get Customer Account Details", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f", "versionId": "d14a310e-5b6f-4028-93bd-25d5318f176b", "name": "Ajax get customer accounts", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871", "versionId": "62f7d590-81c1-43b2-8adf-64124adb1f3e", "name": "Ajax Get ODC Product codes", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.badb27e5-ab0e-4227-bebf-eb6d54984f36", "versionId": "18a28f54-72aa-4d3c-9b1c-26a3f9605bed", "name": "Audit Closure Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "versionId": "1382d1e5-4d3e-48a1-9c56-e2c1342710c9", "name": "Audit Create Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "versionId": "53c70d0a-2e2e-4c55-9973-e0c4eacf2f52", "name": "Audit ODC Update Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2d90a780-82bb-4941-b27e-d586607ce191", "versionId": "0df5b9dd-9586-48b7-9813-b17c9e15df1d", "name": "Audit Request History", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a1d1f1de-87c1-424f-8115-086a8221db8b", "versionId": "12879b4c-7ebd-4500-9f7c-a58760291453", "name": "Audit Reversal Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd", "versionId": "97315d56-b618-4e50-85da-f470f8f8aa63", "name": "Authorize FileNet", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "versionId": "82bbc204-9432-4081-b8a0-104b2219474c", "name": "BoxUtils", "type": "process", "typeName": "Process", "details": {"processType": "11"}, "subType": "11", "hasDetails": false}, {"id": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "versionId": "1812600f-233f-4424-8b78-d0d82862e944", "name": "Branch Hub filter service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.40a7d34d-1192-436a-ba5a-0360265d3261", "versionId": "c9c58a63-fe98-45f3-a2d6-d2f0f3f3f3ff", "name": "Calculate Change Amount", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd", "versionId": "44347ee5-e5d4-4e52-902a-0c75ba8f74fb", "name": "Calculate Default Amount Complete", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.812db3ff-6589-474c-bcc5-21fde39e4d25", "versionId": "772b74e8-d59f-4526-a7c0-52728951475d", "name": "Cancel and Delete Transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "versionId": "eb358d90-4d43-42df-a4a7-cd88da2d55ed", "name": "cancel request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2f8e9d68-4adb-42dd-a9f6-634969db03dc", "versionId": "b6b36f0f-af11-4144-aa46-d39750902231", "name": "Check GL Account", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "versionId": "c1d19b2a-90ea-4231-9cca-065d226aa8e1", "name": "Check Parent Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2af38b7e-9461-4887-9ad2-24d4565ee49b", "versionId": "2e4dd8db-74dc-496c-b70b-cbc0710d127c", "name": "ClosureACT01 - Create ODC Closure Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "versionId": "21577f8c-2df9-4566-867c-ec0b2d8182db", "name": "ClosureACT02 - Review ODC Closure Request by Compliance Rep", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8", "versionId": "e01b683e-1c97-4ca9-99a7-3b87a9627907", "name": "ClosureACT03 - Review ODC Closure Request by Trade FO", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.da205dca-ccab-4203-9f89-719cb8957cc5", "versionId": "f7a78373-5e71-41d8-8e11-d6c9c7e9ef57", "name": "ClosureACT04 - Review ODC Closure Request by Trade FO Checker", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.93bd8272-8f96-42db-92a3-1bdb463c2c28", "versionId": "54a260cb-e48b-4db7-8b45-708bb909eb01", "name": "ClosureACT05 - ODC Closure Execution", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "versionId": "8004b27f-ff8f-461d-bd60-38d9fb4d7368", "name": "ClosureACT06 - ODC Closure Execution Review", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "versionId": "2d44f93c-7507-483a-9a42-c82155bd0166", "name": "Col01 - Create ODC Collection Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.d5923909-74e7-467b-afb5-eef943fb9698", "versionId": "b6e7910f-ef9c-4691-89d3-f6b311230cba", "name": "Col01 - test", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "versionId": "e44dbf59-fb14-40c0-9374-7689ef0a2c4d", "name": "Col02 - Review ODC Collection Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.9395be71-805c-4443-85ee-48c92de214a3", "versionId": "388fedc6-f078-4b14-a057-d0465cc60849", "name": "Compare Validations", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.5d77055c-98a8-4191-9b74-c7120a5823be", "versionId": "78d80004-b3e8-4015-8160-b3ae3b3ad3ea", "name": "Create", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "versionId": "2f282072-cc7e-4f91-a680-b115235ae194", "name": "Create amend audit service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9", "versionId": "f98ead3b-39b5-422a-b96a-f066cc63e6a5", "name": "Create CIF Folder", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.46b984a3-b4ad-405a-abd3-8631f907efe4", "versionId": "f8e30919-05e5-410e-b324-f077ea1b0472", "name": "Create FileNet Folder", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "versionId": "6415877a-8b28-461c-8678-da20a8bd7fad", "name": "Create Folder FileNet", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e2ffa7a5-7c4f-4625-90e6-1d620f946497", "versionId": "c7dc9099-18ab-4d7b-adff-38c22761dc34", "name": "Create Folder Structure", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e0a673da-2e76-4a12-8896-d868bab63e83", "versionId": "6246d9e2-970f-4112-a5ec-ca82a319deaf", "name": "Deployment Service Flow", "type": "process", "typeName": "Process", "details": {"processType": "13"}, "subType": "13", "hasDetails": false}, {"id": "1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "versionId": "f6012b23-364c-4dcc-9b2f-62a0cad72961", "name": "Document Generation", "type": "process", "typeName": "Process", "details": {"processType": "11"}, "subType": "11", "hasDetails": false}, {"id": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "versionId": "2fd5fb36-d939-4309-899f-4e0ac13819e5", "name": "Exception Handling", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "versionId": "43837a43-23fe-4fde-9d48-cd45e4400de0", "name": "Execution HUB Filter Service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8e583b1e-1719-4e19-a6ce-6f41202527d4", "versionId": "726a60e9-20d1-464f-be10-cac0f6e5b358", "name": "Generate BPM Request Number", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "versionId": "0f8b5d23-2ad9-4e92-a134-ff82524258aa", "name": "Generate Remittance Letter", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "versionId": "2ab775de-cfd8-4ef4-84c3-78d836c0426f", "name": "Get account transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "versionId": "d3e91fab-eaa6-42c0-bc2b-1993a9797adb", "name": "Get Actions By ScreenName 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "versionId": "481d6826-1a12-4925-b771-84e41e86c71b", "name": "Get Actions for screens", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.f3735333-84b5-461b-9811-0817300dfb0b", "versionId": "51adad51-fef8-40a1-bfc8-cdc9146187c5", "name": "Get bank BIC codes", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f", "versionId": "25d936f9-6075-44a7-8da2-d1e5f68e9901", "name": "Get BIC Codes", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.769a8281-bf53-46c6-a4c8-571fc192f312", "versionId": "ce4d2ec4-0272-4163-b263-5eeb0cf5d121", "name": "Get charge amount", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "versionId": "57b9519b-c7b3-4dd4-9f16-ff374ea487f8", "name": "Get Charges Completed", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "versionId": "93427c98-30b1-4e9b-afd1-dd78819d8b88", "name": "Get Customer and Party Account List", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "versionId": "d7b6d4fd-c803-4126-b7ce-66b725ee7fe1", "name": "Get Customer Information", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.672c16cb-04d7-4e67-a904-779d9009e1ce", "versionId": "36e03776-e05b-452d-b6de-63a7ffa7e26f", "name": "Get Debited Nostro Vostro Account", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f", "versionId": "b9d3a129-d9ba-4e97-91d9-e9e67aa01b6d", "name": "Get Exchange Rate", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5", "versionId": "b140fdd6-57d9-4bee-95fe-e4736776d96d", "name": "Get Exchange Rate 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e", "versionId": "99590f81-1b9d-486b-ac13-6c3e800fdae6", "name": "Get Exchange Rate New", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.09d158a9-e4e4-4987-827f-4a4923c76843", "versionId": "5705639a-56b2-4fad-b807-141f3552a058", "name": "Get HUB name by code", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a9e6ba43-572e-4e44-b72a-285af67ec153", "versionId": "59c62612-504b-4989-a138-67fa4172d4bf", "name": "Get ODC charges", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "versionId": "33995fae-589a-4b95-bb3c-8abada244c7e", "name": "Get ODC charges 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "versionId": "f4a258c2-bb9f-42ca-ada7-e28ce1a91934", "name": "Get ODC Initiators", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "versionId": "f58cde10-b5fc-458d-8b18-10ee2601f718", "name": "Get Parent Request 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b1a705db-e502-470d-ad30-b08231994382", "versionId": "685b456d-8331-4e15-8435-507759e74816", "name": "Get Party Details", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.58febcb0-50a3-4963-8368-32c96c00d116", "versionId": "74c4c993-b191-4db5-b2ce-3a3a004e5f54", "name": "Get Request and Customer Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.d034d01a-267a-423b-a582-7444da255e09", "versionId": "cf53053e-c203-4386-b5cb-2dd80b75ae52", "name": "Get Request Nature", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.9e31ab59-ee74-4591-92f7-292855228c1b", "versionId": "7d2185a5-4430-4985-8522-ec1a6488d07d", "name": "Get Request Type", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.4855448a-1c86-4167-afa5-d79d3273d48b", "versionId": "6e3bc9bd-3726-456b-932c-ef8ab05bbde2", "name": "get RequestID", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.69ebf680-a301-4a98-ab97-9803a829bb25", "versionId": "9ee7bba9-5dd2-4974-a177-471c44906f41", "name": "Get Required Documents", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "versionId": "2867c960-8837-4a75-8b94-f1c61e5f5356", "name": "Get Required Documents 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.f18b1174-01f4-419d-80de-4b239479a7f1", "versionId": "0cd7d184-dc72-475b-a146-f1570fd0f490", "name": "get Reversal Reason id", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "versionId": "e3330927-d39f-472a-8e13-00fffb123dde", "name": "get search Criteria List", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "versionId": "3f6df8cc-626f-4428-a80d-46cc65930974", "name": "Get terms and conditions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc", "versionId": "2c9659be-2ace-42c5-a494-3e8842519db1", "name": "getEPV", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "versionId": "60722f6e-3f02-4e69-af40-bc9baf2ba628", "name": "History", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.302e4a54-16ab-47b9-9b70-d933329c72e7", "versionId": "be2a3126-ee11-4cb2-a3b8-708159a021f2", "name": "Hub Filter service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a28b2951-6ed0-484f-81e5-3d5d147c88f9", "versionId": "917a87a1-d033-43a3-a2dd-ce56114e8edc", "name": "Init Create ODC Request Service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "versionId": "8856132e-d9e1-48a0-99c8-999f2e996e33", "name": "Init ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "versionId": "7e52ed01-6dbe-4b2e-b2e1-421b774f9c5c", "name": "Initiate ODC Process", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "versionId": "11b29981-5b4f-4708-b14b-a10a0c95da8b", "name": "Log FC Transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "versionId": "9ee21618-4ac4-44bd-9e2a-77dc15c7a31c", "name": "ODC BO Initialization", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a3badb88-0400-49ae-ac1d-0e0ace050eb7", "versionId": "5bba26f0-66b8-470e-a1f1-91cace51e36c", "name": "ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير De_1", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.bd03b303-b465-40a1-a4c5-f08887b0cc2e", "versionId": "20231a0b-478b-48ec-99b6-aa3843bb0658", "name": "ODC Position Screen", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.55f0e416-bc0f-4110-9fce-c26af87b8915", "versionId": "296402b8-231b-4455-8fa0-710cab39e086", "name": "ODC Search Screen", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "versionId": "8ed1b310-c0a7-4a1b-9389-c19ee528707b", "name": "Print barcode", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "versionId": "12a25f86-b68a-4af4-958b-1a646d73db0c", "name": "Query BC Contract", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.028269d3-7705-46be-b7bb-3893e93d0240", "versionId": "1b81c58c-4961-4b95-a8fa-3422edd86cbe", "name": "Query BC contract 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b9bf1811-6e1e-41a3-828b-39059fa168cb", "versionId": "964de40b-0522-459f-a4fb-b63a9b125c1e", "name": "RACT01 - Create ODC Reversal Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.c71c7233-23ac-4e5f-a241-09cd96502615", "versionId": "5114311e-2333-45ef-981f-94070d569a0c", "name": "RACT02 - Review ODC Reversal Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "versionId": "2cfea046-37c5-434e-9f08-99940c2849ff", "name": "retrieve bills data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "versionId": "863323c5-c9f1-4ebb-8bb6-835644e37b23", "name": "retrieve charges and commision data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.77e38ca7-0df5-4ad2-98b9-25547cc61e1e", "versionId": "057ce9c7-5f9c-4321-89b4-ce0ffdbfbf78", "name": "Retrieve Country List From FC", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2f93c4b5-368e-4a13-aff6-b12926260bb3", "versionId": "a98912a2-804a-4147-876d-4038ef0000fb", "name": "retrieve DB lookups", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5", "versionId": "5534fa7b-3667-4287-83ca-3125449f7b02", "name": "retrieve invoice data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "versionId": "91cf47a3-59bb-4063-9c71-0c4efd9afc23", "name": "retrieve multiTenorDates", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "versionId": "55b7163e-f619-4530-b569-bf80238dd204", "name": "retrieve Odc request Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e62adcff-5053-4977-814b-9e511ca5d190", "versionId": "998f25b5-9684-4c61-abb0-a378b8c9bec3", "name": "retrieve Odc request Data oneToMany Relation", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "versionId": "41ebe9f1-593e-4b17-a752-6b1b929d7bf0", "name": "Retrieve ProductCode DB", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "versionId": "f95d146a-6c23-4ba7-85b6-bf3d0ee3d552", "name": "Retrieve Request Number", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "versionId": "b2ded4be-8e2d-4411-8c25-3346f14d22f2", "name": "retrieve Search Criteria Result", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.1b3eba82-0c58-41be-ba53-c071d336f323", "versionId": "d14e6e91-2dc3-4d75-aaf4-7c52bd9ebb44", "name": "Retrieve Search Result Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "versionId": "2adb820e-e4d2-4b13-812b-a734fd7d3c1e", "name": "Send Escalation Mail", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a6205b87-57cc-47bb-abfc-edff0743b08e", "versionId": "35c7c37e-292f-400c-bfe4-ad2eaa23ecc9", "name": "Send Mail to CAD Team", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e6908115-4586-455f-bf73-b96b60019972", "versionId": "a2a3e3ed-6c41-41b9-ba99-4e21b4092909", "name": "Send Rem Letter Mail", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.be113c16-c0f5-4037-b134-f403be3fe56f", "versionId": "779fb191-e982-44b2-b736-4f3fa7eb9f53", "name": "Service Flow", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.6a8c5669-195a-4ca0-80cc-606bbf87f9d0", "versionId": "feb0af45-0b25-44df-bad6-f8288abfe0c6", "name": "Service Flow_1", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2d3ab562-82df-48a5-9de7-f5d964218191", "versionId": "e1c75fd3-f8b2-4779-a4f1-4aecf972a0bc", "name": "Set document default properties", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "versionId": "db4be141-e3c9-4dd3-97a2-997aa022479e", "name": "Set document default properties 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.81656d33-5348-479b-a7af-5631356d9476", "versionId": "0bd7cd63-4a1c-4d29-b8ec-8b4dc6aebb2d", "name": "Set Status And Sub Status", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "versionId": "bc883da2-9235-4dad-85f4-25817ed4b654", "name": "Start ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.9862410e-bba9-43a0-b511-69ae076e3b64", "versionId": "06f9ad28-019c-42f1-82d6-ef124c4d347e", "name": "test", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8", "versionId": "ef91abe5-1a3c-439b-88b8-8efbc4c61758", "name": "Test Date", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.159431e7-0daa-4266-819a-968be03f82ff", "versionId": "7b68ed25-1cc4-4606-b79a-63185f7456fe", "name": "testASA", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "versionId": "86e4fd32-c430-438d-a46b-870d97e311a2", "name": "Trade Compliance", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "versionId": "1175767b-5bee-4b1a-9730-a32d090df2ed", "name": "Update ODC status", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6", "versionId": "72575335-0758-4d6b-a6c7-e9ec794dddf0", "name": "UT TF", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.13b84d91-6118-4776-9c68-8accac7d1220", "versionId": "317a030d-7fc8-4bf7-97b2-157878cd7c75", "name": "Validate Required Documents", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "versionId": "f8eed9d0-81b2-4e47-8fa4-d6d5eebd1940", "name": "Validate Required Documents 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}]}