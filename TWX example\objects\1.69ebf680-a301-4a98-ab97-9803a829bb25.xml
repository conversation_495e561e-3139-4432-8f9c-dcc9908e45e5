<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.69ebf680-a301-4a98-ab97-9803a829bb25" name="Get Required Documents">
        <lastModified>1691617707567</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.69b615e3-20cd-4de9-9df9-0e7af735a75f</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>3b86aa8f-f5c3-4b1d-990c-ecb5bf5d0a94</guid>
        <versionId>9ee7bba9-5dd2-4974-a177-471c44906f41</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:83e1efe624431d49:-7084ad56:189daab98ba:146f" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.8db2ca03-2aee-4a0b-8636-affe11004029"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"3bd1a24a-ca05-4f93-99f5-aac30299585e"},{"incoming":["c759beb0-6d2a-47a9-87c6-b4b7e1a9c189","a0ed2bd2-8738-4447-8eee-f50b9efc3455"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2ed5"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"12f06050-006a-4285-b84e-a2c771cce366"},{"targetRef":"69b615e3-20cd-4de9-9df9-0e7af735a75f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Is ODC Amendment","declaredType":"sequenceFlow","id":"2027.8db2ca03-2aee-4a0b-8636-affe11004029","sourceRef":"3bd1a24a-ca05-4f93-99f5-aac30299585e"},{"startQuantity":1,"outgoing":["15f1bfe6-39bd-41be-92b7-b87d26ae4b8d"],"incoming":["89e955c9-5280-4c73-8d4f-b2b09a9d3791"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":200,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Validate Required Documents","dataInputAssociation":[{"targetRef":"2055.3fd8e212-86fb-49bd-b067-39770ac7ff07","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requestType"]}}]},{"targetRef":"2055.789376b6-6edc-4ab4-ac12-1470f4a6ad44","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.documentSource"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"0f34aaf0-fade-4185-95ec-0e0d8ec57ec9","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requiredDocuments"]}}],"sourceRef":["2055.e14b7e6f-8f3b-4fa2-ba26-4975d2431248"]}],"calledElement":"1.13b84d91-6118-4776-9c68-8accac7d1220"},{"targetRef":"f3ea8f4d-8520-4346-b7cf-5cf119c55ee5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2d9b"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get Documents In Folder","declaredType":"sequenceFlow","id":"15f1bfe6-39bd-41be-92b7-b87d26ae4b8d","sourceRef":"0f34aaf0-fade-4185-95ec-0e0d8ec57ec9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requiredDocuments","isCollection":true,"declaredType":"dataObject","id":"2056.8912a9dd-904f-4ea4-9e4c-88085df3a537"},{"outgoing":["89e955c9-5280-4c73-8d4f-b2b09a9d3791","c759beb0-6d2a-47a9-87c6-b4b7e1a9c189"],"incoming":["2027.8db2ca03-2aee-4a0b-8636-affe11004029"],"default":"89e955c9-5280-4c73-8d4f-b2b09a9d3791","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":95,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Is ODC Amendment","declaredType":"exclusiveGateway","id":"69b615e3-20cd-4de9-9df9-0e7af735a75f"},{"targetRef":"0f34aaf0-fade-4185-95ec-0e0d8ec57ec9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Validate Required Documents","declaredType":"sequenceFlow","id":"89e955c9-5280-4c73-8d4f-b2b09a9d3791","sourceRef":"69b615e3-20cd-4de9-9df9-0e7af735a75f"},{"targetRef":"12f06050-006a-4285-b84e-a2c771cce366","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  !=\t  tw.epv.RequestType.Create"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true,"customBendPoint":[{"x":396,"y":16}]}]},"name":"To End","declaredType":"sequenceFlow","id":"c759beb0-6d2a-47a9-87c6-b4b7e1a9c189","sourceRef":"69b615e3-20cd-4de9-9df9-0e7af735a75f"},{"outgoing":["5c023ae4-6907-4e77-995f-31f626cb96df"],"incoming":["15f1bfe6-39bd-41be-92b7-b87d26ae4b8d"],"matchAllSearchCriteria":true,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":310,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["ContentTask"]},"operationRef":"FOLDER_OP_GET_DOCS_IN_FOLDER","implementation":"##WebService","serverName":"FileNet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask","startQuantity":1,"name":"Get Documents In Folder","dataInputAssociation":[{"targetRef":"FOLDER_ID","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.folderId"]}}]},{"targetRef":"SERVER_NAME","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.FileNet"]}}]}],"isForCompensation":false,"useMappedVariable":true,"completionQuantity":1,"id":"f3ea8f4d-8520-4346-b7cf-5cf119c55ee5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a","declaredType":"TFormalExpression","content":["tw.local.documents"]}}],"sourceRef":["DOCUMENTS"]}],"orderOverride":false},{"targetRef":"ae6eeff8-7423-4097-baa3-fb271aae159f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Missing Documents","declaredType":"sequenceFlow","id":"5c023ae4-6907-4e77-995f-31f626cb96df","sourceRef":"f3ea8f4d-8520-4346-b7cf-5cf119c55ee5"},{"itemSubjectRef":"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a","name":"documents","isCollection":true,"declaredType":"dataObject","id":"2056.9749d330-797d-4d6d-976a-ac690610e74f"},{"startQuantity":1,"outgoing":["a0ed2bd2-8738-4447-8eee-f50b9efc3455"],"incoming":["5c023ae4-6907-4e77-995f-31f626cb96df"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":457,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Missing Documents","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ae6eeff8-7423-4097-baa3-fb271aae159f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMessage = \"The following documents are required:\\n\";\r\ntw.local.errorExist = false;\r\nvar allDocumentTypes= [];\r\nvar haveODC = false;\r\n\r\nfor (var i=0; i&lt;tw.local.documents.listLength; i++) {\r\n\ttw.local.debug = \"documents\";\r\n\tif (tw.local.documents[i].objectTypeId==\"ODCDocument\") {\r\n\t\tvar haveODC = true;\r\n\t}\r\n}\r\nif (haveODC) \r\n{\r\n\t\tfor (var i=0; i&lt;tw.local.documentsTypesSelected.listLength; i++) \r\n\t\t{\r\n\t\t\ttw.local.debug = \"documentsTypesSelected\";\r\n\t\t\tallDocumentTypes[i] = tw.local.documentsTypesSelected[i].name;\r\n\t\t}\t\r\n}\r\nelse\r\n{\r\n\ttw.local.errorMessage = \"Must upload document of type ODC Document\";\r\n\ttw.local.errorExist=true;\r\n}"]}},{"targetRef":"12f06050-006a-4285-b84e-a2c771cce366","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Validate Required Documents","declaredType":"sequenceFlow","id":"a0ed2bd2-8738-4447-8eee-f50b9efc3455","sourceRef":"ae6eeff8-7423-4097-baa3-fb271aae159f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"debug","isCollection":false,"declaredType":"dataObject","id":"2056.caf03665-0b67-4dfc-9c3f-db827600aa3e"}],"laneSet":[{"id":"fe9e897b-f192-4146-8d77-160e5e9c4851","lane":[{"flowNodeRef":["3bd1a24a-ca05-4f93-99f5-aac30299585e","12f06050-006a-4285-b84e-a2c771cce366","0f34aaf0-fade-4185-95ec-0e0d8ec57ec9","69b615e3-20cd-4de9-9df9-0e7af735a75f","f3ea8f4d-8520-4346-b7cf-5cf119c55ee5","ae6eeff8-7423-4097-baa3-fb271aae159f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"6e004b5d-d68a-4f5d-b5bd-1da0a3e3a833","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Required Documents","declaredType":"process","id":"1.69ebf680-a301-4a98-ab97-9803a829bb25","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.c34407a3-77ee-4ecd-bf20-994c8569a8e1"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"id":"2055.a382db83-1b95-4aa1-bc58-1a5a8eedcb32"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"599ffa87-2995-4b81-8bb3-0f0fe5ede333","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.bf34b56a-0e19-4559-a015-073ff6e1f5e2","2055.8fa76fd4-044c-4acc-a32a-bf6dac3361ea","2055.0f547dda-c60b-4219-afe5-eee211cd8d70","2055.e732a08f-ff6e-4591-b6d1-21008a8969fd"]}],"outputSet":[{"dataOutputRefs":["2055.c34407a3-77ee-4ecd-bf20-994c8569a8e1","2055.a382db83-1b95-4aa1-bc58-1a5a8eedcb32"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"Correspondent\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"documentSource","isCollection":false,"id":"2055.bf34b56a-0e19-4559-a015-073ff6e1f5e2"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"Advance Payment\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestType","isCollection":false,"id":"2055.8fa76fd4-044c-4acc-a32a-bf6dac3361ea"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"{00B3EA88-0000-C735-A621-11E22CFFF8BE}\""}]},"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.0f547dda-c60b-4219-afe5-eee211cd8d70"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.Attachment();\nautoObject[0] = new tw.object.Attachment();\nautoObject[0].name = \"\";\nautoObject[0].description = \"\";\nautoObject[0].arabicName = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e","name":"documentsTypesSelected","isCollection":true,"id":"2055.e732a08f-ff6e-4591-b6d1-21008a8969fd"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="documentSource">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bf34b56a-0e19-4559-a015-073ff6e1f5e2</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>95ddceb6-e699-4328-8adb-63dfb992a4e6</guid>
            <versionId>ab936132-3c0a-46ad-adc7-20d10014e4e0</versionId>
        </processParameter>
        <processParameter name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8fa76fd4-044c-4acc-a32a-bf6dac3361ea</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>07219825-55e9-4c4d-9d7c-f445d1e79a0a</guid>
            <versionId>e43c80b6-c44e-448b-aaee-754ad319b32d</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0f547dda-c60b-4219-afe5-eee211cd8d70</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>dbf2cd3c-023a-4bb9-ba89-5493c2b574ca</guid>
            <versionId>1e9bd288-004e-4a37-bef3-fb79ad3f2601</versionId>
        </processParameter>
        <processParameter name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e732a08f-ff6e-4591-b6d1-21008a8969fd</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.07383e61-26ca-4c95-9b61-00fbdbc9735e</classId>
            <seq>4</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.Attachment();
autoObject[0] = new tw.object.Attachment();
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6043fb19-0935-4451-8961-e411ffcb9b9a</guid>
            <versionId>60b0325a-bdf7-4efa-9176-026046318451</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c34407a3-77ee-4ecd-bf20-994c8569a8e1</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0ba04f81-a7e3-40be-affc-659f7ce19057</guid>
            <versionId>f83ac99a-014d-45fe-897a-12f0b563921a</versionId>
        </processParameter>
        <processParameter name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a382db83-1b95-4aa1-bc58-1a5a8eedcb32</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6c3fb426-1708-41e4-8e5b-59ac46907fe0</guid>
            <versionId>3db121a2-bf23-4e73-99b5-7162f08354ff</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a8e04dde-af25-400f-af6f-24973ea6c1d4</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>82</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>10467a67-f4bc-49ae-8649-9ff869f6eb98</guid>
            <versionId>967d0d6a-4a72-468c-af91-20eca2d5e9ae</versionId>
        </processParameter>
        <processParameter name="ECMError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.567d3ba5-fdc4-4b66-959c-e2a0f8b31ddc</processParameterId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6</classId>
            <seq>83</seq>
            <hasDefault>false</hasDefault>
            <defaultValue>ECMError</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8b2d9fa6-fa99-4ae3-bdc8-45674dd79c88</guid>
            <versionId>03cc6735-b703-4d1c-82da-223a96ff4473</versionId>
        </processParameter>
        <processVariable name="requiredDocuments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8912a9dd-904f-4ea4-9e4c-88085df3a537</processVariableId>
            <description isNull="true" />
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1020f639-38e8-4c28-86af-32459c9b9591</guid>
            <versionId>e5664c1f-19fe-4ffc-befc-ff4ffba787ed</versionId>
        </processVariable>
        <processVariable name="documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9749d330-797d-4d6d-976a-ac690610e74f</processVariableId>
            <description isNull="true" />
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.490f939c-3c6d-4ef7-9707-33b5b618877a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7b35f447-6233-456d-8a4b-5cabfa323a09</guid>
            <versionId>7a149f40-98ce-4445-84ae-9ecde471e66a</versionId>
        </processVariable>
        <processVariable name="debug">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.caf03665-0b67-4dfc-9c3f-db827600aa3e</processVariableId>
            <description isNull="true" />
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>59f84abc-770b-4d3d-9515-68b4e03af7ec</guid>
            <versionId>8a8e3c8c-04d4-4d2e-a154-8f084ff7cb31</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f3ea8f4d-8520-4346-b7cf-5cf119c55ee5</processItemId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <name>Get Documents In Folder</name>
            <tWComponentName>ECMConnector</tWComponentName>
            <tWComponentId>3030.0eb4a50d-c43b-453d-bafc-12f9b6d3799e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2ed8</guid>
            <versionId>09614235-0829-4de8-9619-86d25d918f16</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="310" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <ecmConnectorId>3030.0eb4a50d-c43b-453d-bafc-12f9b6d3799e</ecmConnectorId>
                <definition>&lt;config type="com.lombardisoftware.client.persistence.ECMConnectorConfiguration"&gt;&#xD;
  &lt;inputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;folderId&lt;/name&gt;&#xD;
      &lt;type&gt;ECMID&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.folderId&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;serverName&lt;/name&gt;&#xD;
      &lt;type&gt;String&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.env.FileNet&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;false&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/inputParameters&gt;&#xD;
  &lt;outputParameters&gt;&#xD;
    &lt;parameter&gt;&#xD;
      &lt;name&gt;documents&lt;/name&gt;&#xD;
      &lt;type&gt;ECMDocument&lt;/type&gt;&#xD;
      &lt;description&gt;&lt;/description&gt;&#xD;
      &lt;defaultValue&gt;&lt;/defaultValue&gt;&#xD;
      &lt;argumentVariable&gt;tw.local.documents&lt;/argumentVariable&gt;&#xD;
      &lt;isArray&gt;&lt;/isArray&gt;&#xD;
      &lt;isList&gt;true&lt;/isList&gt;&#xD;
    &lt;/parameter&gt;&#xD;
  &lt;/outputParameters&gt;&#xD;
  &lt;matchAllSearchCriteria&gt;true&lt;/matchAllSearchCriteria&gt;&#xD;
  &lt;searchParameters /&gt;&#xD;
  &lt;selectParameters /&gt;&#xD;
  &lt;orderOverride&gt;false&lt;/orderOverride&gt;&#xD;
  &lt;orderOverrideValue&gt;&lt;/orderOverrideValue&gt;&#xD;
  &lt;operationType&gt;FOLDER_OP_GET_DOCS_IN_FOLDER&lt;/operationType&gt;&#xD;
  &lt;server&gt;FileNet&lt;/server&gt;&#xD;
  &lt;objectTypeSelection&gt;DOCUMENT&lt;/objectTypeSelection&gt;&#xD;
  &lt;useMappedVariable&gt;true&lt;/useMappedVariable&gt;&#xD;
  &lt;faultParameterId&gt;2055.567d3ba5-fdc4-4b66-959c-e2a0f8b31ddc&lt;/faultParameterId&gt;&#xD;
&lt;/config&gt;</definition>
                <guid>7116fe03-bc71-44b6-be54-1313095b7967</guid>
                <versionId>ffd56673-72a7-4cd0-8cf6-77589aa3e0f8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0f34aaf0-fade-4185-95ec-0e0d8ec57ec9</processItemId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <name>Validate Required Documents</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.760e9cd0-61e9-4477-bcbd-3e16c38c89bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2ed6</guid>
            <versionId>1afabe42-4e9c-4734-b7a0-78b177811186</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="200" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.760e9cd0-61e9-4477-bcbd-3e16c38c89bb</subProcessId>
                <attachedProcessRef>/1.13b84d91-6118-4776-9c68-8accac7d1220</attachedProcessRef>
                <guid>3ac3b6b3-1680-403d-a76e-085df96dd812</guid>
                <versionId>520538b8-dd81-4892-9816-3616256eb015</versionId>
                <parameterMapping name="requiredDocuments">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ee546188-6072-4a68-9efc-4d8cd9a709a4</parameterMappingId>
                    <processParameterId>2055.e14b7e6f-8f3b-4fa2-ba26-4975d2431248</processParameterId>
                    <parameterMappingParentId>3012.760e9cd0-61e9-4477-bcbd-3e16c38c89bb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requiredDocuments</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>8c552a46-f7dc-4b31-908e-74f04af76f1b</guid>
                    <versionId>0331327d-12c4-4da0-8d34-2cb5d09ba444</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.20e140cf-1f32-42e6-9554-48a910c634b7</parameterMappingId>
                    <processParameterId>2055.3fd8e212-86fb-49bd-b067-39770ac7ff07</processParameterId>
                    <parameterMappingParentId>3012.760e9cd0-61e9-4477-bcbd-3e16c38c89bb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestType</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>08c66ead-6302-4ac2-ada1-9eef47e65f88</guid>
                    <versionId>a1a0359c-50e8-4183-bddb-04c90a07718d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="documentSource">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2fe915a1-b829-4418-8f53-37ef8e6552ab</parameterMappingId>
                    <processParameterId>2055.789376b6-6edc-4ab4-ac12-1470f4a6ad44</processParameterId>
                    <parameterMappingParentId>3012.760e9cd0-61e9-4477-bcbd-3e16c38c89bb</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.documentSource</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>44826ca5-f3ed-4e56-a7a0-3f08719a24b8</guid>
                    <versionId>d7a0963c-2b96-401d-94dd-f6893a07a800</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.69b615e3-20cd-4de9-9df9-0e7af735a75f</processItemId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <name>Is ODC Amendment</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.c56b9009-0ecb-49e6-8d4a-d7f1f994e272</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2ed4</guid>
            <versionId>6c3f4453-07cc-4ae8-9740-8e44dc09349a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="95" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.c56b9009-0ecb-49e6-8d4a-d7f1f994e272</switchId>
                <guid>a527a14c-4599-400f-8ee2-d5fab481c083</guid>
                <versionId>b2501ac4-3530-41b6-92e8-08359f56aa95</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.f6c225d2-365c-4d38-bf03-cacc56d824d1</switchConditionId>
                    <switchId>3013.c56b9009-0ecb-49e6-8d4a-d7f1f994e272</switchId>
                    <seq>1</seq>
                    <endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:146e</endStateId>
                    <condition>tw.local.requestType	  !=	  tw.epv.RequestType.Create</condition>
                    <guid>a73b6d5e-4c9d-4127-9577-995b7450d551</guid>
                    <versionId>514edad9-85e6-4e63-ae1e-6effcfaab2d4</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ae6eeff8-7423-4097-baa3-fb271aae159f</processItemId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <name>Set Missing Documents</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0bdbf89b-1f8c-4c76-9f8e-a09b54aca663</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2ed7</guid>
            <versionId>ed4a54d0-2dbd-49d9-b039-5eea73b758e2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="457" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0bdbf89b-1f8c-4c76-9f8e-a09b54aca663</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.errorMessage = "The following documents are required:\n";&#xD;
tw.local.errorExist = false;&#xD;
var allDocumentTypes= [];&#xD;
var haveODC = false;&#xD;
&#xD;
for (var i=0; i&lt;tw.local.documents.listLength; i++) {&#xD;
	tw.local.debug = "documents";&#xD;
	if (tw.local.documents[i].objectTypeId=="ODCDocument") {&#xD;
		var haveODC = true;&#xD;
	}&#xD;
}&#xD;
if (haveODC) &#xD;
{&#xD;
		for (var i=0; i&lt;tw.local.documentsTypesSelected.listLength; i++) &#xD;
		{&#xD;
			tw.local.debug = "documentsTypesSelected";&#xD;
			allDocumentTypes[i] = tw.local.documentsTypesSelected[i].name;&#xD;
		}	&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.errorMessage = "Must upload document of type ODC Document";&#xD;
	tw.local.errorExist=true;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>fdcef69d-5b3c-42df-b221-0c9850f689eb</guid>
                <versionId>908c3933-fd3b-4ad8-9e86-8759de631063</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.12f06050-006a-4285-b84e-a2c771cce366</processItemId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.c2f30d80-f85b-4df4-ba85-51f514112435</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2ed5</guid>
            <versionId>f3c6a4e6-bd3e-4868-92c9-6563bffcb5ca</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.c2f30d80-f85b-4df4-ba85-51f514112435</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ec0bb826-db85-4ecf-835c-54f0d60e88d2</guid>
                <versionId>463210d8-722e-464e-86a6-4843776e48d0</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.be006f2d-d182-4432-aaf5-e1797661961a</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <guid>ba002b04-c0e1-4b76-9c56-b558f66cf713</guid>
            <versionId>65b576e7-8396-4af5-bbdb-1773535db19e</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.69b615e3-20cd-4de9-9df9-0e7af735a75f</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Required Documents" id="1.69ebf680-a301-4a98-ab97-9803a829bb25" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="599ffa87-2995-4b81-8bb3-0f0fe5ede333" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="documentSource" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.bf34b56a-0e19-4559-a015-073ff6e1f5e2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"Correspondent"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="requestType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.8fa76fd4-044c-4acc-a32a-bf6dac3361ea">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"Advance Payment"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.0f547dda-c60b-4219-afe5-eee211cd8d70">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"{00B3EA88-0000-C735-A621-11E22CFFF8BE}"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="documentsTypesSelected" itemSubjectRef="itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e" isCollection="true" id="2055.e732a08f-ff6e-4591-b6d1-21008a8969fd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.Attachment();
autoObject[0] = new tw.object.Attachment();
autoObject[0].name = "";
autoObject[0].description = "";
autoObject[0].arabicName = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.c34407a3-77ee-4ecd-bf20-994c8569a8e1" />
                        
                        
                        <ns16:dataOutput name="errorExist" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.a382db83-1b95-4aa1-bc58-1a5a8eedcb32" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.bf34b56a-0e19-4559-a015-073ff6e1f5e2</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.8fa76fd4-044c-4acc-a32a-bf6dac3361ea</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.0f547dda-c60b-4219-afe5-eee211cd8d70</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.e732a08f-ff6e-4591-b6d1-21008a8969fd</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.c34407a3-77ee-4ecd-bf20-994c8569a8e1</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.a382db83-1b95-4aa1-bc58-1a5a8eedcb32</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="fe9e897b-f192-4146-8d77-160e5e9c4851">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="6e004b5d-d68a-4f5d-b5bd-1da0a3e3a833" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>3bd1a24a-ca05-4f93-99f5-aac30299585e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>12f06050-006a-4285-b84e-a2c771cce366</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0f34aaf0-fade-4185-95ec-0e0d8ec57ec9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>69b615e3-20cd-4de9-9df9-0e7af735a75f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f3ea8f4d-8520-4346-b7cf-5cf119c55ee5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ae6eeff8-7423-4097-baa3-fb271aae159f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="3bd1a24a-ca05-4f93-99f5-aac30299585e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.8db2ca03-2aee-4a0b-8636-affe11004029</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="12f06050-006a-4285-b84e-a2c771cce366">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2ed5</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c759beb0-6d2a-47a9-87c6-b4b7e1a9c189</ns16:incoming>
                        
                        
                        <ns16:incoming>a0ed2bd2-8738-4447-8eee-f50b9efc3455</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="3bd1a24a-ca05-4f93-99f5-aac30299585e" targetRef="69b615e3-20cd-4de9-9df9-0e7af735a75f" name="To Is ODC Amendment" id="2027.8db2ca03-2aee-4a0b-8636-affe11004029">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.13b84d91-6118-4776-9c68-8accac7d1220" name="Validate Required Documents" id="0f34aaf0-fade-4185-95ec-0e0d8ec57ec9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="200" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>89e955c9-5280-4c73-8d4f-b2b09a9d3791</ns16:incoming>
                        
                        
                        <ns16:outgoing>15f1bfe6-39bd-41be-92b7-b87d26ae4b8d</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.3fd8e212-86fb-49bd-b067-39770ac7ff07</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.789376b6-6edc-4ab4-ac12-1470f4a6ad44</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.documentSource</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.e14b7e6f-8f3b-4fa2-ba26-4975d2431248</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requiredDocuments</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="0f34aaf0-fade-4185-95ec-0e0d8ec57ec9" targetRef="f3ea8f4d-8520-4346-b7cf-5cf119c55ee5" name="To Get Documents In Folder" id="15f1bfe6-39bd-41be-92b7-b87d26ae4b8d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2d9b</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="requiredDocuments" id="2056.8912a9dd-904f-4ea4-9e4c-88085df3a537" />
                    
                    
                    <ns16:exclusiveGateway default="89e955c9-5280-4c73-8d4f-b2b09a9d3791" name="Is ODC Amendment" id="69b615e3-20cd-4de9-9df9-0e7af735a75f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="95" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.8db2ca03-2aee-4a0b-8636-affe11004029</ns16:incoming>
                        
                        
                        <ns16:outgoing>89e955c9-5280-4c73-8d4f-b2b09a9d3791</ns16:outgoing>
                        
                        
                        <ns16:outgoing>c759beb0-6d2a-47a9-87c6-b4b7e1a9c189</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="69b615e3-20cd-4de9-9df9-0e7af735a75f" targetRef="0f34aaf0-fade-4185-95ec-0e0d8ec57ec9" name="To Validate Required Documents" id="89e955c9-5280-4c73-8d4f-b2b09a9d3791">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="69b615e3-20cd-4de9-9df9-0e7af735a75f" targetRef="12f06050-006a-4285-b84e-a2c771cce366" name="To End" id="c759beb0-6d2a-47a9-87c6-b4b7e1a9c189">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="396" y="16" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  !=	  tw.epv.RequestType.Create</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns4:contentTask serverName="FileNet" operationRef="FOLDER_OP_GET_DOCS_IN_FOLDER" name="Get Documents In Folder" id="f3ea8f4d-8520-4346-b7cf-5cf119c55ee5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="310" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>ContentTask</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>15f1bfe6-39bd-41be-92b7-b87d26ae4b8d</ns16:incoming>
                        
                        
                        <ns16:outgoing>5c023ae4-6907-4e77-995f-31f626cb96df</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>FOLDER_ID</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.folderId</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>SERVER_NAME</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.FileNet</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>DOCUMENTS</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a">tw.local.documents</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns4:contentTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="f3ea8f4d-8520-4346-b7cf-5cf119c55ee5" targetRef="ae6eeff8-7423-4097-baa3-fb271aae159f" name="To Set Missing Documents" id="5c023ae4-6907-4e77-995f-31f626cb96df">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a" isCollection="true" name="documents" id="2056.9749d330-797d-4d6d-976a-ac690610e74f" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Missing Documents" id="ae6eeff8-7423-4097-baa3-fb271aae159f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="457" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5c023ae4-6907-4e77-995f-31f626cb96df</ns16:incoming>
                        
                        
                        <ns16:outgoing>a0ed2bd2-8738-4447-8eee-f50b9efc3455</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.errorMessage = "The following documents are required:\n";&#xD;
tw.local.errorExist = false;&#xD;
var allDocumentTypes= [];&#xD;
var haveODC = false;&#xD;
&#xD;
for (var i=0; i&lt;tw.local.documents.listLength; i++) {&#xD;
	tw.local.debug = "documents";&#xD;
	if (tw.local.documents[i].objectTypeId=="ODCDocument") {&#xD;
		var haveODC = true;&#xD;
	}&#xD;
}&#xD;
if (haveODC) &#xD;
{&#xD;
		for (var i=0; i&lt;tw.local.documentsTypesSelected.listLength; i++) &#xD;
		{&#xD;
			tw.local.debug = "documentsTypesSelected";&#xD;
			allDocumentTypes[i] = tw.local.documentsTypesSelected[i].name;&#xD;
		}	&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.errorMessage = "Must upload document of type ODC Document";&#xD;
	tw.local.errorExist=true;&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ae6eeff8-7423-4097-baa3-fb271aae159f" targetRef="12f06050-006a-4285-b84e-a2c771cce366" name="To Validate Required Documents" id="a0ed2bd2-8738-4447-8eee-f50b9efc3455">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="debug" id="2056.caf03665-0b67-4dfc-9c3f-db827600aa3e" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Get Documents In Folder">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.15f1bfe6-39bd-41be-92b7-b87d26ae4b8d</processLinkId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0f34aaf0-fade-4185-95ec-0e0d8ec57ec9</fromProcessItemId>
            <endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-2d9b</endStateId>
            <toProcessItemId>2025.f3ea8f4d-8520-4346-b7cf-5cf119c55ee5</toProcessItemId>
            <guid>a2cbaa98-caf3-46e9-9e4b-a8f044d0df6b</guid>
            <versionId>1efc8a9c-a03f-4077-840c-aefccbe66334</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0f34aaf0-fade-4185-95ec-0e0d8ec57ec9</fromProcessItemId>
            <toProcessItemId>2025.f3ea8f4d-8520-4346-b7cf-5cf119c55ee5</toProcessItemId>
        </link>
        <link name="To Validate Required Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.89e955c9-5280-4c73-8d4f-b2b09a9d3791</processLinkId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.69b615e3-20cd-4de9-9df9-0e7af735a75f</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.0f34aaf0-fade-4185-95ec-0e0d8ec57ec9</toProcessItemId>
            <guid>2665d4a2-f989-4d8b-b3e2-cc3859f0c508</guid>
            <versionId>358526ec-6c3c-45e8-aeed-765439273adc</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.69b615e3-20cd-4de9-9df9-0e7af735a75f</fromProcessItemId>
            <toProcessItemId>2025.0f34aaf0-fade-4185-95ec-0e0d8ec57ec9</toProcessItemId>
        </link>
        <link name="To Validate Required Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a0ed2bd2-8738-4447-8eee-f50b9efc3455</processLinkId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ae6eeff8-7423-4097-baa3-fb271aae159f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.12f06050-006a-4285-b84e-a2c771cce366</toProcessItemId>
            <guid>4bd8c82d-d53e-43ec-a742-27b3f032f2d7</guid>
            <versionId>9d1703d8-02ec-42d3-91e4-1b69e55564a1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ae6eeff8-7423-4097-baa3-fb271aae159f</fromProcessItemId>
            <toProcessItemId>2025.12f06050-006a-4285-b84e-a2c771cce366</toProcessItemId>
        </link>
        <link name="To Set Missing Documents">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5c023ae4-6907-4e77-995f-31f626cb96df</processLinkId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f3ea8f4d-8520-4346-b7cf-5cf119c55ee5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ae6eeff8-7423-4097-baa3-fb271aae159f</toProcessItemId>
            <guid>d2be8507-23ba-4ca4-aa2d-a5073aa7f628</guid>
            <versionId>a64ad309-527a-4be5-a0c6-a1fba02cbda2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f3ea8f4d-8520-4346-b7cf-5cf119c55ee5</fromProcessItemId>
            <toProcessItemId>2025.ae6eeff8-7423-4097-baa3-fb271aae159f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c759beb0-6d2a-47a9-87c6-b4b7e1a9c189</processLinkId>
            <processId>1.69ebf680-a301-4a98-ab97-9803a829bb25</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.69b615e3-20cd-4de9-9df9-0e7af735a75f</fromProcessItemId>
            <endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:146e</endStateId>
            <toProcessItemId>2025.12f06050-006a-4285-b84e-a2c771cce366</toProcessItemId>
            <guid>aaa6b896-38e1-47f8-b6e7-ce6970cf2fb3</guid>
            <versionId>eebde95c-74c1-4f84-97e2-ee259214bad0</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="396" y="16" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.69b615e3-20cd-4de9-9df9-0e7af735a75f</fromProcessItemId>
            <toProcessItemId>2025.12f06050-006a-4285-b84e-a2c771cce366</toProcessItemId>
        </link>
    </process>
</teamworks>

