{"typeName": "Environment Property Variable", "count": 18, "objects": [{"id": "21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "versionId": "f0c29a38-66c0-4f01-99e3-95891f4aa4cd", "name": "Col_Actions", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "versionId": "4fb40a86-6ebe-4a8f-a67f-97e8a1ad9f79", "name": "Col_ScreenNames", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.00a114f4-2b01-4c48-aad9-bd62580da24b", "versionId": "0e192c1e-ff10-4383-9bee-a91299a88299", "name": "Col_SLA", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "versionId": "a2e0a4f1-9a12-4fe6-a90e-8f94ea5c21d9", "name": "CreationActions", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.51a88928-60d9-48d1-b133-fa98f61b49a6", "versionId": "8c547f72-0dc0-4296-affb-4a2160dcb625", "name": "ECMProperties", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c", "versionId": "63932d06-9062-443e-a513-8892148fffdc", "name": "Mails", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.dbbaa047-8f02-4397-b1b5-41f11b0256b3", "versionId": "5f8db4c6-2265-4cad-aa68-531075abe6b6", "name": "ODCCreationSLA", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.679fe48b-845c-41d8-b41a-884a27c2acf3", "versionId": "26c951c2-4455-49dc-bf21-96405071c50c", "name": "ODCProcessName", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.93c5c002-7ac4-4283-83ee-63b8662f9223", "versionId": "787291f6-5ebc-4162-813c-706d5f3f669b", "name": "ProcessDetails", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "versionId": "97ba8feb-0017-4384-9f96-8ff8181f41a1", "name": "RequestNature", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.062854b5-6513-4da8-84ab-0126f90e550d", "versionId": "521b2922-4fa2-4948-81ee-3ba2f3a22265", "name": "RequestState", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "versionId": "fd3df966-ad36-481d-b66d-1a0b9f1f32fb", "name": "RequestType", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.bed40437-f5de-4b1c-a063-7040de4075df", "versionId": "006e834d-7c18-4dd6-853e-b16ae35ab834", "name": "ScreenNames", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.0ac53667-a1c4-4086-b17a-f78b72a6299a", "versionId": "2d2eb361-0624-4a07-917c-4c64a464ec28", "name": "searchCriteria", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.340b122c-2fdf-400c-822c-b0c52fb7b022", "versionId": "8cb54f7c-a2db-4b51-ac76-1ecf1f515376", "name": "Status", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.96f93187-360b-430a-9042-84a97749fff7", "versionId": "59ff4d9e-9a2e-4f01-b647-e22f399c32ce", "name": "<PERSON><PERSON><PERSON>", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "versionId": "79c6e100-e701-463e-892d-2c931d6ffaa9", "name": "TermsAndConditions", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "versionId": "128acb22-0680-4190-9f73-22129718aeea", "name": "userRole", "type": "epv", "typeName": "Environment Property Variable", "details": {}}]}