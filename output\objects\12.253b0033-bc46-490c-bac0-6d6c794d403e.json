{"id": "12.253b0033-bc46-490c-bac0-6d6c794d403e", "versionId": "ba658780-2273-442e-9e79-8705dc1dfb57", "name": "ItmDef_1914247323_253b0033_bc46_490c_bac0_6d6c794d403e", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.253b0033-bc46-490c-bac0-6d6c794d403e", "name": "ItmDef_1914247323_253b0033_bc46_490c_bac0_6d6c794d403e", "lastModified": "1693480732430", "lastModifiedBy": "abdelrahman.saleh", "classId": "12.253b0033-bc46-490c-bac0-6d6c794d403e", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": "3", "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.253b0033-bc46-490c-bac0-6d6c794d403e", "dependencySummary": "<dependencySummary id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1755\">\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1754\">\r\n    <refId>/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1753\">\r\n      <name>externalId</name>\r\n      <value>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1752\">\r\n      <name>mimeType</name>\r\n      <value>xsd</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n</dependencySummary>", "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{\"namespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"typeName\":\"generateDocumentUsingPOSTRequest\"}],\"shadow\":[false]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{\"content\":[\"request\"]}],\"appinfo\":[{\"propertyName\":[\"request\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"_minOccurs\":1,\"typeName\":\"DocumentGenerationRequest\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":1}]}]},\"name\":\"request\",\"type\":\"{http:\\/\\/NBEODCR}BrokenReference\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063\"}}]},\"name\":\"ItmDef_1914247323_253b0033_bc46_490c_bac0_6d6c794d403e\"}],\"id\":\"_12.253b0033-bc46-490c-bac0-6d6c794d403e\"}", "description": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-176c", "versionId": "ba658780-2273-442e-9e79-8705dc1dfb57", "definition": {"property": {"name": "request", "description": "request", "classRef": "/12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "DocumentGenerationRequest", "typeNamespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "minOccurs": "1", "maxOccurs": "1", "nillable": "false", "order": "1", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": "generateDocumentUsingPOSTRequest", "namespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}