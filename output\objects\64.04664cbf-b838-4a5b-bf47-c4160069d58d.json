{"id": "64.04664cbf-b838-4a5b-bf47-c4160069d58d", "versionId": "30bb0b7d-c031-4cfa-8bf1-cb139dccd90b", "name": "ODC Parties CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "Parties", "configOptions": ["odcPartiesCVVIS", "odcPartiesCVBTNVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "// this.showCollectigBankRefSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifInfo.get(\"value\") == true) {\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}\r\r\n// this.partyTypeSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifNOForPartyTypes.get(\"value\") == true){\r\r\n//\t console.log(this.context.options.retrieveCifInfo)\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}"}]}, "hasDetails": true}