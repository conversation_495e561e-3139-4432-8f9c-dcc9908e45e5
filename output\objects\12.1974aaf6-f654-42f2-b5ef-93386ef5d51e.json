{"id": "12.1974aaf6-f654-42f2-b5ef-93386ef5d51e", "versionId": "45f165d0-e514-49a7-8498-5b500a69e2c0", "name": "GeneratedDocumentInfo", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.1974aaf6-f654-42f2-b5ef-93386ef5d51e", "name": "GeneratedDocumentInfo", "lastModified": "1697245280501", "lastModifiedBy": "abdelrahman.saleh", "classId": "12.1974aaf6-f654-42f2-b5ef-93386ef5d51e", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.1974aaf6-f654-42f2-b5ef-93386ef5d51e", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/NBEODCR\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{}],\"shadow\":[false]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"customerName\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"customerName\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"customerAddress\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"customerAddress\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"deliveryTerms\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"deliveryTerms\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"deliveryTermsExtra\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"deliveryTermsExtra\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"paymentInstructions\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"paymentInstructions\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"paymentInstructionsExtra\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"paymentInstructionsExtra\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"Instructions\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"Instructions\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"InstructionsExtra\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"InstructionsExtra\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"specialInstructions\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"specialInstructions\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"specialInstructionsExtra\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"specialInstructionsExtra\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"regenerateRemLetter\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"regenerateRemLetter\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Boolean\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"regenerateRemLetterOption\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"regenerateRemLetterOption\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}NameValuePair\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"regenerateRemLetterTitle\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"regenerateRemLetterTitle\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"destinationFolder\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"destinationFolder\",\"type\":\"{http:\\/\\/CIT}ECMFolder\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.240930df-e9da-40a6-a4e8-4b41b42bb183\"}}]},\"name\":\"GeneratedDocumentInfo\"}],\"id\":\"_12.1974aaf6-f654-42f2-b5ef-93386ef5d51e\"}", "description": {"isNull": "true"}, "guid": "guid:2e93acfacc1269a3:3c8702fd:18968c7904f:-5884", "versionId": "45f165d0-e514-49a7-8498-5b500a69e2c0", "definition": {"property": [{"name": "customerName", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "customerAddress", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "deliveryTerms", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "deliveryTermsExtra", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "paymentInstructions", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "paymentInstructionsExtra", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "Instructions", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "InstructionsExtra", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "specialInstructions", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "specialInstructionsExtra", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "regenerateRemLetter", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "regenerateRemLetterOption", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "regenerateRemLetterTitle", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "destinationFolder", "description": {"isNull": "true"}, "classRef": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.240930df-e9da-40a6-a4e8-4b41b42bb183", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}], "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "GeneratedDocumentInfo", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}