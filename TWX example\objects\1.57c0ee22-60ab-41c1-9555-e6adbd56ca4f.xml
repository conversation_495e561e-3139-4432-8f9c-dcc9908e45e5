<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f" name="Ajax get customer accounts">
        <lastModified>*************</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.0eb0ac24-516a-4377-8784-6a90131b9cab</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:fef170a08f25d496:5466e087:189df5f8551:75c0</guid>
        <versionId>d14a310e-5b6f-4028-93bd-25d5318f176b</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:28a9" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.94386f9e-6a02-4984-86b7-0f31799e48ea"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a71fea67-c43f-4b82-80b9-6be75bfec81e"},{"incoming":["e3a37d6a-1ea2-469c-88d7-1b6bc0014b2c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:75c2"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"28f3f5c7-6afb-4e33-875c-264a3c228063"},{"targetRef":"0eb0ac24-516a-4377-8784-6a90131b9cab","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To check if currency concatenated to the CIF","declaredType":"sequenceFlow","id":"2027.94386f9e-6a02-4984-86b7-0f31799e48ea","sourceRef":"a71fea67-c43f-4b82-80b9-6be75bfec81e"},{"startQuantity":1,"outgoing":["f97a67fb-b708-40ba-8387-405b3698a3c4"],"incoming":["e4774c62-a4c9-409a-814e-71a375e19e80","2027.94386f9e-6a02-4984-86b7-0f31799e48ea"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":239,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Retrieve customer accounts","dataInputAssociation":[{"targetRef":"2055.ca98719d-77f8-4fde-9663-52914ee14551","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.e8bee736-2a93-4760-aafb-38e55006f6b9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC creation and Amendement\""]}}]},{"targetRef":"2055.9d786589-d5a5-4fb6-8913-b6b1a1a57b1c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.b7a97c7d-99cb-44db-827f-0333fa136b3e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.d9d57b1e-9356-4664-87b3-27347adfe41d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.c0ca46fc-7eff-41ee-961f-0fc0dc8f8b3a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.f3935930-ab2f-433c-8bb0-566cbabffe45","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"0eb0ac24-516a-4377-8784-6a90131b9cab","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","declaredType":"TFormalExpression","content":["tw.local.accounts"]}}],"sourceRef":["2055.cdb1e363-7e09-4015-8916-b750b411405d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.9e696ce2-3597-4dd7-bed9-e8cf45602b03"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.status"]}}],"sourceRef":["2055.6a8c1cae-f653-4cbe-a332-230489206d39"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.error.errorCode"]}}],"sourceRef":["2055.98ee7d56-00f0-4503-9aa9-a320f73a3361"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.2f75a761-4364-4c36-b175-6c50df867d38"]}],"calledElement":"1.75ff304b-c398-4669-9ed0-70ab03152e8d"},{"targetRef":"5b03d536-3c58-4923-85f2-8905b375d4d5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2a69"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To map output","declaredType":"sequenceFlow","id":"f97a67fb-b708-40ba-8387-405b3698a3c4","sourceRef":"0eb0ac24-516a-4377-8784-6a90131b9cab"},{"startQuantity":1,"outgoing":["e3a37d6a-1ea2-469c-88d7-1b6bc0014b2c"],"incoming":["f97a67fb-b708-40ba-8387-405b3698a3c4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":413,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"5b03d536-3c58-4923-85f2-8905b375d4d5","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = new tw.object.listOf.NameValuePair();\r\nif(!!tw.local.accounts){\r\n      \r\n     \r\n\t for(var i=0;i&lt;tw.local.accounts.listLength;i++)\r\n\t {\r\n\t    if(!!tw.local.collectionCurrency){\r\n\t       if(tw.local.collectionCurrency.toLowerCase() == tw.local.accounts[i].currencyCode.toLowerCase()){\r\n\t          tw.local.results[i] = new tw.object.NameValuePair();\r\n                tw.local.results[i].value = tw.local.accounts[i].branchCode;\r\n\t\t    tw.local.results[i].name = tw.local.accounts[i].accountNO;\r\n\t       }\r\n\t    }\r\n\t else{\r\n\t          tw.local.results[i] = new tw.object.NameValuePair();\r\n                tw.local.results[i].value = tw.local.accounts[i].branchCode;\r\n\t\t    tw.local.results[i].name = tw.local.accounts[i].accountNO;\r\n\t \r\n\t }\r\n\t \r\n\t \r\n\t}\r\n}\r\n\/\/if(tw.local.accounts != null &amp;&amp; tw.local.accounts.listLength != 0)\r\n\/\/{\r\n\/\/\r\n\/\/\tfor(var i=0;i&lt;tw.local.accounts.listLength;i++)\r\n\/\/\t{\r\n\/\/\t\t\/\/filter by the customer accounts by the selected collection currency\r\n\/\/\t\tif(tw.local.currency != \"\" &amp;&amp; tw.local.currency != null)\r\n\/\/\t\t{\r\n\/\/\t\t\tif(tw.local.accounts[i].currencyCode.toLowerCase() == tw.local.currency.toLowerCase())\r\n\/\/\t\t\t{\r\n\/\/\t\t\t\ttw.local.results[i] = new tw.object.NameValuePair();\r\n\/\/\t\t\t\ttw.local.results[i].value = tw.local.accounts[i].accountNO;\r\n\/\/\t\t\t\ttw.local.results[i].name = tw.local.accounts[i].branchCode;\r\n\/\/\t\t\t}\r\n\/\/\t\t\t\t\t\t\r\n\/\/\t\t}\r\n\/\/\t\telse\r\n\/\/\t\t{\r\n\/\/\t\t\ttw.local.results[i] = new tw.object.NameValuePair();\r\n\/\/\t\t\ttw.local.results[i].value = tw.local.accounts[i].accountNO;\r\n\/\/\t\t\ttw.local.results[i].name = tw.local.accounts[i].branchCode;\r\n\/\/\t\t}\r\n\/\/\t\t\r\n\/\/\t}\r\n\/\/}"]}},{"targetRef":"28f3f5c7-6afb-4e33-875c-264a3c228063","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e3a37d6a-1ea2-469c-88d7-1b6bc0014b2c","sourceRef":"5b03d536-3c58-4923-85f2-8905b375d4d5"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"accounts","isCollection":true,"declaredType":"dataObject","id":"2056.b46156ba-9a2f-4985-8437-5a9431908f9f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"status","isCollection":false,"declaredType":"dataObject","id":"2056.ec23c960-8cce-4ca3-8fce-2c1ef5a5fc2d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();\nautoObject.errorText = \"\";\nautoObject.errorCode = \"\";\nautoObject.serviceInError = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.259a9f6d-9c04-4515-84fd-e65a16bd583c"},{"incoming":["a255199f-2a90-4216-8e6e-374d86fa8d04"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"6c6fe6b4-d22d-4938-8507-0436cb027d78","otherAttributes":{"eventImplId":"e3d8e11b-beeb-4d2c-87d3-67358fde47ed"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":592,"y":198,"declaredType":"TNodeVisualInfo","height":24}],"preAssignmentScript":["\/\/tw.local.error = {};"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"declaredType":"TFormalExpression"}}]}],"declaredType":"endEvent","id":"918487b3-3e5f-4840-8b02-dc891914c889"},{"startQuantity":1,"outgoing":["e4774c62-a4c9-409a-814e-71a375e19e80"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":87,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"check if currency concatenated to the CIF","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"dc5f212d-289c-491c-888c-d8580dcae9fb","scriptFormat":"text\/x-javascript","script":{"content":["var lengthBefore = tw.local.data.length;\r\nvar splitString = tw.local.data.split('_');\r\nif(splitString[0].length &lt;  lengthBefore)\r\n{\r\n\ttw.local.cif = splitString[0];\r\n\ttw.local.currency = splitString[1];\r\n}\r\nelse\r\n{\r\n\ttw.local.cif = tw.local.data;\r\n\ttw.local.currency = \"\"; \r\n}"]}},{"targetRef":"0eb0ac24-516a-4377-8784-6a90131b9cab","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Retrieve customer accounts","declaredType":"sequenceFlow","id":"e4774c62-a4c9-409a-814e-71a375e19e80","sourceRef":"dc5f212d-289c-491c-888c-d8580dcae9fb"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cif","isCollection":false,"declaredType":"dataObject","id":"2056.d1193417-6c7e-4b7b-8ee9-06c7a8d64434"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currency","isCollection":false,"declaredType":"dataObject","id":"2056.5d4912d0-40c6-43cc-8441-5ef3538a64ee"},{"startQuantity":1,"outgoing":["a255199f-2a90-4216-8e6e-374d86fa8d04"],"incoming":["e7ee4746-1aee-4a1b-8a5f-feae26feb32c"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":326,"y":175,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Get Customer Account\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"8e3e8cae-790a-43db-80b1-d7cf801f27d3","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"918487b3-3e5f-4840-8b02-dc891914c889","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"a255199f-2a90-4216-8e6e-374d86fa8d04","sourceRef":"8e3e8cae-790a-43db-80b1-d7cf801f27d3"},{"parallelMultiple":false,"outgoing":["e7ee4746-1aee-4a1b-8a5f-feae26feb32c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"6ddfa253-2093-4261-8ba9-37ada571b03c"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"406ea209-77d3-4232-8b3f-68f9bfabc306","otherAttributes":{"eventImplId":"1cc86bd0-408b-41c7-8f8f-6147673431b0"}}],"attachedToRef":"0eb0ac24-516a-4377-8784-6a90131b9cab","extensionElements":{"nodeVisualInfo":[{"width":24,"x":274,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"5b525d87-7a0a-428c-89d4-08317d9fca7c","outputSet":{}},{"targetRef":"8e3e8cae-790a-43db-80b1-d7cf801f27d3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"e7ee4746-1aee-4a1b-8a5f-feae26feb32c","sourceRef":"5b525d87-7a0a-428c-89d4-08317d9fca7c"}],"laneSet":[{"id":"b915b8d0-1d87-47bf-8452-e941f310ffd5","lane":[{"flowNodeRef":["a71fea67-c43f-4b82-80b9-6be75bfec81e","28f3f5c7-6afb-4e33-875c-264a3c228063","0eb0ac24-516a-4377-8784-6a90131b9cab","5b03d536-3c58-4923-85f2-8905b375d4d5","918487b3-3e5f-4840-8b02-dc891914c889","dc5f212d-289c-491c-888c-d8580dcae9fb","8e3e8cae-790a-43db-80b1-d7cf801f27d3","5b525d87-7a0a-428c-89d4-08317d9fca7c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"61f3292d-11fa-4706-86c3-5c8da27a170a","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[false]},"documentation":[{"textFormat":"text\/plain"}],"name":"Ajax get customer accounts","declaredType":"process","id":"1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.208f75bb-611b-4393-83d6-d60470c4a6a4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.7fdee937-d555-46b6-88d2-46c40e165c27"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"id":"2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074"}],"inputSet":[{"dataInputRefs":["2055.0bc98ce8-10aa-460a-8857-8a8600dafc90","2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861"]}],"outputSet":[{"dataOutputRefs":["2055.208f75bb-611b-4393-83d6-d60470c4a6a4","2055.7fdee937-d555-46b6-88d2-46c40e165c27","2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"13996525\"\r\n\/\/\"08732459_EGP\"\r\n\"08732459\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","documentation":[{"content":["\/\/&amp;quot;08732459&amp;quot;"],"textFormat":"text\/plain"}],"name":"data","isCollection":false,"id":"2055.0bc98ce8-10aa-460a-8857-8a8600dafc90"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"EUR\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"collectionCurrency","isCollection":false,"id":"2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0bc98ce8-10aa-460a-8857-8a8600dafc90</processParameterId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"13996525"&#xD;
//"08732459_EGP"&#xD;
"08732459"</defaultValue>
            <isLocked>false</isLocked>
            <description>//&amp;quot;08732459&amp;quot;</description>
            <guid>eb38701e-a6f6-4cf6-9125-fa318aa271fb</guid>
            <versionId>5a65e4cf-23e8-4ffb-b151-6271d8b242ef</versionId>
        </processParameter>
        <processParameter name="collectionCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861</processParameterId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"EUR"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>42225b63-b30c-4707-969b-f364ccdd1579</guid>
            <versionId>483102df-8bad-44a1-8e80-ea861e6afa1c</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.608a5f4e-b17a-4d3c-b4c3-6857406a498a</processParameterId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>30bd40fc-ba79-4a86-a886-dcc3ef0b10e3</guid>
            <versionId>2f5d57fc-d7e2-4d80-93d8-bc56a2511a82</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.208f75bb-611b-4393-83d6-d60470c4a6a4</processParameterId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d7caf3d2-3459-40b7-8d3c-6acf89681805</guid>
            <versionId>d47401b3-94fc-4064-9398-e23a850c27b5</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7fdee937-d555-46b6-88d2-46c40e165c27</processParameterId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f227afa0-6738-47ee-9006-0d3d8225ea00</guid>
            <versionId>6413efd2-1e1e-4641-a428-250da4d9450b</versionId>
        </processParameter>
        <processParameter name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074</processParameterId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ee9eb09f-3b87-4947-9231-76f9a7b8f820</guid>
            <versionId>4d768939-c7b3-4063-9471-d8d28001a065</versionId>
        </processParameter>
        <processVariable name="accounts">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b46156ba-9a2f-4985-8437-5a9431908f9f</processVariableId>
            <description isNull="true" />
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2596d8b0-0446-41ff-873d-26c466a342e3</guid>
            <versionId>3e45706f-0156-4fcf-9851-f24d648e0c66</versionId>
        </processVariable>
        <processVariable name="status">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ec23c960-8cce-4ca3-8fce-2c1ef5a5fc2d</processVariableId>
            <description isNull="true" />
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fa463edf-9908-4890-9a75-dfacc63ebfbe</guid>
            <versionId>6c2c8580-3d17-400c-a195-6ea56e422c3b</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.259a9f6d-9c04-4515-84fd-e65a16bd583c</processVariableId>
            <description isNull="true" />
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</defaultValue>
            <guid>4096e09f-e93d-4bfe-a4b2-e1f4a88c6bbe</guid>
            <versionId>dcf05162-18d0-4c15-822e-42bdfa26468f</versionId>
        </processVariable>
        <processVariable name="cif">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d1193417-6c7e-4b7b-8ee9-06c7a8d64434</processVariableId>
            <description isNull="true" />
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>031ee48d-5bf0-4d99-a57f-1cb1fa633d43</guid>
            <versionId>28666531-f9bd-4c79-ad28-c38580b3ba29</versionId>
        </processVariable>
        <processVariable name="currency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5d4912d0-40c6-43cc-8441-5ef3538a64ee</processVariableId>
            <description isNull="true" />
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3bbf4dd0-0796-4333-939c-4de077e6e3e6</guid>
            <versionId>09eab23e-64d1-4dda-923e-75019885ff93</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.dc5f212d-289c-491c-888c-d8580dcae9fb</processItemId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <name>check if currency concatenated to the CIF</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.731c503f-870c-4857-b6a7-a5075e8666c1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b04b859e4:3ba5</guid>
            <versionId>0741d92b-e64d-44f1-8e2b-fb0bd9e71453</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="87" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.731c503f-870c-4857-b6a7-a5075e8666c1</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var lengthBefore = tw.local.data.length;&#xD;
var splitString = tw.local.data.split('_');&#xD;
if(splitString[0].length &lt;  lengthBefore)&#xD;
{&#xD;
	tw.local.cif = splitString[0];&#xD;
	tw.local.currency = splitString[1];&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.cif = tw.local.data;&#xD;
	tw.local.currency = ""; &#xD;
}</script>
                <isRule>false</isRule>
                <guid>cacd2c1d-b02a-41dc-a3a4-2cde3f39ff97</guid>
                <versionId>d765b396-2f70-4290-b177-2b22c7dde121</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5b03d536-3c58-4923-85f2-8905b375d4d5</processItemId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <name>map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.3b1f5fb3-896d-4733-a1d5-fc0b6b35498d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:75c7</guid>
            <versionId>118eb0e7-88ed-4704-9d18-b72177b03d6b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="413" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.3b1f5fb3-896d-4733-a1d5-fc0b6b35498d</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(!!tw.local.accounts){&#xD;
      &#xD;
     &#xD;
	 for(var i=0;i&lt;tw.local.accounts.listLength;i++)&#xD;
	 {&#xD;
	    if(!!tw.local.collectionCurrency){&#xD;
	       if(tw.local.collectionCurrency.toLowerCase() == tw.local.accounts[i].currencyCode.toLowerCase()){&#xD;
	          tw.local.results[i] = new tw.object.NameValuePair();&#xD;
                tw.local.results[i].value = tw.local.accounts[i].branchCode;&#xD;
		    tw.local.results[i].name = tw.local.accounts[i].accountNO;&#xD;
	       }&#xD;
	    }&#xD;
	 else{&#xD;
	          tw.local.results[i] = new tw.object.NameValuePair();&#xD;
                tw.local.results[i].value = tw.local.accounts[i].branchCode;&#xD;
		    tw.local.results[i].name = tw.local.accounts[i].accountNO;&#xD;
	 &#xD;
	 }&#xD;
	 &#xD;
	 &#xD;
	}&#xD;
}&#xD;
//if(tw.local.accounts != null &amp;&amp; tw.local.accounts.listLength != 0)&#xD;
//{&#xD;
//&#xD;
//	for(var i=0;i&lt;tw.local.accounts.listLength;i++)&#xD;
//	{&#xD;
//		//filter by the customer accounts by the selected collection currency&#xD;
//		if(tw.local.currency != "" &amp;&amp; tw.local.currency != null)&#xD;
//		{&#xD;
//			if(tw.local.accounts[i].currencyCode.toLowerCase() == tw.local.currency.toLowerCase())&#xD;
//			{&#xD;
//				tw.local.results[i] = new tw.object.NameValuePair();&#xD;
//				tw.local.results[i].value = tw.local.accounts[i].accountNO;&#xD;
//				tw.local.results[i].name = tw.local.accounts[i].branchCode;&#xD;
//			}&#xD;
//						&#xD;
//		}&#xD;
//		else&#xD;
//		{&#xD;
//			tw.local.results[i] = new tw.object.NameValuePair();&#xD;
//			tw.local.results[i].value = tw.local.accounts[i].accountNO;&#xD;
//			tw.local.results[i].name = tw.local.accounts[i].branchCode;&#xD;
//		}&#xD;
//		&#xD;
//	}&#xD;
//}</script>
                <isRule>false</isRule>
                <guid>b67d7ed6-448c-4610-b1ad-a530e27a562e</guid>
                <versionId>10e597e0-0923-4361-b756-5c4a0721f322</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8e3e8cae-790a-43db-80b1-d7cf801f27d3</processItemId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3e087a7f-a13d-45f3-ba15-f9dbda6d9381</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:2663</guid>
            <versionId>53807f8e-a244-4707-974b-218aea56c3ab</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="326" y="175">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3e087a7f-a13d-45f3-ba15-f9dbda6d9381</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>c6acc603-7e32-4799-b8e0-a9f81d943852</guid>
                <versionId>426ab583-0b7b-4000-95df-f85d453bf2f0</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b96b8377-d87b-4bdf-ac48-0da8dec54dcd</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.3e087a7f-a13d-45f3-ba15-f9dbda6d9381</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ed0ad0f4-8e47-4442-86cc-d887833247e7</guid>
                    <versionId>2f6d58ed-2bb0-4a9f-817c-ee0e92cd5830</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e17bb876-fbc6-4d3c-bc2f-491380cb99c8</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.3e087a7f-a13d-45f3-ba15-f9dbda6d9381</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>cb1b4ae9-3ca3-4452-933f-ef4d0a0b1bd3</guid>
                    <versionId>31b13ab0-8db6-4aee-a567-ae0debd9b08b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f319cc73-b265-43cc-b7a7-7f7d60557bf2</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.3e087a7f-a13d-45f3-ba15-f9dbda6d9381</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Get Customer Account"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>52d24323-d578-4f1b-bf14-cc6e8b12af24</guid>
                    <versionId>a368b40d-5587-4caf-bb0c-8ddce9a4da40</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0eb0ac24-516a-4377-8784-6a90131b9cab</processItemId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <name>Retrieve customer accounts</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.8e3e8cae-790a-43db-80b1-d7cf801f27d3</errorHandlerItemId>
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:75c6</guid>
            <versionId>5fb827dc-1721-4552-8537-7865f0b19251</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="239" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:2663</errorHandlerItem>
                <errorHandlerItemId>2025.8e3e8cae-790a-43db-80b1-d7cf801f27d3</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.75ff304b-c398-4669-9ed0-70ab03152e8d</attachedProcessRef>
                <guid>50519fcd-55e3-4c71-8a48-067f3a2bd9c0</guid>
                <versionId>f9078181-e78d-4726-8733-ac3e1f9f6f94</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.14b2bd9b-e535-4348-b8d2-9a5fe73ee4d6</parameterMappingId>
                    <processParameterId>2055.d9d57b1e-9356-4664-87b3-27347adfe41d</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e3fc6703-c642-41ca-bdb1-fa410a805af1</guid>
                    <versionId>000eb292-8323-43e1-8bd2-fedc349cf3d2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e57bc90b-3329-4b9c-aed5-a5db671ce0e8</parameterMappingId>
                    <processParameterId>2055.2f75a761-4364-4c36-b175-6c50df867d38</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6e0f6d5f-876e-49c0-9c5f-605eb498db75</guid>
                    <versionId>06eb651d-eae9-42a8-9b4d-90a97014411a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.24e38097-2621-4636-a47e-635a35aa1cc6</parameterMappingId>
                    <processParameterId>2055.c60b2a07-f8cb-402a-958b-7a1bd44b7358</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>217639b9-64d3-4a5f-b5b4-ff06e9446a8b</guid>
                    <versionId>183df347-47ea-41d4-b7e3-88da3894d49b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2f0a5bde-2e2d-4638-9753-5d8f840362fc</parameterMappingId>
                    <processParameterId>2055.9e696ce2-3597-4dd7-bed9-e8cf45602b03</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>1139a70f-91ff-4961-87c9-4f25368fc1fb</guid>
                    <versionId>1ebb7b46-2e56-49a1-b8fe-11fe98bb0c14</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a9ceee29-e6f4-469f-871e-7301019287ea</parameterMappingId>
                    <processParameterId>2055.98ee7d56-00f0-4503-9aa9-a320f73a3361</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.error.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>dc9f6bd0-81f1-4673-966f-0657bd3517dc</guid>
                    <versionId>3596008c-6713-41e9-a2f4-fb215215ab1d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b7e9eec7-fb24-4ef9-9a64-9ef3fb2880f8</parameterMappingId>
                    <processParameterId>2055.ca98719d-77f8-4fde-9663-52914ee14551</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>13311adf-359e-43f4-b1aa-7de1e260eb78</guid>
                    <versionId>3a2f9b76-a35e-43ea-80db-cec914b08447</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3e22b405-ceea-4153-8d29-3aa6b7328add</parameterMappingId>
                    <processParameterId>2055.e8bee736-2a93-4760-aafb-38e55006f6b9</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC creation and Amendement"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>45e9c661-bd6a-4243-8c5a-a34eefb71734</guid>
                    <versionId>4f12c46d-e756-479c-bbf0-69c515982e82</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="status">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.497179f8-8bb0-40c4-86ae-7cf7a78253ec</parameterMappingId>
                    <processParameterId>2055.6a8c1cae-f653-4cbe-a332-230489206d39</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.status</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a7e8a391-9afd-4c6d-acff-6d711479587f</guid>
                    <versionId>511bfc40-569f-4598-ba7e-32d27a09ed9e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNo">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0935f729-ba20-4738-bf62-376d0705d255</parameterMappingId>
                    <processParameterId>2055.f3935930-ab2f-433c-8bb0-566cbabffe45</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>08d61627-72ee-4360-92a2-264706325e28</guid>
                    <versionId>55d93b3d-17af-45dc-9b1e-f8dc6f391940</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ef4150be-1d15-421f-b72d-cb247919fd06</parameterMappingId>
                    <processParameterId>2055.9d786589-d5a5-4fb6-8913-b6b1a1a57b1c</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4253682e-18a7-4d5f-8ffe-551672a515e9</guid>
                    <versionId>6fc51d2f-12af-4435-8399-0dbcc32f90f4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.689e5e02-13f6-45e9-9b2e-e04366cd688b</parameterMappingId>
                    <processParameterId>2055.c0ca46fc-7eff-41ee-961f-0fc0dc8f8b3a</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>929aae52-a0bc-4679-b8d5-e32988a07cd8</guid>
                    <versionId>93683d3d-e9a5-4e88-bb60-aa4d755e986f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="accountsList">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5dacfae4-4d04-4862-a8f4-d352efe6551d</parameterMappingId>
                    <processParameterId>2055.cdb1e363-7e09-4015-8916-b750b411405d</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.accounts</value>
                    <classRef>/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>da3b8a83-21af-41a8-84e9-e0c84a2597f1</guid>
                    <versionId>b2f0c788-5398-47bd-95af-b9c9b12f9030</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f432992b-448c-4662-bb7d-13bd7bd3e83a</parameterMappingId>
                    <processParameterId>2055.b7a97c7d-99cb-44db-827f-0333fa136b3e</processParameterId>
                    <parameterMappingParentId>3012.8eade1ce-8672-43c3-bec5-2a8f3d3297d7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7cc533d2-e56e-4579-9ff1-6be9cd705918</guid>
                    <versionId>e5c664d3-f39c-44db-b476-937ec0bc68de</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.918487b3-3e5f-4840-8b02-dc891914c889</processItemId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.e6ab5f9c-0ea5-43da-860e-be557f38562b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:761a</guid>
            <versionId>8fa79b46-080c-4380-9819-f799f817fc03</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.33cbcee5-ea93-4903-9957-c22a97edf64d</processItemPrePostId>
                <processItemId>2025.918487b3-3e5f-4840-8b02-dc891914c889</processItemId>
                <location>1</location>
                <script>//tw.local.error = {};</script>
                <guid>a69dfe6f-c80f-4cd1-8e9f-f565ec68c13a</guid>
                <versionId>4eb85889-d8f8-4749-a653-d606c2ae8aaf</versionId>
            </processPrePosts>
            <layoutData x="592" y="198">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.e6ab5f9c-0ea5-43da-860e-be557f38562b</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>9deeae27-5864-4fa3-a967-f94feb1160d7</guid>
                <versionId>3dcce751-853a-4112-857c-1b796b2429c5</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.08540e2c-7101-4a2f-a6a4-44810fa1c835</parameterMappingId>
                    <processParameterId>2055.608a5f4e-b17a-4d3c-b4c3-6857406a498a</processParameterId>
                    <parameterMappingParentId>3007.e6ab5f9c-0ea5-43da-860e-be557f38562b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a330892b-e04e-4955-8c9c-2097e777a424</guid>
                    <versionId>5f13d14d-f3ef-435e-b1ad-06de4c644838</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.28f3f5c7-6afb-4e33-875c-264a3c228063</processItemId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.660e7eb8-**************-f8053e45d0c4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:75c2</guid>
            <versionId>e7fdc768-a0d7-40f8-ade8-3cb12934f5b3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.660e7eb8-**************-f8053e45d0c4</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a3278935-0c30-42e9-ac09-3adc5baeeb3c</guid>
                <versionId>20ad3c17-280a-420b-a95f-54b3493b489d</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.0eb0ac24-516a-4377-8784-6a90131b9cab</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftTop" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Ajax get customer accounts" id="1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>false</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.0bc98ce8-10aa-460a-8857-8a8600dafc90">
                            
                            
                            <ns16:documentation textFormat="text/plain">//&amp;quot;08732459&amp;quot;</ns16:documentation>
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"13996525"&#xD;
//"08732459_EGP"&#xD;
"08732459"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="collectionCurrency" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"EUR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.208f75bb-611b-4393-83d6-d60470c4a6a4" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.7fdee937-d555-46b6-88d2-46c40e165c27" />
                        
                        
                        <ns16:dataOutput name="isSuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.0bc98ce8-10aa-460a-8857-8a8600dafc90</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.208f75bb-611b-4393-83d6-d60470c4a6a4</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.7fdee937-d555-46b6-88d2-46c40e165c27</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="b915b8d0-1d87-47bf-8452-e941f310ffd5">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="61f3292d-11fa-4706-86c3-5c8da27a170a" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a71fea67-c43f-4b82-80b9-6be75bfec81e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>28f3f5c7-6afb-4e33-875c-264a3c228063</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0eb0ac24-516a-4377-8784-6a90131b9cab</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5b03d536-3c58-4923-85f2-8905b375d4d5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>918487b3-3e5f-4840-8b02-dc891914c889</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>dc5f212d-289c-491c-888c-d8580dcae9fb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8e3e8cae-790a-43db-80b1-d7cf801f27d3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5b525d87-7a0a-428c-89d4-08317d9fca7c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a71fea67-c43f-4b82-80b9-6be75bfec81e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.94386f9e-6a02-4984-86b7-0f31799e48ea</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="28f3f5c7-6afb-4e33-875c-264a3c228063">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:75c2</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e3a37d6a-1ea2-469c-88d7-1b6bc0014b2c</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a71fea67-c43f-4b82-80b9-6be75bfec81e" targetRef="0eb0ac24-516a-4377-8784-6a90131b9cab" name="To check if currency concatenated to the CIF" id="2027.94386f9e-6a02-4984-86b7-0f31799e48ea">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.75ff304b-c398-4669-9ed0-70ab03152e8d" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Retrieve customer accounts" id="0eb0ac24-516a-4377-8784-6a90131b9cab">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="239" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e4774c62-a4c9-409a-814e-71a375e19e80</ns16:incoming>
                        
                        
                        <ns16:incoming>2027.94386f9e-6a02-4984-86b7-0f31799e48ea</ns16:incoming>
                        
                        
                        <ns16:outgoing>f97a67fb-b708-40ba-8387-405b3698a3c4</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ca98719d-77f8-4fde-9663-52914ee14551</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e8bee736-2a93-4760-aafb-38e55006f6b9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC creation and Amendement"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9d786589-d5a5-4fb6-8913-b6b1a1a57b1c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.b7a97c7d-99cb-44db-827f-0333fa136b3e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d9d57b1e-9356-4664-87b3-27347adfe41d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c0ca46fc-7eff-41ee-961f-0fc0dc8f8b3a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f3935930-ab2f-433c-8bb0-566cbabffe45</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.cdb1e363-7e09-4015-8916-b750b411405d</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42">tw.local.accounts</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9e696ce2-3597-4dd7-bed9-e8cf45602b03</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.6a8c1cae-f653-4cbe-a332-230489206d39</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.status</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.98ee7d56-00f0-4503-9aa9-a320f73a3361</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.error.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.2f75a761-4364-4c36-b175-6c50df867d38</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="0eb0ac24-516a-4377-8784-6a90131b9cab" targetRef="5b03d536-3c58-4923-85f2-8905b375d4d5" name="To map output" id="f97a67fb-b708-40ba-8387-405b3698a3c4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a69</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="map output" id="5b03d536-3c58-4923-85f2-8905b375d4d5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="413" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f97a67fb-b708-40ba-8387-405b3698a3c4</ns16:incoming>
                        
                        
                        <ns16:outgoing>e3a37d6a-1ea2-469c-88d7-1b6bc0014b2c</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.NameValuePair();&#xD;
if(!!tw.local.accounts){&#xD;
      &#xD;
     &#xD;
	 for(var i=0;i&lt;tw.local.accounts.listLength;i++)&#xD;
	 {&#xD;
	    if(!!tw.local.collectionCurrency){&#xD;
	       if(tw.local.collectionCurrency.toLowerCase() == tw.local.accounts[i].currencyCode.toLowerCase()){&#xD;
	          tw.local.results[i] = new tw.object.NameValuePair();&#xD;
                tw.local.results[i].value = tw.local.accounts[i].branchCode;&#xD;
		    tw.local.results[i].name = tw.local.accounts[i].accountNO;&#xD;
	       }&#xD;
	    }&#xD;
	 else{&#xD;
	          tw.local.results[i] = new tw.object.NameValuePair();&#xD;
                tw.local.results[i].value = tw.local.accounts[i].branchCode;&#xD;
		    tw.local.results[i].name = tw.local.accounts[i].accountNO;&#xD;
	 &#xD;
	 }&#xD;
	 &#xD;
	 &#xD;
	}&#xD;
}&#xD;
//if(tw.local.accounts != null &amp;&amp; tw.local.accounts.listLength != 0)&#xD;
//{&#xD;
//&#xD;
//	for(var i=0;i&lt;tw.local.accounts.listLength;i++)&#xD;
//	{&#xD;
//		//filter by the customer accounts by the selected collection currency&#xD;
//		if(tw.local.currency != "" &amp;&amp; tw.local.currency != null)&#xD;
//		{&#xD;
//			if(tw.local.accounts[i].currencyCode.toLowerCase() == tw.local.currency.toLowerCase())&#xD;
//			{&#xD;
//				tw.local.results[i] = new tw.object.NameValuePair();&#xD;
//				tw.local.results[i].value = tw.local.accounts[i].accountNO;&#xD;
//				tw.local.results[i].name = tw.local.accounts[i].branchCode;&#xD;
//			}&#xD;
//						&#xD;
//		}&#xD;
//		else&#xD;
//		{&#xD;
//			tw.local.results[i] = new tw.object.NameValuePair();&#xD;
//			tw.local.results[i].value = tw.local.accounts[i].accountNO;&#xD;
//			tw.local.results[i].name = tw.local.accounts[i].branchCode;&#xD;
//		}&#xD;
//		&#xD;
//	}&#xD;
//}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="5b03d536-3c58-4923-85f2-8905b375d4d5" targetRef="28f3f5c7-6afb-4e33-875c-264a3c228063" name="To End" id="e3a37d6a-1ea2-469c-88d7-1b6bc0014b2c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="accounts" id="2056.b46156ba-9a2f-4985-8437-5a9431908f9f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="status" id="2056.ec23c960-8cce-4ca3-8fce-2c1ef5a5fc2d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.259a9f6d-9c04-4515-84fd-e65a16bd583c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:endEvent name="End Event" id="918487b3-3e5f-4840-8b02-dc891914c889">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="592" y="198" width="24" height="24" />
                            
                            
                            <ns3:preAssignmentScript>//tw.local.error = {};</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a255199f-2a90-4216-8e6e-374d86fa8d04</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" />
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="6c6fe6b4-d22d-4938-8507-0436cb027d78" eventImplId="e3d8e11b-beeb-4d2c-87d3-67358fde47ed">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="check if currency concatenated to the CIF" id="dc5f212d-289c-491c-888c-d8580dcae9fb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="87" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e4774c62-a4c9-409a-814e-71a375e19e80</ns16:outgoing>
                        
                        
                        <ns16:script>var lengthBefore = tw.local.data.length;&#xD;
var splitString = tw.local.data.split('_');&#xD;
if(splitString[0].length &lt;  lengthBefore)&#xD;
{&#xD;
	tw.local.cif = splitString[0];&#xD;
	tw.local.currency = splitString[1];&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.cif = tw.local.data;&#xD;
	tw.local.currency = ""; &#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="dc5f212d-289c-491c-888c-d8580dcae9fb" targetRef="0eb0ac24-516a-4377-8784-6a90131b9cab" name="To Retrieve customer accounts" id="e4774c62-a4c9-409a-814e-71a375e19e80">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="cif" id="2056.d1193417-6c7e-4b7b-8ee9-06c7a8d64434" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currency" id="2056.5d4912d0-40c6-43cc-8441-5ef3538a64ee" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exception Handling" id="8e3e8cae-790a-43db-80b1-d7cf801f27d3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="326" y="175" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e7ee4746-1aee-4a1b-8a5f-feae26feb32c</ns16:incoming>
                        
                        
                        <ns16:outgoing>a255199f-2a90-4216-8e6e-374d86fa8d04</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Get Customer Account"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8e3e8cae-790a-43db-80b1-d7cf801f27d3" targetRef="918487b3-3e5f-4840-8b02-dc891914c889" name="To End Event" id="a255199f-2a90-4216-8e6e-374d86fa8d04">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="0eb0ac24-516a-4377-8784-6a90131b9cab" parallelMultiple="false" name="Error" id="5b525d87-7a0a-428c-89d4-08317d9fca7c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="274" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e7ee4746-1aee-4a1b-8a5f-feae26feb32c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="6ddfa253-2093-4261-8ba9-37ada571b03c" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="406ea209-77d3-4232-8b3f-68f9bfabc306" eventImplId="1cc86bd0-408b-41c7-8f8f-6147673431b0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="5b525d87-7a0a-428c-89d4-08317d9fca7c" targetRef="8e3e8cae-790a-43db-80b1-d7cf801f27d3" name="To Exception Handling" id="e7ee4746-1aee-4a1b-8a5f-feae26feb32c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f97a67fb-b708-40ba-8387-405b3698a3c4</processLinkId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0eb0ac24-516a-4377-8784-6a90131b9cab</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2a69</endStateId>
            <toProcessItemId>2025.5b03d536-3c58-4923-85f2-8905b375d4d5</toProcessItemId>
            <guid>49a56692-51d7-4884-a609-43d442e39305</guid>
            <versionId>5c8138a8-060a-4f2a-b572-613b8d68fd90</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0eb0ac24-516a-4377-8784-6a90131b9cab</fromProcessItemId>
            <toProcessItemId>2025.5b03d536-3c58-4923-85f2-8905b375d4d5</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e3a37d6a-1ea2-469c-88d7-1b6bc0014b2c</processLinkId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5b03d536-3c58-4923-85f2-8905b375d4d5</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.28f3f5c7-6afb-4e33-875c-264a3c228063</toProcessItemId>
            <guid>837405e4-7265-4502-b23f-b73ac45c754f</guid>
            <versionId>b3e65cdf-a84f-40cd-bed1-7730473410c1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5b03d536-3c58-4923-85f2-8905b375d4d5</fromProcessItemId>
            <toProcessItemId>2025.28f3f5c7-6afb-4e33-875c-264a3c228063</toProcessItemId>
        </link>
        <link name="To Retrieve customer accounts">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e4774c62-a4c9-409a-814e-71a375e19e80</processLinkId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.dc5f212d-289c-491c-888c-d8580dcae9fb</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.0eb0ac24-516a-4377-8784-6a90131b9cab</toProcessItemId>
            <guid>f918cc4e-d8c7-4987-9f8d-ab80698d45f4</guid>
            <versionId>dd52b033-80a0-423a-93ef-1d62823ac141</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.dc5f212d-289c-491c-888c-d8580dcae9fb</fromProcessItemId>
            <toProcessItemId>2025.0eb0ac24-516a-4377-8784-6a90131b9cab</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a255199f-2a90-4216-8e6e-374d86fa8d04</processLinkId>
            <processId>1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8e3e8cae-790a-43db-80b1-d7cf801f27d3</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.918487b3-3e5f-4840-8b02-dc891914c889</toProcessItemId>
            <guid>187a742a-b78c-4499-b252-703aaa4981ff</guid>
            <versionId>e5d07fc2-a774-41cf-93a4-c8aae1a180fc</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8e3e8cae-790a-43db-80b1-d7cf801f27d3</fromProcessItemId>
            <toProcessItemId>2025.918487b3-3e5f-4840-8b02-dc891914c889</toProcessItemId>
        </link>
    </process>
</teamworks>

