{"id": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "versionId": "2f282072-cc7e-4f91-a680-b115235ae194", "name": "Create amend audit service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "Create amend audit service", "lastModified": "1698318251227", "lastModifiedBy": "fatma", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.f9db43af-0083-4085-8e05-252c7bc1525c", "2025.f9db43af-0083-4085-8e05-252c7bc1525c"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7089", "versionId": "2f282072-cc7e-4f91-a680-b115235ae194", "dependencySummary": "<dependencySummary id=\"bpdid:266f6f4955d8489f:7b0b9b81:18b66504d45:3d74\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.60dc1e7b-3895-478c-82bb-f29568774e84\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"ba1977c7-1543-4f12-8801-3fae56f9e277\"},{\"incoming\":[\"d7d4d18a-bd71-4c6b-860c-41797c800e2f\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7087\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"01516e5f-85fc-45a4-8336-888f9e7ec3b1\"},{\"targetRef\":\"f9db43af-0083-4085-8e05-252c7bc1525c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To parent request no not empty?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.60dc1e7b-3895-478c-82bb-f29568774e84\",\"sourceRef\":\"ba1977c7-1543-4f12-8801-3fae56f9e277\"},{\"startQuantity\":1,\"outgoing\":[\"eda2dc71-b433-4552-89dd-e2801b59e36d\"],\"incoming\":[\"47bfefe4-7514-4e92-8fa7-053ab8bf00c4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":285,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Insert query\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"6cccd4e9-cc7c-4062-85b2-a10af0901632\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.query = \\\"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\\\"\\r\\n+\\\"values('\\\"+tw.local.odcRequest.requestNo+\\\"','\\\"+tw.local.odcRequest.requestNature.name+\\\"','\\\"+tw.local.odcRequest.requestType.name+\\\"',sysdate,'\\\"+tw.local.odcRequest.BasicDetails.requestState+\\\"','\\\"+tw.local.odcRequest.appInfo.status+\\\"','\\\"+tw.local.odcRequest.appInfo.subStatus+\\\"','\\\"+tw.local.odcRequest.BasicDetails.contractStage+\\\"','\\\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\\\"','\\\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\\\"','\\\"+tw.local.odcRequest.BasicDetails.productCategory.value+\\\"','\\\"+tw.local.odcRequest.BasicDetails.commodityDescription+\\\"','\\\"+false+\\\"','\\\"+tw.local.odcRequest.CustomerInfo.cif+\\\"','\\\"+tw.local.odcRequest.CustomerInfo.customerName+\\\"','\\\"+tw.local.odcRequest.FcCollections.currency.name+\\\"','\\\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\\\"','\\\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\\\"','\\\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\\\"',sysdate,'\\\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\\\"','\\\"+tw.local.odcRequest.appInfo.stepName+\\\"','\\\"+tw.local.odcRequest.appInfo.instanceID+\\\"');\\\"\\r\\n\\/\\/'\\\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\\\"'\"]}},{\"targetRef\":\"1615fc68-5350-4fd2-8dee-a6115f36a883\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Execute statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"eda2dc71-b433-4552-89dd-e2801b59e36d\",\"sourceRef\":\"6cccd4e9-cc7c-4062-85b2-a10af0901632\"},{\"startQuantity\":1,\"outgoing\":[\"d7d4d18a-bd71-4c6b-860c-41797c800e2f\"],\"incoming\":[\"eda2dc71-b433-4552-89dd-e2801b59e36d\",\"6b928f1a-fc75-4129-8300-e6290bacf2e9\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":512,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Execute statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.query\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"1615fc68-5350-4fd2-8dee-a6115f36a883\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlResults\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"01516e5f-85fc-45a4-8336-888f9e7ec3b1\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"d7d4d18a-bd71-4c6b-860c-41797c800e2f\",\"sourceRef\":\"1615fc68-5350-4fd2-8dee-a6115f36a883\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"query\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c1a9df3f-d507-4b27-8766-04c7f72168aa\"},{\"outgoing\":[\"47bfefe4-7514-4e92-8fa7-053ab8bf00c4\",\"7d2d0ca6-22ca-439c-801f-3d5f9504608f\"],\"incoming\":[\"2027.60dc1e7b-3895-478c-82bb-f29568774e84\"],\"default\":\"47bfefe4-7514-4e92-8fa7-053ab8bf00c4\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":100,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"is Update?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"f9db43af-0083-4085-8e05-252c7bc1525c\"},{\"targetRef\":\"6cccd4e9-cc7c-4062-85b2-a10af0901632\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"47bfefe4-7514-4e92-8fa7-053ab8bf00c4\",\"sourceRef\":\"f9db43af-0083-4085-8e05-252c7bc1525c\"},{\"startQuantity\":1,\"outgoing\":[\"6b928f1a-fc75-4129-8300-e6290bacf2e9\"],\"incoming\":[\"7d2d0ca6-22ca-439c-801f-3d5f9504608f\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":283,\"y\":184,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Update query\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"98e51491-7304-42a1-8af6-bc22fe4af2ef\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.query = \\\"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,parentRequestNo,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\\\"\\r\\n+\\\"values('\\\"+tw.local.odcRequest.requestNo+\\\"','\\\"+tw.local.odcRequest.requestNature.name+\\\"','\\\"+tw.local.odcRequest.requestType.name+\\\"',sysdate,'\\\"+tw.local.odcRequest.BasicDetails.requestState+\\\"','\\\"+tw.local.odcRequest.appInfo.status+\\\"','\\\"+tw.local.odcRequest.appInfo.subStatus+\\\"','\\\"+tw.local.odcRequest.parentRequestNo+\\\"','\\\"+tw.local.odcRequest.BasicDetails.contractStage+\\\"','\\\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\\\"','\\\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\\\"','\\\"+tw.local.odcRequest.BasicDetails.productCategory.value+\\\"','\\\"+tw.local.odcRequest.BasicDetails.commodityDescription+\\\"','\\\"+false+\\\"','\\\"+tw.local.odcRequest.CustomerInfo.cif+\\\"','\\\"+tw.local.odcRequest.CustomerInfo.customerName+\\\"','\\\"+tw.local.odcRequest.FcCollections.currency.name+\\\"','\\\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\\\"','\\\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\\\"','\\\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\\\"',sysdate,'\\\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\\\"','\\\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\\\"','\\\"+tw.local.odcRequest.appInfo.stepName+\\\"','\\\"+tw.local.odcRequest.appInfo.instanceID+\\\"');\\\"\\r\\n\\/\\/'\\\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\\\"'\"]}},{\"targetRef\":\"98e51491-7304-42a1-8af6-bc22fe4af2ef\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNature != null && tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"7d2d0ca6-22ca-439c-801f-3d5f9504608f\",\"sourceRef\":\"f9db43af-0083-4085-8e05-252c7bc1525c\"},{\"targetRef\":\"1615fc68-5350-4fd2-8dee-a6115f36a883\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Execute statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"6b928f1a-fc75-4129-8300-e6290bacf2e9\",\"sourceRef\":\"98e51491-7304-42a1-8af6-bc22fe4af2ef\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestNumber\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.cc74ca5d-a7f0-4f86-8b0e-7ee1692b27ab\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"sqlResults\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.c2286d23-6bc2-4bba-8cbf-36aada72929a\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();\\nautoObject[0].sql = \\\"\\\";\\nautoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\nautoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\\nautoObject[0].parameters[0].value = null;\\nautoObject[0].parameters[0].type = \\\"\\\";\\nautoObject[0].parameters[0].mode = \\\"\\\";\\nautoObject[0].maxRows = 0;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"name\":\"queryList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.166981a3-1293-4eac-84d5-ca8099ce579f\"},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":949,\"y\":107,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Execute multiple statements\",\"dataInputAssociation\":[{\"targetRef\":\"2055.9695b715-db44-410b-8a74-65cf1d31d8ab\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.queryList\"]}}]},{\"targetRef\":\"2055.a634f531-c979-476c-91db-a17bf5e55c90\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"ab3ca23b-bc15-4cc6-86f4-5443af54d24b\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlResults\"]}}],\"sourceRef\":[\"2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e\"]}],\"calledElement\":\"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7\"},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":742,\"y\":98,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"multiple statements\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"27152ee5-08fa-43cf-8397-429860e1b017\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.queryList = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\\r\\ntw.local.queryList[0] = new tw.object.toolkit.TWSYS.SQLStatement();\\r\\ntw.local.queryList[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[0].value = tw.local.odcRequest.requestNo;\\r\\n\\r\\ntw.local.queryList[0].parameters[1] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[1].value = tw.local.odcRequest.requestNature.name;\\r\\n\\r\\ntw.local.queryList[0].parameters[2] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[2].value = tw.local.odcRequest.requestType.name;\\r\\n\\r\\ntw.local.queryList[0].parameters[3] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[3].value = tw.local.odcRequest.requestDate;\\r\\n\\r\\n\\r\\ntw.local.queryList[0].parameters[4] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[4].value = tw.local.odcRequest.BasicDetails.requestState;\\r\\n\\r\\ntw.local.queryList[0].parameters[5] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[5].value = tw.local.odcRequest.appInfo.status;\\r\\n\\r\\n\\r\\ntw.local.queryList[0].parameters[6] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[6].value = tw.local.odcRequest.appInfo.subStatus;\\r\\n\\r\\ntw.local.queryList[0].parameters[7] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[7].value = tw.local.odcRequest.BasicDetails.contractStage;\\r\\n\\r\\ntw.local.queryList[0].parameters[8] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[8].value = tw.local.odcRequest.BasicDetails.exportPurpose.value;\\r\\n\\r\\ntw.local.queryList[0].parameters[9] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[9].value = tw.local.odcRequest.BasicDetails.paymentTerms.value;\\r\\n\\r\\ntw.local.queryList[0].parameters[10] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[10].value = tw.local.odcRequest.BasicDetails.productCategory.value;\\r\\n\\r\\ntw.local.queryList[0].parameters[11] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[11].value = tw.local.odcRequest.BasicDetails.commodityDescription;\\r\\n\\r\\ntw.local.queryList[0].parameters[12] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[12].value = false;\\r\\n\\r\\ntw.local.queryList[0].parameters[13] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[13].value = tw.local.odcRequest.CustomerInfo.cif;\\r\\n\\r\\ntw.local.queryList[0].parameters[14] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[14].value = tw.local.odcRequest.CustomerInfo.customerName;\\r\\n\\r\\ntw.local.queryList[0].parameters[15] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[15].value = tw.local.odcRequest.FcCollections.currency.name;\\r\\n\\r\\ntw.local.queryList[0].parameters[16] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[16].value = tw.local.odcRequest.FcCollections.standardExchangeRate;\\r\\n\\r\\ntw.local.queryList[0].parameters[17] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[17].value = tw.local.odcRequest.FcCollections.negotiatedExchangeRate;\\r\\n\\r\\ntw.local.queryList[0].parameters[18] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[18].value = tw.local.odcRequest.FcCollections.totalAllocatedAmount;\\r\\n\\r\\ntw.local.queryList[0].parameters[19] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[19].value = tw.local.odcRequest.FinancialDetailsBR.currency.name;\\r\\n\\r\\ntw.local.queryList[0].parameters[20] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[20].value = sysdate;\\r\\n\\r\\ntw.local.queryList[0].parameters[21] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[21].value = tw.local.odcRequest.FinancialDetailsBR.documentAmount;\\r\\n\\r\\ntw.local.queryList[0].parameters[22] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[22].value = tw.local.odcRequest.FinancialDetailsBR.amountAdvanced;\\r\\n\\r\\ntw.local.queryList[0].parameters[23] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[23].value = tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value;\\r\\n\\r\\ntw.local.queryList[0].parameters[24] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[24].value = tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value;\\r\\n\\r\\ntw.local.queryList[0].parameters[25] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[25].value = tw.local.odcRequest.appInfo.stepName;\\r\\n\\r\\ntw.local.queryList[0].parameters[26] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[0].parameters[26].value = tw.local.odcRequest.appInfo.instanceID;\\r\\n\\r\\ntw.local.queryList[0].sql = tw.local.query = \\\"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);\\\";\\r\\ntw.local.queryList[0].maxRows = -1;\\r\\n\\r\\nif(tw.local.odcRequest.BasicDetails.Bills.listLength != 0)\\r\\n{\\r\\n\\ttw.local.queryList[1] = new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\ttw.local.queryList[1].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\tfor(var i=0;i<tw.local.)\\r\\n\\t{\\r\\n\\t\\r\\n\\t}\\r\\n}\\r\\ntw.local.queryList[1].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\\r\\ntw.local.queryList[1].parameters[0].value = tw.local.odcRequest.requestNo;\\r\\n\\r\\ntw.local.queryList[1].sql = tw.local.query = \\\"insert into ODC_REQUESTINFO ();\\\";\\r\\ntw.local.queryList[1].maxRows = -1;\\r\\n\"]}},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":691,\"y\":225,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Insert parties\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"937313a5-c693-4dbe-87ff-5bc4931cc1c4\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\\r\\nvar par = 0;\\r\\nvar ind = 0;\\r\\nfunction initSql(){\\r\\n\\tvar sqlsttmnt = new tw.object.SQLStatement();\\r\\n\\tsqlsttmnt.parameters = new tw.object.listOf.SQLParameter();\\r\\n\\treturn sqlsttmnt;\\r\\n}\\r\\n\\r\\ntw.local.sqlStatements[ind] = initSql();\\r\\n\\/\\/ adding the sql statement abdelrahman wrote temporarily - to be restructured\\r\\n\\r\\ntw.local.sqlStatements[ind++].sql = tw.local.query;\\r\\n\\r\\n\\/\\/inserting parties\\r\\n\\/\\/to be changed after changing parties to list)\\r\\n\\/\\/for(var p=0 ; p< 3 ; p++){\\r\\ntw.local.sqlStatements[ind] = initSql();\\r\\ntw.local.sqlStatements[ind].sql =\\\"Insert into ODC_PARTIESINFO (PARTYID , PARTYTYPE, CIF, REQUESRID) VALUES (?, ?, ?, ? );\\\"\\r\\n\\r\\ntw.local.sqlStatements[ind].parameters[par++] = \\r\\n \\r\\n\\/\\/ }\\r\\n\"]}},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ee764bf6-eb5f-4208-8320-828cbf64c02e\"},{\"itemSubjectRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"name\":\"sqlStatements\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.ce6180b9-2169-4336-865f-c352a900e59c\"}],\"laneSet\":[{\"id\":\"2e483409-9a1d-4bdc-8d79-e232083cae7e\",\"lane\":[{\"flowNodeRef\":[\"ba1977c7-1543-4f12-8801-3fae56f9e277\",\"01516e5f-85fc-45a4-8336-888f9e7ec3b1\",\"6cccd4e9-cc7c-4062-85b2-a10af0901632\",\"1615fc68-5350-4fd2-8dee-a6115f36a883\",\"f9db43af-0083-4085-8e05-252c7bc1525c\",\"98e51491-7304-42a1-8af6-bc22fe4af2ef\",\"ab3ca23b-bc15-4cc6-86f4-5443af54d24b\",\"27152ee5-08fa-43cf-8397-429860e1b017\",\"937313a5-c693-4dbe-87ff-5bc4931cc1c4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"48b52bdb-ee44-455d-8fc1-49b2264b356f\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Create amend audit service\",\"declaredType\":\"process\",\"id\":\"1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7\",\"epvProcessLinkId\":\"c58e638c-e5f0-4675-8baa-96b8fd0432f8\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.96457aad-7a8d-49ff-80b0-2a989d85568f\"]}],\"outputSet\":[{}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"new\\\";\\nautoObject.requestNature.value = \\\"new\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.party = new tw.object.listOf.partyTypes();\\nautoObject.Parties.party[0] = new tw.object.partyTypes();\\nautoObject.Parties.party[0].partyCIF = \\\"\\\";\\nautoObject.Parties.party[0].partyId = \\\"\\\";\\nautoObject.Parties.party[0].partyName = \\\"\\\";\\nautoObject.Parties.party[0].country = \\\"\\\";\\nautoObject.Parties.party[0].language = \\\"\\\";\\nautoObject.Parties.party[0].refrence = \\\"\\\";\\nautoObject.Parties.party[0].address1 = \\\"\\\";\\nautoObject.Parties.party[0].address2 = \\\"\\\";\\nautoObject.Parties.party[0].address3 = \\\"\\\";\\nautoObject.Parties.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.party[0].partyType.name = \\\"\\\";\\nautoObject.Parties.party[0].partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.96457aad-7a8d-49ff-80b0-2a989d85568f\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.96457aad-7a8d-49ff-80b0-2a989d85568f", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"new\";\r\nautoObject.requestNature.value = \"new\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.party = new tw.object.listOf.partyTypes();\r\nautoObject.Parties.party[0] = new tw.object.partyTypes();\r\nautoObject.Parties.party[0].partyCIF = \"\";\r\nautoObject.Parties.party[0].partyId = \"\";\r\nautoObject.Parties.party[0].partyName = \"\";\r\nautoObject.Parties.party[0].country = \"\";\r\nautoObject.Parties.party[0].language = \"\";\r\nautoObject.Parties.party[0].refrence = \"\";\r\nautoObject.Parties.party[0].address1 = \"\";\r\nautoObject.Parties.party[0].address2 = \"\";\r\nautoObject.Parties.party[0].address3 = \"\";\r\nautoObject.Parties.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.party[0].partyType.name = \"\";\r\nautoObject.Parties.party[0].partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "9454736b-3870-4ad5-b58a-9e1f3fd18884", "versionId": "fe4cd7db-7451-41ca-b9bf-0dd61005b05a"}, "processVariable": [{"name": "query", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c1a9df3f-d507-4b27-8766-04c7f72168aa", "description": {"isNull": "true"}, "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "66390904-be44-4491-81e1-195ae5b27bd8", "versionId": "399c1ae2-c283-4617-9911-a6f26af0cf7b"}, {"name": "requestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.cc74ca5d-a7f0-4f86-8b0e-7ee1692b27ab", "description": {"isNull": "true"}, "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ada00d44-87b1-4860-a36c-54e36d26da57", "versionId": "415b72d2-d7d6-4f03-b7ab-eb68cee6b9ce"}, {"name": "sqlResults", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c2286d23-6bc2-4bba-8cbf-36aada72929a", "description": {"isNull": "true"}, "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f3451482-fa61-4dab-93ff-f57436a5c49c", "versionId": "eab80d88-b2c6-4d64-8d93-0e69c1ff287f"}, {"name": "queryList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.166981a3-1293-4eac-84d5-ca8099ce579f", "description": {"isNull": "true"}, "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "namespace": "2", "seq": "4", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "31c2a901-d34b-4e01-9cda-9a0408a1c633", "versionId": "d88c8abd-a6a9-4640-be7d-3311ce8dedd0"}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ee764bf6-eb5f-4208-8320-828cbf64c02e", "description": {"isNull": "true"}, "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fb367b97-fe79-4a09-b826-11b9422b90f2", "versionId": "123999d5-1e8a-4d80-b3f0-aab83fa852f0"}, {"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ce6180b9-2169-4336-865f-c352a900e59c", "description": {"isNull": "true"}, "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "namespace": "2", "seq": "6", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0f4d2e16-1cfc-4a3d-892c-be3cc98f594f", "versionId": "b2d085c6-82f5-4bf0-939c-3bddd8ecc0a3"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.98e51491-7304-42a1-8af6-bc22fe4af2ef", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "Update query", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.a72299b3-805e-4be2-910f-468768ccddf6", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:18b7", "versionId": "1355e904-0b7a-4b9c-8f3a-a2ed40769d7b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.a5b7fca0-77ac-4dd2-97cf-5c7d29a48867", "processItemId": "2025.98e51491-7304-42a1-8af6-bc22fe4af2ef", "location": "1", "script": {"isNull": "true"}, "guid": "bf066835-6dff-422f-9abb-257e1b67d560", "versionId": "71eb4d62-df79-4b8c-9140-621bf364210e"}, "layoutData": {"x": "283", "y": "184", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.a72299b3-805e-4be2-910f-468768ccddf6", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,parentRequestNo,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\"\r\r\n+\"values('\"+tw.local.odcRequest.requestNo+\"','\"+tw.local.odcRequest.requestNature.name+\"','\"+tw.local.odcRequest.requestType.name+\"',sysdate,'\"+tw.local.odcRequest.BasicDetails.requestState+\"','\"+tw.local.odcRequest.appInfo.status+\"','\"+tw.local.odcRequest.appInfo.subStatus+\"','\"+tw.local.odcRequest.parentRequestNo+\"','\"+tw.local.odcRequest.BasicDetails.contractStage+\"','\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\"','\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\"','\"+tw.local.odcRequest.BasicDetails.productCategory.value+\"','\"+tw.local.odcRequest.BasicDetails.commodityDescription+\"','\"+false+\"','\"+tw.local.odcRequest.CustomerInfo.cif+\"','\"+tw.local.odcRequest.CustomerInfo.customerName+\"','\"+tw.local.odcRequest.FcCollections.currency.name+\"','\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\"',sysdate,'\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\"','\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\"','\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\"','\"+tw.local.odcRequest.appInfo.stepName+\"','\"+tw.local.odcRequest.appInfo.instanceID+\"');\"\r\r\n//'\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\"'", "isRule": "false", "guid": "f5f5c181-90a5-4018-8a80-d1af846596ab", "versionId": "a67c079d-90cb-4a5f-89f0-1be8292c64c0"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f9db43af-0083-4085-8e05-252c7bc1525c", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "is Update?", "tWComponentName": "Switch", "tWComponentId": "3013.5aaf2482-10f3-4c39-9953-360815311d0c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:1820", "versionId": "348f5661-818e-471e-a135-66b9d3b2ab5c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "100", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.5aaf2482-10f3-4c39-9953-360815311d0c", "guid": "93dfdda5-6c02-498c-a3eb-31a2fc640d9a", "versionId": "ea5bdd63-4fd7-4eae-93b8-4760521e69a9", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.f6187c31-bc6d-4236-a38f-f420f2b2e72f", "switchId": "3013.5aaf2482-10f3-4c39-9953-360815311d0c", "seq": "1", "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:3d73", "condition": "tw.local.odcRequest.requestNature != null && tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest", "guid": "82e42e36-45fc-4cf1-9f06-a2794224bc4c", "versionId": "fa486926-1a3e-466f-8cee-4f3ce21a3909"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1615fc68-5350-4fd2-8dee-a6115f36a883", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "Execute statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.b454fbd5-43cb-49bd-9e10-fea81a634496", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-691", "versionId": "3a5160fe-8a18-407e-85eb-7d64ea4422f0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "512", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.b454fbd5-43cb-49bd-9e10-fea81a634496", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "10949f11-0664-44d4-96e3-6a6f90450382", "versionId": "df1aa44d-741d-43ac-8746-3a6d8943566a", "parameterMapping": [{"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.be29e4b1-1e89-4406-b5c8-f681d3031f45", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.b454fbd5-43cb-49bd-9e10-fea81a634496", "useDefault": "false", "value": "tw.local.sqlResults", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "80819e27-6b52-42b4-99b5-1017920e0ee8", "versionId": "51991954-de03-4b3c-8a6d-d784c1a90404", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e391d95c-7704-459b-9509-3ea0fcee6e90", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.b454fbd5-43cb-49bd-9e10-fea81a634496", "useDefault": "false", "value": "null", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "6a7ae82c-7130-48ea-b0e2-f051dc904b7f", "versionId": "523056ce-755f-4233-bdb2-38032333d51e", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.09ba5c47-34ca-4981-8f1e-f37c83ab7510", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.b454fbd5-43cb-49bd-9e10-fea81a634496", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "4f8f796d-3486-4541-ab59-440b6c4976ec", "versionId": "7b461df4-c82c-4c01-a7f5-0ecbedd89cb8", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.8007495c-1f83-4ad5-bd81-7d47277155d1", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.b454fbd5-43cb-49bd-9e10-fea81a634496", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ddf39a99-a8ed-4611-a98c-e167f115cab9", "versionId": "dffed26e-fd34-486a-a462-63b71c56be5d", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1062f248-d609-41ca-8209-213be2ea9cf6", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.b454fbd5-43cb-49bd-9e10-fea81a634496", "useDefault": "false", "value": "tw.local.query", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "63a274f1-afef-46cb-809c-dac48197c5c3", "versionId": "fcf80c05-6fa7-49a5-b64e-a011643f5a8c", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6cccd4e9-cc7c-4062-85b2-a10af0901632", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "Insert query", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.********-5385-45d5-b0a5-c5ed45eb12fc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-692", "versionId": "868a2ff7-1961-4190-a53d-************", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "285", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.********-5385-45d5-b0a5-c5ed45eb12fc", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\"\r\r\n+\"values('\"+tw.local.odcRequest.requestNo+\"','\"+tw.local.odcRequest.requestNature.name+\"','\"+tw.local.odcRequest.requestType.name+\"',sysdate,'\"+tw.local.odcRequest.BasicDetails.requestState+\"','\"+tw.local.odcRequest.appInfo.status+\"','\"+tw.local.odcRequest.appInfo.subStatus+\"','\"+tw.local.odcRequest.BasicDetails.contractStage+\"','\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\"','\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\"','\"+tw.local.odcRequest.BasicDetails.productCategory.value+\"','\"+tw.local.odcRequest.BasicDetails.commodityDescription+\"','\"+false+\"','\"+tw.local.odcRequest.CustomerInfo.cif+\"','\"+tw.local.odcRequest.CustomerInfo.customerName+\"','\"+tw.local.odcRequest.FcCollections.currency.name+\"','\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\"',sysdate,'\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\"','\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\"','\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\"','\"+tw.local.odcRequest.appInfo.stepName+\"','\"+tw.local.odcRequest.appInfo.instanceID+\"');\"\r\r\n//'\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\"'", "isRule": "false", "guid": "cb4650c6-4697-4f48-99c2-b7210ac64a38", "versionId": "270f6a53-e69e-4c5b-9e7e-fb078e774e50"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ab3ca23b-bc15-4cc6-86f4-5443af54d24b", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "Execute multiple statements", "tWComponentName": "SubProcess", "tWComponentId": "3012.2072bba9-e300-4f9a-b4ed-570ba97f0607", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:aff098473ecd546d:1d42df0a:18b1b2b8841:1c83", "versionId": "883498f9-31ca-41f6-83ca-65752a741093", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "949", "y": "107", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.2072bba9-e300-4f9a-b4ed-570ba97f0607", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7", "guid": "f72277f0-eb14-44ca-9468-335b4d452ef8", "versionId": "a282b8f5-3cd5-46ca-b4a5-f89bbc35491d", "parameterMapping": [{"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.497a896b-cbc7-4ffd-8c2c-203f7100497e", "processParameterId": "2055.9695b715-db44-410b-8a74-65cf1d31d8ab", "parameterMappingParentId": "3012.2072bba9-e300-4f9a-b4ed-570ba97f0607", "useDefault": "false", "value": "tw.local.queryList", "classRef": "/12.3990d8b4-983d-4250-bd06-3600f527fed0", "isList": "true", "isInput": "true", "guid": "a50bd39c-f1a4-49f0-978c-13aeaa0f2130", "versionId": "4df9d5f6-e4ff-4edb-acf0-249d802f795e", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e7fc6dab-6c48-481c-b999-acaf2ddc22b6", "processParameterId": "2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e", "parameterMappingParentId": "3012.2072bba9-e300-4f9a-b4ed-570ba97f0607", "useDefault": "false", "value": "tw.local.sqlResults", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "62c19069-0119-4491-a097-462ff40e817b", "versionId": "610b29e6-752d-48c7-a726-432037f52f66", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.9bb11a65-31fd-4707-bc23-889871f2d3cc", "processParameterId": "2055.a634f531-c979-476c-91db-a17bf5e55c90", "parameterMappingParentId": "3012.2072bba9-e300-4f9a-b4ed-570ba97f0607", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "93fa5b09-bb77-429f-a301-c08c9ca17314", "versionId": "a40d1287-bc9b-4168-afbe-70135dd29317", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.27152ee5-08fa-43cf-8397-429860e1b017", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "multiple statements", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.830687d0-62fc-486f-ad98-f675dedbdc5a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4a051bcdb927c971:-1aedb8c3:18b41a2c30b:-87a", "versionId": "9b76e029-1cf6-4605-bba1-31125c7f767e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "742", "y": "98", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.830687d0-62fc-486f-ad98-f675dedbdc5a", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.queryList = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\ntw.local.queryList[0] = new tw.object.toolkit.TWSYS.SQLStatement();\r\r\ntw.local.queryList[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[0].value = tw.local.odcRequest.requestNo;\r\r\n\r\r\ntw.local.queryList[0].parameters[1] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[1].value = tw.local.odcRequest.requestNature.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[2] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[2].value = tw.local.odcRequest.requestType.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[3] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[3].value = tw.local.odcRequest.requestDate;\r\r\n\r\r\n\r\r\ntw.local.queryList[0].parameters[4] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[4].value = tw.local.odcRequest.BasicDetails.requestState;\r\r\n\r\r\ntw.local.queryList[0].parameters[5] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[5].value = tw.local.odcRequest.appInfo.status;\r\r\n\r\r\n\r\r\ntw.local.queryList[0].parameters[6] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[6].value = tw.local.odcRequest.appInfo.subStatus;\r\r\n\r\r\ntw.local.queryList[0].parameters[7] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[7].value = tw.local.odcRequest.BasicDetails.contractStage;\r\r\n\r\r\ntw.local.queryList[0].parameters[8] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[8].value = tw.local.odcRequest.BasicDetails.exportPurpose.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[9] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[9].value = tw.local.odcRequest.BasicDetails.paymentTerms.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[10] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[10].value = tw.local.odcRequest.BasicDetails.productCategory.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[11] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[11].value = tw.local.odcRequest.BasicDetails.commodityDescription;\r\r\n\r\r\ntw.local.queryList[0].parameters[12] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[12].value = false;\r\r\n\r\r\ntw.local.queryList[0].parameters[13] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[13].value = tw.local.odcRequest.CustomerInfo.cif;\r\r\n\r\r\ntw.local.queryList[0].parameters[14] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[14].value = tw.local.odcRequest.CustomerInfo.customerName;\r\r\n\r\r\ntw.local.queryList[0].parameters[15] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[15].value = tw.local.odcRequest.FcCollections.currency.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[16] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[16].value = tw.local.odcRequest.FcCollections.standardExchangeRate;\r\r\n\r\r\ntw.local.queryList[0].parameters[17] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[17].value = tw.local.odcRequest.FcCollections.negotiatedExchangeRate;\r\r\n\r\r\ntw.local.queryList[0].parameters[18] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[18].value = tw.local.odcRequest.FcCollections.totalAllocatedAmount;\r\r\n\r\r\ntw.local.queryList[0].parameters[19] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[19].value = tw.local.odcRequest.FinancialDetailsBR.currency.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[20] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[20].value = sysdate;\r\r\n\r\r\ntw.local.queryList[0].parameters[21] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[21].value = tw.local.odcRequest.FinancialDetailsBR.documentAmount;\r\r\n\r\r\ntw.local.queryList[0].parameters[22] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[22].value = tw.local.odcRequest.FinancialDetailsBR.amountAdvanced;\r\r\n\r\r\ntw.local.queryList[0].parameters[23] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[23].value = tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[24] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[24].value = tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[25] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[25].value = tw.local.odcRequest.appInfo.stepName;\r\r\n\r\r\ntw.local.queryList[0].parameters[26] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[26].value = tw.local.odcRequest.appInfo.instanceID;\r\r\n\r\r\ntw.local.queryList[0].sql = tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);\";\r\r\ntw.local.queryList[0].maxRows = -1;\r\r\n\r\r\nif(tw.local.odcRequest.BasicDetails.Bills.listLength != 0)\r\r\n{\r\r\n\ttw.local.queryList[1] = new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\ttw.local.queryList[1].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\tfor(var i=0;i<tw.local.)\r\r\n\t{\r\r\n\t\r\r\n\t}\r\r\n}\r\r\ntw.local.queryList[1].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[1].parameters[0].value = tw.local.odcRequest.requestNo;\r\r\n\r\r\ntw.local.queryList[1].sql = tw.local.query = \"insert into ODC_REQUESTINFO ();\";\r\r\ntw.local.queryList[1].maxRows = -1;\r\r\n", "isRule": "false", "guid": "ba792b92-25bf-4c2a-ac2f-6b3b9c570e62", "versionId": "99a52c8b-bffe-4404-b4e3-6ff89af5d2ef"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.01516e5f-85fc-45a4-8336-888f9e7ec3b1", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.6d34dc3a-ef29-4073-a9ed-d18c491b5874", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7087", "versionId": "a34ac8d6-2071-4aa0-b1ff-780f6afe6112", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.6d34dc3a-ef29-4073-a9ed-d18c491b5874", "haltProcess": "false", "guid": "a46c6783-eb6e-4e7e-b593-5da82ffb7dcd", "versionId": "32f8adde-c7b3-4b09-9862-bf00d44a935c"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.937313a5-c693-4dbe-87ff-5bc4931cc1c4", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "name": "Insert parties", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.d936282c-cc4b-4420-9a8f-fe42e580b5f7", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:266f6f4955d8489f:7b0b9b81:18b5b81b903:-49e0", "versionId": "bdfbc7f9-4213-4f5a-8271-421e0274259f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "691", "y": "225", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.d936282c-cc4b-4420-9a8f-fe42e580b5f7", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar par = 0;\r\r\nvar ind = 0;\r\r\nfunction initSql(){\r\r\n\tvar sqlsttmnt = new tw.object.SQLStatement();\r\r\n\tsqlsttmnt.parameters = new tw.object.listOf.SQLParameter();\r\r\n\treturn sqlsttmnt;\r\r\n}\r\r\n\r\r\ntw.local.sqlStatements[ind] = initSql();\r\r\n// adding the sql statement a<PERSON><PERSON><PERSON><PERSON> wrote temporarily - to be restructured\r\r\n\r\r\ntw.local.sqlStatements[ind++].sql = tw.local.query;\r\r\n\r\r\n//inserting parties\r\r\n//to be changed after changing parties to list)\r\r\n//for(var p=0 ; p< 3 ; p++){\r\r\ntw.local.sqlStatements[ind] = initSql();\r\r\ntw.local.sqlStatements[ind].sql =\"Insert into ODC_PARTIESINFO (PARTYID , PARTYTYPE, CIF, REQUESRID) VALUES (?, ?, ?, ? );\"\r\r\n\r\r\ntw.local.sqlStatements[ind].parameters[par++] = \r\r\n \r\r\n// }\r\r\n", "isRule": "false", "guid": "b4cd7c47-1399-497c-8809-691d1a82cda3", "versionId": "f9278a22-7f40-4a1b-a419-0d208d06a626"}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.70b7c9bb-1800-424e-abbc-fde9aaad04bd", "epvId": "/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "guid": "04895450-0edd-40ea-a731-8afd7c54dfb8", "versionId": "77e68121-d9da-4fd6-ba71-a8427e40f941"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Create amend audit service", "id": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "epvProcessLinkId": "c58e638c-e5f0-4675-8baa-96b8fd0432f8"}}}, "ns16:dataInput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.96457aad-7a8d-49ff-80b0-2a989d85568f", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"new\";\r\nautoObject.requestNature.value = \"new\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.party = new tw.object.listOf.partyTypes();\r\nautoObject.Parties.party[0] = new tw.object.partyTypes();\r\nautoObject.Parties.party[0].partyCIF = \"\";\r\nautoObject.Parties.party[0].partyId = \"\";\r\nautoObject.Parties.party[0].partyName = \"\";\r\nautoObject.Parties.party[0].country = \"\";\r\nautoObject.Parties.party[0].language = \"\";\r\nautoObject.Parties.party[0].refrence = \"\";\r\nautoObject.Parties.party[0].address1 = \"\";\r\nautoObject.Parties.party[0].address2 = \"\";\r\nautoObject.Parties.party[0].address3 = \"\";\r\nautoObject.Parties.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.party[0].partyType.name = \"\";\r\nautoObject.Parties.party[0].partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "useDefault": "true"}}}, "ns16:inputSet": {"ns16:dataInputRefs": "2055.96457aad-7a8d-49ff-80b0-2a989d85568f"}, "ns16:outputSet": ""}, "ns16:laneSet": {"id": "2e483409-9a1d-4bdc-8d79-e232083cae7e", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "48b52bdb-ee44-455d-8fc1-49b2264b356f", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["ba1977c7-1543-4f12-8801-3fae56f9e277", "01516e5f-85fc-45a4-8336-888f9e7ec3b1", "6cccd4e9-cc7c-4062-85b2-a10af0901632", "1615fc68-5350-4fd2-8dee-a6115f36a883", "f9db43af-0083-4085-8e05-252c7bc1525c", "98e51491-7304-42a1-8af6-bc22fe4af2ef", "ab3ca23b-bc15-4cc6-86f4-5443af54d24b", "27152ee5-08fa-43cf-8397-429860e1b017", "937313a5-c693-4dbe-87ff-5bc4931cc1c4"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "ba1977c7-1543-4f12-8801-3fae56f9e277", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.60dc1e7b-3895-478c-82bb-f29568774e84"}, "ns16:endEvent": {"name": "End", "id": "01516e5f-85fc-45a4-8336-888f9e7ec3b1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7087"}, "ns16:incoming": "d7d4d18a-bd71-4c6b-860c-41797c800e2f"}, "ns16:sequenceFlow": [{"sourceRef": "ba1977c7-1543-4f12-8801-3fae56f9e277", "targetRef": "f9db43af-0083-4085-8e05-252c7bc1525c", "name": "To parent request no not empty?", "id": "2027.60dc1e7b-3895-478c-82bb-f29568774e84", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "6cccd4e9-cc7c-4062-85b2-a10af0901632", "targetRef": "1615fc68-5350-4fd2-8dee-a6115f36a883", "name": "To Execute statement", "id": "eda2dc71-b433-4552-89dd-e2801b59e36d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "1615fc68-5350-4fd2-8dee-a6115f36a883", "targetRef": "01516e5f-85fc-45a4-8336-888f9e7ec3b1", "name": "To End", "id": "d7d4d18a-bd71-4c6b-860c-41797c800e2f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "f9db43af-0083-4085-8e05-252c7bc1525c", "targetRef": "6cccd4e9-cc7c-4062-85b2-a10af0901632", "name": "No", "id": "47bfefe4-7514-4e92-8fa7-053ab8bf00c4", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "f9db43af-0083-4085-8e05-252c7bc1525c", "targetRef": "98e51491-7304-42a1-8af6-bc22fe4af2ef", "name": "Yes", "id": "7d2d0ca6-22ca-439c-801f-3d5f9504608f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.requestNature != null && tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "98e51491-7304-42a1-8af6-bc22fe4af2ef", "targetRef": "1615fc68-5350-4fd2-8dee-a6115f36a883", "name": "To Execute statement", "id": "6b928f1a-fc75-4129-8300-e6290bacf2e9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Insert query", "id": "6cccd4e9-cc7c-4062-85b2-a10af0901632", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "285", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "47bfefe4-7514-4e92-8fa7-053ab8bf00c4", "ns16:outgoing": "eda2dc71-b433-4552-89dd-e2801b59e36d", "ns16:script": "tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\"\r\r\n+\"values('\"+tw.local.odcRequest.requestNo+\"','\"+tw.local.odcRequest.requestNature.name+\"','\"+tw.local.odcRequest.requestType.name+\"',sysdate,'\"+tw.local.odcRequest.BasicDetails.requestState+\"','\"+tw.local.odcRequest.appInfo.status+\"','\"+tw.local.odcRequest.appInfo.subStatus+\"','\"+tw.local.odcRequest.BasicDetails.contractStage+\"','\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\"','\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\"','\"+tw.local.odcRequest.BasicDetails.productCategory.value+\"','\"+tw.local.odcRequest.BasicDetails.commodityDescription+\"','\"+false+\"','\"+tw.local.odcRequest.CustomerInfo.cif+\"','\"+tw.local.odcRequest.CustomerInfo.customerName+\"','\"+tw.local.odcRequest.FcCollections.currency.name+\"','\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\"',sysdate,'\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\"','\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\"','\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\"','\"+tw.local.odcRequest.appInfo.stepName+\"','\"+tw.local.odcRequest.appInfo.instanceID+\"');\"\r\r\n//'\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\"'"}, {"scriptFormat": "text/x-javascript", "name": "Update query", "id": "98e51491-7304-42a1-8af6-bc22fe4af2ef", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "283", "y": "184", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "7d2d0ca6-22ca-439c-801f-3d5f9504608f", "ns16:outgoing": "6b928f1a-fc75-4129-8300-e6290bacf2e9", "ns16:script": "tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,parentRequestNo,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\"\r\r\n+\"values('\"+tw.local.odcRequest.requestNo+\"','\"+tw.local.odcRequest.requestNature.name+\"','\"+tw.local.odcRequest.requestType.name+\"',sysdate,'\"+tw.local.odcRequest.BasicDetails.requestState+\"','\"+tw.local.odcRequest.appInfo.status+\"','\"+tw.local.odcRequest.appInfo.subStatus+\"','\"+tw.local.odcRequest.parentRequestNo+\"','\"+tw.local.odcRequest.BasicDetails.contractStage+\"','\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\"','\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\"','\"+tw.local.odcRequest.BasicDetails.productCategory.value+\"','\"+tw.local.odcRequest.BasicDetails.commodityDescription+\"','\"+false+\"','\"+tw.local.odcRequest.CustomerInfo.cif+\"','\"+tw.local.odcRequest.CustomerInfo.customerName+\"','\"+tw.local.odcRequest.FcCollections.currency.name+\"','\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\"',sysdate,'\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\"','\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\"','\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\"','\"+tw.local.odcRequest.appInfo.stepName+\"','\"+tw.local.odcRequest.appInfo.instanceID+\"');\"\r\r\n//'\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\"'"}, {"scriptFormat": "text/x-javascript", "name": "multiple statements", "id": "27152ee5-08fa-43cf-8397-429860e1b017", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "742", "y": "98", "width": "95", "height": "70"}}, "ns16:script": "tw.local.queryList = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\ntw.local.queryList[0] = new tw.object.toolkit.TWSYS.SQLStatement();\r\r\ntw.local.queryList[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[0].value = tw.local.odcRequest.requestNo;\r\r\n\r\r\ntw.local.queryList[0].parameters[1] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[1].value = tw.local.odcRequest.requestNature.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[2] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[2].value = tw.local.odcRequest.requestType.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[3] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[3].value = tw.local.odcRequest.requestDate;\r\r\n\r\r\n\r\r\ntw.local.queryList[0].parameters[4] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[4].value = tw.local.odcRequest.BasicDetails.requestState;\r\r\n\r\r\ntw.local.queryList[0].parameters[5] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[5].value = tw.local.odcRequest.appInfo.status;\r\r\n\r\r\n\r\r\ntw.local.queryList[0].parameters[6] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[6].value = tw.local.odcRequest.appInfo.subStatus;\r\r\n\r\r\ntw.local.queryList[0].parameters[7] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[7].value = tw.local.odcRequest.BasicDetails.contractStage;\r\r\n\r\r\ntw.local.queryList[0].parameters[8] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[8].value = tw.local.odcRequest.BasicDetails.exportPurpose.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[9] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[9].value = tw.local.odcRequest.BasicDetails.paymentTerms.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[10] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[10].value = tw.local.odcRequest.BasicDetails.productCategory.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[11] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[11].value = tw.local.odcRequest.BasicDetails.commodityDescription;\r\r\n\r\r\ntw.local.queryList[0].parameters[12] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[12].value = false;\r\r\n\r\r\ntw.local.queryList[0].parameters[13] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[13].value = tw.local.odcRequest.CustomerInfo.cif;\r\r\n\r\r\ntw.local.queryList[0].parameters[14] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[14].value = tw.local.odcRequest.CustomerInfo.customerName;\r\r\n\r\r\ntw.local.queryList[0].parameters[15] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[15].value = tw.local.odcRequest.FcCollections.currency.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[16] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[16].value = tw.local.odcRequest.FcCollections.standardExchangeRate;\r\r\n\r\r\ntw.local.queryList[0].parameters[17] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[17].value = tw.local.odcRequest.FcCollections.negotiatedExchangeRate;\r\r\n\r\r\ntw.local.queryList[0].parameters[18] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[18].value = tw.local.odcRequest.FcCollections.totalAllocatedAmount;\r\r\n\r\r\ntw.local.queryList[0].parameters[19] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[19].value = tw.local.odcRequest.FinancialDetailsBR.currency.name;\r\r\n\r\r\ntw.local.queryList[0].parameters[20] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[20].value = sysdate;\r\r\n\r\r\ntw.local.queryList[0].parameters[21] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[21].value = tw.local.odcRequest.FinancialDetailsBR.documentAmount;\r\r\n\r\r\ntw.local.queryList[0].parameters[22] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[22].value = tw.local.odcRequest.FinancialDetailsBR.amountAdvanced;\r\r\n\r\r\ntw.local.queryList[0].parameters[23] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[23].value = tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[24] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[24].value = tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value;\r\r\n\r\r\ntw.local.queryList[0].parameters[25] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[25].value = tw.local.odcRequest.appInfo.stepName;\r\r\n\r\r\ntw.local.queryList[0].parameters[26] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[0].parameters[26].value = tw.local.odcRequest.appInfo.instanceID;\r\r\n\r\r\ntw.local.queryList[0].sql = tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);\";\r\r\ntw.local.queryList[0].maxRows = -1;\r\r\n\r\r\nif(tw.local.odcRequest.BasicDetails.Bills.listLength != 0)\r\r\n{\r\r\n\ttw.local.queryList[1] = new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\ttw.local.queryList[1].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\tfor(var i=0;i<tw.local.)\r\r\n\t{\r\r\n\t\r\r\n\t}\r\r\n}\r\r\ntw.local.queryList[1].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\r\ntw.local.queryList[1].parameters[0].value = tw.local.odcRequest.requestNo;\r\r\n\r\r\ntw.local.queryList[1].sql = tw.local.query = \"insert into ODC_REQUESTINFO ();\";\r\r\ntw.local.queryList[1].maxRows = -1;\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Insert parties", "id": "937313a5-c693-4dbe-87ff-5bc4931cc1c4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "691", "y": "225", "width": "95", "height": "70"}}, "ns16:script": "tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar par = 0;\r\r\nvar ind = 0;\r\r\nfunction initSql(){\r\r\n\tvar sqlsttmnt = new tw.object.SQLStatement();\r\r\n\tsqlsttmnt.parameters = new tw.object.listOf.SQLParameter();\r\r\n\treturn sqlsttmnt;\r\r\n}\r\r\n\r\r\ntw.local.sqlStatements[ind] = initSql();\r\r\n// adding the sql statement a<PERSON><PERSON><PERSON><PERSON> wrote temporarily - to be restructured\r\r\n\r\r\ntw.local.sqlStatements[ind++].sql = tw.local.query;\r\r\n\r\r\n//inserting parties\r\r\n//to be changed after changing parties to list)\r\r\n//for(var p=0 ; p< 3 ; p++){\r\r\ntw.local.sqlStatements[ind] = initSql();\r\r\ntw.local.sqlStatements[ind].sql =\"Insert into ODC_PARTIESINFO (PARTYID , PARTYTYPE, CIF, REQUESRID) VALUES (?, ?, ?, ? );\"\r\r\n\r\r\ntw.local.sqlStatements[ind].parameters[par++] = \r\r\n \r\r\n// }\r\r\n"}], "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Execute statement", "id": "1615fc68-5350-4fd2-8dee-a6115f36a883", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "512", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["eda2dc71-b433-4552-89dd-e2801b59e36d", "6b928f1a-fc75-4129-8300-e6290bacf2e9"], "ns16:outgoing": "d7d4d18a-bd71-4c6b-860c-41797c800e2f", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.query", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.sqlResults", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Execute multiple statements", "id": "ab3ca23b-bc15-4cc6-86f4-5443af54d24b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "949", "y": "107", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.9695b715-db44-410b-8a74-65cf1d31d8ab", "ns16:assignment": {"ns16:from": {"_": "tw.local.queryList", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0"}}}, {"ns16:targetRef": "2055.a634f531-c979-476c-91db-a17bf5e55c90", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e", "ns16:assignment": {"ns16:to": {"_": "tw.local.sqlResults", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "query", "id": "2056.c1a9df3f-d507-4b27-8766-04c7f72168aa"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestNumber", "id": "2056.cc74ca5d-a7f0-4f86-8b0e-7ee1692b27ab"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "sqlResults", "id": "2056.c2286d23-6bc2-4bba-8cbf-36aada72929a"}, {"itemSubjectRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0", "isCollection": "true", "name": "queryList", "id": "2056.166981a3-1293-4eac-84d5-ca8099ce579f", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();\r\nautoObject[0].sql = \"\";\r\nautoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\nautoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\nautoObject[0].parameters[0].value = null;\r\nautoObject[0].parameters[0].type = \"\";\r\nautoObject[0].parameters[0].mode = \"\";\r\nautoObject[0].maxRows = 0;\r\nautoObject", "useDefault": "false"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.ee764bf6-eb5f-4208-8320-828cbf64c02e"}, {"itemSubjectRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0", "isCollection": "true", "name": "sqlStatements", "id": "2056.ce6180b9-2169-4336-865f-c352a900e59c"}], "ns16:exclusiveGateway": {"default": "47bfefe4-7514-4e92-8fa7-053ab8bf00c4", "name": "is Update?", "id": "f9db43af-0083-4085-8e05-252c7bc1525c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "100", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "2027.60dc1e7b-3895-478c-82bb-f29568774e84", "ns16:outgoing": ["47bfefe4-7514-4e92-8fa7-053ab8bf00c4", "7d2d0ca6-22ca-439c-801f-3d5f9504608f"]}}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d7d4d18a-bd71-4c6b-860c-41797c800e2f", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.1615fc68-5350-4fd2-8dee-a6115f36a883", "2025.1615fc68-5350-4fd2-8dee-a6115f36a883"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.01516e5f-85fc-45a4-8336-888f9e7ec3b1", "2025.01516e5f-85fc-45a4-8336-888f9e7ec3b1"], "guid": "da3b239c-474e-43fc-9d76-ca35b177a81b", "versionId": "01c6d24e-ff15-461e-a0e3-3b655b4a1840", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7d2d0ca6-22ca-439c-801f-3d5f9504608f", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f9db43af-0083-4085-8e05-252c7bc1525c", "2025.f9db43af-0083-4085-8e05-252c7bc1525c"], "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:3d73", "toProcessItemId": ["2025.98e51491-7304-42a1-8af6-bc22fe4af2ef", "2025.98e51491-7304-42a1-8af6-bc22fe4af2ef"], "guid": "841f5c29-92f1-48fe-990d-84674f75fb1e", "versionId": "71bc6cb7-90bd-4d62-b161-5ef30b1339ed", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "No", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.47bfefe4-7514-4e92-8fa7-053ab8bf00c4", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f9db43af-0083-4085-8e05-252c7bc1525c", "2025.f9db43af-0083-4085-8e05-252c7bc1525c"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.6cccd4e9-cc7c-4062-85b2-a10af0901632", "2025.6cccd4e9-cc7c-4062-85b2-a10af0901632"], "guid": "e98893f9-9778-4a50-b327-8fd61a0618f0", "versionId": "812a8eb1-a0e3-44c8-accd-243bba265363", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Execute statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.eda2dc71-b433-4552-89dd-e2801b59e36d", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.6cccd4e9-cc7c-4062-85b2-a10af0901632", "2025.6cccd4e9-cc7c-4062-85b2-a10af0901632"], "endStateId": "Out", "toProcessItemId": ["2025.1615fc68-5350-4fd2-8dee-a6115f36a883", "2025.1615fc68-5350-4fd2-8dee-a6115f36a883"], "guid": "e48383ba-c6a0-4ab1-9441-baff03e8f629", "versionId": "e2f4b98f-d4f7-4ebb-a805-351d5297d4f7", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Execute statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6b928f1a-fc75-4129-8300-e6290bacf2e9", "processId": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.98e51491-7304-42a1-8af6-bc22fe4af2ef", "2025.98e51491-7304-42a1-8af6-bc22fe4af2ef"], "endStateId": "Out", "toProcessItemId": ["2025.1615fc68-5350-4fd2-8dee-a6115f36a883", "2025.1615fc68-5350-4fd2-8dee-a6115f36a883"], "guid": "5966c8ae-5a15-4fa0-bc96-e96a61ad80dc", "versionId": "e340a06a-0a6d-4987-ae47-7d2eba9cf0b1", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}