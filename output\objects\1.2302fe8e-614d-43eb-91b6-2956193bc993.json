{"id": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "versionId": "7e52ed01-6dbe-4b2e-b2e1-421b774f9c5c", "name": "Initiate ODC Process", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "Initiate ODC Process", "lastModified": "1699520054207", "lastModifiedBy": "heba", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "2025.4b02a02d-49e7-477f-89fa-84162350bcc6"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.3fef31ef-99fc-471b-8796-1c23daa19be9", "2025.3fef31ef-99fc-471b-8796-1c23daa19be9"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76b8", "versionId": "7e52ed01-6dbe-4b2e-b2e1-421b774f9c5c", "dependencySummary": "<dependencySummary id=\"bpdid:651a1a6abf396537:64776e00:18baeba64af:255a\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.0fb58f07-d096-4ec8-8255-943ace2831b9\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":107,\"y\":186,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"94eb8d0f-ebc9-4403-8b97-5b8944ca4e29\"},{\"incoming\":[\"3f66f592-cb04-439f-8325-1f2e8ecc9a4d\",\"a1b36c32-c98f-42db-8685-ae5d76aa03f3\",\"b0fbef09-c987-43e4-8185-d3711fae162f\",\"77d41f45-95a5-45bc-8fe7-3a7bfad68d61\",\"e6e4f61c-312e-4a01-822f-a72b5237ec42\",\"096f719b-e0bb-4ad3-88a8-662265249cfa\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":940,\"y\":189,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ba\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"acf670a7-5e34-496c-8768-b18bbb4c09a4\"},{\"targetRef\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Request Type\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.0fb58f07-d096-4ec8-8255-943ace2831b9\",\"sourceRef\":\"94eb8d0f-ebc9-4403-8b97-5b8944ca4e29\"},{\"startQuantity\":1,\"outgoing\":[\"77d41f45-95a5-45bc-8fe7-3a7bfad68d61\"],\"incoming\":[\"4f5524cb-d0f1-4b79-82db-7c1a6c25b45c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":421,\"y\":89,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"start Amend  Process\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"d7efe1f4-4396-40af-8fb8-b4d7fb45108e\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nvar input = new tw.object.Map();\\r\\ninput.put(\\\"ODCRequest\\\",tw.local.odcRequest);\\r\\ninput.put(\\\"routingDetails\\\",tw.local.routingDetails);\\r\\n \\r\\ntw.local.processName = tw.epv.ODCProcessName.create+\\\"\\\";\\r\\n\\/\\/tw.local.testData= tw.local.processName;\\r\\n\\r\\n\\/\\/if(tw.env.runningServerIP == \\\"************\\\")\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").findSnapshotByName(\\\"ODC_10-09-2023_V4\\\").findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\/\\/else\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\t\\r\\n\\r\\n\\/\\/tw.system.model.findProcessAppByName(\\\"NBE ODC Processes\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\r\\ntw.system.startProcessByName(tw.local.processName, input, false)\"]}},{\"outgoing\":[\"4f5524cb-d0f1-4b79-82db-7c1a6c25b45c\",\"bca20caf-ee0c-4bf4-8856-65b582207bc8\",\"9ad63445-77ea-4b28-8a25-b1a81c64533d\",\"a99a5322-5d68-4786-804d-67495a9f8aaf\",\"********-9dd1-4da5-84d4-127606177a5e\",\"dc094bde-97a9-4302-810e-edd5581e8cdd\"],\"incoming\":[\"2027.0fb58f07-d096-4ec8-8255-943ace2831b9\"],\"default\":\"9ad63445-77ea-4b28-8a25-b1a81c64533d\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":137,\"y\":182,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}],\"preAssignmentScript\":[]},\"name\":\"Request Type\",\"declaredType\":\"exclusiveGateway\",\"id\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\"},{\"startQuantity\":1,\"outgoing\":[\"3f66f592-cb04-439f-8325-1f2e8ecc9a4d\"],\"incoming\":[\"bca20caf-ee0c-4bf4-8856-65b582207bc8\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":530,\"y\":166,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"start collection process\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"841f3829-ef78-434b-8948-6b4406876e41\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nvar input = new tw.object.Map();\\r\\ninput.put(\\\"odcRequest\\\",tw.local.odcRequest);\\r\\ninput.put(\\\"routingDetails\\\",tw.local.routingDetails);\\r\\n\\r\\n\\r\\ntw.local.processName = tw.epv.ODCProcessName.collection+\\\"\\\";\\r\\n\\r\\n\\/\\/if(tw.env.runningServerIP == \\\"************\\\")\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").findSnapshotByName(\\\"ODC_10-09-2023_V4\\\").findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\/\\/else\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\t\\r\\n\\r\\n\\/\\/tw.system.model.findProcessAppByName(\\\"NBE ODC Processes\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\r\\ntw.system.startProcessByName(tw.local.processName, input, false);\"]}},{\"targetRef\":\"841f3829-ef78-434b-8948-6b4406876e41\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestType\\t  ==\\t  tw.epv.RequestType.Collection\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Collection\",\"declaredType\":\"sequenceFlow\",\"id\":\"bca20caf-ee0c-4bf4-8856-65b582207bc8\",\"sourceRef\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\"},{\"startQuantity\":1,\"outgoing\":[\"e6e4f61c-312e-4a01-822f-a72b5237ec42\"],\"incoming\":[\"9ad63445-77ea-4b28-8a25-b1a81c64533d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":386,\"y\":12,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"start Create Process\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"913cc9dc-ccc4-4af5-86c4-5216c01014b4\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nvar input = new tw.object.Map();\\r\\ninput.put(\\\"ODCRequest\\\",tw.local.odcRequest);\\r\\ninput.put(\\\"routingDetails\\\",tw.local.routingDetails);\\r\\n\\r\\ntw.local.processName = tw.epv.ODCProcessName.create;\\r\\n \\r\\n\\/\\/if(tw.env.runningServerIP == \\\"************\\\")\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").findSnapshotByName(\\\"ODC_10-09-2023_V4\\\").findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\/\\/else\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\t\\r\\n\\r\\n\\/\\/tw.system.model.findProcessAppByName(\\\"NBE ODC Processes\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\r\\ntw.system.startProcessByName(tw.local.processName, input, false);\"]}},{\"targetRef\":\"913cc9dc-ccc4-4af5-86c4-5216c01014b4\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestType\\t  ==\\t  tw.epv.RequestType.Create\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Create ODC\",\"declaredType\":\"sequenceFlow\",\"id\":\"9ad63445-77ea-4b28-8a25-b1a81c64533d\",\"sourceRef\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\"},{\"startQuantity\":1,\"outgoing\":[\"a1b36c32-c98f-42db-8685-ae5d76aa03f3\"],\"incoming\":[\"a99a5322-5d68-4786-804d-67495a9f8aaf\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":658,\"y\":245,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"start Reversal  Process\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"6ba55b6b-22b9-47d9-89f1-e32116d3a9db\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nvar input = new tw.object.Map();\\r\\ninput.put(\\\"odcRequest\\\",tw.local.odcRequest);\\r\\ninput.put(\\\"routingDetails\\\",tw.local.routingDetails);\\r\\n\\r\\ntw.local.processName = tw.epv.ODCProcessName.reversal+\\\"\\\";\\r\\n \\r\\n\\/\\/if(tw.env.runningServerIP == \\\"************\\\")\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").findSnapshotByName(\\\"ODC_10-09-2023_V4\\\").findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\/\\/else\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\t\\r\\n\\r\\n\\/\\/tw.system.model.findProcessAppByName(\\\"NBE ODC Processes\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\r\\ntw.system.startProcessByName(tw.local.processName, input, false);\"]}},{\"startQuantity\":1,\"outgoing\":[\"b0fbef09-c987-43e4-8185-d3711fae162f\"],\"incoming\":[\"********-9dd1-4da5-84d4-127606177a5e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":797,\"y\":313,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"start Closure Process\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"f89a4d43-aa72-47b9-8945-1e978931d874\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nvar input = new tw.object.Map();\\r\\ninput.put(\\\"odcRequest\\\",tw.local.odcRequest);\\r\\ninput.put(\\\"routingDetails\\\",tw.local.routingDetails);\\r\\n \\r\\ntw.local.processName = tw.epv.ODCProcessName.closure+\\\"\\\";\\r\\n \\r\\n\\/\\/if(tw.env.runningServerIP == \\\"************\\\")\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").findSnapshotByName(\\\"ODC_10-09-2023_V4\\\").findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\/\\/else\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\t\\r\\n\\r\\n\\/\\/tw.system.model.findProcessAppByName(\\\"NBE ODC Processes\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\r\\ntw.system.startProcessByName(tw.local.processName, input, false);\"]}},{\"targetRef\":\"6ba55b6b-22b9-47d9-89f1-e32116d3a9db\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestType\\t  ==\\t  tw.epv.RequestType.Reversal\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Reversal\",\"declaredType\":\"sequenceFlow\",\"id\":\"a99a5322-5d68-4786-804d-67495a9f8aaf\",\"sourceRef\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\"},{\"targetRef\":\"f89a4d43-aa72-47b9-8945-1e978931d874\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestType\\t  ==\\t  tw.epv.RequestType.Closure\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Closure\",\"declaredType\":\"sequenceFlow\",\"id\":\"********-9dd1-4da5-84d4-127606177a5e\",\"sourceRef\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\"},{\"targetRef\":\"acf670a7-5e34-496c-8768-b18bbb4c09a4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"3f66f592-cb04-439f-8325-1f2e8ecc9a4d\",\"sourceRef\":\"841f3829-ef78-434b-8948-6b4406876e41\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\\\"ODC Creation and Amendment \\u0627\\u0646\\u0634\\u0627\\u0621 \\u0648 \\u062a\\u062d\\u062f\\u064a\\u062b \\u062a\\u062d\\u0635\\u064a\\u0644 \\u0645\\u0633\\u062a\\u0646\\u062f\\u0649 \\u062a\\u0635\\u062f\\u064a\\u0631\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"processName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.f19f3e47-ad97-46d2-8c9c-ab411519ee88\"},{\"parallelMultiple\":false,\"outgoing\":[\"f6bfc0e9-24e8-428c-8359-dfe22bb8fc58\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"cdb58da0-9006-4637-8a9f-1b8ed3af6db2\",\"otherAttributes\":{\"eventImplId\":\"0605d09e-8a63-44bf-8c54-ebe04934d57e\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1479,\"y\":432,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"81840969-afc5-4ece-8fb5-9583a41609c8\"},{\"targetRef\":\"3fef31ef-99fc-471b-8796-1c23daa19be9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exp. Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"f6bfc0e9-24e8-428c-8359-dfe22bb8fc58\",\"sourceRef\":\"81840969-afc5-4ece-8fb5-9583a41609c8\"},{\"targetRef\":\"d7efe1f4-4396-40af-8fb8-b4d7fb45108e\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestType\\t  ==\\t  tw.epv.RequestType.Amendment\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Amendment\",\"declaredType\":\"sequenceFlow\",\"id\":\"4f5524cb-d0f1-4b79-82db-7c1a6c25b45c\",\"sourceRef\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\"},{\"targetRef\":\"acf670a7-5e34-496c-8768-b18bbb4c09a4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"a1b36c32-c98f-42db-8685-ae5d76aa03f3\",\"sourceRef\":\"6ba55b6b-22b9-47d9-89f1-e32116d3a9db\"},{\"targetRef\":\"acf670a7-5e34-496c-8768-b18bbb4c09a4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"b0fbef09-c987-43e4-8185-d3711fae162f\",\"sourceRef\":\"f89a4d43-aa72-47b9-8945-1e978931d874\"},{\"targetRef\":\"acf670a7-5e34-496c-8768-b18bbb4c09a4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"77d41f45-95a5-45bc-8fe7-3a7bfad68d61\",\"sourceRef\":\"d7efe1f4-4396-40af-8fb8-b4d7fb45108e\"},{\"targetRef\":\"acf670a7-5e34-496c-8768-b18bbb4c09a4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e6e4f61c-312e-4a01-822f-a72b5237ec42\",\"sourceRef\":\"913cc9dc-ccc4-4af5-86c4-5216c01014b4\"},{\"startQuantity\":1,\"outgoing\":[\"096f719b-e0bb-4ad3-88a8-662265249cfa\"],\"incoming\":[\"dc094bde-97a9-4302-810e-edd5581e8cdd\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":850,\"y\":405,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"start Recreate Process\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"8960c77d-df1f-42ee-8ef2-6bb15ea1921e\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nvar input = new tw.object.Map();\\r\\ninput.put(\\\"ODCRequest\\\",tw.local.odcRequest);\\r\\ninput.put(\\\"routingDetails\\\",tw.local.routingDetails);\\r\\n\\r\\n\\r\\ntw.local.processName = tw.epv.ODCProcessName.create+\\\"\\\";\\r\\n\\r\\n\\r\\n\\/\\/if(tw.env.runningServerIP == \\\"************\\\")\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").findSnapshotByName(\\\"ODC_10-09-2023_V4\\\").findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\/\\/else\\r\\n\\/\\/\\ttw.system.model.findProcessAppByAcronym(\\\"NBEODCR\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\t\\r\\n\\r\\n\\/\\/tw.system.model.findProcessAppByName(\\\"NBE ODC Processes\\\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\\r\\n\\r\\ntw.system.startProcessByName(tw.local.processName, input, false)\"]}},{\"targetRef\":\"8960c77d-df1f-42ee-8ef2-6bb15ea1921e\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestType\\t  ==\\t  tw.epv.RequestType.Recreate\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To start Recreate Process\",\"declaredType\":\"sequenceFlow\",\"id\":\"dc094bde-97a9-4302-810e-edd5581e8cdd\",\"sourceRef\":\"4b02a02d-49e7-477f-89fa-84162350bcc6\"},{\"targetRef\":\"acf670a7-5e34-496c-8768-b18bbb4c09a4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"096f719b-e0bb-4ad3-88a8-662265249cfa\",\"sourceRef\":\"8960c77d-df1f-42ee-8ef2-6bb15ea1921e\"},{\"incoming\":[\"f6bfc0e9-24e8-428c-8359-dfe22bb8fc58\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"0e58d863-2474-4a3e-898a-776741b273f8\",\"otherAttributes\":{\"eventImplId\":\"f14b6252-6d17-45bb-83b8-26ff2b9f994a\"}}],\"extensionElements\":{\"postAssignmentScript\":[\"if(tw.local.errorMessage!=null)\\r\\n{\\t\\r\\n\\tlog.info(\\\"============================= ODC ================================================================\\\");\\r\\n\\tlog.info(\\\"=============================Start Service ->  Initiate ODC Process ===========================================\\\");\\r\\n\\tlog.info(\\\"=======================Instace Id= \\\"+tw.system.currentProcessInstanceID +\\\" :: Error Message: \\\"+ tw.local.errorMessage);\\r\\n\\tlog.info(\\\"=============================End Service ->  Initiate ODC Process ===========================================\\\");\\r\\n\\t}\\r\\n\\r\\n\\r\\n\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":1531,\"y\":432,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event1\",\"dataInputAssociation\":[{\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}]}],\"declaredType\":\"endEvent\",\"id\":\"3fef31ef-99fc-471b-8796-1c23daa19be9\"},{\"startQuantity\":1,\"outgoing\":[\"8ef4a9ce-6b19-4b36-82bd-898ed60b413f\"],\"incoming\":[\"be95c2c1-d6e3-42a3-88f5-62a1d26fe478\",\"6aae309b-3e79-4776-8ca4-11f0241fa76d\",\"1c50a334-8e56-4fc4-8d18-8ffe08eb65d7\",\"d2354b5c-80f8-4e81-8070-c825bbf904f6\",\"282cef0b-4720-4282-81f5-3dbdafc414b2\",\"81414926-c0a5-4364-8570-a949ac22f913\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":1137,\"y\":210,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Initiate ODC Process\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"parallelMultiple\":false,\"outgoing\":[\"be95c2c1-d6e3-42a3-88f5-62a1d26fe478\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"e356eebc-c9d8-4044-8ad8-d58093b5b561\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"c9bf18ce-7f60-4e12-8fa7-f952431d7647\",\"otherAttributes\":{\"eventImplId\":\"402d5c39-ea1f-41d2-8bbe-85834ef9959e\"}}],\"attachedToRef\":\"913cc9dc-ccc4-4af5-86c4-5216c01014b4\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":469,\"y\":17,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"bfbec735-387a-4f92-85c4-bc6408bfd10a\",\"outputSet\":{}},{\"targetRef\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"be95c2c1-d6e3-42a3-88f5-62a1d26fe478\",\"sourceRef\":\"bfbec735-387a-4f92-85c4-bc6408bfd10a\"},{\"parallelMultiple\":false,\"outgoing\":[\"81414926-c0a5-4364-8570-a949ac22f913\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"e72682e5-291f-4d15-80a5-31aa3a0a5fd3\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"93c47cdc-1d65-4620-8659-da71c56610dc\",\"otherAttributes\":{\"eventImplId\":\"f1dbcf05-f85c-4799-81ba-b2d075017bd9\"}}],\"attachedToRef\":\"d7efe1f4-4396-40af-8fb8-b4d7fb45108e\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":504,\"y\":94,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"4a5f82bd-83ce-4379-8174-163144a5402c\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"6aae309b-3e79-4776-8ca4-11f0241fa76d\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"a987228d-c94d-4edb-825b-31e596aa179b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"e7471dab-5d8b-4ef5-8acb-3ee0b047d04e\",\"otherAttributes\":{\"eventImplId\":\"902f03d5-5082-4ae1-8569-bc6a8486fa05\"}}],\"attachedToRef\":\"8960c77d-df1f-42ee-8ef2-6bb15ea1921e\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":885,\"y\":463,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error2\",\"declaredType\":\"boundaryEvent\",\"id\":\"e15b43a1-b5f2-47a0-827f-356d0d1fc4b5\",\"outputSet\":{}},{\"targetRef\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"6aae309b-3e79-4776-8ca4-11f0241fa76d\",\"sourceRef\":\"e15b43a1-b5f2-47a0-827f-356d0d1fc4b5\"},{\"parallelMultiple\":false,\"outgoing\":[\"1c50a334-8e56-4fc4-8d18-8ffe08eb65d7\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"71884565-98e3-4cc7-835d-a8d459bb9284\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"3dd162cf-1225-4bea-8110-08dde396ef61\",\"otherAttributes\":{\"eventImplId\":\"3c6f693d-67df-4570-8156-b115aa5e2039\"}}],\"attachedToRef\":\"f89a4d43-aa72-47b9-8945-1e978931d874\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":832,\"y\":371,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error3\",\"declaredType\":\"boundaryEvent\",\"id\":\"b731f798-9d81-4d01-899f-f6c68adf24bd\",\"outputSet\":{}},{\"targetRef\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"1c50a334-8e56-4fc4-8d18-8ffe08eb65d7\",\"sourceRef\":\"b731f798-9d81-4d01-899f-f6c68adf24bd\"},{\"parallelMultiple\":false,\"outgoing\":[\"d2354b5c-80f8-4e81-8070-c825bbf904f6\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"eaae946e-bcc4-4fdc-84b5-9bc5fe3a85ec\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"37bc0b66-8954-45e0-8b4f-e06b558674af\",\"otherAttributes\":{\"eventImplId\":\"2bceb9ea-88c1-4ded-87ef-0969e9e03ced\"}}],\"attachedToRef\":\"6ba55b6b-22b9-47d9-89f1-e32116d3a9db\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":693,\"y\":303,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error4\",\"declaredType\":\"boundaryEvent\",\"id\":\"c45db874-d635-4df7-85b9-d6efb83e0c17\",\"outputSet\":{}},{\"targetRef\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"d2354b5c-80f8-4e81-8070-c825bbf904f6\",\"sourceRef\":\"c45db874-d635-4df7-85b9-d6efb83e0c17\"},{\"parallelMultiple\":false,\"outgoing\":[\"282cef0b-4720-4282-81f5-3dbdafc414b2\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"3dd6e9a0-bc87-4a9a-881e-519228aa77b8\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"3a046557-d69a-4fed-84f7-13fa8b36a673\",\"otherAttributes\":{\"eventImplId\":\"6b787de9-f688-462d-8452-51b318d18554\"}}],\"attachedToRef\":\"841f3829-ef78-434b-8948-6b4406876e41\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":539,\"y\":224,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error5\",\"declaredType\":\"boundaryEvent\",\"id\":\"c8b5cd28-ab1a-4af6-8181-7284cc27708d\",\"outputSet\":{}},{\"targetRef\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"282cef0b-4720-4282-81f5-3dbdafc414b2\",\"sourceRef\":\"c8b5cd28-ab1a-4af6-8181-7284cc27708d\"},{\"targetRef\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"81414926-c0a5-4364-8570-a949ac22f913\",\"sourceRef\":\"4a5f82bd-83ce-4379-8174-163144a5402c\"},{\"incoming\":[\"8ef4a9ce-6b19-4b36-82bd-898ed60b413f\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"c38d1f96-1a98-46c0-843b-c2baa0677c7d\",\"otherAttributes\":{\"eventImplId\":\"bbe8733f-4a49-4ea3-88ed-7bb7040497f5\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1328,\"y\":232,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"0348001f-bcde-4377-86ec-1ef2c006fd74\"},{\"targetRef\":\"0348001f-bcde-4377-86ec-1ef2c006fd74\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"8ef4a9ce-6b19-4b36-82bd-898ed60b413f\",\"sourceRef\":\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\"}],\"laneSet\":[{\"id\":\"5a0fc6be-577e-4926-8431-81d2f5453c8c\",\"lane\":[{\"flowNodeRef\":[\"94eb8d0f-ebc9-4403-8b97-5b8944ca4e29\",\"acf670a7-5e34-496c-8768-b18bbb4c09a4\",\"d7efe1f4-4396-40af-8fb8-b4d7fb45108e\",\"4b02a02d-49e7-477f-89fa-84162350bcc6\",\"841f3829-ef78-434b-8948-6b4406876e41\",\"913cc9dc-ccc4-4af5-86c4-5216c01014b4\",\"6ba55b6b-22b9-47d9-89f1-e32116d3a9db\",\"f89a4d43-aa72-47b9-8945-1e978931d874\",\"81840969-afc5-4ece-8fb5-9583a41609c8\",\"8960c77d-df1f-42ee-8ef2-6bb15ea1921e\",\"3fef31ef-99fc-471b-8796-1c23daa19be9\",\"0aaf3b3c-845b-49b1-85fd-e17648df01f2\",\"bfbec735-387a-4f92-85c4-bc6408bfd10a\",\"4a5f82bd-83ce-4379-8174-163144a5402c\",\"e15b43a1-b5f2-47a0-827f-356d0d1fc4b5\",\"b731f798-9d81-4d01-899f-f6c68adf24bd\",\"c45db874-d635-4df7-85b9-d6efb83e0c17\",\"c8b5cd28-ab1a-4af6-8181-7284cc27708d\",\"0348001f-bcde-4377-86ec-1ef2c006fd74\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":501}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"8615812e-9edc-47db-8da2-9aea7ebb911b\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Initiate ODC Process\",\"declaredType\":\"process\",\"id\":\"1.2302fe8e-614d-43eb-91b6-2956193bc993\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"id\":\"2055.de6250d6-ea05-49bc-896c-eada027a9562\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"instanceId\",\"isCollection\":false,\"id\":\"2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"taskId\",\"isCollection\":false,\"id\":\"2055.743a63fc-5839-4d37-8d0e-a582fda8ee91\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.192ba003-65ef-451c-89ea-378afe19f644\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"issuccessful\",\"isCollection\":false,\"id\":\"2055.5368acb7-69e7-470f-8f37-b60b75384c47\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"53602806-844a-4a91-800a-7aee97897167\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.679fe48b-845c-41d8-b41a-884a27c2acf3\",\"epvProcessLinkId\":\"f6146c51-4982-4dac-839e-10b8676c179a\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262\",\"2055.82e3268d-9082-40bf-8a48-16f0710ff117\",\"2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.de6250d6-ea05-49bc-896c-eada027a9562\",\"2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644\",\"2055.743a63fc-5839-4d37-8d0e-a582fda8ee91\",\"2055.192ba003-65ef-451c-89ea-378afe19f644\",\"2055.5368acb7-69e7-470f-8f37-b60b75384c47\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\\\"Reversal\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestType\",\"isCollection\":false,\"id\":\"2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.82e3268d-9082-40bf-8a48-16f0710ff117\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.odcRoutingDetails();\\nautoObject.hubCode = \\\"\\\";\\nautoObject.branchCode = \\\"\\\";\\nautoObject.initiatorUser = \\\"\\\";\\nautoObject.branchName = \\\"\\\";\\nautoObject.hubName = \\\"\\\";\\nautoObject.branchSeq = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"name\":\"routingDetails\",\"isCollection\":false,\"id\":\"2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "94db1a1d-5663-48af-b58d-5a234e81081c", "versionId": "622f4178-faa7-4ee1-86a5-9852838334b5"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.82e3268d-9082-40bf-8a48-16f0710ff117", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "b19a064b-40f9-4d88-9fa6-68e313f53afb", "versionId": "de2266a5-6617-4cd9-808f-fabf204beec0"}, {"name": "routingDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "1", "isArrayOf": "false", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "********-d3f2-4af1-b5c4-ee1edfa652a0", "versionId": "47071b3b-7b65-43b0-a122-9f6629414bb4"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.de6250d6-ea05-49bc-896c-eada027a9562", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "cf318efa-4bd9-4cdd-ba73-76ae17363933", "versionId": "05146451-a4c7-4c18-b2ba-afa0797f2f6f"}, {"name": "instanceId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "bc589c2f-4fea-4c03-9f34-dba7de11b88c", "versionId": "b5fda59b-8e0a-449a-b4aa-4c86fc97b91a"}, {"name": "taskId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.743a63fc-5839-4d37-8d0e-a582fda8ee91", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f10a8fcd-3a80-42c2-b681-d7726b1376e6", "versionId": "d6bcf4b0-7836-4ee6-b473-34b1ebe41a11"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.192ba003-65ef-451c-89ea-378afe19f644", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "7", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "cdf8801e-4c27-4bb7-95fd-681e935d095d", "versionId": "bb3304c7-0f3e-481b-80f7-09431fad7968"}, {"name": "issuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5368acb7-69e7-470f-8f37-b60b75384c47", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4170ab95-896b-4fc4-bfbf-a485b6d9a9b1", "versionId": "6f2343d1-146d-4f5a-8053-fcc8f23d7df0"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.3a3787de-b3c6-456e-8519-33a90f6ddca8", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "3", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "9", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "45a3b9ec-12a1-4e44-8095-9ba76f9c942d", "versionId": "77b8a8a3-cb32-451e-b30a-2f246db09780"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.fba555ed-0c51-4a4a-92a2-d80e37ffafac", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "parameterType": "3", "isArrayOf": "false", "classId": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "seq": "10", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "166894f7-9f96-4568-8ba5-6a72db45e7eb", "versionId": "80064209-bf08-42ed-9fa9-87fed630b000"}], "processVariable": {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f19f3e47-ad97-46d2-8c9c-ab411519ee88", "description": {"isNull": "true"}, "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5ea332c7-c5ba-4671-bfd9-1dd1cd57bcea", "versionId": "510efa7b-47fb-46e1-8df6-7c0f2aba1ce8"}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.841f3829-ef78-434b-8948-6b4406876e41", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "start collection process", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.86c72b34-f50a-4f56-a539-b4266e75c9b3", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "guid": "guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d00", "versionId": "014787f0-a25a-4c9a-978f-173a4235ecec", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.19e7169b-c801-4a68-8716-1af9f1bd037a", "processItemId": "2025.841f3829-ef78-434b-8948-6b4406876e41", "location": "2", "script": {"isNull": "true"}, "guid": "a9a6b4a2-3819-41b9-95e8-f039b3e9377c", "versionId": "99f316a9-5612-430d-b832-988bce826d6e"}, "layoutData": {"x": "530", "y": "166", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error5", "locationId": "bottomLeft", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:651a1a6abf396537:64776e00:18baeba64af:2400", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "bottomCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.86c72b34-f50a-4f56-a539-b4266e75c9b3", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.collection+\"\";\r\r\n\r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);", "isRule": "false", "guid": "f0a7961d-53ae-4acd-a68c-2d1876c7fc25", "versionId": "89dfa82c-cb69-4363-a06d-a8549b023a68"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "start Amend  Process", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.903b95eb-64fe-43e1-b1c4-c8cc06e9c9a3", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "guid": "guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ca", "versionId": "040213cb-7052-4c9e-a884-97586c998bbd", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "421", "y": "89", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "rightTop", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:651a1a6abf396537:64776e00:18baeba64af:2400", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "topLeft", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.903b95eb-64fe-43e1-b1c4-c8cc06e9c9a3", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n \r\r\ntw.local.processName = tw.epv.ODCProcessName.create+\"\";\r\r\n//tw.local.testData= tw.local.processName;\r\r\n\r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false)", "isRule": "false", "guid": "782df17e-a469-48e7-a479-a566d60d407d", "versionId": "ba65c0ad-d782-4b31-8c12-eec002853e04"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "start Reversal  Process", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.342887c9-0915-4d5e-8024-513a4de27ffe", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "guid": "guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d4f", "versionId": "06d86271-ee70-4f67-80f5-e15979967f6c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "658", "y": "245", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error4", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:651a1a6abf396537:64776e00:18baeba64af:2400", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "bottomCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.342887c9-0915-4d5e-8024-513a4de27ffe", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.reversal+\"\";\r\r\n \r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);", "isRule": "false", "guid": "86e2c90b-c18c-49aa-b63b-d4738f3c5f65", "versionId": "a9a5f8c8-5a70-4432-bd1c-720700ac59dd"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:651a1a6abf396537:64776e00:18baeba64af:2400", "versionId": "0f176861-3c0c-4457-862b-38c7a7bbb25a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "1137", "y": "210", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "2307a33f-45c6-4767-bf50-92e48f5b8140", "versionId": "61d08adb-dc9a-4496-b9fc-8cf0c6aabb18", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0f8e84b6-7a85-47f4-a9b4-9df08e29e140", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "5b522881-04ff-4dd2-a315-21ebca862fd0", "versionId": "80fbac0e-3fd5-4093-8c9a-8717539e8653", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6d1a7491-be5b-4d7f-baea-9396ed8dea89", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97", "useDefault": "false", "value": "\"Initiate ODC Process\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "c18e6839-fb8f-4d03-8977-0a74d1f014a5", "versionId": "99d07249-f843-42af-ba22-3a8debbd1ca4", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.fa418df9-0e00-4ecf-aef7-24bb20812770", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "a57bdd27-ca86-461b-84ff-32d72b214be8", "versionId": "9d2cf5ea-dd57-4e6d-8fe4-72b<PERSON>ad9a8b0", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "Request Type", "tWComponentName": "Switch", "tWComponentId": "3013.4d73b434-e964-42cd-b0ea-28c92cc39866", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76cb", "versionId": "16045ee0-e14d-487f-9e3f-5fdae0351584", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.351f94e2-2471-42f8-a549-bd4e18f184b9", "processItemId": "2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "location": "1", "script": {"isNull": "true"}, "guid": "1c4e1bbf-7b6e-4bee-a6cb-f27177787d44", "versionId": "9a9f9b1c-4977-4296-9f08-a47ac42f3a73"}, "layoutData": {"x": "137", "y": "182", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.4d73b434-e964-42cd-b0ea-28c92cc39866", "guid": "c6d7e7f7-eab2-4180-a7fb-5612556873db", "versionId": "4bd51ca5-7e31-4538-930e-ce37fb249731", "SwitchCondition": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.a562d881-0eeb-4589-83e4-1b0196944fa8", "switchId": "3013.4d73b434-e964-42cd-b0ea-28c92cc39866", "seq": "1", "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2554", "condition": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Amendment", "guid": "e238cfb0-3063-4c37-b7ea-76653b9196a6", "versionId": "8680f1d3-0692-472c-bf06-524f27cf618c"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.18db7e57-0015-4737-ac5f-11dd0a089961", "switchId": "3013.4d73b434-e964-42cd-b0ea-28c92cc39866", "seq": "2", "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2555", "condition": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Collection", "guid": "28cb2dd0-0cfe-4e07-be02-e15cc0d6e9c9", "versionId": "5591db06-ff35-4f4a-a9ed-4712d718ba7a"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.4fc4e0d3-d60c-41f4-8419-adfb9914c2ae", "switchId": "3013.4d73b434-e964-42cd-b0ea-28c92cc39866", "seq": "3", "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2556", "condition": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Reversal", "guid": "8632688e-f54f-4603-b61e-e50d6fffa19c", "versionId": "7f9931ae-a94a-42c3-a3b4-63f8f06c21dc"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.06369c43-74b7-414a-a7b4-2667ef13e2ff", "switchId": "3013.4d73b434-e964-42cd-b0ea-28c92cc39866", "seq": "4", "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2557", "condition": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Closure", "guid": "1a722e44-0879-4352-87ab-df85c43a4dc2", "versionId": "641b4c94-7495-4e86-81e0-ebfeadb9f455"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.7f80a620-5a3f-4ef4-a341-c276b38cac07", "switchId": "3013.4d73b434-e964-42cd-b0ea-28c92cc39866", "seq": "5", "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2558", "condition": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Recreate", "guid": "00305ea3-dc6b-45ef-bd58-064c508f82c8", "versionId": "4e0cff73-ccb7-4798-a713-e38e4d67515a"}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0348001f-bcde-4377-86ec-1ef2c006fd74", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.a47c2f7f-ff10-40a1-a412-de828985baca", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:651a1a6abf396537:64776e00:18baeba64af:2559", "versionId": "59c3537d-09d6-4d42-a63d-63983be34056", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1328", "y": "232", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.a47c2f7f-ff10-40a1-a412-de828985baca", "message": "", "faultStyle": "1", "guid": "8cd8b379-4e72-4dbc-a79e-07f6f83f2638", "versionId": "827a9be3-ab2c-41c9-84f4-89569449e47e", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.50726256-7d80-4f6e-8c80-8a9488c9b419", "processParameterId": "2055.fba555ed-0c51-4a4a-92a2-d80e37ffafac", "parameterMappingParentId": "3007.a47c2f7f-ff10-40a1-a412-de828985baca", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "isList": "false", "isInput": "false", "guid": "daad7c7c-8b92-4c54-9be5-1e8a1db46030", "versionId": "eede5a9b-d8a4-4c32-a0e2-76890acd37fa", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f89a4d43-aa72-47b9-8945-1e978931d874", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "start Closure Process", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.caaafb3b-68ae-43d2-9daf-cf52a871a712", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "guid": "guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d50", "versionId": "8ea7297c-aba4-42c8-8676-6081e6d07290", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "797", "y": "313", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error3", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:651a1a6abf396537:64776e00:18baeba64af:2400", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "bottomCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.caaafb3b-68ae-43d2-9daf-cf52a871a712", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n \r\r\ntw.local.processName = tw.epv.ODCProcessName.closure+\"\";\r\r\n \r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);", "isRule": "false", "guid": "e93a0ae5-bf73-46e3-8fd1-d95185740d36", "versionId": "43fe57eb-321b-491a-932f-0e0de4a65a4e"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "start Create Process", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.46143d15-727a-4ff1-8518-b55ea927f0c9", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "guid": "guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d01", "versionId": "a7af3b72-8db9-40de-8326-1e52e5419080", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "386", "y": "12", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "rightTop", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:651a1a6abf396537:64776e00:18baeba64af:2400", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "topLeft", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.46143d15-727a-4ff1-8518-b55ea927f0c9", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.create;\r\r\n \r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);", "isRule": "false", "guid": "4c61eea4-02e7-402e-b68d-aea8de54974c", "versionId": "194d07e1-547e-40b6-aafa-9c2c9421611a"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "start Recreate Process", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.5255b4a5-9ef8-4742-b4fe-3be4446ad041", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "guid": "guid:b391c9341fe28de0:43131724:18a837ed779:2fbe", "versionId": "d0b10187-d7e9-4411-b90b-c0006a978fd1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "850", "y": "405", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:651a1a6abf396537:64776e00:18baeba64af:2400", "errorHandlerItemId": "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "bottomCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.5255b4a5-9ef8-4742-b4fe-3be4446ad041", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.create+\"\";\r\r\n\r\r\n\r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false)", "isRule": "false", "guid": "cca914c7-0e6b-4b51-930e-65e25e932cf9", "versionId": "b6a09da5-a626-4846-81cf-523f7494bbc0"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3fef31ef-99fc-471b-8796-1c23daa19be9", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "End Event1", "tWComponentName": "Exception", "tWComponentId": "3007.aea5e251-9d5b-4565-acc9-48afeb10f5de", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:b391c9341fe28de0:43131724:18a837ed779:64fb", "versionId": "d44b40de-6aee-47a2-8060-beb7d212ee8a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.bf4eb8e1-af21-4f78-8a5b-10a772f57d2d", "processItemId": "2025.3fef31ef-99fc-471b-8796-1c23daa19be9", "location": "2", "script": "if(tw.local.errorMessage!=null)\r\r\n{\t\r\r\n\tlog.info(\"============================= ODC ================================================================\");\r\r\n\tlog.info(\"=============================Start Service ->  Initiate ODC Process ===========================================\");\r\r\n\tlog.info(\"=======================Instace Id= \"+tw.system.currentProcessInstanceID +\" :: Error Message: \"+ tw.local.errorMessage);\r\r\n\tlog.info(\"=============================End Service ->  Initiate ODC Process ===========================================\");\r\r\n\t}\r\r\n\r\r\n\r\r\n", "guid": "41267ec8-9218-491a-a46b-************", "versionId": "7ada5d53-5055-4207-8ea9-45484675b2e4"}, "layoutData": {"x": "1531", "y": "432", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.aea5e251-9d5b-4565-acc9-48afeb10f5de", "message": "", "faultStyle": "1", "guid": "ab83db39-4cc4-4859-a225-46be7456c48a", "versionId": "483cd07d-3b5d-4d27-918f-3ace27063a7a", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f812c31a-cdc9-4e50-8ad1-7b2b744d850b", "processParameterId": "2055.3a3787de-b3c6-456e-8519-33a90f6ddca8", "parameterMappingParentId": "3007.aea5e251-9d5b-4565-acc9-48afeb10f5de", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "0d1da441-2f63-4bf5-92e2-ed883a2117f4", "versionId": "de1a5450-9af0-4b24-9341-9b3f2d5f0100", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.acf670a7-5e34-496c-8768-b18bbb4c09a4", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.5d21f221-5850-4b82-8e9f-5db88e017fb3", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ba", "versionId": "fb23b4a7-cb46-475d-9a62-b1ff0263c425", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "940", "y": "189", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.5d21f221-5850-4b82-8e9f-5db88e017fb3", "haltProcess": "false", "guid": "2b8a71fc-ae79-43b4-b6e7-4a56acac6dea", "versionId": "2e899098-27f4-4b45-812b-cfb847316fc5"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.55384695-9c9c-4b6c-97fb-e9ac1a2d9dc0", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "guid": "960e2c14-0177-4266-963d-88e9c2f4819d", "versionId": "04a84102-1f39-451b-842a-5ca5f745cd0d"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.8515d147-5313-49ad-8e78-76ea92180077", "epvId": "/21.679fe48b-845c-41d8-b41a-884a27c2acf3", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "guid": "96437a44-a48e-40b1-8ced-8e9c866b8945", "versionId": "804ef0fb-1355-48e5-82fe-cd14ff7c8af9"}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "107", "y": "186", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "1479", "y": "432", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Initiate ODC Process", "id": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "53602806-844a-4a91-800a-7aee97897167"}, {"epvId": "21.679fe48b-845c-41d8-b41a-884a27c2acf3", "epvProcessLinkId": "f6146c51-4982-4dac-839e-10b8676c179a"}]}}, "ns16:dataInput": [{"name": "requestType", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"Reversal\"", "useDefault": "false"}}}, {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.82e3268d-9082-40bf-8a48-16f0710ff117", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "useDefault": "false"}}}, {"name": "routingDetails", "itemSubjectRef": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "isCollection": "false", "id": "2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.odcRoutingDetails();\r\nautoObject.hubCode = \"\";\r\nautoObject.branchCode = \"\";\r\nautoObject.initiatorUser = \"\";\r\nautoObject.branchName = \"\";\r\nautoObject.hubName = \"\";\r\nautoObject.branchSeq = \"\";\r\nautoObject", "useDefault": "false"}}}], "ns16:dataOutput": [{"name": "isSuccessful", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.de6250d6-ea05-49bc-896c-eada027a9562"}, {"name": "instanceId", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644"}, {"name": "taskId", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.743a63fc-5839-4d37-8d0e-a582fda8ee91"}, {"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.192ba003-65ef-451c-89ea-378afe19f644"}, {"name": "issuccessful", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.5368acb7-69e7-470f-8f37-b60b75384c47"}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262", "2055.82e3268d-9082-40bf-8a48-16f0710ff117", "2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf"]}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.de6250d6-ea05-49bc-896c-eada027a9562", "2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644", "2055.743a63fc-5839-4d37-8d0e-a582fda8ee91", "2055.192ba003-65ef-451c-89ea-378afe19f644", "2055.5368acb7-69e7-470f-8f37-b60b75384c47"]}}, "ns16:laneSet": {"id": "5a0fc6be-577e-4926-8431-81d2f5453c8c", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "8615812e-9edc-47db-8da2-9aea7ebb911b", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "501", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["94eb8d0f-ebc9-4403-8b97-5b8944ca4e29", "acf670a7-5e34-496c-8768-b18bbb4c09a4", "d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "4b02a02d-49e7-477f-89fa-84162350bcc6", "841f3829-ef78-434b-8948-6b4406876e41", "913cc9dc-ccc4-4af5-86c4-5216c01014b4", "6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "f89a4d43-aa72-47b9-8945-1e978931d874", "81840969-afc5-4ece-8fb5-9583a41609c8", "8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "3fef31ef-99fc-471b-8796-1c23daa19be9", "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "bfbec735-387a-4f92-85c4-bc6408bfd10a", "4a5f82bd-83ce-4379-8174-163144a5402c", "e15b43a1-b5f2-47a0-827f-356d0d1fc4b5", "b731f798-9d81-4d01-899f-f6c68adf24bd", "c45db874-d635-4df7-85b9-d6efb83e0c17", "c8b5cd28-ab1a-4af6-8181-7284cc27708d", "0348001f-bcde-4377-86ec-1ef2c006fd74"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "94eb8d0f-ebc9-4403-8b97-5b8944ca4e29", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "107", "y": "186", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.0fb58f07-d096-4ec8-8255-943ace2831b9"}, "ns16:endEvent": [{"name": "End", "id": "acf670a7-5e34-496c-8768-b18bbb4c09a4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "940", "y": "189", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ba"}, "ns16:incoming": ["3f66f592-cb04-439f-8325-1f2e8ecc9a4d", "a1b36c32-c98f-42db-8685-ae5d76aa03f3", "b0fbef09-c987-43e4-8185-d3711fae162f", "77d41f45-95a5-45bc-8fe7-3a7bfad68d61", "e6e4f61c-312e-4a01-822f-a72b5237ec42", "096f719b-e0bb-4ad3-88a8-662265249cfa"]}, {"name": "End Event1", "id": "3fef31ef-99fc-471b-8796-1c23daa19be9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1531", "y": "432", "width": "24", "height": "24"}, "ns3:postAssignmentScript": "if(tw.local.errorMessage!=null)\r\r\n{\t\r\r\n\tlog.info(\"============================= ODC ================================================================\");\r\r\n\tlog.info(\"=============================Start Service ->  Initiate ODC Process ===========================================\");\r\r\n\tlog.info(\"=======================Instace Id= \"+tw.system.currentProcessInstanceID +\" :: Error Message: \"+ tw.local.errorMessage);\r\r\n\tlog.info(\"=============================End Service ->  Initiate ODC Process ===========================================\");\r\r\n\t}\r\r\n\r\r\n\r\r\n"}, "ns16:incoming": "f6bfc0e9-24e8-428c-8359-dfe22bb8fc58", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:errorEventDefinition": {"id": "0e58d863-2474-4a3e-898a-776741b273f8", "eventImplId": "f14b6252-6d17-45bb-83b8-26ff2b9f994a", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}, {"name": "End Event", "id": "0348001f-bcde-4377-86ec-1ef2c006fd74", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1328", "y": "232", "width": "24", "height": "24"}}, "ns16:incoming": "8ef4a9ce-6b19-4b36-82bd-898ed60b413f", "ns16:errorEventDefinition": {"id": "c38d1f96-1a98-46c0-843b-c2baa0677c7d", "eventImplId": "bbe8733f-4a49-4ea3-88ed-7bb7040497f5", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "94eb8d0f-ebc9-4403-8b97-5b8944ca4e29", "targetRef": "4b02a02d-49e7-477f-89fa-84162350bcc6", "name": "To Request Type", "id": "2027.0fb58f07-d096-4ec8-8255-943ace2831b9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4b02a02d-49e7-477f-89fa-84162350bcc6", "targetRef": "841f3829-ef78-434b-8948-6b4406876e41", "name": "Collection", "id": "bca20caf-ee0c-4bf4-8856-65b582207bc8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Collection", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "4b02a02d-49e7-477f-89fa-84162350bcc6", "targetRef": "913cc9dc-ccc4-4af5-86c4-5216c01014b4", "name": "Create ODC", "id": "9ad63445-77ea-4b28-8a25-b1a81c64533d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Create", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "4b02a02d-49e7-477f-89fa-84162350bcc6", "targetRef": "6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "name": "Reversal", "id": "a99a5322-5d68-4786-804d-67495a9f8aaf", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Reversal", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "4b02a02d-49e7-477f-89fa-84162350bcc6", "targetRef": "f89a4d43-aa72-47b9-8945-1e978931d874", "name": "Closure", "id": "********-9dd1-4da5-84d4-127606177a5e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Closure", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "841f3829-ef78-434b-8948-6b4406876e41", "targetRef": "acf670a7-5e34-496c-8768-b18bbb4c09a4", "name": "To End", "id": "3f66f592-cb04-439f-8325-1f2e8ecc9a4d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "81840969-afc5-4ece-8fb5-9583a41609c8", "targetRef": "3fef31ef-99fc-471b-8796-1c23daa19be9", "name": "To <PERSON><PERSON>. <PERSON>ling", "id": "f6bfc0e9-24e8-428c-8359-dfe22bb8fc58", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4b02a02d-49e7-477f-89fa-84162350bcc6", "targetRef": "d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "name": "Amendment", "id": "4f5524cb-d0f1-4b79-82db-7c1a6c25b45c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Amendment", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "targetRef": "acf670a7-5e34-496c-8768-b18bbb4c09a4", "name": "To End", "id": "a1b36c32-c98f-42db-8685-ae5d76aa03f3", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "f89a4d43-aa72-47b9-8945-1e978931d874", "targetRef": "acf670a7-5e34-496c-8768-b18bbb4c09a4", "name": "To End", "id": "b0fbef09-c987-43e4-8185-d3711fae162f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "targetRef": "acf670a7-5e34-496c-8768-b18bbb4c09a4", "name": "To End", "id": "77d41f45-95a5-45bc-8fe7-3a7bfad68d61", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "913cc9dc-ccc4-4af5-86c4-5216c01014b4", "targetRef": "acf670a7-5e34-496c-8768-b18bbb4c09a4", "name": "To End", "id": "e6e4f61c-312e-4a01-822f-a72b5237ec42", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "4b02a02d-49e7-477f-89fa-84162350bcc6", "targetRef": "8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "name": "To start Recreate Process", "id": "dc094bde-97a9-4302-810e-edd5581e8cdd", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.requestType\t  ==\t  tw.epv.RequestType.Recreate", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "targetRef": "acf670a7-5e34-496c-8768-b18bbb4c09a4", "name": "To End", "id": "096f719b-e0bb-4ad3-88a8-662265249cfa", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "bfbec735-387a-4f92-85c4-bc6408bfd10a", "targetRef": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "name": "To Exception Handling", "id": "be95c2c1-d6e3-42a3-88f5-62a1d26fe478", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "e15b43a1-b5f2-47a0-827f-356d0d1fc4b5", "targetRef": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "name": "To Exception Handling", "id": "6aae309b-3e79-4776-8ca4-11f0241fa76d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "b731f798-9d81-4d01-899f-f6c68adf24bd", "targetRef": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "name": "To Exception Handling", "id": "1c50a334-8e56-4fc4-8d18-8ffe08eb65d7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "c45db874-d635-4df7-85b9-d6efb83e0c17", "targetRef": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "name": "To Exception Handling", "id": "d2354b5c-80f8-4e81-8070-c825bbf904f6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "c8b5cd28-ab1a-4af6-8181-7284cc27708d", "targetRef": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "name": "To Exception Handling", "id": "282cef0b-4720-4282-81f5-3dbdafc414b2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4a5f82bd-83ce-4379-8174-163144a5402c", "targetRef": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "name": "To Exception Handling", "id": "81414926-c0a5-4364-8570-a949ac22f913", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "targetRef": "0348001f-bcde-4377-86ec-1ef2c006fd74", "name": "To End Event", "id": "8ef4a9ce-6b19-4b36-82bd-898ed60b413f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "start Amend  Process", "id": "d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "421", "y": "89", "width": "95", "height": "70"}}, "ns16:incoming": "4f5524cb-d0f1-4b79-82db-7c1a6c25b45c", "ns16:outgoing": "77d41f45-95a5-45bc-8fe7-3a7bfad68d61", "ns16:script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n \r\r\ntw.local.processName = tw.epv.ODCProcessName.create+\"\";\r\r\n//tw.local.testData= tw.local.processName;\r\r\n\r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false)"}, {"scriptFormat": "text/x-javascript", "name": "start collection process", "id": "841f3829-ef78-434b-8948-6b4406876e41", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "530", "y": "166", "width": "95", "height": "70"}, "ns3:postAssignmentScript": ""}, "ns16:incoming": "bca20caf-ee0c-4bf4-8856-65b582207bc8", "ns16:outgoing": "3f66f592-cb04-439f-8325-1f2e8ecc9a4d", "ns16:script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.collection+\"\";\r\r\n\r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);"}, {"scriptFormat": "text/x-javascript", "name": "start Create Process", "id": "913cc9dc-ccc4-4af5-86c4-5216c01014b4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "386", "y": "12", "width": "95", "height": "70"}}, "ns16:incoming": "9ad63445-77ea-4b28-8a25-b1a81c64533d", "ns16:outgoing": "e6e4f61c-312e-4a01-822f-a72b5237ec42", "ns16:script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.create;\r\r\n \r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);"}, {"scriptFormat": "text/x-javascript", "name": "start Reversal  Process", "id": "6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "658", "y": "245", "width": "95", "height": "70"}}, "ns16:incoming": "a99a5322-5d68-4786-804d-67495a9f8aaf", "ns16:outgoing": "a1b36c32-c98f-42db-8685-ae5d76aa03f3", "ns16:script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.reversal+\"\";\r\r\n \r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);"}, {"scriptFormat": "text/x-javascript", "name": "start Closure Process", "id": "f89a4d43-aa72-47b9-8945-1e978931d874", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "797", "y": "313", "width": "95", "height": "70"}}, "ns16:incoming": "********-9dd1-4da5-84d4-127606177a5e", "ns16:outgoing": "b0fbef09-c987-43e4-8185-d3711fae162f", "ns16:script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n \r\r\ntw.local.processName = tw.epv.ODCProcessName.closure+\"\";\r\r\n \r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false);"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "start Recreate Process", "id": "8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "850", "y": "405", "width": "95", "height": "70"}}, "ns16:incoming": "dc094bde-97a9-4302-810e-edd5581e8cdd", "ns16:outgoing": "096f719b-e0bb-4ad3-88a8-662265249cfa", "ns16:script": "\r\r\nvar input = new tw.object.Map();\r\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\r\n\r\r\n\r\r\ntw.local.processName = tw.epv.ODCProcessName.create+\"\";\r\r\n\r\r\n\r\r\n//if(tw.env.runningServerIP == \"************\")\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\r\n//else\r\r\n//\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\t\r\r\n\r\r\n//tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\r\n\r\r\ntw.system.startProcessByName(tw.local.processName, input, false)"}], "ns16:exclusiveGateway": {"default": "9ad63445-77ea-4b28-8a25-b1a81c64533d", "name": "Request Type", "id": "4b02a02d-49e7-477f-89fa-84162350bcc6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "137", "y": "182", "width": "32", "height": "32"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.0fb58f07-d096-4ec8-8255-943ace2831b9", "ns16:outgoing": ["4f5524cb-d0f1-4b79-82db-7c1a6c25b45c", "bca20caf-ee0c-4bf4-8856-65b582207bc8", "9ad63445-77ea-4b28-8a25-b1a81c64533d", "a99a5322-5d68-4786-804d-67495a9f8aaf", "********-9dd1-4da5-84d4-127606177a5e", "dc094bde-97a9-4302-810e-edd5581e8cdd"]}, "ns16:dataObject": {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "processName", "id": "2056.f19f3e47-ad97-46d2-8c9c-ab411519ee88", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير\"", "useDefault": "false"}}}, "ns16:intermediateCatchEvent": {"name": "Error Event", "id": "81840969-afc5-4ece-8fb5-9583a41609c8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1479", "y": "432", "width": "24", "height": "24"}}, "ns16:outgoing": "f6bfc0e9-24e8-428c-8359-dfe22bb8fc58", "ns16:errorEventDefinition": {"id": "cdb58da0-9006-4637-8a9f-1b8ed3af6db2", "eventImplId": "0605d09e-8a63-44bf-8c54-ebe04934d57e", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:callActivity": {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exception Handling", "id": "0aaf3b3c-845b-49b1-85fd-e17648df01f2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1137", "y": "210", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["be95c2c1-d6e3-42a3-88f5-62a1d26fe478", "6aae309b-3e79-4776-8ca4-11f0241fa76d", "1c50a334-8e56-4fc4-8d18-8ffe08eb65d7", "d2354b5c-80f8-4e81-8070-c825bbf904f6", "282cef0b-4720-4282-81f5-3dbdafc414b2", "81414926-c0a5-4364-8570-a949ac22f913"], "ns16:outgoing": "8ef4a9ce-6b19-4b36-82bd-898ed60b413f", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Initiate ODC Process\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "913cc9dc-ccc4-4af5-86c4-5216c01014b4", "parallelMultiple": "false", "name": "Error", "id": "bfbec735-387a-4f92-85c4-bc6408bfd10a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "469", "y": "17", "width": "24", "height": "24"}}, "ns16:outgoing": "be95c2c1-d6e3-42a3-88f5-62a1d26fe478", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "e356eebc-c9d8-4044-8ad8-d58093b5b561"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "c9bf18ce-7f60-4e12-8fa7-f952431d7647", "eventImplId": "402d5c39-ea1f-41d2-8bbe-85834ef9959e", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "parallelMultiple": "false", "name": "Error1", "id": "4a5f82bd-83ce-4379-8174-163144a5402c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "504", "y": "94", "width": "24", "height": "24"}}, "ns16:outgoing": "81414926-c0a5-4364-8570-a949ac22f913", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "e72682e5-291f-4d15-80a5-31aa3a0a5fd3"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "93c47cdc-1d65-4620-8659-da71c56610dc", "eventImplId": "f1dbcf05-f85c-4799-81ba-b2d075017bd9", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "parallelMultiple": "false", "name": "Error2", "id": "e15b43a1-b5f2-47a0-827f-356d0d1fc4b5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "885", "y": "463", "width": "24", "height": "24"}}, "ns16:outgoing": "6aae309b-3e79-4776-8ca4-11f0241fa76d", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "a987228d-c94d-4edb-825b-31e596aa179b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "e7471dab-5d8b-4ef5-8acb-3ee0b047d04e", "eventImplId": "902f03d5-5082-4ae1-8569-bc6a8486fa05", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "f89a4d43-aa72-47b9-8945-1e978931d874", "parallelMultiple": "false", "name": "Error3", "id": "b731f798-9d81-4d01-899f-f6c68adf24bd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "832", "y": "371", "width": "24", "height": "24"}}, "ns16:outgoing": "1c50a334-8e56-4fc4-8d18-8ffe08eb65d7", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "71884565-98e3-4cc7-835d-a8d459bb9284"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "3dd162cf-1225-4bea-8110-08dde396ef61", "eventImplId": "3c6f693d-67df-4570-8156-b115aa5e2039", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "parallelMultiple": "false", "name": "Error4", "id": "c45db874-d635-4df7-85b9-d6efb83e0c17", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "693", "y": "303", "width": "24", "height": "24"}}, "ns16:outgoing": "d2354b5c-80f8-4e81-8070-c825bbf904f6", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "eaae946e-bcc4-4fdc-84b5-9bc5fe3a85ec"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "37bc0b66-8954-45e0-8b4f-e06b558674af", "eventImplId": "2bceb9ea-88c1-4ded-87ef-0969e9e03ced", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "841f3829-ef78-434b-8948-6b4406876e41", "parallelMultiple": "false", "name": "Error5", "id": "c8b5cd28-ab1a-4af6-8181-7284cc27708d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "539", "y": "224", "width": "24", "height": "24"}}, "ns16:outgoing": "282cef0b-4720-4282-81f5-3dbdafc414b2", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "3dd6e9a0-bc87-4a9a-881e-519228aa77b8"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "3a046557-d69a-4fed-84f7-13fa8b36a673", "eventImplId": "6b787de9-f688-462d-8452-51b318d18554", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.77d41f45-95a5-45bc-8fe7-3a7bfad68d61", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e"], "endStateId": "Out", "toProcessItemId": ["2025.acf670a7-5e34-496c-8768-b18bbb4c09a4", "2025.acf670a7-5e34-496c-8768-b18bbb4c09a4"], "guid": "1c4d10b5-ca78-4698-b4bf-1f315dae0586", "versionId": "1f055295-b2da-4dc2-ae3b-d8822e2eece7", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a1b36c32-c98f-42db-8685-ae5d76aa03f3", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db"], "endStateId": "Out", "toProcessItemId": ["2025.acf670a7-5e34-496c-8768-b18bbb4c09a4", "2025.acf670a7-5e34-496c-8768-b18bbb4c09a4"], "guid": "a3f2e688-83e9-40e7-b383-7608264f87c7", "versionId": "2a13f6b7-abca-4873-b4e4-ed8e83c45df3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "Collection", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.bca20caf-ee0c-4bf4-8856-65b582207bc8", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "2025.4b02a02d-49e7-477f-89fa-84162350bcc6"], "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2555", "toProcessItemId": ["2025.841f3829-ef78-434b-8948-6b4406876e41", "2025.841f3829-ef78-434b-8948-6b4406876e41"], "guid": "d627d29b-a500-4b19-a8ac-bd2ab69dbf29", "versionId": "3aac8688-0921-4deb-8724-c31f9d61aff2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Amendment", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4f5524cb-d0f1-4b79-82db-7c1a6c25b45c", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "2025.4b02a02d-49e7-477f-89fa-84162350bcc6"], "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2554", "toProcessItemId": ["2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e", "2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e"], "guid": "a4eea9ec-efd9-4094-8b84-f1348fe767cc", "versionId": "3ea05a01-005c-4d32-b4dd-6805278ef0fe", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Create ODC", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.9ad63445-77ea-4b28-8a25-b1a81c64533d", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "2025.4b02a02d-49e7-477f-89fa-84162350bcc6"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4", "2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4"], "guid": "92c467ba-1aad-4cd3-80d9-983e6a9c274a", "versionId": "c23d97e2-be60-4622-bd7b-556ee7fdd8b2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e6e4f61c-312e-4a01-822f-a72b5237ec42", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4", "2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4"], "endStateId": "Out", "toProcessItemId": ["2025.acf670a7-5e34-496c-8768-b18bbb4c09a4", "2025.acf670a7-5e34-496c-8768-b18bbb4c09a4"], "guid": "fa23cf65-d630-4462-b777-097544e8ed3e", "versionId": "c996527f-e9e6-4cf5-9c55-25d33c5da663", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "Reversal", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a99a5322-5d68-4786-804d-67495a9f8aaf", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "2025.4b02a02d-49e7-477f-89fa-84162350bcc6"], "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2556", "toProcessItemId": ["2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db", "2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db"], "guid": "267d50c4-691b-4e48-8bf1-aa81fef5508d", "versionId": "cf28ab99-1919-4c86-a30f-b023badc15d8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.3f66f592-cb04-439f-8325-1f2e8ecc9a4d", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.841f3829-ef78-434b-8948-6b4406876e41", "2025.841f3829-ef78-434b-8948-6b4406876e41"], "endStateId": "Out", "toProcessItemId": ["2025.acf670a7-5e34-496c-8768-b18bbb4c09a4", "2025.acf670a7-5e34-496c-8768-b18bbb4c09a4"], "guid": "6d79dba6-67d8-4016-adb8-1de083344c11", "versionId": "dbc4503d-1496-4dbf-80dd-64db63921551", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.096f719b-e0bb-4ad3-88a8-662265249cfa", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e"], "endStateId": "Out", "toProcessItemId": ["2025.acf670a7-5e34-496c-8768-b18bbb4c09a4", "2025.acf670a7-5e34-496c-8768-b18bbb4c09a4"], "guid": "70ceb723-2975-4801-aed1-67707bcb7b70", "versionId": "e3b9094b-c4f4-40c5-82af-f06172236a29", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To End Event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.8ef4a9ce-6b19-4b36-82bd-898ed60b413f", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2", "2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.0348001f-bcde-4377-86ec-1ef2c006fd74", "2025.0348001f-bcde-4377-86ec-1ef2c006fd74"], "guid": "19e9d660-05af-4ea1-b06d-98db015ce4d9", "versionId": "e7a22029-2bcc-470e-91a0-a4f06dc90c07", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Closure", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.********-9dd1-4da5-84d4-127606177a5e", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "2025.4b02a02d-49e7-477f-89fa-84162350bcc6"], "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2557", "toProcessItemId": ["2025.f89a4d43-aa72-47b9-8945-1e978931d874", "2025.f89a4d43-aa72-47b9-8945-1e978931d874"], "guid": "e887d415-f469-4d42-88dd-6eedd03e5891", "versionId": "ebdcd806-8a14-45d4-9a3a-ff67954e5fe6", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To start Recreate Process", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.dc094bde-97a9-4302-810e-edd5581e8cdd", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4b02a02d-49e7-477f-89fa-84162350bcc6", "2025.4b02a02d-49e7-477f-89fa-84162350bcc6"], "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:2558", "toProcessItemId": ["2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e", "2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e"], "guid": "9cae9ffb-d5b1-4b44-8e4c-c80df3f02417", "versionId": "f6c65923-43f5-4b4b-a68a-e2c20944a9f3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.b0fbef09-c987-43e4-8185-d3711fae162f", "processId": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f89a4d43-aa72-47b9-8945-1e978931d874", "2025.f89a4d43-aa72-47b9-8945-1e978931d874"], "endStateId": "Out", "toProcessItemId": ["2025.acf670a7-5e34-496c-8768-b18bbb4c09a4", "2025.acf670a7-5e34-496c-8768-b18bbb4c09a4"], "guid": "7e8aa77b-da7c-44fa-aab4-af5e10eee54b", "versionId": "fe8b486b-7041-4ef0-906d-af097ae211d7", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}