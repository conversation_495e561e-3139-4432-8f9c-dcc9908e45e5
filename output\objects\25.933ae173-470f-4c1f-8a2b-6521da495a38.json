{"id": "25.933ae173-470f-4c1f-8a2b-6521da495a38", "versionId": "********-b951-4eed-b5a7-b9c8ff257a6c", "name": "ODC Reversal اعادة قيد تحصيل مستندى تصدير", "type": "bpd", "typeName": "Business Process Definition", "details": {}, "_fullObjectData": {"teamworks": {"bpd": {"id": "25.933ae173-470f-4c1f-8a2b-6521da495a38", "name": "ODC Reversal اعادة قيد تحصيل مستندى تصدير", "bpdParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "bpdParameterId": "2007.86f87789-824f-4930-8c19-5a719284c659", "bpdId": "25.933ae173-470f-4c1f-8a2b-6521da495a38", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "0", "documentation": "", "hasDefault": "false", "defaultValue": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject", "isReadOnly": "false", "guid": "c8ae64ba-c918-41ea-9219-8efc28f6786d", "versionId": "9edff5d8-2eaf-42a4-bf97-7d75328a5f60"}, {"name": "routingDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "bpdParameterId": "2007.684c7152-3e6b-4505-8f37-4ed21a611236", "bpdId": "25.933ae173-470f-4c1f-8a2b-6521da495a38", "parameterType": "1", "isArrayOf": "false", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "seq": "1", "documentation": "", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isReadOnly": "false", "guid": "2dc15982-92f8-448b-841e-85594042843c", "versionId": "76c1cd5f-993d-45d9-bcf4-2b598cf4c7b2"}], "lastModified": "*************", "lastModifiedBy": "eslam1", "bpdId": "25.933ae173-470f-4c1f-8a2b-6521da495a38", "isTrackingEnabled": "true", "isSpcEnabled": "false", "restrictedName": {"isNull": "true"}, "isCriticalPathEnabled": "false", "participantRef": {"isNull": "true"}, "businessDataParticipantRef": {"isNull": "true"}, "perfMetricParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6", "ownerTeamParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e", "timeScheduleType": {"isNull": "true"}, "timeScheduleName": "NBEWork", "timeScheduleExpression": {"isNull": "true"}, "holidayScheduleType": {"isNull": "true"}, "holidayScheduleName": "NBEHoliday", "holidayScheduleExpression": {"isNull": "true"}, "timezoneType": {"isNull": "true"}, "timezone": "Egypt", "timezoneExpression": {"isNull": "true"}, "internalName": {"isNull": "true"}, "description": "", "type": "1", "rootBpdId": {"isNull": "true"}, "parentBpdId": {"isNull": "true"}, "parentFlowObjectId": {"isNull": "true"}, "xmlData": {"isNull": "true"}, "bpmn2Data": "<ns15:definitions xmlns:ns15=\"http://www.omg.org/spec/BPMN/20100524/MODEL\" xmlns:ns2=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup\" xmlns:ns3=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process\" xmlns:ns4=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle\" xmlns:ns5=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case\" xmlns:ns6=\"http://www.ibm.com/bpm/Extensions\" xmlns:ns7=\"http://www.ibm.com/xmlns/prod/bpm/uca\" xmlns:ns8=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team\" xmlns:ns9=\"http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary\" xmlns:ns10=\"http://www.omg.org/spec/DD/20100524/DI\" xmlns:ns11=\"http://www.omg.org/spec/DD/20100524/DC\" xmlns:ns12=\"http://www.omg.org/spec/BPMN/20100524/BPMNDI\" xmlns:ns13=\"http://www.ibm.com/xmlns/prod/bpm/graph\" xmlns:ns14=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice\" xmlns:ns16=\"http://www.ibm.com/bpm/processappsettings\" xmlns:ns17=\"http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth\" xmlns:ns18=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns19=\"http://www.ibm.com/xmlns/links\" xmlns:ns20=\"http://www.ibm.com/bpm/uitheme\" xmlns:ns21=\"http://www.ibm.com/bpm/coachview\" xmlns:ns22=\"http://www.ibm.com/xmlns/tagging\" id=\"fe1ac9f6-647e-47c2-9e22-608f9d73ef7a\" targetNamespace=\"\" expressionLanguage=\"http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript\"><ns15:process name=\"ODC Reversal اعادة قيد تحصيل مستندى تصدير\" id=\"25.933ae173-470f-4c1f-8a2b-6521da495a38\" ns3:executionMode=\"longRunning\"><ns15:documentation /><ns15:extensionElements><ns4:bpdExtension instanceName=\"&quot; Request Type: &quot;+ tw.local.odcRequest.requestType.name  +&quot; , &quot;+&quot; CIF: &quot;+ tw.local.odcRequest.cif +&quot; , &quot; +&quot;Request Number: &quot; +tw.system.process.instanceId\" dueDateEnabled=\"false\" atRiskCalcEnabled=\"false\" enableTracking=\"false\" allowProjectedPathManagement=\"false\" optimizeExecForLatency=\"false\" sBOSyncEnabled=\"true\" allowContentOperations=\"false\" autoTrackingEnabled=\"false\" autoTrackingName=\"at1693545399110\"><ns4:dueDateSettings type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">8</ns4:dueDate></ns4:dueDateSettings><ns4:workSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timeScheduleName>NBEWork</ns4:timeScheduleName><ns4:timezoneType>0</ns4:timezoneType><ns4:timezone>Egypt</ns4:timezone><ns4:holidayScheduleType>0</ns4:holidayScheduleType><ns4:holidayScheduleName>NBEHoliday</ns4:holidayScheduleName></ns4:workSchedule></ns4:bpdExtension><ns5:caseExtension><ns5:caseFolder id=\"5aba4da2-4484-4810-835b-d5873189e7d7\" /></ns5:caseExtension><ns5:isConvergedProcess>true</ns5:isConvergedProcess></ns15:extensionElements><ns15:ioSpecification><ns15:extensionElements><ns3:epvProcessLinks><ns3:epvProcessLinkRef epvId=\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\" epvProcessLinkId=\"1e68ed8f-13f7-4cb5-806c-8d8636ef2b03\" /><ns3:epvProcessLinkRef epvId=\"21.dbbaa047-8f02-4397-b1b5-41f11b0256b3\" epvProcessLinkId=\"014acb69-59a8-45e7-822c-05953bccc3e0\" /></ns3:epvProcessLinks></ns15:extensionElements><ns15:dataInput name=\"odcRequest\" itemSubjectRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\" isCollection=\"false\" id=\"2007.86f87789-824f-4930-8c19-5a719284c659\"><ns15:extensionElements><ns4:searchableField id=\"7b6a22dc-233a-4ecd-8cbb-ee6921af4adc\" alias=\"InitiatorHubBranch\" path=\".initiator\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"f8755e6e-8272-431b-82c8-e9f1e7ed15ce\" alias=\"parentRequestNumber\" path=\".parentRequestNo\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"7eb6d0f9-71e2-4496-86ce-d833141978e6\" alias=\"requestDate\" path=\".requestDate\" type=\"12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\" /><ns4:searchableField id=\"a339ecd6-4974-4376-8f65-0fcf632e8222\" alias=\"requestStatus\" path=\".appInfo.status\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"cbf1ac69-8dd1-4ef4-8633-6640a9052e85\" alias=\"InitiatorUserName\" path=\".appInfo.initiator\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"e19ec9a2-b0e8-430d-89e9-a631fd4af208\" alias=\"customerCIF\" path=\".CustomerInfo.cif\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"83c8ec89-336a-4889-8831-2db11de97e8d\" alias=\"customerName\" path=\".customerName\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns4:searchableField id=\"891a5bad-505c-4409-8a7d-e7068726a11a\" alias=\"customerName_1\" path=\".CustomerInfo.customerName\" type=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" /><ns3:defaultValue useDefault=\"false\">var autoObject = new tw.object.ODCRequest();\r\r\nautoObject.initiator = \"\";\r\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.requestNature.name = \"\";\r\r\nautoObject.requestNature.value = \"\";\r\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.requestType.name = \"\";\r\r\nautoObject.requestType.value = \"\";\r\r\nautoObject.cif = \"\";\r\r\nautoObject.customerName = \"\";\r\r\nautoObject.parentRequestNo = \"\";\r\r\nautoObject.requestDate = new TWDate();\r\r\nautoObject.ImporterName = \"\";\r\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\r\nautoObject.appInfo.requestDate = \"\";\r\r\nautoObject.appInfo.status = \"\";\r\r\nautoObject.appInfo.subStatus = \"\";\r\r\nautoObject.appInfo.initiator = \"\";\r\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.appInfo.branch.name = \"\";\r\r\nautoObject.appInfo.branch.value = \"\";\r\r\nautoObject.appInfo.requestName = \"\";\r\r\nautoObject.appInfo.requestType = \"\";\r\r\nautoObject.appInfo.stepName = \"\";\r\r\nautoObject.appInfo.appRef = \"\";\r\r\nautoObject.appInfo.appID = \"\";\r\r\nautoObject.appInfo.instanceID = \"\";\r\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\r\nautoObject.CustomerInfo.cif = \"\";\r\r\nautoObject.CustomerInfo.customerName = \"\";\r\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\r\nautoObject.CustomerInfo.customerType = \"\";\r\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\r\nautoObject.CustomerInfo.country = \"\";\r\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\r\nautoObject.BasicDetails.requestNature = \"\";\r\r\nautoObject.BasicDetails.requestType = \"\";\r\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\r\nautoObject.BasicDetails.requestState = \"\";\r\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\r\nautoObject.BasicDetails.contractStage = \"\";\r\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.currency.name = \"\";\r\r\nautoObject.FcCollections.currency.value = \"\";\r\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\r\nautoObject.FcCollections.fromDate = new TWDate();\r\r\nautoObject.FcCollections.ToDate = new TWDate();\r\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.accountNo.name = \"\";\r\r\nautoObject.FcCollections.accountNo.value = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\nautoObject.FcCollections.isReversed = false;\r\r\nautoObject.FcCollections.usedAmount = 0.0;\r\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\r\nautoObject.ImporterDetails.importerName = \"\";\r\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\r\nautoObject.ImporterDetails.bank = \"\";\r\r\nautoObject.ImporterDetails.BICCode = \"\";\r\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\r\nautoObject.OdcCollection.amount = 0.0;\r\r\nautoObject.OdcCollection.currency = \"\";\r\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\r\nautoObject.ReversalReason.reversalReason = \"\";\r\r\nautoObject.ReversalReason.closureReason = \"\";\r\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ContractCreation.productCode.name = \"\";\r\r\nautoObject.ContractCreation.productCode.value = \"\";\r\r\nautoObject.ContractCreation.productDescription = \"\";\r\r\nautoObject.ContractCreation.Stage = \"\";\r\r\nautoObject.ContractCreation.userReference = \"\";\r\r\nautoObject.ContractCreation.sourceReference = \"\";\r\r\nautoObject.ContractCreation.currency = \"\";\r\r\nautoObject.ContractCreation.amount = 0.0;\r\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\r\nautoObject.ContractCreation.tenorDays = 0;\r\r\nautoObject.ContractCreation.transitDays = 0;\r\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\r\nautoObject.Parties = new tw.object.odcParties();\r\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\r\nautoObject.Parties.Drawer.partyId = \"\";\r\r\nautoObject.Parties.Drawer.partyName = \"\";\r\r\nautoObject.Parties.Drawer.country = \"\";\r\r\nautoObject.Parties.Drawer.Language = \"\";\r\r\nautoObject.Parties.Drawer.Reference = \"\";\r\r\nautoObject.Parties.Drawer.address1 = \"\";\r\r\nautoObject.Parties.Drawer.address2 = \"\";\r\r\nautoObject.Parties.Drawer.address3 = \"\";\r\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\r\nautoObject.Parties.Drawee.partyId = \"\";\r\r\nautoObject.Parties.Drawee.partyName = \"\";\r\r\nautoObject.Parties.Drawee.country = \"\";\r\r\nautoObject.Parties.Drawee.Language = \"\";\r\r\nautoObject.Parties.Drawee.Reference = \"\";\r\r\nautoObject.Parties.Drawee.address1 = \"\";\r\r\nautoObject.Parties.Drawee.address2 = \"\";\r\r\nautoObject.Parties.Drawee.address3 = \"\";\r\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\r\nautoObject.Parties.collectingBank.id = \"\";\r\r\nautoObject.Parties.collectingBank.name = \"\";\r\r\nautoObject.Parties.collectingBank.country = \"\";\r\r\nautoObject.Parties.collectingBank.language = \"\";\r\r\nautoObject.Parties.collectingBank.reference = \"\";\r\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\r\nautoObject.Parties.collectingBank.cif = \"\";\r\r\nautoObject.Parties.collectingBank.media = \"\";\r\r\nautoObject.Parties.collectingBank.address = \"\";\r\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\r\nautoObject.Parties.partyTypes.country = \"\";\r\r\nautoObject.Parties.partyTypes.language = \"\";\r\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\r\nautoObject.complianceApproval = false;\r\r\nautoObject.stepLog = new tw.object.StepLog();\r\r\nautoObject.stepLog.startTime = new TWDate();\r\r\nautoObject.stepLog.endTime = new TWDate();\r\r\nautoObject.stepLog.userName = \"\";\r\r\nautoObject.stepLog.role = \"\";\r\r\nautoObject.stepLog.step = \"\";\r\r\nautoObject.stepLog.action = \"\";\r\r\nautoObject.stepLog.comment = \"\";\r\r\nautoObject.stepLog.terminateReason = \"\";\r\r\nautoObject.stepLog.returnReason = \"\";\r\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\nautoObject.actions[0] = \"\";\r\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\r\nautoObject.attachmentDetails.folderID = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\r\nautoObject.complianceComments[0].userName = \"\";\r\r\nautoObject.complianceComments[0].role = \"\";\r\r\nautoObject.complianceComments[0].step = \"\";\r\r\nautoObject.complianceComments[0].action = \"\";\r\r\nautoObject.complianceComments[0].comment = \"\";\r\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\r\nautoObject.complianceComments[0].returnReason = \"\";\r\r\nautoObject.History = new tw.object.listOf.StepLog();\r\r\nautoObject.History[0] = new tw.object.StepLog();\r\r\nautoObject.History[0].startTime = new TWDate();\r\r\nautoObject.History[0].endTime = new TWDate();\r\r\nautoObject.History[0].userName = \"\";\r\r\nautoObject.History[0].role = \"\";\r\r\nautoObject.History[0].step = \"\";\r\r\nautoObject.History[0].action = \"\";\r\r\nautoObject.History[0].comment = \"\";\r\r\nautoObject.History[0].terminateReason = \"\";\r\r\nautoObject.History[0].returnReason = \"\";\r\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\nautoObject.documentSource.name = \"\";\r\r\nautoObject.documentSource.value = \"\";\r\r\nautoObject.folderID = \"\";\r\r\nautoObject.isLiquidated = false;\r\r\nautoObject</ns3:defaultValue></ns15:extensionElements></ns15:dataInput><ns15:dataInput name=\"routingDetails\" itemSubjectRef=\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\" isCollection=\"false\" id=\"2007.684c7152-3e6b-4505-8f37-4ed21a611236\" /><ns15:inputSet /><ns15:inputSet id=\"_33e6e276-9f24-47a5-8f75-c1819ec07f1d\" /><ns15:outputSet /><ns15:outputSet id=\"_5b5b1b2e-cab6-4f6f-96d2-c0d0f82c82e8\" /></ns15:ioSpecification><ns15:laneSet id=\"320b2a03-2ef6-48f6-9980-49703310afb3\"><ns15:lane name=\"Hub / center Maker\" partitionElementRef=\"24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce\" id=\"2e4a3c95-9839-4c37-870f-b0215a22597b\" ns4:isSystemLane=\"false\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"0\" y=\"0\" width=\"3000\" height=\"200\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:flowNodeRef>4d00b4e6-b345-483f-af99-b6815f8ebb6f</ns15:flowNodeRef><ns15:flowNodeRef>cfc9c782-1e15-4918-8597-81c8a226db13</ns15:flowNodeRef><ns15:flowNodeRef>c43302a8-b1fa-4fa9-805b-d2abed461cf9</ns15:flowNodeRef></ns15:lane><ns15:lane name=\"Hub / center Checker\" partitionElementRef=\"24.8e005024-3fe0-4848-8c3c-f1e9483900c6\" id=\"7e6e9014-5f8d-44eb-8ceb-ecba6505368c\" ns4:isSystemLane=\"false\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"0\" y=\"201\" width=\"3000\" height=\"200\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:flowNodeRef>204734d8-ab45-42fa-8dde-9f2d35136f07</ns15:flowNodeRef><ns15:flowNodeRef>a66b788f-eb9d-4662-8d5a-15e1618e166e</ns15:flowNodeRef><ns15:flowNodeRef>6869d68e-0a73-474c-a9da-f7c788cf4d26</ns15:flowNodeRef><ns15:flowNodeRef>60aa4589-527a-4330-8c62-e27d874075b2</ns15:flowNodeRef><ns15:flowNodeRef>462fbe06-d514-4387-8592-2acd0f6a0a5f</ns15:flowNodeRef></ns15:lane><ns15:lane name=\"System\" partitionElementRef=\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\" id=\"9180d59b-6f02-4dad-a33f-d017ed000296\" ns4:isSystemLane=\"true\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"0\" y=\"402\" width=\"3000\" height=\"200\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:flowNodeRef>956dcbec-d5f4-4a3d-8f90-86bed3f21e33</ns15:flowNodeRef><ns15:flowNodeRef>c8b8dc76-b1ba-4d4e-8653-ea6dc4248654</ns15:flowNodeRef></ns15:lane></ns15:laneSet><ns15:startEvent name=\"Start\" id=\"4d00b4e6-b345-483f-af99-b6815f8ebb6f\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"39\" y=\"82\" width=\"24\" height=\"24\" color=\"#F8F8F8\" /><ns3:postAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.isFirstTime</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">true</ns15:to></ns3:postAssignment><ns3:default>8adc5346-4dc4-48ea-88bc-847fe8c3e8bb</ns3:default></ns15:extensionElements><ns15:outgoing>8adc5346-4dc4-48ea-88bc-847fe8c3e8bb</ns15:outgoing></ns15:startEvent><ns15:endEvent name=\"Approve\" id=\"6869d68e-0a73-474c-a9da-f7c788cf4d26\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"728\" y=\"128\" width=\"24\" height=\"24\" color=\"#F8F8F8\" /></ns15:extensionElements><ns15:incoming>8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3</ns15:incoming></ns15:endEvent><ns15:callActivity calledElement=\"1.b9bf1811-6e1e-41a3-828b-39059fa168cb\" default=\"1d1902fe-e246-4af8-843c-92b62fbe98d3\" name=\"ACT01 Create ODC Reversal Request\" id=\"cfc9c782-1e15-4918-8597-81c8a226db13\"><ns15:extensionElements><ns4:activityExtension conditional=\"false\"><ns4:conditionScript /></ns4:activityExtension><ns13:nodeVisualInfo x=\"338\" y=\"59\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:subject>Create ODC Reversal Request –  إنشاء طلب اعادة قيد تحصيل مستندى تصدير &lt;#= tw.system.process.instanceId #&gt;</ns4:subject><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">parseFloat(tw.epv.ODCCreationSLA.RACT01)</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>Africa/Cairo</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timeScheduleName>NBEWork</ns4:timeScheduleName><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType><ns4:holidayScheduleName>NBEHoliday</ns4:holidayScheduleName></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>UserTask</ns4:activityType><ns3:postAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.isFirstTime</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">false</ns15:to></ns3:postAssignment></ns15:extensionElements><ns15:incoming>cc8203a1-7910-4b2f-8009-0eeb7a8e3387</ns15:incoming><ns15:incoming>8adc5346-4dc4-48ea-88bc-847fe8c3e8bb</ns15:incoming><ns15:outgoing>1d1902fe-e246-4af8-843c-92b62fbe98d3</ns15:outgoing><ns15:dataInputAssociation><ns15:targetRef>2055.602aae62-b023-4b72-8c17-f498f81897a8</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\">tw.local.isFirstTime</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.0f07a9fc-f9d5-4131-8028-34ad2f6c6eed</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.system.user.fullName</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.46373b11-9f39-48b9-8787-37bd5d89f78a</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\">tw.local.routingDetails</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.parentPath</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.1bba753e-260d-4827-8632-e837d4c62da9</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.lastAction</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.parentPath</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns4:activityPerformer distribution=\"LastUser\" name=\"Lane\"><ns4:teamFilterService serviceRef=\"1.302e4a54-16ab-47b9-9b70-d933329c72e7\"><ns15:dataInputAssociation><ns15:targetRef>2055.b95f0910-f183-4dab-9065-28297a14ceef</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">\"BPM_ODC_HUB_\"+tw.local.routingDetails.hubCode+\"_CU_MKR\"</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.routingDetails.hubCode</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_MKR\"</ns15:from></ns15:assignment></ns15:dataInputAssociation></ns4:teamFilterService></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:callActivity calledElement=\"1.c71c7233-23ac-4e5f-a241-09cd96502615\" default=\"d1c41520-7c04-4ae2-8052-46fb397d3f2a\" name=\"ACT02 Review ODC Reversal Request\" id=\"204734d8-ab45-42fa-8dde-9f2d35136f07\"><ns15:extensionElements><ns4:activityExtension conditional=\"false\"><ns4:conditionScript /></ns4:activityExtension><ns13:nodeVisualInfo x=\"338\" y=\"45\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:subject>Review ODC Reversal Request – مراجعة طلب اعادة قيد تحصيل مستندى تصدير </ns4:subject><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">parseFloat(tw.epv.ODCCreationSLA.RACT02)</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>Africa/Cairo</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timeScheduleName>NBEWork</ns4:timeScheduleName><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType><ns4:holidayScheduleName>NBEHoliday</ns4:holidayScheduleName></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>UserTask</ns4:activityType></ns15:extensionElements><ns15:incoming>1d1902fe-e246-4af8-843c-92b62fbe98d3</ns15:incoming><ns15:outgoing>d1c41520-7c04-4ae2-8052-46fb397d3f2a</ns15:outgoing><ns15:dataInputAssociation><ns15:targetRef>2055.a82241b6-421c-44f4-83f7-f6d3d3d3a024</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.8b489eb0-ce3f-46d6-8896-60fbb2cd2288</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.lastAction</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.34b891a3-f943-4cf2-8753-3a356f300282</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.parentPath</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataOutputAssociation><ns15:sourceRef>2055.d49e3545-463a-4eb4-8273-1e61d066f668</ns15:sourceRef><ns15:assignment><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:to></ns15:assignment></ns15:dataOutputAssociation><ns4:activityPerformer distribution=\"None\" name=\"Lane\"><ns4:teamFilterService serviceRef=\"1.302e4a54-16ab-47b9-9b70-d933329c72e7\"><ns15:dataInputAssociation><ns15:targetRef>2055.b95f0910-f183-4dab-9065-28297a14ceef</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">\"BPM_ODC_HUB_\"+tw.local.routingDetails.hubCode+\"_CU_CHKR\"</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.routingDetails.hubCode</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_CHKR\"</ns15:from></ns15:assignment></ns15:dataInputAssociation></ns4:teamFilterService></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:sequenceFlow sourceRef=\"cfc9c782-1e15-4918-8597-81c8a226db13\" targetRef=\"204734d8-ab45-42fa-8dde-9f2d35136f07\" name=\"To ACT02 Review ODC Reversal Request\" id=\"1d1902fe-e246-4af8-843c-92b62fbe98d3\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>topCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:exclusiveGateway default=\"cc8203a1-7910-4b2f-8009-0eeb7a8e3387\" name=\"Checker Decision Action\" id=\"a66b788f-eb9d-4662-8d5a-15e1618e166e\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"532\" y=\"64\" width=\"32\" height=\"32\" /></ns15:extensionElements><ns15:incoming>d1c41520-7c04-4ae2-8052-46fb397d3f2a</ns15:incoming><ns15:outgoing>8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3</ns15:outgoing><ns15:outgoing>45efef31-a3f7-48df-8e5f-55d0bd5a1a21</ns15:outgoing><ns15:outgoing>cc8203a1-7910-4b2f-8009-0eeb7a8e3387</ns15:outgoing></ns15:exclusiveGateway><ns15:sequenceFlow sourceRef=\"204734d8-ab45-42fa-8dde-9f2d35136f07\" targetRef=\"a66b788f-eb9d-4662-8d5a-15e1618e166e\" name=\"To Checker Decision Action\" id=\"d1c41520-7c04-4ae2-8052-46fb397d3f2a\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:endEvent name=\"Cancel\" id=\"60aa4589-527a-4330-8c62-e27d874075b2\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"729\" y=\"67\" width=\"24\" height=\"24\" /></ns15:extensionElements><ns15:incoming>45efef31-a3f7-48df-8e5f-55d0bd5a1a21</ns15:incoming></ns15:endEvent><ns15:sequenceFlow sourceRef=\"a66b788f-eb9d-4662-8d5a-15e1618e166e\" targetRef=\"60aa4589-527a-4330-8c62-e27d874075b2\" name=\"To Cancel\" id=\"45efef31-a3f7-48df-8e5f-55d0bd5a1a21\"><ns15:extensionElements><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" /><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements><ns15:conditionExpression xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest</ns15:conditionExpression></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"a66b788f-eb9d-4662-8d5a-15e1618e166e\" targetRef=\"6869d68e-0a73-474c-a9da-f7c788cf4d26\" name=\"To Approve\" id=\"8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3\"><ns15:extensionElements><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" /><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements><ns15:conditionExpression xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.approveRequest</ns15:conditionExpression></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"a66b788f-eb9d-4662-8d5a-15e1618e166e\" targetRef=\"cfc9c782-1e15-4918-8597-81c8a226db13\" name=\"Back To initiator\" id=\"cc8203a1-7910-4b2f-8009-0eeb7a8e3387\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation><ns13:targetPortLocation>rightCenter</ns13:targetPortLocation><ns13:showLabel>true</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements><ns15:conditionExpression xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.</ns15:conditionExpression></ns15:sequenceFlow><ns15:dataObject itemSubjectRef=\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\" isCollection=\"false\" name=\"isFirstTime\" id=\"862e24d6-e072-4653-8abd-681972b119f0\"><ns15:extensionElements><ns3:defaultValue useDefault=\"true\">true</ns3:defaultValue></ns15:extensionElements></ns15:dataObject><ns15:dataObject itemSubjectRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" isCollection=\"false\" name=\"lastAction\" id=\"159f0712-06fa-4aa8-8a5d-bef2234b0a9f\" /><ns15:sequenceFlow sourceRef=\"4d00b4e6-b345-483f-af99-b6815f8ebb6f\" targetRef=\"cfc9c782-1e15-4918-8597-81c8a226db13\" name=\"To ODC Initiators\" id=\"8adc5346-4dc4-48ea-88bc-847fe8c3e8bb\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:callActivity calledElement=\"1.d7acf968-6740-4e52-b037-2049466eeeb2\" default=\"949eda48-8282-4104-8add-a859c1fa92f3\" name=\"Send Escalation mail\" id=\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\"><ns15:extensionElements><ns4:deleteTaskOnCompletion>true</ns4:deleteTaskOnCompletion><ns13:nodeVisualInfo x=\"378\" y=\"22\" width=\"95\" height=\"70\" /><ns4:userTaskSettings><ns4:activityPriority type=\"Priority\"><ns4:priority>Normal</ns4:priority></ns4:activityPriority><ns4:activityDueDate type=\"TimeCalculation\"><ns4:dueDate unit=\"Hours\" timeOfDay=\"00:00\">1</ns4:dueDate><ns4:timeZone type=\"TimeZone\"><ns4:value>(use default)</ns4:value></ns4:timeZone></ns4:activityDueDate><ns4:activityAssignmentType>Lane</ns4:activityAssignmentType><ns4:activityWorkSchedule><ns4:timeScheduleType>0</ns4:timeScheduleType><ns4:timezoneType>0</ns4:timezoneType><ns4:holidayScheduleType>0</ns4:holidayScheduleType></ns4:activityWorkSchedule></ns4:userTaskSettings><ns4:activityType>ServiceTask</ns4:activityType><ns4:activityExtension conditional=\"false\"><ns4:conditionScript /></ns4:activityExtension></ns15:extensionElements><ns15:incoming>f88a6f6e-d92d-4874-8501-b54942aec83d</ns15:incoming><ns15:incoming>f2324e42-816f-420c-8247-62c22a708d72</ns15:incoming><ns15:outgoing>949eda48-8282-4104-8add-a859c1fa92f3</ns15:outgoing><ns15:dataInputAssociation><ns15:targetRef>2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\">tw.local.odcRequest</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.taskId</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns15:dataInputAssociation><ns15:targetRef>2055.9771d7e8-ca59-430e-8b1a-194cf04c1182</ns15:targetRef><ns15:assignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\" evaluatesToTypeRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\">tw.local.mailTo</ns15:from></ns15:assignment></ns15:dataInputAssociation><ns4:activityPerformer distribution=\"None\" name=\"Lane\"><ns4:teamFilterService /></ns4:activityPerformer><ns4:activityPerformer distribution=\"None\" name=\"Team\"><ns4:teamFilterService /></ns4:activityPerformer><ns15:performer name=\"Expert\" /></ns15:callActivity><ns15:dataObject itemSubjectRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" isCollection=\"false\" name=\"taskId\" id=\"db3fa75c-e63e-443f-8487-98f335bea12e\" /><ns15:dataObject itemSubjectRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" isCollection=\"false\" name=\"mailTo\" id=\"4e5d0b49-dad8-4560-8d99-82f26b0daeb8\" /><ns15:boundaryEvent cancelActivity=\"false\" attachedToRef=\"cfc9c782-1e15-4918-8597-81c8a226db13\" parallelMultiple=\"false\" name=\"Boundary Event1\" id=\"c43302a8-b1fa-4fa9-805b-d2abed461cf9\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"347\" y=\"117\" width=\"24\" height=\"24\" /><ns3:default>f88a6f6e-d92d-4874-8501-b54942aec83d</ns3:default><ns3:postAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.taskId</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.system.step.task.id</ns15:to></ns3:postAssignment><ns3:postAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.mailTo</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">\"\"</ns15:to></ns3:postAssignment></ns15:extensionElements><ns15:outgoing>f88a6f6e-d92d-4874-8501-b54942aec83d</ns15:outgoing><ns15:timerEventDefinition id=\"cb3ec0ad-00f1-4b8c-8e62-ac8b2c7b2945\" eventImplId=\"cfbc88db-07d4-47c8-816b-abf2a1695f2f\"><ns15:extensionElements><ns4:timerEventSettings><ns4:dateType>DueDate</ns4:dateType><ns4:relativeDirection>AfterDueDate</ns4:relativeDirection><ns4:relativeTime>0</ns4:relativeTime><ns4:relativeTimeResolution>Minutes</ns4:relativeTimeResolution><ns4:toleranceInterval>0</ns4:toleranceInterval><ns4:toleranceIntervalResolution>Minutes</ns4:toleranceIntervalResolution><ns4:useCalendar>false</ns4:useCalendar></ns4:timerEventSettings></ns15:extensionElements></ns15:timerEventDefinition></ns15:boundaryEvent><ns15:boundaryEvent cancelActivity=\"false\" attachedToRef=\"204734d8-ab45-42fa-8dde-9f2d35136f07\" parallelMultiple=\"false\" name=\"Boundary Event2\" id=\"462fbe06-d514-4387-8592-2acd0f6a0a5f\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"373\" y=\"103\" width=\"24\" height=\"24\" /><ns3:default>f2324e42-816f-420c-8247-62c22a708d72</ns3:default><ns3:postAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.taskId</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.system.step.task.id</ns15:to></ns3:postAssignment><ns3:postAssignment><ns15:from xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">tw.local.mailTo</ns15:from><ns15:to xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xsi:type=\"ns15:tFormalExpression\">\"\"</ns15:to></ns3:postAssignment></ns15:extensionElements><ns15:outgoing>f2324e42-816f-420c-8247-62c22a708d72</ns15:outgoing><ns15:timerEventDefinition id=\"613ceb42-45eb-44ef-8435-************\" eventImplId=\"819261af-c815-4817-85c4-22b850f6f31a\"><ns15:extensionElements><ns4:timerEventSettings><ns4:dateType>DueDate</ns4:dateType><ns4:relativeDirection>AfterDueDate</ns4:relativeDirection><ns4:relativeTime>0</ns4:relativeTime><ns4:relativeTimeResolution>Minutes</ns4:relativeTimeResolution><ns4:toleranceInterval>0</ns4:toleranceInterval><ns4:toleranceIntervalResolution>Minutes</ns4:toleranceIntervalResolution><ns4:useCalendar>false</ns4:useCalendar></ns4:timerEventSettings></ns15:extensionElements></ns15:timerEventDefinition></ns15:boundaryEvent><ns15:sequenceFlow sourceRef=\"c43302a8-b1fa-4fa9-805b-d2abed461cf9\" targetRef=\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\" name=\"To Send Escalation mail\" id=\"f88a6f6e-d92d-4874-8501-b54942aec83d\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:sequenceFlow sourceRef=\"462fbe06-d514-4387-8592-2acd0f6a0a5f\" targetRef=\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\" name=\"To Send Escalation mail\" id=\"f2324e42-816f-420c-8247-62c22a708d72\"><ns15:extensionElements><ns13:linkVisualInfo><ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation><ns13:targetPortLocation>topLeft</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>false</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:endEvent name=\"End Event\" id=\"c8b8dc76-b1ba-4d4e-8653-ea6dc4248654\"><ns15:extensionElements><ns13:nodeVisualInfo x=\"715\" y=\"45\" width=\"24\" height=\"24\" /></ns15:extensionElements><ns15:incoming>949eda48-8282-4104-8add-a859c1fa92f3</ns15:incoming></ns15:endEvent><ns15:sequenceFlow sourceRef=\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\" targetRef=\"c8b8dc76-b1ba-4d4e-8653-ea6dc4248654\" name=\"To End Event\" id=\"949eda48-8282-4104-8add-a859c1fa92f3\"><ns15:extensionElements><ns3:sequenceFlowImplementation sboSyncEnabled=\"true\" /><ns13:linkVisualInfo><ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation><ns13:targetPortLocation>leftCenter</ns13:targetPortLocation><ns13:showLabel>false</ns13:showLabel><ns13:showCoachControlLabel>false</ns13:showCoachControlLabel><ns13:labelPosition>0.0</ns13:labelPosition><ns13:saveExecutionContext>true</ns13:saveExecutionContext></ns13:linkVisualInfo><ns3:happySequence>true</ns3:happySequence></ns15:extensionElements></ns15:sequenceFlow><ns15:dataObject itemSubjectRef=\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\" isCollection=\"false\" name=\"parentPath\" id=\"f2304205-e593-453a-805b-8a8ffe5c8497\" /><ns15:resourceRole name=\"participantRef\" /><ns15:resourceRole name=\"businessDataParticipantRef\" /><ns15:resourceRole name=\"perfMetricParticipantRef\"><ns15:resourceRef>24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</ns15:resourceRef></ns15:resourceRole><ns15:resourceRole name=\"ownerTeamParticipantRef\"><ns15:resourceRef>24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ns15:resourceRef></ns15:resourceRole></ns15:process><ns15:interface name=\"ODC ReversalInterface\" id=\"_749e4b07-74c9-4dac-8864-d13deb52baf9\" /></ns15:definitions>\r\r\n", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"8adc5346-4dc4-48ea-88bc-847fe8c3e8bb\"],\"isInterrupting\":true,\"extensionElements\":{\"default\":[\"8adc5346-4dc4-48ea-88bc-847fe8c3e8bb\"],\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":39,\"y\":82,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"postAssignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isFirstTime\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"true\"]}}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"4d00b4e6-b345-483f-af99-b6815f8ebb6f\"},{\"incoming\":[\"8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":728,\"y\":128,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Approve\",\"declaredType\":\"endEvent\",\"id\":\"6869d68e-0a73-474c-a9da-f7c788cf4d26\"},{\"outgoing\":[\"1d1902fe-e246-4af8-843c-92b62fbe98d3\"],\"incoming\":[\"cc8203a1-7910-4b2f-8009-0eeb7a8e3387\",\"8adc5346-4dc4-48ea-88bc-847fe8c3e8bb\"],\"extensionElements\":{\"activityExtension\":[{\"conditionScript\":\"\",\"conditional\":false,\"transactionalBehavior\":\"NotSet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension\"}],\"nodeVisualInfo\":[{\"width\":95,\"x\":338,\"y\":59,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"subject\":\"Create ODC Reversal Request \\u2013  \\u0625\\u0646\\u0634\\u0627\\u0621 \\u0637\\u0644\\u0628 \\u0627\\u0639\\u0627\\u062f\\u0629 \\u0642\\u064a\\u062f \\u062a\\u062d\\u0635\\u064a\\u0644 \\u0645\\u0633\\u062a\\u0646\\u062f\\u0649 \\u062a\\u0635\\u062f\\u064a\\u0631 <#= tw.system.process.instanceId #>\",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"timeScheduleName\":\"NBEWork\",\"holidayScheduleName\":\"NBEHoliday\",\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"parseFloat(tw.epv.ODCCreationSLA.RACT01)\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"Africa\\/Cairo\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"UserTask\"],\"postAssignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isFirstTime\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"false\"]}}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"LastUser\",\"teamFilterService\":{\"dataInputAssociation\":[{\"targetRef\":\"2055.b95f0910-f183-4dab-9065-28297a14ceef\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"BPM_ODC_HUB_\\\"+tw.local.routingDetails.hubCode+\\\"_CU_MKR\\\"\"]}}]},{\"targetRef\":\"2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.hubCode\"]}}]},{\"targetRef\":\"2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"BPM_IDC_HUB_\\\"+tw.local.routingDetails.hubCode+\\\"_EXE_MKR\\\"\"]}}]}],\"serviceRef\":\"1.302e4a54-16ab-47b9-9b70-d933329c72e7\"}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"default\":\"1d1902fe-e246-4af8-843c-92b62fbe98d3\",\"name\":\"ACT01 Create ODC Reversal Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.602aae62-b023-4b72-8c17-f498f81897a8\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isFirstTime\"]}}]},{\"targetRef\":\"2055.0f07a9fc-f9d5-4131-8028-34ad2f6c6eed\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user.fullName\"]}}]},{\"targetRef\":\"2055.46373b11-9f39-48b9-8787-37bd5d89f78a\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails\"]}}]},{\"targetRef\":\"2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"cfc9c782-1e15-4918-8597-81c8a226db13\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.lastAction\"]}}],\"sourceRef\":[\"2055.1bba753e-260d-4827-8632-e837d4c62da9\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}],\"sourceRef\":[\"2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108\"]}],\"calledElement\":\"1.b9bf1811-6e1e-41a3-828b-39059fa168cb\"},{\"outgoing\":[\"d1c41520-7c04-4ae2-8052-46fb397d3f2a\"],\"incoming\":[\"1d1902fe-e246-4af8-843c-92b62fbe98d3\"],\"extensionElements\":{\"activityExtension\":[{\"conditionScript\":\"\",\"conditional\":false,\"transactionalBehavior\":\"NotSet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension\"}],\"nodeVisualInfo\":[{\"width\":95,\"x\":338,\"y\":45,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"subject\":\"Review ODC Reversal Request \\u2013 \\u0645\\u0631\\u0627\\u062c\\u0639\\u0629 \\u0637\\u0644\\u0628 \\u0627\\u0639\\u0627\\u062f\\u0629 \\u0642\\u064a\\u062f \\u062a\\u062d\\u0635\\u064a\\u0644 \\u0645\\u0633\\u062a\\u0646\\u062f\\u0649 \\u062a\\u0635\\u062f\\u064a\\u0631 \",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"timeScheduleName\":\"NBEWork\",\"holidayScheduleName\":\"NBEHoliday\",\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"parseFloat(tw.epv.ODCCreationSLA.RACT02)\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"Africa\\/Cairo\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"UserTask\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{\"dataInputAssociation\":[{\"targetRef\":\"2055.b95f0910-f183-4dab-9065-28297a14ceef\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"BPM_ODC_HUB_\\\"+tw.local.routingDetails.hubCode+\\\"_CU_CHKR\\\"\"]}}]},{\"targetRef\":\"2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.hubCode\"]}}]},{\"targetRef\":\"2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"BPM_IDC_HUB_\\\"+tw.local.routingDetails.hubCode+\\\"_EXE_CHKR\\\"\"]}}]}],\"serviceRef\":\"1.302e4a54-16ab-47b9-9b70-d933329c72e7\"}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"default\":\"d1c41520-7c04-4ae2-8052-46fb397d3f2a\",\"name\":\"ACT02 Review ODC Reversal Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.a82241b6-421c-44f4-83f7-f6d3d3d3a024\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.8b489eb0-ce3f-46d6-8896-60fbb2cd2288\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.lastAction\"]}}]},{\"targetRef\":\"2055.34b891a3-f943-4cf2-8753-3a356f300282\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"204734d8-ab45-42fa-8dde-9f2d35136f07\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.d49e3545-463a-4eb4-8273-1e61d066f668\"]}],\"calledElement\":\"1.c71c7233-23ac-4e5f-a241-09cd96502615\"},{\"targetRef\":\"204734d8-ab45-42fa-8dde-9f2d35136f07\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To ACT02 Review ODC Reversal Request\",\"declaredType\":\"sequenceFlow\",\"id\":\"1d1902fe-e246-4af8-843c-92b62fbe98d3\",\"sourceRef\":\"cfc9c782-1e15-4918-8597-81c8a226db13\"},{\"outgoing\":[\"8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3\",\"45efef31-a3f7-48df-8e5f-55d0bd5a1a21\",\"cc8203a1-7910-4b2f-8009-0eeb7a8e3387\"],\"incoming\":[\"d1c41520-7c04-4ae2-8052-46fb397d3f2a\"],\"default\":\"cc8203a1-7910-4b2f-8009-0eeb7a8e3387\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":532,\"y\":64,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Checker Decision Action\",\"declaredType\":\"exclusiveGateway\",\"id\":\"a66b788f-eb9d-4662-8d5a-15e1618e166e\"},{\"targetRef\":\"a66b788f-eb9d-4662-8d5a-15e1618e166e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Checker Decision Action\",\"declaredType\":\"sequenceFlow\",\"id\":\"d1c41520-7c04-4ae2-8052-46fb397d3f2a\",\"sourceRef\":\"204734d8-ab45-42fa-8dde-9f2d35136f07\"},{\"incoming\":[\"45efef31-a3f7-48df-8e5f-55d0bd5a1a21\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":729,\"y\":67,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Cancel\",\"declaredType\":\"endEvent\",\"id\":\"60aa4589-527a-4330-8c62-e27d874075b2\"},{\"targetRef\":\"60aa4589-527a-4330-8c62-e27d874075b2\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.cancelRequest\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"To Cancel\",\"declaredType\":\"sequenceFlow\",\"id\":\"45efef31-a3f7-48df-8e5f-55d0bd5a1a21\",\"sourceRef\":\"a66b788f-eb9d-4662-8d5a-15e1618e166e\"},{\"targetRef\":\"6869d68e-0a73-474c-a9da-f7c788cf4d26\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.approveRequest\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"To Approve\",\"declaredType\":\"sequenceFlow\",\"id\":\"8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3\",\"sourceRef\":\"a66b788f-eb9d-4662-8d5a-15e1618e166e\"},{\"targetRef\":\"cfc9c782-1e15-4918-8597-81c8a226db13\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}],\"happySequence\":[true]},\"name\":\"Back To initiator\",\"declaredType\":\"sequenceFlow\",\"id\":\"cc8203a1-7910-4b2f-8009-0eeb7a8e3387\",\"sourceRef\":\"a66b788f-eb9d-4662-8d5a-15e1618e166e\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"true\"}]},\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isFirstTime\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"862e24d6-e072-4653-8abd-681972b119f0\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"lastAction\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"159f0712-06fa-4aa8-8a5d-bef2234b0a9f\"},{\"targetRef\":\"cfc9c782-1e15-4918-8597-81c8a226db13\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To ODC Initiators\",\"declaredType\":\"sequenceFlow\",\"id\":\"8adc5346-4dc4-48ea-88bc-847fe8c3e8bb\",\"sourceRef\":\"4d00b4e6-b345-483f-af99-b6815f8ebb6f\"},{\"outgoing\":[\"949eda48-8282-4104-8add-a859c1fa92f3\"],\"incoming\":[\"f88a6f6e-d92d-4874-8501-b54942aec83d\",\"f2324e42-816f-420c-8247-62c22a708d72\"],\"extensionElements\":{\"activityExtension\":[{\"conditionScript\":\"\",\"conditional\":false,\"transactionalBehavior\":\"NotSet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension\"}],\"deleteTaskOnCompletion\":[true],\"nodeVisualInfo\":[{\"width\":95,\"x\":378,\"y\":22,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"userTaskSettings\":[{\"activityAssignmentType\":\"Lane\",\"activityWorkSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"holidayScheduleType\":0},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings\",\"activityPriority\":{\"type\":\"Priority\",\"priority\":\"Normal\"},\"activityDueDate\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"1\",\"timeOfDay\":\"00:00\"},\"timeZone\":{\"type\":\"TimeZone\",\"value\":\"(use default)\"},\"type\":\"TimeCalculation\"}}],\"activityType\":[\"ServiceTask\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"resourceRole\":[{\"teamAssignmentType\":\"Reference\",\"name\":\"Lane\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"teamAssignmentType\":\"Reference\",\"name\":\"Team\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer\",\"distribution\":\"None\",\"teamFilterService\":{}},{\"name\":\"Expert\",\"declaredType\":\"performer\"}],\"default\":\"949eda48-8282-4104-8add-a859c1fa92f3\",\"name\":\"Send Escalation mail\",\"dataInputAssociation\":[{\"targetRef\":\"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]}}]},{\"targetRef\":\"2055.9771d7e8-ca59-430e-8b1a-194cf04c1182\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailTo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\",\"calledElement\":\"1.d7acf968-6740-4e52-b037-2049466eeeb2\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"taskId\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"db3fa75c-e63e-443f-8487-98f335bea12e\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"mailTo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"4e5d0b49-dad8-4560-8d99-82f26b0daeb8\"},{\"parallelMultiple\":false,\"outgoing\":[\"f88a6f6e-d92d-4874-8501-b54942aec83d\"],\"eventDefinition\":[{\"extensionElements\":{\"timerEventSettings\":[{\"relativeTime\":\"0\",\"relativeTimeResolution\":\"Minutes\",\"dateType\":\"DueDate\",\"toleranceInterval\":\"0\",\"useCalendar\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings\",\"toleranceIntervalResolution\":\"Minutes\",\"relativeDirection\":\"AfterDueDate\"}]},\"declaredType\":\"timerEventDefinition\",\"id\":\"cb3ec0ad-00f1-4b8c-8e62-ac8b2c7b2945\",\"otherAttributes\":{\"eventImplId\":\"cfbc88db-07d4-47c8-816b-abf2a1695f2f\"}}],\"attachedToRef\":\"cfc9c782-1e15-4918-8597-81c8a226db13\",\"extensionElements\":{\"default\":[\"f88a6f6e-d92d-4874-8501-b54942aec83d\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":347,\"y\":117,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"postAssignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.step.task.id\"]}},{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailTo\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]},\"cancelActivity\":false,\"name\":\"Boundary Event1\",\"declaredType\":\"boundaryEvent\",\"id\":\"c43302a8-b1fa-4fa9-805b-d2abed461cf9\"},{\"parallelMultiple\":false,\"outgoing\":[\"f2324e42-816f-420c-8247-62c22a708d72\"],\"eventDefinition\":[{\"extensionElements\":{\"timerEventSettings\":[{\"relativeTime\":\"0\",\"relativeTimeResolution\":\"Minutes\",\"dateType\":\"DueDate\",\"toleranceInterval\":\"0\",\"useCalendar\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings\",\"toleranceIntervalResolution\":\"Minutes\",\"relativeDirection\":\"AfterDueDate\"}]},\"declaredType\":\"timerEventDefinition\",\"id\":\"613ceb42-45eb-44ef-8435-************\",\"otherAttributes\":{\"eventImplId\":\"819261af-c815-4817-85c4-22b850f6f31a\"}}],\"attachedToRef\":\"204734d8-ab45-42fa-8dde-9f2d35136f07\",\"extensionElements\":{\"default\":[\"f2324e42-816f-420c-8247-62c22a708d72\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":373,\"y\":103,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"postAssignment\":[{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.step.task.id\"]}},{\"from\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailTo\"]},\"declaredType\":\"TAssignment\",\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]},\"cancelActivity\":false,\"name\":\"Boundary Event2\",\"declaredType\":\"boundaryEvent\",\"id\":\"462fbe06-d514-4387-8592-2acd0f6a0a5f\"},{\"targetRef\":\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Send Escalation mail\",\"declaredType\":\"sequenceFlow\",\"id\":\"f88a6f6e-d92d-4874-8501-b54942aec83d\",\"sourceRef\":\"c43302a8-b1fa-4fa9-805b-d2abed461cf9\"},{\"targetRef\":\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To Send Escalation mail\",\"declaredType\":\"sequenceFlow\",\"id\":\"f2324e42-816f-420c-8247-62c22a708d72\",\"sourceRef\":\"462fbe06-d514-4387-8592-2acd0f6a0a5f\"},{\"incoming\":[\"949eda48-8282-4104-8add-a859c1fa92f3\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":715,\"y\":45,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"c8b8dc76-b1ba-4d4e-8653-ea6dc4248654\"},{\"targetRef\":\"c8b8dc76-b1ba-4d4e-8653-ea6dc4248654\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}],\"happySequence\":[true]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"949eda48-8282-4104-8add-a859c1fa92f3\",\"sourceRef\":\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"f2304205-e593-453a-805b-8a8ffe5c8497\"}],\"laneSet\":[{\"id\":\"320b2a03-2ef6-48f6-9980-49703310afb3\",\"lane\":[{\"flowNodeRef\":[\"4d00b4e6-b345-483f-af99-b6815f8ebb6f\",\"cfc9c782-1e15-4918-8597-81c8a226db13\",\"c43302a8-b1fa-4fa9-805b-d2abed461cf9\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":200}]},\"name\":\"Hub \\/ center Maker\",\"partitionElementRef\":\"24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce\",\"declaredType\":\"lane\",\"id\":\"2e4a3c95-9839-4c37-870f-b0215a22597b\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"false\"}},{\"flowNodeRef\":[\"204734d8-ab45-42fa-8dde-9f2d35136f07\",\"a66b788f-eb9d-4662-8d5a-15e1618e166e\",\"6869d68e-0a73-474c-a9da-f7c788cf4d26\",\"60aa4589-527a-4330-8c62-e27d874075b2\",\"462fbe06-d514-4387-8592-2acd0f6a0a5f\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":201,\"declaredType\":\"TNodeVisualInfo\",\"height\":200}]},\"name\":\"Hub \\/ center Checker\",\"partitionElementRef\":\"24.8e005024-3fe0-4848-8c3c-f1e9483900c6\",\"declaredType\":\"lane\",\"id\":\"7e6e9014-5f8d-44eb-8ceb-ecba6505368c\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"false\"}},{\"flowNodeRef\":[\"956dcbec-d5f4-4a3d-8f90-86bed3f21e33\",\"c8b8dc76-b1ba-4d4e-8653-ea6dc4248654\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":402,\"declaredType\":\"TNodeVisualInfo\",\"height\":200}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"9180d59b-6f02-4dad-a33f-d017ed000296\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"resourceRole\":[{\"name\":\"participantRef\",\"declaredType\":\"resourceRole\"},{\"name\":\"businessDataParticipantRef\",\"declaredType\":\"resourceRole\"},{\"resourceRef\":\"24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6\",\"name\":\"perfMetricParticipantRef\",\"declaredType\":\"resourceRole\"},{\"resourceRef\":\"24.48d9e8e1-2b51-4173-ac71-9a6c533d134e\",\"name\":\"ownerTeamParticipantRef\",\"declaredType\":\"resourceRole\"}],\"isClosed\":false,\"extensionElements\":{\"bpdExtension\":[{\"allowContentOperations\":false,\"enableTracking\":false,\"workSchedule\":{\"timezoneType\":0,\"timeScheduleType\":0,\"timezone\":\"Egypt\",\"timeScheduleName\":\"NBEWork\",\"holidayScheduleName\":\"NBEHoliday\",\"holidayScheduleType\":0},\"instanceName\":\"\\\" Request Type: \\\"+ tw.local.odcRequest.requestType.name  +\\\" , \\\"+\\\" CIF: \\\"+ tw.local.odcRequest.cif +\\\" , \\\" +\\\"Request Number: \\\" +tw.system.process.instanceId\",\"dueDateSettings\":{\"dueDate\":{\"unit\":\"Hours\",\"value\":\"8\",\"timeOfDay\":\"00:00\"},\"type\":\"TimeCalculation\"},\"autoTrackingName\":\"at1693545399110\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension\",\"optimizeExecForLatency\":false,\"dueDateEnabled\":false,\"atRiskCalcEnabled\":false,\"allowProjectedPathManagement\":false,\"autoTrackingEnabled\":false,\"sboSyncEnabled\":true}],\"caseExtension\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension\",\"caseFolder\":{\"allowSubfoldersCreation\":false,\"allowLocalDoc\":false,\"id\":\"5aba4da2-4484-4810-835b-d5873189e7d7\",\"allowExternalFolder\":false,\"allowExternalDoc\":false}}],\"isConvergedProcess\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"ODC Reversal \\u0627\\u0639\\u0627\\u062f\\u0629 \\u0642\\u064a\\u062f \\u062a\\u062d\\u0635\\u064a\\u0644 \\u0645\\u0633\\u062a\\u0646\\u062f\\u0649 \\u062a\\u0635\\u062f\\u064a\\u0631\",\"declaredType\":\"process\",\"id\":\"25.933ae173-470f-4c1f-8a2b-6521da495a38\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"longRunning\"},\"ioSpecification\":{\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"1e68ed8f-13f7-4cb5-806c-8d8636ef2b03\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.dbbaa047-8f02-4397-b1b5-41f11b0256b3\",\"epvProcessLinkId\":\"014acb69-59a8-45e7-822c-05953bccc3e0\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{},{\"id\":\"_33e6e276-9f24-47a5-8f75-c1819ec07f1d\"}],\"outputSet\":[{},{\"id\":\"_5b5b1b2e-cab6-4f6f-96d2-c0d0f82c82e8\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject\"}],\"searchableField\":[{\"path\":\".initiator\",\"alias\":\"InitiatorHubBranch\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"7b6a22dc-233a-4ecd-8cbb-ee6921af4adc\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".parentRequestNo\",\"alias\":\"parentRequestNumber\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"f8755e6e-8272-431b-82c8-e9f1e7ed15ce\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".requestDate\",\"alias\":\"requestDate\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"7eb6d0f9-71e2-4496-86ce-d833141978e6\",\"type\":\"12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\"},{\"path\":\".appInfo.status\",\"alias\":\"requestStatus\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"a339ecd6-4974-4376-8f65-0fcf632e8222\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".appInfo.initiator\",\"alias\":\"InitiatorUserName\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"cbf1ac69-8dd1-4ef4-8633-6640a9052e85\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".CustomerInfo.cif\",\"alias\":\"customerCIF\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"e19ec9a2-b0e8-430d-89e9-a631fd4af208\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".customerName\",\"alias\":\"customerName\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"83c8ec89-336a-4889-8831-2db11de97e8d\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"},{\"path\":\".CustomerInfo.customerName\",\"alias\":\"customerName_1\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField\",\"id\":\"891a5bad-505c-4409-8a7d-e7068726a11a\",\"type\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2007.86f87789-824f-4930-8c19-5a719284c659\"},{\"itemSubjectRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"name\":\"routingDetails\",\"isCollection\":false,\"id\":\"2007.684c7152-3e6b-4505-8f37-4ed21a611236\"}]}},{\"name\":\"ODC ReversalInterface\",\"declaredType\":\"interface\",\"id\":\"_749e4b07-74c9-4dac-8864-d13deb52baf9\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"fe1ac9f6-647e-47c2-9e22-608f9d73ef7a\"}", "migrationData": {"isNull": "true"}, "rwfData": {"isNull": "true"}, "rwfStatus": {"isNull": "true"}, "templateId": {"isNull": "true"}, "externalId": {"isNull": "true"}, "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:6750", "versionId": "********-b951-4eed-b5a7-b9c8ff257a6c", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "BusinessProcessDiagram": {"id": "bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-151", "metadata": {"entry": {"key": "SAP_META.SHOULDRECOVER", "value": "no"}}, "name": ["ODC Reversal اعادة قيد تحصيل مستندى تصدير", "ODC Reversal اعادة قيد تحصيل مستندى تصدير"], "documentation": "", "dimension": {"size": {"w": "600", "h": "150"}}, "author": "mohamed.reda", "isTrackingEnabled": "false", "isCriticalPathEnabled": "false", "isSpcEnabled": "false", "isDueDateEnabled": "false", "isAtRiskCalcEnabled": "false", "creationDate": "1693545399117", "modificationDate": "1702815828398", "perfMetricParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6", "ownerTeamParticipantRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e", "metricSettings": {"itemType": "2"}, "instanceNameExpression": "\" Request Type: \"+ tw.local.odcRequest.requestType.name  +\" , \"+\" CIF: \"+ tw.local.odcRequest.cif +\" , \" +\"Request Number: \" +tw.system.process.instanceId", "dueDateType": "2", "dueDateTime": "8", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "dueDateCustom": "", "officeIntegration": {"sharePointParentSiteDisabled": "true", "sharePointParentSiteName": "<#= tw.system.process.name #>", "sharePointParentSiteTemplate": "ParentSiteTemplate.stp", "sharePointWorkspaceSiteName": "<#= tw.system.process.name #> <#= tw.system.process.instanceId #>", "sharePointWorkspaceSiteDescription": "This site has been automatically generated for managing collaborations and documents \r\nfor the Lombardi TeamWorks process instance: <#= tw.system.process.name #> <#= tw.system.process.instanceId #>\r\n\r\nTeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=<#= tw.system.process.instanceId #>\r\n\r\n", "sharePointWorkspaceSiteTemplate": "WorkspaceSiteTemplate.stp", "sharePointLCID": "1033"}, "timeScheduleType": "0", "timeScheduleName": "NBEWork", "holidayScheduleName": "NBEHoliday", "holidayScheduleType": "0", "timezone": "Egypt", "timezoneType": "0", "executionProfile": "default", "isSBOSyncEnabled": "true", "allowContentOperations": "false", "isLegacyCaseMigrated": "false", "hasCaseObjectParams": "false", "defaultPool": {"BpmnObjectId": {"id": "320b2a03-2ef6-48f6-9980-49703310afb3"}}, "defaultInstanceUI": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7893"}, "ownerTeamInstanceUI": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7894"}, "simulationScenario": {"id": "bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:6763", "name": "<PERSON><PERSON><PERSON>", "simNumInstances": "100", "simMinutesBetween": "30", "maxInstances": "100", "useMaxInstances": "true", "continueFromReal": "false", "useParticipantCalendars": "false", "useDuration": "false", "duration": "86400", "startTime": "1693545399551"}, "flow": [{"id": "1d1902fe-e246-4af8-843c-92b62fbe98d3", "connectionType": "SequenceFlow", "name": "To ACT02 Review ODC Reversal Request", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7845"}}}, {"id": "cc8203a1-7910-4b2f-8009-0eeb7a8e3387", "connectionType": "SequenceFlow", "name": "Back To initiator", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784f", "expression": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions."}}}, {"id": "45efef31-a3f7-48df-8e5f-55d0bd5a1a21", "connectionType": "SequenceFlow", "name": "To Cancel", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7853", "expression": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest"}}}, {"id": "f88a6f6e-d92d-4874-8501-b54942aec83d", "connectionType": "SequenceFlow", "name": "To Send Escalation mail", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7844"}}}, {"id": "d1c41520-7c04-4ae2-8052-46fb397d3f2a", "connectionType": "SequenceFlow", "name": "To Checker Decision Action", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7843"}}}, {"id": "8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3", "connectionType": "SequenceFlow", "name": "To Approve", "documentation": "", "nameVisible": "true", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7851", "expression": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.approveRequest"}}}, {"id": "8adc5346-4dc4-48ea-88bc-847fe8c3e8bb", "connectionType": "SequenceFlow", "name": "To ODC Initiators", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7842"}}}, {"id": "949eda48-8282-4104-8add-a859c1fa92f3", "connectionType": "SequenceFlow", "name": "To End Event", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7841"}}}, {"id": "f2324e42-816f-420c-8247-62c22a708d72", "connectionType": "SequenceFlow", "name": "To Send Escalation mail", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7840"}}}], "pool": {"id": "320b2a03-2ef6-48f6-9980-49703310afb3", "name": "Pool", "documentation": "", "restrictedName": "at1693545399110", "dimension": {"size": {"w": "3000", "h": "600"}}, "autoTrackingEnabled": "false", "lane": [{"id": "2e4a3c95-9839-4c37-870f-b0215a22597b", "name": "Hub / center Maker", "height": "200", "laneColor": "0", "systemLane": "false", "attachedParticipant": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce", "flowObject": [{"id": "cfc9c782-1e15-4918-8597-81c8a226db13", "componentType": "Activity", "name": "ACT01 Create ODC Reversal Request", "position": {"location": {"x": "338", "y": "59"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "1", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "1", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.b9bf1811-6e1e-41a3-828b-39059fa168cb", "sendToType": "1", "taskRouting": "3", "dueDateType": "1", "dueDateTime": "parseFloat(tw.epv.ODCCreationSLA.RACT01)", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "subject": "Create ODC Reversal Request –  إنشاء طلب اعادة قيد تحصيل مستندى تصدير <#= tw.system.process.instanceId #>", "forceSend": "true", "timeSchedule": "NBEWork", "timeScheduleType": "0", "timeZone": "Africa/Cairo", "timeZoneType": "0", "holidaySchedule": "NBEHoliday", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787e", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.602aae62-b023-4b72-8c17-f498f81897a8"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787d", "name": "isFirstTime", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "input": "true", "value": "tw.local.isFirstTime", "parameterId": "2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787c", "name": "routingDetails", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "input": "true", "value": "tw.local.routingDetails", "parameterId": "2055.46373b11-9f39-48b9-8787-37bd5d89f78a"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787b", "name": "parentPath", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.parentPath", "parameterId": "2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74"}], "outputActivityParameterMapping": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787a", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7879", "name": "lastAction", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.lastAction", "parameterId": "2055.1bba753e-260d-4827-8632-e837d4c62da9"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7878", "name": "parentPath", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.parentPath", "parameterId": "2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108"}], "laneFilter": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7877", "serviceType": "1", "teamRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce", "attachedActivityId": "/1.302e4a54-16ab-47b9-9b70-d933329c72e7", "inputActivityParameterMapping": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7876", "name": "hubCode", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.routingDetails.hubCode", "parameterId": "2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7875", "name": "hubGroupName", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_MKR\"", "parameterId": "2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2"}]}, "teamFilter": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7874", "serviceType": "1"}}}, "inputPort": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7850", "positionId": "rightCenter", "input": "true", "flow": {"ref": "cc8203a1-7910-4b2f-8009-0eeb7a8e3387"}}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784d", "positionId": "leftCenter", "input": "true", "flow": {"ref": "8adc5346-4dc4-48ea-88bc-847fe8c3e8bb"}}], "outputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785b", "positionId": "bottomCenter", "flow": {"ref": "1d1902fe-e246-4af8-843c-92b62fbe98d3"}}, "assignment": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7880", "assignTime": "2", "to": "false", "from": "tw.local.isFirstTime"}, "attachedEvent": {"id": "c43302a8-b1fa-4fa9-805b-d2abed461cf9", "componentType": "Event", "name": "Boundary Event1", "documentation": "", "position": {"location": {"x": "0", "y": "0"}}, "positionId": "bottomLeft", "dropIconUrl": "0", "colorInput": "Color", "component": {"nameVisible": "true", "eventType": "3", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "false", "EventAction": {"id": "cb3ec0ad-00f1-4b8c-8e62-ac8b2c7b2945", "actionType": "2", "actionSubType": "0", "EventActionImplementation": {"id": "cfbc88db-07d4-47c8-816b-abf2a1695f2f", "dateType": "1", "relativeDirection": "1", "relativeTime": "0", "relativeTimeResolution": "0", "toleranceInterval": "0", "toleranceIntervalResolution": "0", "UseCalendar": "false"}}}, "outputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784c", "positionId": "bottomCenter", "flow": {"ref": "f88a6f6e-d92d-4874-8501-b54942aec83d"}}, "assignment": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7863", "assignTime": "2", "to": "tw.system.step.task.id", "from": "tw.local.taskId"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7862", "assignTime": "2", "to": "\"\"", "from": "tw.local.mailTo"}]}}, {"id": "4d00b4e6-b345-483f-af99-b6815f8ebb6f", "componentType": "Event", "name": "Start", "documentation": "", "position": {"location": {"x": "39", "y": "82"}}, "dropIconUrl": "0", "colorInput": "#F8F8F8", "component": {"nameVisible": "true", "eventType": "1", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "outputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784e", "positionId": "rightCenter", "flow": {"ref": "8adc5346-4dc4-48ea-88bc-847fe8c3e8bb"}}, "assignment": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7864", "assignTime": "2", "to": "true", "from": "tw.local.isFirstTime"}}]}, {"id": "7e6e9014-5f8d-44eb-8ceb-ecba6505368c", "name": "Hub / center Checker", "height": "200", "laneColor": "0", "systemLane": "false", "attachedParticipant": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6", "flowObject": [{"id": "204734d8-ab45-42fa-8dde-9f2d35136f07", "componentType": "Activity", "name": "ACT02 Review ODC Reversal Request", "position": {"location": {"x": "338", "y": "45"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "1", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "1", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.c71c7233-23ac-4e5f-a241-09cd96502615", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "parseFloat(tw.epv.ODCCreationSLA.RACT02)", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "subject": "Review ODC Reversal Request – مراجعة طلب اعادة قيد تحصيل مستندى تصدير ", "forceSend": "true", "timeSchedule": "NBEWork", "timeScheduleType": "0", "timeZone": "Africa/Cairo", "timeZoneType": "0", "holidaySchedule": "NBEHoliday", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7872", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.a82241b6-421c-44f4-83f7-f6d3d3d3a024"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7871", "name": "lastAction", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.lastAction", "parameterId": "2055.8b489eb0-ce3f-46d6-8896-60fbb2cd2288"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7870", "name": "parentPath", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.parentPath", "parameterId": "2055.34b891a3-f943-4cf2-8753-3a356f300282"}], "outputActivityParameterMapping": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786f", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.d49e3545-463a-4eb4-8273-1e61d066f668"}, "laneFilter": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786e", "serviceType": "1", "teamRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6", "attachedActivityId": "/1.302e4a54-16ab-47b9-9b70-d933329c72e7", "inputActivityParameterMapping": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786d", "name": "hubCode", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.routingDetails.hubCode", "parameterId": "2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786c", "name": "hubGroupName", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_CHKR\"", "parameterId": "2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2"}]}, "teamFilter": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786b", "serviceType": "1"}}}, "inputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785a", "positionId": "topCenter", "input": "true", "flow": {"ref": "1d1902fe-e246-4af8-843c-92b62fbe98d3"}}, "outputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7859", "positionId": "rightCenter", "flow": {"ref": "d1c41520-7c04-4ae2-8052-46fb397d3f2a"}}, "attachedEvent": {"id": "462fbe06-d514-4387-8592-2acd0f6a0a5f", "componentType": "Event", "name": "Boundary Event2", "documentation": "", "position": {"location": {"x": "0", "y": "0"}}, "positionId": "bottomCenter", "dropIconUrl": "0", "colorInput": "Color", "component": {"nameVisible": "true", "eventType": "3", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "false", "EventAction": {"id": "613ceb42-45eb-44ef-8435-************", "actionType": "2", "actionSubType": "0", "EventActionImplementation": {"id": "819261af-c815-4817-85c4-22b850f6f31a", "dateType": "1", "relativeDirection": "1", "relativeTime": "0", "relativeTimeResolution": "0", "toleranceInterval": "0", "toleranceIntervalResolution": "0", "UseCalendar": "false"}}}, "outputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784a", "positionId": "bottomCenter", "flow": {"ref": "f2324e42-816f-420c-8247-62c22a708d72"}}, "assignment": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785f", "assignTime": "2", "to": "tw.system.step.task.id", "from": "tw.local.taskId"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785e", "assignTime": "2", "to": "\"\"", "from": "tw.local.mailTo"}]}}, {"id": "6869d68e-0a73-474c-a9da-f7c788cf4d26", "componentType": "Event", "name": "Approve", "documentation": "", "position": {"location": {"x": "728", "y": "128"}}, "dropIconUrl": "0", "colorInput": "#F8F8F8", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7852", "positionId": "leftCenter", "input": "true", "flow": {"ref": "8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3"}}}, {"id": "60aa4589-527a-4330-8c62-e27d874075b2", "componentType": "Event", "name": "Cancel", "documentation": "", "position": {"location": {"x": "729", "y": "67"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7854", "positionId": "leftCenter", "input": "true", "flow": {"ref": "45efef31-a3f7-48df-8e5f-55d0bd5a1a21"}}}, {"id": "a66b788f-eb9d-4662-8d5a-15e1618e166e", "componentType": "Gateway", "name": "Checker Decision Action", "documentation": "", "position": {"location": {"x": "532", "y": "64"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "gatewayType": "1", "splitJoinType": "0"}, "inputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7858", "positionId": "leftCenter", "input": "true", "flow": {"ref": "d1c41520-7c04-4ae2-8052-46fb397d3f2a"}}, "outputPort": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7857", "positionId": "bottomCenter", "flow": {"ref": "8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3"}}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7856", "positionId": "rightCenter", "flow": {"ref": "45efef31-a3f7-48df-8e5f-55d0bd5a1a21"}}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7855", "positionId": "topCenter", "flow": {"ref": "cc8203a1-7910-4b2f-8009-0eeb7a8e3387"}}]}]}, {"id": "9180d59b-6f02-4dad-a33f-d017ed000296", "name": "System", "height": "200", "laneColor": "0", "systemLane": "true", "attachedParticipant": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "flowObject": [{"id": "956dcbec-d5f4-4a3d-8f90-86bed3f21e33", "componentType": "Activity", "name": "Send Escalation mail", "documentation": "Escalation mail service: <div>   this service is running when the task has delay to be done </div>", "position": {"location": {"x": "378", "y": "22"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "4", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "3", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.d7acf968-6740-4e52-b037-2049466eeeb2", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "1", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "forceSend": "true", "noTask": "true", "timeSchedule": "(use default)", "timeScheduleType": "0", "timeZone": "(use default)", "timeZoneType": "0", "holidaySchedule": "(use default)", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7869", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.odcRequest", "parameterId": "2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7868", "name": "taskId", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.taskId", "parameterId": "2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1"}], "laneFilter": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7866", "serviceType": "1", "teamRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b"}, "teamFilter": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7865", "serviceType": "1"}}}, "inputPort": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784b", "positionId": "leftCenter", "input": "true", "flow": {"ref": "f88a6f6e-d92d-4874-8501-b54942aec83d"}}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7849", "positionId": "topLeft", "input": "true", "flow": {"ref": "f2324e42-816f-420c-8247-62c22a708d72"}}], "outputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7848", "positionId": "rightCenter", "flow": {"ref": "949eda48-8282-4104-8add-a859c1fa92f3"}}}, {"id": "c8b8dc76-b1ba-4d4e-8653-ea6dc4248654", "componentType": "Event", "name": "End Event", "documentation": "", "position": {"location": {"x": "715", "y": "45"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7847", "positionId": "leftCenter", "input": "true", "flow": {"ref": "949eda48-8282-4104-8add-a859c1fa92f3"}}}]}], "inputParameter": [{"id": "86f87789-824f-4930-8c19-5a719284c659", "bpdParameterId": "2007.86f87789-824f-4930-8c19-5a719284c659", "isProcessInstanceCorrelator": "false"}, {"id": "684c7152-3e6b-4505-8f37-4ed21a611236", "bpdParameterId": "2007.684c7152-3e6b-4505-8f37-4ed21a611236", "isProcessInstanceCorrelator": "false"}], "privateVariable": [{"id": "862e24d6-e072-4653-8abd-681972b119f0", "name": "isFirstTime", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "arrayOf": "false", "hasDefault": "true", "defaultValue": "true", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}, {"id": "159f0712-06fa-4aa8-8a5d-bef2234b0a9f", "name": "lastAction", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayOf": "false", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}, {"id": "db3fa75c-e63e-443f-8487-98f335bea12e", "name": "taskId", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayOf": "false", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}, {"id": "4e5d0b49-dad8-4560-8d99-82f26b0daeb8", "name": "mailTo", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayOf": "false", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}, {"id": "f2304205-e593-453a-805b-8a8ffe5c8497", "name": "parentPath", "description": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayOf": "false", "hasDefault": "false", "visibleInSearch": "false", "isProcessInstanceCorrelator": "false", "isSharedContext": "false"}], "epv": [{"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7882", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3"}, {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7881", "epvId": "/21.dbbaa047-8f02-4397-b1b5-41f11b0256b3"}], "searchableField": [{"id": "7b6a22dc-233a-4ecd-8cbb-ee6921af4adc", "name": "InitiatorHubBranch", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4894", "expression": "tw.local.odcRequest.initiator"}}, {"id": "f8755e6e-8272-431b-82c8-e9f1e7ed15ce", "name": "parentRequestNumber", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4893", "expression": "tw.local.odcRequest.parentRequestNo"}}, {"id": "7eb6d0f9-71e2-4496-86ce-d833141978e6", "name": "requestDate", "type": "5", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4892", "expression": "tw.local.odcRequest.requestDate"}}, {"id": "a339ecd6-4974-4376-8f65-0fcf632e8222", "name": "requestStatus", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4891", "expression": "tw.local.odcRequest.appInfo.status"}}, {"id": "cbf1ac69-8dd1-4ef4-8633-6640a9052e85", "name": "InitiatorUserName", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-4890", "expression": "tw.local.odcRequest.appInfo.initiator"}}, {"id": "e19ec9a2-b0e8-430d-89e9-a631fd4af208", "name": "customerCIF", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-488f", "expression": "tw.local.odcRequest.CustomerInfo.cif"}}, {"id": "83c8ec89-336a-4889-8831-2db11de97e8d", "name": "customerName", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-488e", "expression": "tw.local.odcRequest.customerName"}}, {"id": "891a5bad-505c-4409-8a7d-e7068726a11a", "name": "customerName_1", "type": "2", "expression": {"id": "bpdid:a32442299065080d:748af990:1975177df1a:-488d", "expression": "tw.local.odcRequest.CustomerInfo.customerName"}}]}, "extension": {"id": "bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7846", "type": "CASE", "caseFolder": {"id": "5aba4da2-4484-4810-835b-d5873189e7d7", "allowLocalDoc": "false", "allowExternalDoc": "false", "allowSubfoldersCreation": "false", "allowExternalFolder": "false"}}}}}}}