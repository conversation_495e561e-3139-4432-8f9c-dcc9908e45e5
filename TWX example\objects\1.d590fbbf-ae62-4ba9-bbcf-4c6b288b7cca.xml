<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca" name="get search Criteria List">
        <lastModified>1697049915209</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.b9758410-f877-485c-84f1-bd6b1038e639</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e48</guid>
        <versionId>e3330927-d39f-472a-8e13-00fffb123dde</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:aff098473ecd546d:1d42df0a:18b1b2b8841:2b27" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"5a489bae-f3b2-4e4e-8483-811a8ce22eb7"},{"incoming":["264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e4a"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"6ac8db85-c4f0-4845-8436-04d472b352e4"},{"targetRef":"b9758410-f877-485c-84f1-bd6b1038e639","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22","sourceRef":"5a489bae-f3b2-4e4e-8483-811a8ce22eb7"},{"startQuantity":1,"outgoing":["264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee"],"incoming":["2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":181,"y":56,"declaredType":"TNodeVisualInfo","height":70}]},"name":"search Criteria","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b9758410-f877-485c-84f1-bd6b1038e639","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.search =new tw.object.listOf.NameValuePair();\r\n\r\ntw.local.search[0]= new tw.object.NameValuePair();\r\ntw.local.search[0].name = \"CIF\";\r\ntw.local.search[0].value = \"cif\";\r\n\r\ntw.local.search[1]= new tw.object.NameValuePair();\r\ntw.local.search[1].name = \"Request Type \u0646\u0648\u0639 \u0627\u0644\u0637\u0644\u0628\";\r\ntw.local.search[1].value = \"requestType\";\r\n\r\ntw.local.search[2]= new tw.object.NameValuePair();\r\ntw.local.search[2].name = \"BPM Request Number \u0631\u0642\u0645 \u0627\u0644\u0637\u0644\u0628\";\r\ntw.local.search[2].value = \"bpmRequestNumber\";\r\n\r\ntw.local.search[3]= new tw.object.NameValuePair();\r\ntw.local.search[3].name = \"Request Status \u062d\u0627\u0644\u0647 \u0627\u0644\u0637\u0644\u0628\";\r\ntw.local.search[3].value = \"requestStatus\";\r\n\r\ntw.local.search[4]= new tw.object.NameValuePair();\r\ntw.local.search[4].name = \"Request Date  \u062a\u0627\u0631\u064a\u062e \u0627\u0644\u0637\u0644\u0628\";\r\ntw.local.search[4].value = \"requestDate\";\r\n\r\ntw.local.search[5]= new tw.object.NameValuePair();\r\ntw.local.search[5].name = \"Invoice Number \u0631\u0642\u0645 \u0627\u0644\u0641\u0627\u062a\u0648\u0631\u0647\";\r\ntw.local.search[5].value = \"invoiceNumber\";\r\n\r\ntw.local.search[6]= new tw.object.NameValuePair();\r\ntw.local.search[6].name = \"Bill Of Lading Ref \u0631\u0642\u0645 \u0628\u0648\u0644\u064a\u0635\u0647 \u0627\u0644\u0634\u062d\u0646\";\r\ntw.local.search[6].value = \"billOfLading\";\r\n\r\ntw.local.search[7]= new tw.object.NameValuePair();\r\ntw.local.search[7].name = \"Trade Fo Reference Number \u0631\u0642\u0645 \u0645\u0648\u0627\u0641\u0642\u0629 \u0648\u062d\u062f\u0629 \u062a\u0645\u0648\u064a\u0644 \u0627\u0644\u062a\u062c\u0627\u0631\u0629\";\r\ntw.local.search[7].value = \"tradeFoRefNo\";\r\n\r\n\r\ntw.local.results = tw.local.search;\r\n\r\n\r\n"]}},{"targetRef":"6ac8db85-c4f0-4845-8436-04d472b352e4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee","sourceRef":"b9758410-f877-485c-84f1-bd6b1038e639"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"search","isCollection":true,"declaredType":"dataObject","id":"2056.3374f806-1875-4fde-88d1-53473372df47"}],"laneSet":[{"id":"a0f94208-8cab-4a77-8604-8d94bafcc9a0","lane":[{"flowNodeRef":["5a489bae-f3b2-4e4e-8483-811a8ce22eb7","6ac8db85-c4f0-4845-8436-04d472b352e4","b9758410-f877-485c-84f1-bd6b1038e639"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"43e2c23e-ca8e-4c67-83ed-e9158ecae48f","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"get search Criteria List","declaredType":"process","id":"1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.8683896a-48aa-4699-8f02-f8e2b33f6489"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.0ac53667-a1c4-4086-b17a-f78b72a6299a","epvProcessLinkId":"2c9f4115-c436-4fb5-8b7e-05670e498122","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.5ba0f331-49f6-4a09-818d-80878e8bb52d"]}],"outputSet":[{"dataOutputRefs":["2055.8683896a-48aa-4699-8f02-f8e2b33f6489"]}],"dataInput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.5ba0f331-49f6-4a09-818d-80878e8bb52d"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5ba0f331-49f6-4a09-818d-80878e8bb52d</processParameterId>
            <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>90e8de49-8852-4b7a-a66a-60a0162d0244</guid>
            <versionId>8f598dd3-debd-43f7-863a-7e52178a6c97</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.8683896a-48aa-4699-8f02-f8e2b33f6489</processParameterId>
            <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4b8ec363-8cc7-401c-b214-7b3c622025cf</guid>
            <versionId>41c9253c-41fb-4a47-9d87-0d4b92987288</versionId>
        </processParameter>
        <processVariable name="search">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3374f806-1875-4fde-88d1-53473372df47</processVariableId>
            <description isNull="true" />
            <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0adc1cb6-a19e-4d3d-a244-fab8bad7e291</guid>
            <versionId>ea981b3c-0e2b-44e2-8d7a-7e0ddafc487c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6ac8db85-c4f0-4845-8436-04d472b352e4</processItemId>
            <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.05f60301-46b8-487c-9f95-9b12c9562c36</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e4a</guid>
            <versionId>8494410d-926a-419c-9820-d0939db7a55d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.05f60301-46b8-487c-9f95-9b12c9562c36</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2fcea4f2-00cc-49af-8730-30735f9812ed</guid>
                <versionId>21ed9dc2-55fb-45fd-b8b1-c03d96539bd5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b9758410-f877-485c-84f1-bd6b1038e639</processItemId>
            <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
            <name>search Criteria</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.25147c16-173d-419a-95ef-7f3d30dac30f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e53</guid>
            <versionId>e19d88e4-9482-4024-a6c4-6f4babc2ecff</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="181" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.25147c16-173d-419a-95ef-7f3d30dac30f</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.search =new tw.object.listOf.NameValuePair();&#xD;
&#xD;
tw.local.search[0]= new tw.object.NameValuePair();&#xD;
tw.local.search[0].name = "CIF";&#xD;
tw.local.search[0].value = "cif";&#xD;
&#xD;
tw.local.search[1]= new tw.object.NameValuePair();&#xD;
tw.local.search[1].name = "Request Type نوع الطلب";&#xD;
tw.local.search[1].value = "requestType";&#xD;
&#xD;
tw.local.search[2]= new tw.object.NameValuePair();&#xD;
tw.local.search[2].name = "BPM Request Number رقم الطلب";&#xD;
tw.local.search[2].value = "bpmRequestNumber";&#xD;
&#xD;
tw.local.search[3]= new tw.object.NameValuePair();&#xD;
tw.local.search[3].name = "Request Status حاله الطلب";&#xD;
tw.local.search[3].value = "requestStatus";&#xD;
&#xD;
tw.local.search[4]= new tw.object.NameValuePair();&#xD;
tw.local.search[4].name = "Request Date  تاريخ الطلب";&#xD;
tw.local.search[4].value = "requestDate";&#xD;
&#xD;
tw.local.search[5]= new tw.object.NameValuePair();&#xD;
tw.local.search[5].name = "Invoice Number رقم الفاتوره";&#xD;
tw.local.search[5].value = "invoiceNumber";&#xD;
&#xD;
tw.local.search[6]= new tw.object.NameValuePair();&#xD;
tw.local.search[6].name = "Bill Of Lading Ref رقم بوليصه الشحن";&#xD;
tw.local.search[6].value = "billOfLading";&#xD;
&#xD;
tw.local.search[7]= new tw.object.NameValuePair();&#xD;
tw.local.search[7].name = "Trade Fo Reference Number رقم موافقة وحدة تمويل التجارة";&#xD;
tw.local.search[7].value = "tradeFoRefNo";&#xD;
&#xD;
&#xD;
tw.local.results = tw.local.search;&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>4eeb166c-2f1b-4c0b-9cc6-871ea96eb8a8</guid>
                <versionId>f7c233c1-9b0f-4b7c-995a-************</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.12b60f4f-1f32-472e-983d-174f0b813f7e</epvProcessLinkId>
            <epvId>/21.0ac53667-a1c4-4086-b17a-f78b72a6299a</epvId>
            <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
            <guid>a8de7c47-021a-46ba-875b-45f7e45eccb3</guid>
            <versionId>af2119a4-f4fe-4d57-8714-99334bc15c66</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.b9758410-f877-485c-84f1-bd6b1038e639</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="get search Criteria List" id="1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.0ac53667-a1c4-4086-b17a-f78b72a6299a" epvProcessLinkId="2c9f4115-c436-4fb5-8b7e-05670e498122" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.5ba0f331-49f6-4a09-818d-80878e8bb52d" />
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.8683896a-48aa-4699-8f02-f8e2b33f6489" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.5ba0f331-49f6-4a09-818d-80878e8bb52d</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.8683896a-48aa-4699-8f02-f8e2b33f6489</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="a0f94208-8cab-4a77-8604-8d94bafcc9a0">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="43e2c23e-ca8e-4c67-83ed-e9158ecae48f" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>5a489bae-f3b2-4e4e-8483-811a8ce22eb7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6ac8db85-c4f0-4845-8436-04d472b352e4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b9758410-f877-485c-84f1-bd6b1038e639</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="5a489bae-f3b2-4e4e-8483-811a8ce22eb7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="6ac8db85-c4f0-4845-8436-04d472b352e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:6e4a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="5a489bae-f3b2-4e4e-8483-811a8ce22eb7" targetRef="b9758410-f877-485c-84f1-bd6b1038e639" name="To End" id="2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="search Criteria" id="b9758410-f877-485c-84f1-bd6b1038e639">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="181" y="56" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.fb78a29b-99fa-4820-8210-7c81aaaf3a22</ns16:incoming>
                        
                        
                        <ns16:outgoing>264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.search =new tw.object.listOf.NameValuePair();&#xD;
&#xD;
tw.local.search[0]= new tw.object.NameValuePair();&#xD;
tw.local.search[0].name = "CIF";&#xD;
tw.local.search[0].value = "cif";&#xD;
&#xD;
tw.local.search[1]= new tw.object.NameValuePair();&#xD;
tw.local.search[1].name = "Request Type نوع الطلب";&#xD;
tw.local.search[1].value = "requestType";&#xD;
&#xD;
tw.local.search[2]= new tw.object.NameValuePair();&#xD;
tw.local.search[2].name = "BPM Request Number رقم الطلب";&#xD;
tw.local.search[2].value = "bpmRequestNumber";&#xD;
&#xD;
tw.local.search[3]= new tw.object.NameValuePair();&#xD;
tw.local.search[3].name = "Request Status حاله الطلب";&#xD;
tw.local.search[3].value = "requestStatus";&#xD;
&#xD;
tw.local.search[4]= new tw.object.NameValuePair();&#xD;
tw.local.search[4].name = "Request Date  تاريخ الطلب";&#xD;
tw.local.search[4].value = "requestDate";&#xD;
&#xD;
tw.local.search[5]= new tw.object.NameValuePair();&#xD;
tw.local.search[5].name = "Invoice Number رقم الفاتوره";&#xD;
tw.local.search[5].value = "invoiceNumber";&#xD;
&#xD;
tw.local.search[6]= new tw.object.NameValuePair();&#xD;
tw.local.search[6].name = "Bill Of Lading Ref رقم بوليصه الشحن";&#xD;
tw.local.search[6].value = "billOfLading";&#xD;
&#xD;
tw.local.search[7]= new tw.object.NameValuePair();&#xD;
tw.local.search[7].name = "Trade Fo Reference Number رقم موافقة وحدة تمويل التجارة";&#xD;
tw.local.search[7].value = "tradeFoRefNo";&#xD;
&#xD;
&#xD;
tw.local.results = tw.local.search;&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b9758410-f877-485c-84f1-bd6b1038e639" targetRef="6ac8db85-c4f0-4845-8436-04d472b352e4" name="To End" id="264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="search" id="2056.3374f806-1875-4fde-88d1-53473372df47" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.264faa2b-cc7e-4ef6-8d7c-0f0ed65ebcee</processLinkId>
            <processId>1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b9758410-f877-485c-84f1-bd6b1038e639</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6ac8db85-c4f0-4845-8436-04d472b352e4</toProcessItemId>
            <guid>4b9983bf-3451-4bcc-8841-77e4e1513a8d</guid>
            <versionId>4c3106bf-d6ca-4509-8d62-7eda9ff0a473</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b9758410-f877-485c-84f1-bd6b1038e639</fromProcessItemId>
            <toProcessItemId>2025.6ac8db85-c4f0-4845-8436-04d472b352e4</toProcessItemId>
        </link>
    </process>
</teamworks>

