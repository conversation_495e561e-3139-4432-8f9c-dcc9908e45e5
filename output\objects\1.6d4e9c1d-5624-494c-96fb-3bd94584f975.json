{"id": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "versionId": "70fbc4e8-bbe0-438f-a2d2-24bceb75bd61", "name": "Act05 - ODC Execution Hub – Initiation Review", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "regeneratedRemittanceLetterTitleVIS", "hasDefault": false, "type": "1"}, {"name": "lastAction", "hasDefault": false, "type": "1"}, {"name": "parentPath", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}, {"name": "fromExechecker", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "errorPanelVIS", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "deliveryTerms", "hasDefault": false}, {"name": "paymentTerms", "hasDefault": false}, {"name": "specialInstructions", "hasDefault": false}, {"name": "instructions", "hasDefault": false}, {"name": "requestTypeVIS", "hasDefault": false}, {"name": "parentRequestNoVIS", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "bankRefVIS", "hasDefault": false}, {"name": "multiTenorDatesVIS", "hasDefault": false}, {"name": "terminationReasonVIS", "hasDefault": false}, {"name": "error", "hasDefault": false}, {"name": "requestIdStr", "hasDefault": false}, {"name": "addressBICList", "hasDefault": false}, {"name": "partyTypeName", "hasDefault": false}, {"name": "customerFullDetails", "hasDefault": false}, {"name": "index", "hasDefault": false}, {"name": "calculatedChangeAmnt", "hasDefault": false}, {"name": "exRate", "hasDefault": false}, {"name": "todayDate", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "ACT05", "id": "2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Update request state and status in DB", "id": "2025.bdb6c456-66a6-432e-8861-99065124f8df", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit ODC Request", "id": "2025.********-72f3-46ed-8fed-7236b2c2451f", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit Request History", "id": "2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Cancel and Delete Transactions", "id": "2025.ecfb199d-d6ea-4243-8c36-180ca040be95", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel request", "id": "2025.37b03ea4-b507-4006-8d61-bcdf1b904a87", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Exclusive Gateway", "id": "2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited", "id": "2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited?", "id": "2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "terminate?", "id": "2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Copy of Exclusive Gateway", "id": "2025.a15aa745-1422-4867-839a-4d4db1c1696c", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "Validation script", "id": "2025.641a0c30-7293-44e2-88fe-45f081e293b7", "script": "tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)\r\r\n\tmandatory(tw.local.odcRequest.stepLog.terminateReason, \"tw.local.odcRequest.stepLog.terminateReason\");\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length < len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n\t\r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Init Script", "id": "2025.cc1ecdab-a272-4b43-8a48-0488be71b66c", "script": "tw.local.odcRequest.stepLog = {};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT05;\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT05;\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT05;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.lastAction;\r\r\n\r\r\nif(tw.local.lastAction != tw.epv.CreationActions.terminateRequest)\r\r\n\ttw.local.terminationReasonVIS =\"None\";\r\r\nelse\r\r\n\ttw.local.terminationReasonVIS =\"Editable\";\r\r\n\r\r\n\r\r\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\r\r\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\r\r\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\r\r\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\r\r\n\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\n//Parent Request Number\r\r\nif(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest){\r\r\n\ttw.local.parentRequestNoVIS = \"Readonly\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\n//Contract Stage\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\n/*Document Generation Section*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.requestTypeVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.requestTypeVIS = \"NONE\";\t\r\r\n}\r\r\n/*Importer Details Visibility*/\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.bankRefVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.bankRefVIS = \"NONE\";\t\r\r\n}\r\r\n/* Financial Detais Trade Fo*/\r\r\nif(tw.local.odcRequest.BasicDetails.paymentTerms.name == \"001\")\r\r\n{\r\r\n\ttw.local.multiTenorDatesVIS = \"None\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.multiTenorDatesVIS = \"Readonly\";\r\r\n}\r\r\n\r\r\ntw.local.errorPanelVIS=\"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Setting Status and sub status", "id": "2025.6554c1b1-2d24-46de-8d7f-adca48b9b791", "script": "if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.authorize)\r\r\n{\r\r\n\tif( (tw.local.odcRequest.requestType.value == tw.epv.RequestType.Create || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment ||tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate) \r\r\n\t&& tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter == true)\r\r\n\t{\r\r\n\ttw.local.odcRequest.appInfo.status    = \" Completed\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Printing Documents for Customer\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.appInfo.status    = \"Completed\";\r\r\n\t\ttw.local.odcRequest.appInfo.subStatus = \"Completed\";\r\r\n\t\ttw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;\r\r\n\t}\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Initiation\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Trade Fo Review\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.obtainApprovals){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing \";\r\r\n}\r\r\n\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"Terminated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Terminated\";\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "Act05 - ODC Execution Hub – Initiation Review", "lastModified": "1700595174121", "lastModifiedBy": "heba", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.28156bcc-db35-451c-b58a-d8bfa74c5d8e", "2025.28156bcc-db35-451c-b58a-d8bfa74c5d8e"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "true", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "353efed5-03fc-46cd-b4c5-1ba50f06696a", "versionId": "70fbc4e8-bbe0-438f-a2d2-24bceb75bd61", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"mobileReady\":[true],\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.eb0577af-381e-45da-b582-253e20167664\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":120,\"y\":201,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"72a57c58-1959-4daf-9f06-f1b9170feb41\"},{\"outgoing\":[\"2027.71a6d402-3d7f-42ec-a438-ecb665ed4474\",\"2027.f4fc1f44-14ec-4fe6-8509-f7597d4dc37a\"],\"incoming\":[\"2027.fe407a1a-943e-4385-81f2-fdd0cd009af0\",\"2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":350,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"],\"preAssignmentScript\":[]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f138f6a9-e27a-413e-86bc-ddeb6d979ad2\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"569dfbbb-7e7c-408a-85bd-ad9a4c1499af\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cffdb4cc-b87d-4e77-8a7d-191bf3c42b63\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"44c8df6f-4cd9-4c09-8b9a-ed073dc673f5\",\"optionName\":\"basicDetailsVIS\",\"value\":\"\\\"READONLY\\\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2b736bbe-1f10-4cc2-8941-69da2e80f3d1\",\"optionName\":\"parentRequestNoVis\",\"value\":\"tw.local.parentRequestNoVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"528ec7d8-abab-43da-8df6-0629e5105f46\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"Readonly\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5d26d8ab-f2e1-4495-8b72-9dc9fdd29e88\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0cd58d74-2f94-4bbd-8e50-b73ad0c55082\",\"optionName\":\"contractStageVIS\",\"value\":\"tw.local.contractStageVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"403869b2-09dd-4375-80eb-6892e66337de\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"tw.local.multiTenorDatesVIS\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"7f199f4d-c7a9-497a-89b4-15228c4256c4\",\"version\":\"8550\"},{\"layoutItemId\":\"Document_Generation_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"346cb219-0124-426d-8fc0-9cf5e483c354\",\"optionName\":\"@label\",\"value\":\"Letter Generation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e9cd7085-34e9-4873-8116-6eb83f5b1115\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8836366c-f207-4a8a-8551-ba9e6a36955a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"de03e1fc-c428-40e5-837e-c252177483c0\",\"optionName\":\"documentGeneratonVIS\",\"value\":\"\\\"READONLY\\\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ad857c79-4a7e-4136-8153-196d171a5218\",\"optionName\":\"instructions\",\"value\":\"tw.local.instructions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f13c819e-71c9-4bd7-860c-758d8971bd7d\",\"optionName\":\"specialInstructions\",\"value\":\"tw.local.specialInstructions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e830dfd9-5ac2-4cf9-8ac9-3470be4d22dc\",\"optionName\":\"paymentTerms\",\"value\":\"tw.local.paymentTerms\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3da62dd3-da09-44bb-8183-817cadaeaa0c\",\"optionName\":\"deliveryTerms\",\"value\":\"tw.local.deliveryTerms\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1c45119a-c682-4cc8-8931-c6e779048e2d\",\"optionName\":\"requestTypeVIS\",\"value\":\"tw.local.requestTypeVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8e8094b3-2709-4969-820f-2e57675b5b84\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"54ebe88d-0143-439a-806c-072bd05fd822\",\"optionName\":\"regeneratedRemittanceLetterTitleVIS\",\"value\":\"tw.local.regeneratedRemittanceLetterTitleVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0d70b2d0-09d0-40cf-8f60-8f57c2654cc9\",\"optionName\":\"DocumentGenerationVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"721c49d0-363a-4020-83c6-1a2ff48b8122\",\"optionName\":\"remittanceLetterButton\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ddc93a75-24a1-46eb-81a7-35d180788b6a\",\"optionName\":\"documentGenerationVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.1d99aba8-195f-4eee-ba8c-a47926bc21e2\",\"binding\":\"tw.local.odcRequest.GeneratedDocumentInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"bf13348a-57c9-46f5-8b74-cba703b6aade\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4b7335dc-565a-4874-87fb-d6b5faf147d6\",\"optionName\":\"@label\",\"value\":\"Customer Info\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"12f7655f-8be9-42c7-82d8-fed7984dc3c2\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1c4ab8ed-1743-4726-8da8-92ad4490ee1c\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ff44b7eb-15c2-4e32-8c15-46cd3896ff42\",\"optionName\":\"customerInfoVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0e976027-c953-4109-8734-5a4123923a1b\",\"optionName\":\"requestTypeVis\",\"value\":\"\\\"READONLY\\\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6105ea32-783a-424a-8ace-15470ba86d92\",\"optionName\":\"listsVIS\",\"value\":\"READONLY\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"a1bb5a88-33a1-4a0c-8264-48202758152d\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"53c207e1-4e34-4e9f-826f-313f99a55eab\",\"optionName\":\"@label\",\"value\":\"Financial Details - Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ffcc6dc8-d25a-4f68-8c9e-7cc29724f213\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"69ba7164-e143-463f-87cb-765e8ed61190\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a0802725-4c63-4a8b-891d-a5cb1d5604b0\",\"optionName\":\"requestTradeFoVis\",\"value\":\"\\\"READONLY\\\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4c328eab-520b-4a0d-8e85-1627d1a57ab6\",\"optionName\":\"financialDetailsVis\",\"value\":\"\\\"READONLY\\\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"258a69b0-040d-45d4-8f22-997d10cf2717\",\"optionName\":\"fcCollectionVis\",\"value\":\"\\\"READONLY\\\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7125e955-3995-4da3-8ae8-87e4114a3924\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"25ccef02-1b87-4604-8763-bb72019b8f3a\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"7021f345-19f1-4c0c-8103-01315252d575\",\"version\":\"8550\"},{\"layoutItemId\":\"FC_Collections_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"783cb246-2d9d-4ddb-87cc-33e490a2fc35\",\"optionName\":\"@label\",\"value\":\"FC Collections\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4e21faf2-ce42-4a21-864e-80ccdda2e9eb\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8ed0576a-89e4-4fb5-82b5-206b1af02b43\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f410b7a5-4000-4443-817d-536ab90b2bf0\",\"optionName\":\"FCVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4c33a035-bcdf-4537-88ea-9a90c35a1d8e\",\"optionName\":\"addBtnVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5dce4ec4-b1a4-4a57-88b6-bce9779b7ebd\",\"optionName\":\"retrieveBtnVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"98b1beed-e637-4896-84a5-a154f803fb95\",\"optionName\":\"collectionCurrencyVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f493bbb0-8ddd-4902-815a-c2f099477a2b\",\"optionName\":\"negotiatedExchangeRateVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"*************-4b9d-8be6-3ab7a98e5924\",\"optionName\":\"activityType\",\"value\":\"read\"}],\"viewUUID\":\"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe\",\"binding\":\"tw.local.odcRequest.FcCollections\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"223910ad-acc4-4fa9-8355-84dbb81e27ef\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_FO_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3e6fd210-6f53-47fb-89fd-02f1065fd681\",\"optionName\":\"@label\",\"value\":\"Financial Details FO\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"21c61e1c-e927-439d-80f9-6e10a7ca8f1c\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"823c4dca-d9ba-43f8-8271-80d3ba6eb6a3\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d66b93b2-a6fd-449f-8cd7-0af835ce413f\",\"optionName\":\"financialDetailsFOVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a725b13c-c076-44a5-8df3-9b35e471b9b3\",\"optionName\":\"paymentTermsVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"af99d90e-d14e-4e76-8d12-27c76409e450\",\"optionName\":\"act3VIS\",\"value\":\"READONLY\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"563ed960-18aa-4cab-874b-2d9362e6edfa\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3abc8a36-e9e5-41c9-8c3d-d25e2a25447f\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"tw.local.multiTenorDatesVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"337da150-bf50-4054-88c1-49e953754129\",\"optionName\":\"documentAmount\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.documentAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ad5cf30d-96df-40d1-8499-a9fe6790e06b\",\"optionName\":\"amountAdvanced\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1ce513e5-79c6-4b45-8734-ed4407ec2372\",\"optionName\":\"todayDate\",\"value\":\"tw.local.todayDate\"}],\"viewUUID\":\"64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1\",\"binding\":\"tw.local.odcRequest.FinancialDetailsFO\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"39acdc23-0cb1-4b9b-8af9-055facf20675\",\"version\":\"8550\"},{\"layoutItemId\":\"Importer_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"036150e5-afe4-403d-8705-8a378cce2928\",\"optionName\":\"@label\",\"value\":\"Importer Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0f48ee69-fb3a-44f1-8fb4-ed372b91677e\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8d666992-ffa8-48b2-8785-4d8a0b5ef886\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"56c2e32a-6b19-4c71-8dd9-86e0e7b71782\",\"optionName\":\"importerDetailsVis\",\"value\":\"\\\"READONLY\\\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"********-5f41-4108-8203-1dd673310dbc\",\"optionName\":\"bankRefVIS\",\"value\":\"tw.local.bankRefVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b65aa889-3d9c-4e86-8563-0afdcccc7408\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6376cd1f-6820-4ad6-8922-41c4d62368ae\",\"optionName\":\"importerDetailsCVVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.0ff96fd8-0740-4d17-887e-a56c8ef7921b\",\"binding\":\"tw.local.odcRequest.ImporterDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"7ea29daf-f330-4c06-83d0-d09cb2294f05\",\"version\":\"8550\"},{\"layoutItemId\":\"Products_and_Shipment_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"35f475fd-171c-4029-882c-c135dcdbdae7\",\"optionName\":\"@label\",\"value\":\"Products \\/Shipment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"34d55ca0-0683-43dd-88cb-b775ba58ee68\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"deea6fd8-824d-4bcd-8b85-eb2eec425d4b\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e420d8fb-dd34-469a-83b7-2815146527f5\",\"optionName\":\"productsVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0c681230-2d2f-4793-8df9-7879a15fd523\",\"optionName\":\"shipmentVis\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.5c068fcf-**************-6bb898b38609\",\"binding\":\"tw.local.odcRequest.ProductShipmentDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"91393cb7-a80b-4654-8d44-aeb3361ddf58\",\"version\":\"8550\"},{\"layoutItemId\":\"Contract_Creation_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"78424390-c9f9-4180-832a-2945360b17d9\",\"optionName\":\"@label\",\"value\":\"Contract Creation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8d7d6f50-7e3e-4d8a-84d0-b20fc7464e9b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"283b2a37-5768-4b70-80c9-a47fb71fd141\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"51195ce9-767c-45d3-8481-5367bdf3e4be\",\"optionName\":\"contractCreationVis\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"54f294e6-26d0-491d-873f-cbcad6d148ce\",\"optionName\":\"nbeCollectableAmount\",\"value\":\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e1a51802-a34f-4917-8373-c2756aec09fe\",\"optionName\":\"bpmRequestNumber\",\"value\":\"tw.local.odcRequest.BasicDetails.flexCubeContractNo\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4a27d1d7-6935-4043-899c-7915c0181201\",\"optionName\":\"contractStage\",\"value\":\"tw.local.odcRequest.BasicDetails.contractStage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b557b54a-3405-4a52-8f34-0e8b5a3579d8\",\"optionName\":\"currency\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.currency.value\"}],\"viewUUID\":\"64.0f40f56d-733f-4bd5-916c-92ae7dccbb10\",\"binding\":\"tw.local.odcRequest.ContractCreation\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"e4c436c6-bde6-4ece-8195-69317186c81b\",\"version\":\"8550\"},{\"layoutItemId\":\"ODC_Parties_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1dc58519-040f-4063-833d-49d566346322\",\"optionName\":\"@label\",\"value\":\"Parties\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"748d67c3-e6e7-4fe8-8923-c6f60a6fb20b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cab5fe53-2658-45cd-8858-bfe9f9b9a18f\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c38de764-bacf-4e00-8817-246275c0ef0e\",\"optionName\":\"odcPartiesCVVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a6524159-5c85-4337-80d0-2a748d5d0a22\",\"optionName\":\"odcPartiesCVBTNVIS\",\"value\":\"None\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"db591f50-fcab-4709-816a-988d6bd9d5ec\",\"optionName\":\"retrieveCifInfo\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0a629296-ffee-44fa-881d-691bc8dc569e\",\"optionName\":\"isChecker\",\"value\":\"true\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"85245668-71d0-4253-8a53-64bd6350fd71\",\"optionName\":\"isReturned\",\"value\":\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"266aa38a-9349-4ecf-8833-4d1ac3f01ed1\",\"optionName\":\"addressBICList\",\"value\":\"tw.local.addressBICList[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"415025bf-0f42-4610-8755-ebb28b862227\",\"optionName\":\"partyTypeName\",\"value\":\"tw.local.partyTypeName\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5fa6b05b-602b-4ba5-8a47-7a6bc0ee7755\",\"optionName\":\"customerFullDetails\",\"value\":\"tw.local.customerFullDetails\"}],\"viewUUID\":\"64.3d608948-c1fa-4ac3-8d4b-6a42f90a5132\",\"binding\":\"tw.local.odcRequest.Parties\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"5cccde2b-0339-4e94-8920-45cd2821e324\",\"version\":\"8550\"},{\"layoutItemId\":\"Charges_And_Commissions_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8fee4f09-482d-4f48-818a-516c371ebc1d\",\"optionName\":\"@label\",\"value\":\"Charges &Commissions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4024180d-f533-4304-877c-1a846b968ad3\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8975c82b-f1bb-4426-84ec-36f8b52e7280\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6baa1542-76f3-4677-8439-8372bfd43ada\",\"optionName\":\"chargesAndCommVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4fe2efd7-a111-4df0-85b2-ce563b077cbf\",\"optionName\":\"btnVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"dbeccd3f-3a14-427a-8e48-685f1948edda\",\"optionName\":\"commissionSectionVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5eac6017-7391-4be4-8f43-36047375745f\",\"optionName\":\"amountCollectableByNBE\",\"value\":\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"63a08a22-4980-47ac-8c53-8aaca4964722\",\"optionName\":\"index\",\"value\":\"tw.local.index\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"22f0ee9e-3f63-410d-833b-88bd200d52b8\",\"optionName\":\"calculatedChangeAmnt\",\"value\":\"tw.local.calculatedChangeAmnt\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"25bd8fe4-d29a-4540-82ed-bd0b9a6f2a74\",\"optionName\":\"chargesCustomerAccountList\",\"value\":\"tw.local.odcRequest.customerAndPartyAccountList[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bc14e93b-832d-419c-8254-371abb231d14\",\"optionName\":\"exRate\",\"value\":\"tw.local.exRate\"}],\"viewUUID\":\"64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52\",\"binding\":\"tw.local.odcRequest.ChargesAndCommissions[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"90e3ba02-57ac-4d85-88ad-1f5f63a46ab9\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment_Comments_View1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"96edc205-324f-4941-8ed6-d3141ae5980c\",\"optionName\":\"@label\",\"value\":\"Attachment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"deb225d5-aae7-42dd-8f40-cc96b236244f\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97a68835-5a68-4cff-8473-7353d6e553d1\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b04ce35d-a50b-4cc7-8f02-9314ee8673a9\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"07fb3834-54f4-422d-8aad-ab130810af0c\",\"optionName\":\"canUpdate\",\"value\":\"false\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f6acd6a0-609c-4413-8607-ef20c0c9a74c\",\"optionName\":\"remittanceLetterPath\",\"value\":\"tw.local.odcRequest.folderPath\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f5bd2e4d-**************-3776041b1aca\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d3883651-8f97-4c3e-84c4-da71c0036c56\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0acbd55c-6512-435f-8e8c-b00211185c35\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d53f1d55-6aaf-455d-8031-c9c2cb582514\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"96fc0cf4-7a48-4343-8803-d4a943144702\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"4f59508d-9e4b-4845-8f49-4b3cbefe2edf\"}],\"layoutItemId\":\"Tab_Section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2f8fccd3-9879-4e3e-8e48-17d3c6e63e01\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"29c98562-024e-47da-845a-3abfd51a7b46\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"462b95ce-b660-4c4a-8bfc-70f72c026223\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5601885e-a6bf-4bc1-8bec-2a7630731cba\",\"optionName\":\"colorStyle\",\"value\":\"P\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"18173205-5915-445b-8182-8a61d30535f7\",\"optionName\":\"tabsStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"S\\\"}]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3633c329-cafd-4c5f-8c1b-ada1f756d223\",\"optionName\":\"sizeStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"X\\\"}]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4ebc0729-1286-45bf-8043-6ab848a2d0b4\",\"optionName\":\"@visibility\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"REQUIRED\\\"}]}\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f883482a-d226-4e45-8ef1-1188a8ded128\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"9c799fe1-8ec9-407f-842e-c0edaeb25c88\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"181464ab-fb2d-416a-81cc-ecc576909a18\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"142337c6-2374-4a83-8447-c556028e0e09\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c890191c-653a-4b5e-8503-6e1133efe5e7\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"366b9b64-0ed4-43c6-88a6-5d2039676b3e\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"48937872-ef9b-4a24-8922-9ffcf4bf8c84\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d26f50e7-e132-4f11-82b0-3da304589c0f\",\"optionName\":\"buttonName\",\"value\":\"Submit\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6ced93af-e593-48f3-87e2-313d6a562130\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7dc8b8c7-0732-4d7d-82c2-6450dc15b713\",\"optionName\":\"approvals\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4de1b979-652e-4708-8395-ccec614834a4\",\"optionName\":\"complianceApprovalVis\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d0cd978a-f34a-4fd7-8364-2441474fe56e\",\"optionName\":\"complianceApproval\",\"value\":\"tw.local.odcRequest.complianceApproval\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4dc5ec94-851e-4ae9-8e1d-a10d1ceee7af\",\"optionName\":\"terminateReasonVIS\",\"value\":\"tw.local.terminationReasonVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"75e96ac3-fe24-432e-8288-8152e5d24c72\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c03f344a-0939-44db-85ab-ad195646d835\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4d1e26c1-b630-4277-8041-cd36c3520e2a\",\"optionName\":\"errorPanelVIS\",\"value\":\"tw.local.errorPanelVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0e61e89a-da07-4836-8ee7-b19500c14988\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ed353563-f158-4979-8e81-a1658701cfb1\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f56555b9-b710-4f8a-8e40-2ad85d713d88\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"878c8a86-5eff-4312-86a0-b6df155b9bfa\",\"optionName\":\"compcheckerCommentVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8200269c-f392-48bb-8964-b1118734971b\",\"optionName\":\"tradeFoComment\",\"value\":\"tw.local.odcRequest.tradeFoComment\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"447df0ab-ad8a-478a-86a4-0c7e7b7d7600\",\"optionName\":\"exeHubMkrComment\",\"value\":\"tw.local.odcRequest.exeHubMkrComment\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ebd24a13-d431-4fba-876f-6cf1d3da0a81\",\"optionName\":\"compcheckerComment\",\"value\":\"tw.local.odcRequest.compcheckerComment\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"4329d93d-28af-4675-8b80-5ee05652e2ca\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"ACT05\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a\"},{\"targetRef\":\"2025.cc1ecdab-a272-4b43-8a48-0488be71b66c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.eb0577af-381e-45da-b582-253e20167664\",\"sourceRef\":\"72a57c58-1959-4daf-9f06-f1b9170feb41\"},{\"outgoing\":[\"2027.fe407a1a-943e-4385-81f2-fdd0cd009af0\"],\"incoming\":[\"2027.71a6d402-3d7f-42ec-a438-ecb665ed4474\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.fe407a1a-943e-4385-81f2-fdd0cd009af0\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":330,\"y\":48,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.d66ea097-e44f-4cdb-80d7-56787db47e15\"},{\"targetRef\":\"2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To ACT05\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fe407a1a-943e-4385-81f2-fdd0cd009af0\",\"sourceRef\":\"2025.d66ea097-e44f-4cdb-80d7-56787db47e15\"},{\"targetRef\":\"2025.d66ea097-e44f-4cdb-80d7-56787db47e15\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"4c24e95a-00e6-4177-bb4a-2b3e774a2b58\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topLeft\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Stay on page\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.71a6d402-3d7f-42ec-a438-ecb665ed4474\",\"sourceRef\":\"2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a\"},{\"targetRef\":\"2025.641a0c30-7293-44e2-88fe-45f081e293b7\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"f3108d09-aaa2-4149-aefd-66780575c2fc\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f4fc1f44-14ec-4fe6-8509-f7597d4dc37a\",\"sourceRef\":\"2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.13397444-fcf2-44ca-85db-86d46bd4b025\"},{\"outgoing\":[\"2027.b17f7677-d5ae-498f-8297-16ac73254928\",\"2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f\"],\"incoming\":[\"2027.f609d65c-c7be-4e5c-8298-d96641b30a09\"],\"default\":\"2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":685,\"y\":194,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e\"},{\"targetRef\":\"2025.6554c1b1-2d24-46de-8d7f-adca48b9b791\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b17f7677-d5ae-498f-8297-16ac73254928\",\"sourceRef\":\"2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e\"},{\"incoming\":[\"2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":673,\"y\":94,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.d779b83f-6bdb-442c-8e7c-60c53ba6331c\"},{\"targetRef\":\"2025.d779b83f-6bdb-442c-8e7c-60c53ba6331c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f\",\"sourceRef\":\"2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e\"},{\"startQuantity\":1,\"outgoing\":[\"2027.f609d65c-c7be-4e5c-8298-d96641b30a09\"],\"incoming\":[\"2027.f4fc1f44-14ec-4fe6-8509-f7597d4dc37a\"],\"default\":\"2027.f609d65c-c7be-4e5c-8298-d96641b30a09\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":527,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Validation script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.641a0c30-7293-44e2-88fe-45f081e293b7\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\" tw.local.errorMessage =\\\"\\\";\\r\\n var mandatoryTriggered = false;\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/\\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\\r\\nmandatory(tw.local.odcRequest.stepLog.action, \\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\\r\\n\\tmandatory(tw.local.odcRequest.stepLog.returnReason, \\\"tw.local.odcRequest.stepLog.returnReason\\\");\\r\\n\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)\\r\\n\\tmandatory(tw.local.odcRequest.stepLog.terminateReason, \\\"tw.local.odcRequest.stepLog.terminateReason\\\");\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- Validation Functions ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/*\\r\\n* =========================================================================================================\\r\\n*  \\r\\n* Add a coach validation error \\r\\n* \\t\\t\\r\\n* EX:\\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\\r\\n*\\r\\n* =========================================================================================================\\r\\n*\\/\\r\\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n{\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\n\\/*\\r\\n* =================================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is less than given length\\r\\n*\\t\\r\\n* EX:\\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =================================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field != null && field != undefined && field.length < len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\/*\\r\\n* =======================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is greater than given length\\r\\n*\\t\\r\\n* EX:\\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =======================================================================================================================\\r\\n*\\/\\r\\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field.length > len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\/*\\r\\n* ==================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the field is null 'Mandatory'\\r\\n*\\t\\r\\n* EX:\\tnotNull(tw.local.name , 'tw.local.name')\\r\\n*\\r\\n* ==================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction mandatory(field , fieldName)\\r\\n{\\r\\n\\tif (field == null || field == undefined )\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\t\\t\\t\\r\\n\\t\\tswitch (typeof field)\\r\\n\\t\\t{\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\r\\n\\t\\t\\tdefault:\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\/\\/ VALIDATE DATE OBJECT\\r\\n\\t\\t\\t\\tif( field && field.getTime && isFinite(field.getTime()) ) {}\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\telse\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\t\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\n\\t\\r\\n\\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\\\"EDITABLE\\\": tw.local.errorPanelVIS =\\\"NONE\\\";\"]}},{\"targetRef\":\"2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exclusive Gateway\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f609d65c-c7be-4e5c-8298-d96641b30a09\",\"sourceRef\":\"2025.641a0c30-7293-44e2-88fe-45f081e293b7\"},{\"startQuantity\":1,\"outgoing\":[\"2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99\"],\"incoming\":[\"2027.eb0577af-381e-45da-b582-253e20167664\"],\"default\":\"2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":180,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Init Script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.cc1ecdab-a272-4b43-8a48-0488be71b66c\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.stepLog = {};\\r\\ntw.local.odcRequest.stepLog.startTime = new Date();\\r\\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT05;\\r\\ntw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT05;\\r\\n\\r\\ntw.local.actionConditions = {};\\r\\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\\r\\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT05;\\r\\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\\r\\ntw.local.actionConditions.lastStepAction= tw.local.lastAction;\\r\\n\\r\\nif(tw.local.lastAction != tw.epv.CreationActions.terminateRequest)\\r\\n\\ttw.local.terminationReasonVIS =\\\"None\\\";\\r\\nelse\\r\\n\\ttw.local.terminationReasonVIS =\\\"Editable\\\";\\r\\n\\r\\n\\r\\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\\r\\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\\r\\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\\r\\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\\r\\n\\r\\n\\/*Visibilty Conditions*\\/\\r\\n\\/*Basic Details CV Visibility*\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\n\\/\\/Parent Request Number\\r\\nif(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest){\\r\\n\\ttw.local.parentRequestNoVIS = \\\"Readonly\\\";\\r\\n}\\t\\r\\nelse{\\r\\n\\ttw.local.parentRequestNoVIS = \\\"None\\\";\\r\\n}\\t\\r\\n\\/\\/Contract Stage\\r\\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\\r\\n\\ttw.local.contractStageVIS = \\\"READONLY\\\";\\r\\n}\\r\\nelse{\\r\\ntw.local.contractStageVIS = \\\"NONE\\\";\\r\\n}\\r\\n\\/*Document Generation Section*\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\\r\\n\\ttw.local.requestTypeVIS = \\\"EDITABLE\\\";\\r\\n}\\r\\nelse{\\r\\n\\ttw.local.requestTypeVIS = \\\"NONE\\\";\\t\\r\\n}\\r\\n\\/*Importer Details Visibility*\\/\\r\\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\\r\\n\\ttw.local.bankRefVIS = \\\"EDITABLE\\\";\\r\\n}\\r\\nelse{\\r\\n\\ttw.local.bankRefVIS = \\\"NONE\\\";\\t\\r\\n}\\r\\n\\/* Financial Detais Trade Fo*\\/\\r\\nif(tw.local.odcRequest.BasicDetails.paymentTerms.name == \\\"001\\\")\\r\\n{\\r\\n\\ttw.local.multiTenorDatesVIS = \\\"None\\\";\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\ttw.local.multiTenorDatesVIS = \\\"Readonly\\\";\\r\\n}\\r\\n\\r\\ntw.local.errorPanelVIS=\\\"NONE\\\";\"]}},{\"targetRef\":\"2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Get Actions\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99\",\"sourceRef\":\"2025.cc1ecdab-a272-4b43-8a48-0488be71b66c\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorPanelVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.360b5581-289c-4fcd-8a20-a1a9c5821c4e\"},{\"startQuantity\":1,\"outgoing\":[\"2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c\"],\"incoming\":[\"2027.b17f7677-d5ae-498f-8297-16ac73254928\"],\"default\":\"2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":800,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Setting Status and sub status\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.6554c1b1-2d24-46de-8d7f-adca48b9b791\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.authorize)\\r\\n{\\r\\n\\tif( (tw.local.odcRequest.requestType.value == tw.epv.RequestType.Create || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment ||tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate) \\r\\n\\t&& tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter == true)\\r\\n\\t{\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\" Completed\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Printing Documents for Customer\\\";\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\ttw.local.odcRequest.appInfo.status    = \\\"Completed\\\";\\r\\n\\t\\ttw.local.odcRequest.appInfo.subStatus = \\\"Completed\\\";\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;\\r\\n\\t}\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"In Execution\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Execution Hub Initiation\\\";\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"In Approval\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Trade Fo Review\\\";\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.obtainApprovals){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"In Execution\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Execution Hub Processing \\\";\\r\\n}\\r\\n\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"Terminated\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Terminated\\\";\\r\\n}\\r\\n\\r\\n\"]}},{\"targetRef\":\"2025.bdb6c456-66a6-432e-8861-99065124f8df\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To History\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c\",\"sourceRef\":\"2025.6554c1b1-2d24-46de-8d7f-adca48b9b791\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9cb38119-f2b6-4f1b-843f-8f1426417c61\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"deliveryTerms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2aacc7a1-6a1b-4f1e-889a-e0502b64ae53\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"paymentTerms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1724623e-dec9-451a-88bb-0042a05b292c\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"specialInstructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7a11a7d0-f02c-494b-8c8a-ff6d12f903bc\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"instructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a85b81e8-27f0-4c72-89a6-890c4e75bcc0\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d6b87c98-933a-45ee-8861-663b4bc5a301\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNoVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.f9f460a8-56f2-41eb-8762-f7f00a779817\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.91daf81c-44bd-470f-83ca-9ccde1ada22d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"bankRefVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a9f4dd55-7f06-4d5e-817f-634f609c7cdd\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"multiTenorDatesVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a7380a59-4bb7-47bb-8240-cb7d2a4dbc67\"},{\"outgoing\":[\"2027.fe456fee-5993-45ed-8fb5-af30880abfdc\"],\"incoming\":[\"2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":913,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.fe456fee-5993-45ed-8fb5-af30880abfdc\",\"name\":\"Update request state and status in DB\",\"dataInputAssociation\":[{\"targetRef\":\"2055.a84d91ec-e620-4417-85c4-7dd7db58ff31\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.c5194a72-2de4-483f-823c-47d5b98b572c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.3974caa7-9f10-4f46-8275-17080a25476e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.b05449cf-c459-4405-809c-888b00e3e968\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}]},{\"targetRef\":\"2055.b537f107-9e83-46e0-8979-6fe5796c7a7d\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.stepName\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.bdb6c456-66a6-432e-8861-99065124f8df\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.8e0cea15-236a-4b79-86a8-f93756a4ac86\"]}],\"calledElement\":\"1.2cab04cd-6063-4a13-b148-ec9788e07bf4\"},{\"targetRef\":\"2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fe456fee-5993-45ed-8fb5-af30880abfdc\",\"sourceRef\":\"2025.bdb6c456-66a6-432e-8861-99065124f8df\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"terminationReasonVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.040b6028-73e3-497d-84fb-e406efe1bbd1\"},{\"startQuantity\":1,\"outgoing\":[\"2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0\"],\"default\":\"2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0\",\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":1038,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"name\":\"Audit ODC Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.********-72f3-46ed-8fed-7236b2c2451f\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.254cf8eb-2743-4c53-8c52-e51c8c22884e\"]}],\"calledElement\":\"1.7ee96dd0-834b-44cb-af41-b21585627e49\"},{\"incoming\":[\"2027.29500b3c-9929-4033-83eb-882732e5ba10\",\"2027.1f66477a-c795-4cd2-85e5-69baba4d46f0\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1919,\"y\":201,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}],\"preAssignmentScript\":[\"tw.local.fromExechecker = true;\"]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"2025.d5d5c651-2ba4-4499-8473-2674156f23f5\"},{\"outgoing\":[\"2027.51be54a0-ee9a-46a0-8136-623c05259495\"],\"incoming\":[\"2027.a29fe9cb-1812-412d-8b73-28a3fa508012\",\"2027.fe456fee-5993-45ed-8fb5-af30880abfdc\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":1227,\"y\":179,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.51be54a0-ee9a-46a0-8136-623c05259495\",\"name\":\"Audit Request History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.userRole.CACT05\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"outgoing\":[\"2027.b912c63a-7ea5-4785-81b6-21cce02ed77e\",\"2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0\"],\"incoming\":[\"2027.51be54a0-ee9a-46a0-8136-623c05259495\"],\"default\":\"2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1345,\"y\":198,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543\"},{\"outgoing\":[\"2027.a29fe9cb-1812-412d-8b73-28a3fa508012\",\"2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f\"],\"incoming\":[\"2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0\"],\"default\":\"2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1137,\"y\":198,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd\"},{\"incoming\":[\"2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f\",\"2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1225,\"y\":277,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.024c598d-f089-41f0-8ea8-5038ef7741e2\"},{\"targetRef\":\"2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Audited\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.51be54a0-ee9a-46a0-8136-623c05259495\",\"sourceRef\":\"2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd\"},{\"targetRef\":\"2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To terminate?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b912c63a-7ea5-4785-81b6-21cce02ed77e\",\"sourceRef\":\"2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543\"},{\"targetRef\":\"2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0\",\"sourceRef\":\"2025.********-72f3-46ed-8fed-7236b2c2451f\"},{\"targetRef\":\"2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.a29fe9cb-1812-412d-8b73-28a3fa508012\",\"sourceRef\":\"2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd\"},{\"targetRef\":\"2025.024c598d-f089-41f0-8ea8-5038ef7741e2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f\",\"sourceRef\":\"2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd\"},{\"targetRef\":\"2025.024c598d-f089-41f0-8ea8-5038ef7741e2\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0\",\"sourceRef\":\"2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543\"},{\"outgoing\":[\"2027.7041aaff-918a-4429-8eaa-cede08b4ac2f\",\"2027.29500b3c-9929-4033-83eb-882732e5ba10\"],\"incoming\":[\"2027.b912c63a-7ea5-4785-81b6-21cce02ed77e\"],\"default\":\"2027.29500b3c-9929-4033-83eb-882732e5ba10\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1417,\"y\":198,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"terminate?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30\"},{\"targetRef\":\"2025.ecfb199d-d6ea-4243-8c36-180ca040be95\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.terminateRequest\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Cancel and Delete Transactions\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7041aaff-918a-4429-8eaa-cede08b4ac2f\",\"sourceRef\":\"2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30\"},{\"startQuantity\":1,\"outgoing\":[\"2027.d83bd288-4780-447f-8096-5e052cd0618d\"],\"incoming\":[\"2027.7041aaff-918a-4429-8eaa-cede08b4ac2f\"],\"default\":\"2027.d83bd288-4780-447f-8096-5e052cd0618d\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1527,\"y\":231,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.requestIdStr=tw.local.odcRequest.requestID+\\\"\\\";\"]},\"name\":\"Cancel and Delete Transactions\",\"dataInputAssociation\":[{\"targetRef\":\"2055.8c25b7d2-65fe-4ffa-83d3-f054a2ad4209\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestIdStr\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.ecfb199d-d6ea-4243-8c36-180ca040be95\",\"calledElement\":\"1.812db3ff-6589-474c-bcc5-21fde39e4d25\"},{\"targetRef\":\"2025.37b03ea4-b507-4006-8d61-bcdf1b904a87\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d83bd288-4780-447f-8096-5e052cd0618d\",\"sourceRef\":\"2025.ecfb199d-d6ea-4243-8c36-180ca040be95\"},{\"targetRef\":\"2025.d5d5c651-2ba4-4499-8473-2674156f23f5\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.29500b3c-9929-4033-83eb-882732e5ba10\",\"sourceRef\":\"2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30\"},{\"outgoing\":[\"2027.179c02bc-3983-4cf4-8303-7d1af33a66d3\"],\"incoming\":[\"2027.d83bd288-4780-447f-8096-5e052cd0618d\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":1700,\"y\":231,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.179c02bc-3983-4cf4-8303-7d1af33a66d3\",\"name\":\"cancel request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.55a76aa1-e513-4fd3-835f-7fe160120af7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.77d2b3d6-2a2a-4520-ad08-31686157d431\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderID\"]}}]},{\"targetRef\":\"2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.37b03ea4-b507-4006-8d61-bcdf1b904a87\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a\"]}],\"calledElement\":\"1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc\"},{\"targetRef\":\"2025.a15aa745-1422-4867-839a-4d4db1c1696c\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.179c02bc-3983-4cf4-8303-7d1af33a66d3\",\"sourceRef\":\"2025.37b03ea4-b507-4006-8d61-bcdf1b904a87\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.53c3ca54-28d1-48c3-8287-553300571d94\"},{\"incoming\":[\"2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1817,\"y\":437,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 3\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.e46993c6-1ffa-4e7f-8639-d4bb8ad09266\"},{\"outgoing\":[\"2027.1f66477a-c795-4cd2-85e5-69baba4d46f0\",\"2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4\"],\"incoming\":[\"2027.179c02bc-3983-4cf4-8303-7d1af33a66d3\"],\"default\":\"2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1835,\"y\":250,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Copy of Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.a15aa745-1422-4867-839a-4d4db1c1696c\"},{\"targetRef\":\"2025.d5d5c651-2ba4-4499-8473-2674156f23f5\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Copy of To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1f66477a-c795-4cd2-85e5-69baba4d46f0\",\"sourceRef\":\"2025.a15aa745-1422-4867-839a-4d4db1c1696c\"},{\"targetRef\":\"2025.e46993c6-1ffa-4e7f-8639-d4bb8ad09266\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Copy of To Stay on page 3\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4\",\"sourceRef\":\"2025.a15aa745-1422-4867-839a-4d4db1c1696c\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestIdStr\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8536ea82-5ea8-447a-8b31-f62165a54be0\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"addressBICList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.5d21f0ba-5415-494b-8f93-452d3c74b1ef\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"partyTypeName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.5cbb628b-1479-468f-8f1b-b7af7c682bee\"},{\"itemSubjectRef\":\"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651\",\"name\":\"customerFullDetails\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.23299dd3-3b7e-45b1-86aa-6e7dfced47c2\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"index\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.11a3ceb5-2045-4417-8d63-f05962edc0bf\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"calculatedChangeAmnt\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b535c6db-f211-4076-8a49-3d5afd5b2fca\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"exRate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1f5074e5-603e-464e-8b83-a6a931d522f4\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"new Date()\"}]},\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"todayDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7267a014-7afe-4b66-81f4-b7526ae4e163\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"39b9fc11-9df3-4f89-8cee-a8b3336af51e\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"bdd9cb0d-f61e-4641-a902-75c9b3d16d76\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Act05 - ODC Execution Hub \\u2013 Initiation Review\",\"declaredType\":\"globalUserTask\",\"id\":\"1.6d4e9c1d-5624-494c-96fb-3bd94584f975\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.43126547-b78b-4e92-bab9-47081d86a36a\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"fromExechecker\",\"isCollection\":false,\"id\":\"2055.6b42b31e-df5e-4c9e-8914-5bda302e956c\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.a58f5da6-00ea-41e6-8a5d-927ee74418fb\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"2bf28052-a3ae-44c2-8e4c-bb8cc287665e\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"7780c61b-9b9b-4cce-8b94-d4af8ace2955\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"bdf92c99-ced1-4689-8251-4062b731fa77\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.769dc134-1d15-4dd4-a967-c5f61cf352dc\",\"epvProcessLinkId\":\"4dbfdd3b-6a34-4801-8134-88edf761d198\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"7bc07a22-ff7d-4439-8815-e159b287f4cb\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7\",\"epvProcessLinkId\":\"97615e59-a381-45f6-8ef0-fbf41c6b12e2\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.062854b5-6513-4da8-84ab-0126f90e550d\",\"epvProcessLinkId\":\"426975fc-8954-4de0-8836-4d66a6d5dcaf\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"162c1eed-960c-4a0f-8775-a9bb325b098b\"}],\"outputSet\":[{\"id\":\"7f7e65ee-bbc8-4b68-b5a7-d099d6c460c4\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.43609737-d968-42ad-9a14-2da88598005a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"regeneratedRemittanceLetterTitleVIS\",\"isCollection\":false,\"id\":\"2055.e3114267-71c4-4261-8f02-f45b08a80f8c\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"lastAction\",\"isCollection\":false,\"id\":\"2055.09800ab8-e6d8-4ee9-8954-dc0c77df48f0\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.a532e1f6-27cc-44ab-8ba6-1ab2ea92ec2d\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"a8a305e4-905c-4353-91f1-cb1313575512\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.43609737-d968-42ad-9a14-2da88598005a", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "e5749d16-39b7-49b2-a960-db46c9818756", "versionId": "48637c70-4961-4b7c-a655-4086f9ffcd46"}, {"name": "regeneratedRemittanceLetterTitleVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e3114267-71c4-4261-8f02-f45b08a80f8c", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "0b9461ed-0d1d-4c72-aaa4-3ea411aef843", "versionId": "2e1e740b-51ca-4907-b14b-4bfbe6b4235d"}, {"name": "lastAction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.09800ab8-e6d8-4ee9-8954-dc0c77df48f0", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9b0b8516-e0a2-4d0b-b73b-ad00fe955e51", "versionId": "444cc3ce-5d20-4a6b-b2f2-a5e74b3907a4"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a532e1f6-27cc-44ab-8ba6-1ab2ea92ec2d", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "b377ac9c-fd7d-478a-bcc7-2397bacc6bb9", "versionId": "bb5d5f44-5a9a-4c0f-b49a-97f33cb10ed5"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.43126547-b78b-4e92-bab9-47081d86a36a", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "00dc2ffe-e358-4b20-b618-2a04fd813c20", "versionId": "52e924a5-512f-4b42-ae81-6385c8006af8"}, {"name": "fromExechecker", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6b42b31e-df5e-4c9e-8914-5bda302e956c", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "55aaa903-92bf-4acf-ba31-d567dbbc24fd", "versionId": "a3738fab-0d69-4956-ac19-283e5aae39dd"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.13397444-fcf2-44ca-85db-86d46bd4b025", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bfac0977-59f0-4556-ae74-7cf7c732f773", "versionId": "a9e49c08-09e6-4cc2-9501-803bc11e91b2"}, {"name": "errorPanelVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.360b5581-289c-4fcd-8a20-a1a9c5821c4e", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a5dedde9-6a71-47fb-a79f-c2a765984213", "versionId": "b6d2fcb6-2885-4389-aa36-fe1423a0bc5d"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9cb38119-f2b6-4f1b-843f-8f1426417c61", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "efe9c185-37d0-4ec6-b4ce-dcf5d80385cb", "versionId": "617b59ed-9906-4a62-be51-3b79a9188b95"}, {"name": "deliveryTerms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2aacc7a1-6a1b-4f1e-889a-e0502b64ae53", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "26dba0f4-5d0c-4d05-867d-437698f86f16", "versionId": "286f2c6d-24ac-49f1-8bbc-1dcd74ab062e"}, {"name": "paymentTerms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1724623e-dec9-451a-88bb-0042a05b292c", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "6db46551-e7b5-4b9f-99e4-e7acb3dae96b", "versionId": "0f23f24d-a582-4d8a-8bfe-a1f3113a7162"}, {"name": "specialInstructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7a11a7d0-f02c-494b-8c8a-ff6d12f903bc", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1e713ebe-cf3a-4815-9dd6-e6b1f71eed62", "versionId": "6e88b5c2-4670-41fa-9d84-ab088bf7e247"}, {"name": "instructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a85b81e8-27f0-4c72-89a6-890c4e75bcc0", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0d5fec1e-2855-4719-a397-65879058565f", "versionId": "832c68a8-43c7-42a7-b0fc-2aee2435f2c5"}, {"name": "requestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d6b87c98-933a-45ee-8861-663b4bc5a301", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "141ced78-ce3b-44d4-95ee-92a800946570", "versionId": "b0e8c0ac-711c-41da-b14d-c883b8317df7"}, {"name": "parentRequestNoVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f9f460a8-56f2-41eb-8762-f7f00a779817", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "171f0fb5-0ed2-4534-97be-88999abfbd90", "versionId": "4fcc0695-f1cb-432d-b9f1-dae30c7499b6"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.91daf81c-44bd-470f-83ca-9ccde1ada22d", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "aab4b964-afbd-4964-a5de-970c70066bb6", "versionId": "f07712b1-270b-49b3-b9ee-8ce30875fb34"}, {"name": "bankRefVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a9f4dd55-7f06-4d5e-817f-634f609c7cdd", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5f5b863d-e26f-418e-af39-161409c3dc46", "versionId": "7e135e10-c587-4dec-be0c-4e36fcaca998"}, {"name": "multiTenorDatesVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a7380a59-4bb7-47bb-8240-cb7d2a4dbc67", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "12", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a7801a34-1f84-4149-91ed-b730fc3a0493", "versionId": "72fbfa2e-cdf4-48b9-b453-9746a3ae515e"}, {"name": "terminationReasonVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.040b6028-73e3-497d-84fb-e406efe1bbd1", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "13", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "cb86cf9f-cc53-45f4-b95a-4efa02d626a0", "versionId": "980abbf6-279d-482b-b61b-35d87b59dd2c"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.53c3ca54-28d1-48c3-8287-553300571d94", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "14", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "58050b47-9ee4-4a6f-ad11-09d703bffc94", "versionId": "692c987e-5b8e-4d81-9d77-51923f2d0165"}, {"name": "requestIdStr", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8536ea82-5ea8-447a-8b31-f62165a54be0", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "15", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fb367db2-2529-403d-b624-fbef921ff90f", "versionId": "88436e1d-025d-4db8-ba6c-21c36e3d7fde"}, {"name": "addressBICList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5d21f0ba-5415-494b-8f93-452d3c74b1ef", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "16", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0e75add2-9f87-40ae-82f3-4d074b25a94c", "versionId": "d1a70ca5-fcac-49f4-8737-e8ad68aec4e1"}, {"name": "partyTypeName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5cbb628b-1479-468f-8f1b-b7af7c682bee", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "17", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "850d40ad-23f5-40f0-b29e-46faf9630344", "versionId": "bdd03a42-fba1-4696-8b72-21d020e3aaaa"}, {"name": "customerFullDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.23299dd3-3b7e-45b1-86aa-6e7dfced47c2", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "18", "isArrayOf": "false", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "e734c881-5f65-4c33-9cee-34e192352991", "versionId": "a793c6ee-da85-4ae1-84de-0d75492510b4"}, {"name": "index", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.11a3ceb5-2045-4417-8d63-f05962edc0bf", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "19", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "74457d82-c69f-4830-8f1e-d02ee0c66874", "versionId": "774bae68-42f9-4a9b-a6d7-979321e048fc"}, {"name": "calculatedChangeAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b535c6db-f211-4076-8a49-3d5afd5b2fca", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "20", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "2bc5c4d9-3a17-489c-b523-237ac4b2d89f", "versionId": "62d97d81-6d99-460a-925e-96b5779fe88d"}, {"name": "exRate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1f5074e5-603e-464e-8b83-a6a931d522f4", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "21", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "6b9537ef-f2ec-407c-8841-2e4e14f81634", "versionId": "1025aec5-179e-4028-ac9a-8ca3e7449979"}, {"name": "todayDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7267a014-7afe-4b66-81f4-b7526ae4e163", "description": {"isNull": "true"}, "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "namespace": "2", "seq": "22", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f60c8105-**************-3cc278f3e4d3", "versionId": "45a0181a-867e-40cf-bba6-9a78ab7f477f"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bdb6c456-66a6-432e-8861-99065124f8df", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "Update request state and status in DB", "tWComponentName": "SubProcess", "tWComponentId": "3012.241ebeb3-4e18-435d-9ab2-8f88fdcdc17c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:aff098473ecd546d:1d42df0a:18b1b2b8841:-76f5", "versionId": "1a949226-ca7d-4003-a2ee-d0e61efe54e2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.241ebeb3-4e18-435d-9ab2-8f88fdcdc17c", "attachedProcessRef": "/1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "guid": "63ef63b1-675c-4793-b3e4-e6fa6a69a797", "versionId": "a64e0188-6658-4989-b9e9-23dd0d5f97f6"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "Audit Request History", "tWComponentName": "SubProcess", "tWComponentId": "3012.96f2a950-1939-4aaf-ad8f-8c8b201ea0bc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:340c", "versionId": "38cf00bd-60ce-41da-86b6-8fdcdfef8d30", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.96f2a950-1939-4aaf-ad8f-8c8b201ea0bc", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "d2b3fb67-daf3-4554-b5d5-7e416ca62193", "versionId": "508f897f-dd93-4251-8cce-b93894057c76"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.********-72f3-46ed-8fed-7236b2c2451f", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "Audit ODC Request", "tWComponentName": "SubProcess", "tWComponentId": "3012.0ab1a01c-b589-4f62-ad5d-6a92545d4f47", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:340b", "versionId": "55686c50-bdd3-44a0-ba9a-781830484e00", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.0ab1a01c-b589-4f62-ad5d-6a92545d4f47", "attachedProcessRef": "/1.7ee96dd0-834b-44cb-af41-b21585627e49", "guid": "30860a78-24a1-4669-8456-0026883a7c7c", "versionId": "7135b21b-0d41-46b8-b8e1-87a9ab443d35"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9db66be0-b755-4230-b7e2-fbdeb1078b25", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.8d61bf76-4be5-4a83-acb4-1fbd91fe9ae0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ef0d56db9ddf1554:-58d13970:189ac38838e:-7214", "versionId": "87e033a4-d5f9-4534-ac94-efaf4681b97c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.8d61bf76-4be5-4a83-acb4-1fbd91fe9ae0", "haltProcess": "false", "guid": "43c9ca82-44a7-46b5-a6b5-1d46767a0ad4", "versionId": "82eb8605-5c7e-4d24-913d-53e12d2b7972"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.28156bcc-db35-451c-b58a-d8bfa74c5d8e", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.2e5e1d59-dd66-4c43-b077-bc0d81bd85e5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ef0d56db9ddf1554:-58d13970:189ac38838e:-7215", "versionId": "8c24d7da-9bc5-4a0e-ad5b-ac63e516032c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.37b03ea4-b507-4006-8d61-bcdf1b904a87", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "cancel request", "tWComponentName": "SubProcess", "tWComponentId": "3012.12361bf6-1fa2-41c7-a20a-27208079bc66", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:7ef9", "versionId": "d17a09e2-054a-4773-a81c-d469625e38ad", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.12361bf6-1fa2-41c7-a20a-27208079bc66", "attachedProcessRef": "/1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "guid": "ff3fbbd6-9ef3-4a9f-ba93-0547fe264d35", "versionId": "28bbbf69-bc05-43d8-90d8-b0210d69de94"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ecfb199d-d6ea-4243-8c36-180ca040be95", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "name": "Cancel and Delete Transactions", "tWComponentName": "SubProcess", "tWComponentId": "3012.fb3a4f8b-9380-42cb-b1d4-c46d9e53b113", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3223", "versionId": "dd846516-9ca9-4e72-b2ee-5d191375cdc2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.fb3a4f8b-9380-42cb-b1d4-c46d9e53b113", "attachedProcessRef": "/1.812db3ff-6589-474c-bcc5-21fde39e4d25", "guid": "90728660-f4dd-4e54-ba19-45c165f06f49", "versionId": "d259437e-572e-4377-ac4c-c5aaffd4dd5c"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.267d68e6-4b83-40a8-8c76-5007001c11e6", "epvId": "/21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "422eb627-1b6b-4c37-a8e8-641711df3319", "versionId": "58d7d8c3-cea4-4c32-8889-3176a6d22708"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.aab8572a-f2dc-4da1-8833-64be65fa6eb9", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "3f45ba5e-2aef-413d-802c-9404c326708c", "versionId": "7ca7375b-a27a-426e-8cd6-83bdc36ffa12"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.5ef74e3e-b125-4583-8a15-1b384962c55d", "epvId": "/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "20a20edb-6135-4542-9e8c-f04f1661400a", "versionId": "8ccddcbc-44e8-4010-903b-9a69e8e3c420"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.1ebf443c-96cd-4d64-be57-f5aa85b5a653", "epvId": "/21.062854b5-6513-4da8-84ab-0126f90e550d", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "1b2fce3d-679a-4db1-a000-c222dc3239d0", "versionId": "8e60dd09-9c36-4042-a031-050fea2ca6f9"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.13d27421-5d6b-4597-aa77-86d783b56e42", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "cac6f348-29a1-4d13-9aae-0fd4a25099d4", "versionId": "abbba233-f264-47f7-83a6-305fa10f31e0"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.3fb9a47f-c39a-4539-aab1-edf1a3578120", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "6aba6ee8-2b92-49a5-afdc-479e880b39c8", "versionId": "e36cfb69-233b-4fcc-b3c6-6971c2dedcdf"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.b316f11a-69f3-4cf8-b863-09f2633241db", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "ca1b94ef-7760-4445-972d-4ea5e6dd3d58", "versionId": "e90fcaca-c150-401a-871e-eba855dfc646"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.b4449e06-9a5d-4ba1-bc63-2ad3064e2c1e", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "guid": "c38c24da-35a7-4372-bdb0-48ef18881d17", "versionId": "e1607f55-104d-4d53-bbb3-2275bbc91d3d"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "a8a305e4-905c-4353-91f1-cb1313575512", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "Act05 - ODC Execution Hub – Initiation Review", "id": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "bdd9cb0d-f61e-4641-a902-75c9b3d16d76", "ns16:startEvent": {"name": "Start", "id": "72a57c58-1959-4daf-9f06-f1b9170feb41", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "120", "y": "201", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.eb0577af-381e-45da-b582-253e20167664"}, "ns3:formTask": {"name": "ACT05", "id": "2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a", "ns16:extensionElements": {"ns3:validationStayOnPagePaths": "okbutton", "ns13:nodeVisualInfo": {"x": "350", "y": "177", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": ["2027.fe407a1a-943e-4385-81f2-fdd0cd009af0", "2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99"], "ns16:outgoing": ["2027.71a6d402-3d7f-42ec-a438-ecb665ed4474", "2027.f4fc1f44-14ec-4fe6-8509-f7597d4dc37a"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "4329d93d-28af-4675-8b80-5ee05652e2ca", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "181464ab-fb2d-416a-81cc-ecc576909a18", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "142337c6-2374-4a83-8447-c556028e0e09", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "c890191c-653a-4b5e-8503-6e1133efe5e7", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "366b9b64-0ed4-43c6-88a6-5d2039676b3e", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "48937872-ef9b-4a24-8922-9ffcf4bf8c84", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "d26f50e7-e132-4f11-82b0-3da304589c0f", "ns19:optionName": "buttonName", "ns19:value": "Submit"}, {"ns19:id": "6ced93af-e593-48f3-87e2-313d6a562130", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "7dc8b8c7-0732-4d7d-82c2-6450dc15b713", "ns19:optionName": "approvals", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "4de1b979-652e-4708-8395-ccec614834a4", "ns19:optionName": "complianceApprovalVis", "ns19:value": "NONE"}, {"ns19:id": "d0cd978a-f34a-4fd7-8364-2441474fe56e", "ns19:optionName": "complianceApproval", "ns19:value": "tw.local.odcRequest.complianceApproval", "ns19:valueType": "dynamic"}, {"ns19:id": "4dc5ec94-851e-4ae9-8e1d-a10d1ceee7af", "ns19:optionName": "terminateReasonVIS", "ns19:value": "tw.local.terminationReasonVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "75e96ac3-fe24-432e-8288-8152e5d24c72", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "c03f344a-0939-44db-85ab-ad195646d835", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "4d1e26c1-b630-4277-8041-cd36c3520e2a", "ns19:optionName": "errorPanelVIS", "ns19:value": "tw.local.errorPanelVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "0e61e89a-da07-4836-8ee7-b19500c14988", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "ed353563-f158-4979-8e81-a1658701cfb1", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "f56555b9-b710-4f8a-8e40-2ad85d713d88", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "878c8a86-5eff-4312-86a0-b6df155b9bfa", "ns19:optionName": "compcheckerCommentVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "8200269c-f392-48bb-8964-b1118734971b", "ns19:optionName": "tradeFoComment", "ns19:value": "tw.local.odcRequest.tradeFoComment", "ns19:valueType": "dynamic"}, {"ns19:id": "447df0ab-ad8a-478a-86a4-0c7e7b7d7600", "ns19:optionName": "exeHubMkrComment", "ns19:value": "tw.local.odcRequest.exeHubMkrComment", "ns19:valueType": "dynamic"}, {"ns19:id": "ebd24a13-d431-4fba-876f-6cf1d3da0a81", "ns19:optionName": "compcheckerComment", "ns19:value": "tw.local.odcRequest.compcheckerComment", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "9c799fe1-8ec9-407f-842e-c0edaeb25c88", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f883482a-d226-4e45-8ef1-1188a8ded128", "ns19:layoutItemId": "Tab_Section1", "ns19:configData": [{"ns19:id": "2f8fccd3-9879-4e3e-8e48-17d3c6e63e01", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "29c98562-024e-47da-845a-3abfd51a7b46", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "462b95ce-b660-4c4a-8bfc-70f72c026223", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "5601885e-a6bf-4bc1-8bec-2a7630731cba", "ns19:optionName": "colorStyle", "ns19:value": "P"}, {"ns19:id": "18173205-5915-445b-8182-8a61d30535f7", "ns19:optionName": "tabsStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"}, {"ns19:id": "3633c329-cafd-4c5f-8c1b-ada1f756d223", "ns19:optionName": "sizeStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}, {"ns19:id": "4ebc0729-1286-45bf-8043-6ab848a2d0b4", "ns19:optionName": "@visibility", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"REQUIRED\"}]}"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "4f59508d-9e4b-4845-8f49-4b3cbefe2edf", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "7f199f4d-c7a9-497a-89b4-15228c4256c4", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "f138f6a9-e27a-413e-86bc-ddeb6d979ad2", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "569dfbbb-7e7c-408a-85bd-ad9a4c1499af", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "cffdb4cc-b87d-4e77-8a7d-191bf3c42b63", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "44c8df6f-4cd9-4c09-8b9a-ed073dc673f5", "ns19:optionName": "basicDetailsVIS", "ns19:value": "\"READONLY\""}, {"ns19:id": "2b736bbe-1f10-4cc2-8941-69da2e80f3d1", "ns19:optionName": "parentRequestNoVis", "ns19:value": "tw.local.parentRequestNoVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "528ec7d8-abab-43da-8df6-0629e5105f46", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "5d26d8ab-f2e1-4495-8b72-9dc9fdd29e88", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "<PERSON><PERSON><PERSON>", "ns19:valueType": "static"}, {"ns19:id": "0cd58d74-2f94-4bbd-8e50-b73ad0c55082", "ns19:optionName": "contractStageVIS", "ns19:value": "tw.local.contractStageVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "403869b2-09dd-4375-80eb-6892e66337de", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "tw.local.multiTenorDatesVIS", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "bf13348a-57c9-46f5-8b74-cba703b6aade", "ns19:layoutItemId": "Document_Generation_CV1", "ns19:configData": [{"ns19:id": "346cb219-0124-426d-8fc0-9cf5e483c354", "ns19:optionName": "@label", "ns19:value": "Letter Generation"}, {"ns19:id": "e9cd7085-34e9-4873-8116-6eb83f5b1115", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "8836366c-f207-4a8a-8551-ba9e6a36955a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "de03e1fc-c428-40e5-837e-c252177483c0", "ns19:optionName": "documentGeneratonVIS", "ns19:value": "\"READONLY\""}, {"ns19:id": "ad857c79-4a7e-4136-8153-196d171a5218", "ns19:optionName": "instructions", "ns19:value": "tw.local.instructions", "ns19:valueType": "dynamic"}, {"ns19:id": "f13c819e-71c9-4bd7-860c-758d8971bd7d", "ns19:optionName": "specialInstructions", "ns19:value": "tw.local.specialInstructions", "ns19:valueType": "dynamic"}, {"ns19:id": "e830dfd9-5ac2-4cf9-8ac9-3470be4d22dc", "ns19:optionName": "paymentTerms", "ns19:value": "tw.local.paymentTerms", "ns19:valueType": "dynamic"}, {"ns19:id": "3da62dd3-da09-44bb-8183-817cadaeaa0c", "ns19:optionName": "deliveryTerms", "ns19:value": "tw.local.deliveryTerms", "ns19:valueType": "dynamic"}, {"ns19:id": "1c45119a-c682-4cc8-8931-c6e779048e2d", "ns19:optionName": "requestTypeVIS", "ns19:value": "tw.local.requestTypeVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "8e8094b3-2709-4969-820f-2e57675b5b84", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "54ebe88d-0143-439a-806c-072bd05fd822", "ns19:optionName": "regeneratedRemittanceLetterTitleVIS", "ns19:value": "tw.local.regeneratedRemittanceLetterTitleVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "0d70b2d0-09d0-40cf-8f60-8f57c2654cc9", "ns19:optionName": "DocumentGenerationVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "721c49d0-363a-4020-83c6-1a2ff48b8122", "ns19:optionName": "remittanceLetter<PERSON><PERSON>on", "ns19:value": "None"}, {"ns19:id": "ddc93a75-24a1-46eb-81a7-35d180788b6a", "ns19:optionName": "documentGenerationVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.1d99aba8-195f-4eee-ba8c-a47926bc21e2", "ns19:binding": "tw.local.odcRequest.GeneratedDocumentInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "a1bb5a88-33a1-4a0c-8264-48202758152d", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "4b7335dc-565a-4874-87fb-d6b5faf147d6", "ns19:optionName": "@label", "ns19:value": "Customer Info"}, {"ns19:id": "12f7655f-8be9-42c7-82d8-fed7984dc3c2", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "1c4ab8ed-1743-4726-8da8-92ad4490ee1c", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "ff44b7eb-15c2-4e32-8c15-46cd3896ff42", "ns19:optionName": "customerInfoVIS", "ns19:value": "READONLY"}, {"ns19:id": "0e976027-c953-4109-8734-5a4123923a1b", "ns19:optionName": "requestTypeVis", "ns19:value": "\"READONLY\""}, {"ns19:id": "6105ea32-783a-424a-8ace-15470ba86d92", "ns19:optionName": "listsVIS", "ns19:value": "READONLY"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "7021f345-19f1-4c0c-8103-01315252d575", "ns19:layoutItemId": "Financial_Details_Branch1", "ns19:configData": [{"ns19:id": "53c207e1-4e34-4e9f-826f-313f99a55eab", "ns19:optionName": "@label", "ns19:value": "Financial Details - Branch"}, {"ns19:id": "ffcc6dc8-d25a-4f68-8c9e-7cc29724f213", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "69ba7164-e143-463f-87cb-765e8ed61190", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "a0802725-4c63-4a8b-891d-a5cb1d5604b0", "ns19:optionName": "requestTradeFoVis", "ns19:value": "\"READONLY\""}, {"ns19:id": "4c328eab-520b-4a0d-8e85-1627d1a57ab6", "ns19:optionName": "financialDetailsVis", "ns19:value": "\"READONLY\""}, {"ns19:id": "258a69b0-040d-45d4-8f22-997d10cf2717", "ns19:optionName": "fcCollectionVis", "ns19:value": "\"READONLY\""}, {"ns19:id": "7125e955-3995-4da3-8ae8-87e4114a3924", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "25ccef02-1b87-4604-8763-bb72019b8f3a", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "223910ad-acc4-4fa9-8355-84dbb81e27ef", "ns19:layoutItemId": "FC_Collections_CV1", "ns19:configData": [{"ns19:id": "783cb246-2d9d-4ddb-87cc-33e490a2fc35", "ns19:optionName": "@label", "ns19:value": "FC Collections"}, {"ns19:id": "4e21faf2-ce42-4a21-864e-80ccdda2e9eb", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "8ed0576a-89e4-4fb5-82b5-206b1af02b43", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "f410b7a5-4000-4443-817d-536ab90b2bf0", "ns19:optionName": "FCVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "4c33a035-bcdf-4537-88ea-9a90c35a1d8e", "ns19:optionName": "addBtnVIS", "ns19:value": "None"}, {"ns19:id": "5dce4ec4-b1a4-4a57-88b6-bce9779b7ebd", "ns19:optionName": "retrieveBtnVis", "ns19:value": "None"}, {"ns19:id": "98b1beed-e637-4896-84a5-a154f803fb95", "ns19:optionName": "collectionCurrencyVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "f493bbb0-8ddd-4902-815a-c2f099477a2b", "ns19:optionName": "negotiatedExchangeRateVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "*************-4b9d-8be6-3ab7a98e5924", "ns19:optionName": "activityType", "ns19:value": "read"}], "ns19:viewUUID": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "ns19:binding": "tw.local.odcRequest.FcCollections"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "39acdc23-0cb1-4b9b-8af9-055facf20675", "ns19:layoutItemId": "Financial_Details_FO_CV1", "ns19:configData": [{"ns19:id": "3e6fd210-6f53-47fb-89fd-02f1065fd681", "ns19:optionName": "@label", "ns19:value": "Financial Details FO"}, {"ns19:id": "21c61e1c-e927-439d-80f9-6e10a7ca8f1c", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "823c4dca-d9ba-43f8-8271-80d3ba6eb6a3", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "d66b93b2-a6fd-449f-8cd7-0af835ce413f", "ns19:optionName": "financialDetailsFOVis", "ns19:value": "READONLY"}, {"ns19:id": "a725b13c-c076-44a5-8df3-9b35e471b9b3", "ns19:optionName": "paymentTermsVis", "ns19:value": "READONLY"}, {"ns19:id": "af99d90e-d14e-4e76-8d12-27c76409e450", "ns19:optionName": "act3VIS", "ns19:value": "READONLY"}, {"ns19:id": "563ed960-18aa-4cab-874b-2d9362e6edfa", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "3abc8a36-e9e5-41c9-8c3d-d25e2a25447f", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "tw.local.multiTenorDatesVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "337da150-bf50-4054-88c1-49e953754129", "ns19:optionName": "documentAmount", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.documentAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "ad5cf30d-96df-40d1-8499-a9fe6790e06b", "ns19:optionName": "amountAdvanced", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.amountAdvanced", "ns19:valueType": "dynamic"}, {"ns19:id": "1ce513e5-79c6-4b45-8734-ed4407ec2372", "ns19:optionName": "todayDate", "ns19:value": "tw.local.todayDate", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1", "ns19:binding": "tw.local.odcRequest.FinancialDetailsFO"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "7ea29daf-f330-4c06-83d0-d09cb2294f05", "ns19:layoutItemId": "Importer_Details_CV1", "ns19:configData": [{"ns19:id": "036150e5-afe4-403d-8705-8a378cce2928", "ns19:optionName": "@label", "ns19:value": "Importer Details"}, {"ns19:id": "0f48ee69-fb3a-44f1-8fb4-ed372b91677e", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "8d666992-ffa8-48b2-8785-4d8a0b5ef886", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "56c2e32a-6b19-4c71-8dd9-86e0e7b71782", "ns19:optionName": "importerDetailsVis", "ns19:value": "\"READONLY\""}, {"ns19:id": "********-5f41-4108-8203-1dd673310dbc", "ns19:optionName": "bankRefVIS", "ns19:value": "tw.local.bankRefVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "b65aa889-3d9c-4e86-8563-0afdcccc7408", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "6376cd1f-6820-4ad6-8922-41c4d62368ae", "ns19:optionName": "importerDetailsCVVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.0ff96fd8-0740-4d17-887e-a56c8ef7921b", "ns19:binding": "tw.local.odcRequest.ImporterDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "91393cb7-a80b-4654-8d44-aeb3361ddf58", "ns19:layoutItemId": "Products_and_Shipment_Details_CV1", "ns19:configData": [{"ns19:id": "35f475fd-171c-4029-882c-c135dcdbdae7", "ns19:optionName": "@label", "ns19:value": "Products /Shipment"}, {"ns19:id": "34d55ca0-0683-43dd-88cb-b775ba58ee68", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "deea6fd8-824d-4bcd-8b85-eb2eec425d4b", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "e420d8fb-dd34-469a-83b7-2815146527f5", "ns19:optionName": "productsVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "0c681230-2d2f-4793-8df9-7879a15fd523", "ns19:optionName": "shipmentVis", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.5c068fcf-**************-6bb898b38609", "ns19:binding": "tw.local.odcRequest.ProductShipmentDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "e4c436c6-bde6-4ece-8195-69317186c81b", "ns19:layoutItemId": "Contract_Creation_CV1", "ns19:configData": [{"ns19:id": "78424390-c9f9-4180-832a-2945360b17d9", "ns19:optionName": "@label", "ns19:value": "Contract Creation"}, {"ns19:id": "8d7d6f50-7e3e-4d8a-84d0-b20fc7464e9b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "283b2a37-5768-4b70-80c9-a47fb71fd141", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "51195ce9-767c-45d3-8481-5367bdf3e4be", "ns19:optionName": "contractCreationVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "54f294e6-26d0-491d-873f-cbcad6d148ce", "ns19:optionName": "nbeCollectableAmount", "ns19:value": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "e1a51802-a34f-4917-8373-c2756aec09fe", "ns19:optionName": "bpmRequestNumber", "ns19:value": "tw.local.odcRequest.BasicDetails.flexCubeContractNo", "ns19:valueType": "dynamic"}, {"ns19:id": "4a27d1d7-6935-4043-899c-7915c0181201", "ns19:optionName": "contractStage", "ns19:value": "tw.local.odcRequest.BasicDetails.contractStage", "ns19:valueType": "dynamic"}, {"ns19:id": "b557b54a-3405-4a52-8f34-0e8b5a3579d8", "ns19:optionName": "currency", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.currency.value", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.0f40f56d-733f-4bd5-916c-92ae7dccbb10", "ns19:binding": "tw.local.odcRequest.ContractCreation"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "5cccde2b-0339-4e94-8920-45cd2821e324", "ns19:layoutItemId": "ODC_Parties_CV1", "ns19:configData": [{"ns19:id": "1dc58519-040f-4063-833d-49d566346322", "ns19:optionName": "@label", "ns19:value": "Parties"}, {"ns19:id": "748d67c3-e6e7-4fe8-8923-c6f60a6fb20b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "cab5fe53-2658-45cd-8858-bfe9f9b9a18f", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "c38de764-bacf-4e00-8817-246275c0ef0e", "ns19:optionName": "odcPartiesCVVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "a6524159-5c85-4337-80d0-2a748d5d0a22", "ns19:optionName": "odcPartiesCVBTNVIS", "ns19:value": "None"}, {"ns19:id": "db591f50-fcab-4709-816a-988d6bd9d5ec", "ns19:optionName": "retrieveCifInfo", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "0a629296-ffee-44fa-881d-691bc8dc569e", "ns19:optionName": "<PERSON><PERSON><PERSON><PERSON>", "ns19:value": "true"}, {"ns19:id": "85245668-71d0-4253-8a53-64bd6350fd71", "ns19:optionName": "isReturned", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "266aa38a-9349-4ecf-8833-4d1ac3f01ed1", "ns19:optionName": "addressBICList", "ns19:value": "tw.local.addressBICList[]", "ns19:valueType": "dynamic"}, {"ns19:id": "415025bf-0f42-4610-8755-ebb28b862227", "ns19:optionName": "partyTypeName", "ns19:value": "tw.local.partyTypeName", "ns19:valueType": "dynamic"}, {"ns19:id": "5fa6b05b-602b-4ba5-8a47-7a6bc0ee7755", "ns19:optionName": "customerFullDetails", "ns19:value": "tw.local.customerFullDetails", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.3d608948-c1fa-4ac3-8d4b-6a42f90a5132", "ns19:binding": "tw.local.odcRequest.Parties"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "90e3ba02-57ac-4d85-88ad-1f5f63a46ab9", "ns19:layoutItemId": "Charges_And_Commissions_CV1", "ns19:configData": [{"ns19:id": "8fee4f09-482d-4f48-818a-516c371ebc1d", "ns19:optionName": "@label", "ns19:value": "Charges &Commissions"}, {"ns19:id": "4024180d-f533-4304-877c-1a846b968ad3", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "8975c82b-f1bb-4426-84ec-36f8b52e7280", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "6baa1542-76f3-4677-8439-8372bfd43ada", "ns19:optionName": "chargesAndCommVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "4fe2efd7-a111-4df0-85b2-ce563b077cbf", "ns19:optionName": "btnVis", "ns19:value": "None"}, {"ns19:id": "dbeccd3f-3a14-427a-8e48-685f1948edda", "ns19:optionName": "commissionSectionVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "5eac6017-7391-4be4-8f43-36047375745f", "ns19:optionName": "amountCollectableByNBE", "ns19:value": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "63a08a22-4980-47ac-8c53-8aaca4964722", "ns19:optionName": "index", "ns19:value": "tw.local.index", "ns19:valueType": "dynamic"}, {"ns19:id": "22f0ee9e-3f63-410d-833b-88bd200d52b8", "ns19:optionName": "calculatedChangeAmnt", "ns19:value": "tw.local.calculatedChangeAmnt", "ns19:valueType": "dynamic"}, {"ns19:id": "25bd8fe4-d29a-4540-82ed-bd0b9a6f2a74", "ns19:optionName": "chargesCustomerAccountList", "ns19:value": "tw.local.odcRequest.customerAndPartyAccountList[]", "ns19:valueType": "dynamic"}, {"ns19:id": "bc14e93b-832d-419c-8254-371abb231d14", "ns19:optionName": "exRate", "ns19:value": "tw.local.exRate", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "ns19:binding": "tw.local.odcRequest.ChargesAndCommissions[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f5bd2e4d-**************-3776041b1aca", "ns19:layoutItemId": "Attachment_Comments_View1", "ns19:configData": [{"ns19:id": "96edc205-324f-4941-8ed6-d3141ae5980c", "ns19:optionName": "@label", "ns19:value": "Attachment"}, {"ns19:id": "deb225d5-aae7-42dd-8f40-cc96b236244f", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "97a68835-5a68-4cff-8473-7353d6e553d1", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "b04ce35d-a50b-4cc7-8f02-9314ee8673a9", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "07fb3834-54f4-422d-8aad-ab130810af0c", "ns19:optionName": "canUpdate", "ns19:value": "false"}, {"ns19:id": "f6acd6a0-609c-4413-8607-ef20c0c9a74c", "ns19:optionName": "remittanceLetter<PERSON>ath", "ns19:value": "tw.local.odcRequest.folderPath", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "96fc0cf4-7a48-4343-8803-d4a943144702", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "d3883651-8f97-4c3e-84c4-da71c0036c56", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "0acbd55c-6512-435f-8e8c-b00211185c35", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "d53f1d55-6aaf-455d-8031-c9c2cb582514", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:sequenceFlow": [{"sourceRef": "72a57c58-1959-4daf-9f06-f1b9170feb41", "targetRef": "2025.cc1ecdab-a272-4b43-8a48-0488be71b66c", "name": "To Coach", "id": "2027.eb0577af-381e-45da-b582-253e20167664", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.d66ea097-e44f-4cdb-80d7-56787db47e15", "targetRef": "2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a", "name": "To ACT05", "id": "2027.fe407a1a-943e-4385-81f2-fdd0cd009af0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a", "targetRef": "2025.d66ea097-e44f-4cdb-80d7-56787db47e15", "name": "To Stay on page", "id": "2027.71a6d402-3d7f-42ec-a438-ecb665ed4474", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topLeft", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "4c24e95a-00e6-4177-bb4a-2b3e774a2b58", "ns3:coachEventPath": "DC_Templete1/saveState"}}}, {"sourceRef": "2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a", "targetRef": "2025.641a0c30-7293-44e2-88fe-45f081e293b7", "name": "To End", "id": "2027.f4fc1f44-14ec-4fe6-8509-f7597d4dc37a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "f3108d09-aaa2-4149-aefd-66780575c2fc", "ns3:coachEventPath": "DC_Templete1/submit"}}}, {"sourceRef": "2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e", "targetRef": "2025.6554c1b1-2d24-46de-8d7f-adca48b9b791", "name": "yes", "id": "2027.b17f7677-d5ae-498f-8297-16ac73254928", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e", "targetRef": "2025.d779b83f-6bdb-442c-8e7c-60c53ba6331c", "name": "no", "id": "2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.641a0c30-7293-44e2-88fe-45f081e293b7", "targetRef": "2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e", "name": "To Exclusive Gateway", "id": "2027.f609d65c-c7be-4e5c-8298-d96641b30a09", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.cc1ecdab-a272-4b43-8a48-0488be71b66c", "targetRef": "2025.7b988368-8e88-4e1e-8b8b-78d59f87eb1a", "name": "To Get Actions", "id": "2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.6554c1b1-2d24-46de-8d7f-adca48b9b791", "targetRef": "2025.bdb6c456-66a6-432e-8861-99065124f8df", "name": "To History", "id": "2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.bdb6c456-66a6-432e-8861-99065124f8df", "targetRef": "2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd", "name": "To End", "id": "2027.fe456fee-5993-45ed-8fb5-af30880abfdc", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd", "targetRef": "2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543", "name": "To Audited", "id": "2027.51be54a0-ee9a-46a0-8136-623c05259495", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543", "targetRef": "2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30", "name": "To terminate?", "id": "2027.b912c63a-7ea5-4785-81b6-21cce02ed77e", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.********-72f3-46ed-8fed-7236b2c2451f", "targetRef": "2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd", "name": "To Audited?", "id": "2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd", "targetRef": "2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd", "name": "Yes", "id": "2027.a29fe9cb-1812-412d-8b73-28a3fa508012", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd", "targetRef": "2025.024c598d-f089-41f0-8ea8-5038ef7741e2", "name": "No", "id": "2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543", "targetRef": "2025.024c598d-f089-41f0-8ea8-5038ef7741e2", "name": "No", "id": "2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30", "targetRef": "2025.ecfb199d-d6ea-4243-8c36-180ca040be95", "name": "To Cancel and Delete Transactions", "id": "2027.7041aaff-918a-4429-8eaa-cede08b4ac2f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.terminateRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.ecfb199d-d6ea-4243-8c36-180ca040be95", "targetRef": "2025.37b03ea4-b507-4006-8d61-bcdf1b904a87", "name": "Yes", "id": "2027.d83bd288-4780-447f-8096-5e052cd0618d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30", "targetRef": "2025.d5d5c651-2ba4-4499-8473-2674156f23f5", "name": "To End", "id": "2027.29500b3c-9929-4033-83eb-882732e5ba10", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.37b03ea4-b507-4006-8d61-bcdf1b904a87", "targetRef": "2025.a15aa745-1422-4867-839a-4d4db1c1696c", "name": "To End", "id": "2027.179c02bc-3983-4cf4-8303-7d1af33a66d3", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.a15aa745-1422-4867-839a-4d4db1c1696c", "targetRef": "2025.d5d5c651-2ba4-4499-8473-2674156f23f5", "name": "<PERSON><PERSON> of To End", "id": "2027.1f66477a-c795-4cd2-85e5-69baba4d46f0", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.a15aa745-1422-4867-839a-4d4db1c1696c", "targetRef": "2025.e46993c6-1ffa-4e7f-8639-d4bb8ad09266", "name": "Co<PERSON> of To Stay on page 3", "id": "2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:intermediateThrowEvent": [{"name": "Stay on page", "id": "2025.d66ea097-e44f-4cdb-80d7-56787db47e15", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "330", "y": "48", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.fe407a1a-943e-4385-81f2-fdd0cd009af0"}, "ns16:incoming": "2027.71a6d402-3d7f-42ec-a438-ecb665ed4474", "ns16:outgoing": "2027.fe407a1a-943e-4385-81f2-fdd0cd009af0", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.d779b83f-6bdb-442c-8e7c-60c53ba6331c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "673", "y": "94", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.024c598d-f089-41f0-8ea8-5038ef7741e2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1225", "y": "277", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f", "2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0"], "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 3", "id": "2025.e46993c6-1ffa-4e7f-8639-d4bb8ad09266", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1817", "y": "437", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4", "ns3:stayOnPageEventDefinition": ""}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.13397444-fcf2-44ca-85db-86d46bd4b025"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorPanelVIS", "id": "2056.360b5581-289c-4fcd-8a20-a1a9c5821c4e"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.9cb38119-f2b6-4f1b-843f-8f1426417c61"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "deliveryTerms", "id": "2056.2aacc7a1-6a1b-4f1e-889a-e0502b64ae53"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "paymentTerms", "id": "2056.1724623e-dec9-451a-88bb-0042a05b292c"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "specialInstructions", "id": "2056.7a11a7d0-f02c-494b-8c8a-ff6d12f903bc"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "instructions", "id": "2056.a85b81e8-27f0-4c72-89a6-890c4e75bcc0"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestTypeVIS", "id": "2056.d6b87c98-933a-45ee-8861-663b4bc5a301"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNoVIS", "id": "2056.f9f460a8-56f2-41eb-8762-f7f00a779817"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.91daf81c-44bd-470f-83ca-9ccde1ada22d"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "bankRefVIS", "id": "2056.a9f4dd55-7f06-4d5e-817f-634f609c7cdd"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "multiTenorDatesVIS", "id": "2056.a7380a59-4bb7-47bb-8240-cb7d2a4dbc67"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "terminationReasonVIS", "id": "2056.040b6028-73e3-497d-84fb-e406efe1bbd1"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.53c3ca54-28d1-48c3-8287-553300571d94"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestIdStr", "id": "2056.8536ea82-5ea8-447a-8b31-f62165a54be0"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "addressBICList", "id": "2056.5d21f0ba-5415-494b-8f93-452d3c74b1ef"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "partyTypeName", "id": "2056.5cbb628b-1479-468f-8f1b-b7af7c682bee"}, {"itemSubjectRef": "itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "isCollection": "false", "name": "customerFullDetails", "id": "2056.23299dd3-3b7e-45b1-86aa-6e7dfced47c2"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "index", "id": "2056.11a3ceb5-2045-4417-8d63-f05962edc0bf"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "calculatedChangeAmnt", "id": "2056.b535c6db-f211-4076-8a49-3d5afd5b2fca"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "exRate", "id": "2056.1f5074e5-603e-464e-8b83-a6a931d522f4"}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "todayDate", "id": "2056.7267a014-7afe-4b66-81f4-b7526ae4e163", "ns16:extensionElements": {"ns3:defaultValue": {"_": "new Date()", "useDefault": "true"}}}], "ns16:exclusiveGateway": [{"default": "2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f", "name": "Exclusive Gateway", "id": "2025.1019b59f-c146-44b4-8ea4-a8bf78a6426e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "685", "y": "194", "width": "32", "height": "32"}}, "ns16:incoming": "2027.f609d65c-c7be-4e5c-8298-d96641b30a09", "ns16:outgoing": ["2027.b17f7677-d5ae-498f-8297-16ac73254928", "2027.8f10f083-5ac8-4bb4-8189-e66834c9f11f"]}, {"default": "2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0", "name": "Audited", "id": "2025.a92c0fd9-bfcd-4a03-8d42-9597c3335543", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1345", "y": "198", "width": "32", "height": "32"}}, "ns16:incoming": "2027.51be54a0-ee9a-46a0-8136-623c05259495", "ns16:outgoing": ["2027.b912c63a-7ea5-4785-81b6-21cce02ed77e", "2027.b3543828-fc6e-4ecc-8677-7f32c2715fd0"]}, {"default": "2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f", "name": "Audited?", "id": "2025.7c6cf2c4-23ba-4d39-8e6c-b0170a8630dd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1137", "y": "198", "width": "32", "height": "32"}}, "ns16:incoming": "2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0", "ns16:outgoing": ["2027.a29fe9cb-1812-412d-8b73-28a3fa508012", "2027.4dd0bab9-e0f0-42ae-8e50-f6462952b08f"]}, {"default": "2027.29500b3c-9929-4033-83eb-882732e5ba10", "name": "terminate?", "id": "2025.0e2e3571-97f7-49df-8aa9-10c6f555fd30", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1417", "y": "198", "width": "32", "height": "32"}}, "ns16:incoming": "2027.b912c63a-7ea5-4785-81b6-21cce02ed77e", "ns16:outgoing": ["2027.7041aaff-918a-4429-8eaa-cede08b4ac2f", "2027.29500b3c-9929-4033-83eb-882732e5ba10"]}, {"default": "2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4", "name": "Copy of Exclusive Gateway", "id": "2025.a15aa745-1422-4867-839a-4d4db1c1696c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1835", "y": "250", "width": "32", "height": "32"}}, "ns16:incoming": "2027.179c02bc-3983-4cf4-8303-7d1af33a66d3", "ns16:outgoing": ["2027.1f66477a-c795-4cd2-85e5-69baba4d46f0", "2027.eaf578aa-1775-4c8f-8dd3-87e040aadeb4"]}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "default": "2027.f609d65c-c7be-4e5c-8298-d96641b30a09", "name": "Validation script", "id": "2025.641a0c30-7293-44e2-88fe-45f081e293b7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "527", "y": "175", "width": "95", "height": "70", "color": "#95D087"}}, "ns16:incoming": "2027.f4fc1f44-14ec-4fe6-8509-f7597d4dc37a", "ns16:outgoing": "2027.f609d65c-c7be-4e5c-8298-d96641b30a09", "ns16:script": " tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)\r\r\n\tmandatory(tw.local.odcRequest.stepLog.terminateReason, \"tw.local.odcRequest.stepLog.terminateReason\");\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length < len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n\t\r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";"}, {"scriptFormat": "text/x-javascript", "default": "2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99", "name": "Init Script", "id": "2025.cc1ecdab-a272-4b43-8a48-0488be71b66c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "180", "y": "177", "width": "95", "height": "70"}}, "ns16:incoming": "2027.eb0577af-381e-45da-b582-253e20167664", "ns16:outgoing": "2027.2c4e67d3-5913-45bb-8aa2-8d1fef601a99", "ns16:script": "tw.local.odcRequest.stepLog = {};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT05;\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT05;\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT05;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.lastAction;\r\r\n\r\r\nif(tw.local.lastAction != tw.epv.CreationActions.terminateRequest)\r\r\n\ttw.local.terminationReasonVIS =\"None\";\r\r\nelse\r\r\n\ttw.local.terminationReasonVIS =\"Editable\";\r\r\n\r\r\n\r\r\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\r\r\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\r\r\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\r\r\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\r\r\n\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\n//Parent Request Number\r\r\nif(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest){\r\r\n\ttw.local.parentRequestNoVIS = \"Readonly\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\n//Contract Stage\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\n/*Document Generation Section*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.requestTypeVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.requestTypeVIS = \"NONE\";\t\r\r\n}\r\r\n/*Importer Details Visibility*/\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.bankRefVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.bankRefVIS = \"NONE\";\t\r\r\n}\r\r\n/* Financial Detais Trade Fo*/\r\r\nif(tw.local.odcRequest.BasicDetails.paymentTerms.name == \"001\")\r\r\n{\r\r\n\ttw.local.multiTenorDatesVIS = \"None\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.multiTenorDatesVIS = \"Readonly\";\r\r\n}\r\r\n\r\r\ntw.local.errorPanelVIS=\"NONE\";"}, {"scriptFormat": "text/x-javascript", "default": "2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c", "name": "Setting Status and sub status", "id": "2025.6554c1b1-2d24-46de-8d7f-adca48b9b791", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "800", "y": "178", "width": "95", "height": "70"}}, "ns16:incoming": "2027.b17f7677-d5ae-498f-8297-16ac73254928", "ns16:outgoing": "2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c", "ns16:script": "if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.authorize)\r\r\n{\r\r\n\tif( (tw.local.odcRequest.requestType.value == tw.epv.RequestType.Create || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment ||tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate) \r\r\n\t&& tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter == true)\r\r\n\t{\r\r\n\ttw.local.odcRequest.appInfo.status    = \" Completed\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Printing Documents for Customer\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.appInfo.status    = \"Completed\";\r\r\n\t\ttw.local.odcRequest.appInfo.subStatus = \"Completed\";\r\r\n\t\ttw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;\r\r\n\t}\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Initiation\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Trade Fo Review\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.obtainApprovals){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing \";\r\r\n}\r\r\n\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"Terminated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Terminated\";\r\r\n}\r\r\n\r\r\n"}], "ns16:callActivity": [{"calledElement": "1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "default": "2027.fe456fee-5993-45ed-8fb5-af30880abfdc", "name": "Update request state and status in DB", "id": "2025.bdb6c456-66a6-432e-8861-99065124f8df", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "913", "y": "178", "width": "95", "height": "70"}}, "ns16:incoming": "2027.c9a82a4b-41ae-4229-8534-109a4aa2eb9c", "ns16:outgoing": "2027.fe456fee-5993-45ed-8fb5-af30880abfdc", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.a84d91ec-e620-4417-85c4-7dd7db58ff31", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.c5194a72-2de4-483f-823c-47d5b98b572c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.3974caa7-9f10-4f46-8275-17080a25476e", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b05449cf-c459-4405-809c-888b00e3e968", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b537f107-9e83-46e0-8979-6fe5796c7a7d", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.stepName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.8e0cea15-236a-4b79-86a8-f93756a4ac86", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "default": "2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0", "name": "Audit ODC Request", "id": "2025.********-72f3-46ed-8fed-7236b2c2451f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1038", "y": "178", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:outgoing": "2027.d2920026-1766-4a4b-8b5d-535d2c48d6a0", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.afad40c5-a38b-475c-8154-b4dabd94b6fe", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.254cf8eb-2743-4c53-8c52-e51c8c22884e", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.51be54a0-ee9a-46a0-8136-623c05259495", "name": "Audit Request History", "id": "2025.8e5726be-bb16-4c40-82bd-f0cf174b31fd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1227", "y": "179", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": ["2027.a29fe9cb-1812-412d-8b73-28a3fa508012", "2027.fe456fee-5993-45ed-8fb5-af30880abfdc"], "ns16:outgoing": "2027.51be54a0-ee9a-46a0-8136-623c05259495", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.epv.userRole.CACT05", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.812db3ff-6589-474c-bcc5-21fde39e4d25", "default": "2027.d83bd288-4780-447f-8096-5e052cd0618d", "name": "Cancel and Delete Transactions", "id": "2025.ecfb199d-d6ea-4243-8c36-180ca040be95", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1527", "y": "231", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.requestIdStr=tw.local.odcRequest.requestID+\"\";"}, "ns16:incoming": "2027.7041aaff-918a-4429-8eaa-cede08b4ac2f", "ns16:outgoing": "2027.d83bd288-4780-447f-8096-5e052cd0618d", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.8c25b7d2-65fe-4ffa-83d3-f054a2ad4209", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestIdStr", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.179c02bc-3983-4cf4-8303-7d1af33a66d3", "name": "cancel request", "id": "2025.37b03ea4-b507-4006-8d61-bcdf1b904a87", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1700", "y": "231", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.d83bd288-4780-447f-8096-5e052cd0618d", "ns16:outgoing": "2027.179c02bc-3983-4cf4-8303-7d1af33a66d3", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.55a76aa1-e513-4fd3-835f-7fe160120af7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.77d2b3d6-2a2a-4520-ad08-31686157d431", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:targetRef": "2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:endEvent": {"name": "End", "id": "2025.d5d5c651-2ba4-4499-8473-2674156f23f5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1919", "y": "201", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:preAssignmentScript": "tw.local.fromExechecker = true;"}, "ns16:incoming": ["2027.29500b3c-9929-4033-83eb-882732e5ba10", "2027.1f66477a-c795-4cd2-85e5-69baba4d46f0"]}, "ns3:htmlHeaderTag": {"id": "39b9fc11-9df3-4f89-8cee-a8b3336af51e", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:mobileReady": "true", "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "2bf28052-a3ae-44c2-8e4c-bb8cc287665e"}, {"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "7780c61b-9b9b-4cce-8b94-d4af8ace2955"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "bdf92c99-ced1-4689-8251-4062b731fa77"}, {"epvId": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "epvProcessLinkId": "4dbfdd3b-6a34-4801-8134-88edf761d198"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "7bc07a22-ff7d-4439-8815-e159b287f4cb"}, {"epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "epvProcessLinkId": "97615e59-a381-45f6-8ef0-fbf41c6b12e2"}, {"epvId": "21.062854b5-6513-4da8-84ab-0126f90e550d", "epvProcessLinkId": "426975fc-8954-4de0-8836-4d66a6d5dcaf"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.a58f5da6-00ea-41e6-8a5d-927ee74418fb"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.43609737-d968-42ad-9a14-2da88598005a", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, {"name": "regeneratedRemittanceLetterTitleVIS", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.e3114267-71c4-4261-8f02-f45b08a80f8c"}, {"name": "lastAction", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.09800ab8-e6d8-4ee9-8954-dc0c77df48f0"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a532e1f6-27cc-44ab-8ba6-1ab2ea92ec2d"}], "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.43126547-b78b-4e92-bab9-47081d86a36a"}, {"name": "fromExechecker", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.6b42b31e-df5e-4c9e-8914-5bda302e956c"}], "ns16:inputSet": {"id": "162c1eed-960c-4a0f-8775-a9bb325b098b"}, "ns16:outputSet": {"id": "7f7e65ee-bbc8-4b68-b5a7-d099d6c460c4"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ea31d42d-a362-41d2-8f59-f3007826a3f0", "processId": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.28156bcc-db35-451c-b58a-d8bfa74c5d8e", "2025.28156bcc-db35-451c-b58a-d8bfa74c5d8e"], "endStateId": "Out", "toProcessItemId": ["2025.9db66be0-b755-4230-b7e2-fbdeb1078b25", "2025.9db66be0-b755-4230-b7e2-fbdeb1078b25"], "guid": "c9a3e824-712d-4b79-a64b-65dc1d6cb24d", "versionId": "f874a5b7-749c-4d2c-ba5a-71fe5cdead3c", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}