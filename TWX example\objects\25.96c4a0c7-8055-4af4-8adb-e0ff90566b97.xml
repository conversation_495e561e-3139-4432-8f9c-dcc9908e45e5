<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.96c4a0c7-8055-4af4-8adb-e0ff90566b97" name="Trade Compliance Unified subprocess">
        <lastModified>1739179939907</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <bpdId>25.96c4a0c7-8055-4af4-8adb-e0ff90566b97</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef isNull="true" />
        <ownerTeamParticipantRef isNull="true" />
        <timeScheduleType isNull="true" />
        <timeScheduleName isNull="true" />
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName isNull="true" />
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone isNull="true" />
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>2</type>
        <rootBpdId>25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9</rootBpdId>
        <parentBpdId>25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9</parentBpdId>
        <parentFlowObjectId>96c4a0c7-8055-4af4-8adb-e0ff90566b97</parentFlowObjectId>
        <xmlData isNull="true" />
        <bpmn2Data isNull="true" />
        <dependencySummary isNull="true" />
        <jsonData isNull="true" />
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-ca5</guid>
        <versionId>3252646c-337c-4ca2-a04a-6f0942cdc552</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-142">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>Trade Compliance Unified subprocess</name>
            <documentation></documentation>
            <name>Trade Compliance Unified subprocess</name>
            <dimension>
                <size w="0" h="0" />
            </dimension>
            <author>bawadmin</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>true</isAtRiskCalcEnabled>
            <creationDate>1691052736153</creationDate>
            <modificationDate>1739179939907</modificationDate>
            <metricSettings itemType="2" />
            <instanceNameExpression>"Trade Compliance Unified subprocess:" + tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>1</dueDateTime>
            <dueDateTimeResolution>2</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://NBEdevBAW:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <holidayScheduleType>0</holidayScheduleType>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>false</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="4f8f2faa-bc76-45fe-86e2-fd76b446f7c5" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9cf" />
            <ownerTeamInstanceUI id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9d0" />
            <simulationScenario id="bpdid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-c2c">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1691052736319</startTime>
            </simulationScenario>
            <flow id="853aa405-4ef8-4ca4-89a1-6264a3ae8c29" connectionType="SequenceFlow">
                <name>To Escalation email service</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c0" />
                </connection>
            </flow>
            <flow id="700a2c9b-fbcb-49f8-8691-dd9afe661086" connectionType="SequenceFlow">
                <name>To End Event2</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8bf" />
                </connection>
            </flow>
            <flow id="a74def41-38fa-4044-872e-87314182eafc" connectionType="SequenceFlow">
                <name>To ACT07- Review ODC By Compliance</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8be" />
                </connection>
            </flow>
            <flow id="324570fb-767a-41e6-82b1-4caf0784ec32" connectionType="SequenceFlow">
                <name>To Subprocess End</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8bd" />
                </connection>
            </flow>
            <pool id="4f8f2faa-bc76-45fe-86e2-fd76b446f7c5">
                <name>Pool</name>
                <documentation></documentation>
                <dimension>
                    <size w="3000" h="400" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="dee7eb59-f491-4346-a98b-08a5b3b848cb">
                    <name>Compliance</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b09bd69-9498-4f11-beaf-41aba731a014</attachedParticipant>
                    <flowObject id="bc337dcc-6688-472a-8487-e99cca9b9d71" componentType="Activity">
                        <name>ACT07- Review ODC By Compliance</name>
                        <position>
                            <location x="224" y="38" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <narrative></narrative>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.7276be8c-00f4-4766-949c-bcd033b050c3</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>Number(tw.epv.ODCCreationSLA.CACT07)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject> Review Request by Trade Compliance  – مراجعة طلب تحصيل مستندى تصدير من إدارة الالتزام </subject>
                                <narrative></narrative>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9ca">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.6102a850-faf2-401e-9c7a-d8f3a6215da0</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c9">
                                    <name>ECMproperties</name>
                                    <classId>/12.b698dbfb-84da-40a5-9db3-676815055e65</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest.attachmentDetails.ecmProperties</value>
                                    <parameterId>2055.3502bdff-abe8-4815-b2bb-71d90d6236ce</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c8">
                                    <name>folderId</name>
                                    <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest.attachmentDetails.folderID</value>
                                    <parameterId>2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c7">
                                    <name>attachment</name>
                                    <classId>/12.07383e61-26ca-4c95-9b61-00fbdbc9735e</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest.attachmentDetails.attachment</value>
                                    <parameterId>2055.6c11359f-601f-4a8a-adab-80428b282316</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c6">
                                    <name>compApprovalInit</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.compApprovalInit</value>
                                    <parameterId>2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c5">
                                    <name>fromTradeFo</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                                    <input>true</input>
                                    <value>tw.local.fromTradeFo</value>
                                    <parameterId>2055.dd027994-1915-4bca-8452-c0b7021d8794</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c4">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.0f59046c-cb7e-4393-bec7-d27739cecd93</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c3">
                                    <name>complianceComments</name>
                                    <classId>/12.e4654440-58a7-47b2-8f98-3eaa9cccad49</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest.complianceComments</value>
                                    <parameterId>2055.36778ac9-0d81-4996-991e-f909c6f6aa5a</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c2">
                                    <name>attachment</name>
                                    <classId>/12.07383e61-26ca-4c95-9b61-00fbdbc9735e</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest.attachmentDetails.attachment</value>
                                    <parameterId>2055.f89a842a-9d83-45bf-adeb-2837b1beb056</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c1">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b09bd69-9498-4f11-beaf-41aba731a014</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c0">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b5">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="a74def41-38fa-4044-872e-87314182eafc" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b4">
                            <positionId>rightCenter</positionId>
                            <flow ref="324570fb-767a-41e6-82b1-4caf0784ec32" />
                        </outputPort>
                        <attachedEvent id="f3fc74cf-9590-49b2-8297-07492e9ce8f3" componentType="Event">
                            <name>Error</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="364c3569-8bef-4100-8997-cc68e719b1ca">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="b6a28059-e9a1-48d4-8661-e1d2216f08fd">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b2">
                                <positionId>bottomCenter</positionId>
                                <flow ref="853aa405-4ef8-4ca4-89a1-6264a3ae8c29" />
                            </outputPort>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9ba">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b9">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="90571470-899e-44db-b0da-bb2842927ca3" componentType="Event">
                        <name>Subprocess Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="25" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b6">
                            <positionId>rightCenter</positionId>
                            <flow ref="a74def41-38fa-4044-872e-87314182eafc" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="75ad6090-c994-431b-90d3-803dc6b15c5a" componentType="Event">
                        <name>Subprocess End</name>
                        <documentation></documentation>
                        <position>
                            <location x="650" y="80" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b3">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="324570fb-767a-41e6-82b1-4caf0784ec32" />
                        </inputPort>
                    </flowObject>
                </lane>
                <lane id="1a0244d0-d147-40ea-84e4-d4ff72885a2e">
                    <name>system</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</attachedParticipant>
                    <flowObject id="726c29b6-ca92-4806-8dc0-3cea258f1845" componentType="Activity">
                        <name>Escalation email service</name>
                        <documentation>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</documentation>
                        <position>
                            <location x="482" y="42" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.d7acf968-6740-4e52-b037-2049466eeeb2</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9be">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9bd">
                                    <name>taskId</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.taskID</value>
                                    <parameterId>2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9bc">
                                    <serviceType>1</serviceType>
                                    <teamRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9bb">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b1">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="853aa405-4ef8-4ca4-89a1-6264a3ae8c29" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b0">
                            <positionId>rightCenter</positionId>
                            <flow ref="700a2c9b-fbcb-49f8-8691-dd9afe661086" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="2285ed84-ab47-40b1-8b3e-5545fc5020f8" componentType="Event">
                        <name>End Event2</name>
                        <documentation></documentation>
                        <position>
                            <location x="662" y="65" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9af">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="700a2c9b-fbcb-49f8-8691-dd9afe661086" />
                        </inputPort>
                    </flowObject>
                </lane>
                <epv id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9cd">
                    <epvId>/21.dbbaa047-8f02-4397-b1b5-41f11b0256b3</epvId>
                </epv>
            </pool>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

