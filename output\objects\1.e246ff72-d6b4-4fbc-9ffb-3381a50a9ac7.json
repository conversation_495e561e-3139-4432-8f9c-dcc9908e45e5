{"id": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "versionId": "2867c960-8837-4a75-8b94-f1c61e5f5356", "name": "Get Required Documents 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "name": "Get Required Documents 2", "lastModified": "1699524074689", "lastModifiedBy": "so<PERSON>ia", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.ee42c9e4-14dd-461d-8072-1376f66937ca", "2025.ee42c9e4-14dd-461d-8072-1376f66937ca"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "dbcd6042-3abb-4774-ad38-731a1ea0befe", "versionId": "2867c960-8837-4a75-8b94-f1c61e5f5356", "dependencySummary": "<dependencySummary id=\"bpdid:651a1a6abf396537:64776e00:18baeba64af:300e\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"9f341f37-845b-4376-8132-c7b451771a28\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":265,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"d230b937-036c-4eea-9f13-b124395e692e\"},{\"incoming\":[\"62eeda52-f7c5-455b-ae03-d1f1dd120195\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":740,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bf\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"b869dae4-a686-4c35-b2d2-e5c96ec03d17\"},{\"outgoing\":[\"603f88b9-4f72-4af4-95ff-7d04e50dc660\"],\"incoming\":[\"9f341f37-845b-4376-8132-c7b451771a28\"],\"matchAllSearchCriteria\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":400,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[],\"activityType\":[\"ContentTask\"]},\"operationRef\":\"FOLDER_OP_GET_DOCS_IN_FOLDER\",\"implementation\":\"##WebService\",\"serverName\":\"FileNet\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TContentTask\",\"startQuantity\":1,\"name\":\"Get Documents In Folder\",\"dataInputAssociation\":[{\"targetRef\":\"FOLDER_ID\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.folderId\"]}}]},{\"targetRef\":\"SERVER_NAME\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.FileNet\"]}}]}],\"isForCompensation\":false,\"useMappedVariable\":true,\"completionQuantity\":1,\"id\":\"ee42c9e4-14dd-461d-8072-1376f66937ca\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.documents\"]}}],\"sourceRef\":[\"DOCUMENTS\"]}],\"orderOverride\":false},{\"targetRef\":\"886f3c4c-8291-4e97-8f45-fe20f41fe905\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Set Missing Documents\",\"declaredType\":\"sequenceFlow\",\"id\":\"603f88b9-4f72-4af4-95ff-7d04e50dc660\",\"sourceRef\":\"ee42c9e4-14dd-461d-8072-1376f66937ca\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMDocument();\\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMDocument();\\nautoObject[0].objectId = \\\"\\\";\\nautoObject[0].serverName = \\\"\\\";\\nautoObject[0].repositoryId = \\\"\\\";\\nautoObject[0].objectTypeId = \\\"\\\";\\nautoObject[0].name = \\\"\\\";\\nautoObject[0].contentURL = \\\"\\\";\\nautoObject[0].creationDate = new TWDate();\\nautoObject[0].createdBy = \\\"\\\";\\nautoObject[0].lastModificationDate = new TWDate();\\nautoObject[0].lastModifiedBy = \\\"\\\";\\nautoObject[0].versionLabel = \\\"\\\";\\nautoObject[0].isLatestVersion = false;\\nautoObject[0].isMajorVersion = false;\\nautoObject[0].isLatestMajorVersion = false;\\nautoObject[0].checkinComment = \\\"\\\";\\nautoObject[0].properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject[0].properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject[0].properties[0].objectTypeId = \\\"\\\";\\nautoObject[0].properties[0].value = null;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a\",\"name\":\"documents\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.b089d7c5-8abe-4c6e-85dc-de497dea0598\"},{\"startQuantity\":1,\"outgoing\":[\"62eeda52-f7c5-455b-ae03-d1f1dd120195\"],\"incoming\":[\"603f88b9-4f72-4af4-95ff-7d04e50dc660\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":547,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Missing Documents\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"886f3c4c-8291-4e97-8f45-fe20f41fe905\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"var docExist= false;\\r\\ntw.local.errorMessage=\\\"\\\";\\r\\n\\r\\nif (!!tw.local.documents && tw.local.documents.listLength > 0) {\\r\\n\\tfor (var j=0; j<tw.local.documents.listLength; j++) {\\r\\n\\r\\n\\t\\tif ( tw.local.documents[j].objectTypeId == \\\"ODCDocuments\\\") {  \\r\\n\\t\\t\\tdocExist= true\\r\\n\\t\\t\\tbreak;\\t\\t\\t\\t\\r\\n\\t\\t}\\r\\n\\t}\\/\\/enf of for loop\\r\\n}\\r\\n\\r\\n\\r\\nif (docExist==false)\\r\\n\\ttw.local.errorMessage=  \\\"<li>\\\"+ \\\"'Please upload 'Customer Request' Document\\\" +\\\"<\\/li>\\\";\\r\\n\\r\\n\\r\\n\"]}},{\"targetRef\":\"b869dae4-a686-4c35-b2d2-e5c96ec03d17\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Validate Required Documents\",\"declaredType\":\"sequenceFlow\",\"id\":\"62eeda52-f7c5-455b-ae03-d1f1dd120195\",\"sourceRef\":\"886f3c4c-8291-4e97-8f45-fe20f41fe905\"},{\"parallelMultiple\":false,\"outgoing\":[\"4610b27a-8a16-4a05-8f87-c990678fbd49\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"4eeb05a5-dc1a-498d-adf7-c0791003d21f\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"01c4868b-b445-44ec-8942-aebb2eb07d93\",\"otherAttributes\":{\"eventImplId\":\"332bc231-23bd-4cb6-85c6-4e6a676d92c2\"}}],\"attachedToRef\":\"ee42c9e4-14dd-461d-8072-1376f66937ca\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":435,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"aa99c25a-00df-42eb-bc9e-bbfb204715ab\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"afa728c5-f024-4357-8c31-7387ed4a6e03\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"6d33b726-33ff-47b7-a2bc-bb0fc855f0b8\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"c1b284cf-bafc-4daa-8aa2-29b46fcc5b0d\",\"otherAttributes\":{\"eventImplId\":\"c634a25f-d714-4b8d-8155-c3a041c5e0af\"}}],\"attachedToRef\":\"886f3c4c-8291-4e97-8f45-fe20f41fe905\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":582,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error2\",\"declaredType\":\"boundaryEvent\",\"id\":\"af930738-d36f-4e57-b8e6-f5d94314787a\",\"outputSet\":{}},{\"startQuantity\":1,\"outgoing\":[\"ae61db14-9c26-40ca-ab1f-e157a0e8a5e1\"],\"incoming\":[\"4610b27a-8a16-4a05-8f87-c990678fbd49\",\"afa728c5-f024-4357-8c31-7387ed4a6e03\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":547,\"y\":167,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Error Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Get Required Documents 2\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"53e7135a-0e89-45c0-9c92-da1f1c53c967\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"53e7135a-0e89-45c0-9c92-da1f1c53c967\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"4610b27a-8a16-4a05-8f87-c990678fbd49\",\"sourceRef\":\"aa99c25a-00df-42eb-bc9e-bbfb204715ab\"},{\"targetRef\":\"53e7135a-0e89-45c0-9c92-da1f1c53c967\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"afa728c5-f024-4357-8c31-7387ed4a6e03\",\"sourceRef\":\"af930738-d36f-4e57-b8e6-f5d94314787a\"},{\"targetRef\":\"27d806a4-e4b3-4c61-bb80-149825d59a5c\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"ae61db14-9c26-40ca-ab1f-e157a0e8a5e1\",\"sourceRef\":\"53e7135a-0e89-45c0-9c92-da1f1c53c967\"},{\"incoming\":[\"ae61db14-9c26-40ca-ab1f-e157a0e8a5e1\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"398d5181-6f76-4594-882c-a192a21cc100\",\"otherAttributes\":{\"eventImplId\":\"6a53d0e9-5805-45db-8c90-8f5da7292cc7\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":752,\"y\":190,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"dataInputAssociation\":[{\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}]}],\"declaredType\":\"endEvent\",\"id\":\"27d806a4-e4b3-4c61-bb80-149825d59a5c\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6d83ac2f-7a03-4af3-8ec1-93d2a246984d\"},{\"targetRef\":\"ee42c9e4-14dd-461d-8072-1376f66937ca\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Documents In Folder\",\"declaredType\":\"sequenceFlow\",\"id\":\"9f341f37-845b-4376-8132-c7b451771a28\",\"sourceRef\":\"d230b937-036c-4eea-9f13-b124395e692e\"}],\"laneSet\":[{\"id\":\"2b271906-eef7-4a77-bfbd-1a54490e89db\",\"lane\":[{\"flowNodeRef\":[\"d230b937-036c-4eea-9f13-b124395e692e\",\"b869dae4-a686-4c35-b2d2-e5c96ec03d17\",\"ee42c9e4-14dd-461d-8072-1376f66937ca\",\"886f3c4c-8291-4e97-8f45-fe20f41fe905\",\"aa99c25a-00df-42eb-bc9e-bbfb204715ab\",\"af930738-d36f-4e57-b8e6-f5d94314787a\",\"53e7135a-0e89-45c0-9c92-da1f1c53c967\",\"27d806a4-e4b3-4c61-bb80-149825d59a5c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"3294eae9-f954-453c-ab46-eb921f3f9ea6\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get Required Documents 2\",\"declaredType\":\"process\",\"id\":\"1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"49c3a2b6-10bf-4d4e-8cfb-b2cb91e8037b\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.f43a0726-0192-4d4c-942e-83e973ee5015\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"{D01E4D8A-0000-CA47-A825-DBDAE9D5B1EA}\\\"\"}]},\"itemSubjectRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"name\":\"folderId\",\"isCollection\":false,\"id\":\"2055.f43a0726-0192-4d4c-942e-83e973ee5015\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "folderId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.f43a0726-0192-4d4c-942e-83e973ee5015", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "parameterType": "1", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "seq": "1", "hasDefault": "true", "defaultValue": "\"{D01E4D8A-0000-CA47-A825-DBDAE9D5B1EA}\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "ed5d01b7-50f0-4777-b960-079d3859af87", "versionId": "8ff3b62c-13df-4e34-b201-80eeefb59e95"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9864425e-b387-4e66-beb1-eb699cd1143c", "versionId": "6b70533c-2dcc-493c-88ff-00e807b80051"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.92feb460-d2b0-4146-ad2f-5119229ca290", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "parameterType": "3", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "84", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "abf4db22-3339-4950-a140-b4454bd28537", "versionId": "f767f6fe-3628-4be1-816b-05a0c0b06419"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d669e07f-84fd-4ec6-8658-c277f3e3633a", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "146", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "83e63222-684f-492a-aa2a-a1dc5edd3374", "versionId": "b83d8f10-69cb-4382-9e96-b5c23dbaa23c"}, {"name": "ECMError", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2f9d58fe-75a7-457b-82cb-fa048aa891a8", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "parameterType": "3", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.bfb48b9b-8247-49fb-8f78-82b738b817a6", "seq": "147", "hasDefault": "false", "defaultValue": "ECMError", "isLocked": "false", "description": {"isNull": "true"}, "guid": "c0582e65-dd8a-4ee4-ae67-8daa483662fb", "versionId": "3a5bb757-bc05-4c81-966d-5b62cd523bc1"}], "processVariable": [{"name": "documents", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b089d7c5-8abe-4c6e-85dc-de497dea0598", "description": {"isNull": "true"}, "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.490f939c-3c6d-4ef7-9707-33b5b618877a", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMDocument();\r\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMDocument();\r\nautoObject[0].objectId = \"\";\r\nautoObject[0].serverName = \"\";\r\nautoObject[0].repositoryId = \"\";\r\nautoObject[0].objectTypeId = \"\";\r\nautoObject[0].name = \"\";\r\nautoObject[0].contentURL = \"\";\r\nautoObject[0].creationDate = new TWDate();\r\nautoObject[0].createdBy = \"\";\r\nautoObject[0].lastModificationDate = new TWDate();\r\nautoObject[0].lastModifiedBy = \"\";\r\nautoObject[0].versionLabel = \"\";\r\nautoObject[0].isLatestVersion = false;\r\nautoObject[0].isMajorVersion = false;\r\nautoObject[0].isLatestMajorVersion = false;\r\nautoObject[0].checkinComment = \"\";\r\nautoObject[0].properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject[0].properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject[0].properties[0].objectTypeId = \"\";\r\nautoObject[0].properties[0].value = null;\r\nautoObject", "guid": "64e60a46-9a95-4cb1-a355-e8949b4e9f52", "versionId": "d51e650e-a115-4c4f-b349-9978c33fcda3"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6d83ac2f-7a03-4af3-8ec1-93d2a246984d", "description": {"isNull": "true"}, "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "47a0bbec-b6fd-47df-95b7-0b241dc95a5e", "versionId": "9381d72c-b1da-4994-b6f2-490e4adb4753"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.27d806a4-e4b3-4c61-bb80-149825d59a5c", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.1826b540-ac6c-406d-943f-8d4107f85ae5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15c0", "versionId": "4b2d030d-d4f0-443d-9734-8e2fcb98a4ec", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "752", "y": "190", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.1826b540-ac6c-406d-943f-8d4107f85ae5", "message": "", "faultStyle": "1", "guid": "31294865-22da-4ef8-a846-2ca45f5232d3", "versionId": "db1171c1-88e3-495e-a004-261c5c62aaa6", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.50de3c73-6cb3-46be-8ceb-747c310cfd4b", "processParameterId": "2055.92feb460-d2b0-4146-ad2f-5119229ca290", "parameterMappingParentId": "3007.1826b540-ac6c-406d-943f-8d4107f85ae5", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "d7555ab2-9301-4e3e-88b4-eec8d244f049", "versionId": "d058d263-c424-4acd-9341-201804d0222c", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ee42c9e4-14dd-461d-8072-1376f66937ca", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "name": "Get Documents In Folder", "tWComponentName": "ECMConnector", "tWComponentId": "3030.dfe1dd92-3921-46ca-9749-71235080c403", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.53e7135a-0e89-45c0-9c92-da1f1c53c967", "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bb", "versionId": "882a4762-9a4e-4fe4-819c-a615efdb4be0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.b034d4ce-1c0d-496a-9b60-b7f3f920b0fa", "processItemId": "2025.ee42c9e4-14dd-461d-8072-1376f66937ca", "location": "1", "script": {"isNull": "true"}, "guid": "b0ec5808-cb67-4572-ac72-afdaa4fd6b93", "versionId": "c506b191-3c8f-4aed-b840-76c70b2e3991"}, "layoutData": {"x": "400", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bd", "errorHandlerItemId": "2025.53e7135a-0e89-45c0-9c92-da1f1c53c967", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "ecmConnectorId": "3030.dfe1dd92-3921-46ca-9749-71235080c403", "definition": "<config type=\"com.lombardisoftware.client.persistence.ECMConnectorConfiguration\">\r\r\n  <inputParameters>\r\r\n    <parameter>\r\r\n      <name>folderId</name>\r\r\n      <type>ECMID</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.folderId</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n    <parameter>\r\r\n      <name>serverName</name>\r\r\n      <type>String</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.env.FileNet</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>false</isList>\r\r\n    </parameter>\r\r\n  </inputParameters>\r\r\n  <outputParameters>\r\r\n    <parameter>\r\r\n      <name>documents</name>\r\r\n      <type>ECMDocument</type>\r\r\n      <description></description>\r\r\n      <defaultValue></defaultValue>\r\r\n      <argumentVariable>tw.local.documents</argumentVariable>\r\r\n      <isArray></isArray>\r\r\n      <isList>true</isList>\r\r\n    </parameter>\r\r\n  </outputParameters>\r\r\n  <matchAllSearchCriteria>true</matchAllSearchCriteria>\r\r\n  <searchParameters />\r\r\n  <selectParameters />\r\r\n  <orderOverride>false</orderOverride>\r\r\n  <orderOverrideValue></orderOverrideValue>\r\r\n  <operationType>FOLDER_OP_GET_DOCS_IN_FOLDER</operationType>\r\r\n  <server>FileNet</server>\r\r\n  <objectTypeSelection>DOCUMENT</objectTypeSelection>\r\r\n  <useMappedVariable>true</useMappedVariable>\r\r\n  <faultParameterId>2055.2f9d58fe-75a7-457b-82cb-fa048aa891a8</faultParameterId>\r\r\n</config>", "guid": "a0ba5120-ffa2-4ac6-b0ee-8d241b198e6f", "versionId": "4b1b9e6c-9780-4874-ae63-f2d978149691"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.886f3c4c-8291-4e97-8f45-fe20f41fe905", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "name": "Set Missing Documents", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.2b71e3fa-cf27-4bbd-a256-c843d66735b6", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.53e7135a-0e89-45c0-9c92-da1f1c53c967", "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15c1", "versionId": "9f95bbd3-755f-4fe2-a31f-799ed9f8a68d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "547", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bd", "errorHandlerItemId": "2025.53e7135a-0e89-45c0-9c92-da1f1c53c967", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.2b71e3fa-cf27-4bbd-a256-c843d66735b6", "scriptTypeId": "2", "isActive": "true", "script": "var docExist= false;\r\r\ntw.local.errorMessage=\"\";\r\r\n\r\r\nif (!!tw.local.documents && tw.local.documents.listLength > 0) {\r\r\n\tfor (var j=0; j<tw.local.documents.listLength; j++) {\r\r\n\r\r\n\t\tif ( tw.local.documents[j].objectTypeId == \"ODCDocuments\") {  \r\r\n\t\t\tdocExist= true\r\r\n\t\t\tbreak;\t\t\t\t\r\r\n\t\t}\r\r\n\t}//enf of for loop\r\r\n}\r\r\n\r\r\n\r\r\nif (docExist==false)\r\r\n\ttw.local.errorMessage=  \"<li>\"+ \"'Please upload 'Customer Request' Document\" +\"</li>\";\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "d642883d-1ce9-4b1d-867f-ccef66a909d2", "versionId": "eeed9cba-2690-49c7-8cc7-b2ab9b1ffc96"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.53e7135a-0e89-45c0-9c92-da1f1c53c967", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "name": "Erro<PERSON>", "tWComponentName": "SubProcess", "tWComponentId": "3012.118b4b87-c75f-4c98-858c-d84f432a308f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bd", "versionId": "c2495188-1822-4bbd-aaf9-3e04bb827354", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "547", "y": "167", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.118b4b87-c75f-4c98-858c-d84f432a308f", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "b9c6611c-7881-4c41-8760-5f6cf373a3d6", "versionId": "af12380e-8aae-4eb1-893f-39b720ed3377", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e269e728-c8bf-4fec-ba16-c50777f9e428", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.118b4b87-c75f-4c98-858c-d84f432a308f", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "b53ee722-e845-42f0-b8ed-8ffed7e70d5b", "versionId": "6d744249-f396-4c79-b550-43e766b55319", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ad55bc2e-f2b6-45b4-a67f-e3669e58d24e", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.118b4b87-c75f-4c98-858c-d84f432a308f", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "f5b92c90-a23f-4817-a5a3-413a2193f919", "versionId": "7a9daaa2-a239-40cf-8ce7-2e7b432e47de", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.bdd39a55-0cb2-44df-be83-418afb6d9ee7", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.118b4b87-c75f-4c98-858c-d84f432a308f", "useDefault": "false", "value": "\"Get Required Documents 2\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "24f0b301-d939-40ea-b382-ab8b1484efd3", "versionId": "8ae1b8ed-4787-4f90-83ea-79d0a2f3b526", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b869dae4-a686-4c35-b2d2-e5c96ec03d17", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.47c91513-31db-47f4-ae4d-38e85e4753db", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bf", "versionId": "ec4b32a0-e0b0-4899-9a9f-f2c124964e67", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "740", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.47c91513-31db-47f4-ae4d-38e85e4753db", "haltProcess": "false", "guid": "a40b46c3-a3a4-4f85-845a-95de83adb24e", "versionId": "5a42bcd0-5369-4daa-b513-7c1b56377f1e"}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.749a06f9-6105-4df6-be0c-8172b4a882f5", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "guid": "f8fe307f-7578-4f5f-9fa1-cc2a7eff3310", "versionId": "2f529315-2a93-4f5c-8a3b-d4e453cb8080"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "265", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Required Documents 2", "id": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "49c3a2b6-10bf-4d4e-8cfb-b2cb91e8037b"}}}, "ns16:dataInput": {"name": "folderId", "itemSubjectRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01", "isCollection": "false", "id": "2055.f43a0726-0192-4d4c-942e-83e973ee5015", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"{D01E4D8A-0000-CA47-A825-DBDAE9D5B1EA}\"", "useDefault": "true"}}}, "ns16:dataOutput": {"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b"}, "ns16:inputSet": {"ns16:dataInputRefs": "2055.f43a0726-0192-4d4c-942e-83e973ee5015"}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b"}}, "ns16:laneSet": {"id": "2b271906-eef7-4a77-bfbd-1a54490e89db", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "3294eae9-f954-453c-ab46-eb921f3f9ea6", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["d230b937-036c-4eea-9f13-b124395e692e", "b869dae4-a686-4c35-b2d2-e5c96ec03d17", "ee42c9e4-14dd-461d-8072-1376f66937ca", "886f3c4c-8291-4e97-8f45-fe20f41fe905", "aa99c25a-00df-42eb-bc9e-bbfb204715ab", "af930738-d36f-4e57-b8e6-f5d94314787a", "53e7135a-0e89-45c0-9c92-da1f1c53c967", "27d806a4-e4b3-4c61-bb80-149825d59a5c"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "d230b937-036c-4eea-9f13-b124395e692e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "265", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "9f341f37-845b-4376-8132-c7b451771a28"}, "ns16:endEvent": [{"name": "End", "id": "b869dae4-a686-4c35-b2d2-e5c96ec03d17", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "740", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15bf"}, "ns16:incoming": "62eeda52-f7c5-455b-ae03-d1f1dd120195"}, {"name": "End Event", "id": "27d806a4-e4b3-4c61-bb80-149825d59a5c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "752", "y": "190", "width": "24", "height": "24"}}, "ns16:incoming": "ae61db14-9c26-40ca-ab1f-e157a0e8a5e1", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:errorEventDefinition": {"id": "398d5181-6f76-4594-882c-a192a21cc100", "eventImplId": "6a53d0e9-5805-45db-8c90-8f5da7292cc7", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns4:contentTask": {"serverName": "FileNet", "operationRef": "FOLDER_OP_GET_DOCS_IN_FOLDER", "name": "Get Documents In Folder", "id": "ee42c9e4-14dd-461d-8072-1376f66937ca", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "400", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "ContentTask", "ns3:preAssignmentScript": ""}, "ns16:incoming": "9f341f37-845b-4376-8132-c7b451771a28", "ns16:outgoing": "603f88b9-4f72-4af4-95ff-7d04e50dc660", "ns16:dataInputAssociation": [{"ns16:targetRef": "FOLDER_ID", "ns16:assignment": {"ns16:from": {"_": "tw.local.folderId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:targetRef": "SERVER_NAME", "ns16:assignment": {"ns16:from": {"_": "tw.env.FileNet", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "DOCUMENTS", "ns16:assignment": {"ns16:to": {"_": "tw.local.documents", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a"}}}}, "ns16:sequenceFlow": [{"sourceRef": "ee42c9e4-14dd-461d-8072-1376f66937ca", "targetRef": "886f3c4c-8291-4e97-8f45-fe20f41fe905", "name": "To Set Missing Documents", "id": "603f88b9-4f72-4af4-95ff-7d04e50dc660", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "886f3c4c-8291-4e97-8f45-fe20f41fe905", "targetRef": "b869dae4-a686-4c35-b2d2-e5c96ec03d17", "name": "To Validate Required Documents", "id": "62eeda52-f7c5-455b-ae03-d1f1dd120195", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "aa99c25a-00df-42eb-bc9e-bbfb204715ab", "targetRef": "53e7135a-0e89-45c0-9c92-da1f1c53c967", "name": "To <PERSON><PERSON>r <PERSON>", "id": "4610b27a-8a16-4a05-8f87-c990678fbd49", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "af930738-d36f-4e57-b8e6-f5d94314787a", "targetRef": "53e7135a-0e89-45c0-9c92-da1f1c53c967", "name": "To <PERSON><PERSON>r <PERSON>", "id": "afa728c5-f024-4357-8c31-7387ed4a6e03", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "53e7135a-0e89-45c0-9c92-da1f1c53c967", "targetRef": "27d806a4-e4b3-4c61-bb80-149825d59a5c", "name": "To End", "id": "ae61db14-9c26-40ca-ab1f-e157a0e8a5e1", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}, {"sourceRef": "d230b937-036c-4eea-9f13-b124395e692e", "targetRef": "ee42c9e4-14dd-461d-8072-1376f66937ca", "name": "To Get Documents In Folder", "id": "9f341f37-845b-4376-8132-c7b451771a28", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.490f939c-3c6d-4ef7-9707-33b5b618877a", "isCollection": "true", "name": "documents", "id": "2056.b089d7c5-8abe-4c6e-85dc-de497dea0598", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.SYSCM.ECMDocument();\r\nautoObject[0] = new tw.object.toolkit.SYSCM.ECMDocument();\r\nautoObject[0].objectId = \"\";\r\nautoObject[0].serverName = \"\";\r\nautoObject[0].repositoryId = \"\";\r\nautoObject[0].objectTypeId = \"\";\r\nautoObject[0].name = \"\";\r\nautoObject[0].contentURL = \"\";\r\nautoObject[0].creationDate = new TWDate();\r\nautoObject[0].createdBy = \"\";\r\nautoObject[0].lastModificationDate = new TWDate();\r\nautoObject[0].lastModifiedBy = \"\";\r\nautoObject[0].versionLabel = \"\";\r\nautoObject[0].isLatestVersion = false;\r\nautoObject[0].isMajorVersion = false;\r\nautoObject[0].isLatestMajorVersion = false;\r\nautoObject[0].checkinComment = \"\";\r\nautoObject[0].properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject[0].properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject[0].properties[0].objectTypeId = \"\";\r\nautoObject[0].properties[0].value = null;\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.6d83ac2f-7a03-4af3-8ec1-93d2a246984d"}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Set Missing Documents", "id": "886f3c4c-8291-4e97-8f45-fe20f41fe905", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "547", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "603f88b9-4f72-4af4-95ff-7d04e50dc660", "ns16:outgoing": "62eeda52-f7c5-455b-ae03-d1f1dd120195", "ns16:script": "var docExist= false;\r\r\ntw.local.errorMessage=\"\";\r\r\n\r\r\nif (!!tw.local.documents && tw.local.documents.listLength > 0) {\r\r\n\tfor (var j=0; j<tw.local.documents.listLength; j++) {\r\r\n\r\r\n\t\tif ( tw.local.documents[j].objectTypeId == \"ODCDocuments\") {  \r\r\n\t\t\tdocExist= true\r\r\n\t\t\tbreak;\t\t\t\t\r\r\n\t\t}\r\r\n\t}//enf of for loop\r\r\n}\r\r\n\r\r\n\r\r\nif (docExist==false)\r\r\n\ttw.local.errorMessage=  \"<li>\"+ \"'Please upload 'Customer Request' Document\" +\"</li>\";\r\r\n\r\r\n\r\r\n"}, "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "ee42c9e4-14dd-461d-8072-1376f66937ca", "parallelMultiple": "false", "name": "Error1", "id": "aa99c25a-00df-42eb-bc9e-bbfb204715ab", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "435", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "4610b27a-8a16-4a05-8f87-c990678fbd49", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "4eeb05a5-dc1a-498d-adf7-c0791003d21f"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "01c4868b-b445-44ec-8942-aebb2eb07d93", "eventImplId": "332bc231-23bd-4cb6-85c6-4e6a676d92c2", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "886f3c4c-8291-4e97-8f45-fe20f41fe905", "parallelMultiple": "false", "name": "Error2", "id": "af930738-d36f-4e57-b8e6-f5d94314787a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "582", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "afa728c5-f024-4357-8c31-7387ed4a6e03", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "6d33b726-33ff-47b7-a2bc-bb0fc855f0b8"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "c1b284cf-bafc-4daa-8aa2-29b46fcc5b0d", "eventImplId": "c634a25f-d714-4b8d-8155-c3a041c5e0af", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}], "ns16:callActivity": {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Erro<PERSON>", "id": "53e7135a-0e89-45c0-9c92-da1f1c53c967", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "547", "y": "167", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["4610b27a-8a16-4a05-8f87-c990678fbd49", "afa728c5-f024-4357-8c31-7387ed4a6e03"], "ns16:outgoing": "ae61db14-9c26-40ca-ab1f-e157a0e8a5e1", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Get Required Documents 2\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}}}}, "link": [{"name": "To Set Missing Documents", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.603f88b9-4f72-4af4-95ff-7d04e50dc660", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ee42c9e4-14dd-461d-8072-1376f66937ca", "2025.ee42c9e4-14dd-461d-8072-1376f66937ca"], "endStateId": "Out", "toProcessItemId": ["2025.886f3c4c-8291-4e97-8f45-fe20f41fe905", "2025.886f3c4c-8291-4e97-8f45-fe20f41fe905"], "guid": "45b161db-**************-97c4b3ca2c70", "versionId": "1db96a4a-00e0-4792-aaa5-43f47740337c", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ae61db14-9c26-40ca-ab1f-e157a0e8a5e1", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.53e7135a-0e89-45c0-9c92-da1f1c53c967", "2025.53e7135a-0e89-45c0-9c92-da1f1c53c967"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.27d806a4-e4b3-4c61-bb80-149825d59a5c", "2025.27d806a4-e4b3-4c61-bb80-149825d59a5c"], "guid": "d34ef7b8-e8da-4825-bf7f-17ca28d5d29b", "versionId": "20c512b5-38e2-4534-a3f5-3f396fbb52e3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Validate Required Documents", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.62eeda52-f7c5-455b-ae03-d1f1dd120195", "processId": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.886f3c4c-8291-4e97-8f45-fe20f41fe905", "2025.886f3c4c-8291-4e97-8f45-fe20f41fe905"], "endStateId": "Out", "toProcessItemId": ["2025.b869dae4-a686-4c35-b2d2-e5c96ec03d17", "2025.b869dae4-a686-4c35-b2d2-e5c96ec03d17"], "guid": "3ba60f1b-2e93-44ec-bb4c-794697520fdf", "versionId": "a11b0381-14fc-4a7d-96e0-35a758b2498a", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}