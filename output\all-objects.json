{"totalCount": 280, "objects": [{"id": "25.96c4a0c7-8055-4af4-8adb-e0ff90566b97", "versionId": "3252646c-337c-4ca2-a04a-6f0942cdc552", "name": "", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "62.baa265b6-9a89-4025-a3ea-79bc6384c47d", "versionId": "79e9ee66-4b14-4f6f-bd2a-36577d5a88b3", "name": "$$REST$BINDING$1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "type": "environmentVariableSet", "typeName": "Environment Variables", "details": {}}, {"id": "61.68f611c2-cc89-4e7e-ba1f-44a3b929e0c9", "versionId": "f2e117a2-64f3-4029-9e4f-9e50dfb1eef5", "name": "0aacb363-83b1-4d0a-959d-7ab705b08e5c.zip", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "12.90d4772d-4081-4a73-a8c2-e7f904511cd6", "versionId": "9a9a9242-0b9c-4928-83e5-7caba133c00d", "name": "AccountDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1", "versionId": "1bd76b05-5153-462d-993b-a53d8e782b07", "name": "Act01 - Create or Amend ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "versionId": "99d1d41f-38c8-4a04-b60c-56bdd6a0e721", "name": "ACT02 Review ODC Request By Compliance Rep", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046", "versionId": "b5c8a00c-4a4c-420f-95bd-6203311f9b9a", "name": "Act03 -  Review ODC Request by Trade Front Office", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "versionId": "69ae4197-2b05-43e5-b85f-c7987517a34b", "name": "ACT04 - ODC Execution Hub Initiation", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e", "versionId": "94c77e27-2519-45af-b545-d09e381df821", "name": "ACT04 - ODC Execution Hub Initiation 2", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.9db9271e-48cd-403e-accb-5dcc0e326e99", "versionId": "aabe6006-a38e-4f86-b9a0-8898fe3408db", "name": "ACT04 - ODC Execution Hub Initiation 3", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.6d4e9c1d-5624-494c-96fb-3bd94584f975", "versionId": "70fbc4e8-bbe0-438f-a2d2-24bceb75bd61", "name": "Act05 - ODC Execution Hub – Initiation Review", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9", "versionId": "d2ff0bc2-3f2f-40de-8897-6c929dcae36d", "name": "ACT06 -Print Remittance Letter", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "12.004a1efa-0a17-40a6-a5b9-125042216ff4", "versionId": "d245357c-55f6-4b91-a732-7c4043636e80", "name": "ActionConditions", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285", "versionId": "bc13d0da-10ab-416c-b52c-f7897f3c5bf3", "name": "Ajax Get Customer Account Details", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f", "versionId": "d14a310e-5b6f-4028-93bd-25d5318f176b", "name": "Ajax get customer accounts", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.6967c8b4-3d3e-41ac-b2ad-0effe7b6f871", "versionId": "62f7d590-81c1-43b2-8adf-64124adb1f3e", "name": "Ajax Get ODC Product codes", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "12.ed32bb17-02ab-4015-9dea-e9d2bc787f0d", "versionId": "a2d3b6a3-9f6a-44a5-9e05-ba9546a1fe10", "name": "AmountDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.06a9548f-e79a-4754-a93e-956a150c9a91", "versionId": "e9208e24-6700-4c0c-9ed4-d845f8935f5b", "name": "Approvals", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "versionId": "8dd42bf7-8ad9-4b16-a259-f0d503cb3f7c", "name": "Attachment", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "versionId": "2de00cf6-d27b-4cc6-81a2-d1b6bba2edef", "name": "Attachment", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "attach", "configOptions": ["canUpdate", "canCreate", "canDelete", "ECMProperties", "remittanceLetter<PERSON>ath"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.add = function() {\r\r\n//\talert(\"adding\");\r\r\n////\tthis.context.binding.get(\"value\").set(\"name\", \"Others\");\r\r\n////\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n////\t\r\r\n//}\r\r\n//this.context.options.selectedAttach.get(\"value\").get(\"name\")\r\r\n\r\r\nvar flag = false\r\r\nthis.makeflagtrue = function () {\r\r\n\tflag = true;\r\r\n\talert(\"makeing\");\r\r\n}\r\r\nthis.addAttacmentValidation=function () {\r\r\n\talert(flag);\r\r\n\tvar len = this.context.binding.get(\"value\").length();\r\r\n\tthis.context.binding.get(\"value\").get(len-1).set(\"name\", \"others\")\r\r\n//\t\r\r\n//\tthis.context.binding.get(\"value\").get(len).set(\"name\", \"Others\");\r\r\n//\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n\r\r\n\t\r\r\n}\r\r\nthis.visControl = function  () {\r\r\n\r\r\n\tif (this.context.options.visiable.get(\"value\")) {\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,false);\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}\r\r\nthis.setCMISquery = function  () {\r\r\n\tvar parent=this.context.options.parentPath;\r\r\n\tvar query =\"SELECT * FROM cmis:Document WHERE IN_TREE('\"+parent+\"')\";\r\r\n\tthis.context.options.cmisQuery.set(\"value\", query);\r\r\n}"}]}, "hasDetails": true}, {"id": "64.3361b968-662d-491d-8e02-666b6b3648ec", "versionId": "5bc4218b-087f-424c-9ba1-0eb7aced65ca", "name": "Attachment 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "attach", "configOptions": ["canUpdate", "canCreate", "canDelete", "ECMProperties", "remittanceLetter<PERSON>ath", "updateProperties"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.add = function() {\r\r\n//\talert(\"adding\");\r\r\n////\tthis.context.binding.get(\"value\").set(\"name\", \"Others\");\r\r\n////\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n////\t\r\r\n//}\r\r\n//this.context.options.selectedAttach.get(\"value\").get(\"name\")\r\r\n\r\r\nthis.test2 = function(){\r\r\n\tthis.context.options.updateProperties.get(\"value\").get(0).set(\"name\", \"Bate5aaa\");\r\r\n\tconsole.log(this.context.options.updateProperties.get(\"value\").items)\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\nvar flag = false\r\r\nthis.makeflagtrue = function () {\r\r\n\tflag = true;\r\r\n\talert(\"makeing\");\r\r\n}\r\r\nthis.addAttacmentValidation=function () {\r\r\n\talert(flag);\r\r\n\tvar len = this.context.binding.get(\"value\").length();\r\r\n\tthis.context.binding.get(\"value\").get(len-1).set(\"name\", \"others\")\r\r\n//\t\r\r\n//\tthis.context.binding.get(\"value\").get(len).set(\"name\", \"Others\");\r\r\n//\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n\r\r\n\t\r\r\n}\r\r\nthis.visControl = function  () {\r\r\n\r\r\n\tif (this.context.options.visiable.get(\"value\")) {\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,false);\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}\r\r\nthis.setCMISquery = function  () {\r\r\n\tvar parent=this.context.options.parentPath;\r\r\n\tvar query =\"SELECT * FROM cmis:Document WHERE IN_TREE('\"+parent+\"')\";\r\r\n\tthis.context.options.cmisQuery.set(\"value\", query);\r\r\n}"}]}, "hasDetails": true}, {"id": "12.55bb335a-d3b3-4749-a082-859e2a48ace9", "versionId": "3f34233e-558f-47d5-a5b8-eedb1dfd6349", "name": "attachmentDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.badb27e5-ab0e-4227-bebf-eb6d54984f36", "versionId": "18a28f54-72aa-4d3c-9b1c-26a3f9605bed", "name": "Audit Closure Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "versionId": "1382d1e5-4d3e-48a1-9c56-e2c1342710c9", "name": "Audit Create Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "versionId": "53c70d0a-2e2e-4c55-9973-e0c4eacf2f52", "name": "Audit ODC Update Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2d90a780-82bb-4941-b27e-d586607ce191", "versionId": "0df5b9dd-9586-48b7-9813-b17c9e15df1d", "name": "Audit Request History", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a1d1f1de-87c1-424f-8115-086a8221db8b", "versionId": "12879b4c-7ebd-4500-9f7c-a58760291453", "name": "Audit Reversal Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd", "versionId": "97315d56-b618-4e50-85da-f470f8f8aa63", "name": "Authorize FileNet", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "61.0ad6c2e7-6560-4811-9852-ad20a5b14c90", "versionId": "13e37789-efcf-4540-b4cf-495ebc139a05", "name": "avalon-framework-4.1.4.jar", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "61.44c2943b-f742-4b94-aced-a678f2523ee7", "versionId": "e0810ca2-f978-42ad-bd21-deaa1157966e", "name": "barcode.jar", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "61.6f022f05-805c-446f-a5a8-152a05e9016a", "versionId": "02b62376-9121-4348-9951-ebb6c6ca760d", "name": "barcode4j-2.0.jar", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "versionId": "122c2152-ac38-426a-a5d0-7bd7f51c5c49", "name": "Basic Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "this.valueDate = bpmext.ui.getView('/DC_Templete1/8/valueDate').setEnabled()", "bindingType": "basicDetails", "configOptions": ["parentRequestNoVis", "flexCubeContractNoVIS", "basicDetailsCVVIS", "contractStageVIS", "multiTenorDatesVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.multiTenorDatesVIS = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"paymentTerms\").get(\"name\") == \"001\")\r\r\n\t\tthis.context.options.multiTenorDatesVIS.set(\"value\", \"None\");\r\r\n\t\t\r\r\n\telse\r\r\n\t\tthis.context.options.multiTenorDatesVIS.set(\"value\", \"Editable\");\r\r\n}\r\r\n\r\r\nthis.testVis = function(){\r\r\n\tvar valueDateView = this.valueDate;\r\r\n}"}]}, "hasDetails": true}, {"id": "12.ba753452-90b9-4866-9471-8425f722be03", "versionId": "4a3d8dfb-993f-4c3d-bf4b-0c157fe190b3", "name": "BasicDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.2d7d54a5-0d98-443e-ad21-49ff93c6dc08", "versionId": "18eabf5b-4435-4046-a557-0c264e1a1f41", "name": "Bills", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.a3fab14a-01e9-4ed9-afdd-6f811c8f210d", "versionId": "82bbc204-9432-4081-b8a0-104b2219474c", "name": "BoxUtils", "type": "process", "typeName": "Process", "details": {"processType": "11"}, "subType": "11", "hasDetails": false}, {"id": "24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4", "versionId": "810f835e-ee93-4052-91c7-1db6459ebc54", "name": "Branch compliance Representative Checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.3bf11e60-c652-49be-a22b-e25b3ef79931", "versionId": "44277c59-ff55-499a-87f6-b1e01b622097", "name": "Branch compliance Representative Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "versionId": "1812600f-233f-4424-8b78-d0d82862e944", "name": "Branch Hub filter service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "24.d01bf37a-ec31-4585-aa1e-627d4ea6d162", "versionId": "c1d23dd7-28f1-44b1-81b1-1c4572f16307", "name": "Branch Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.a93a495e-ebfc-42ac-87e6-951e0ec1a877", "versionId": "b9faf003-3246-48ad-8bf7-3ccc7444d8b3", "name": "Branch Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "1.40a7d34d-1192-436a-ba5a-0360265d3261", "versionId": "c9c58a63-fe98-45f3-a2d6-d2f0f3f3f3ff", "name": "Calculate Change Amount", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd", "versionId": "44347ee5-e5d4-4e52-902a-0c75ba8f74fb", "name": "Calculate Default Amount Complete", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.812db3ff-6589-474c-bcc5-21fde39e4d25", "versionId": "772b74e8-d59f-4526-a7c0-52728951475d", "name": "Cancel and Delete Transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "versionId": "eb358d90-4d43-42df-a4a7-cd88da2d55ed", "name": "cancel request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.74c6fa59-aa38-49a4-89bc-f00692e89191", "versionId": "83f0eba5-cd01-40ef-b913-38fd871d516c", "name": "Charges And Commissions CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "chargesAndCommission", "configOptions": ["chargesAndCommVis", "btnVis", "amountCollectableByNBE", "addchargeBtn", "exchangeRate"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//console.log(\"outside vis function \"+this.context.options.addchargeBtn.get(\"value\"));\r\r\n\r\r\n//this.chargesSectionVIS = function()\r\r\n//{\r\r\n//\tvar addChargeBtn= this.context.options.addchargeBtn.get(\"value\") ;\r\r\n//\tconsole.log(\"inside vis function \"+addChargeBtn);\r\r\n//\t\r\r\n//\tif(addChargeBtn == false)\r\r\n//\t\tthis.ui.get(\"chargesAndCommVis\").setVisible(false,true);\r\r\n//\telse\r\r\n//debugger;\r\r\n//\t\tthis.ui.get(\"chargesVIS\").setVisible(true,true);\r\r\n//\t\tthis.ui.get(\"chargesVIS\").setVisible(true,true);\r\r\n//}\r\r\n\r\r\n//this.calculateDefaultAmount = function(index)\r\r\n//{console.log(\"inside  calculateDefaultAmount function\");\r\r\n//\tvar Index      = index.ui.getIndex();\r\r\n//\tvar rateType   = this.ui.get(\"rateType[\"+index+\"]\").getData();\r\r\n//\tvar flatAmount = this.ui.get(\"flatAmount[\"+index+\"]\").getData();\r\r\n//\tvar rate       = this.ui.get(\"rate[\"+index+\"]\").getData();\r\r\n//\t\r\r\n//\tconsole.log(\"index >>\"+Index);\r\r\n//\tconsole.log(\"rate type >>\"+rateType);\r\r\n//\tconsole.log(\"flat amount >>\"+flatAmount);\r\r\n//\t\r\r\n//\tif(this.ui.get(\"rateType[\"+Index+\"]\").getData() == \"flat amount\")\r\r\n//\t{\r\r\n//\t\tthis.ui.get(\"defaultAmount[\"+Index+\"]\").setData(flatAmount);\r\r\n//\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setData(flatAmount);\r\r\n//\t}\r\r\n//\telse if(this.ui.get(\"rateType[\"+Index+\"]\").getData() == \"fixed rate\")\r\r\n//\t{\r\r\n//\t\tvar result = (rate/100)* this.context.options.amountCollectableByNBE;\r\r\n//\t\t\r\r\n//\t\tthis.ui.get(\"defaultAmount[\"+Index+\"]\").setData(result);\r\r\n//\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setData(result)\r\r\n//\t\t\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//this.accountTypes = function (value){\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (value.getData() == \"Customer Account\"){\r\r\n//\t\tthis.ui.get(\"customerAccountNo[\"+index+\"]\").setEnabled(true);\r\r\n//\t\t\r\r\n//       \tthis.ui.get(\"accountBranchCode[\"+index+\"]\").setEnabled(false);\r\r\n//       \tthis.ui.get(\"accountCurrency[\"+index+\"]\").setEnabled(false);\r\r\n//\t\tthis.ui.get(\"glAccountNo[\"+index+\"]\").setEnabled(false);\r\r\n//    \t      this.ui.get(\"glAccountNo[\"+index+\"]\").setData(\"\");\r\r\n//    \t\t\r\r\n//\t}else if (value.getData() == \"GL Account\"){\r\r\n//\t\tthis.ui.get(\"customerAccountNo[\"+index+\"]\").setEnabled(false);\r\r\n//\t\t  this.ui.get(\"customerAccountNo[\"+index+\"]\").setData(\"\");\r\r\n//\t\t  \r\r\n//       \tthis.ui.get(\"accountBranchCode[\"+index+\"]\").setEnabled(true);\r\r\n//       \tthis.ui.get(\"accountCurrency[\"+index+\"]\").setEnabled(true);\r\r\n//\t\tthis.ui.get(\"glAccountNo[\"+index+\"]\").setEnabled(true);\r\r\n//    \t    \r\r\n//    }\r\r\n//}\r\r\n//\r\r\n//this.calculateDebitAmount = function (value){\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tvar changeAmount = this.ui.get(\"changeAmount[\"+index+\"]\").getData();\r\r\n//\tvar negRate = this.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").getData();\r\r\n//\tvar result = changeAmount * negRate;\r\r\n//\tthis.ui.get(\"debitedamount[\"+index+\"]\").setData(result);\r\r\n//}"}]}, "hasDetails": true}, {"id": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "versionId": "a72c1fc9-ee22-4196-9904-eb4eb8bfe774", "name": "Charges And Commissions CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (!this.context.options.isChecker.get(\"value\")) {\r\r\n\tthis.ui.get(\"GetChargesBtn\").click();\r\r\n}else{\r\r\n\tthis.ui.get(\"Vertical_Layout7\").setEnabled(false);\r\r\n}", "bindingType": "chargesAndCommission", "configOptions": ["amountCollectableByNBE", "accountList", "accountNo", "index", "commissionSectionVIS", "calculatedChangeAmnt", "chargesCustomerAccountList", "exRate", "<PERSON><PERSON><PERSON><PERSON>"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//On Change Amount and Negotiated Exchange Rate - calculate Debited Amount\r\r\nthis.calculateDebitedAmount = function(index){\r\r\n\tvar chargeAmount = this.context.binding.get(\"value\").get(index).get(\"changeAmount\");\r\r\n\tvar NegotiableRate = this.context.binding.get(\"value\").get(index).get(\"debitedAmount\").get(\"negotiatedExRate\");\r\r\n\tvar debitedAmount = chargeAmount * NegotiableRate;\r\r\n\t\r\r\n\tthis.context.binding.get(\"value\").get(index).get(\"debitedAmount\").set(\"amountInAccount\", debitedAmount);\r\r\n}\r\r\n\r\r\n//On Account Class - Set vis for GL and Customer account\r\r\n//this.accountNumVis = function(value){\r\r\n//\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\") == \"001\") {\r\r\n//\t    //Customer\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setData(\"\");\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t    }\r\r\n//\t\r\r\n//\t} else {\r\r\n//\t    //GL\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setData(null);\r\r\n//\t\r\r\n//\t        //GL account cant be overDrafted or not\r\r\n//\t        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setEnabled(false);\r\r\n//\t    }\r\r\n//\t}\r\r\n//\r\r\n//}\r\r\n\r\r\n//On Rate Type - Set Vis for Flat amount and fixed rate\r\r\nthis.rateTypeVis = function(value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rateType= value.getData();\r\r\n\tif(rateType!=null && rateType == \"Flat Amount\"){\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setData(0);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setData(0);\r\r\n\t}\r\r\n\telse if(rateType!=null && rateType == \"Fixed Rate\"){\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//====================================================================================================\r\r\n//Generic Validate Positive amount (accepts 0) (Change Amount)\r\r\nthis.validatePositive = function(value){\r\r\n\tif (value.getData() < 0) {\r\r\n\t\tvalue.setValid(false,\"Must be >= 0\");\r\r\n//\t\tvalue.setData(0.0);\r\r\n\t}else{\r\r\n\t\tvalue.setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//Generic Validate Digits Only (Account Branch Code)\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Change of Change Percentage - Validate Change percentage before calculate change amount\r\r\nthis.amountValidation = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"rateType\") ==\"Fixed Rate\") {\t\t\r\r\n\t\tif (value.getData() < 0 || value.getData() > 100){\r\r\n\t\t\tvalue.setData(0);\r\r\n\t\t\tvalue.setValid(false, \"Must be >= 0 and < 100\");\r\r\n\t\t}else{\r\r\n\t\t\tvalue.setValid(true);\r\r\n\t\t\tthis.calcChangeAmnt(value);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nvar newIndex;\r\r\n//call from amountValidation function - Calculate change amount service call\r\r\nthis.calcChangeAmnt = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar requestAmnt = this.context.options.amountCollectableByNBE.get(\"value\");\r\r\n\tvar changePercentage = this.context.binding.get(\"value\").get(index).get(\"changePercentage\");\r\r\n\t\r\r\n\tvar concatedDefault = requestAmnt + \",\" + changePercentage;\r\r\n\tthis.ui.get(\"calculateChangeAmount1[\"+index+\"]\").execute(concatedDefault);\r\r\n\tnewIndex = index;\r\r\n}\r\r\n\r\r\n//On result of calculateChangeAmount1 - Map Service call ouput\r\r\nthis.setChangeAmnt = function(){\r\r\n\tvar index = newIndex;\r\r\n\tvar changeAmount = this.context.options.calculatedChangeAmnt.get(\"value\");\r\r\n\tif(!!changeAmount){\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",changeAmount);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",0);\r\r\n\t}\r\r\n} \r\r\n\r\r\n//On change of Account Num - Set Account Info for each customer Account\r\r\nthis.fillAccountData = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tfor (var i = 0; i < this.context.options.chargesCustomerAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t//Set isOverDraft\r\r\n\t\t\tvar commClassCode = this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0, 1);\r\r\n\r\r\n\t\t\tif (code == \"O\" || code == \"D\") {\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\r\r\n\t\t\t//Set Account Info\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"branchCode\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\", {});\r\r\n\t\t\tthis.ui.get(\"accountCurrency[\"+index+\"]\").setData(this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").set(\"vale\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\",this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balance\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t\t\r\r\n\t\t\tthis.context.options.index.set(\"value\", index);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of GL Account Num - Reset Data on GL account change\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"isGLFound\",false);\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"glVerifyMSG\",\"\");\r\r\n}\r\r\n\r\r\n//On click of validate GL Btn - Call A service call to validate GL\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"glAccountNo\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\n//On result of Validate GL - Map the result of GL service call\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"isGLFound\") ) {\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(true);\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"Verified\");\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of Account Cry - Apply vis and Call A service to get exchange rate between Default Cry and Account Cry\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar cryCode = value.getData();\r\r\n//\tvar cryCode = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").get(\"value\");\r\r\n\tif(cryCode != \"\" && cryCode != null && cryCode != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == cryCode) {\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = cryCode;\r\r\n\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Result of get exchange rate\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\n//On Debited Amount in Account Currency and Account Balance- validate is over Draft\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar accountBalance = this.ui.get(\"accountBalance[\"+index+\"]\").getData();\r\r\n\tvar debitedAmount = this.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").getData();\r\r\n\t\r\r\n\t//Need to add &&accountClass is Customer Account-->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\r\n\tif (debitedAmount > accountBalance) {\r\r\n\t\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t\t}else if (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == false){\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"Error: Must be < Account Balance\");\r\r\n\t\t}\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n// Reusable function to set visibility and enabled state\r\r\nthis.setUIVisibilityAndState = function (ui, index, config) {\r\r\n    for (var i = 0; i < config.length; i++) {\r\r\n\t\tvar item = config[i];\r\r\n\t\tui.get(item.name + \"[\" + index + \"]\").setVisible(item.visible, true);\r\r\n\t\tif (item.enabled !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setEnabled(item.enabled);\r\r\n\t\t}\r\r\n\t\tif (item.data !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setData(item.data);\r\r\n\t\t}\r\r\n    }\r\r\n};\r\r\n\r\r\n// Handle Customer Account\r\r\nthis.handleCustomerAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: true },\r\r\n        { name: \"accountBalance\", visible: true },\r\r\n        { name: \"customerAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: false },\r\r\n        { name: \"GLSection1\", visible: false },\r\r\n        { name: \"accountCurrency\", enabled: false }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"glAccountNo\", data: \"\" });\r\r\n    } else {\r\r\n        config.push({ name: \"customerAccountNo\", enabled: false });\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n    this.ui.get(\"accountBranchCode[\" + index + \"]\").setEnabled(false);\r\r\n};\r\r\n\r\r\n// Handle GL Account\r\r\nthis.handleGLAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: false },\r\r\n        { name: \"accountBalance\", visible: false },\r\r\n        { name: \"customerAccountNo\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"GLSection1\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: true, enabled: !isChecker },\r\r\n        { name: \"accountCurrency\", enabled: !isChecker },\r\r\n        { name: \"accountBranchCode\", enabled: !isChecker }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"customerAccountNo\", data: null });\r\r\n        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n};\r\r\n\r\r\n// Main function\r\r\nthis.accountNumVis = function (value) {\r\r\n    var index = value;\r\r\n    var accountClass = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\");\r\r\n    var isChecker = this.context.options.isChecker.get(\"value\");\r\r\n\r\r\n    if (accountClass === \"001\") {\r\r\n        // Customer\r\r\n        this.handleCustomerAccount(index, isChecker);\r\r\n    } else {\r\r\n        // GL\r\r\n        this.handleGLAccount(index, isChecker);\r\r\n    }\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": ".panel-heading {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    justify-content: space-between; /* Distribute space between title and controls */\r\r\n    height: 50px; /* Fixed height for the panel heading */\r\r\n    padding: 0 10px; /* Add horizontal padding */\r\r\n    box-sizing: border-box; /* Include padding and border in height calculations */\r\r\n    overflow: hidden; /* Hide any overflowing content */\r\r\n}\r\r\n\r\r\n.panel-title {\r\r\n    flex: 1; /* Allow title to grow and fill available space */\r\r\n    white-space: nowrap; /* Prevent text from wrapping */\r\r\n    overflow: hidden; /* Hide overflow text */\r\r\n    text-overflow: ellipsis; /* Add ellipsis if text overflows */\r\r\n    font-size: 14px; /* Adjust font size to make the title smaller */\r\r\n    line-height: 1; /* Adjust line height for better text fitting */\r\r\n    margin: 0; /* Ensure no extra margin affecting layout */\r\r\n}\r\r\n\r\r\n.panel-heading-controls {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    margin-left: auto; /* Push controls to the right */\r\r\n}"}]}, "hasDetails": true}, {"id": "12.e3018bad-b453-4bf5-96fd-09a5141cd061", "versionId": "941edc2c-cd76-47dc-9f9d-2c425890710b", "name": "ChargesAndCommissions", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.2f8e9d68-4adb-42dd-a9f6-634969db03dc", "versionId": "b6b36f0f-af11-4144-aa46-d39750902231", "name": "Check GL Account", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "versionId": "c1d19b2a-90ea-4231-9cca-065d226aa8e1", "name": "Check Parent Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "24.940ca5e1-5f71-40c0-8748-89f4b53c2525", "versionId": "8d91fc81-a61e-4f31-aeca-f2fd791bd1db", "name": "Closure Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.5f9278b1-621c-429c-9e91-b2b6d2850a95", "versionId": "2d67cf60-8ab0-4d15-9453-ab28e7269ea5", "name": "Closure Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "1.2af38b7e-9461-4887-9ad2-24d4565ee49b", "versionId": "2e4dd8db-74dc-496c-b70b-cbc0710d127c", "name": "ClosureACT01 - Create ODC Closure Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "versionId": "21577f8c-2df9-4566-867c-ec0b2d8182db", "name": "ClosureACT02 - Review ODC Closure Request by Compliance Rep", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8", "versionId": "e01b683e-1c97-4ca9-99a7-3b87a9627907", "name": "ClosureACT03 - Review ODC Closure Request by Trade FO", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.da205dca-ccab-4203-9f89-719cb8957cc5", "versionId": "f7a78373-5e71-41d8-8e11-d6c9c7e9ef57", "name": "ClosureACT04 - Review ODC Closure Request by Trade FO Checker", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.93bd8272-8f96-42db-92a3-1bdb463c2c28", "versionId": "54a260cb-e48b-4db7-8b45-708bb909eb01", "name": "ClosureACT05 - ODC Closure Execution", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "versionId": "8004b27f-ff8f-461d-bd60-38d9fb4d7368", "name": "ClosureACT06 - ODC Closure Execution Review", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "versionId": "f0c29a38-66c0-4f01-99e3-95891f4aa4cd", "name": "Col_Actions", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "versionId": "4fb40a86-6ebe-4a8f-a67f-97e8a1ad9f79", "name": "Col_ScreenNames", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "21.00a114f4-2b01-4c48-aad9-bd62580da24b", "versionId": "0e192c1e-ff10-4383-9bee-a91299a88299", "name": "Col_SLA", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "1.6255f65f-d7ce-452d-9058-3f856ea792e0", "versionId": "2d44f93c-7507-483a-9a42-c82155bd0166", "name": "Col01 - Create ODC Collection Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.d5923909-74e7-467b-afb5-eef943fb9698", "versionId": "b6e7910f-ef9c-4691-89d3-f6b311230cba", "name": "Col01 - test", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "versionId": "e44dbf59-fb14-40c0-9374-7689ef0a2c4d", "name": "Col02 - Review ODC Collection Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "12.b80<PERSON>ace-d932-480c-ab0a-f072d7a8a5db", "versionId": "fec6a28e-99a6-4568-aaf2-4d8095e9e6d3", "name": "CollectingBank", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "64.24e33a8b-e481-4548-81a5-6647b0c96320", "versionId": "9bd3cc2d-089b-489c-9a22-9c78560bf5ce", "name": "Commissions And Charges", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "CommissionsAndChargesDetails", "configOptions": ["CommAccountList", "customerCIF", "accounteeCIF", "caseCIF", "draweeCIF", "<PERSON><PERSON><PERSON><PERSON>", "exRate", "accountIndexC", "alertMessage", "errorVis"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.debitedAmountSum = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar changeAm = this.ui.get(\"ChangeAmount[\"+index+\"]\").getData();\r\r\n\tvar nRate = this.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").getData();\r\r\n\tvar sum = changeAm * nRate;\r\r\n\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setData(sum);\r\r\n}\r\r\n\r\r\nthis.executeService = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (value.getData() == \"Customer Account\"){\r\r\n       \tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setEnabled(false);\r\r\n       \tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setEnabled(false);\r\r\n        \tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setEnabled(false);\r\r\n       \tthis.ui.get(\"Owner1[\"+index+\"]\").setEnabled(true);\r\r\n    \t\tif (this.ui.get(\"Owner1[\"+index+\"]\").getData() != \"\") {\r\r\n   \t  \t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(true);\r\r\n    \t  \t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setData(\"\");\r\r\n    \t\t}\r\r\n\t}else if (value.getData() == \"GL Account\"){\r\r\n\t\tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setEnabled(true);\r\r\n       \tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Owner1[\"+index+\"]\").setEnabled(false);\r\r\n   \t  \tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"Decimal1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"Output_Text1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setData(\"\");\r\r\n    }\r\r\n}\r\r\n\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\tthis.context.options.accountIndexC.set(\"value\", value.ui.getIndex());\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"GLAccountNumber\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\nthis.setAccountInfo = function (value){\r\r\n\tfor (var i=0; i<this.context.options.CommAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t// this.context.options.commClassCode.set(\"value\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountClassCode\"));\r\r\n\t\t\t// var commClassCode =  this.context.options.commClassCode.get(\"value\");//SA04\r\r\n\t\t\tcommClassCode = this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0,1);\r\r\n\t\t\t\r\r\n\t\t\tif (code == \"O\" || code == \"D\"){\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountBranchCode\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountCurrency\",{});\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").get(\"accountCurrency\").set(\"code\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountBalance\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"debitedAccount\").get(\"isGLFoundC\")) {\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+record+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.setPartyCIF = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(true);\r\r\n\tif (value.getData() == \"Accountee\") {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.accounteeCIF.get(\"value\"));\r\r\n\t}else if (value.getData() == \"Case In Need\") {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.caseCIF.get(\"value\"));\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.draweeCIF.get(\"value\"));\r\r\n\t}\r\r\n\tthis.ui.get(\"GetCustomerAccount1[\"+index+\"]\").execute(this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"commCIF\"));\r\r\n}\r\r\n\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(value.getData() != \"\" && value.getData() != null && value.getData() != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == value.getData()) {\r\r\n\t\t\tthis.ui.get(\"StandardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setData(this.ui.get(\"StandardExchangeRate[\"+index+\"]\").getData());\r\r\n\t\t}else{\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = value.getData();\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"StandardExchangeRate[\"+index+\"]\").setData(rate.toFixed(6));\r\r\n\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setData(rate.toFixed(6));\r\r\n\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\nthis.setCommAccounts = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"commAccountList\" , []);\r\r\n\tfor (var i=0; i<this.context.options.CommAccountList.get(\"value\").length(); i++) {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"commAccountList\").add({name:this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\") , value:this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\")});\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setData(false);\r\r\n}\r\r\n\r\r\nthis.accountVis = function(value){\r\r\n\tvalue.setEnabled(false);\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(this.context.options.isChecker.get(\"value\") == true || this.ui.get(\"Owner1[\"+index+\"]\").getData() == \"\"){\r\r\n\t\tvalue.setEnabled(false);\r\r\n\t}else\r\r\n\t\tvalue.setEnabled(true);\r\r\n}\r\r\n\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n//--------------------------------------------------------------------------------Drop_2----------------------------------------------------------------------------------------\r\r\n//------------------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}"}]}, "hasDetails": true}, {"id": "61.34ee4297-a5d2-4df0-882b-3f89c31112df", "versionId": "9264cef6-707a-4ace-925e-8eed6b8f8eef", "name": "common.js", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "1.9395be71-805c-4443-85ee-48c92de214a3", "versionId": "388fedc6-f078-4b14-a057-d0465cc60849", "name": "Compare Validations", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "64.0f40f56d-733f-4bd5-916c-92ae7dccbb10", "versionId": "af64a198-833a-4315-bc6b-a2056724e2f5", "name": "Contract Creation CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "contractCreation", "configOptions": ["contractCreationVis", "contractStage", "bpmRequestNumber", "currency", "nbeCollectableAmount", "todayDate"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.testSave = function(){\r\r\n\tvar saveBtn = bpmext.ui.getView(\"/DC_Templete1/saveState\");\r\r\n\tconsole.dir(saveBtn);\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\nthis.getViewByPath = function (path) {\r\r\n\treturn bpmext.ui.getView(path) || null;\r\r\n};\r\r\n\r\r\nthis.getFieldById = function (path) {\r\r\n\treturn this.ui.get(path) || null;\r\r\n};"}]}, "hasDetails": true}, {"id": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "versionId": "64571bed-66b5-41fa-87ad-9a9c8819f9f3", "name": "Contract Liquidation", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (this.context.options.isChecker.get(\"value\")) {\r\r\n\r\r\n\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(false);\r\r\n\tthis.ui.get(\"debitedAccountNo1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(false);\r\r\n}else{\r\r\n//\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(true);\r\r\n//\tthis.ui.get(\"debitedAccountNo1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(true);\r\r\n}", "bindingType": "odcRequest", "configOptions": ["glAccountVerified", "contractLiqVis", "customerAccounts", "exchangeRate", "<PERSON><PERSON><PERSON><PERSON>", "contractLiquidatedMSG", "verifyGLMsg"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//Validate Debited Nostro/Vostro Account\t\t\t\t\t\t\t\t\t\t\r\r\n this.valiadateAccCurrency = function (value)\r\r\n {\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo   = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\");\t\r\r\n\tvar currency    = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\tvar liqCurency  = this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"liqCurrency\");\t\r\r\n\t\r\r\n\tif (accountNo == value.getData() && currency != liqCurency) { \r\r\n//\t\t value.setData(\"\");\r\r\n//\t\t this.ui.get( \"debitedAccountNo1\").setValid(false,\"This account's currency must be the same as liquidation currency\");\t\r\r\n \tbreak;\r\r\n \t}\r\r\n\telse\r\r\n\t\t this.ui.get( \"debitedAccountNo1\").setValid(true);\t\r\r\n\t}//end of for\r\r\n}\r\r\n\r\r\n//Init Credit Account Data\r\r\nthis.setAccountInfo = function (value)\r\r\n {\r\r\n\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo= this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") ? this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") : \"\";\r\r\n\t\tif (accountNo == value.getData()) { \r\r\n\t\t\tvar branchCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"branchCode\");\r\r\n\t\t\tvar currency = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\t\t\tvar balance = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balance\");\r\r\n\t\t\tvar balanceSign = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balanceType\");\r\r\n\t\t\tvar classCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t \t\tclassCode= classCode? classCode.substring(0,1):\"\";\r\r\n\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"branchCode\", branchCode);\r\r\n\t \t\t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balance\", balance);\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balanceSign\", balanceSign);\r\r\n//\t \t\tconsole.log(\"------------------classCode: \"+classCode);\r\r\n\t \t\tif(classCode == \"O\" || classCode == \"D\")\r\r\n\t \t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",true);\r\r\n\t\t\telse\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",false);\r\r\n\t\t\t\t\t\t\r\r\n//\t\t\tvar od= this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n//\t\t\tconsole.log(\"------------------is over draft : \"+od);\t\t \t\t\r\r\n\t \t\t//SET CURRENCY\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"currency\", {} );\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"name\", currency);\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"value\", currency);\r\r\n\t\t\t\r\r\n\t\t\t//SET EXCHANGE RATE\r\r\n//\t\t\tview.setNegoRate(currency);\t\r\r\n \t\t}\r\r\n\t}\r\r\n}\r\r\n //Set credited Amount\r\r\n this.setNegoRate = function(value)\r\r\n {\t\r\r\n\r\r\n//\tconsole.log(\"----------------------INSIDE setNegoRate\");\r\r\n  \tvar liqCurr   =  this.ui.get(\"LiquidateionCurrency\").getData()? this.ui.get(\"LiquidateionCurrency\").getData():\"\";  \t\t \t\r\r\n  \tvar accCurr   =  value.getData()? value.getData():\"\"; \t \t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr == accCurr ) ) { \r\r\n\t\tthis.ui.get(\"standardExRate\").setData(1);\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setData(1);\r\r\n  \t\tthis.ui.get(\"negotiatedExRate\").setEnabled(false); \t\r\r\n \t} \t\t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr != accCurr ) ){\r\r\n//\t\t//call get exchange rate service\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n//\t\t console.log(\"---------- liqCurr != accCurr -------------------------\");\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setEnabled(true);\r\r\n//  \t\tvar inputCurrency={fromCurrency : liqCurr , toCurrency : accCurr};\r\r\n//  \t\tconsole.log(\"----------inputCurrency \"+inputCurrency);\r\r\n  \t\t\r\r\n//  \t\tvar parsedInputCurrency =  JSON.stringify(inputCurrency);\r\r\n//  \t\tconsole.log(\"----------parsedInputCurrency=   \"+parsedInputCurrency);\r\r\n//     \tconsole.log(\"*******************************************************************\");\r\r\n//\t\talert(parsedInputCurrency);\r\r\n//\t\tthis.ui.get(\"SC_getExRate\").execute(parsedInputCurrency);\r\r\n\t\t\r\r\n\t\tconcatedCurrency = {ccFrom : liqCurr , ccTo : accCurr , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\tinputCurr = JSON.stringify(concatedCurrency);\r\r\n\r\r\n\t\tthis.ui.get(\"GetExchangeRate\").execute(inputCurr);\r\r\n \t}\t\t\t\r\r\n}\r\r\n//SET Credited Amount From Exchange Service\r\r\n this.SetCreditAmountDetails = function(value){\r\r\n\r\r\n\tconsole.log(\"----------------------INSIDE SetCreditAmountDetails\");\r\r\n\tvar exRate = this.context.options.exchangeRate.get(\"value\");\r\r\n\r\r\n\tconsole.log(\"--------------- rate \"+ exRate);\r\r\n\tconsole.log(\"---------------ExRate fixed =  \"+ exRate.toFixed(6));\r\r\n\t\r\r\n\tthis.ui.get(\"standardExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\t\r\r\n\r\r\n } \r\r\n//calculate credited amount in cuurency\r\r\nthis.setAmountInCurrency = function(){\r\r\n\tvar liqAmount  =  this.ui.get(\"LiquidationAmount\").getData()? this.ui.get(\"LiquidationAmount\").getData():0;\r\r\n\tvar negoExRate =  this.ui.get(\"negotiatedExRate\").getData()? this.ui.get(\"negotiatedExRate\").getData():0;\r\r\n\t\r\r\n\tif( liqAmount > 0   &&   negoExRate > 0 )\r\r\n\t\tthis.ui.get(\"creditedAmountinAccCurrency\").setData(liqAmount * negoExRate);\r\r\n}\r\r\n\r\r\n//validate overDraft\r\r\nthis.validateOverDraft = function () {\r\r\n\tvar AccBalance          =  this.ui.get(\"AccountBalance\").getData()? this.ui.get(\"AccountBalance\").getData() : 0;\r\r\n\tvar amountinAccCurrency =  this.ui.get(\"creditedAmountinAccCurrency\").getData()? this.ui.get(\"creditedAmountinAccCurrency\").getData() : 0;\r\r\n\tvar overDraft           =  this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n\t\r\r\n\tif(amountinAccCurrency > AccBalance){\r\r\n\t\tif (overDraft) \r\r\n\t\t\t{\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"WARNING: Amount in account currency should be less than Account Balance\");\r\r\n\t\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"ERROR: Amount in Account Currency > Account Balance and Account Type is not Overdraft\");\r\r\n\t\t\t}\r\r\n\t\r\r\n\t}else{\r\r\n\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(true);\r\r\n\t}//end of if\r\r\n\t\r\r\n}\r\r\n\r\r\n//On load of account class - set vis according to account class (customer & gl)\r\r\nthis.accountNumVis = function (value) {\r\r\n    var accountClass = value.getData().name;\r\r\n    \r\r\n    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n        if (accountClass == \"Customer Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true,true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true,true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n\r\r\n        } else if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(true);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false,true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false,true);\r\r\n\r\r\n        }\r\r\n    } else {\r\r\n        this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n        this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n        \r\r\n        if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"glAccountNo\").setEnabled(false);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n   \r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false, true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false, true);\r\r\n            \r\r\n        } else {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\t\tthis.ui.get(\"customerAccountNo\").setEnabled(false);\r\r\n\t\t\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true, true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n        }\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of account class - reset data before choosing another account\r\r\nthis.resetAccountInfo = function (value){\r\r\n    this.ui.get(\"glAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"customerAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"AccountBalance\").setData(\"\");\r\r\n    this.ui.get(\"AccountBranchCode\").setData(\"\");\r\r\n    this.ui.get(\"BalanceSign\").setData(\"\");\r\r\n    this.ui.get(\"accountCurrency\").setData(\"\");\r\r\n    \r\r\n    this.ui.get(\"standardExRate\").setData(\"\");\r\r\n    this.ui.get(\"creditedAmountinAccCurrency\").setData(\"\");\r\r\n    this.ui.get(\"negotiatedExRate\").setData(\"\");\r\r\n    this.ui.get(\"verifiedText\").setData(\"\");\r\r\n    \r\r\n    this.accountNumVis(value);\r\r\n}"}]}, "hasDetails": true}, {"id": "12.80dacdbe-03aa-474f-9fb6-3a43c32fef58", "versionId": "deef7a36-1140-49d5-8ac2-d93b55f1d213", "name": "ContractCreation", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.29905e05-3ca0-49e0-96a6-a1035efdf052", "versionId": "f3cd86fa-d1cf-4499-a838-b5e1d5caf82b", "name": "ContractLiquidation", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.5d77055c-98a8-4191-9b74-c7120a5823be", "versionId": "78d80004-b3e8-4015-8160-b3ae3b3ad3ea", "name": "Create", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249", "versionId": "2f282072-cc7e-4f91-a680-b115235ae194", "name": "Create amend audit service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8ac85528-d7d4-438c-bebd-7b9fe047d0f9", "versionId": "f98ead3b-39b5-422a-b96a-f066cc63e6a5", "name": "Create CIF Folder", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.46b984a3-b4ad-405a-abd3-8631f907efe4", "versionId": "f8e30919-05e5-410e-b324-f077ea1b0472", "name": "Create FileNet Folder", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8a1c5f67-55b2-4785-97cb-9ec3e4441186", "versionId": "6415877a-8b28-461c-8678-da20a8bd7fad", "name": "Create Folder FileNet", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e2ffa7a5-7c4f-4625-90e6-1d620f946497", "versionId": "c7dc9099-18ab-4d7b-adff-38c22761dc34", "name": "Create Folder Structure", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "versionId": "a2e0a4f1-9a12-4fe6-a90e-8f94ea5c21d9", "name": "CreationActions", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "versionId": "b2affbad-b447-40bf-9336-acb174aa96ad", "name": "Customer Information cv", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "customerInformation", "configOptions": ["customerInfoVIS", "listsVIS"]}, "hasDetails": true}, {"id": "12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "versionId": "8a39674b-485d-4bf1-be4d-e4b844ec41f4", "name": "CustomerInfo", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "24.97bc604d-23d4-4c32-82d9-7c314516642f", "versionId": "6a689023-cea9-4d61-9d13-dbee72e450a7", "name": "DC Central Unit Supervisor", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.13887169-b900-4899-a81f-01e15c00c91f", "versionId": "39b6c253-3b30-4161-89a1-f2a5c0518b15", "name": "DC control Unit Supervisor", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "64.5df8245e-3f18-41b6-8394-548397e4652f", "versionId": "385262a6-4cc6-44c7-aa6d-6db9baf128aa", "name": "DC History", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "stepLog"}, "hasDetails": true}, {"id": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "versionId": "3f5595f6-740a-432d-af7f-278d37e4845c", "name": "DC start request template", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "odcRequest", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "Untitled"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.showApprovals = function  () {\r\r\n//\tif (this.context.options.hasApprovals.get(\"value\")) {\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(true,true);\r\r\n//\t\tif (this.context.options.approvalsReadOnly.get(\"value\")) {\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(false);\r\r\n//\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(true);\r\r\n//\t\t}\r\r\n//\r\r\n//\t}else{\r\r\n//\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(false,true);\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//\r\r\n//this.showReturnReason = function  () {\r\r\n//\tif (this.context.options.hasReturnReason.get(\"value\") && ((this.context.options.selectedAction.get(\"value\") == \"Return To Trade FO\") || (this.context.options.selectedAction.get(\"value\") == \"Return to Initiator\") || (this.context.options.selectedAction.get(\"value\") == \"Return To Maker\"))) {\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(true,true);\r\r\n//\t}else{\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n//\r\r\n//this.showAction = function  () {\r\r\n//\tif (this.context.binding.get(\"value\").get(\"subStatus\") == \"\") {\r\r\n//\t\tthis.ui.get(\"Single_Select1\").setVisible(false,true);\r\r\n//\t\tthis.ui.get(\"Text1\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n\r\r\n//updateActionList = function  () {\r\r\n//\r\r\n//\r\r\n//console.log(this.context.options.errorPanelVIS.get(\"value\"));\r\r\n//\r\r\n////var compApp = ;\r\r\n//\r\r\n////\tif(this.context.options.complianceApproval.get(\"value\"))\r\r\n////\t{\r\r\n////\t\tthis.context.options.action.set(\"value\", [\"123\",\"34\"]);\r\r\n////\t}\r\r\n//}"}]}, "hasDetails": true}, {"id": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "versionId": "a4863016-bca6-415f-9407-7cd45040cbde", "name": "DC Templete", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "appinfo", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "returnReasonVIS", "tradeFoComment", "tradeFoCommentVis", "exeHubMkrComment", "exeHubMkrCommentVis", "compcheckerComment", "compcheckerCommentVIS", "disableSubmit"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": "/* Main container for tabs */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n    display: flex;\r\r\n    flex-wrap: wrap; /* Allow tabs to wrap onto multiple lines */\r\r\n    align-items: center;\r\r\n    padding: 5px;\r\r\n    border-bottom: 1px solid #ddd;\r\r\n    background-color: #f8f9fa;\r\r\n    list-style-type: none; /* Remove default list styling */\r\r\n    margin: 0;\r\r\n}\r\r\n\r\r\n/* Style each tab */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n    /*margin-right: 5px;*/\r\r\n    margin-bottom: 2px;\r\r\n    list-style-type: none; /* Ensure no bullets */\r\r\n    white-space: nowrap; /* Prevent text wrapping inside each tab */\r\r\n}\r\r\n\r\r\n/* Tab anchor styling */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n    display: block;\r\r\n    padding: 5px 5px;\r\r\n    background-color: #ffffff;\r\r\n    border: 1px solid #ddd;\r\r\n    border-radius: 3px;\r\r\n    color: #007bff;\r\r\n    text-decoration: none;\r\r\n    transition: background-color 0.3s ease, color 0.3s ease;\r\r\n    height:26px;\r\r\n}\r\r\n\r\r\n/* Active tab and hover state */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.active > a,\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a:hover {\r\r\n    background-color: #007bff;\r\r\n    color: #ffffff;\r\r\n}\r\r\n\r\r\n/* Hide the secondary dropdown menu if it exists */\r\r\nul.nav.nav-tabs-mnu.nav-tabs-simple.tabs-primary {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Make sure the first <ul> doesn't get altered by dropdown behavior */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-toggle {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-menu {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Responsive adjustments to ensure tabs wrap correctly */\r\r\n@media (max-width: 768px) {\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n        flex-direction: row;\r\r\n        flex-wrap: wrap; /* Ensure tabs wrap on small screens */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n        margin-right: 0;\r\r\n        white-space: normal; /* Allow text to wrap within tabs */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n        text-align: center; /* Center text within tabs */\r\r\n    }\r\r\n}\r\r\n/* Ensure tabs don't leave white space when removed */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.hidden {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Ensure all tabs are displayed */\r\r\n.nav-tabs > li {\r\r\n    display: block !important; /* Override any display:none */\r\r\n    /*visibility: visible !important; /* Ensure visibility */\r\r\n}"}]}, "hasDetails": true}, {"id": "64.77f06102-fddf-42a1-9b6b-6580b729871c", "versionId": "2c6b6d88-a3e8-4b28-9aa5-e87623af52eb", "name": "DC Templete 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "require([\r\r\n    \"dojo/_base/lang\", \r\r\n    \"com.ibm.bpm.coach/engine\"\r\r\n], function (lang, engine) {\r\r\n\r\r\n    // Function to get the coach data\r\r\n    function getCoachData() {\r\r\n        var coachData = null;\r\r\n\t\r\r\n\t  console.log(\"<<<<ENGINE>>>\");\r\r\n        console.dir(engine.GlobalAssets);\r\r\n        \r\r\n        console.log(\"\");\r\r\n        if (engine && engine.coachView) {\r\r\n            coachData = engine.coachView.getCoachData();\r\r\n        }\r\r\n\r\r\n        if (!coachData) {\r\r\n            console.log(\"Coach data not found\");\r\r\n            return null;\r\r\n        }\r\r\n\r\r\n        return coachData;\r\r\n    }\r\r\n\r\r\n    // Example usage\r\r\n    var data = getCoachData();\r\r\n    console.log(\"Coach Data:\", data);\r\r\n});", "bindingType": "appinfo", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "data", "conditions"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.setActions = function () {\r\r\nvar complianceApproval= true;\r\r\n//\tthis.ui.get(\"retrieveRequest\").setData(false);\r\r\n\tthis.ui.get(\"\")\r\r\n\r\r\n}"}]}, "hasDetails": true}, {"id": "1.e0a673da-2e76-4a12-8896-d868bab63e83", "versionId": "6246d9e2-970f-4112-a5ec-ca82a319deaf", "name": "Deployment Service Flow", "type": "process", "typeName": "Process", "details": {"processType": "13"}, "subType": "13", "hasDetails": false}, {"id": "1.7f097f90-cf8f-43fa-875f-f106fe9cac70", "versionId": "f6012b23-364c-4dcc-9b2f-62a0cad72961", "name": "Document Generation", "type": "process", "typeName": "Process", "details": {"processType": "11"}, "subType": "11", "hasDetails": false}, {"id": "64.1d99aba8-195f-4eee-ba8c-a47926bc21e2", "versionId": "1a1713a3-de37-49e2-bdcb-d84680269095", "name": "Document Generation CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "documentGeneration", "configOptions": ["requestTypeVIS", "regeneratedRemittanceLetterTitleVIS", "deliveryTerms", "paymentTerms", "specialInstructions", "instructions", "requestType", "remittanceLetter<PERSON><PERSON>on", "barcode", "generationStatus", "documentGenerationVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.EngLetterRegExp = function(char)\r\r\n//{\r\r\n//console.log(\"inside reg exp fn\");\r\r\n//console.log(char +\"chaaracterrr\");\r\r\n//\t//if(this.context.options.requestType.get(\"value\") == \"create\" ||this.context.options.requestType.get(\"value\") == \"amend\" ||this.context.options.requestType.get(\"value\") == \"recreate\")\r\r\n//\t\r\r\n//\t\tvar regex = \"/^[A-Za-z][A-Za-z0-9]*$/\";\r\r\n////\t\tchar.match(regex);\r\r\n//\t\tif( regex.test(char) == true)\r\r\n//\t\t{\r\r\n//\t\t\tthis.ui.get(\"customerName\").setValid(true);\r\r\n//\t\t\tconsole.log(\"match\");\r\r\n//\t\t}\t\r\r\n//\t\telse\r\r\n//\t\t{\r\r\n//\t\t\tthis.ui.get(\"customerName\").setValid(false , \"This field accepts alpanumeric only\");\r\r\n//\t\t\tconsole.log(\"doesnt match\");\r\r\n//\t\t}\t\t\t\r\r\n//}\r\r\nthis.ShowRegeneratedRemittanceLetter = function()\r\r\n{\r\r\n\t//if(this.context.binding.get(\"value\").get(\"regenerateRemLetter\") == true\r\r\n\tif(this.context.binding.get(\"value\").get(\"regenerateRemLetter\") == true && this.context.binding.get(\"value\").get(\"regenerateRemLetterOption\").get(\"value\") == \"other\")\r\r\n\t{\t\r\r\n\t\tthis.context.options.regeneratedRemittanceLetterTitleVIS.set(\"value\", \"EDITABLE\");\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\tthis.context.options.regeneratedRemittanceLetterTitleVIS.set(\"value\", \"NONE\");\r\r\n\t}\r\r\n}\r\r\n////////////////////////////////////"}]}, "hasDetails": true}, {"id": "12.2d0c4c5f-5bd5-47be-8861-e5fd791c3063", "versionId": "9621acdb-733d-40ec-8438-385f61755704", "name": "DocumentGenerationRequest", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.a0722085-9246-4af1-b86e-8a21be7e94cc", "versionId": "7c7bae4a-dc4f-49bb-950c-4ad1a8315b92", "name": "DocumentGenerationResponse", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.74193c98-6d23-4451-b526-7bb6b010b116", "versionId": "a4efaae0-414d-4b4e-ae10-f7870c294d95", "name": "Drawee", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.cdbb2c22-8516-45ab-925a-2933e6e1bed5", "versionId": "6c1d2559-c84c-44cb-810c-04104de10045", "name": "Drawer", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.57fea321-b2d0-479e-93d1-735d7a7ae033", "versionId": "f88aadb3-fa4f-4599-8b1b-0c50210bc7b0", "name": "EcmDocumentInformation", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.b698dbfb-84da-40a5-9db3-676815055e65", "versionId": "75acba00-60e4-4027-b3cc-265d0523c0f3", "name": "ECMproperties", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.51a88928-60d9-48d1-b133-fa98f61b49a6", "versionId": "8c547f72-0dc0-4296-affb-4a2160dcb625", "name": "ECMProperties", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "62.e6994ce9-1cd9-4d76-8fe5-0c7bd4f3ca1f", "versionId": "efc2b9cd-22ab-4ea0-b881-464eea5824a6", "name": "Environment Variables", "type": "environmentVariableSet", "typeName": "Environment Variables", "details": {}}, {"id": "12.9ad7c20d-998e-4629-84b9-f365c85a6733", "versionId": "229827b8-d6db-4c57-a151-c007e05fff6c", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "versionId": "2fd5fb36-d939-4309-899f-4e0ac13819e5", "name": "Exception Handling", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9", "versionId": "43837a43-23fe-4fde-9d48-cd45e4400de0", "name": "Execution HUB Filter Service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "versionId": "f28116da-b2e5-4a00-a7a7-1562a7ca4ae3", "name": "FC Collections CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "fcCollections", "configOptions": ["FCVIS", "retrieveBtnVis", "customerCif", "collectionCurrencyVIS", "negotiatedExchangeRateVIS", "requestCurrency", "addBtnVIS", "activityType"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//amountAllocatedForCurrentRequest * negotiatedExchangeRate\r\r\n //allocatedAmountInRequestCurrency\r\r\n //Function to calculate allocated amount in request currency\r\r\n// this.calculateAllocatedAmount = function(){\r\r\n//\tvar rate = this.context.binding.get(\"value\").get(\"negotiatedExchangeRate\");\r\r\n//\tconsole.log(\"Negotiated Exchange Rate\");\r\r\n//\tvar amount ;\r\r\n//\t\t\r\r\n//\tvar result = rate * amount;\r\r\n//}\r\r\n\r\r\n\r\r\nthis.negotiatedVIS = function  (me) {\r\r\n\tif (me.getData() == 1) {\r\r\n\t\tthis.context.options.negotiatedExchangeRateVIS.set(\"value\", \"READONLY\");\r\r\n\t}else{\r\r\n\t\tthis.context.options.negotiatedExchangeRateVIS.set(\"value\", \"DEFAULT\");\r\r\n\t}\r\r\n} \r\r\n\r\r\n\r\r\nthis.showRetrieveTransactionBtn = function(){\r\r\n//console.log(\"///////////////////\"+this.context.options.activityType.get(\"value\"));\r\r\nif(this.context.options.activityType.get(\"value\") == \"write\")\r\r\n{\r\r\n\ttry {\r\r\n\t\t\r\r\n\t\r\r\n\t\tif(this.context.binding.get(\"value\").get(\"currency\").get(\"name\") == \"\" || this.context.binding.get(\"value\").get(\"fromDate\") == null ||\r\r\n\t\tthis.context.binding.get(\"value\").get(\"ToDate\") == null || this.context.binding.get(\"value\").get(\"accountNo\").get(\"value\") == \"\") \r\r\n\t\t{\r\r\n\t\t \tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t\t \tconsole.log(\"not visible\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get(\"Button1\").setVisible(true,true);\t\r\r\n\t\t\tconsole.log(\" visible\");\r\r\n\t\t}\r\r\n\t} catch (err) {\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n\telse{\r\r\n\t\t\tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}, {"id": "12.88fbc33e-4ee3-4815-9620-f04917f7a3a2", "versionId": "0a4a3615-6604-4076-b7d3-fe5b6311cd0e", "name": "FCCollections", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134", "versionId": "e8a18f78-609f-4ebd-b237-0c1e584d8df0", "name": "FCTransactions", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "versionId": "51456dd2-66ca-4a40-8805-14a62712aefd", "name": "Financial Details Branch", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetails", "configOptions": ["financialDetailsCVVis", "currencyDocAmountVIS", "today"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.FCVIS= function()\r\r\n{\r\r\n\tif(this.context.binding.get(\"value\").get(\"amountAdvanced\") > 0)\r\r\n\t\tthis.ui.get(\"fcPanel\").setVisible(true);\r\r\n\telse\r\r\n\t\tthis.ui.get(\"fcPanel\").setVisible(false,true);\r\r\n\r\r\n}"}]}, "hasDetails": true}, {"id": "64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1", "versionId": "3afd7001-91c6-496d-bb5a-cb784ede3ad6", "name": "Financial Details Trade FO CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetailsFO", "configOptions": ["financialDetailsFOVis", "requestType", "act3VIS", "multiTenorDatesVIS", "todayDate", "documentAmount", "amountAdvanced"], "inlineScripts": []}, "hasDetails": true}, {"id": "64.af339c2a-1b07-4416-a616-ec3121b73970", "versionId": "ee583e61-8bd1-4df8-a72c-7697f5b2d063", "name": "Financial Details Trade FO CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetailsFO", "configOptions": ["financialDetailsFOVis", "requestType", "act3VIS", "multiTenorDatesVIS", "todayDate", "documentAmount", "amountAdvanced", "columns"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.seColumnVis = function(me) {\r\r\n\tvar tableColumn=[\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"N\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false}\r\r\n\t];\r\r\n\t\r\r\nvar columnss2 = [\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 0,\r\r\n        type: \"Date_Time_Picker\",\r\r\n        label: \"Installment Date التاريخ\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 1,\r\r\n        type: \"Date_Time_Picker\",\r\r\n        label: \"Mat Date\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 2,\r\r\n        type: \"Integer\",\r\r\n        label: \"Tenor Days\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 3,\r\r\n        type: \"Decimal\",\r\r\n        label: \"Installment Amount المبلغ\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 4,\r\r\n        type: \"Decimal\",\r\r\n        label: \"Rebate\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    }\r\r\n]\r\r\n\t\r\r\n//\tthis.context.options.columns.set(\"value\", tableColumn);\r\r\n\tme.setColumns(columnss2);\r\r\n//\tconsole.log( JSON.stringify(me.getColumns()));\r\r\n//\tme.refresh();\r\r\n\t\r\r\n}"}]}, "hasDetails": true}, {"id": "12.d2a537b9-92c2-48c5-a18c-72e499761454", "versionId": "8c07c51c-ddf3-41f9-906d-f80bea261b03", "name": "FinancialDetailsBranch", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.65f0e3ad-27ed-4e8c-9713-56c6ccbffff5", "versionId": "40b3e2b9-50f1-4880-8304-98491d4b3feb", "name": "FinancialDetailsFO", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "64.63a48491-564d-4a28-972c-df08820c76ff", "versionId": "9038759a-d909-4706-88ae-ae7a4ed18868", "name": "functionSearch", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "// Constants\r\r\n\r\r\nconst EVENT_TYPES = [\r\r\n\t\"eventON_LOAD\",\r\r\n\t\"eventON_CHANGE\",\r\r\n\t\"eventON_INPUT\",\r\r\n\t\"eventON_BLUR\",\r\r\n\t\"eventON_FOCUS\",\r\r\n\t\"eventON_SVCERROR\",\r\r\n\t\"eventON_SVCITEMS\",\r\r\n\t\"eventON_BEFORE_SHOW_DAY\"\r\r\n];\r\r\nconst SEARCH_OPTIONS = {\r\r\n\tALL: \"all\",\r\r\n\tFUNCTION_NAME: \"funName\",\r\r\n\tNOT_USED: \"notUsed\"\r\r\n};\r\r\nconst SYSTEM_PROPERTIES = ['constructor', 'prototype', '__proto__'];\r\r\n\r\r\n/**\r\r\n * Builds a map of control labels to their bound object values for specified events\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {Array<string>} eventTypes - Array of event types to check for\r\r\n * @returns {Map<string, Array<{eventType: string, value: string}>} Map of control labels to arrays of event type and bound object value pairs\r\r\n */\r\r\nfunction buildBoundObjectMap(viewName, eventTypes) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst boundObjectMap = new Map();\r\r\n\tconsole.dir(view);\r\r\n\tif (!view || !view._bpmextViewNode || !view._bpmextViewNode._children) {\r\r\n\t\tconsole.error(\"View or view children not found\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\r\r\n\tconst children = view._bpmextViewNode._children;\r\r\n\tif (!children || Object.keys(children).length === 0) {\r\r\n\t\tconsole.error(\"No children found in the view\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\t// Iterate through all child controls\r\r\n\tfor (const controlType in children) {\r\r\n\t\tif (Object.prototype.hasOwnProperty.call(children, controlType)) {\r\r\n\t\t\tconst controls = children[controlType];\r\r\n\r\r\n\t\t\t// Process each control of this type\r\r\n\t\t\tfor (const control of controls) {\r\r\n\t\t\t\tif (!control._data) continue;\r\r\n\r\r\n\t\t\t\tconst label = control._data.getLabel ? control._data.getLabel() : controlType;\r\r\n\r\r\n\t\t\t\t// Check for each event type\r\r\n\t\t\t\tfor (const eventType of eventTypes) {\r\r\n\t\t\t\t\tif (control._data.context && control._data.context.options && control._data.context.options[eventType] && control._data.context.options[eventType].boundObject && control._data.context.options[eventType].boundObject.value) {\r\r\n\t\t\t\t\t\t// Get the bound object value\r\r\n\t\t\t\t\t\tconst value = control._data.context.options[eventType].boundObject.value;\r\r\n\r\r\n\t\t\t\t\t\t// Initialize array for this label if it doesn't exist\r\r\n\t\t\t\t\t\tif (!boundObjectMap.has(label)) {\r\r\n\t\t\t\t\t\t\tboundObjectMap.set(label, []);\r\r\n\t\t\t\t\t\t}\r\r\n\r\r\n\t\t\t\t\t\t// Add event type and value to the array for this label\r\r\n\t\t\t\t\t\tboundObjectMap.get(label).push({\r\r\n\t\t\t\t\t\t\teventType: eventType,\r\r\n\t\t\t\t\t\t\tvalue: value,\r\r\n\t\t\t\t\t\t});\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn boundObjectMap;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for a string in the bound object values and returns matching control labels\r\r\n * @param {Map<string, Array<{eventType: string, value: string}>} boundObjectMap - Map of control labels to arrays of event type and bound object value pairs\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchBoundObjects(boundObjectMap, searchString) {\r\r\n\tif (!searchString || typeof searchString !== \"string\") {\r\r\n\t\treturn [];\r\r\n\t}\r\r\n\r\r\n\tconst matchingResults = [];\r\r\n\r\r\n\t// Convert search string to lowercase for case-insensitive search\r\r\n\tconst searchLower = searchString.toLowerCase();\r\r\n\r\r\n\t// Search through all values in the map\r\r\n\tboundObjectMap.forEach((eventArray, label) => {\r\r\n\t\t// Find matching events for this control\r\r\n\t\tconst matchingEvents = eventArray.filter((event) => typeof event.value === \"string\" && event.value.toLowerCase().includes(searchLower));\r\r\n\r\r\n\t\t// If we found any matches, add this control to the results\r\r\n\t\tif (matchingEvents.length > 0) {\r\r\n\t\t\tmatchingResults.push({\r\r\n\t\t\t\tlabel: label,\r\r\n\t\t\t\tevents: matchingEvents,\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn matchingResults;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Main function to search for bound objects in a view\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchFunctionsInView(viewName, searchString) {\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\treturn searchBoundObjects(boundObjectMap, searchString);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets just the labels of controls that match the search string\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<string>} Array of distinct control labels that match the search\r\r\n */\r\r\nfunction getMatchingControlLabels(viewName, searchString) {\r\r\n\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\treturn results.map((result) => result.label);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats search results as an HTML table\r\r\n * @param {Array<{label: string, events: Array<{eventType: string, value: string}>}>} results - Search results from searchFunctionsInView\r\r\n * @param {string} searchString - The search string used (for highlighting)\r\r\n * @returns {string} HTML table representation of the search results\r\r\n */\r\r\nfunction formatResultsAsHtml(results, searchString) {\r\r\n\tif (!results || results.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No matching controls found</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn text.replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Highlight the search string in the value\r\r\n\tfunction highlightSearchString(text, search) {\r\r\n\t\tif (!search || !text.includes(search)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(search), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with unified design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each result\r\r\n\tresults.forEach((result) => {\r\r\n\t\tresult.events.forEach((event, index) => {\r\r\n\t\t\thtml += `\r\r\n            <tr>\r\r\n                <td>${escapeHtml(searchString)}</td>\r\r\n                <td>${index === 0 ? escapeHtml(result.label) : \"\"}</td>\r\r\n                <td class=\"event-type\">${escapeHtml(event.eventType.replace(\"event\", \"\"))}</td>\r\r\n                <td>${highlightSearchString(event.value, searchString)}</td>\r\r\n            </tr>\r\r\n            `;\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Checks if a function name is a system function\r\r\n * @param {string} functionName - The function name to check\r\r\n * @returns {boolean} True if it's a system function\r\r\n */\r\r\nfunction isSystemFunction(functionName) {\r\r\n\treturn functionName.startsWith('event') ||\r\r\n\t\t   functionName.includes('ON_') ||\r\r\n\t\t   functionName.startsWith('get') ||\r\r\n\t\t   functionName.startsWith('set') ||\r\r\n\t\t   functionName.startsWith('_');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets all user-defined functions from a view\r\r\n * @param {Object} view - The view object\r\r\n * @returns {Array<string>} Array of user function names\r\r\n */\r\r\nfunction getUserFunctions(view) {\r\r\n\tconst userFunctions = [];\r\r\n\tconst ownPropertyNames = Object.getOwnPropertyNames(view);\r\r\n\r\r\n\tfor (const key of ownPropertyNames) {\r\r\n\t\ttry {\r\r\n\t\t\t// Skip system properties\r\r\n\t\t\tif (SYSTEM_PROPERTIES.includes(key)) {\r\r\n\t\t\t\tcontinue;\r\r\n\t\t\t}\r\r\n\r\r\n\t\t\tconst value = view[key];\r\r\n\r\r\n\t\t\t// Check if it's a user-defined function\r\r\n\t\t\tif (typeof value === 'function' && !isSystemFunction(key)) {\r\r\n\t\t\t\tuserFunctions.push(key);\r\r\n\t\t\t}\r\r\n\t\t} catch (e) {\r\r\n\t\t\tconsole.log(`Error accessing property ${key}: ${e.message}`);\r\r\n\t\t}\r\r\n\t}\r\r\n\r\r\n\treturn userFunctions;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Extracts all user-defined function names from a view and finds their usage in controls\r\r\n * @param {string} viewName - The name of the view to extract functions from\r\r\n * @returns {Array<{name: string, controller: string, eventType: string, context: string}>} Array of function details found in the view\r\r\n */\r\r\nfunction extractFunctionNamesFromView(viewName) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst functionDetails = [];\r\r\n\r\r\n\tif (!view) {\r\r\n\t\tconsole.error(\"View not found\");\r\r\n\t\treturn functionDetails;\r\r\n\t}\r\r\n\r\r\n\tconsole.dir(view); // Debug logging\r\r\n\r\r\n\t// Get all user-defined functions\r\r\n\tconst userFunctions = getUserFunctions(view);\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\r\r\n\t// Find usage for each function\r\r\n\tuserFunctions.forEach(functionName => {\r\r\n\t\tconst usageResults = searchBoundObjects(boundObjectMap, functionName);\r\r\n\r\r\n\t\tif (usageResults.length > 0) {\r\r\n\t\t\t// Function is used in controls\r\r\n\t\t\tusageResults.forEach(result => {\r\r\n\t\t\t\tresult.events.forEach(event => {\r\r\n\t\t\t\t\tfunctionDetails.push({\r\r\n\t\t\t\t\t\tname: functionName,\r\r\n\t\t\t\t\t\tcontroller: result.label,\r\r\n\t\t\t\t\t\teventType: event.eventType.replace(\"event\", \"\"),\r\r\n\t\t\t\t\t\tcontext: event.value || functionName\r\r\n\t\t\t\t\t});\r\r\n\t\t\t\t});\r\r\n\t\t\t});\r\r\n\t\t} else {\r\r\n\t\t\t// Function exists but not used\r\r\n\t\t\tfunctionDetails.push({\r\r\n\t\t\t\tname: functionName,\r\r\n\t\t\t\tcontroller: 'Not Used',\r\r\n\t\t\t\teventType: 'Available',\r\r\n\t\t\t\tcontext: 'Function not bound to any control'\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn functionDetails;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Filters function details to show only unused functions\r\r\n * @param {Array<{name: string, controller: string, eventType: string}>} functionDetails - All function details\r\r\n * @returns {Array<{name: string, controller: string, eventType: string}>} Filtered function details\r\r\n */\r\r\nfunction getNotUsedFunctions(functionDetails) {\r\r\n\treturn functionDetails.filter(detail => detail.controller === 'Not Used');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats function details as an HTML table with grouping\r\r\n * @param {Array<{name: string, controller: string, eventType: string, context: string}>} functionDetails - Array of function details\r\r\n * @returns {string} HTML table representation of the function details\r\r\n */\r\r\nfunction formatFunctionNamesAsHtml(functionDetails) {\r\r\n\tif (!functionDetails || functionDetails.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No functions found in the view</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn String(text).replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Group by function name first, then by controller\r\r\n\tconst groupedByFunction = {};\r\r\n\tfunctionDetails.forEach(detail => {\r\r\n\t\tif (!groupedByFunction[detail.name]) {\r\r\n\t\t\tgroupedByFunction[detail.name] = {};\r\r\n\t\t}\r\r\n\t\tif (!groupedByFunction[detail.name][detail.controller]) {\r\r\n\t\t\tgroupedByFunction[detail.name][detail.controller] = [];\r\r\n\t\t}\r\r\n\t\tgroupedByFunction[detail.name][detail.controller].push({\r\r\n\t\t\teventType: detail.eventType,\r\r\n\t\t\tcontext: detail.context\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\t// Sort function names alphabetically\r\r\n\tconst sortedFunctionNames = Object.keys(groupedByFunction).sort((a, b) =>\r\r\n\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t);\r\r\n\r\r\n\t// Highlight function name in context\r\r\n\tfunction highlightFunctionInContext(text, functionName) {\r\r\n\t\tif (!functionName || !text.includes(functionName)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(functionName), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with simple, clean design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each function group\r\r\n\tsortedFunctionNames.forEach((functionName) => {\r\r\n\t\tconst controllers = groupedByFunction[functionName];\r\r\n\t\tconst sortedControllers = Object.keys(controllers).sort((a, b) =>\r\r\n\t\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t\t);\r\r\n\r\r\n\t\tlet isFirstRowForFunction = true;\r\r\n\r\r\n\t\tsortedControllers.forEach((controller) => {\r\r\n\t\t\tconst eventDetails = controllers[controller];\r\r\n\t\t\tlet isFirstRowForController = true;\r\r\n\r\r\n\t\t\teventDetails.forEach((detail) => {\r\r\n\t\t\t\tconst functionGroupClass = isFirstRowForFunction ? 'function-group' : '';\r\r\n\t\t\t\tconst controllerGroupClass = isFirstRowForController ? 'controller-group' : '';\r\r\n\r\r\n\t\t\t\thtml += `\r\r\n                <tr class=\"${functionGroupClass}\">\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForFunction ? escapeHtml(functionName) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForController ? escapeHtml(controller) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"event-type\">${escapeHtml(detail.eventType)}</td>\r\r\n                    <td>${highlightFunctionInContext(detail.context, functionName)}</td>\r\r\n                </tr>\r\r\n                `;\r\r\n\r\r\n\t\t\t\tisFirstRowForFunction = false;\r\r\n\t\t\t\tisFirstRowForController = false;\r\r\n\t\t\t});\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for functions in a view and returns the results as HTML\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchFor - Search option (all, funName, notUsed)\r\r\n * @param {string} searchString - String to search for in bound object values (used when searchFor is funName)\r\r\n * @returns {string} HTML representation of the search results\r\r\n */\r\r\nfunction searchFunctionsAndFormatAsHtml(viewName, searchFor, searchString) {\r\r\n\tswitch (searchFor) {\r\r\n\t\tcase SEARCH_OPTIONS.ALL:\r\r\n\t\t\t// Show all user-defined functions\r\r\n\t\t\tconst allFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(allFunctionDetails);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.FUNCTION_NAME:\r\r\n\t\t\t// Search for specific function name\r\r\n\t\t\tif (!searchString) {\r\r\n\t\t\t\treturn \"<div class='no-results'>Please enter a function name to search for</div>\";\r\r\n\t\t\t}\r\r\n\t\t\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\t\t\treturn formatResultsAsHtml(results, searchString);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.NOT_USED:\r\r\n\t\t\t// Show only unused functions\r\r\n\t\t\tconst allFunctions = extractFunctionNamesFromView(viewName);\r\r\n\t\t\tconst notUsedFunctions = getNotUsedFunctions(allFunctions);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(notUsedFunctions);\r\r\n\r\r\n\t\tdefault:\r\r\n\t\t\t// Default to showing all functions\r\r\n\t\t\tconst defaultFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(defaultFunctionDetails);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.executeSearch = function () {\r\r\n\tvar viewName = this.ui.get(\"viewName\").getData();\r\r\n\tif (!viewName) {\r\r\n\t\tconsole.error(\"View name is empty\");\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tvar searchForVal = this.ui.get(\"searchFor\").getData();\r\r\n\tvar funName = this.ui.get(\"functionName\").getData();\r\r\n\r\r\n\tconsole.log(`Search type: ${searchForVal}, Function name: ${funName || 'N/A'}, View: ${viewName}`);\r\r\n\r\r\n\tvar htmlResults = searchFunctionsAndFormatAsHtml(viewName, searchForVal, funName);\r\r\n\tif (!!htmlResults) {\r\r\n\t\tthis.ui.get(\"result\").setData(htmlResults);\r\r\n\t}\r\r\n};"}]}, "hasDetails": true}, {"id": "1.8e583b1e-1719-4e19-a6ce-6f41202527d4", "versionId": "726a60e9-20d1-464f-be10-cac0f6e5b358", "name": "Generate BPM Request Number", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.3e8e25f3-d222-42e7-b682-10a48d0bf0f2", "versionId": "0f8b5d23-2ad9-4e92-a134-ff82524258aa", "name": "Generate Remittance Letter", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "12.1974aaf6-f654-42f2-b5ef-93386ef5d51e", "versionId": "45f165d0-e514-49a7-8498-5b500a69e2c0", "name": "GeneratedDocumentInfo", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "versionId": "2ab775de-cfd8-4ef4-84c3-78d836c0426f", "name": "Get account transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "versionId": "d3e91fab-eaa6-42c0-bc2b-1993a9797adb", "name": "Get Actions By ScreenName 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "versionId": "481d6826-1a12-4925-b771-84e41e86c71b", "name": "Get Actions for screens", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.f3735333-84b5-461b-9811-0817300dfb0b", "versionId": "51adad51-fef8-40a1-bfc8-cdc9146187c5", "name": "Get bank BIC codes", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f", "versionId": "25d936f9-6075-44a7-8da2-d1e5f68e9901", "name": "Get BIC Codes", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.769a8281-bf53-46c6-a4c8-571fc192f312", "versionId": "ce4d2ec4-0272-4163-b263-5eeb0cf5d121", "name": "Get charge amount", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "versionId": "57b9519b-c7b3-4dd4-9f16-ff374ea487f8", "name": "Get Charges Completed", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "versionId": "93427c98-30b1-4e9b-afd1-dd78819d8b88", "name": "Get Customer and Party Account List", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "versionId": "d7b6d4fd-c803-4126-b7ce-66b725ee7fe1", "name": "Get Customer Information", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.672c16cb-04d7-4e67-a904-779d9009e1ce", "versionId": "36e03776-e05b-452d-b6de-63a7ffa7e26f", "name": "Get Debited Nostro Vostro Account", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f", "versionId": "b9d3a129-d9ba-4e97-91d9-e9e67aa01b6d", "name": "Get Exchange Rate", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.d2c8a30b-64a6-40b2-bb6b-8ace82441ed5", "versionId": "b140fdd6-57d9-4bee-95fe-e4736776d96d", "name": "Get Exchange Rate 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e", "versionId": "99590f81-1b9d-486b-ac13-6c3e800fdae6", "name": "Get Exchange Rate New", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.09d158a9-e4e4-4987-827f-4a4923c76843", "versionId": "5705639a-56b2-4fad-b807-141f3552a058", "name": "Get HUB name by code", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a9e6ba43-572e-4e44-b72a-285af67ec153", "versionId": "59c62612-504b-4989-a138-67fa4172d4bf", "name": "Get ODC charges", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "versionId": "33995fae-589a-4b95-bb3c-8abada244c7e", "name": "Get ODC charges 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "versionId": "f4a258c2-bb9f-42ca-ada7-e28ce1a91934", "name": "Get ODC Initiators", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8c345de2-fda0-48d8-bd24-ec6bd26c767f", "versionId": "f58cde10-b5fc-458d-8b18-10ee2601f718", "name": "Get Parent Request 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b1a705db-e502-470d-ad30-b08231994382", "versionId": "685b456d-8331-4e15-8435-507759e74816", "name": "Get Party Details", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.58febcb0-50a3-4963-8368-32c96c00d116", "versionId": "74c4c993-b191-4db5-b2ce-3a3a004e5f54", "name": "Get Request and Customer Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.d034d01a-267a-423b-a582-7444da255e09", "versionId": "cf53053e-c203-4386-b5cb-2dd80b75ae52", "name": "Get Request Nature", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.9e31ab59-ee74-4591-92f7-292855228c1b", "versionId": "7d2185a5-4430-4985-8522-ec1a6488d07d", "name": "Get Request Type", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.4855448a-1c86-4167-afa5-d79d3273d48b", "versionId": "6e3bc9bd-3726-456b-932c-ef8ab05bbde2", "name": "get RequestID", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.69ebf680-a301-4a98-ab97-9803a829bb25", "versionId": "9ee7bba9-5dd2-4974-a177-471c44906f41", "name": "Get Required Documents", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7", "versionId": "2867c960-8837-4a75-8b94-f1c61e5f5356", "name": "Get Required Documents 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.f18b1174-01f4-419d-80de-4b239479a7f1", "versionId": "0cd7d184-dc72-475b-a146-f1570fd0f490", "name": "get Reversal Reason id", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.d590fbbf-ae62-4ba9-bbcf-4c6b288b7cca", "versionId": "e3330927-d39f-472a-8e13-00fffb123dde", "name": "get search Criteria List", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.ce54fd48-8513-44df-9dc5-530fe191c83f", "versionId": "3f6df8cc-626f-4428-a80d-46cc65930974", "name": "Get terms and conditions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc", "versionId": "2c9659be-2ace-42c5-a494-3e8842519db1", "name": "getEPV", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "61.6c107f22-ba5c-47d3-aacc-e3561380a3a2", "versionId": "12cb2496-e762-4144-879f-48ceeb827cd0", "name": "header1.png", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "versionId": "60722f6e-3f02-4e69-af40-bc9baf2ba628", "name": "History", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "6023.3c0ee9b7-a4a7-4812-9e58-f2cc9dd2fd92", "versionId": "a654415a-11c2-4992-b1c6-cccd57da22ed", "name": "http://localhost:8080/v2/api-docs", "type": "eSArtifact", "typeName": "Esartifact", "details": {}}, {"id": "6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4", "versionId": "9f3f1b2e-9f4c-48a6-89e7-6c790cee5c1e", "name": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice.xsd", "type": "eSArtifact", "typeName": "Esartifact", "details": {}}, {"id": "24.5a62da37-5a68-4735-b00d-12829a30ea97", "versionId": "049f8938-ee40-402d-b8aa-97ea9195d697", "name": "HUB compliance Representative  Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.d39b0e70-e7b8-468a-9fe9-1bd4b8f7dbec", "versionId": "0b943e44-4716-4f4d-8001-267362d691f6", "name": "HUB compliance Representative Checker", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.7858d46a-5678-49eb-bfb0-758ef8ec2464", "versionId": "b76dffa3-b85d-4b2b-852c-1384bef8b49a", "name": "Hub CU Checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.cda98858-50ea-46de-89cf-0293c9ae9a00", "versionId": "999c0d4a-c7a3-44d9-a496-197f86ca5c13", "name": "Hub CU Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.c1a3c19e-010a-4618-9c2f-16ff28c0245b", "versionId": "ba85583f-3103-4dd5-ad65-b85eb880fb93", "name": "HUB CU Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.84c9f3c0-b39d-42f8-9437-500732ca636c", "versionId": "f09ea9f3-ae16-4e92-84d9-ea35683ec9c6", "name": "HUB Execution Checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.219ec5c0-0c40-4f2d-ab50-d2c3b503ff14", "versionId": "d6309310-aec6-4786-b1ba-2d18c888bf92", "name": "HUB Execution Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.51364718-ca09-4922-bed7-2eab4ed3c5a9", "versionId": "cb4e9364-0539-4cd1-b67c-cd29b1e9f073", "name": "HUB Execution Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "1.302e4a54-16ab-47b9-9b70-d933329c72e7", "versionId": "be2a3126-ee11-4cb2-a3b8-708159a021f2", "name": "Hub Filter service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "versionId": "50d524c6-1b7b-4b28-a77e-5a644b96a625", "name": "IDC Party", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "", "bindingType": "input", "configOptions": ["partyTypeList", "warningMess", "BeneficiaryDetails", "partyIndex", "customerFullDetails", "selectedBIC", "customerCIF", "accounteeCIF", "caseCIF", "addressBICList", "selectedCIF", "alertMessage", "errorVis", "draweeCIF", "requestType", "<PERSON><PERSON><PERSON><PERSON>", "requestState", "columns", "addParty", "deleteParty", "deletedCIF", "appID"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "var isAmndFinal = false;\r\r\nvar length = 0;\r\r\nthis.setColumnsVis = function () {\r\r\n\tvar vis = \"V\";\r\r\n\tif ((this.context.options.requestType.get(\"value\") == \"IDC Amendment\" || this.context.options.requestType.get(\"value\") == \"IDC Completion\") && !this.context.options.isChecker.get(\"value\")) {\r\r\n        this.context.options.addParty.set(\"value\", false);\r\r\n        this.context.options.deleteParty.set(\"value\", false);\r\r\n        if (this.context.options.requestState.get(\"value\") == \"Initial\"){\r\r\n            vis = \"R\";\r\r\n            isAmndFinal = false;\r\r\n        }else if (this.context.options.requestState.get(\"value\") == \"Final\"){\r\r\n        \tvis = \"R\";\r\r\n            length = this.context.binding.get(\"value\").length();\r\r\n            if (length < 5) {\r\r\n                this.context.options.addParty.set(\"value\", true);\r\r\n                isAmndFinal = true;\r\r\n            }    \r\r\n        }\r\r\n    }else{\r\r\n        vis = \"V\";\r\r\n        this.context.options.addParty.set(\"value\", true);\r\r\n        this.context.options.deleteParty.set(\"value\", true);\r\r\n        isAmndFinal = false;\r\r\n    }\r\r\n\r\r\n    var columns = [];\r\r\n    columns = [\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"}\r\r\n    ];\r\r\n    this.context.options.columns.set(\"value\", []);\r\r\n    this.context.options.columns.set(\"value\", columns);\r\r\n}\r\r\n\r\r\nthis.addAccountee = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n   \tif (isAmndFinal && record == length) {\r\r\n\t    \tvalue.setEnabled(true);\r\r\n\t    \tthis.context.options.addParty.set(\"value\", false);\r\r\n\t    \tthis.context.options.deleteParty.set(\"value\", true);\r\r\n\t    \tthis.ui.get(\"PartyTable/partytype[\"+record+\"]\").setData({name:\"Accountee\",value:\"Accountee\"});\r\r\n    \t}\r\r\n}\r\r\n\r\r\nthis.setMediaVis = function (value) {\r\r\n\tvar r = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(r).get(\"partyType\").get(\"name\") == \"Remitting Bank\") {\r\r\n\t\tvalue.setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tvalue.hide();\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.onDeleteParty = function (value1,value2) {\r\r\n\tvar i = value1.getRecordIndex(value2);\r\r\n\tvar type = value2.partyType.name;\r\r\n\r\r\n\tif((i == 0 || i == 1) || (isAmndFinal && i != length)) {\r\r\n\t\talert(\"You can not delete this row\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n\t\tthis.context.options.addParty.set(\"value\", true);\r\r\n\r\r\n\t\tthis.context.options.deletedCIF.set(\"value\", value2.partyId);\r\r\n\t\tif (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \t\tthis.ui.get(\"update\").click();\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n//------------------------------------------------------------------\r\r\nthis.validateOneType = function (type,value) {\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar n = 0;\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == type){\r\r\n\t\t\tn+=1\r\r\n\t\t}\r\r\n\t}\r\r\n\tif(value.getData().name == type && n>1){\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"Only one  is allowed of same Type\");\r\r\n\t\tthis.context.binding.get(\"value\").remove(record);\r\r\n\t}else{\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"\");\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetPartyItem = function (record) {\r\r\n\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/CIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/partyname[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/country[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/language[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/reference[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address1[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address2[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address3[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n}\r\r\n\r\r\nthis.validateParty = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"country\", \"EG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\")\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(false);\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(false);\r\r\n\t//validateOneType\r\r\n\tthis.validateOneType(\"Accountee\",value);\r\r\n\tthis.validateOneType(\"Drawee\",value);\r\r\n\tthis.validateOneType(\"Case in Need\",value);\r\r\n\tthis.validateOneType(\"Drawer\",value);\r\r\n\tthis.validateOneType(\"Remitting Bank\",value);\r\r\n\t\r\r\n\t//Reset Data\r\r\n\tthis.resetPartyItem(record);\t\r\r\n    \t//Default values\r\r\n\r\r\n\tif (this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\") == \"Remitting Bank\"){\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"SWIFT\");\r\r\n\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address\", \"\");\r\r\n//\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n//\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t}\r\r\n\t\r\r\n\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n\r\r\n}\r\r\n\r\r\nthis.setOwnerCIF = function () {\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Accountee\"){\r\r\n\t\t\tthis.context.options.accounteeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Drawee\"){\r\r\n\t\t\tthis.context.options.draweeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Case in Need\"){\r\r\n\t\t\tthis.context.options.caseCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\nthis.setPartyId = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar type = this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"isRetrived\", false);\r\r\n\t\r\r\n\t//Set Owner CIF\r\r\n//\tthis.setOwnerCIF();\r\r\n\t//Validation\r\r\n\tif(isNaN(Number(value.getData())) || value.getData().length < 8){\r\r\n        value.setValid(false , \"must be 8 digits\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n        value.setValid(true);\r\r\n        if (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n        }\r\r\n\t\treturn true;\t\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.concatOnDelete = function (value){\r\r\n\tvar newString = \"\";\r\r\n\tfor (var i=0; i<this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif (i == value && i > 1) {\r\r\n\t\t\tcontinue;\r\r\n\t\t}\r\r\n\t\tnewString +=this.context.binding.get(\"value\").get(i).get(\"partyType\").name +\",\";\r\r\n\t}\r\r\n\tthis.context.options.concatString.set(\"value\", newString);\r\r\n}\r\r\n\r\r\nthis.drawerNotNBE = function (){\r\r\n    if (this.context.binding.get(\"value\").get(1).get(\"isNbeCustomer\") == false){\r\r\n        this.context.binding.get(\"value\").get(1).set(\"country\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"country\").get(\"englishdescription\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(1).set(\"name\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"name\"));\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.mapPartyData = function (){\r\r\n      record = this.context.options.partyIndex.get(\"value\");\r\r\n\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") == \"Remitting Bank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\"){\r\r\n\r\r\n\t\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setValid(false,\"This CIF is not corresponding to a Bank\");\r\r\n\t}else{\t\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",\"EG\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",this.context.options.customerFullDetails.get(\"value\").get(\"nationality\"));\r\r\n\t\t}\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"language\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\t\t}else{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", this.context.options.customerFullDetails.get(\"value\").get(\"language\"));\r\r\n\t\t}\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"branch\", {});\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"value\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") === \"Remitting Bank\"){\t\r\r\n\t\t\t\r\r\n\t\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\t\tvar input = cif+\"-\"+appID;\r\r\n\t\t\tthis.ui.get(\"GetAddressBIC\").execute(input);\r\r\n\t\t\tif (!!this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\")) {\t\r\r\n\t\t\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\"));\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\",\"NO REF\");\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(true);\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(true);\r\r\n\t\t}else{\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\");\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.clickEx = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tthis.context.options.partyIndex.set(\"value\", record);\r\r\n\tvalue.setData(true);\r\r\n\tif (!!this.context.binding.get(\"value\").get(record).get(\"partyCIF\")) {\r\r\n\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\tvar input = cif +\"-\"+appID;\r\r\n\t    \tthis.ui.get(\"GetPartyDetails1\").execute(input);\r\r\n//\t    \tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t}\t\r\r\n}\r\r\n\r\r\nthis.setBIC = function (value){\r\r\n    this.context.options.selectedBIC.set(\"value\", value.getData());\r\r\n}\r\r\n//---------------------------------------------------------------------------Drop_2--------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}\r\r\n\r\r\nthis.disableRetrieveBtn = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tif (record != 1){\r\r\n//\t\tvalue.setEnabled(false);\r\r\n\t\tvalue.hide();\r\r\n\t}else{\r\r\n\t\tvalue.setEnabled(true);\r\r\n\t}\r\r\n\tif(record == 1 && this.context.binding.get(\"value\").get(record).get(\"isNbeCustomer\") == true){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else if (record != 1){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else {\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}, {"id": "64.0ff96fd8-0740-4d17-887e-a56c8ef7921b", "versionId": "f2f1658d-ae7b-45ee-a60e-36b46b282024", "name": "Importer Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "importerDetails", "configOptions": ["importerDetailsCVVIS", "requestType", "bankRefVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.collectingBankRefVIS  = function()\r\r\n{\r\r\n\tif(this.context.options.requestType.get(\"value\") == \"amend\" || this.context.options.requestType.get(\"value\") == \"recreate\" )\r\r\n\t\tthis.context.options.bankRefVIS.set(\"value\", \"Editable\");\r\r\n\telse\r\r\n\r\r\n\t\tthis.context.options.bankRefVIS.set(\"value\", \"None\");\r\r\n}"}]}, "hasDetails": true}, {"id": "12.53a1b1c3-7342-40f0-9f89-24bf1d5ea7cf", "versionId": "e701223e-814e-44ed-ba04-9e85dc33fa56", "name": "ImporterDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.a28b2951-6ed0-484f-81e5-3d5d147c88f9", "versionId": "917a87a1-d033-43a3-a2dd-ce56114e8edc", "name": "Init Create ODC Request Service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "versionId": "8856132e-d9e1-48a0-99c8-999f2e996e33", "name": "Init ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "versionId": "7e52ed01-6dbe-4b2e-b2e1-421b774f9c5c", "name": "Initiate ODC Process", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "12.6b7da6cc-ded4-44e5-a5ab-864b19299f2f", "versionId": "29561c58-f88c-4499-b4d2-17d0185a8e32", "name": "Invoice", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "61.03c776ac-d2b4-4bbe-b4df-c302f6d1d325", "versionId": "bc487627-2396-420f-9377-503efe4e787a", "name": "itextpdf-5.1.0.jar", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "12.2e5f0222-20a7-4c21-bdaa-5284bb68cf3d", "versionId": "fadb4a5e-db8e-42be-9c01-28ab590158e1", "name": "ItmDef_1412542389_2e5f0222_20a7_4c21_bdaa_5284bb68cf3d", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.253b0033-bc46-490c-bac0-6d6c794d403e", "versionId": "ba658780-2273-442e-9e79-8705dc1dfb57", "name": "ItmDef_1914247323_253b0033_bc46_490c_bac0_6d6c794d403e", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "64.1da5da8a-d92b-4788-add6-533189d60ad0", "versionId": "8c515d6c-be9c-40f0-b3da-415911a71f4b", "name": "layout", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {}, "hasDetails": false}, {"id": "64.3e998f87-f87f-443f-b820-c2f7d465fa68", "versionId": "7ea0a2ea-391b-4ffd-b4eb-f2fd890c4773", "name": "Layout 3P CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {}, "hasDetails": false}, {"id": "64.701ac070-61ee-4489-9c65-6cc48db9510a", "versionId": "9eeb1345-7563-4417-88a6-845d0a059643", "name": "Layout 4P CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {}, "hasDetails": false}, {"id": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "versionId": "11b29981-5b4f-4708-b14b-a10a0c95da8b", "name": "Log FC Transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "50.411567ee-9528-4af5-acce-fa1c1b418a1e", "versionId": "f4111048-9307-42de-b236-301ab1956b77", "name": "Lookups", "type": "resourceBundleGroup", "typeName": "Resource Bundle", "details": {}}, {"id": "21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c", "versionId": "63932d06-9062-443e-a513-8892148fffdc", "name": "Mails", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "12.5ab4dfaa-bb18-4ceb-b653-8f1a20041d49", "versionId": "70baabff-bfe0-4de3-a967-e99d5ef6c292", "name": "Msg_1108305804_5ab4dfaa_bb18_4ceb_b653_8f1a20041d49", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.a894d8aa-2563-4e74-8c8a-8b87c2aba05b", "versionId": "9ee35003-7589-40b0-9cc6-22e4f584c2b8", "name": "Msg_913804518_a894d8aa_2563_4e74_8c8a_8b87c2aba05b", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "64.7efffff5-e4b6-4afc-a9a2-4e9b478d26a5", "versionId": "e146c342-750c-4f66-b669-01213bf55fe1", "name": "Multi Tenor Dates CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "multiTenorDates"}, "hasDetails": true}, {"id": "12.58617e47-17c6-4d9a-8fb0-1edafe8cd663", "versionId": "890399b8-9a6e-45d8-ab02-6276e368589b", "name": "MultiTenorDates", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "versionId": "9ee21618-4ac4-44bd-9e2a-77dc15c7a31c", "name": "ODC BO Initialization", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964", "versionId": "8085dbb1-53f3-4f16-b3ba-7ce403f4f24e", "name": "ODC Closure اقفال تحصيل مستندى تصدير", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "24.ab30711e-0674-4175-bb8c-e87b2f03fa98", "versionId": "4ddad2c1-bdac-420c-92ac-358710b1be2b", "name": "ODC Collection Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.8ab3fca1-29f3-4ddd-892d-55f78a0f69bc", "versionId": "80363967-8022-49dd-a6e3-7b57464c83df", "name": "ODC Collection Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "25.0b754ec1-9413-443e-9edc-f7cc6429b0d6", "versionId": "7af263f8-ec28-4d86-9715-bda0c6db59f3", "name": "ODC Collection تسجيل حصائل على مستند تحصيل تصدير", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "64.041a4da1-f77a-4f68-8837-a712d1a38d59", "versionId": "dc0256a7-8e64-4c99-9b51-54d7514a45ec", "name": "ODC Collections CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "odcCollection", "configOptions": ["odcCollectionVis"]}, "hasDetails": true}, {"id": "24.c1950331-89e8-4fb3-825b-52bc99292612", "versionId": "6733a439-ecaf-42c2-af7c-0927c7d22049", "name": "ODC compliance Representative checkers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.f542e8cc-d0f3-4179-8fd0-1db0628a8b36", "versionId": "64c78fc6-021c-4feb-8d9d-b6262eb12285", "name": "ODC compliance Representative Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9", "versionId": "8af1b4b7-9041-46d7-a78e-959b105f2457", "name": "ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "1.a3badb88-0400-49ae-ac1d-0e0ace050eb7", "versionId": "5bba26f0-66b8-470e-a1f1-91cace51e36c", "name": "ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير De_1", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "24.b469f9ac-8dba-4742-bc4d-38c0f4f28ce3", "versionId": "55b18b45-ec0f-4bb9-bfa5-bd6d9526e9a4", "name": "ODC Creation Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.e7977935-dba2-437e-9671-4ec41d29e437", "versionId": "dfd1d9ac-aebb-430b-b7ec-0e4d1f390b1c", "name": "ODC initators", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.4e372b2d-494e-42ad-a5af-cb2e77259a38", "versionId": "71db332b-8285-4518-adb4-1262cdabf102", "name": "ODC Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "64.3d608948-c1fa-4ac3-8d4b-6a42f90a5132", "versionId": "5d16a9a4-8300-453d-869f-94ee95145d51", "name": "ODC Parties CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (!this.context.options.isReturned.get(\"value\")) {\t\r\r\n\tthis.ui.get(\"GetCustomerAndPartyAccountListBtn\").click();\r\r\n}", "bindingType": "Parties", "configOptions": ["odcPartiesCVVIS", "odcPartiesCVBTNVIS", "partyTypeName", "customerFullDetails", "addressBICList", "isReturned", "<PERSON><PERSON><PERSON><PERSON>"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//Note : old script is below commented!!\r\r\n\r\r\n//On retrieve cif info btn - service call to retrieve party info\r\r\nthis.getPartyDetails = function (cif,name) {\r\r\n\t\r\r\n    this.context.options.partyTypeName.set(\"value\", name);\r\r\n    \r\r\n    var customerCIF = this.ui.get(cif).getData();\r\r\n    //validate cif before using it\r\r\n    if (isNaN(Number(customerCIF)) || customerCIF.length < 8) {\r\r\n        this.ui.get(cif).setValid(false, \"CIF must be 8 Digits\");\r\r\n\r\r\n    } else {\r\r\n        this.ui.get(cif).setValid(true);\r\r\n        this.ui.get(\"GetPartyDetails\").execute(customerCIF);\r\r\n    }\r\r\n}\r\r\n\r\r\n//on GetPartyDetails results - map the retrieved data\r\r\nthis.mapPartyData = function (name) {\r\r\n\r\r\n    if (name == \"collectingBank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\") {\r\r\n        var cif = \"collectingBankCIF\";\r\r\n        this.ui.get(cif).setValid(false, \"This CIF is not corresponding to a Bank\");\r\r\n\r\r\n    } else if (name == \"collectingBank\") {\r\r\n\t  var bankCIF = this.context.options.customerFullDetails.get(\"value\").get(\"customerNo\")\r\r\n        this.context.binding.get(\"value\").get(name).set(\"id\", bankCIF);\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"EG\");\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"reference\", \"NO REF\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"media\", \"SWIFT\");\r\r\n\t  \r\r\n\t  //Service call to get Address code list\r\r\n        this.ui.get(\"GetAddressBIC\").execute(bankCIF);\r\r\n\r\r\n    } else {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyId\", this.context.options.customerFullDetails.get(\"value\").get(\"customerNo\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyName\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"EG\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"ENG\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"refrence\", \"NO REF\");\r\r\n        \r\r\n        //Hidden btn to get Customer And Party Account List for charges - also its called on view load!!!\r\r\n        this.ui.get(\"GetCustomerAndPartyAccountListBtn\").click();\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of cif field - reset party info\r\r\nthis.resetPartyOnCifChange = function (name){\r\r\n    if (name == \"collectingBank\") {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"id\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"name\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"reference\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"media\", \"\");\r\r\n        this.context.options.addressBICList.set(\"value\", [])\r\r\n\r\r\n    } else {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyId\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyName\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"refrence\", \"\");\r\r\n    }\r\r\n}\r\r\n\r\r\n\r\r\n// this.showCollectigBankRefSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifInfo.get(\"value\") == true) {\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}\r\r\n// this.partyTypeSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifNOForPartyTypes.get(\"value\") == true){\r\r\n//\t console.log(this.context.options.retrieveCifInfo)\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}"}]}, "hasDetails": true}, {"id": "64.04664cbf-b838-4a5b-bf47-c4160069d58d", "versionId": "30bb0b7d-c031-4cfa-8bf1-cb139dccd90b", "name": "ODC Parties CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "Parties", "configOptions": ["odcPartiesCVVIS", "odcPartiesCVBTNVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "// this.showCollectigBankRefSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifInfo.get(\"value\") == true) {\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}\r\r\n// this.partyTypeSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifNOForPartyTypes.get(\"value\") == true){\r\r\n//\t console.log(this.context.options.retrieveCifInfo)\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}"}]}, "hasDetails": true}, {"id": "1.bd03b303-b465-40a1-a4c5-f08887b0cc2e", "versionId": "20231a0b-478b-48ec-99b6-aa3843bb0658", "name": "ODC Position Screen", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "24.f66dc7c7-4ada-40ba-ad62-7deff8804a27", "versionId": "fce69022-293b-488f-b27c-e533a5c1dae9", "name": "ODC Position Screen Initiators", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "25.933ae173-470f-4c1f-8a2b-6521da495a38", "versionId": "06482096-b951-4eed-b5a7-b9c8ff257a6c", "name": "ODC Reversal اعادة قيد تحصيل مستندى تصدير", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "1.55f0e416-bc0f-4110-9fce-c26af87b8915", "versionId": "296402b8-231b-4455-8fa0-710cab39e086", "name": "ODC Search Screen", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "12.6caeaaf8-3886-4b02-bf7a-25371ece2294", "versionId": "89cc14fe-8485-44b2-bf63-e7a586e37aef", "name": "ODCCollection", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.dbbaa047-8f02-4397-b1b5-41f11b0256b3", "versionId": "5f8db4c6-2265-4cad-aa68-531075abe6b6", "name": "ODCCreationSLA", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "50.41101508-d2e4-4682-b3ef-b9b22266bb5a", "versionId": "d90377d9-6e71-42cf-8f33-976bbcbd5ad6", "name": "odcLookupsTable", "type": "resourceBundleGroup", "typeName": "Resource Bundle", "details": {}}, {"id": "12.cbf7e7d2-cb5c-4667-889f-9f05b7c947ce", "versionId": "ab9beade-75aa-4dbf-9ed3-cb01b656bffd", "name": "odcParties", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.679fe48b-845c-41d8-b41a-884a27c2acf3", "versionId": "26c951c2-4455-49dc-bf21-96405071c50c", "name": "ODCProcessName", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "12.5f8547c1-515a-481c-b74a-f20ec40faff9", "versionId": "c23098df-71e9-4add-9e4a-203c60e154f7", "name": "ODCRequest", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "versionId": "3793d607-c255-4971-80fe-9dd69b16f667", "name": "odcRoutingDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.c923d7fa-ce48-4122-8c3e-afbd6e8e5917", "versionId": "7897d0b4-94c4-4df5-a5b7-7907a1cfb653", "name": "Parties", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "64.9d678f8f-ae90-4e9d-bfb5-10fbbf775731", "versionId": "dbb77c8b-14e2-4925-aafe-7b469c794b54", "name": "Parties CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "parties", "configOptions": ["partiesVis"], "inlineScripts": []}, "hasDetails": true}, {"id": "12.3e75c63e-2df3-4510-8895-56d6203d0609", "versionId": "4b759312-31eb-4636-9a26-32bd405f2792", "name": "PartyDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.d269ee9a-c6ac-4c71-908a-48eb92d1c60f", "versionId": "3f5bdf4b-05f7-4c9d-bd70-08490f422497", "name": "partyTypes", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.6ffb3c9a-8fbc-428c-afac-6d92a7114943", "versionId": "8ed1b310-c0a7-4a1b-9389-c19ee528707b", "name": "Print barcode", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.5510de3d-75a5-4353-8d6d-82f540b6d556", "versionId": "8e1130c2-4503-4eb0-a825-dd9c0bdb85c5", "name": "Print Barcode", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "barcode", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.load=function(){\r\r\n\r\r\nvar generatedBarCode=this.ui.get(\"Data1\");\r\r\n\r\r\n\r\r\nif(generatedBarCode!=null){\r\r\n\r\r\ndocument.getElementById('tdbarcode').innerHTML=generatedBarCode.getText();\r\r\n\r\r\n\r\r\n}\r\r\n\r\r\n}"}]}, "hasDetails": true}, {"id": "61.6c9a4cc1-6c0c-4b3f-a0dd-1e296f3e3f0c", "versionId": "c65271fd-d543-4b1f-b436-81f1cd859e31", "name": "print.min.js", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "63.96e0a0d5-7f81-467c-a91a-62a59ec268de", "versionId": "f4faf856-5956-404b-8385-98726f806be9", "name": "Process App Settings", "type": "projectDefaults", "typeName": "Project Settings", "details": {}}, {"id": "21.93c5c002-7ac4-4283-83ee-63b8662f9223", "versionId": "787291f6-5ebc-4162-813c-706d5f3f669b", "name": "ProcessDetails", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "64.5c068fcf-4364-4084-8825-6bb898b38609", "versionId": "7db23117-87d3-4297-98bc-73fd5c7cf4e9", "name": "Products and Shipment Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "shipmentDetails", "configOptions": ["productsVis", "shipmentVis"]}, "hasDetails": true}, {"id": "12.aad95560-c6ad-4add-bae6-8a1b432b6545", "versionId": "e69b336a-65bb-4607-abc2-c5ba51d705a8", "name": "ProductShipmentDetails", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "versionId": "12a25f86-b68a-4af4-958b-1a646d73db0c", "name": "Query BC Contract", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.028269d3-7705-46be-b7bb-3893e93d0240", "versionId": "1b81c58c-4961-4b95-a8fa-3422edd86cbe", "name": "Query BC contract 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b9bf1811-6e1e-41a3-828b-39059fa168cb", "versionId": "964de40b-0522-459f-a4fb-b63a9b125c1e", "name": "RACT01 - Create ODC Reversal Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.c71c7233-23ac-4e5f-a241-09cd96502615", "versionId": "5114311e-2333-45ef-981f-94070d569a0c", "name": "RACT02 - Review ODC Reversal Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "versionId": "97ba8feb-0017-4384-9f96-8ff8181f41a1", "name": "RequestNature", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "12.796a077f-9dd5-47e3-88a9-4908b204fdb0", "versionId": "e24d0f34-aa04-4e0d-8685-404bed09f7b6", "name": "RequestState", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.062854b5-6513-4da8-84ab-0126f90e550d", "versionId": "521b2922-4fa2-4948-81ee-3ba2f3a22265", "name": "RequestState", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "12.8cf8193c-ddf5-44ec-8584-1443729<PERSON>ce", "versionId": "0367ab96-f04e-4f66-8ff2-a386f18afe43", "name": "RequestTemplatePojo", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "versionId": "fd3df966-ad36-481d-b66d-1a0b9f1f32fb", "name": "RequestType", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8", "versionId": "f0053244-cb35-4537-a419-e0e17f8ad5f9", "name": "ResponseTemplatePojo", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "versionId": "2cfea046-37c5-434e-9f08-99940c2849ff", "name": "retrieve bills data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "versionId": "863323c5-c9f1-4ebb-8bb6-835644e37b23", "name": "retrieve charges and commision data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.77e38ca7-0df5-4ad2-98b9-25547cc61e1e", "versionId": "057ce9c7-5f9c-4321-89b4-ce0ffdbfbf78", "name": "Retrieve Country List From FC", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2f93c4b5-368e-4a13-aff6-b12926260bb3", "versionId": "a98912a2-804a-4147-876d-4038ef0000fb", "name": "retrieve DB lookups", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5", "versionId": "5534fa7b-3667-4287-83ca-3125449f7b02", "name": "retrieve invoice data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "versionId": "91cf47a3-59bb-4063-9c71-0c4efd9afc23", "name": "retrieve multiTenorDates", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.5ee374de-1b69-445e-8a25-b5cb3f304ab4", "versionId": "55b7163e-f619-4530-b569-bf80238dd204", "name": "retrieve Odc request Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e62adcff-5053-4977-814b-9e511ca5d190", "versionId": "998f25b5-9684-4c61-abb0-a378b8c9bec3", "name": "retrieve Odc request Data oneToMany Relation", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "versionId": "41ebe9f1-593e-4b17-a752-6b1b929d7bf0", "name": "Retrieve ProductCode DB", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "versionId": "f95d146a-6c23-4ba7-85b6-bf3d0ee3d552", "name": "Retrieve Request Number", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "versionId": "b2ded4be-8e2d-4411-8c25-3346f14d22f2", "name": "retrieve Search Criteria Result", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.1b3eba82-0c58-41be-ba53-c071d336f323", "versionId": "d14e6e91-2dc3-4d75-aaf4-7c52bd9ebb44", "name": "Retrieve Search Result Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.f0c268ac-0772-4735-af5b-5fc6caec30a1", "versionId": "20ae7397-1248-4d0c-853a-6467781ec4ed", "name": "Reversal Closure CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "reversalClosureReason", "configOptions": ["reversalReasonVIS", "closureReasonVIS", "executionHubVIS"]}, "hasDetails": true}, {"id": "24.47d810bd-7938-44a3-9e7f-3bdddc0d4dff", "versionId": "d7821a39-b4ab-4795-9239-ff57f7d12c54", "name": "Reversal Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.87350f86-1636-41a7-80ca-60c055fb71f2", "versionId": "5915172e-fe34-4eed-9307-1f0fdde38d45", "name": "Reversal Owners", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "12.cd8189aa-0888-4e27-9937-95dfb001a822", "versionId": "8c909dae-62e2-4f8e-8063-6101e9ab2c34", "name": "ReversalReason", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.bed40437-f5de-4b1c-a063-7040de4075df", "versionId": "006e834d-7c18-4dd6-853e-b16ae35ab834", "name": "ScreenNames", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "64.e2910043-5f79-408a-81b4-3af68cb1deac", "versionId": "35e0a560-da5a-41c8-815e-07c4f6458377", "name": "Search Templete", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "appinfo", "configOptions": ["backBtnVIS", "EndBtnVIS", "errorMsg"], "inlineScripts": []}, "hasDetails": true}, {"id": "12.4e08a264-0cfe-41ef-b4b7-74959345f193", "versionId": "975a8d34-9f4e-483f-928e-101e92da51d7", "name": "searchCriteria", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.0ac53667-a1c4-4086-b17a-f78b72a6299a", "versionId": "2d2eb361-0624-4a07-917c-4c64a464ec28", "name": "searchCriteria", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "12.76e102f0-ce5e-45ff-b7b0-32b0993a6588", "versionId": "7671f21b-6118-4ba6-8474-e5b8a87d8e99", "name": "searchResult", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "versionId": "2adb820e-e4d2-4b13-812b-a734fd7d3c1e", "name": "Send Escalation Mail", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.a6205b87-57cc-47bb-abfc-edff0743b08e", "versionId": "35c7c37e-292f-400c-bfe4-ad2eaa23ecc9", "name": "Send Mail to CAD Team", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.e6908115-4586-455f-bf73-b96b60019972", "versionId": "a2a3e3ed-6c41-41b9-ba99-4e21b4092909", "name": "Send Rem Letter Mail", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "12.8566ac97-e9e5-481e-9605-42b0dc11da47", "versionId": "841603be-bbfc-45e7-a27a-72b8b1fc5b25", "name": "sequence", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.be113c16-c0f5-4037-b134-f403be3fe56f", "versionId": "779fb191-e982-44b2-b736-4f3fa7eb9f53", "name": "Service Flow", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.6a8c5669-195a-4ca0-80cc-606bbf87f9d0", "versionId": "feb0af45-0b25-44df-bad6-f8288abfe0c6", "name": "Service Flow_1", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.2d3ab562-82df-48a5-9de7-f5d964218191", "versionId": "e1c75fd3-f8b2-4779-a4f1-4aecf972a0bc", "name": "Set document default properties", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "versionId": "db4be141-e3c9-4dd3-97a2-997aa022479e", "name": "Set document default properties 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.81656d33-5348-479b-a7af-5631356d9476", "versionId": "0bd7cd63-4a1c-4d29-b8ec-8b4dc6aebb2d", "name": "Set Status And Sub Status", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.7c4993bc-f7d5-4689-96c6-89c5b908583f", "versionId": "3e728fac-930b-4161-9fb2-c7fcac0ddd33", "name": "Start New Request CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "ODCRequest", "configOptions": ["retrieveRequestBtn", "createRequestBtn", "errorMessgae", "retrieveCustomer", "errorPnlVis", "parentrequestTypeVIS", "userConditions", "role"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "///show parent request number if request nature is update request\r\r\nthis.showParentRequestNo = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"update\"){\r\r\n\t\t//this.context.options.parentrequestTypeVIS.set(\"Editable\",  \"NONE\");\r\r\n\t\tthis.ui.get(\"updateRequestTypeSection\").setVisible(true,true);\r\r\n\t}\r\r\n\telse{\r\r\n\t\t//this.context.options.parentrequestTypeVIS.set(\"value\",  \"NONE\");\r\r\n\t\tthis.ui.get(\"updateRequestTypeSection\").setVisible(false,true);\r\r\n\t}\r\r\n}\r\r\n// hide create contract if request nature , request type , cif no and parent request no is empty\r\r\nthis.showCreateBtn = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"\" ||this.context.binding.get(\"value\").get(\"requestType\").get(\"value\") == \"\" ||this.context.binding.get(\"value\").get(\"cif\") == \"\" ||\r\r\n\t(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"update\" && this.context.binding.get(\"value\").get(\"parentRequestNo\") == \"\" ))\r\r\n\t{\r\r\n\t \tthis.ui.get(\"createRequestBtn\").setVisible(false,true);\r\r\n\t}\r\r\n\telse{\r\r\n\t\tthis.ui.get(\"createRequestBtn\").setVisible(true,true);\t\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}, {"id": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "versionId": "bc883da2-9235-4dad-85f4-25817ed4b654", "name": "Start ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "21.340b122c-2fdf-400c-822c-b0c52fb7b022", "versionId": "8cb54f7c-a2db-4b51-ac76-1ecf1f515376", "name": "Status", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "versionId": "4e50d24e-0965-4f32-8eaf-42e04df85414", "name": "StepLog", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.96f93187-360b-430a-9042-84a97749fff7", "versionId": "59ff4d9e-9a2e-4f01-b647-e22f399c32ce", "name": "<PERSON><PERSON><PERSON>", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "64.331e6374-b546-4447-9923-316c70693dd1", "versionId": "0db3b06a-9f16-4991-8ced-d67558109b4c", "name": "temp", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "//alert(this.context.viewid)", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.mandatory2 = function (me){\r\r\n//\tme.setEnabled(false,true)\r\r\n\tme.setValid(false,\"shit\");\r\r\n//\tif(me.getData() == null || me.getData() == undefined){\r\r\n//\t\tme.setValid(false,\"shit\");\r\r\n//\t\treturn false\r\r\n//\t}else{\r\r\n//\t\tme.setValid(true);\r\r\n//\t\treturn true\r\r\n//\t} \r\r\n}\r\r\n\r\r\n//---------------------------------------Validation Library------------------------------------\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nvar validationList = [];\r\r\nthis.addError = function (path, message) {\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n\t// return validationList;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the field is null 'Mandatory', \r\r\n   message is OPTIONAL!! , DEFAULT is 'This Field Is Mandatory',\r\r\n   EX: mandatory(tw.local.name , 'tw.local.name', 'validation message') */\r\r\n\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\tif (!message) {\r\r\n\t\tmessage = \"This Field Is Mandatory\";\r\r\n\t}\r\r\n\r\r\n\tif (value == null) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.mandatoryList = function (mandatoryList) {\r\r\n\tmandatoryList.forEach((element) => {\r\r\n\t\tthis.mandatory(element[0], element[1], element[2]);\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past and the last variable is OPTIONAL to exclude today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude) {\r\r\n\t\tif (value != null && value < checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value <= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude && exclude === true) {\r\r\n\t\tif (value != null && value > checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value >= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (value && value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (value && value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.exactLength = function (value, path, len, message) {\r\r\n\tif (value && value.length != len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (value && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (value && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Validates the before and after decimal length (even if string).\r\r\n   message is OPTIONAL , DEFAULT `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it` \r\r\n   Ex: validateDecimal (tw.local.decimal, 'tw.local.decimal', 10, 6, 'validation message')*/\r\r\nthis.validateDecimal = function (value, path, beforeDecimal, afterDecimal, message) {\r\r\n\tif (!value) return;\r\r\n\tif (!message || message == \"\") {\r\r\n\t\tmessage = `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it`;\r\r\n\t}\r\r\n\t// Handle string or number input\r\r\n\tif (typeof value === \"number\") {\r\r\n\t\tvalue = value.toString();\r\r\n\t}\r\r\n\t// Regex to check overall format\r\r\n\tconst regex = new RegExp(\"^\\\\d{1,\" + beforeDecimal + \"}\\\\.?\\\\d{0,\" + afterDecimal + \"}$\");\r\r\n\r\r\n\tif (regex.test(value) == false) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\tthis.setErrorList();\r\r\n};\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};"}]}, "hasDetails": true}, {"id": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "versionId": "79c6e100-e701-463e-892d-2c931d6ffaa9", "name": "TermsAndConditions", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "1.9862410e-bba9-43a0-b511-69ae076e3b64", "versionId": "06f9ad28-019c-42f1-82d6-ef124c4d347e", "name": "test", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8", "versionId": "ef91abe5-1a3c-439b-88b8-8efbc4c61758", "name": "Test Date", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.98459c6f-cb8f-462d-9fae-63d331db4606", "versionId": "9088f2b0-71c5-4a5b-991b-658253716d2c", "name": "test view", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "newtest", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.changeIt = function(me){\r\r\n\tvar v1 = me.getData();\r\r\n\tvar test2 = bpmext.ui.getView(\"/test_view_21\");\r\r\n\ttest2.setData(v1);\r\r\n\t\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};"}]}, "hasDetails": true}, {"id": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "versionId": "2991e37c-d215-49ba-a874-3dbff01d5c0a", "name": "test view 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "test2", "inlineScripts": []}, "hasDetails": true}, {"id": "1.159431e7-0daa-4266-819a-968be03f82ff", "versionId": "7b68ed25-1cc4-4606-b79a-63185f7456fe", "name": "testASA", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "64.436a7747-a90a-4b7e-8d02-ff4803d46ce0", "versionId": "15d228f6-0bd1-4af6-88b3-1af012b927a5", "name": "testTable", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "fo"}, "hasDetails": true}, {"id": "64.b4f7785b-f352-434f-9dc2-a68e51beac4e", "versionId": "4c168860-9eae-4b1d-a4e0-c42078b4ed87", "name": "testView", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "testData"}, "hasDetails": true}, {"id": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "versionId": "86e4fd32-c430-438d-a46b-870d97e311a2", "name": "Trade Compliance", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": []}, "elements": {"formTasks": [], "callActivities": [], "exclusiveGateways": [], "scriptTasks": []}}, "subType": "10", "hasDetails": true}, {"id": "24.33723a74-9421-41a6-9d94-5464d6689d7d", "versionId": "36fffbb3-e86f-4cfc-ab00-2075df9ab424", "name": "Trade Compliance Checker", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.55c1fe56-ce36-4b4b-87bd-380022cde382", "versionId": "8b8cf17f-db58-4284-86c7-f2ad044d9901", "name": "Trade compliance Maker", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.abae0138-0c20-4914-b2ec-523594f4a93d", "versionId": "352e390c-1ac6-426d-b0e2-6ddda4a012c0", "name": "Trade compliance Manager", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.69e88363-c139-41f2-9dbf-64274c11a0dc", "versionId": "aa8d6d8c-46b0-4bab-92ba-5b71ff9093f4", "name": "Trade finance Control Unit", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89", "versionId": "f8d25f3b-0b03-40df-beee-9a37fd159eaa", "name": "Trade FO Makers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.ec65db21-fa8b-430c-9116-176de416229a", "versionId": "af559561-b69f-477c-ab2b-4b4c3fde2004", "name": "Trade FO Managers", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "versionId": "1175767b-5bee-4b1a-9730-a32d090df2ed", "name": "Update ODC status", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "12.a07f8f54-e398-452e-8542-5bef5fc7fbd7", "versionId": "e4ccea8c-6fec-408d-9bc7-ab9c175cd386", "name": "userConditions", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "versionId": "128acb22-0680-4190-9f73-22129718aeea", "name": "userRole", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "1.f528d3fa-1d30-4c10-bf6b-97d86fd181b6", "versionId": "72575335-0758-4d6b-a6c7-e9ec794dddf0", "name": "UT TF", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.13b84d91-6118-4776-9c68-8accac7d1220", "versionId": "317a030d-7fc8-4bf7-97b2-157878cd7c75", "name": "Validate Required Documents", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "1.46ffdf85-029f-4715-857e-d78bf56f8839", "versionId": "f8eed9d0-81b2-4e47-8fa4-d6d5eebd1940", "name": "Validate Required Documents 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "subType": "12", "hasDetails": false}, {"id": "64.72428c7b-aa19-4400-bea7-59743c5442cc", "versionId": "c79eb96d-5560-467c-84c7-7135acc836e4", "name": "Validation Helper", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"configOptions": ["runTimeValid", "stop"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "_this = this;\r\r\n\r\r\nvar displayText = \"Display_Text1\";\r\r\nconst generalTab = \"GENERAL\";\r\r\nvar tabSectionId = \"\";\r\r\nvar tabSection = \"\";\r\r\nconst errorSectionId = \"ErrorSection_Panel1\";\r\r\nconst stopVU = this.context.options.stop.get(\"value\") || false;\r\r\nconst runTime = this.context.options.runTimeValid.get(\"value\") || false;\r\r\nconst initExpanded = this.context.options.initExpanded?.get(\"value\") || true;\r\r\n\r\r\n_this.startVU = function () {\r\r\n\ttabSection = document.querySelector('[role=\"tablist\"]');\r\r\n\ttabSectionId = _this.getTabSectionId(tabSection);\r\r\n\tconst redCircles = document.querySelectorAll(\".red-circle\");\r\r\n\r\r\n\tvar viewErrorList = [];\r\r\n\tviewErrorList = bpmext.ui.getInvalidViews();\r\r\n\r\r\n\tif (viewErrorList.length == 0) {\r\r\n\t\tthis.ui.get(displayText).setText(\"\");\r\r\n\t\t_this.ui.get(errorSectionId).setVisible(false, true);\r\r\n\t\t_this.resetRedCircle(redCircles);\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tconst errMapList = viewErrorList\r\r\n\t\t.map((view) => _this.constructErrorMap(view))\r\r\n\t\t.filter(function (obj) {\r\r\n\t\t\treturn obj != null;\r\r\n\t\t});\r\r\n\r\r\n\tvar viewMapList = [];\r\r\n\t_this.resetRedCircle(redCircles);\r\r\n\tviewMapList = _this.organizeErrorsByTab(errMapList);\r\r\n\tconsole.dir(viewMapList);\r\r\n\t// Add counter red circle\r\r\n\t_this.resetRedCircle(redCircles);\r\r\n\tviewMapList.forEach((viewMap) => _this.addRedCircleToTab(viewMap));\r\r\n\r\r\n\t//Add panel with tabs and messages\r\r\n\t_this.ui.get(errorSectionId).setVisible(true, true);\r\r\n\tsetTimeout(() => {\r\r\n\t\t_this.constructValidPanel(viewMapList);\r\r\n\t}, 200);\r\r\n};\r\r\n\r\r\n_this.getTabInfoFirst = function (tabSection) {\r\r\n\tconst tabElementList = tabSection?.children;\r\r\n\tvar tabsInfo = {};\r\r\n\tfor (var i = 0; i < tabElementList.length; i++) {\r\r\n\t\tvar tabElement = tabElementList[i];\r\r\n\r\r\n\t\tvar tabInnerText = tabElement.innerText.split(\"\\n\")[0].replaceAll(\" \", \"\");\r\r\n\t\tif (!tabInnerText || tabElement.getAttribute(\"role\") !== \"tab\") continue;\r\r\n\r\r\n\t\ttabsInfo[tabInnerText] = {\r\r\n\t\t\ttabDomID: tabElement.id,\r\r\n\t\t\ttabPathId: i,\r\r\n\t\t};\r\r\n\t}\r\r\n\r\r\n\treturn tabsInfo;\r\r\n};\r\r\n\r\r\n_this.resetRedCircle = function (redCircles) {\r\r\n\t// const redCircles = document.querySelectorAll(\".red-circle\");\r\r\n\tif (!redCircles) return;\r\r\n\tredCircles.forEach((circle) => circle.remove());\r\r\n};\r\r\n\r\r\n_this.getTabSectionId = function (tabSection) {\r\r\n\tif (!tabSection) return;\r\r\n\tvar currentElement = tabSection;\r\r\n\r\r\n\twhile (currentElement && currentElement !== document.body) {\r\r\n\t\tif (currentElement.classList.contains(\"Tab_Section\")) {\r\r\n\t\t\ttabSectionId = currentElement.getAttribute(\"control-name\");\r\r\n\t\t\tbreak;\r\r\n\t\t}\r\r\n\r\r\n\t\tcurrentElement = currentElement.parentElement;\r\r\n\t}\r\r\n\r\r\n\treturn tabSectionId;\r\r\n};\r\r\n\r\r\n_this.constructValidPanel = function (viewMapList) {\r\r\n\tif (!viewMapList || viewMapList.length == 0) return;\r\r\n\tvar tabNameListHTML = ``;\r\r\n\r\r\n\tfor (var i = 0; i < viewMapList.length; i++) {\r\r\n\t\tvar tabData = viewMapList[i].tab;\r\r\n\t\tvar messageList = viewMapList[i].messages;\r\r\n\r\r\n\t\tif (!tabData) continue;\r\r\n\r\r\n\t\tvar tabDomId = tabData.domId;\r\r\n\t\tvar tabName = tabData.name || generalTab;\r\r\n\t\tvar errorListId = `error-list-${tabDomId}`;\r\r\n\t\tvar tabIndex = tabData.pathId;\r\r\n\t\tvar errorListHTML = _this.generateErrorListHTML(messageList, tabName, tabIndex);\r\r\n\r\r\n\t\ttabNameListHTML += _this.generateTabItemHTML(tabName, tabDomId, errorListId, tabIndex, errorListHTML);\r\r\n\t}\r\r\n\r\r\n\ttabNameListHTML = `<ul class=\"tab-list\">${tabNameListHTML}</ul>`;\r\r\n\t_this.ui.get(displayText).setText(tabNameListHTML);\r\r\n};\r\r\n\r\r\n_this.activateTab = function (tabDomId, tabName, tabIndex) {\r\r\n\tif (!tabName || !tabIndex) return;\r\r\n\r\r\n\tif (tabName !== generalTab) {\r\r\n\t\tpage.ui.get(tabSectionId).setCurrentPane(tabIndex);\r\r\n\t\tif (tabDomId) {\r\r\n\t\t\tvar tabElement = document.getElementById(tabDomId);\r\r\n\t\t\t_this.highLighElement(tabElement);\r\r\n\t\t}\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.activateField = function (tabName, panelString, tabIndex, fieldPathId) {\r\r\n\tconst panelList = panelString.split(\"@@\").filter((e) => e !== \"\");\r\r\n\r\r\n\tif (tabIndex && tabSectionId && tabName !== generalTab) {\r\r\n\t\tpage.ui.get(tabSectionId).setCurrentPane(tabIndex);\r\r\n\t}\r\r\n\tif (panelList && panelList.length > 0) {\r\r\n\t\tfor (let i = 0; i < panelList.length; i++) {\r\r\n\t\t\tpage.ui.get(panelList[i]).expand();\r\r\n\t\t}\r\r\n\t}\r\r\n\tif (!fieldPathId) return;\r\r\n\r\r\n\tsetTimeout(() => {\r\r\n\t\t_this.focusOnElement(fieldPathId);\r\r\n\t}, 300);\r\r\n};\r\r\n\r\r\n_this.focusOnElement = function (fieldPathId) {\r\r\n\tvar fieldElement = page.ui.get(fieldPathId).context.element;\r\r\n\t_this.highLighElement(fieldElement);\r\r\n\r\r\n\tpage.ui.get(fieldPathId).focus();\r\r\n};\r\r\n\r\r\n_this.highLighElement = function (fieldElement) {\r\r\n\tif (!fieldElement) return;\r\r\n\r\r\n\tfieldElement.classList.add(\"highlighted-field\");\r\r\n\tsetTimeout(function () {\r\r\n\t\tfieldElement.classList.remove(\"highlighted-field\");\r\r\n\t}, 1500);\r\r\n};\r\r\n\r\r\n_this.addRedCircleToTab = function (viewMap) {\r\r\n\tif (!viewMap.tab.domId) return;\r\r\n\r\r\n\tconst messagesCount = viewMap.messages.length;\r\r\n\tconst tabDomId = viewMap.tab.domId;\r\r\n\tconst tabElement = document.getElementById(tabDomId);\r\r\n\tif (!tabElement) return;\r\r\n\r\r\n\t// Combine DOM reads\r\r\n\tconst existingCircle = tabElement.querySelector(\".red-circle\");\r\r\n\tconst newCircleContent = `<div class=\"red-circle\">${messagesCount}</div>`;\r\r\n\r\r\n\t// Combine DOM writes\r\r\n\tif (!existingCircle) {\r\r\n\t\ttabElement.insertAdjacentHTML(\"beforeend\", newCircleContent);\r\r\n\t} else {\r\r\n\t\texistingCircle.innerText = messagesCount;\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.constructErrorMap = function (fieldElement) {\r\r\n\tif (!fieldElement || !fieldElement.context.element || !fieldElement._bpmextViewNode) return null;\r\r\n\r\r\n\tvar fieldDomId = fieldElement.context.element.id;\r\r\n\tvar fieldParents = _this.getFieldParents(fieldDomId);\r\r\n      // var isField = Object.keys(fieldElement._bpmextViewNode._data.context.subview).length === 0;\r\r\n      var isField = true;\r\r\n\tif (isField) {\r\r\n\t\terrorMap = {\r\r\n\t\t\tfield: {\r\r\n\t\t\t\tmessage: fieldElement._bpmextVE?.errors?.[0]?.message || \"\",\r\r\n\t\t\t\tdomId: fieldDomId,\r\r\n\t\t\t\tpathId: fieldElement.context.element.getAttribute(\"control-name\"),\r\r\n\t\t\t\tviewId: fieldElement.context.element.getAttribute(\"data-viewid\"),\r\r\n\t\t\t\tlabel: fieldElement.getLabel(),\r\r\n\t\t\t},\r\r\n\r\r\n\t\t\tpanels: fieldParents.cPanelList /*[list of \"SPARKCPanel\"]*/,\r\r\n\r\r\n\t\t\tview: fieldParents.viewObj,\r\r\n\t\t};\r\r\n\t\treturn errorMap;\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.getFieldParents = function (elementId) {\r\r\n\tvar fieldParents = {\r\r\n\t\tviewObj: {\r\r\n\t\t\tname: \"\",\r\r\n\t\t\tdomId: \"\",\r\r\n\t\t\tpathId: \"\",\r\r\n\t\t},\r\r\n\t\tcPanelList: [],\r\r\n\t};\r\r\n\tconst cPanelClass = \"Collapsible_Panel\";\r\r\n\tconst tabClass = \"tab-pane\";\r\r\n\r\r\n\tvar currentElement = document.getElementById(elementId);\r\r\n\r\r\n\twhile (currentElement && currentElement !== document.body) {\r\r\n\t\tif (currentElement.classList.contains(tabClass)) {\r\r\n\t\t\tfieldParents.viewObj.name = currentElement.getAttribute(\"aria-label\");\r\r\n\t\t\tfieldParents.viewObj.domId = currentElement.id;\r\r\n\t\t\tfieldParents.viewObj.pathId = currentElement.getAttribute(\"control-name\");\r\r\n\t\t} else if (currentElement.classList.contains(cPanelClass)) {\r\r\n\t\t\tfieldParents.cPanelList.unshift(currentElement.getAttribute(\"control-name\"));\r\r\n\t\t}\r\r\n\r\r\n\t\tcurrentElement = currentElement.parentNode;\r\r\n\t}\r\r\n\tconsole.dir(fieldParents);\r\r\n\treturn fieldParents;\r\r\n};\r\r\n\r\r\n_this.organizeErrorsByTab = function (errorList) {\r\r\n\tconst viewMap = new Map();\r\r\n\tlet tabsInfo = {};\r\r\n\r\r\n\tif (tabSection) {\r\r\n\t\ttabsInfo = _this.getTabInfoFirst(tabSection);\r\r\n\t}\r\r\n\r\r\n\terrorList.forEach((error) => {\r\r\n\t\tif (error) {\r\r\n\t\t\tconst viewName = error.view.name;\r\r\n\t\t\tconst sanitizedViewName = viewName?.replaceAll(\" \", \"\");\r\r\n\r\r\n\t\t\tif (!viewMap.has(viewName)) {\r\r\n\t\t\t\tviewMap.set(viewName, {\r\r\n\t\t\t\t\tview: {\r\r\n\t\t\t\t\t\tname: viewName,\r\r\n\t\t\t\t\t\tdomId: error.view.domId,\r\r\n\t\t\t\t\t\tpathId: error.view.pathId,\r\r\n\t\t\t\t\t},\r\r\n\t\t\t\t\tmessages: [],\r\r\n\t\t\t\t\ttab: {\r\r\n\t\t\t\t\t\tname: viewName,\r\r\n\t\t\t\t\t\tdomId: tabsInfo[sanitizedViewName]?.tabDomID,\r\r\n\t\t\t\t\t\tpathId: tabsInfo[sanitizedViewName]?.tabPathId,\r\r\n\t\t\t\t\t},\r\r\n\t\t\t\t});\r\r\n\t\t\t}\r\r\n\t\t\t// Add the error message to the corresponding tab entry\r\r\n\t\t\tconst viewEntry = viewMap.get(viewName);\r\r\n\t\t\tviewEntry.messages.push({\r\r\n\t\t\t\tmessage: error.field.message,\r\r\n\t\t\t\tfield: {\r\r\n\t\t\t\t\tdomId: error.field.domId,\r\r\n\t\t\t\t\tpathId: error.field.pathId,\r\r\n\t\t\t\t\tviewId: error.field.viewId,\r\r\n\t\t\t\t\tlabel: error.field.label,\r\r\n\t\t\t\t\tpanels: [...error.panels],\r\r\n\t\t\t\t},\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\t// Convert the map values to an array of tab objects\r\r\n\treturn [...viewMap.values()];\r\r\n};\r\r\n\r\r\n_this.generateTabItemHTML = function (tabName, tabDomId, errorListId, tabIndex, errorListHTML) {\r\r\n\tconst initialButtonText = initExpanded ? \"Hide\" : \"Show\";\r\r\n\tconst initialDisplayStyle = initExpanded ? \"block\" : \"none\";\r\r\n\treturn `<li class=\"tab-item\">\r\r\n            <div class=\"tab-container\">\r\r\n                <div class=\"tab-header\">\r\r\n                    <div class=\"gradient-box\">\r\r\n                        <a href=\"#${tabDomId}\" class=\"tab-name\"\r\r\n                            onclick=\"_this.toggleErrorList('${errorListId}'); event.preventDefault(); event.stopPropagation();\">${tabName}</a>\r\r\n                    </div>\r\r\n                </div>\r\r\n                <ul id=\"${errorListId}\" class=\"error-list\" style=\"display: ${initialDisplayStyle};\">${errorListHTML}</ul>\r\r\n            </div>\r\r\n        </li>`;\r\r\n};\r\r\n\r\r\n_this.generateErrorListHTML = function (listOfErrors, tabName, tabIndex) {\r\r\n\treturn listOfErrors\r\r\n\t\t.map(function (error) {\r\r\n\t\t\tconst fieldDomId = error.field.domId;\r\r\n\t\t\tconst fieldPathId = error.field.pathId;\r\r\n\t\t\tconst label = error.field.label;\r\r\n\t\t\tconst targetMessage = `<b>${label}</b> : ${error.message}`;\r\r\n\t\t\tconst panelString = error.field.panels.join(\"@@\");\r\r\n\r\r\n\t\t\treturn `<li><span class=\"bullet\">&#8226;</span> <a href=\"#${fieldDomId}\" class=\"message-link\" message-id=\"${fieldDomId}\" onclick=\"_this.activateField('${tabName}','${panelString}','${tabIndex}','${fieldPathId}');\">${targetMessage}</a></li>`;\r\r\n\t\t})\r\r\n\t\t.join(\"\");\r\r\n};\r\r\n\r\r\n_this.toggleErrorList = function (errorListId) {\r\r\n\tconst errorList = document.getElementById(errorListId);\r\r\n\tif (errorList) {\r\r\n\t\tconst isCollapsed = errorList.style.display === \"none\" || !errorList.style.display;\r\r\n\t\terrorList.style.display = isCollapsed ? \"block\" : \"none\";\r\r\n\t\t//   if (button) {\r\r\n\t\t//       button.textContent = isCollapsed ? \"Hide\" : \"Show\";\r\r\n\t\t//   }\r\r\n\t}\r\r\n};\r\r\n//=======================================REQUIRED===============================================//\r\r\nrequire([\"com.ibm.bpm.coach/engine\"], function (engine) {\r\r\n\tvar dve = engine._deliverValidationEvents;\r\r\n\tengine._deliverValidationEvents = function (event, viewMap, isClear) {\r\r\n\t\tdve(event, viewMap, isClear); // original processing first\r\r\n\t\t// console.log(\"_deliverValidationEvents\", event, viewMap, isClear);\r\r\n\t}.bind(engine);\r\r\n\tvar hve = engine.handleValidationEvent;\r\r\n\tengine.handleValidationEvent = function (event) {\r\r\n\t\thve(event);\r\r\n\t\t// console.log(\"handleValidationEvent\", event);\r\r\n\t\tif (!stopVU) {\r\r\n\t\t\t_this.startVU();\r\r\n\t\t}\r\r\n\t}.bind(engine);\r\r\n});\r\r\n\r\r\nvar uvvs = bpmext && bpmext.ui && bpmext.ui.updateViewValidationState;\r\r\nif (uvvs) {\r\r\n\tbpmext.ui.updateViewValidationState = function (view, event) {\r\r\n\t\tuvvs(view, event); //call original handler\r\r\n\t\t// console.log(\"updateViewValidationState\", view, event);\r\r\n\t\tif (!stopVU && runTime == true) {\r\r\n\t\t\t_this.startVU();\r\r\n\t\t}\r\r\n\t};\r\r\n}"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": "/* Style for the red circle counter */\r\r\n.red-circle {\r\r\n\tposition: absolute;\r\r\n\ttop: 0;\r\r\n\tright: 0;\r\r\n\twidth: 17px;\r\r\n\theight: 17px;\r\r\n\tbackground-color: red;\r\r\n\tborder-radius: 50%;\r\r\n\tdisplay: flex;\r\r\n\tjustify-content: center;\r\r\n\talign-items: center;\r\r\n\tcolor: white;\r\r\n\tfont-weight: bold;\r\r\n}\r\r\n\r\r\n.tab-link {\r\r\n\tfont-size: medium;\r\r\n}\r\r\n\r\r\n/* Style for the tab list */\r\r\n.tab-list {\r\r\n\tlist-style-type: none;\r\r\n\tpadding: 0;\r\r\n\tmargin: 0; /* Remove default margin */\r\r\n}\r\r\n\r\r\n/* Style for each tab item */\r\r\n.tab-item {\r\r\n\tmargin-bottom: 5px; /* Reduce space between tabs */\r\r\n\tborder: none;\r\r\n\tpadding: 5px;\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\tlist-style: none;\r\r\n}\r\r\n\r\r\n/* Style for the tab name */\r\r\n.tab-name {\r\r\n\tfont-size: 16px;\r\r\n\tmargin: 0;\r\r\n\tcolor: #8a1412; /* Replaced red with a shade of orange */\r\r\n\ttext-decoration: none;\r\r\n\tfont-weight: bold;\r\r\n}\r\r\n\r\r\n.tab-name:hover {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400;\r\r\n}\r\r\n\r\r\n.tab-name:focus {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400;\r\r\n}\r\r\n\r\r\n.tab-container {\r\r\n\tdisplay: flex;\r\r\n\tflex-direction: column;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n.tab-header {\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n/* Style for the gradient box */\r\r\n.gradient-box {\r\r\n\tflex: 1;\r\r\n\tpadding: 10px 20px;\r\r\n\tbackground: linear-gradient(45deg, rgba(255, 255, 255, 0.8), rgba(240, 240, 240, 0.8));\r\r\n\tborder-radius: 4px;\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\tcursor: auto; /* Change cursor to auto to indicate non-clickable */\r\r\n\ttransition: none;\r\r\n}\r\r\n\r\r\n/* .gradient-box:hover {\r\r\n\ttransform: scale(1.05);\r\r\n\tbackground: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(240, 240, 240, 1));\r\r\n} */\r\r\n\r\r\n.tab {\r\r\n\tposition: relative;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n.tab::after {\r\r\n\tcontent: attr(error-count);\r\r\n\tcolor: red;\r\r\n\tfont-size: 10px;\r\r\n\tposition: absolute;\r\r\n\tright: 5px;\r\r\n\ttop: 5px;\r\r\n}\r\r\n\r\r\n/* Add animation for the highlighted field */\r\r\n.highlighted-field {\r\r\n\tanimation-name: highlight;\r\r\n\tanimation-duration: 1.5s;\r\r\n}\r\r\n\r\r\n@keyframes highlight {\r\r\n\tfrom {\r\r\n\t\tbackground-color: yellow;\r\r\n\t}\r\r\n\tto {\r\r\n\t\tbackground-color: initial;\r\r\n\t}\r\r\n}\r\r\n\r\r\n.error-list {\r\r\n\tdisplay: none;\r\r\n\tmargin-left: 20px;\r\r\n\tpadding: 0;\r\r\n\tlist-style-type: none;\r\r\n\ttransition: max-height 0.2s ease-out;\r\r\n\toverflow: hidden;\r\r\n}\r\r\n\r\r\n.error-list.collapsed {\r\r\n\tmax-height: 0;\r\r\n\ttransition: max-height 0.3s ease-out;\r\r\n}\r\r\n\r\r\n.error-list.expanded {\r\r\n\tmax-height: 500px; /* Adjust this value as needed */\r\r\n\ttransition: max-height 0.3s ease-in;\r\r\n}\r\r\n\r\r\n/* Style for the toggle button */\r\r\n.toggle-button {\r\r\n\tmargin-left: 10px;\r\r\n\tbackground-color: #f7f7f7; /* Off-white background */\r\r\n\tcolor: #8a1412; /* Darker red for text */\r\r\n\tborder: 1px solid #e0e0e0; /* Off-white border */\r\r\n\tborder-radius: 4px;\r\r\n\tpadding: 5px 10px;\r\r\n\tcursor: pointer;\r\r\n\ttransition: background-color 0.3s ease, transform 0.3s ease;\r\r\n}\r\r\n\r\r\n.toggle-button:hover {\r\r\n\tbackground-color: #e8e8e8; /* Slightly darker off-white on hover */\r\r\n\ttransform: scale(1.05);\r\r\n}\r\r\n\r\r\n.bullet {\r\r\n\tcolor: #ff8c00; /* Replaced red with a shade of orange */\r\r\n\tmargin-right: 5px; /* Adjust spacing between bullet and link */\r\r\n}\r\r\n\r\r\n.message-link {\r\r\n\tcursor: pointer;\r\r\n\tcolor: #8a1412; /* Replaced red with a shade of orange */\r\r\n\ttext-decoration: none;\r\r\n\tpadding: 5px; /* Add padding for better hover area */\r\r\n\ttransition: color 0.2s ease, background-color 0.2s ease; /* Add transition for smooth animation */\r\r\n\tposition: relative;\r\r\n}\r\r\n\r\r\n.message-link:hover {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400; /* Darker shade of orange on hover */\r\r\n\t/* background-color: rgba(255, 140, 0, 0.1); */\r\r\n}"}]}, "hasDetails": true}, {"id": "50.72059ba3-20f9-4926-b151-02b418301dd4", "versionId": "0105edf4-66a1-4613-b304-d4cc62c2595e", "name": "ValidationMessages", "type": "resourceBundleGroup", "typeName": "Resource Bundle", "details": {}}]}