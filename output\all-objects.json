{"totalCount": 48, "objects": [{"id": "1.f24c65ec-bf84-4145-86bf-c3d6aac64167", "versionId": "662a2f49-c07c-40ce-ae65-312db8a43700", "name": "1.f24c65ec-bf84-4145-86bf-c3d6aac64167", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "60.ace32f52-838c-4f58-9bdf-b0c4edc225b9", "versionId": "f907965b-4d7f-4698-9213-c2c7ca1df315", "name": "ace32f52-838c-4f58-9bdf-b0c4edc225b9 Serviço externo operation1", "type": "externalActivity", "typeName": "External Activity", "details": {}}, {"id": "1.b50518e0-7e3a-4e1a-a2d1-6dfba66e21d1", "versionId": "04e9ee0e-3851-43c1-86c9-9e128c3fc245", "name": "AJAX1", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "61.6253f466-59d3-4226-bee6-9b688f53a928", "versionId": "9a9ed192-3468-4527-942e-c22b237cccd4", "name": "apgexport.png", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "61.ba42797f-6efb-447d-814c-3fc043020c9e", "versionId": "d841b5de-bf09-4ba6-98a6-dfe7a0201790", "name": "apgexport.png", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "61.e82b624e-97ce-4617-9a7a-b2093d67cb45", "versionId": "a122ad24-56b9-4445-9e4c-21d0008bf195", "name": "apgexport.png", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "51.beda80b4-2aab-4083-9c9e-b4f651def006", "versionId": "8c83b5e9-4b59-4fa6-b725-abbc6193f6af", "name": "Atributo1", "type": "userAttributeDefinition", "typeName": "User Attribute", "details": {}}, {"id": "1.2f2be729-828d-424a-b820-dd05c5fdc267", "versionId": "9c6d9a2b-23b1-4903-8157-29e25c9a6bbf", "name": "CSHS1", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.c5621759-80de-428a-89a2-86fb776fb9ba", "versionId": "18e71039-a92d-486f-91c4-d66c80d18919", "name": "CSHS2", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.58e2b9fc-0846-494a-b2c4-8c0421da19b8", "versionId": "89700996-d5c9-444f-92b2-6553aff10d98", "name": "CSHS3", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "12.60da4770-d3a3-4937-840f-8fd74f8c33ce", "versionId": "e4f50724-0f37-4374-830b-3225e60f6667", "name": "Dados1", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "12.7425eece-319f-484b-a59f-8efeaaec2582", "versionId": "b5384160-6469-40fe-a242-5ff02b3cb00d", "name": "Dados2", "type": "twClass", "typeName": "Business Object", "details": {}}, {"id": "1.bdd3fc6b-5583-46c6-9ddf-91408b409b0f", "versionId": "64a4e2ac-e566-4ae6-9cf4-751b218e0ca2", "name": "Decision1", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "61.d969694a-1ab6-4f1a-b07a-cf70da0284ef", "versionId": "901e17b2-47cf-4983-a00f-72bace7fda75", "name": "e77f2a7e-10b4-45ee-90eb-e5b1546cc743.zip", "type": "managedAsset", "typeName": "Managed Asset", "details": {}}, {"id": "62.0b946272-4f9c-4cc2-b200-8c7dcd84cced", "versionId": "fb479b21-911b-4d36-9d19-942bfd213f21", "name": "Environment Variables", "type": "environmentVariableSet", "typeName": "Environment Variables", "details": {}}, {"id": "21.ed99f470-25b4-4a03-b89d-888bc265e2aa", "versionId": "2364dacf-6bce-4fdd-8d27-41b83f4a20e7", "name": "EPV1", "type": "epv", "typeName": "Environment Property Variable", "details": {}}, {"id": "24.2a87eb22-940b-4664-be65-5806a5d01ac8", "versionId": "3c34e6db-d79c-448d-8d80-bf72fe8b0127", "name": "Equipa1", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "24.a776709d-cf51-4353-9ca5-42a15c712b02", "versionId": "d915fc56-f819-45ce-a879-9b798f563ae0", "name": "Equipa2", "type": "participant", "typeName": "Participant", "details": {}}, {"id": "71.be51c18b-fbcd-4a02-a662-ac3b51507644", "versionId": "de7fddea-d1af-4efa-8a96-a7118032d440", "name": "Event1", "type": "eventSubscription", "typeName": "Event Subscription", "details": {}}, {"id": "1.c236a1d8-a7f1-4ce1-af92-33d22fc6f33b", "versionId": "846cacfb-94b8-461e-a515-87d9ebacfb8d", "name": "Fluxo de Serviço", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.7f6199ee-84e0-4fd4-aed2-6df579139f8f", "versionId": "b7494878-9ff7-4c11-9e07-75f171b7f40a", "name": "Fluxo de Serviço_1", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.61a0bf71-b88b-4fc3-b79b-f023825ed63e", "versionId": "849e83ef-c42e-418a-8491-c9db4533ded3", "name": "Integration1", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.0295217c-7383-4a27-b311-333ea889f2bb", "versionId": "b65eb81f-6a68-4d7c-b0e1-e0c5c3ee8fe1", "name": "Integration2", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "49.84290403-04af-4258-a028-700286b03e06", "versionId": "00358551-6701-4f1f-bdcf-afde9191a189", "name": "KPI", "type": "metric", "typeName": "Metric", "details": {}}, {"id": "25.0dcc9130-7c0a-4fe9-9055-9e6688d6be01", "versionId": "a8be0ae5-1b04-4588-9019-2ed2411737b5", "name": "Process2", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "25.9358aeb8-22e5-49a5-91ec-e308642efab7", "versionId": "1a3ee2d6-9181-40ed-9de8-a638da13c592", "name": "Processo1", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "25.fc5575cc-e3a5-452a-b444-3a2a876cc3e2", "versionId": "80cd33db-0b32-4749-86de-3424346c9889", "name": "Processo2", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "11.b744896e-f2fd-4d54-90d4-3bb6658f4690", "versionId": "a7b0891f-97ef-4d9d-9f1b-25b5fbf61493", "name": "Report", "type": "report", "typeName": "Report", "details": {}}, {"id": "50.2641e279-160b-4d0d-bc96-528b36793ecf", "versionId": "00f728c3-77cf-4000-937d-f5f09c1ec8db", "name": "Resource1", "type": "resourceBundleGroup", "typeName": "Resource Bundle", "details": {}}, {"id": "20.a0b4d2ce-b601-44f9-9fad-1cf4fc7191cc", "versionId": "b5251097-24e7-4efb-90db-ffa5e657c42f", "name": "Result", "type": "layout", "typeName": "Layout", "details": {}}, {"id": "47.03ba18ea-b1e1-490b-b2fa-345887449938", "versionId": "fecdb217-32a8-4edb-afc6-453181302064", "name": "SAL1", "type": "sla", "typeName": "SLA", "details": {}}, {"id": "13.c6a2ed15-ce98-4ad7-b369-f4aec0d76f7b", "versionId": "383f46cc-46e2-42cf-95e4-07a7bb137595", "name": "Score", "type": "scoreboard", "typeName": "Scoreboard", "details": {}}, {"id": "1.22a9a945-649d-4bc1-9ac0-75f837f4d494", "versionId": "1611ccb5-4efd-4ae8-9c94-27ef99afbbda", "name": "<PERSON><PERSON> tí<PERSON>lo", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "4.09595554-6d3c-4069-8999-00ea6f58fa38", "versionId": "4a9083d8-fcb2-47b0-9d3b-da9aa8319913", "name": "<PERSON><PERSON> tí<PERSON>lo", "type": "underCoverAgent", "typeName": "Undercoveragent", "details": {}}, {"id": "25.05eef081-e788-4d4c-8465-1fcea678278c", "versionId": "f50ca6d3-ee1f-4ada-8ca1-b3e19aadb2fb", "name": "<PERSON><PERSON> tí<PERSON>lo", "type": "bpd", "typeName": "Business Process Definition", "details": {}}, {"id": "64.af46ef40-d360-4e61-a58a-5dcd3b249894", "versionId": "e860c76a-e058-4ce1-95f6-f20f4a182471", "name": "<PERSON><PERSON> tí<PERSON>lo", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {}, "hasDetails": false}, {"id": "1.82b84887-27a8-4ed1-a944-29be95a9b7e5", "versionId": "992543cc-31de-4db5-9afe-8cf6e34fb625", "name": "Sem título2", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "64.393eb28a-759f-4d92-9bee-56f7dab3765f", "versionId": "cc785f21-0d04-42e2-8fd0-2a8a6d5356b9", "name": "Sem título2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "Untitled", "configOptions": ["Untitled1"], "inlineScripts": []}, "hasDetails": true}, {"id": "1.143ff27f-5e08-478c-af65-06723fa26d26", "versionId": "14f9cdff-58fe-4651-9486-e368654c72bf", "name": "Service1", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.c987c4c1-9986-4e5a-b355-9651308f1f80", "versionId": "9cca5474-f48f-4f51-b62d-12fcaafc82b0", "name": "Serviço de implementação", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.07699666-a47b-496a-9826-e8dd33694f7b", "versionId": "5df7e653-a455-4ded-9867-27b1b9b00eec", "name": "Serviço externo", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "1.236d78a5-bcbd-4c3e-a421-cfb181f40791", "versionId": "a9457050-5a86-413e-b5ea-76f6f198cb1c", "name": "Serviço manual de lado-cliente", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "15.8359c270-8960-497f-8ed7-3b4cb8a8d782", "versionId": "960ac38b-e80b-407f-8855-9ab3f0728492", "name": "Timing1", "type": "timingInterval", "typeName": "Timing <PERSON>val", "details": {}}, {"id": "63.550c2db6-dafa-490d-98b5-81a9097f1593", "versionId": "a7220b2e-08ab-47d7-90e8-5b71df1edba4", "name": "<PERSON><PERSON><PERSON> Settings", "type": "projectDefaults", "typeName": "Project Settings", "details": {}}, {"id": "14.8ce729e5-ab2b-42ce-b6f5-6f3c94798330", "versionId": "988bad62-c19c-4e6c-a30c-dae4ad8f26cb", "name": "Track1", "type": "trackingGroup", "typeName": "Tracking Group", "details": {}}, {"id": "1.350b261f-7e76-4fd5-abe4-25817b0090f3", "versionId": "1c04d272-5a1d-4c65-8c34-f974aebaa8c9", "name": "UCA Service", "type": "process", "typeName": "Process", "details": {"processType": "0"}, "subType": "0", "hasDetails": false}, {"id": "4.f32f2065-49b8-4e77-8c58-90d96ffce088", "versionId": "489feab5-9618-4da1-b827-23d616dab054", "name": "UCA1", "type": "underCoverAgent", "typeName": "Undercoveragent", "details": {}}, {"id": "7.152c331d-06cf-4333-9e3f-3d3988774954", "versionId": "e96bfdc9-c491-4826-ab89-fd1b05425cad", "name": "Web", "type": "webService", "typeName": "Web Service", "details": {}}]}