/* CSHS Modal Styles */

/* Reuse base modal styles from coach-view-modal.css */
.cshs-modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
    width: 1200px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.cshs-info {
    padding: 25px;
}

/* CSHS-specific header styling */
.cshs-modal-content .modal-header {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

/* Basic Information Section */
.cshs-basic-info {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 25px;
}

.cshs-basic-info h3 {
    color: #333;
    font-size: 1.2em;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.cshs-basic-info h3::before {
    content: '⚙️';
    margin-right: 10px;
    font-size: 1.1em;
}

/* Variables Section */
.cshs-variables {
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 25px;
}

.cshs-variables h3 {
    color: #333;
    font-size: 1.2em;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.cshs-variables h3::before {
    content: '📊';
    margin-right: 10px;
    font-size: 1.1em;
}

.variables-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.variable-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.variable-group h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

.variable-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.variable-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.variable-name {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
}

.variable-default {
    display: flex;
    align-items: center;
}

.variable-default label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 0.85em;
    color: #6c757d;
    cursor: default;
}

.variable-default input[type="checkbox"] {
    margin: 0;
}

/* Elements Section */
.cshs-elements {
    margin-bottom: 0;
}

.cshs-elements h3 {
    color: #333;
    font-size: 1.2em;
    margin-bottom: 15px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.cshs-elements h3::before {
    content: '🔧';
    margin-right: 10px;
    font-size: 1.1em;
}

.elements-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
}

.element-group {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.element-group h4 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 1em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

.element-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.element-item {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
}

.element-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 10px;
}

.element-name {
    font-family: 'Courier New', monospace;
    font-weight: 600;
    color: #495057;
    flex: 1;
}

.script-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    font-size: 8px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.script-indicator.pre-script {
    background: #28a745;
    title: "Has Pre-Assignment Script";
}

.script-indicator.post-script {
    background: #dc3545;
    title: "Has Post-Assignment Script";
}

.script-content,
.script-detail {
    margin-top: 12px;
    border-top: 1px solid #e9ecef;
    padding-top: 12px;
}

.script-content strong,
.script-detail strong {
    color: #495057;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.script-content pre,
.script-detail pre {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 15px;
    border-radius: 6px;
    overflow-x: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.5;
    margin: 8px 0 0 0;
    border: 1px solid #333;
}

.script-content code,
.script-detail code {
    background: none;
    padding: 0;
    border: none;
    color: inherit;
}

/* No items message */
.no-items {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
    background: white;
    border: 1px dashed #dee2e6;
    border-radius: 6px;
}

/* Clickable CSHS styling in main viewer */
.clickable-cshs {
    cursor: pointer;
    transition: all 0.2s ease;
}

.clickable-cshs:hover {
    background-color: #fff3cd;
    border-radius: 4px;
    padding: 2px 4px;
    margin: -2px -4px;
}

.clickable-cshs .view-details-icon {
    color: #ff6b6b;
    font-weight: bold;
    margin-left: 5px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .cshs-modal-content {
        width: 95vw;
        max-height: 95vh;
    }
    
    .variables-section,
    .elements-section {
        grid-template-columns: 1fr;
    }
    
    .cshs-info {
        padding: 20px;
    }
    
    .variable-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
    
    .script-content pre,
    .script-detail pre {
        font-size: 12px;
        padding: 12px;
    }
}

/* Scrollbar styling for CSHS modal */
.cshs-modal-content .modal-body::-webkit-scrollbar {
    width: 8px;
}

.cshs-modal-content .modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.cshs-modal-content .modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.cshs-modal-content .modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
