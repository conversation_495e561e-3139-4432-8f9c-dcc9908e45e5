<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8" name="Branch Hub filter service">
        <lastModified>1691183197613</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.70833324-3be4-4040-809a-81faaa9881ca</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>2529e9f7-5865-462d-b6f5-19c20bd757d0</guid>
        <versionId>1812600f-233f-4424-8b78-d0d82862e944</versionId>
        <dependencySummary>&lt;dependencySummary id="220ef5bc-97ee-4aa9-aa02-d0755d9361e0" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="originalTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7e19664f-430b-48a0-a9c6-ed148686698e</processParameterId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9c06d0ea-f466-4f13-8cad-a86323c1f3ab</guid>
            <versionId>0046fc4a-ac63-4248-b58d-9253028d7feb</versionId>
        </processParameter>
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b849887a-3e1e-4633-be89-6e2adce84383</processParameterId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3d6aac9b-d956-48c0-a114-35f8d5c88728</guid>
            <versionId>c6d154ed-4a56-4294-8416-8c1d951dec81</versionId>
        </processParameter>
        <processParameter name="hubCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857</processParameterId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4949a95d-1755-4fae-b755-b8cf9b4597ec</guid>
            <versionId>600f7fa6-fc38-4115-a306-189de5d7c286</versionId>
        </processParameter>
        <processParameter name="branchGroupName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6</processParameterId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fefd8e3e-39e5-4a30-9f7e-8c82ba9ceec0</guid>
            <versionId>4d8b4546-9abb-47e7-bc62-8b13c1d9ea0b</versionId>
        </processParameter>
        <processParameter name="hubGroupName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d5717fc2-69b2-4168-a2af-80632cedf962</processParameterId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0144e756-d6fa-4814-b0fb-70d8a04ec201</guid>
            <versionId>31d68e30-eb73-41da-aa96-a5d4d72c26c2</versionId>
        </processParameter>
        <processParameter name="filteredTeam">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b11e99c6-7c3b-44b5-9e1e-0e2d0eae1375</processParameterId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7ea951fb-a168-4e8c-ba15-6754f9f94961</guid>
            <versionId>a89bd25f-5048-4916-89cd-506e7a16cefe</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.70833324-3be4-4040-809a-81faaa9881ca</processItemId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <name>IsHub?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.74ee4e65-ae87-42cc-9dc2-1c266f5e41cc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5557</guid>
            <versionId>3770a89a-9025-406e-b098-a9faa2bbe93a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="125" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.74ee4e65-ae87-42cc-9dc2-1c266f5e41cc</switchId>
                <guid>b3993c17-c024-473e-9276-7746a805bff4</guid>
                <versionId>4c106fc6-2121-47b9-9ae8-a586ca79e2f0</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.26975985-5ad1-4af6-8808-34635886b52e</switchConditionId>
                    <switchId>3013.74ee4e65-ae87-42cc-9dc2-1c266f5e41cc</switchId>
                    <seq>1</seq>
                    <endStateId>guid:14611baa90d47fd1:-642f9074:1894f1bfa8e:469</endStateId>
                    <condition>tw.local.hubCode != "" &amp;&amp; tw.local.hubCode != null</condition>
                    <guid>c7da17e0-0b13-46e4-9e82-7df900c6b217</guid>
                    <versionId>9553d105-92b0-4bab-a5ed-3e693b59e754</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.737d2eee-815a-42c1-88ac-41e98ae4495b</processItemId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.e8caafbd-60e9-4ae6-b927-02de6f06f39d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5556</guid>
            <versionId>7aa281b9-878b-4f82-863f-527ea7bededf</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.e8caafbd-60e9-4ae6-b927-02de6f06f39d</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b95b9633-4ad9-471f-a585-7abfdb542981</guid>
                <versionId>6780209f-6078-4e3e-916a-3a9f0bdff80a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3322adc2-980e-42a4-9f4e-185032fc7bcb</processItemId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <name>Get Branch team</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.290c22b3-1800-4a05-b2c9-d2c2e30e590e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5555</guid>
            <versionId>83bc73f6-4cbf-4edc-901f-a0d2f3ed8b82</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="266" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.290c22b3-1800-4a05-b2c9-d2c2e30e590e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
log.info("filtered team before branch call# "+tw.local.filteredTeam.name);&#xD;
&#xD;
var branch_users = tw.system.org.findRoleByName(tw.local.branchCode).allUsers;&#xD;
var group_users = tw.system.org.findRoleByName(tw.local.branchGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ branch_users);&#xD;
	for (var i=0;i&lt;branch_users.listLength;i++)&#xD;
	{&#xD;
		for(var j=0;j&lt;group_users.listLength;j++)&#xD;
		{&#xD;
			if(branch_users[i].name == group_users[j].name &amp;&amp; group_users[j].name != "") &#xD;
			{&#xD;
				tw.local.filteredTeam.members[j] = group_users[j].name;&#xD;
			}&#xD;
		}&#xD;
		&#xD;
	}&#xD;
	log.info("Filtered team :# "+tw.local.filteredTeam);</script>
                <isRule>false</isRule>
                <guid>9b9202ea-ffb7-4717-8610-8d456d0b95f6</guid>
                <versionId>53c22720-9a14-4380-89a4-9b133fc8c7e8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6</processItemId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <name>Get HUB team</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bd8a5c92-d444-47f2-a8e6-929f3d675c3b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5554</guid>
            <versionId>f2f29ef8-2551-44f7-af91-e43ceb311479</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="278" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bd8a5c92-d444-47f2-a8e6-929f3d675c3b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.hubGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</script>
                <isRule>false</isRule>
                <guid>46fa6ea7-df19-45a9-af5e-536fd7b06df9</guid>
                <versionId>2c5a3d40-7aa9-429f-87a0-7f7863a9ed18</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.70833324-3be4-4040-809a-81faaa9881ca</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Branch Hub filter service" id="1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification ns3:readOnlyOutputs="true">
                        
                        
                        <ns16:dataInput name="originalTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.7e19664f-430b-48a0-a9c6-ed148686698e" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b849887a-3e1e-4633-be89-6e2adce84383">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"BR001_SHR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="hubCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"077"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="branchGroupName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"BPM_IDC_BR_COMP_REP_CHKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="hubGroupName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d5717fc2-69b2-4168-a2af-80632cedf962">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"BPM_IDC_HUB_077_COMP_CHKR"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="filteredTeam" itemSubjectRef="itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0" isCollection="false" id="2055.b11e99c6-7c3b-44b5-9e1e-0e2d0eae1375" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.7e19664f-430b-48a0-a9c6-ed148686698e</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.b849887a-3e1e-4633-be89-6e2adce84383</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.d5717fc2-69b2-4168-a2af-80632cedf962</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="3d594f05-a229-44b2-b433-ba9d2a06bff7">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="2c643e15-7ac8-4575-8379-84abbf677436" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>f364d556-9235-4885-8cfd-ee7a045a1664</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>737d2eee-815a-42c1-88ac-41e98ae4495b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>720aa561-61d8-4ad4-bda1-f6b2f93b67d6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>70833324-3be4-4040-809a-81faaa9881ca</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3322adc2-980e-42a4-9f4e-185032fc7bcb</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="f364d556-9235-4885-8cfd-ee7a045a1664">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.fcbd468a-4705-4edb-a4ae-31a781ae52db</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="737d2eee-815a-42c1-88ac-41e98ae4495b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5556</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>cea29c01-2a83-4866-9a75-49e63811431d</ns16:incoming>
                        
                        
                        <ns16:incoming>70a344c8-a850-438c-974e-13a1b6c1ae93</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f364d556-9235-4885-8cfd-ee7a045a1664" targetRef="70833324-3be4-4040-809a-81faaa9881ca" name="To IsHub?" id="2027.fcbd468a-4705-4edb-a4ae-31a781ae52db">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get HUB team" id="720aa561-61d8-4ad4-bda1-f6b2f93b67d6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="278" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>974b51b4-dc30-4dea-8351-24b486874304</ns16:incoming>
                        
                        
                        <ns16:outgoing>cea29c01-2a83-4866-9a75-49e63811431d</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
var users = tw.system.org.findRoleByName(tw.local.hubGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ users);&#xD;
	for (var i = 0; i &lt; users.listLength ; i++)&#xD;
	{&#xD;
		tw.local.filteredTeam.members[i] = users[i].name;&#xD;
	}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="720aa561-61d8-4ad4-bda1-f6b2f93b67d6" targetRef="737d2eee-815a-42c1-88ac-41e98ae4495b" name="To End" id="cea29c01-2a83-4866-9a75-49e63811431d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="fa3f942d-6a8a-46e0-9815-a4c2421d12fe" name="IsHub?" id="70833324-3be4-4040-809a-81faaa9881ca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="125" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.fcbd468a-4705-4edb-a4ae-31a781ae52db</ns16:incoming>
                        
                        
                        <ns16:outgoing>974b51b4-dc30-4dea-8351-24b486874304</ns16:outgoing>
                        
                        
                        <ns16:outgoing>fa3f942d-6a8a-46e0-9815-a4c2421d12fe</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="70833324-3be4-4040-809a-81faaa9881ca" targetRef="720aa561-61d8-4ad4-bda1-f6b2f93b67d6" name="Yes" id="974b51b4-dc30-4dea-8351-24b486874304">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.hubCode != "" &amp;&amp; tw.local.hubCode != null</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Get Branch team" id="3322adc2-980e-42a4-9f4e-185032fc7bcb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="266" y="210" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>fa3f942d-6a8a-46e0-9815-a4c2421d12fe</ns16:incoming>
                        
                        
                        <ns16:outgoing>70a344c8-a850-438c-974e-13a1b6c1ae93</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.filteredTeam = new tw.object.Team();&#xD;
tw.local.filteredTeam.members = new tw.object.listOf.String();&#xD;
tw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;&#xD;
tw.local.filteredTeam.name = tw.local.originalTeam.name;&#xD;
&#xD;
log.info("filtered team before branch call# "+tw.local.filteredTeam.name);&#xD;
&#xD;
var branch_users = tw.system.org.findRoleByName(tw.local.branchCode).allUsers;&#xD;
var group_users = tw.system.org.findRoleByName(tw.local.branchGroupName).allUsers;&#xD;
	&#xD;
log.info("users::: "+ branch_users);&#xD;
	for (var i=0;i&lt;branch_users.listLength;i++)&#xD;
	{&#xD;
		for(var j=0;j&lt;group_users.listLength;j++)&#xD;
		{&#xD;
			if(branch_users[i].name == group_users[j].name &amp;&amp; group_users[j].name != "") &#xD;
			{&#xD;
				tw.local.filteredTeam.members[j] = group_users[j].name;&#xD;
			}&#xD;
		}&#xD;
		&#xD;
	}&#xD;
	log.info("Filtered team :# "+tw.local.filteredTeam);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="70833324-3be4-4040-809a-81faaa9881ca" targetRef="3322adc2-980e-42a4-9f4e-185032fc7bcb" name="No" id="fa3f942d-6a8a-46e0-9815-a4c2421d12fe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3322adc2-980e-42a4-9f4e-185032fc7bcb" targetRef="737d2eee-815a-42c1-88ac-41e98ae4495b" name="To End" id="70a344c8-a850-438c-974e-13a1b6c1ae93">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.08aaa8ba-254d-4d3a-aa4d-48c2190548dd</processLinkId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.70833324-3be4-4040-809a-81faaa9881ca</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.3322adc2-980e-42a4-9f4e-185032fc7bcb</toProcessItemId>
            <guid>a4d6c0c3-6514-49a9-8c33-a812b06ecaed</guid>
            <versionId>5fc85884-5b08-4f26-a913-562be3453d8d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.70833324-3be4-4040-809a-81faaa9881ca</fromProcessItemId>
            <toProcessItemId>2025.3322adc2-980e-42a4-9f4e-185032fc7bcb</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5392c57d-a7c5-44f9-9157-81992ae6b995</processLinkId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3322adc2-980e-42a4-9f4e-185032fc7bcb</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.737d2eee-815a-42c1-88ac-41e98ae4495b</toProcessItemId>
            <guid>72037cbb-adae-44e0-93b5-4c57de5b0da6</guid>
            <versionId>88563146-a222-4816-9500-801d089397be</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.3322adc2-980e-42a4-9f4e-185032fc7bcb</fromProcessItemId>
            <toProcessItemId>2025.737d2eee-815a-42c1-88ac-41e98ae4495b</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d2ee7b44-a5ee-47a9-8dbe-42f000f2a203</processLinkId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.70833324-3be4-4040-809a-81faaa9881ca</fromProcessItemId>
            <endStateId>guid:14611baa90d47fd1:-642f9074:1894f1bfa8e:469</endStateId>
            <toProcessItemId>2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6</toProcessItemId>
            <guid>4e634fed-6b41-46f6-b8d0-a735bc08fcc9</guid>
            <versionId>98badc20-82a4-4110-975a-62239b103200</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.70833324-3be4-4040-809a-81faaa9881ca</fromProcessItemId>
            <toProcessItemId>2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2272e93e-6e5b-40c1-91f6-025df7b1d366</processLinkId>
            <processId>1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.737d2eee-815a-42c1-88ac-41e98ae4495b</toProcessItemId>
            <guid>7ce76dda-170b-4404-a8d1-624a70a7d581</guid>
            <versionId>d65ed943-3f69-43df-8a99-f39d21481d8e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6</fromProcessItemId>
            <toProcessItemId>2025.737d2eee-815a-42c1-88ac-41e98ae4495b</toProcessItemId>
        </link>
    </process>
</teamworks>

