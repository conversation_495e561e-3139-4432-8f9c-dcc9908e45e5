<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.9e31ab59-ee74-4591-92f7-292855228c1b" name="Get Request Type">
        <lastModified>1699525376842</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>614a8b51-8b26-47ac-a134-50a17aad8448</guid>
        <versionId>7d2185a5-**************-ec1a6488d07d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:651a1a6abf396537:64776e00:18baeba64af:31a1" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["1f8ad6f2-e6bd-44e6-89e9-d5619804d06e"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":11,"y":130,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"cb9db41d-5b7c-4f80-9948-96a35c45a57c"},{"incoming":["807c0996-a9b2-4544-8629-fc9f62e1cd5a","0e9b72bd-0257-4fc7-88e1-3d88364a77f9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":754,"y":130,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7d72"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"8e8070c0-9567-4111-bba6-ef4a4ebab199"},{"startQuantity":1,"outgoing":["2be1d914-05d3-4373-b03f-9ea12439808f"],"incoming":["0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":280,"y":107,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Splitting Data","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"672b6342-65f3-436a-9632-04e88a8ae7b8","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.userConditions = new tw.object.userConditions();\r\nvar arr=[];\r\narr = tw.local.data.split('_');\r\n\r\ntw.local.userConditions.requestNature = arr[0];\r\ntw.local.userConditions.role = arr[1];\r\n\r\nif(tw.local.userConditions.requestNature ==null)\r\nlog.info(\"ODC Service Get request type &gt;&gt;&gt;request nature is empty \");\r\n\r\nif(tw.local.userConditions.role ==null)\r\nlog.info(\"ODC Service Get request type &gt;&gt;&gt;role is empty \");\r\n\r\nlog.info(\"=========================================TEST dataaaaaaa===================\"+tw.local.data);\r\nlog.info(\"array\"+arr);\r\nlog.info(\"role===\"+tw.local.userConditions.role);\r\nlog.info(\"request nature===\"+tw.local.userConditions.requestNature);"]}},{"targetRef":"3587fb39-96f8-4185-8e5a-947aa0152f51","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2be1d914-05d3-4373-b03f-9ea12439808f","sourceRef":"672b6342-65f3-436a-9632-04e88a8ae7b8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorData","isCollection":false,"declaredType":"dataObject","id":"2056.3b33e110-285d-4133-87ce-4929b5df3234"},{"targetRef":"f7a3f1e5-1b93-4d44-8819-65281f0d2cf9","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.data\t  !=\t  null"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Splitting Data","declaredType":"sequenceFlow","id":"1f8ad6f2-e6bd-44e6-89e9-d5619804d06e","sourceRef":"cb9db41d-5b7c-4f80-9948-96a35c45a57c"},{"startQuantity":1,"outgoing":["807c0996-a9b2-4544-8629-fc9f62e1cd5a"],"incoming":["2be1d914-05d3-4373-b03f-9ea12439808f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":510,"y":106,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Conditions","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"3587fb39-96f8-4185-8e5a-947aa0152f51","scriptFormat":"text\/x-javascript","script":{"content":["\r\ntw.local.results    =  new tw.object.listOf.NameValuePair();\r\ntw.local.conditions =  new tw.object.listOf.NameValuePair();\r\n\r\nif(tw.local.userConditions.requestNature.toLowerCase() == \"new\")\r\n{\r\n\ttw.local.conditions[0] = new tw.object.NameValuePair();\r\n\ttw.local.conditions[0].name = \"ODC Create\";\r\n\ttw.local.conditions[0].value = \"\" + tw.epv.RequestType.Create;\r\n\tlog.info(\"requset new\");\r\n}\r\nelse if(tw.local.userConditions.requestNature.toLowerCase() == \"update\")\r\n{\r\n\t\r\n\tif(tw.local.userConditions.role.toLowerCase() == \"hub\")\r\n\t{\r\n\t\ttw.local.conditions[0] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[0].name = \"ODC Amendment\";\r\n\t\ttw.local.conditions[0].value = \"\" + tw.epv.RequestType.Amendment; \r\n\t\t\r\n\t\ttw.local.conditions[1] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[1].name = \"ODC Re-create\";\r\n\t\ttw.local.conditions[1].value = \"\" + tw.epv.RequestType.Recreate; \r\n\t\t\r\n\t\ttw.local.conditions[2] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[2].name = \"ODC Closure\";\r\n\t\ttw.local.conditions[2].value = \"\" + tw.epv.RequestType.Closure;\r\n\t\t\r\n\t\ttw.local.conditions[3] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[3].name = \"ODC Collection\";\r\n\t\ttw.local.conditions[3].value = \"\" + tw.epv.RequestType.Collection;\r\n\t\t\r\n\t\ttw.local.conditions[4] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[4].name = \"ODC Reversal\";\r\n\t\ttw.local.conditions[4].value = \"\"+tw.epv.RequestType.Reversal;\r\n\t}\r\n\telse\r\n\t{\r\n\t\ttw.local.conditions[0] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[0].name = \"ODC Amendment\";\r\n\t\ttw.local.conditions[0].value = \"\" + tw.epv.RequestType.Amendment\r\n\t\t\r\n\t\ttw.local.conditions[1] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[1].name = \"ODC Re-create\";\r\n\t\ttw.local.conditions[1].value =\"\" + tw.epv.RequestType.Recreate;\r\n\t\t\r\n\t\ttw.local.conditions[2] = new tw.object.NameValuePair();\r\n\t\ttw.local.conditions[2].name = \"ODC Closure\";\r\n\t\ttw.local.conditions[2].value =\"\" + tw.epv.RequestType.Closure;  \r\n\t}\r\n}\r\n\r\ntw.local.results = tw.local.conditions;"]}},{"targetRef":"8e8070c0-9567-4111-bba6-ef4a4ebab199","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"807c0996-a9b2-4544-8629-fc9f62e1cd5a","sourceRef":"3587fb39-96f8-4185-8e5a-947aa0152f51"},{"itemSubjectRef":"itm.12.a07f8f54-e398-452e-8542-5bef5fc7fbd7","name":"userConditions","isCollection":false,"declaredType":"dataObject","id":"2056.bd8bcd8b-2570-4869-8e35-6a0d44c70fff"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"conditions","isCollection":true,"declaredType":"dataObject","id":"2056.40597a8f-6de8-4acf-8307-085d3af394a8"},{"outgoing":["0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae","0e9b72bd-0257-4fc7-88e1-3d88364a77f9"],"incoming":["1f8ad6f2-e6bd-44e6-89e9-d5619804d06e"],"default":"0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":155,"y":124,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"f7a3f1e5-1b93-4d44-8819-65281f0d2cf9"},{"targetRef":"672b6342-65f3-436a-9632-04e88a8ae7b8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Splitting Data","declaredType":"sequenceFlow","id":"0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae","sourceRef":"f7a3f1e5-1b93-4d44-8819-65281f0d2cf9"},{"targetRef":"8e8070c0-9567-4111-bba6-ef4a4ebab199","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.data\t  ==\t  null"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"0e9b72bd-0257-4fc7-88e1-3d88364a77f9","sourceRef":"f7a3f1e5-1b93-4d44-8819-65281f0d2cf9"}],"laneSet":[{"id":"12587ddd-5f6a-4198-b0fc-f0bbcd6d0e50","lane":[{"flowNodeRef":["cb9db41d-5b7c-4f80-9948-96a35c45a57c","8e8070c0-9567-4111-bba6-ef4a4ebab199","672b6342-65f3-436a-9632-04e88a8ae7b8","3587fb39-96f8-4185-8e5a-947aa0152f51","f7a3f1e5-1b93-4d44-8819-65281f0d2cf9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"665f2035-d79d-48b7-af85-2f922cbd40d6","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Request Type","declaredType":"process","id":"1.9e31ab59-ee74-4591-92f7-292855228c1b","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.a9c3e67b-ea0d-4703-bb02-67bf96578c41"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"867d54c9-bebc-4d84-810c-4e33b7e86250","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"0d8cc12f-5a1e-442a-88d0-2dfe2aa045b0","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{"dataOutputRefs":["2055.a9c3e67b-ea0d-4703-bb02-67bf96578c41"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"null"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.201a1518-163e-49f7-88dc-41e3217538e2"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.201a1518-163e-49f7-88dc-41e3217538e2</processParameterId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>89dfc6ad-4fb3-4268-b703-87d9d4b283ef</guid>
            <versionId>979a6f20-10d3-4f8e-b6e9-2346e3c3ef2a</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a9c3e67b-ea0d-4703-bb02-67bf96578c41</processParameterId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a6f08fba-85b5-45f5-8e3c-243fe6f0a5b3</guid>
            <versionId>ea2b5ab0-27b5-4ca6-9095-762ea093a792</versionId>
        </processParameter>
        <processVariable name="errorData">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3b33e110-285d-4133-87ce-4929b5df3234</processVariableId>
            <description isNull="true" />
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ed4184c2-07ed-4686-8d85-4b7675718559</guid>
            <versionId>35c9d7bc-c441-4672-b06b-887d91594666</versionId>
        </processVariable>
        <processVariable name="userConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bd8bcd8b-2570-4869-8e35-6a0d44c70fff</processVariableId>
            <description isNull="true" />
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a07f8f54-e398-452e-8542-5bef5fc7fbd7</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fb35e873-1c38-49f2-b110-250b1e9fc147</guid>
            <versionId>2cd85ea9-fba8-4ef9-8550-c616ed6dfa22</versionId>
        </processVariable>
        <processVariable name="conditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.40597a8f-6de8-4acf-8307-085d3af394a8</processVariableId>
            <description isNull="true" />
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fb3aec30-5096-46b8-9ba2-775b14b31390</guid>
            <versionId>f69aeb22-6f0e-4b4e-bd86-898a26794414</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</processItemId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.1b45f4bb-5e63-4934-950e-90e8df1a80ab</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:dca</guid>
            <versionId>a4371e52-9d24-443c-9a8b-845597119bfc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="155" y="124">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.1b45f4bb-5e63-4934-950e-90e8df1a80ab</switchId>
                <guid>eda9c04f-131f-4084-bbee-9abdd7c9464a</guid>
                <versionId>999054bf-f1c2-4b3b-82f8-c81c83a99f85</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.6a6356c8-4481-419e-b0bc-0f938a507b8b</switchConditionId>
                    <switchId>3013.1b45f4bb-5e63-4934-950e-90e8df1a80ab</switchId>
                    <seq>1</seq>
                    <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:31a0</endStateId>
                    <condition>tw.local.data	  ==	  null</condition>
                    <guid>49f09f92-aaf1-463e-ae84-7c4a48e10b47</guid>
                    <versionId>254eeadc-9e42-4767-9f65-f98f5beb6121</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8e8070c0-9567-4111-bba6-ef4a4ebab199</processItemId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.fbeecf10-a386-4a6f-9af9-d6f63415eae0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7d72</guid>
            <versionId>dae689a1-93fc-47ba-918a-eaed3ad16c90</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="754" y="130">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.fbeecf10-a386-4a6f-9af9-d6f63415eae0</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>258b321a-d76c-46f7-a31e-03a6dee0fc14</guid>
                <versionId>9c66953f-3c9b-446c-aee7-13770c963aab</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.672b6342-65f3-436a-9632-04e88a8ae7b8</processItemId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <name>Splitting Data</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.20131aae-c868-4833-89e1-91c2d7522ed2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7d71</guid>
            <versionId>ea710495-8d75-44fb-abfd-8e0f2ececd76</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.e508044d-0e17-47a2-bea6-857751505fac</processItemPrePostId>
                <processItemId>2025.672b6342-65f3-436a-9632-04e88a8ae7b8</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>e4e34c46-118e-4bd2-bc98-f723507dbc32</guid>
                <versionId>51e01828-8190-4952-9c11-5b48976136ea</versionId>
            </processPrePosts>
            <layoutData x="280" y="107">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.20131aae-c868-4833-89e1-91c2d7522ed2</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.userConditions = new tw.object.userConditions();&#xD;
var arr=[];&#xD;
arr = tw.local.data.split('_');&#xD;
&#xD;
tw.local.userConditions.requestNature = arr[0];&#xD;
tw.local.userConditions.role = arr[1];&#xD;
&#xD;
if(tw.local.userConditions.requestNature ==null)&#xD;
log.info("ODC Service Get request type &gt;&gt;&gt;request nature is empty ");&#xD;
&#xD;
if(tw.local.userConditions.role ==null)&#xD;
log.info("ODC Service Get request type &gt;&gt;&gt;role is empty ");&#xD;
&#xD;
log.info("=========================================TEST dataaaaaaa==================="+tw.local.data);&#xD;
log.info("array"+arr);&#xD;
log.info("role==="+tw.local.userConditions.role);&#xD;
log.info("request nature==="+tw.local.userConditions.requestNature);</script>
                <isRule>false</isRule>
                <guid>81e86ddd-f15a-4cf5-a1e2-9359210c12b8</guid>
                <versionId>951e8202-b110-4fbb-85a6-a7582aa180c0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3587fb39-96f8-4185-8e5a-947aa0152f51</processItemId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <name>Conditions</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.35098c40-968f-4c9f-8992-7ad5459e95c1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:989374ae3827db3a:f13aa6c:18aa2e844f6:1f1d</guid>
            <versionId>f3aa2f04-e321-41d4-93b9-5951dd0a4218</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="510" y="106">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.35098c40-968f-4c9f-8992-7ad5459e95c1</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
tw.local.results    =  new tw.object.listOf.NameValuePair();&#xD;
tw.local.conditions =  new tw.object.listOf.NameValuePair();&#xD;
&#xD;
if(tw.local.userConditions.requestNature.toLowerCase() == "new")&#xD;
{&#xD;
	tw.local.conditions[0] = new tw.object.NameValuePair();&#xD;
	tw.local.conditions[0].name = "ODC Create";&#xD;
	tw.local.conditions[0].value = "" + tw.epv.RequestType.Create;&#xD;
	log.info("requset new");&#xD;
}&#xD;
else if(tw.local.userConditions.requestNature.toLowerCase() == "update")&#xD;
{&#xD;
	&#xD;
	if(tw.local.userConditions.role.toLowerCase() == "hub")&#xD;
	{&#xD;
		tw.local.conditions[0] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[0].name = "ODC Amendment";&#xD;
		tw.local.conditions[0].value = "" + tw.epv.RequestType.Amendment; &#xD;
		&#xD;
		tw.local.conditions[1] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[1].name = "ODC Re-create";&#xD;
		tw.local.conditions[1].value = "" + tw.epv.RequestType.Recreate; &#xD;
		&#xD;
		tw.local.conditions[2] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[2].name = "ODC Closure";&#xD;
		tw.local.conditions[2].value = "" + tw.epv.RequestType.Closure;&#xD;
		&#xD;
		tw.local.conditions[3] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[3].name = "ODC Collection";&#xD;
		tw.local.conditions[3].value = "" + tw.epv.RequestType.Collection;&#xD;
		&#xD;
		tw.local.conditions[4] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[4].name = "ODC Reversal";&#xD;
		tw.local.conditions[4].value = ""+tw.epv.RequestType.Reversal;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		tw.local.conditions[0] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[0].name = "ODC Amendment";&#xD;
		tw.local.conditions[0].value = "" + tw.epv.RequestType.Amendment&#xD;
		&#xD;
		tw.local.conditions[1] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[1].name = "ODC Re-create";&#xD;
		tw.local.conditions[1].value ="" + tw.epv.RequestType.Recreate;&#xD;
		&#xD;
		tw.local.conditions[2] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[2].name = "ODC Closure";&#xD;
		tw.local.conditions[2].value ="" + tw.epv.RequestType.Closure;  &#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.results = tw.local.conditions;</script>
                <isRule>false</isRule>
                <guid>def77f1f-cdd7-4f05-beac-654216cdea75</guid>
                <versionId>ca16b214-a880-44ed-8f05-d103225138f0</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.14bae706-038c-4165-8de1-ab4539c376b5</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <guid>deb55274-98e6-4de9-9c0b-b2c58e9ddfd1</guid>
            <versionId>7fb84708-974e-4631-9ca2-50a6228c7062</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.548381b0-785f-4969-bd30-2d5ac766f05e</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <guid>22653561-3ca2-4152-b6dc-deea4357826b</guid>
            <versionId>9dc58abf-1c9c-4932-b58d-bcff41300a40</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="11" y="130">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Request Type" id="1.9e31ab59-ee74-4591-92f7-292855228c1b" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="867d54c9-bebc-4d84-810c-4e33b7e86250" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="0d8cc12f-5a1e-442a-88d0-2dfe2aa045b0" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.201a1518-163e-49f7-88dc-41e3217538e2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">null</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.a9c3e67b-ea0d-4703-bb02-67bf96578c41" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.a9c3e67b-ea0d-4703-bb02-67bf96578c41</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="12587ddd-5f6a-4198-b0fc-f0bbcd6d0e50">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="665f2035-d79d-48b7-af85-2f922cbd40d6" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>cb9db41d-5b7c-4f80-9948-96a35c45a57c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8e8070c0-9567-4111-bba6-ef4a4ebab199</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>672b6342-65f3-436a-9632-04e88a8ae7b8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3587fb39-96f8-4185-8e5a-947aa0152f51</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="cb9db41d-5b7c-4f80-9948-96a35c45a57c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="11" y="130" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1f8ad6f2-e6bd-44e6-89e9-d5619804d06e</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="8e8070c0-9567-4111-bba6-ef4a4ebab199">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="754" y="130" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:7d72</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>807c0996-a9b2-4544-8629-fc9f62e1cd5a</ns16:incoming>
                        
                        
                        <ns16:incoming>0e9b72bd-0257-4fc7-88e1-3d88364a77f9</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Splitting Data" id="672b6342-65f3-436a-9632-04e88a8ae7b8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="280" y="107" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae</ns16:incoming>
                        
                        
                        <ns16:outgoing>2be1d914-05d3-4373-b03f-9ea12439808f</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.userConditions = new tw.object.userConditions();&#xD;
var arr=[];&#xD;
arr = tw.local.data.split('_');&#xD;
&#xD;
tw.local.userConditions.requestNature = arr[0];&#xD;
tw.local.userConditions.role = arr[1];&#xD;
&#xD;
if(tw.local.userConditions.requestNature ==null)&#xD;
log.info("ODC Service Get request type &gt;&gt;&gt;request nature is empty ");&#xD;
&#xD;
if(tw.local.userConditions.role ==null)&#xD;
log.info("ODC Service Get request type &gt;&gt;&gt;role is empty ");&#xD;
&#xD;
log.info("=========================================TEST dataaaaaaa==================="+tw.local.data);&#xD;
log.info("array"+arr);&#xD;
log.info("role==="+tw.local.userConditions.role);&#xD;
log.info("request nature==="+tw.local.userConditions.requestNature);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="672b6342-65f3-436a-9632-04e88a8ae7b8" targetRef="3587fb39-96f8-4185-8e5a-947aa0152f51" name="To End" id="2be1d914-05d3-4373-b03f-9ea12439808f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorData" id="2056.3b33e110-285d-4133-87ce-4929b5df3234" />
                    
                    
                    <ns16:sequenceFlow sourceRef="cb9db41d-5b7c-4f80-9948-96a35c45a57c" targetRef="f7a3f1e5-1b93-4d44-8819-65281f0d2cf9" name="To Splitting Data" id="1f8ad6f2-e6bd-44e6-89e9-d5619804d06e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.data	  !=	  null</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Conditions" id="3587fb39-96f8-4185-8e5a-947aa0152f51">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="510" y="106" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2be1d914-05d3-4373-b03f-9ea12439808f</ns16:incoming>
                        
                        
                        <ns16:outgoing>807c0996-a9b2-4544-8629-fc9f62e1cd5a</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
tw.local.results    =  new tw.object.listOf.NameValuePair();&#xD;
tw.local.conditions =  new tw.object.listOf.NameValuePair();&#xD;
&#xD;
if(tw.local.userConditions.requestNature.toLowerCase() == "new")&#xD;
{&#xD;
	tw.local.conditions[0] = new tw.object.NameValuePair();&#xD;
	tw.local.conditions[0].name = "ODC Create";&#xD;
	tw.local.conditions[0].value = "" + tw.epv.RequestType.Create;&#xD;
	log.info("requset new");&#xD;
}&#xD;
else if(tw.local.userConditions.requestNature.toLowerCase() == "update")&#xD;
{&#xD;
	&#xD;
	if(tw.local.userConditions.role.toLowerCase() == "hub")&#xD;
	{&#xD;
		tw.local.conditions[0] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[0].name = "ODC Amendment";&#xD;
		tw.local.conditions[0].value = "" + tw.epv.RequestType.Amendment; &#xD;
		&#xD;
		tw.local.conditions[1] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[1].name = "ODC Re-create";&#xD;
		tw.local.conditions[1].value = "" + tw.epv.RequestType.Recreate; &#xD;
		&#xD;
		tw.local.conditions[2] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[2].name = "ODC Closure";&#xD;
		tw.local.conditions[2].value = "" + tw.epv.RequestType.Closure;&#xD;
		&#xD;
		tw.local.conditions[3] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[3].name = "ODC Collection";&#xD;
		tw.local.conditions[3].value = "" + tw.epv.RequestType.Collection;&#xD;
		&#xD;
		tw.local.conditions[4] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[4].name = "ODC Reversal";&#xD;
		tw.local.conditions[4].value = ""+tw.epv.RequestType.Reversal;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		tw.local.conditions[0] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[0].name = "ODC Amendment";&#xD;
		tw.local.conditions[0].value = "" + tw.epv.RequestType.Amendment&#xD;
		&#xD;
		tw.local.conditions[1] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[1].name = "ODC Re-create";&#xD;
		tw.local.conditions[1].value ="" + tw.epv.RequestType.Recreate;&#xD;
		&#xD;
		tw.local.conditions[2] = new tw.object.NameValuePair();&#xD;
		tw.local.conditions[2].name = "ODC Closure";&#xD;
		tw.local.conditions[2].value ="" + tw.epv.RequestType.Closure;  &#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.results = tw.local.conditions;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="3587fb39-96f8-4185-8e5a-947aa0152f51" targetRef="8e8070c0-9567-4111-bba6-ef4a4ebab199" name="To End" id="807c0996-a9b2-4544-8629-fc9f62e1cd5a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a07f8f54-e398-452e-8542-5bef5fc7fbd7" isCollection="false" name="userConditions" id="2056.bd8bcd8b-2570-4869-8e35-6a0d44c70fff" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="conditions" id="2056.40597a8f-6de8-4acf-8307-085d3af394a8" />
                    
                    
                    <ns16:exclusiveGateway default="0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae" name="Exclusive Gateway" id="f7a3f1e5-1b93-4d44-8819-65281f0d2cf9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="155" y="124" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1f8ad6f2-e6bd-44e6-89e9-d5619804d06e</ns16:incoming>
                        
                        
                        <ns16:outgoing>0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae</ns16:outgoing>
                        
                        
                        <ns16:outgoing>0e9b72bd-0257-4fc7-88e1-3d88364a77f9</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="f7a3f1e5-1b93-4d44-8819-65281f0d2cf9" targetRef="672b6342-65f3-436a-9632-04e88a8ae7b8" name="To Splitting Data" id="0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f7a3f1e5-1b93-4d44-8819-65281f0d2cf9" targetRef="8e8070c0-9567-4111-bba6-ef4a4ebab199" name="To End" id="0e9b72bd-0257-4fc7-88e1-3d88364a77f9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.data	  ==	  null</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.807c0996-a9b2-4544-8629-fc9f62e1cd5a</processLinkId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3587fb39-96f8-4185-8e5a-947aa0152f51</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.8e8070c0-9567-4111-bba6-ef4a4ebab199</toProcessItemId>
            <guid>38bc5301-5ee5-4a64-b097-d388a3eac531</guid>
            <versionId>4df90b38-4e6f-4b2e-8bab-6011f58c85ea</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3587fb39-96f8-4185-8e5a-947aa0152f51</fromProcessItemId>
            <toProcessItemId>2025.8e8070c0-9567-4111-bba6-ef4a4ebab199</toProcessItemId>
        </link>
        <link name="To Splitting Data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0fa6dc2d-89c1-4ad5-8beb-9ffba57baeae</processLinkId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.672b6342-65f3-436a-9632-04e88a8ae7b8</toProcessItemId>
            <guid>59c02fd4-1acb-4784-a216-fca9dbd0ddad</guid>
            <versionId>accb3637-5dc0-47d7-906d-6e83318c5ac1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</fromProcessItemId>
            <toProcessItemId>2025.672b6342-65f3-436a-9632-04e88a8ae7b8</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0e9b72bd-0257-4fc7-88e1-3d88364a77f9</processLinkId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</fromProcessItemId>
            <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:31a0</endStateId>
            <toProcessItemId>2025.8e8070c0-9567-4111-bba6-ef4a4ebab199</toProcessItemId>
            <guid>8af28aa8-55e7-460a-aee7-7486ca3bfa57</guid>
            <versionId>b6561e24-85b5-4f52-9d16-7b4fc33c7e73</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.f7a3f1e5-1b93-4d44-8819-65281f0d2cf9</fromProcessItemId>
            <toProcessItemId>2025.8e8070c0-9567-4111-bba6-ef4a4ebab199</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2be1d914-05d3-4373-b03f-9ea12439808f</processLinkId>
            <processId>1.9e31ab59-ee74-4591-92f7-292855228c1b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.672b6342-65f3-436a-9632-04e88a8ae7b8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3587fb39-96f8-4185-8e5a-947aa0152f51</toProcessItemId>
            <guid>675daf5b-8653-42b3-b61c-96c7ab66879e</guid>
            <versionId>c00cb3d9-594c-4a68-aaef-f0914a2ecc45</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.672b6342-65f3-436a-9632-04e88a8ae7b8</fromProcessItemId>
            <toProcessItemId>2025.3587fb39-96f8-4185-8e5a-947aa0152f51</toProcessItemId>
        </link>
    </process>
</teamworks>

