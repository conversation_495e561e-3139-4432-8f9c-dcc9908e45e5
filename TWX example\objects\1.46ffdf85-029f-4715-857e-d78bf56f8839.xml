<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.46ffdf85-029f-4715-857e-d78bf56f8839" name="Validate Required Documents 2">
        <lastModified>1699443521757</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.b73973ae-cf61-48f7-a7d7-58462330ed5d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>f5f35ded-a8f3-447f-b553-5ed91276b059</guid>
        <versionId>f8eed9d0-81b2-4e47-8fa4-d6d5eebd1940</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:651a1a6abf396537:64776e00:18baeba64af:-4076" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.fbd093a1-004b-4316-870b-898610f44822"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a6e87649-2ef6-44c9-a228-bf00528a2b8d"},{"incoming":["e75637ab-2efc-4947-a2bf-2a4e34c8dd4f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d2"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"1b5a020e-5857-4a63-8df4-727ea9014b25"},{"targetRef":"b73973ae-cf61-48f7-a7d7-58462330ed5d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Documents Table","declaredType":"sequenceFlow","id":"2027.fbd093a1-004b-4316-870b-898610f44822","sourceRef":"a6e87649-2ef6-44c9-a228-bf00528a2b8d"},{"startQuantity":1,"outgoing":["3247959b-80c1-4c86-8ef6-0073e8dbcb6e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":129,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"ruleSet":[{"rules":[{"name":"rule 1","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TRule","ruleId":"7dabda14-1430-445d-850f-0b1907e63ded","type":"DECISION_TABLE","locale":"en","decisionTableHash":"KdbHXPfaz2BRnCbX42wK1xPSEGQTyI7I8PpVfcdI9AE="}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TRuleSet","locale":"en"}]},"implementation":"##unspecified","name":"Documents Table","isForCompensation":false,"completionQuantity":1,"declaredType":"businessRuleTask","id":"20354183-1ad8-4916-8709-13af38dc69ae"},{"targetRef":"b73973ae-cf61-48f7-a7d7-58462330ed5d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Set Documents List","declaredType":"sequenceFlow","id":"3247959b-80c1-4c86-8ef6-0073e8dbcb6e","sourceRef":"20354183-1ad8-4916-8709-13af38dc69ae"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"result","isCollection":false,"declaredType":"dataObject","id":"2056.3a40c528-feef-4005-919f-0a729c2cfb56"},{"startQuantity":1,"outgoing":["e75637ab-2efc-4947-a2bf-2a4e34c8dd4f"],"incoming":["3247959b-80c1-4c86-8ef6-0073e8dbcb6e","2027.fbd093a1-004b-4316-870b-898610f44822"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":280,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Documents List","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b73973ae-cf61-48f7-a7d7-58462330ed5d","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.requiredDocuments = new tw.object.listOf.String();\r\nif (tw.local.result.length &gt; 0) {\r\n\ttw.local.requiredDocuments = tw.local.result.split(\",\")\r\n}"]}},{"targetRef":"1b5a020e-5857-4a63-8df4-727ea9014b25","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e75637ab-2efc-4947-a2bf-2a4e34c8dd4f","sourceRef":"b73973ae-cf61-48f7-a7d7-58462330ed5d"}],"laneSet":[{"id":"e96dd6c0-c048-476e-a8ec-153dee277950","lane":[{"flowNodeRef":["a6e87649-2ef6-44c9-a228-bf00528a2b8d","1b5a020e-5857-4a63-8df4-727ea9014b25","20354183-1ad8-4916-8709-13af38dc69ae","b73973ae-cf61-48f7-a7d7-58462330ed5d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5d75c2da-0aca-4142-844a-9742baa46b85","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Validate Required Documents 2","declaredType":"process","id":"1.46ffdf85-029f-4715-857e-d78bf56f8839","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requiredDocuments","isCollection":true,"id":"2055.26839f0b-31e7-4eea-8bdf-1df39400f19e"}],"inputSet":[{"dataInputRefs":["2055.9507669b-8a1e-4e66-93da-f6ab79643307","2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c"]}],"outputSet":[{"dataOutputRefs":["2055.26839f0b-31e7-4eea-8bdf-1df39400f19e"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Advance Payment\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestType","isCollection":false,"id":"2055.9507669b-8a1e-4e66-93da-f6ab79643307"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"Correspondent\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"documentSource","isCollection":false,"id":"2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9507669b-8a1e-4e66-93da-f6ab79643307</processParameterId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Advance Payment"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c814dfdb-2a68-4bdd-8b91-fba43fbf4dac</guid>
            <versionId>30367342-18c9-4217-a2de-da657f461795</versionId>
        </processParameter>
        <processParameter name="documentSource">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c</processParameterId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"Correspondent"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7e8688ca-2ae4-44ce-9805-9ddde95d857c</guid>
            <versionId>887ae2c6-4211-4cfd-9e34-537b79a710dc</versionId>
        </processParameter>
        <processParameter name="requiredDocuments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.26839f0b-31e7-4eea-8bdf-1df39400f19e</processParameterId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0978950b-78ac-490b-8a8c-54e481ebd02e</guid>
            <versionId>ee984397-4b41-4f71-83b1-cd2467bda1fa</versionId>
        </processParameter>
        <processVariable name="result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3a40c528-feef-4005-919f-0a729c2cfb56</processVariableId>
            <description isNull="true" />
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>5799ba59-e581-4b5b-b6b4-57ee570647cf</guid>
            <versionId>4d416314-5f34-4387-803e-edc9ac174fb6</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1b5a020e-5857-4a63-8df4-727ea9014b25</processItemId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.599539cc-4c7c-48c1-be0f-e2ec3e710fe1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d2</guid>
            <versionId>0ee236d5-0a9b-40b3-ae4b-cf836b8364f3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.599539cc-4c7c-48c1-be0f-e2ec3e710fe1</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>079011e0-3dc1-411f-8bd9-2fee24f74059</guid>
                <versionId>e680dac2-be51-4b52-99fa-12ed3894e658</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.20354183-1ad8-4916-8709-13af38dc69ae</processItemId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <name>Documents Table</name>
            <tWComponentName>ILOGDecision</tWComponentName>
            <tWComponentId>3026.90ceac86-32ff-4bff-8a0b-a6ee2b5c2453</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d1</guid>
            <versionId>a3fce480-8743-4799-b83f-b24372e31143</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="129" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <iLogDecisionId>3026.90ceac86-32ff-4bff-8a0b-a6ee2b5c2453</iLogDecisionId>
                <definition>&lt;iLogDecision&gt;
  &lt;rule&gt;
    &lt;name&gt;rule 1&lt;/name&gt;
    &lt;ruleId&gt;7dabda14-1430-445d-850f-0b1907e63ded&lt;/ruleId&gt;
    &lt;DT xmlns="http://schemas.ilog.com/Rules/7.0/DecisionTable" Version="7.0"&gt;
      &lt;Body&gt;
        &lt;Properties&gt;
          &lt;Property Name="UI.MediaType"&gt;&lt;![CDATA[Web]]&gt;&lt;/Property&gt;
        &lt;/Properties&gt;
        &lt;Structure&gt;
          &lt;ConditionDefinitions&gt;
            &lt;ConditionDefinition Id="C0"&gt;
              &lt;ExpressionDefinition&gt;
                &lt;Text&gt;&lt;![CDATA[requestType contains &lt;a string&gt;]]&gt;&lt;/Text&gt;
              &lt;/ExpressionDefinition&gt;
            &lt;/ConditionDefinition&gt;
            &lt;ConditionDefinition Id="C2"&gt;
              &lt;ExpressionDefinition&gt;
                &lt;Text&gt;&lt;![CDATA[documentSource is one of &lt;strings&gt;]]&gt;&lt;/Text&gt;
              &lt;/ExpressionDefinition&gt;
            &lt;/ConditionDefinition&gt;
          &lt;/ConditionDefinitions&gt;
          &lt;ActionDefinitions&gt;
            &lt;ActionDefinition Id="A0"&gt;
              &lt;ExpressionDefinition&gt;
                &lt;Text&gt;&lt;![CDATA[set result to &lt;a string&gt;]]&gt;&lt;/Text&gt;
              &lt;/ExpressionDefinition&gt;
            &lt;/ActionDefinition&gt;
          &lt;/ActionDefinitions&gt;
        &lt;/Structure&gt;
        &lt;Contents&gt;
          &lt;Partition DefId="C0"&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["Advance Payment"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["ICAP"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Acknowledgement"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Execution Completion"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Completion"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
            &lt;Condition&gt;
              &lt;Expression&gt;
                &lt;Param&gt;&lt;![CDATA["IDC Payment"]]&gt;&lt;/Param&gt;
              &lt;/Expression&gt;
              &lt;Partition DefId="C2"&gt;
                &lt;Condition&gt;
                  &lt;Expression&gt;
                    &lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;
                  &lt;/Expression&gt;
                  &lt;ActionSet&gt;
                    &lt;Action DefId="A0"&gt;
                      &lt;Expression&gt;
                        &lt;Param&gt;&lt;![CDATA["Customer Request"]]&gt;&lt;/Param&gt;
                      &lt;/Expression&gt;
                    &lt;/Action&gt;
                  &lt;/ActionSet&gt;
                &lt;/Condition&gt;
              &lt;/Partition&gt;
            &lt;/Condition&gt;
          &lt;/Partition&gt;
        &lt;/Contents&gt;
      &lt;/Body&gt;
      &lt;Resources DefaultLocale="en_US"&gt;
        &lt;ResourceSet Locale="en"&gt;
          &lt;Data Name="Definitions(C0)#HeaderText"&gt;&lt;![CDATA[Request Type]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(A0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(C2)#HeaderText"&gt;&lt;![CDATA[Document Source]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(C0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;
          &lt;Data Name="Definitions(C2)#Width"&gt;&lt;![CDATA[227]]&gt;&lt;/Data&gt;
        &lt;/ResourceSet&gt;
      &lt;/Resources&gt;
    &lt;/DT&gt;
    &lt;locale&gt;en&lt;/locale&gt;
    &lt;type&gt;DECISION_TABLE&lt;/type&gt;
  &lt;/rule&gt;
  &lt;locale&gt;en&lt;/locale&gt;
&lt;/iLogDecision&gt;</definition>
                <guid>5406c40b-7338-44f4-b676-5e3d3a3ab7d9</guid>
                <versionId>f798b150-f60c-472b-bf3f-939a3cee11cb</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b73973ae-cf61-48f7-a7d7-58462330ed5d</processItemId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <name>Set Documents List</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e8dd8c25-6327-42d8-b9d0-cf2774b499bd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d3</guid>
            <versionId>dfa33204-196e-4ea2-9c5b-37acd160892d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="280" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e8dd8c25-6327-42d8-b9d0-cf2774b499bd</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.requiredDocuments = new tw.object.listOf.String();&#xD;
if (tw.local.result.length &gt; 0) {&#xD;
	tw.local.requiredDocuments = tw.local.result.split(",")&#xD;
}</script>
                <isRule>false</isRule>
                <guid>bf2f6feb-6715-49d9-8370-020208c79208</guid>
                <versionId>3e88b3f9-3b1f-4936-9d61-b3110de6824a</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.b73973ae-cf61-48f7-a7d7-58462330ed5d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="topCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Validate Required Documents 2" id="1.46ffdf85-029f-4715-857e-d78bf56f8839" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="requestType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.9507669b-8a1e-4e66-93da-f6ab79643307">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Advance Payment"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="documentSource" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"Correspondent"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="requiredDocuments" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" id="2055.26839f0b-31e7-4eea-8bdf-1df39400f19e" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.9507669b-8a1e-4e66-93da-f6ab79643307</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.ff041e96-c9de-4f7a-8cfb-b24b11036c5c</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.26839f0b-31e7-4eea-8bdf-1df39400f19e</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="e96dd6c0-c048-476e-a8ec-153dee277950">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5d75c2da-0aca-4142-844a-9742baa46b85" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a6e87649-2ef6-44c9-a228-bf00528a2b8d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1b5a020e-5857-4a63-8df4-727ea9014b25</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>20354183-1ad8-4916-8709-13af38dc69ae</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b73973ae-cf61-48f7-a7d7-58462330ed5d</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a6e87649-2ef6-44c9-a228-bf00528a2b8d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.fbd093a1-004b-4316-870b-898610f44822</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="1b5a020e-5857-4a63-8df4-727ea9014b25">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:15d2</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e75637ab-2efc-4947-a2bf-2a4e34c8dd4f</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a6e87649-2ef6-44c9-a228-bf00528a2b8d" targetRef="b73973ae-cf61-48f7-a7d7-58462330ed5d" name="To Documents Table" id="2027.fbd093a1-004b-4316-870b-898610f44822">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:businessRuleTask implementation="##unspecified" name="Documents Table" id="20354183-1ad8-4916-8709-13af38dc69ae">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="129" y="57" width="95" height="70" />
                            
                            
                            <ns3:ruleSet>
                                
                                
                                <ns3:locale>en</ns3:locale>
                                
                                
                                <ns3:rules>
                                    
                                    
                                    <ns3:ruleId>7dabda14-1430-445d-850f-0b1907e63ded</ns3:ruleId>
                                    
                                    
                                    <ns3:name>rule 1</ns3:name>
                                    
                                    
                                    <ns3:type>DECISION_TABLE</ns3:type>
                                    
                                    
                                    <ns3:decisionTableDefinition>&lt;?xml version="1.0" encoding="UTF-8"?&gt;&lt;DT xmlns="http://schemas.ilog.com/Rules/7.0/DecisionTable" Version="7.0"&gt;&lt;Body&gt;&lt;Properties&gt;&lt;Property Name="UI.MediaType"&gt;&lt;![CDATA[Web]]&gt;&lt;/Property&gt;&lt;/Properties&gt;&lt;Structure&gt;&lt;ConditionDefinitions&gt;&lt;ConditionDefinition Id="C0"&gt;&lt;ExpressionDefinition&gt;&lt;Text&gt;&lt;![CDATA[requestType contains &lt;a string&gt; ]]&gt;&lt;/Text&gt;&lt;/ExpressionDefinition&gt;&lt;/ConditionDefinition&gt;&lt;ConditionDefinition Id="C2"&gt;&lt;ExpressionDefinition&gt;&lt;Text&gt;&lt;![CDATA[documentSource is one of &lt;strings&gt; ]]&gt;&lt;/Text&gt;&lt;/ExpressionDefinition&gt;&lt;/ConditionDefinition&gt;&lt;/ConditionDefinitions&gt;&lt;ActionDefinitions&gt;&lt;ActionDefinition Id="A0"&gt;&lt;ExpressionDefinition&gt;&lt;Text&gt;&lt;![CDATA[set result to &lt;a string&gt; 
]]&gt;&lt;/Text&gt;&lt;/ExpressionDefinition&gt;&lt;/ActionDefinition&gt;&lt;/ActionDefinitions&gt;&lt;/Structure&gt;&lt;Contents&gt;&lt;Partition DefId="C0"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Advance Payment"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["ICAP"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Acknowledgement"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Execution Completion"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Correspondent cover letter,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Execution"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Completion"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Exporter", "Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request,Invoice,Transport document"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["IDC Payment"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;Partition DefId="C2"&gt;&lt;Condition&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA[{ "Correspondent","Exporter","Importer" }]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;ActionSet&gt;&lt;Action DefId="A0"&gt;&lt;Expression&gt;&lt;Param&gt;&lt;![CDATA["Customer Request"]]&gt;&lt;/Param&gt;&lt;/Expression&gt;&lt;/Action&gt;&lt;/ActionSet&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Condition&gt;&lt;/Partition&gt;&lt;/Contents&gt;&lt;/Body&gt;&lt;Resources DefaultLocale="en_US"&gt;&lt;ResourceSet Locale="en"&gt;&lt;Data Name="Definitions(C0)#HeaderText"&gt;&lt;![CDATA[Request Type]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(A0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(C2)#HeaderText"&gt;&lt;![CDATA[Document Source]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(C0)#Width"&gt;&lt;![CDATA[80]]&gt;&lt;/Data&gt;&lt;Data Name="Definitions(C2)#Width"&gt;&lt;![CDATA[227]]&gt;&lt;/Data&gt;&lt;/ResourceSet&gt;&lt;/Resources&gt;&lt;/DT&gt;</ns3:decisionTableDefinition>
                                    
                                    
                                    <ns3:decisionTableHash>KdbHXPfaz2BRnCbX42wK1xPSEGQTyI7I8PpVfcdI9AE=</ns3:decisionTableHash>
                                    
                                    
                                    <ns3:locale>en</ns3:locale>
                                    
                                
                                </ns3:rules>
                                
                            
                            </ns3:ruleSet>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>3247959b-80c1-4c86-8ef6-0073e8dbcb6e</ns16:outgoing>
                        
                    
                    </ns16:businessRuleTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="20354183-1ad8-4916-8709-13af38dc69ae" targetRef="b73973ae-cf61-48f7-a7d7-58462330ed5d" name="To Set Documents List" id="3247959b-80c1-4c86-8ef6-0073e8dbcb6e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="result" id="2056.3a40c528-feef-4005-919f-0a729c2cfb56">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Documents List" id="b73973ae-cf61-48f7-a7d7-58462330ed5d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="280" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3247959b-80c1-4c86-8ef6-0073e8dbcb6e</ns16:incoming>
                        
                        
                        <ns16:incoming>2027.fbd093a1-004b-4316-870b-898610f44822</ns16:incoming>
                        
                        
                        <ns16:outgoing>e75637ab-2efc-4947-a2bf-2a4e34c8dd4f</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.requiredDocuments = new tw.object.listOf.String();&#xD;
if (tw.local.result.length &gt; 0) {&#xD;
	tw.local.requiredDocuments = tw.local.result.split(",")&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b73973ae-cf61-48f7-a7d7-58462330ed5d" targetRef="1b5a020e-5857-4a63-8df4-727ea9014b25" name="To End" id="e75637ab-2efc-4947-a2bf-2a4e34c8dd4f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set Documents List">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3247959b-80c1-4c86-8ef6-0073e8dbcb6e</processLinkId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.20354183-1ad8-4916-8709-13af38dc69ae</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b73973ae-cf61-48f7-a7d7-58462330ed5d</toProcessItemId>
            <guid>4d3f994a-0a43-4586-a84b-76011cb9a2f7</guid>
            <versionId>874258be-af41-47f1-a7ec-4b0778743f35</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.20354183-1ad8-4916-8709-13af38dc69ae</fromProcessItemId>
            <toProcessItemId>2025.b73973ae-cf61-48f7-a7d7-58462330ed5d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e75637ab-2efc-4947-a2bf-2a4e34c8dd4f</processLinkId>
            <processId>1.46ffdf85-029f-4715-857e-d78bf56f8839</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b73973ae-cf61-48f7-a7d7-58462330ed5d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1b5a020e-5857-4a63-8df4-727ea9014b25</toProcessItemId>
            <guid>cf38aaeb-f981-40f6-b665-57902c4c21f1</guid>
            <versionId>b0e9d7b2-6eb3-4aa5-a944-aa244c6aaedf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b73973ae-cf61-48f7-a7d7-58462330ed5d</fromProcessItemId>
            <toProcessItemId>2025.1b5a020e-5857-4a63-8df4-727ea9014b25</toProcessItemId>
        </link>
    </process>
</teamworks>

