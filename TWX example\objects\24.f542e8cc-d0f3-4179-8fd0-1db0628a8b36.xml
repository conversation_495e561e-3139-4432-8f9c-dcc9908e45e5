<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <participant id="24.f542e8cc-d0f3-4179-8fd0-1db0628a8b36" name="ODC compliance Representative Managers">
        <lastModified>1697551882091</lastModified>
        <lastModifiedBy>abdelrahman.saleh</lastModifiedBy>
        <participantId>24.f542e8cc-d0f3-4179-8fd0-1db0628a8b36</participantId>
        <participantDefinition isNull="true" />
        <simulationGroupSize>2</simulationGroupSize>
        <capacityType>1</capacityType>
        <definitionType>3</definitionType>
        <percentAvailable isNull="true" />
        <percentEfficiency isNull="true" />
        <cost>10.00</cost>
        <currencyCode isNull="true" />
        <image isNull="true" />
        <serviceMembersRef isNull="true" />
        <managersRef isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"team":[{"members":{"UserGroups":[{"name":"BPM_ODC_BR_COMP_REP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_077_COMP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_100_COMP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_200_COMP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_310_COMP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_380_COMP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"BPM_ODC_HUB_599_COMP_MNGR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails","type":"StandardMembers"}]},"documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"ODC compliance Representative Managers","declaredType":"resource","id":"24.f542e8cc-d0f3-4179-8fd0-1db0628a8b36"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.w3.org\/1999\/XPath","id":"24.f542e8cc-d0f3-4179-8fd0-1db0628a8b36"}</jsonData>
        <externalId isNull="true" />
        <description></description>
        <guid>guid:4a051bcdb927c971:-1aedb8c3:18b3d655683:2c9e</guid>
        <versionId>64c78fc6-021c-4feb-8d9d-b6262eb12285</versionId>
        <standardMembers>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_BR_COMP_REP_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_077_COMP_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_100_COMP_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_200_COMP_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_310_COMP_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_380_COMP_MNGR</name>
            </standardMember>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_HUB_599_COMP_MNGR</name>
            </standardMember>
        </standardMembers>
        <teamAssignments />
    </participant>
</teamworks>

