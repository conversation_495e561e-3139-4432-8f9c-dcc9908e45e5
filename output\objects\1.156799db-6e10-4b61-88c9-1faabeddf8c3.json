{"id": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "versionId": "33995fae-589a-4b95-bb3c-8abada244c7e", "name": "Get ODC charges 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "name": "Get ODC charges 2", "lastModified": "1693306531902", "lastModifiedBy": "so<PERSON>ia", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.f198db16-e27b-4457-8ff3-d17f3816b177", "2025.f198db16-e27b-4457-8ff3-d17f3816b177"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "103d7996-2c10-459c-a58d-01919ca8021d", "versionId": "33995fae-589a-4b95-bb3c-8abada244c7e", "dependencySummary": "<dependencySummary id=\"c40070e3-4a5c-45b2-9263-66f8bc913c17\" />", "jsonData": {"isNull": "true"}, "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "productCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d9d27843-3752-45c9-b944-bf32ce4a3c09", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "\"OUBC\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "0764b328-a657-4a28-b78e-47a6157d0539", "versionId": "4e5be491-1ed2-4af0-8a8f-481aa16e3b98"}, {"name": "event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a4842d90-096c-4c24-8a50-166b2dd0c383", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"INIT\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "d6cf5463-2db3-463f-9697-f9ca0f09d051", "versionId": "fec7d728-0852-4b17-81a7-26316607ee74"}, {"name": "chargesList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.03e7d166-18c9-4081-a6df-231eef8fc59b", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "parameterType": "2", "isArrayOf": "true", "classId": "/12.e3018bad-b453-4bf5-96fd-09a5141cd061", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "53df2a52-54c0-48c8-b018-1d4be051139b", "versionId": "e0e1940a-d4d0-47c5-b716-6e563f6210aa"}], "processVariable": [{"name": "chargesAndCommisions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6479ba79-02a5-444a-8a57-ecdd606dff2e", "description": {"isNull": "true"}, "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.e9f65280-afe9-44dc-9616-f95c2a14629e", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5eca3d64-26fe-4468-91ab-2b076129e9ca", "versionId": "0a169136-71ed-42e2-bf71-cc04cd392e4c"}, {"name": "currencyList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b270c145-ac59-4987-bb4f-d7360beb1b7e", "description": {"isNull": "true"}, "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fbaad5b5-ef65-46ea-8aed-7ff1200f2fba", "versionId": "711acca0-d0c2-47f1-92e7-3c18aa775b90"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a17d288c-b00e-47b8-b6ce-31cbbd36e034", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.00e317e4-ab0a-4aaf-949e-420b60b9db54", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:415d794a2c221205:3dfd662b:18a40cdf641:-6780", "versionId": "15db84a2-a492-4969-bea3-0cab1e171b0c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.00e317e4-ab0a-4aaf-949e-420b60b9db54", "haltProcess": "false", "guid": "1c2a9a86-080d-4915-aba4-8fe8bf0827ef", "versionId": "2223d9e7-424b-42b3-95a3-47573276b6e8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f198db16-e27b-4457-8ff3-d17f3816b177", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "name": "MW_FC Retrieve Commission and Charges", "tWComponentName": "SubProcess", "tWComponentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:415d794a2c221205:3dfd662b:18a40cdf641:-6781", "versionId": "1a42ac0a-2401-4689-aeca-8b69ebb73b31", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "122", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.e059295b-f72a-4e32-a329-8d32ebe941de", "guid": "c98eb3f7-8f9a-4f47-97d0-f0ac861be9bf", "versionId": "db3d6435-c4c5-4c51-bf41-c9e0946835c6", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.01b18ab4-cc1e-4eb3-aa76-d0a2d1e13dfc", "processParameterId": "2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "5bc2b378-2afd-4048-be04-8d65d4285e42", "versionId": "4a06313b-b53d-43d6-94a0-0034b535427d", "description": {"isNull": "true"}}, {"name": "event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d18f9b9d-e7af-4256-ae20-4e8c1164c562", "processParameterId": "2055.d8783716-2453-46e3-8522-b1b2504092a2", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "false", "value": "tw.local.event", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "d2b9b8f4-a40a-46cf-9318-8a3e1d426f82", "versionId": "5b25e961-693b-4f75-bfc8-3d6f239b47fb", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e29c9910-e848-4954-80bd-85dd2ef725ee", "processParameterId": "2055.78c4796d-3236-45f3-883b-556500834b95", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "3c9b8de2-c79d-4e85-8638-c712ecf95e4e", "versionId": "79c2429b-7cda-4526-b827-b5816debf03e", "description": {"isNull": "true"}}, {"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e664c246-75f9-4c1c-a49e-af44f22ff824", "processParameterId": "2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "true", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "5cd43a04-8630-4a6e-8d28-c39094d61e1c", "versionId": "87003212-6fd8-45ec-b595-98ebbdde8c47", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.83bd4c16-d003-4204-b1a9-6f9549255799", "processParameterId": "2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "79de3eba-d9a8-4e3b-b430-a2641dc4bb6d", "versionId": "8ac7aa84-4df0-45af-9cb5-34633551cf82", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d9e7e24c-e9c6-4019-a648-76a31a8846af", "processParameterId": "2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "fe89ba7c-497a-47ee-95a5-71bf1902117c", "versionId": "9044d9d9-4723-4c4f-91ef-22c29624bd00", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b973f7da-71ef-494b-904a-501142b01d47", "processParameterId": "2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "true", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "c66cc514-f837-45b7-adfb-fbee1de83720", "versionId": "9b1594ef-5300-4ed5-b1d4-b268407e4333", "description": {"isNull": "true"}}, {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0fe65a0b-a14e-489b-978d-88fd747fd5b1", "processParameterId": "2055.b6919232-c019-43a5-8742-9783cfd63371", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "true", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "282c82a5-6e78-474b-9b68-0d44a6c86848", "versionId": "9cc92e0a-a954-4100-b64d-b804712308d3", "description": {"isNull": "true"}}, {"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.839934a1-d6e3-410f-9746-8b8af75e8df4", "processParameterId": "2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "true", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "d6a3a7d3-8a87-4c34-bd78-bdecfa0d01cc", "versionId": "b72a8981-828e-46fb-9d1c-6ad1be714c99", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b51fea27-320c-4d08-8f58-a3ab9d2f57f6", "processParameterId": "2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "true", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "*************-4ed6-ac68-fc73d1084ba8", "versionId": "cfee57da-c8d7-44bf-81c5-fa0feb7bef3d", "description": {"isNull": "true"}}, {"name": "productCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.8f5be2b9-e41b-4c93-b1eb-419fa686a37a", "processParameterId": "2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "false", "value": "tw.local.productCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "66d6aeb5-75e2-45a4-a315-842547d0537e", "versionId": "d4c33880-527e-4fb5-9cfa-1da22e3f483a", "description": {"isNull": "true"}}, {"name": "chargesAndInterest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ca883e0d-44ec-4a4d-a95f-a51c9e980c75", "processParameterId": "2055.dd74dd40-8fa5-4359-8243-08e144b543d2", "parameterMappingParentId": "3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0", "useDefault": "false", "value": "tw.local.chargesAndCommisions", "classRef": "/12.e9f65280-afe9-44dc-9616-f95c2a14629e", "isList": "true", "isInput": "false", "guid": "642d4476-d291-4714-80ab-3e44abdec7ec", "versionId": "f00b591e-6635-419a-9a8b-0e8bad512248", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6e111222-2d3c-4700-a79e-b42d921042cc", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "name": "Get currency list", "tWComponentName": "SubProcess", "tWComponentId": "3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:415d794a2c221205:3dfd662b:18a40cdf641:-6782", "versionId": "204d1a8d-a1d6-4c94-994e-ec890378aa16", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "257", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180", "attachedProcessRef": "/1.2f93c4b5-368e-4a13-aff6-b12926260bb3", "guid": "51f91e03-dc68-4144-b38d-3c628d31aa0f", "versionId": "e4b0b713-fee1-4b1f-93d9-220f7703d961", "parameterMapping": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.27e579a4-**************-9f5bf6e2f28d", "processParameterId": "2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e", "parameterMappingParentId": "3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180", "useDefault": "false", "value": "\"BPM.IDC_CURRENCY\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isList": "false", "isInput": "true", "guid": "a7e4e694-52f1-4254-a35c-967cc5320d19", "versionId": "d9251712-18c5-4cde-9b6a-da8bebee3a1c", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f75a7e28-65e7-4041-a961-7d663c54efd5", "processParameterId": "2055.91436cf4-7a08-4359-8624-bbfa8c1147c0", "parameterMappingParentId": "3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180", "useDefault": "false", "value": "tw.local.currencyList", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isList": "true", "isInput": "false", "guid": "b19289cc-3e3e-4dc2-8ce3-0fe09e975528", "versionId": "ed63ba78-3aec-44fc-a7b9-73d405fe0e48", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.16945213-3ed7-41f0-96b3-de59196f3eea", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "name": "Map output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.9bf5e2eb-7e6b-400a-a14a-a53545ae86e4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:415d794a2c221205:3dfd662b:18a40cdf641:-6783", "versionId": "6c00ec3e-9af0-4b61-bba8-470d617abf53", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "391", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.9bf5e2eb-7e6b-400a-a14a-a53545ae86e4", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();\r\r\nif(tw.local.chargesAndCommisions != null)\r\r\n{\r\r\n\tfor(var i=0; i<tw.local.chargesAndCommisions.listLength;i++)\r\r\n\t{\r\r\n\t\tif(tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == \"charge\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.chargesList[i] = new tw.object.ChargesAndCommissions();\r\r\n//\t\t\ttw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].defaultAmount;\r\r\n\t\t\ttw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;\r\r\n\t\t\ttw.local.chargesList[i].basicAmountCurrency = tw.local.chargesAndCommisions[i].basisAmountCurrency;\r\r\n\t\t\ttw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();\r\r\n\r\r\n\t\t\tfor(var x=0;x<tw.local.currencyList.listLength;x++)\r\r\n\t\t\t{\r\r\n\t\t\t\tif(tw.local.chargesAndCommisions[i].basisAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;\r\r\n\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t\t//tw.local.chargesList[i].defaultCurrency.name = tw.local.chargesAndCommisions[i].basisAmountCurrency;\r\r\n\t\t\ttw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.name = \"\";\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.value = \"\";\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.name = \"\";\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.value = \"\";\r\r\n\t\t\ttw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;\r\r\n\t\t\t\r\r\n\t\t\tif(tw.local.chargesAndCommisions[i].rateType.toLowerCase() == \"Flat Amount\")\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].flatAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);\r\r\n\t\t\t\ttw.local.chargesList[i].defaultAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);//added by sg\r\r\n\t\t\t}\r\r\n\t\t\telse\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].defaultPercentage = parseFloat(tw.local.chargesAndCommisions[i].rate);\r\r\n//\t\t\t\ttw.local.chargesList[i].minimumAmount = parseFloat(tw.local.chargesAndCommisions[i].minAmount);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tif(tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == \"y\")\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].waiver = true;\r\r\n\t\t\t}\r\r\n\t\t\telse\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].waiver = false;\t\t\r\r\n\t\t\t}\r\r\n\r\r\n\t\t}\r\r\n\t\t\t\t\r\r\n\t}\r\r\n}", "isRule": "false", "guid": "ea4cfd66-7e7a-4c82-a3ae-9b3b4074b438", "versionId": "121f0bc5-7987-46c5-9855-dc78d68fdbf5"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get ODC charges 2", "id": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:localizationResourceLinks": ""}, "ns16:dataInput": [{"name": "productCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.d9d27843-3752-45c9-b944-bf32ce4a3c09", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"OUBC\"", "useDefault": "true"}}}, {"name": "event", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a4842d90-096c-4c24-8a50-166b2dd0c383", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"INIT\"", "useDefault": "true"}}}], "ns16:dataOutput": {"name": "chargesList", "itemSubjectRef": "itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061", "isCollection": "true", "id": "2055.03e7d166-18c9-4081-a6df-231eef8fc59b"}, "ns16:inputSet": {"ns16:dataInputRefs": ["2055.d9d27843-3752-45c9-b944-bf32ce4a3c09", "2055.a4842d90-096c-4c24-8a50-166b2dd0c383"]}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.03e7d166-18c9-4081-a6df-231eef8fc59b"}}, "ns16:laneSet": {"id": "0f528387-b112-4312-bd59-de7a5c5e376a", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "61d5b9a8-ca40-43d7-b57e-af88bc3b9cce", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["9399e6fa-c175-4250-8801-977a0a63176d", "a17d288c-b00e-47b8-b6ce-31cbbd36e034", "f198db16-e27b-4457-8ff3-d17f3816b177", "16945213-3ed7-41f0-96b3-de59196f3eea", "6e111222-2d3c-4700-a79e-b42d921042cc"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "9399e6fa-c175-4250-8801-977a0a63176d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.931f06d5-aa73-4501-9fba-64a012bf0d11"}, "ns16:endEvent": {"name": "End", "id": "a17d288c-b00e-47b8-b6ce-31cbbd36e034", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:415d794a2c221205:3dfd662b:18a40cdf641:-6780"}, "ns16:incoming": "68db8231-6b71-406d-a596-569b13ba6c96"}, "ns16:sequenceFlow": [{"sourceRef": "9399e6fa-c175-4250-8801-977a0a63176d", "targetRef": "f198db16-e27b-4457-8ff3-d17f3816b177", "name": "To MW_FC Retrieve Commission and Charges", "id": "2027.931f06d5-aa73-4501-9fba-64a012bf0d11", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "f198db16-e27b-4457-8ff3-d17f3816b177", "targetRef": "6e111222-2d3c-4700-a79e-b42d921042cc", "name": "To Get currency list", "id": "e18f3b9d-1afc-47f9-b5b7-79a315c4131c", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20"}}, {"sourceRef": "16945213-3ed7-41f0-96b3-de59196f3eea", "targetRef": "a17d288c-b00e-47b8-b6ce-31cbbd36e034", "name": "To End", "id": "68db8231-6b71-406d-a596-569b13ba6c96", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "6e111222-2d3c-4700-a79e-b42d921042cc", "targetRef": "16945213-3ed7-41f0-96b3-de59196f3eea", "name": "To Map output", "id": "3c4e6606-a6ca-40b4-ab18-ea14ab215d6d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24"}}], "ns16:callActivity": [{"calledElement": "1.e059295b-f72a-4e32-a329-8d32ebe941de", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "MW_FC Retrieve Commission and Charges", "id": "f198db16-e27b-4457-8ff3-d17f3816b177", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "122", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "2027.931f06d5-aa73-4501-9fba-64a012bf0d11", "ns16:outgoing": "e18f3b9d-1afc-47f9-b5b7-79a315c4131c", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a", "ns16:assignment": {"ns16:from": {"_": "tw.local.productCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.d8783716-2453-46e3-8522-b1b2504092a2", "ns16:assignment": {"ns16:from": {"_": "tw.local.event", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.dd74dd40-8fa5-4359-8243-08e144b543d2", "ns16:assignment": {"ns16:to": {"_": "tw.local.chargesAndCommisions", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e"}}}}, {"calledElement": "1.2f93c4b5-368e-4a13-aff6-b12926260bb3", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Get currency list", "id": "6e111222-2d3c-4700-a79e-b42d921042cc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "257", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "e18f3b9d-1afc-47f9-b5b7-79a315c4131c", "ns16:outgoing": "3c4e6606-a6ca-40b4-ab18-ea14ab215d6d", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e", "ns16:assignment": {"ns16:from": {"_": "\"BPM.IDC_CURRENCY\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.91436cf4-7a08-4359-8624-bbfa8c1147c0", "ns16:assignment": {"ns16:to": {"_": "tw.local.currencyList", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13"}}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Map output", "id": "16945213-3ed7-41f0-96b3-de59196f3eea", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "391", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "3c4e6606-a6ca-40b4-ab18-ea14ab215d6d", "ns16:outgoing": "68db8231-6b71-406d-a596-569b13ba6c96", "ns16:script": "tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();\r\r\nif(tw.local.chargesAndCommisions != null)\r\r\n{\r\r\n\tfor(var i=0; i<tw.local.chargesAndCommisions.listLength;i++)\r\r\n\t{\r\r\n\t\tif(tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == \"charge\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.chargesList[i] = new tw.object.ChargesAndCommissions();\r\r\n//\t\t\ttw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].defaultAmount;\r\r\n\t\t\ttw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;\r\r\n\t\t\ttw.local.chargesList[i].basicAmountCurrency = tw.local.chargesAndCommisions[i].basisAmountCurrency;\r\r\n\t\t\ttw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();\r\r\n\r\r\n\t\t\tfor(var x=0;x<tw.local.currencyList.listLength;x++)\r\r\n\t\t\t{\r\r\n\t\t\t\tif(tw.local.chargesAndCommisions[i].basisAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;\r\r\n\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t\t//tw.local.chargesList[i].defaultCurrency.name = tw.local.chargesAndCommisions[i].basisAmountCurrency;\r\r\n\t\t\ttw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.name = \"\";\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.value = \"\";\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.name = \"\";\r\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.value = \"\";\r\r\n\t\t\ttw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;\r\r\n\t\t\t\r\r\n\t\t\tif(tw.local.chargesAndCommisions[i].rateType.toLowerCase() == \"Flat Amount\")\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].flatAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);\r\r\n\t\t\t\ttw.local.chargesList[i].defaultAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);//added by sg\r\r\n\t\t\t}\r\r\n\t\t\telse\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].defaultPercentage = parseFloat(tw.local.chargesAndCommisions[i].rate);\r\r\n//\t\t\t\ttw.local.chargesList[i].minimumAmount = parseFloat(tw.local.chargesAndCommisions[i].minAmount);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tif(tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == \"y\")\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].waiver = true;\r\r\n\t\t\t}\r\r\n\t\t\telse\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.chargesList[i].waiver = false;\t\t\r\r\n\t\t\t}\r\r\n\r\r\n\t\t}\r\r\n\t\t\t\t\r\r\n\t}\r\r\n}"}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e", "isCollection": "true", "name": "chargesAndCommisions", "id": "2056.6479ba79-02a5-444a-8a57-ecdd606dff2e"}, {"itemSubjectRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "isCollection": "true", "name": "currencyList", "id": "2056.b270c145-ac59-4987-bb4f-d7360beb1b7e"}]}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.50eb924d-e6dd-476c-a03d-cca0a7f91ba6", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.16945213-3ed7-41f0-96b3-de59196f3eea", "2025.16945213-3ed7-41f0-96b3-de59196f3eea"], "endStateId": "Out", "toProcessItemId": ["2025.a17d288c-b00e-47b8-b6ce-31cbbd36e034", "2025.a17d288c-b00e-47b8-b6ce-31cbbd36e034"], "guid": "83f80a0a-0d69-4164-a636-61c8092907a7", "versionId": "2088d9dc-24ed-4a8a-b710-7e35f1ed3953", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Map output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.c5cdf520-c688-4e72-9107-781b7125a0fc", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.6e111222-2d3c-4700-a79e-b42d921042cc", "2025.6e111222-2d3c-4700-a79e-b42d921042cc"], "endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24", "toProcessItemId": ["2025.16945213-3ed7-41f0-96b3-de59196f3eea", "2025.16945213-3ed7-41f0-96b3-de59196f3eea"], "guid": "0565deda-755b-48fc-a935-a1ce4ede572b", "versionId": "928e3ba0-c548-48b7-bea7-d42fe23f4cb8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get currency list", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.30ed345b-e509-432e-a90e-0ec87ab655b6", "processId": "1.156799db-6e10-4b61-88c9-1faabeddf8c3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f198db16-e27b-4457-8ff3-d17f3816b177", "2025.f198db16-e27b-4457-8ff3-d17f3816b177"], "endStateId": "guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20", "toProcessItemId": ["2025.6e111222-2d3c-4700-a79e-b42d921042cc", "2025.6e111222-2d3c-4700-a79e-b42d921042cc"], "guid": "8fb422f6-1a51-444b-9ffa-08e36a39a60b", "versionId": "b040f090-2bf0-4baa-9bd5-2d3432b33e60", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}