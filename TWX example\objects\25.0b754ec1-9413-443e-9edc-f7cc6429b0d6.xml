<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.0b754ec1-9413-443e-9edc-f7cc6429b0d6" name="ODC Collection تسجيل حصائل على مستند تحصيل تصدير">
        <bpdParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.c2d7852f-6525-4794-8231-2b0bfd65a374</bpdParameterId>
            <bpdId>25.0b754ec1-9413-443e-9edc-f7cc6429b0d6</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>0</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue>var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject</defaultValue>
            <isReadOnly>false</isReadOnly>
            <guid>7205fa59-a9c0-4a8e-a035-ffd5a6a3e118</guid>
            <versionId>abdf3c33-8c22-44c0-9179-73b8f5da8bf6</versionId>
        </bpdParameter>
        <bpdParameter name="routingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d</bpdParameterId>
            <bpdId>25.0b754ec1-9413-443e-9edc-f7cc6429b0d6</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
            <seq>1</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue>var autoObject = new tw.object.odcRoutingDetails();
autoObject.hubCode = "";
autoObject.branchCode = "";
autoObject.initiatorUser = "";
autoObject.branchName = "";
autoObject.hubName = "";
autoObject.branchSeq = "";
autoObject</defaultValue>
            <isReadOnly>false</isReadOnly>
            <guid>ac65c700-c0c6-4fa7-a197-a566d88c934d</guid>
            <versionId>57309e69-e3ac-42ad-9748-96e3aa7a3806</versionId>
        </bpdParameter>
        <lastModified>*************</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <bpdId>25.0b754ec1-9413-443e-9edc-f7cc6429b0d6</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</perfMetricParticipantRef>
        <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ownerTeamParticipantRef>
        <timeScheduleType isNull="true" />
        <timeScheduleName>NBEWork</timeScheduleName>
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName>NBEHoliday</holidayScheduleName>
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone>Africa/Cairo</timezone>
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description>&lt;html&gt;&lt;body&gt;-&lt;span style="white-space:pre"&gt;	&lt;/span&gt;This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.&lt;/body&gt;&lt;/html&gt;</description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="e72e3272-05a7-40e4-90d3-3f1b77bcd85c" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="ODC Collection تسجيل حصائل على مستند تحصيل تصدير" id="25.0b754ec1-9413-443e-9edc-f7cc6429b0d6" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation textFormat="text/plain"&gt;-&amp;lt;span style="white-space:pre"&amp;gt;	&amp;lt;/span&amp;gt;This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.&lt;/ns15:documentation&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot; Request Type: &amp;quot;+ tw.local.odcRequest.requestType.value +&amp;quot; CIF : &amp;quot;+ tw.local.odcRequest.cif +&amp;quot; Request Number: &amp;quot;+ tw.system.process.instanceId" dueDateEnabled="false" atRiskCalcEnabled="false" enableTracking="true" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at1691923319746"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:timezone&gt;Africa/Cairo&lt;/ns4:timezone&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="9ead2f75-765e-4737-9f97-38a0bf8292d2" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:extensionElements&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd" epvProcessLinkId="cf5f9c3e-3504-4e3d-85f5-0c9fe51046cb" /&gt;&lt;ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="09d9adeb-4ea2-421d-809b-290fce12ec80" /&gt;&lt;ns3:epvProcessLinkRef epvId="21.00a114f4-2b01-4c48-aad9-bd62580da24b" epvProcessLinkId="0c9a0944-2ae5-405d-85d1-2f6711b8e55d" /&gt;&lt;ns3:epvProcessLinkRef epvId="21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c" epvProcessLinkId="3f2fb007-9b49-4d8e-8271-feacc32ead54" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2007.c2d7852f-6525-4794-8231-2b0bfd65a374"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:searchableField id="5d556fee-ab55-426f-8fae-c633625c524d" alias="CustomerCIF" path=".CustomerInfo.cif" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="8d013837-0e10-4860-8a09-3c720a4eff7c" alias="CustomerName" path=".CustomerInfo.customerName" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="0df39e40-549e-4dfe-8620-b81e23db8442" alias="ParentRequestNumber" path=".parentRequestNo" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="156d15c5-3e68-43b1-813f-d4342fd62a41" alias="Initiation" path=".initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="d1326d94-db9a-43ca-80f9-ed14f69e5b5b" alias="RequestDate" path=".appInfo.requestDate" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="2bf36b4f-823f-4d6c-81bb-c850cb059591" alias="RequestStatus" path=".appInfo.status" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="f5e084bc-df19-47f8-8e4e-a849582d1912" alias="initiatorUsername" path=".appInfo.initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns3:defaultValue useDefault="false"&gt;var autoObject = new tw.object.ODCRequest();&#xD;
autoObject.initiator = "";&#xD;
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.requestNature.name = "";&#xD;
autoObject.requestNature.value = "";&#xD;
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.requestType.name = "";&#xD;
autoObject.requestType.value = "";&#xD;
autoObject.cif = "";&#xD;
autoObject.customerName = "";&#xD;
autoObject.parentRequestNo = "";&#xD;
autoObject.requestDate = new TWDate();&#xD;
autoObject.ImporterName = "";&#xD;
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();&#xD;
autoObject.appInfo.requestDate = "";&#xD;
autoObject.appInfo.status = "";&#xD;
autoObject.appInfo.subStatus = "";&#xD;
autoObject.appInfo.initiator = "";&#xD;
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.appInfo.branch.name = "";&#xD;
autoObject.appInfo.branch.value = "";&#xD;
autoObject.appInfo.requestName = "";&#xD;
autoObject.appInfo.requestType = "";&#xD;
autoObject.appInfo.stepName = "";&#xD;
autoObject.appInfo.appRef = "";&#xD;
autoObject.appInfo.appID = "";&#xD;
autoObject.appInfo.instanceID = "";&#xD;
autoObject.CustomerInfo = new tw.object.CustomerInfo();&#xD;
autoObject.CustomerInfo.cif = "";&#xD;
autoObject.CustomerInfo.customerName = "";&#xD;
autoObject.CustomerInfo.addressLine1 = "";&#xD;
autoObject.CustomerInfo.addressLine2 = "";&#xD;
autoObject.CustomerInfo.addressLine3 = "";&#xD;
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.CustomerInfo.customerSector.name = "";&#xD;
autoObject.CustomerInfo.customerSector.value = "";&#xD;
autoObject.CustomerInfo.customerType = "";&#xD;
autoObject.CustomerInfo.customerNoCBE = "";&#xD;
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.CustomerInfo.facilityType.name = "";&#xD;
autoObject.CustomerInfo.facilityType.value = "";&#xD;
autoObject.CustomerInfo.commercialRegistrationNo = "";&#xD;
autoObject.CustomerInfo.commercialRegistrationOffice = "";&#xD;
autoObject.CustomerInfo.taxCardNo = "";&#xD;
autoObject.CustomerInfo.importCardNo = "";&#xD;
autoObject.CustomerInfo.initiationHub = "";&#xD;
autoObject.CustomerInfo.country = "";&#xD;
autoObject.BasicDetails = new tw.object.BasicDetails();&#xD;
autoObject.BasicDetails.requestNature = "";&#xD;
autoObject.BasicDetails.requestType = "";&#xD;
autoObject.BasicDetails.parentRequestNo = "";&#xD;
autoObject.BasicDetails.requestState = "";&#xD;
autoObject.BasicDetails.flexCubeContractNo = "";&#xD;
autoObject.BasicDetails.contractStage = "";&#xD;
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.exportPurpose.name = "";&#xD;
autoObject.BasicDetails.exportPurpose.value = "";&#xD;
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.paymentTerms.name = "";&#xD;
autoObject.BasicDetails.paymentTerms.value = "";&#xD;
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.productCategory.name = "";&#xD;
autoObject.BasicDetails.productCategory.value = "";&#xD;
autoObject.BasicDetails.commodityDescription = "";&#xD;
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.CountryOfOrigin.name = "";&#xD;
autoObject.BasicDetails.CountryOfOrigin.value = "";&#xD;
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";&#xD;
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
autoObject.BasicDetails.Invoice[0].invoiceNo = "";&#xD;
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
autoObject.GeneratedDocumentInfo.customerName = "";&#xD;
autoObject.GeneratedDocumentInfo.customerAddress = "";&#xD;
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.Instructions[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.InstructionsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";&#xD;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
autoObject.FinancialDetailsBR.documentAmount = 0.0;&#xD;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.currency.name = "";&#xD;
autoObject.FinancialDetailsBR.currency.value = "";&#xD;
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();&#xD;
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;&#xD;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";&#xD;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";&#xD;
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.collectionAccount.name = "";&#xD;
autoObject.FinancialDetailsBR.collectionAccount.value = "";&#xD;
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";&#xD;
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";&#xD;
autoObject.FcCollections = new tw.object.FCCollections();&#xD;
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.currency.name = "";&#xD;
autoObject.FcCollections.currency.value = "";&#xD;
autoObject.FcCollections.standardExchangeRate = 0.0;&#xD;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;&#xD;
autoObject.FcCollections.fromDate = new TWDate();&#xD;
autoObject.FcCollections.ToDate = new TWDate();&#xD;
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.accountNo.name = "";&#xD;
autoObject.FcCollections.accountNo.value = "";&#xD;
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";&#xD;
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";&#xD;
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();&#xD;
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();&#xD;
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;&#xD;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;&#xD;
autoObject.FcCollections.retrievedTransactions[0].currency = "";&#xD;
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();&#xD;
autoObject.FcCollections.selectedTransactions[0].accountNo = "";&#xD;
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";&#xD;
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();&#xD;
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();&#xD;
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions[0].currency = "";&#xD;
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
autoObject.FcCollections.isReversed = false;&#xD;
autoObject.FcCollections.usedAmount = 0.0;&#xD;
autoObject.FcCollections.calculatedAmount = 0.0;&#xD;
autoObject.FcCollections.totalAllocatedAmount = 0.0;&#xD;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.listOfAccounts[0].name = "";&#xD;
autoObject.FcCollections.listOfAccounts[0].value = "";&#xD;
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
autoObject.FinancialDetailsFO.discount = 0.0;&#xD;
autoObject.FinancialDetailsFO.extraCharges = 0.0;&#xD;
autoObject.FinancialDetailsFO.ourCharges = 0.0;&#xD;
autoObject.FinancialDetailsFO.amountSight = 0.0;&#xD;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;&#xD;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;&#xD;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;&#xD;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;&#xD;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();&#xD;
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;&#xD;
autoObject.FinancialDetailsFO.referenceNo = "";&#xD;
autoObject.FinancialDetailsFO.financeApprovalNo = "";&#xD;
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsFO.executionHub.name = "";&#xD;
autoObject.FinancialDetailsFO.executionHub.value = "";&#xD;
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();&#xD;
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();&#xD;
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;&#xD;
autoObject.ImporterDetails = new tw.object.ImporterDetails();&#xD;
autoObject.ImporterDetails.importerName = "";&#xD;
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ImporterDetails.importerCountry.name = "";&#xD;
autoObject.ImporterDetails.importerCountry.value = "";&#xD;
autoObject.ImporterDetails.importerAddress = "";&#xD;
autoObject.ImporterDetails.importerPhoneNo = "";&#xD;
autoObject.ImporterDetails.bank = "";&#xD;
autoObject.ImporterDetails.BICCode = "";&#xD;
autoObject.ImporterDetails.ibanAccount = "";&#xD;
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ImporterDetails.bankCountry.name = "";&#xD;
autoObject.ImporterDetails.bankCountry.value = "";&#xD;
autoObject.ImporterDetails.bankAddress = "";&#xD;
autoObject.ImporterDetails.bankPhoneNo = "";&#xD;
autoObject.ImporterDetails.collectingBankReference = "";&#xD;
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();&#xD;
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";&#xD;
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";&#xD;
autoObject.ProductShipmentDetails.shippingDate = new TWDate();&#xD;
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ProductShipmentDetails.shipmentMethod.name = "";&#xD;
autoObject.ProductShipmentDetails.shipmentMethod.value = "";&#xD;
autoObject.OdcCollection = new tw.object.ODCCollection();&#xD;
autoObject.OdcCollection.amount = 0.0;&#xD;
autoObject.OdcCollection.currency = "";&#xD;
autoObject.OdcCollection.informCADAboutTheCollection = false;&#xD;
autoObject.ReversalReason = new tw.object.ReversalReason();&#xD;
autoObject.ReversalReason.reversalReason = "";&#xD;
autoObject.ReversalReason.closureReason = "";&#xD;
autoObject.ContractCreation = new tw.object.ContractCreation();&#xD;
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ContractCreation.productCode.name = "";&#xD;
autoObject.ContractCreation.productCode.value = "";&#xD;
autoObject.ContractCreation.productDescription = "";&#xD;
autoObject.ContractCreation.Stage = "";&#xD;
autoObject.ContractCreation.userReference = "";&#xD;
autoObject.ContractCreation.sourceReference = "";&#xD;
autoObject.ContractCreation.currency = "";&#xD;
autoObject.ContractCreation.amount = 0.0;&#xD;
autoObject.ContractCreation.baseDate = new TWDate();&#xD;
autoObject.ContractCreation.valueDate = new TWDate();&#xD;
autoObject.ContractCreation.tenorDays = 0;&#xD;
autoObject.ContractCreation.transitDays = 0;&#xD;
autoObject.ContractCreation.maturityDate = new TWDate();&#xD;
autoObject.Parties = new tw.object.odcParties();&#xD;
autoObject.Parties.Drawer = new tw.object.Drawer();&#xD;
autoObject.Parties.Drawer.partyId = "";&#xD;
autoObject.Parties.Drawer.partyName = "";&#xD;
autoObject.Parties.Drawer.country = "";&#xD;
autoObject.Parties.Drawer.Language = "";&#xD;
autoObject.Parties.Drawer.Reference = "";&#xD;
autoObject.Parties.Drawer.address1 = "";&#xD;
autoObject.Parties.Drawer.address2 = "";&#xD;
autoObject.Parties.Drawer.address3 = "";&#xD;
autoObject.Parties.Drawee = new tw.object.Drawee();&#xD;
autoObject.Parties.Drawee.partyId = "";&#xD;
autoObject.Parties.Drawee.partyName = "";&#xD;
autoObject.Parties.Drawee.country = "";&#xD;
autoObject.Parties.Drawee.Language = "";&#xD;
autoObject.Parties.Drawee.Reference = "";&#xD;
autoObject.Parties.Drawee.address1 = "";&#xD;
autoObject.Parties.Drawee.address2 = "";&#xD;
autoObject.Parties.Drawee.address3 = "";&#xD;
autoObject.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
autoObject.Parties.collectingBank.id = "";&#xD;
autoObject.Parties.collectingBank.name = "";&#xD;
autoObject.Parties.collectingBank.country = "";&#xD;
autoObject.Parties.collectingBank.language = "";&#xD;
autoObject.Parties.collectingBank.reference = "";&#xD;
autoObject.Parties.collectingBank.address1 = "";&#xD;
autoObject.Parties.collectingBank.address2 = "";&#xD;
autoObject.Parties.collectingBank.address3 = "";&#xD;
autoObject.Parties.collectingBank.cif = "";&#xD;
autoObject.Parties.collectingBank.media = "";&#xD;
autoObject.Parties.collectingBank.address = "";&#xD;
autoObject.Parties.partyTypes = new tw.object.partyTypes();&#xD;
autoObject.Parties.partyTypes.partyCIF = "";&#xD;
autoObject.Parties.partyTypes.partyId = "";&#xD;
autoObject.Parties.partyTypes.partyName = "";&#xD;
autoObject.Parties.partyTypes.country = "";&#xD;
autoObject.Parties.partyTypes.language = "";&#xD;
autoObject.Parties.partyTypes.refrence = "";&#xD;
autoObject.Parties.partyTypes.address1 = "";&#xD;
autoObject.Parties.partyTypes.address2 = "";&#xD;
autoObject.Parties.partyTypes.address3 = "";&#xD;
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.Parties.partyTypes.partyType.name = "";&#xD;
autoObject.Parties.partyTypes.partyType.value = "";&#xD;
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();&#xD;
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();&#xD;
autoObject.ChargesAndCommissions[0].component = "";&#xD;
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";&#xD;
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";&#xD;
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].waiver = false;&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].rateType = "";&#xD;
autoObject.ChargesAndCommissions[0].description = "";&#xD;
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";&#xD;
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;&#xD;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();&#xD;
autoObject.ContractLiquidation.liqAmount = 0.0;&#xD;
autoObject.ContractLiquidation.liqCurrency = "";&#xD;
autoObject.ContractLiquidation.debitValueDate = new TWDate();&#xD;
autoObject.ContractLiquidation.creditValueDate = new TWDate();&#xD;
autoObject.ContractLiquidation.debitedAccountNo = "";&#xD;
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();&#xD;
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.branchCode = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ContractLiquidation.creditedAccount.currency.name = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.currency.value = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;&#xD;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;&#xD;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();&#xD;
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;&#xD;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;&#xD;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;&#xD;
autoObject.complianceApproval = false;&#xD;
autoObject.stepLog = new tw.object.StepLog();&#xD;
autoObject.stepLog.startTime = new TWDate();&#xD;
autoObject.stepLog.endTime = new TWDate();&#xD;
autoObject.stepLog.userName = "";&#xD;
autoObject.stepLog.role = "";&#xD;
autoObject.stepLog.step = "";&#xD;
autoObject.stepLog.action = "";&#xD;
autoObject.stepLog.comment = "";&#xD;
autoObject.stepLog.terminateReason = "";&#xD;
autoObject.stepLog.returnReason = "";&#xD;
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.actions[0] = "";&#xD;
autoObject.attachmentDetails = new tw.object.attachmentDetails();&#xD;
autoObject.attachmentDetails.folderID = "";&#xD;
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
autoObject.attachmentDetails.ecmProperties.fullPath = "";&#xD;
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;&#xD;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();&#xD;
autoObject.attachmentDetails.attachment[0].name = "";&#xD;
autoObject.attachmentDetails.attachment[0].description = "";&#xD;
autoObject.attachmentDetails.attachment[0].arabicName = "";&#xD;
autoObject.complianceComments = new tw.object.listOf.StepLog();&#xD;
autoObject.complianceComments[0] = new tw.object.StepLog();&#xD;
autoObject.complianceComments[0].startTime = new TWDate();&#xD;
autoObject.complianceComments[0].endTime = new TWDate();&#xD;
autoObject.complianceComments[0].userName = "";&#xD;
autoObject.complianceComments[0].role = "";&#xD;
autoObject.complianceComments[0].step = "";&#xD;
autoObject.complianceComments[0].action = "";&#xD;
autoObject.complianceComments[0].comment = "";&#xD;
autoObject.complianceComments[0].terminateReason = "";&#xD;
autoObject.complianceComments[0].returnReason = "";&#xD;
autoObject.History = new tw.object.listOf.StepLog();&#xD;
autoObject.History[0] = new tw.object.StepLog();&#xD;
autoObject.History[0].startTime = new TWDate();&#xD;
autoObject.History[0].endTime = new TWDate();&#xD;
autoObject.History[0].userName = "";&#xD;
autoObject.History[0].role = "";&#xD;
autoObject.History[0].step = "";&#xD;
autoObject.History[0].action = "";&#xD;
autoObject.History[0].comment = "";&#xD;
autoObject.History[0].terminateReason = "";&#xD;
autoObject.History[0].returnReason = "";&#xD;
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.documentSource.name = "";&#xD;
autoObject.documentSource.value = "";&#xD;
autoObject.folderID = "";&#xD;
autoObject.isLiquidated = false;&#xD;
autoObject&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataInput&gt;&lt;ns15:dataInput name="routingDetails" itemSubjectRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727" isCollection="false" id="2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:defaultValue useDefault="false"&gt;var autoObject = new tw.object.odcRoutingDetails();&#xD;
autoObject.hubCode = "";&#xD;
autoObject.branchCode = "";&#xD;
autoObject.initiatorUser = "";&#xD;
autoObject.branchName = "";&#xD;
autoObject.hubName = "";&#xD;
autoObject.branchSeq = "";&#xD;
autoObject&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataInput&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="_7a4d5a0f-1097-4489-bf6f-ea6a7a28d4a4" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="_3d42305c-af64-476f-add2-628c418da958" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="fc92c62a-eb7e-4a04-b3bb-104f7fc11c37"&gt;&lt;ns15:lane name="Hub Maker" partitionElementRef="24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce" id="2f34dc88-0eda-48b1-82cb-03faf20f7fbd" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="1011" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;2300d98d-7703-498f-a179-da81156caffd&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;6c34a494-03cd-45bc-aa32-0c51125d02b3&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;aec4fe46-5179-4bf0-830d-b2197bde09b3&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;65373fd5-a6e7-471c-89f3-9914a767f490&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;578cd29e-eb65-4611-887c-ca89e350bbe0&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Hub Checker" partitionElementRef="24.8e005024-3fe0-4848-8c3c-f1e9483900c6" id="cbfe586a-2d9a-4152-b545-72306e0bfe17" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="1011" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;3bbe7551-846d-4a0d-88b3-cbeee15bc65b&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;4548b365-e059-4b60-88e6-963bd9bdb102&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;e3ff4aa9-775b-4509-9e93-4b2a929de6ad&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;6b353da2-4799-4357-8635-1d550dcb5798&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.da7e4d23-78cb-4483-98ed-b9c238308a03" id="5cc9debc-a01c-46df-82f3-935c3a9ca390" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="1011" height="262" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;b8ccb671-17d0-4597-8b78-b1c858672409&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;e3080c20-6d98-42f1-85ae-ccf62f692b6e&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;426ad1b2-fd3f-4430-81c1-9d1d0664c795&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;d6e38de3-cffd-4f49-8793-11fd93a0c977&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;31deeeb5-4f4b-450e-892b-519f89b6ba64&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="2300d98d-7703-498f-a179-da81156caffd"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="35" y="100" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:default&gt;40db59ea-ddf4-4f91-ad82-c5dc7ea27829&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;40db59ea-ddf4-4f91-ad82-c5dc7ea27829&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:endEvent name="End Authorize" id="e3ff4aa9-775b-4509-9e93-4b2a929de6ad"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="862" y="45" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:callActivity calledElement="1.6255f65f-d7ce-452d-9058-3f856ea792e0" default="c3a3c0bd-8021-4f3b-8182-ffc4b24b2527" name="Create ODC Collection Request " id="6c34a494-03cd-45bc-aa32-0c51125d02b3"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false" transactionalBehavior="NotSet"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="236" y="77" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:narrative&gt;&amp;lt;#= tw.epv.Col_ScreenNames.ODCCol01 #&amp;gt;  &amp;lt;#= tw.system.process.instanceId #&amp;gt;&lt;/ns4:narrative&gt;&lt;ns4:subject&gt;Create ODC Collection Request – طلب تسجيل حصائل على مستند تحصيل تصدير&amp;lt;#= tw.system.process.instanceId #&amp;gt;&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.Col_SLA.SLA_Act01)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;e65b9e5f-0fff-4bfc-8899-7b8266e14e83&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;40db59ea-ddf4-4f91-ad82-c5dc7ea27829&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;c3a3c0bd-8021-4f3b-8182-ffc4b24b2527&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.fb57a244-f263-444b-8dc2-734946370a33&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"&gt;tw.local.routingDetails&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.115324d0-8168-495d-8187-b6a3c2434105&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.0b50a9fc-1f55-4a3b-84b9-18de25fe1bac&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"&gt;tw.local.customerAccounts&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.f0a5931f-96f0-4329-81d0-83ef90e6192f&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"&gt;tw.local.customerAccounts&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="LastUser" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a76b6fae-3a5c-469c-85df-3d71dc308f87&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.loggedInUser&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="2300d98d-7703-498f-a179-da81156caffd" targetRef="6c34a494-03cd-45bc-aa32-0c51125d02b3" name="To Create ODC Collection Request " id="40db59ea-ddf4-4f91-ad82-c5dc7ea27829"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="65373fd5-a6e7-471c-89f3-9914a767f490" targetRef="3bbe7551-846d-4a0d-88b3-cbeee15bc65b" name="No" id="********-401c-4a45-9acf-2dc238bc3950"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" /&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.429675b0-3048-4910-8b11-e3d3eb0cd480" default="afb60f23-0dec-4ee4-8ef0-07590f68ca18" name="Review ODC Collection Request" id="3bbe7551-846d-4a0d-88b3-cbeee15bc65b"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false" transactionalBehavior="NotSet"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="598" y="48" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:narrative&gt;&amp;lt;#= tw.epv.Col_ScreenNames.ODCCol02 #&amp;gt;  &amp;lt;#= tw.system.process.instanceId #&amp;gt;&lt;/ns4:narrative&gt;&lt;ns4:subject&gt;Review ODC Collection Request&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.Col_SLA.SLA_Act02)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;********-401c-4a45-9acf-2dc238bc3950&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;2d535ad4-ad69-4293-850f-e7ea81aeccb3&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;b6c3d9ed-93d3-4698-8888-547b82966e13&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;afb60f23-0dec-4ee4-8ef0-07590f68ca18&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"&gt;tw.local.customerAccounts&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.568ffb88-c172-4fff-841d-2e47908795cb&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_CHKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:exclusiveGateway default="5a8d21ed-0bb8-4366-824e-f6e661620491" name="Check Action" id="8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="703" y="84" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;afb60f23-0dec-4ee4-8ef0-07590f68ca18&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;5a8d21ed-0bb8-4366-824e-f6e661620491&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;e65b9e5f-0fff-4bfc-8899-7b8266e14e83&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="3bbe7551-846d-4a0d-88b3-cbeee15bc65b" targetRef="8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f" name="To Check Action" id="afb60f23-0dec-4ee4-8ef0-07590f68ca18"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightBottom&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="End Cancel" id="4548b365-e059-4b60-88e6-963bd9bdb102"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="871" y="101" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;5a8d21ed-0bb8-4366-824e-f6e661620491&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f" targetRef="4548b365-e059-4b60-88e6-963bd9bdb102" name="Cancel" id="5a8d21ed-0bb8-4366-824e-f6e661620491"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f" targetRef="e3ff4aa9-775b-4509-9e93-4b2a929de6ad" name="Authorize" id="55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.authorize&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f" targetRef="6c34a494-03cd-45bc-aa32-0c51125d02b3" name="Return to initiator" id="e65b9e5f-0fff-4bfc-8899-7b8266e14e83"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="taskId" id="389ebfab-ad7c-45f7-80b1-7bcacb85f82c" /&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="6c34a494-03cd-45bc-aa32-0c51125d02b3" parallelMultiple="false" name="Timer/Create ODC Collection Request " id="aec4fe46-5179-4bf0-830d-b2197bde09b3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="297" y="135" width="24" height="24" /&gt;&lt;ns3:default&gt;411f55c7-efea-401d-83fa-637a1a614dcc&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskId&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;411f55c7-efea-401d-83fa-637a1a614dcc&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="797540ef-74fd-40a7-830b-b21dd47dc747" eventImplId="0c1429b6-50f2-4f11-8a12-4bad07e715ba"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Hours&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Hours&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:callActivity calledElement="1.d7acf968-6740-4e52-b037-2049466eeeb2" default="0ccb44e3-2a5f-45c4-885e-74c915e4b2a1" name="Send Escalation Mail" id="b8ccb671-17d0-4597-8b78-b1c858672409"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns13:nodeVisualInfo x="501" y="112" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;411f55c7-efea-401d-83fa-637a1a614dcc&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;a45be2a3-5a13-4ad5-8503-1b96f538d134&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;0ccb44e3-2a5f-45c4-885e-74c915e4b2a1&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.9771d7e8-ca59-430e-8b1a-194cf04c1182&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.taskId&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="aec4fe46-5179-4bf0-830d-b2197bde09b3" targetRef="b8ccb671-17d0-4597-8b78-b1c858672409" name="To Send Escalation Mail" id="411f55c7-efea-401d-83fa-637a1a614dcc"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="3bbe7551-846d-4a0d-88b3-cbeee15bc65b" parallelMultiple="false" name="Timer/Review ODC Collection Request" id="6b353da2-4799-4357-8635-1d550dcb5798"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="659" y="106" width="24" height="24" /&gt;&lt;ns3:default&gt;a45be2a3-5a13-4ad5-8503-1b96f538d134&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskId&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;a45be2a3-5a13-4ad5-8503-1b96f538d134&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="97cbc8f0-bf40-4c43-808f-09f05b5d6869" eventImplId="730b13f1-e3ca-4b97-8d5e-c15b872f4ab3"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Hours&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Hours&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="6b353da2-4799-4357-8635-1d550dcb5798" targetRef="b8ccb671-17d0-4597-8b78-b1c858672409" name="To System Task1" id="a45be2a3-5a13-4ad5-8503-1b96f538d134"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="End Event1" id="e3080c20-6d98-42f1-85ae-ccf62f692b6e"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="718" y="202" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;528cd2ca-6545-4614-8638-a383622bd1e2&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;0ccb44e3-2a5f-45c4-885e-74c915e4b2a1&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="662a0040-1dd6-4b44-8213-26b31d166e45" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="customerAccounts" id="47aab0e9-13a2-4912-8526-d7424de98556" /&gt;&lt;ns15:callActivity calledElement="1.a6205b87-57cc-47bb-abfc-edff0743b08e" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2d535ad4-ad69-4293-850f-e7ea81aeccb3" name="Send CAD Team Mail" id="426ad1b2-fd3f-4430-81c1-9d1d0664c795"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns13:nodeVisualInfo x="499" y="22" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.epv.Mails.CadTeam&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;407b35e3-4142-4a62-86b0-c68ffa25d1fb&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;2d535ad4-ad69-4293-850f-e7ea81aeccb3&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.8510e459-4375-44f2-afb0-c0b98b03ee89&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b4c53a07-42dc-4fe2-995e-23d8719e647e&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.taskId&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:exclusiveGateway default="********-401c-4a45-9acf-2dc238bc3950" name="Inform CAD Team?" id="65373fd5-a6e7-471c-89f3-9914a767f490"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="431" y="114" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;c3a3c0bd-8021-4f3b-8182-ffc4b24b2527&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;********-401c-4a45-9acf-2dc238bc3950&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;407b35e3-4142-4a62-86b0-c68ffa25d1fb&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="6c34a494-03cd-45bc-aa32-0c51125d02b3" targetRef="65373fd5-a6e7-471c-89f3-9914a767f490" name="To Inform CAD Team?" id="c3a3c0bd-8021-4f3b-8182-ffc4b24b2527"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightBottom&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="426ad1b2-fd3f-4430-81c1-9d1d0664c795" targetRef="3bbe7551-846d-4a0d-88b3-cbeee15bc65b" name="To Review ODC Collection Request" id="2d535ad4-ad69-4293-850f-e7ea81aeccb3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="65373fd5-a6e7-471c-89f3-9914a767f490" targetRef="426ad1b2-fd3f-4430-81c1-9d1d0664c795" name="Yes" id="407b35e3-4142-4a62-86b0-c68ffa25d1fb"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.OdcCollection.informCADAboutTheCollection&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="true" attachedToRef="426ad1b2-fd3f-4430-81c1-9d1d0664c795" parallelMultiple="false" name="Error" id="d6e38de3-cffd-4f49-8793-11fd93a0c977"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="534" y="10" width="24" height="24" /&gt;&lt;ns3:default&gt;b6c3d9ed-93d3-4698-8888-547b82966e13&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;b6c3d9ed-93d3-4698-8888-547b82966e13&lt;/ns15:outgoing&gt;&lt;ns15:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5852ca58-901b-44b7-84c8-3319037215af" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:errorEventDefinition id="a2c6e895-55bc-4f40-877c-b4a06daf693e" eventImplId="06abad98-ee10-430a-8af6-46e4bdda4792"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:errorEventSettings&gt;&lt;ns4:catchAll&gt;true&lt;/ns4:catchAll&gt;&lt;/ns4:errorEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:errorEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="d6e38de3-cffd-4f49-8793-11fd93a0c977" targetRef="3bbe7551-846d-4a0d-88b3-cbeee15bc65b" name="To Review ODC Collection Request" id="b6c3d9ed-93d3-4698-8888-547b82966e13"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="true" attachedToRef="b8ccb671-17d0-4597-8b78-b1c858672409" parallelMultiple="false" name="Error1" id="31deeeb5-4f4b-450e-892b-519f89b6ba64"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="510" y="170" width="24" height="24" /&gt;&lt;ns3:default&gt;528cd2ca-6545-4614-8638-a383622bd1e2&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;528cd2ca-6545-4614-8638-a383622bd1e2&lt;/ns15:outgoing&gt;&lt;ns15:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ff3cb5ff-fef1-4f10-8f95-37fc667590ce" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:errorEventDefinition id="42aecbb1-bc82-42f9-876d-17d2f2e842b2" eventImplId="31536ba9-7809-4977-82eb-35b7f98a9826"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:errorEventSettings&gt;&lt;ns4:catchAll&gt;true&lt;/ns4:catchAll&gt;&lt;/ns4:errorEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:errorEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="31deeeb5-4f4b-450e-892b-519f89b6ba64" targetRef="e3080c20-6d98-42f1-85ae-ccf62f692b6e" name="To End Event1" id="528cd2ca-6545-4614-8638-a383622bd1e2"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="b8ccb671-17d0-4597-8b78-b1c858672409" targetRef="e3080c20-6d98-42f1-85ae-ccf62f692b6e" name="To End Event1" id="0ccb44e3-2a5f-45c4-885e-74c915e4b2a1"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentPath" id="c8bac588-62b1-43ea-8239-3bb0bb495357" /&gt;&lt;ns15:callActivity calledElement="1.46cfd991-7ee5-4ce2-887a-8b21634e7e95" name="User Task" id="578cd29e-eb65-4611-887c-ca89e350bbe0"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:activityAdHocSettings repeatable="false" hidden="false" triggerType="Automatic" option="Required" /&gt;&lt;ns13:nodeVisualInfo x="794" y="77" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns3:activityPreconditions triggerType="NoPreconditions" documentTriggerMode="External" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.48d9e8e1-2b51-4173-ac71-9a6c533d134e&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="ODC CollectionInterface" id="_c6b2b3b4-6f3c-41d6-8c41-5ee7b96b4b86" /&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["40db59ea-ddf4-4f91-ad82-c5dc7ea27829"],"isInterrupting":true,"extensionElements":{"default":["40db59ea-ddf4-4f91-ad82-c5dc7ea27829"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":35,"y":100,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"2300d98d-7703-498f-a179-da81156caffd"},{"incoming":["55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":862,"y":45,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Authorize","declaredType":"endEvent","id":"e3ff4aa9-775b-4509-9e93-4b2a929de6ad"},{"outgoing":["c3a3c0bd-8021-4f3b-8182-ffc4b24b2527"],"incoming":["e65b9e5f-0fff-4bfc-8899-7b8266e14e83","40db59ea-ddf4-4f91-ad82-c5dc7ea27829"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":236,"y":77,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Create ODC Collection Request \u2013 \u0637\u0644\u0628 \u062a\u0633\u062c\u064a\u0644 \u062d\u0635\u0627\u0626\u0644 \u0639\u0644\u0649 \u0645\u0633\u062a\u0646\u062f \u062a\u062d\u0635\u064a\u0644 \u062a\u0635\u062f\u064a\u0631&lt;#= tw.system.process.instanceId #&gt;","narrative":"&lt;#= tw.epv.Col_ScreenNames.ODCCol01 #&gt;  &lt;#= tw.system.process.instanceId #&gt;","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.Col_SLA.SLA_Act01)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"LastUser","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.a76b6fae-3a5c-469c-85df-3d71dc308f87","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.loggedInUser"]}}]},{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_MKR\""]}}]}],"serviceRef":"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527","name":"Create ODC Collection Request ","dataInputAssociation":[{"targetRef":"2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.fb57a244-f263-444b-8dc2-734946370a33","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","declaredType":"TFormalExpression","content":["tw.local.routingDetails"]}}]},{"targetRef":"2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"6c34a494-03cd-45bc-aa32-0c51125d02b3","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.115324d0-8168-495d-8187-b6a3c2434105"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","declaredType":"TFormalExpression","content":["tw.local.customerAccounts"]}}],"sourceRef":["2055.0b50a9fc-1f55-4a3b-84b9-18de25fe1bac"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","declaredType":"TFormalExpression","content":["tw.local.customerAccounts"]}}],"sourceRef":["2055.f0a5931f-96f0-4329-81d0-83ef90e6192f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4"]}],"calledElement":"1.6255f65f-d7ce-452d-9058-3f856ea792e0"},{"targetRef":"6c34a494-03cd-45bc-aa32-0c51125d02b3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Create ODC Collection Request ","declaredType":"sequenceFlow","id":"40db59ea-ddf4-4f91-ad82-c5dc7ea27829","sourceRef":"2300d98d-7703-498f-a179-da81156caffd"},{"targetRef":"3bbe7551-846d-4a0d-88b3-cbeee15bc65b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"No","declaredType":"sequenceFlow","id":"********-401c-4a45-9acf-2dc238bc3950","sourceRef":"65373fd5-a6e7-471c-89f3-9914a767f490"},{"outgoing":["afb60f23-0dec-4ee4-8ef0-07590f68ca18"],"incoming":["********-401c-4a45-9acf-2dc238bc3950","2d535ad4-ad69-4293-850f-e7ea81aeccb3","b6c3d9ed-93d3-4698-8888-547b82966e13"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":598,"y":48,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review ODC Collection Request","narrative":"&lt;#= tw.epv.Col_ScreenNames.ODCCol02 #&gt;  &lt;#= tw.system.process.instanceId #&gt;","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.Col_SLA.SLA_Act02)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_CHKR\""]}}]}],"serviceRef":"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"afb60f23-0dec-4ee4-8ef0-07590f68ca18","name":"Review ODC Collection Request","dataInputAssociation":[{"targetRef":"2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","declaredType":"TFormalExpression","content":["tw.local.customerAccounts"]}}]},{"targetRef":"2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"3bbe7551-846d-4a0d-88b3-cbeee15bc65b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.568ffb88-c172-4fff-841d-2e47908795cb"]}],"calledElement":"1.429675b0-3048-4910-8b11-e3d3eb0cd480"},{"outgoing":["5a8d21ed-0bb8-4366-824e-f6e661620491","55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c","e65b9e5f-0fff-4bfc-8899-7b8266e14e83"],"incoming":["afb60f23-0dec-4ee4-8ef0-07590f68ca18"],"default":"5a8d21ed-0bb8-4366-824e-f6e661620491","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":703,"y":84,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Check Action","declaredType":"exclusiveGateway","id":"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f"},{"targetRef":"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Check Action","declaredType":"sequenceFlow","id":"afb60f23-0dec-4ee4-8ef0-07590f68ca18","sourceRef":"3bbe7551-846d-4a0d-88b3-cbeee15bc65b"},{"incoming":["5a8d21ed-0bb8-4366-824e-f6e661620491"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":871,"y":101,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Cancel","declaredType":"endEvent","id":"4548b365-e059-4b60-88e6-963bd9bdb102"},{"targetRef":"4548b365-e059-4b60-88e6-963bd9bdb102","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"Cancel","declaredType":"sequenceFlow","id":"5a8d21ed-0bb8-4366-824e-f6e661620491","sourceRef":"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f"},{"targetRef":"e3ff4aa9-775b-4509-9e93-4b2a929de6ad","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.authorize"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"Authorize","declaredType":"sequenceFlow","id":"55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c","sourceRef":"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f"},{"targetRef":"6c34a494-03cd-45bc-aa32-0c51125d02b3","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToMaker"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Return to initiator","declaredType":"sequenceFlow","id":"e65b9e5f-0fff-4bfc-8899-7b8266e14e83","sourceRef":"8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskId","isCollection":false,"declaredType":"dataObject","id":"389ebfab-ad7c-45f7-80b1-7bcacb85f82c"},{"parallelMultiple":false,"outgoing":["411f55c7-efea-401d-83fa-637a1a614dcc"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Hours","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Hours","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"797540ef-74fd-40a7-830b-b21dd47dc747","otherAttributes":{"eventImplId":"0c1429b6-50f2-4f11-8a12-4bad07e715ba"}}],"attachedToRef":"6c34a494-03cd-45bc-aa32-0c51125d02b3","extensionElements":{"default":["411f55c7-efea-401d-83fa-637a1a614dcc"],"nodeVisualInfo":[{"width":24,"x":297,"y":135,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskId"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Timer\/Create ODC Collection Request ","declaredType":"boundaryEvent","id":"aec4fe46-5179-4bf0-830d-b2197bde09b3"},{"outgoing":["0ccb44e3-2a5f-45c4-885e-74c915e4b2a1"],"incoming":["411f55c7-efea-401d-83fa-637a1a614dcc","a45be2a3-5a13-4ad5-8503-1b96f538d134"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"deleteTaskOnCompletion":[true],"nodeVisualInfo":[{"width":95,"x":501,"y":112,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1","name":"Send Escalation Mail","dataInputAssociation":[{"targetRef":"2055.9771d7e8-ca59-430e-8b1a-194cf04c1182","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]},{"targetRef":"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.taskId"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"b8ccb671-17d0-4597-8b78-b1c858672409","calledElement":"1.d7acf968-6740-4e52-b037-2049466eeeb2"},{"targetRef":"b8ccb671-17d0-4597-8b78-b1c858672409","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation Mail","declaredType":"sequenceFlow","id":"411f55c7-efea-401d-83fa-637a1a614dcc","sourceRef":"aec4fe46-5179-4bf0-830d-b2197bde09b3"},{"parallelMultiple":false,"outgoing":["a45be2a3-5a13-4ad5-8503-1b96f538d134"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Hours","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Hours","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"97cbc8f0-bf40-4c43-808f-09f05b5d6869","otherAttributes":{"eventImplId":"730b13f1-e3ca-4b97-8d5e-c15b872f4ab3"}}],"attachedToRef":"3bbe7551-846d-4a0d-88b3-cbeee15bc65b","extensionElements":{"default":["a45be2a3-5a13-4ad5-8503-1b96f538d134"],"nodeVisualInfo":[{"width":24,"x":659,"y":106,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskId"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Timer\/Review ODC Collection Request","declaredType":"boundaryEvent","id":"6b353da2-4799-4357-8635-1d550dcb5798"},{"targetRef":"b8ccb671-17d0-4597-8b78-b1c858672409","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To System Task1","declaredType":"sequenceFlow","id":"a45be2a3-5a13-4ad5-8503-1b96f538d134","sourceRef":"6b353da2-4799-4357-8635-1d550dcb5798"},{"incoming":["528cd2ca-6545-4614-8638-a383622bd1e2","0ccb44e3-2a5f-45c4-885e-74c915e4b2a1"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":718,"y":202,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event1","declaredType":"endEvent","id":"e3080c20-6d98-42f1-85ae-ccf62f692b6e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"declaredType":"dataObject","id":"662a0040-1dd6-4b44-8213-26b31d166e45"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"customerAccounts","isCollection":true,"declaredType":"dataObject","id":"47aab0e9-13a2-4912-8526-d7424de98556"},{"outgoing":["2d535ad4-ad69-4293-850f-e7ea81aeccb3"],"incoming":["407b35e3-4142-4a62-86b0-c68ffa25d1fb"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"deleteTaskOnCompletion":[true],"nodeVisualInfo":[{"width":95,"x":499,"y":22,"declaredType":"TNodeVisualInfo","height":70}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.epv.Mails.CadTeam"]}}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"2d535ad4-ad69-4293-850f-e7ea81aeccb3","name":"Send CAD Team Mail","dataInputAssociation":[{"targetRef":"2055.8510e459-4375-44f2-afb0-c0b98b03ee89","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.b4c53a07-42dc-4fe2-995e-23d8719e647e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.taskId"]}}]},{"targetRef":"2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"426ad1b2-fd3f-4430-81c1-9d1d0664c795","calledElement":"1.a6205b87-57cc-47bb-abfc-edff0743b08e"},{"outgoing":["********-401c-4a45-9acf-2dc238bc3950","407b35e3-4142-4a62-86b0-c68ffa25d1fb"],"incoming":["c3a3c0bd-8021-4f3b-8182-ffc4b24b2527"],"default":"********-401c-4a45-9acf-2dc238bc3950","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":431,"y":114,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Inform CAD Team?","declaredType":"exclusiveGateway","id":"65373fd5-a6e7-471c-89f3-9914a767f490"},{"targetRef":"65373fd5-a6e7-471c-89f3-9914a767f490","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Inform CAD Team?","declaredType":"sequenceFlow","id":"c3a3c0bd-8021-4f3b-8182-ffc4b24b2527","sourceRef":"6c34a494-03cd-45bc-aa32-0c51125d02b3"},{"targetRef":"3bbe7551-846d-4a0d-88b3-cbeee15bc65b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Review ODC Collection Request","declaredType":"sequenceFlow","id":"2d535ad4-ad69-4293-850f-e7ea81aeccb3","sourceRef":"426ad1b2-fd3f-4430-81c1-9d1d0664c795"},{"targetRef":"426ad1b2-fd3f-4430-81c1-9d1d0664c795","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.OdcCollection.informCADAboutTheCollection"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Yes","declaredType":"sequenceFlow","id":"407b35e3-4142-4a62-86b0-c68ffa25d1fb","sourceRef":"65373fd5-a6e7-471c-89f3-9914a767f490"},{"parallelMultiple":false,"outgoing":["b6c3d9ed-93d3-4698-8888-547b82966e13"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5852ca58-901b-44b7-84c8-3319037215af"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a2c6e895-55bc-4f40-877c-b4a06daf693e","otherAttributes":{"eventImplId":"06abad98-ee10-430a-8af6-46e4bdda4792"}}],"attachedToRef":"426ad1b2-fd3f-4430-81c1-9d1d0664c795","extensionElements":{"default":["b6c3d9ed-93d3-4698-8888-547b82966e13"],"nodeVisualInfo":[{"width":24,"x":534,"y":10,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"d6e38de3-cffd-4f49-8793-11fd93a0c977","outputSet":{}},{"targetRef":"3bbe7551-846d-4a0d-88b3-cbeee15bc65b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Review ODC Collection Request","declaredType":"sequenceFlow","id":"b6c3d9ed-93d3-4698-8888-547b82966e13","sourceRef":"d6e38de3-cffd-4f49-8793-11fd93a0c977"},{"parallelMultiple":false,"outgoing":["528cd2ca-6545-4614-8638-a383622bd1e2"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ff3cb5ff-fef1-4f10-8f95-37fc667590ce"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"42aecbb1-bc82-42f9-876d-17d2f2e842b2","otherAttributes":{"eventImplId":"31536ba9-7809-4977-82eb-35b7f98a9826"}}],"attachedToRef":"b8ccb671-17d0-4597-8b78-b1c858672409","extensionElements":{"default":["528cd2ca-6545-4614-8638-a383622bd1e2"],"nodeVisualInfo":[{"width":24,"x":510,"y":170,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"31deeeb5-4f4b-450e-892b-519f89b6ba64","outputSet":{}},{"targetRef":"e3080c20-6d98-42f1-85ae-ccf62f692b6e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End Event1","declaredType":"sequenceFlow","id":"528cd2ca-6545-4614-8638-a383622bd1e2","sourceRef":"31deeeb5-4f4b-450e-892b-519f89b6ba64"},{"targetRef":"e3080c20-6d98-42f1-85ae-ccf62f692b6e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End Event1","declaredType":"sequenceFlow","id":"0ccb44e3-2a5f-45c4-885e-74c915e4b2a1","sourceRef":"b8ccb671-17d0-4597-8b78-b1c858672409"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"declaredType":"dataObject","id":"c8bac588-62b1-43ea-8239-3bb0bb495357"},{"startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"extensionElements":{"activityAdHocSettings":[{"hidden":false,"repeatable":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityAdHocSettings","triggerType":"Automatic","option":"Required"}],"nodeVisualInfo":[{"width":95,"x":794,"y":77,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["UserTask"],"activityPreconditions":[{"documentTriggerMode":"External","sourceFolderReferenceType":"FolderId","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityPreconditions","triggerType":"NoPreconditions","matchAll":true}]},"name":"User Task","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"578cd29e-eb65-4611-887c-ca89e350bbe0","calledElement":"1.46cfd991-7ee5-4ce2-887a-8b21634e7e95"}],"laneSet":[{"id":"fc92c62a-eb7e-4a04-b3bb-104f7fc11c37","lane":[{"flowNodeRef":["2300d98d-7703-498f-a179-da81156caffd","6c34a494-03cd-45bc-aa32-0c51125d02b3","aec4fe46-5179-4bf0-830d-b2197bde09b3","65373fd5-a6e7-471c-89f3-9914a767f490","578cd29e-eb65-4611-887c-ca89e350bbe0"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":1011,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Hub Maker","partitionElementRef":"24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce","declaredType":"lane","id":"2f34dc88-0eda-48b1-82cb-03faf20f7fbd","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["3bbe7551-846d-4a0d-88b3-cbeee15bc65b","8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f","4548b365-e059-4b60-88e6-963bd9bdb102","e3ff4aa9-775b-4509-9e93-4b2a929de6ad","6b353da2-4799-4357-8635-1d550dcb5798"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":1011,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Hub Checker","partitionElementRef":"24.8e005024-3fe0-4848-8c3c-f1e9483900c6","declaredType":"lane","id":"cbfe586a-2d9a-4152-b545-72306e0bfe17","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["b8ccb671-17d0-4597-8b78-b1c858672409","e3080c20-6d98-42f1-85ae-ccf62f692b6e","426ad1b2-fd3f-4430-81c1-9d1d0664c795","d6e38de3-cffd-4f49-8793-11fd93a0c977","31deeeb5-4f4b-450e-892b-519f89b6ba64"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":1011,"x":0,"y":402,"declaredType":"TNodeVisualInfo","height":262}]},"name":"System","partitionElementRef":"24.da7e4d23-78cb-4483-98ed-b9c238308a03","declaredType":"lane","id":"5cc9debc-a01c-46df-82f3-935c3a9ca390","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}}]}],"resourceRole":[{"name":"participantRef","declaredType":"resourceRole"},{"name":"businessDataParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6","name":"perfMetricParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.48d9e8e1-2b51-4173-ac71-9a6c533d134e","name":"ownerTeamParticipantRef","declaredType":"resourceRole"}],"isClosed":false,"extensionElements":{"bpdExtension":[{"allowContentOperations":false,"enableTracking":true,"workSchedule":{"timezoneType":0,"timeScheduleType":0,"timezone":"Africa\/Cairo","timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"instanceName":"\" Request Type: \"+ tw.local.odcRequest.requestType.value +\" CIF : \"+ tw.local.odcRequest.cif +\" Request Number: \"+ tw.system.process.instanceId","dueDateSettings":{"dueDate":{"unit":"Hours","value":"8","timeOfDay":"00:00"},"type":"TimeCalculation"},"autoTrackingName":"at1691923319746","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension","optimizeExecForLatency":false,"dueDateEnabled":false,"atRiskCalcEnabled":false,"allowProjectedPathManagement":false,"autoTrackingEnabled":false,"sboSyncEnabled":true}],"caseExtension":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension","caseFolder":{"allowSubfoldersCreation":false,"allowLocalDoc":false,"id":"9ead2f75-765e-4737-9f97-38a0bf8292d2","allowExternalFolder":false,"allowExternalDoc":false}}],"isConvergedProcess":[true]},"documentation":[{"content":["-&lt;span style=\"white-space:pre\"&gt;\t&lt;\/span&gt;This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube."],"textFormat":"text\/plain"}],"name":"ODC Collection \u062a\u0633\u062c\u064a\u0644 \u062d\u0635\u0627\u0626\u0644 \u0639\u0644\u0649 \u0645\u0633\u062a\u0646\u062f \u062a\u062d\u0635\u064a\u0644 \u062a\u0635\u062f\u064a\u0631","declaredType":"process","id":"25.0b754ec1-9413-443e-9edc-f7cc6429b0d6","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"longRunning"},"ioSpecification":{"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd","epvProcessLinkId":"cf5f9c3e-3504-4e3d-85f5-0c9fe51046cb","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"09d9adeb-4ea2-421d-809b-290fce12ec80","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.00a114f4-2b01-4c48-aad9-bd62580da24b","epvProcessLinkId":"0c9a0944-2ae5-405d-85d1-2f6711b8e55d","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c","epvProcessLinkId":"3f2fb007-9b49-4d8e-8271-feacc32ead54","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{},{"id":"_7a4d5a0f-1097-4489-bf6f-ea6a7a28d4a4"}],"outputSet":[{},{"id":"_3d42305c-af64-476f-add2-628c418da958"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject"}],"searchableField":[{"path":".CustomerInfo.cif","alias":"CustomerCIF","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"5d556fee-ab55-426f-8fae-c633625c524d","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".CustomerInfo.customerName","alias":"CustomerName","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"8d013837-0e10-4860-8a09-3c720a4eff7c","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".parentRequestNo","alias":"ParentRequestNumber","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"0df39e40-549e-4dfe-8620-b81e23db8442","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".initiator","alias":"Initiation","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"156d15c5-3e68-43b1-813f-d4342fd62a41","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.requestDate","alias":"RequestDate","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"d1326d94-db9a-43ca-80f9-ed14f69e5b5b","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.status","alias":"RequestStatus","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"2bf36b4f-823f-4d6c-81bb-c850cb059591","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.initiator","alias":"initiatorUsername","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"f5e084bc-df19-47f8-8e4e-a849582d1912","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2007.c2d7852f-6525-4794-8231-2b0bfd65a374"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.odcRoutingDetails();\nautoObject.hubCode = \"\";\nautoObject.branchCode = \"\";\nautoObject.initiatorUser = \"\";\nautoObject.branchName = \"\";\nautoObject.hubName = \"\";\nautoObject.branchSeq = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","name":"routingDetails","isCollection":false,"id":"2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d"}]}},{"name":"ODC CollectionInterface","declaredType":"interface","id":"_c6b2b3b4-6f3c-41d6-8c41-5ee7b96b4b86"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"e72e3272-05a7-40e4-90d3-3f1b77bcd85c"}</jsonData>
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:374c</guid>
        <versionId>7af263f8-ec28-4d86-9715-bda0c6db59f3</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-195">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>ODC Collection تسجيل حصائل على مستند تحصيل تصدير</name>
            <documentation>&lt;html&gt;&lt;body&gt;-&lt;span style="white-space:pre"&gt;	&lt;/span&gt;This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.&lt;/body&gt;&lt;/html&gt;</documentation>
            <name>ODC Collection تسجيل حصائل على مستند تحصيل تصدير</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>mohamed.reda</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>false</isDueDateEnabled>
            <isAtRiskCalcEnabled>false</isAtRiskCalcEnabled>
            <creationDate>1691923319751</creationDate>
            <modificationDate>*************</modificationDate>
            <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</perfMetricParticipantRef>
            <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ownerTeamParticipantRef>
            <metricSettings itemType="2" />
            <instanceNameExpression>" Request Type: "+ tw.local.odcRequest.requestType.value +" CIF : "+ tw.local.odcRequest.cif +" Request Number: "+ tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>2</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <dueDateCustom></dueDateCustom>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://NBEdevBAW:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <timeScheduleName>NBEWork</timeScheduleName>
            <holidayScheduleName>NBEHoliday</holidayScheduleName>
            <holidayScheduleType>0</holidayScheduleType>
            <timezone>Africa/Cairo</timezone>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="fc92c62a-eb7e-4a04-b3bb-104f7fc11c37" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4619" />
            <ownerTeamInstanceUI id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-461a" />
            <simulationScenario id="bpdid:6ad7eb4224455a46:11f23e39:189eae0bd83:375f">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1691923320096</startTime>
            </simulationScenario>
            <flow id="a45be2a3-5a13-4ad5-8503-1b96f538d134" connectionType="SequenceFlow">
                <name>To System Task1</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bb" />
                </connection>
            </flow>
            <flow id="0ccb44e3-2a5f-45c4-885e-74c915e4b2a1" connectionType="SequenceFlow">
                <name>To End Event1</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ba" />
                </connection>
            </flow>
            <flow id="e65b9e5f-0fff-4bfc-8899-7b8266e14e83" connectionType="SequenceFlow">
                <name>Return to initiator</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cd">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c" connectionType="SequenceFlow">
                <name>Authorize</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cf">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.authorize</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="411f55c7-efea-401d-83fa-637a1a614dcc" connectionType="SequenceFlow">
                <name>To Send Escalation Mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b9" />
                </connection>
            </flow>
            <flow id="5a8d21ed-0bb8-4366-824e-f6e661620491" connectionType="SequenceFlow">
                <name>Cancel</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b8" />
                </connection>
            </flow>
            <flow id="afb60f23-0dec-4ee4-8ef0-07590f68ca18" connectionType="SequenceFlow">
                <name>To Check Action</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b7" />
                </connection>
            </flow>
            <flow id="b6c3d9ed-93d3-4698-8888-547b82966e13" connectionType="SequenceFlow">
                <name>To Review ODC Collection Request</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b6" />
                </connection>
            </flow>
            <flow id="********-401c-4a45-9acf-2dc238bc3950" connectionType="SequenceFlow">
                <name>No</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b5" />
                </connection>
            </flow>
            <flow id="c3a3c0bd-8021-4f3b-8182-ffc4b24b2527" connectionType="SequenceFlow">
                <name>To Inform CAD Team?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b4" />
                </connection>
            </flow>
            <flow id="2d535ad4-ad69-4293-850f-e7ea81aeccb3" connectionType="SequenceFlow">
                <name>To Review ODC Collection Request</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b3" />
                </connection>
            </flow>
            <flow id="528cd2ca-6545-4614-8638-a383622bd1e2" connectionType="SequenceFlow">
                <name>To End Event1</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b2" />
                </connection>
            </flow>
            <flow id="40db59ea-ddf4-4f91-ad82-c5dc7ea27829" connectionType="SequenceFlow">
                <name>To Create ODC Collection Request </name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45b1" />
                </connection>
            </flow>
            <flow id="407b35e3-4142-4a62-86b0-c68ffa25d1fb" connectionType="SequenceFlow">
                <name>Yes</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c3">
                        <expression>tw.local.odcRequest.OdcCollection.informCADAboutTheCollection</expression>
                    </condition>
                </connection>
            </flow>
            <pool id="fc92c62a-eb7e-4a04-b3bb-104f7fc11c37">
                <name>Pool</name>
                <documentation>&lt;html&gt;&lt;body&gt;-&lt;span style="white-space:pre"&gt;	&lt;/span&gt;This process is used to make the collection of proceeds on an ODC and liquidate the contract on Flex Cube.&lt;/body&gt;&lt;/html&gt;</documentation>
                <restrictedName>at1691923319746</restrictedName>
                <dimension>
                    <size w="3000" h="662" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="2f34dc88-0eda-48b1-82cb-03faf20f7fbd">
                    <name>Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce</attachedParticipant>
                    <flowObject id="6c34a494-03cd-45bc-aa32-0c51125d02b3" componentType="Activity">
                        <name>Create ODC Collection Request </name>
                        <position>
                            <location x="236" y="77" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <narrative>&lt;#= tw.epv.Col_ScreenNames.ODCCol01 #&gt;  &lt;#= tw.system.process.instanceId #&gt;</narrative>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.6255f65f-d7ce-452d-9058-3f856ea792e0</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>3</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.Col_SLA.SLA_Act01)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Create ODC Collection Request – طلب تسجيل حصائل على مستند تحصيل تصدير&lt;#= tw.system.process.instanceId #&gt;</subject>
                                <narrative>&lt;#= tw.epv.Col_ScreenNames.ODCCol01 #&gt;  &lt;#= tw.system.process.instanceId #&gt;</narrative>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4605">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.b47fdb39-1fa3-4219-8cbd-5c40e9bece76</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4604">
                                    <name>routingDetails</name>
                                    <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails</value>
                                    <parameterId>2055.fb57a244-f263-444b-8dc2-734946370a33</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4603">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.a2a1bbba-9a3e-4453-84f3-4f78d470cd95</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4602">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.115324d0-8168-495d-8187-b6a3c2434105</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4601">
                                    <name>customerAccounts</name>
                                    <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.customerAccounts</value>
                                    <parameterId>2055.f0a5931f-96f0-4329-81d0-83ef90e6192f</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4600">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.3001fe67-15b3-49f0-876e-e3c5b0ae68c4</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ff">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce</teamRef>
                                    <attachedActivityId>/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fe">
                                        <name>groupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <useDefault>true</useDefault>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_MKR"</value>
                                        <parameterId>2055.b95f0910-f183-4dab-9065-28297a14ceef</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fd">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45da">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="40db59ea-ddf4-4f91-ad82-c5dc7ea27829" />
                        </inputPort>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ce">
                            <positionId>rightTop</positionId>
                            <input>true</input>
                            <flow ref="e65b9e5f-0fff-4bfc-8899-7b8266e14e83" />
                        </inputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c8">
                            <positionId>rightBottom</positionId>
                            <flow ref="c3a3c0bd-8021-4f3b-8182-ffc4b24b2527" />
                        </outputPort>
                        <attachedEvent id="aec4fe46-5179-4bf0-830d-b2197bde09b3" componentType="Event">
                            <name>Timer/Create ODC Collection Request </name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomRight</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="797540ef-74fd-40a7-830b-b21dd47dc747">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="0c1429b6-50f2-4f11-8a12-4bad07e715ba">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cc">
                                <positionId>bottomCenter</positionId>
                                <flow ref="411f55c7-efea-401d-83fa-637a1a614dcc" />
                            </outputPort>
                            <assignment id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e5">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskId</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="578cd29e-eb65-4611-887c-ca89e350bbe0" componentType="Activity">
                        <name>User Task</name>
                        <position>
                            <location x="794" y="77" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>AUTOMATIC</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>28e0abf4-f65e-4a08-bd7b-dfe602249008/1.46cfd991-7ee5-4ce2-887a-8b21634e7e95</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <laneFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e7">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e6">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                            <preconditions id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e9">
                                <triggerType>0</triggerType>
                                <matchAll>true</matchAll>
                                <documentTriggerMode>1</documentTriggerMode>
                                <sourceFolderReferenceType>0</sourceFolderReferenceType>
                            </preconditions>
                        </component>
                    </flowObject>
                    <flowObject id="2300d98d-7703-498f-a179-da81156caffd" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="35" y="100" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45db">
                            <positionId>rightCenter</positionId>
                            <flow ref="40db59ea-ddf4-4f91-ad82-c5dc7ea27829" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="65373fd5-a6e7-471c-89f3-9914a767f490" componentType="Gateway">
                        <name>Inform CAD Team?</name>
                        <documentation></documentation>
                        <position>
                            <location x="431" y="114" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c7">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="c3a3c0bd-8021-4f3b-8182-ffc4b24b2527" />
                        </inputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d8">
                            <positionId>bottomCenter</positionId>
                            <flow ref="407b35e3-4142-4a62-86b0-c68ffa25d1fb" />
                        </outputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d9">
                            <positionId>rightCenter</positionId>
                            <flow ref="********-401c-4a45-9acf-2dc238bc3950" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="cbfe586a-2d9a-4152-b545-72306e0bfe17">
                    <name>Hub Checker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</attachedParticipant>
                    <flowObject id="3bbe7551-846d-4a0d-88b3-cbeee15bc65b" componentType="Activity">
                        <name>Review ODC Collection Request</name>
                        <position>
                            <location x="598" y="48" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <narrative>&lt;#= tw.epv.Col_ScreenNames.ODCCol02 #&gt;  &lt;#= tw.system.process.instanceId #&gt;</narrative>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.429675b0-3048-4910-8b11-e3d3eb0cd480</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.Col_SLA.SLA_Act02)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review ODC Collection Request</subject>
                                <narrative>&lt;#= tw.epv.Col_ScreenNames.ODCCol02 #&gt;  &lt;#= tw.system.process.instanceId #&gt;</narrative>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fb">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45fa">
                                    <name>customerAccounts</name>
                                    <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
                                    <array>true</array>
                                    <input>true</input>
                                    <value>tw.local.customerAccounts</value>
                                    <parameterId>2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f9">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f8">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.568ffb88-c172-4fff-841d-2e47908795cb</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f7">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</teamRef>
                                    <attachedActivityId>/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f6">
                                        <name>groupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <useDefault>true</useDefault>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_CHKR"</value>
                                        <parameterId>2055.b95f0910-f183-4dab-9065-28297a14ceef</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f5">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d7">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="********-401c-4a45-9acf-2dc238bc3950" />
                        </inputPort>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c5">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="2d535ad4-ad69-4293-850f-e7ea81aeccb3" />
                        </inputPort>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c1">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="b6c3d9ed-93d3-4698-8888-547b82966e13" />
                        </inputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d6">
                            <positionId>rightBottom</positionId>
                            <flow ref="afb60f23-0dec-4ee4-8ef0-07590f68ca18" />
                        </outputPort>
                        <attachedEvent id="6b353da2-4799-4357-8635-1d550dcb5798" componentType="Event">
                            <name>Timer/Review ODC Collection Request</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomRight</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="97cbc8f0-bf40-4c43-808f-09f05b5d6869">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="730b13f1-e3ca-4b97-8d5e-c15b872f4ab3">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>1</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>1</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ca">
                                <positionId>bottomCenter</positionId>
                                <flow ref="a45be2a3-5a13-4ad5-8503-1b96f538d134" />
                            </outputPort>
                            <assignment id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45e2">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskId</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="e3ff4aa9-775b-4509-9e93-4b2a929de6ad" componentType="Event">
                        <name>End Authorize</name>
                        <documentation></documentation>
                        <position>
                            <location x="862" y="45" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d0">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="4548b365-e059-4b60-88e6-963bd9bdb102" componentType="Event">
                        <name>End Cancel</name>
                        <documentation></documentation>
                        <position>
                            <location x="871" y="101" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d1">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="5a8d21ed-0bb8-4366-824e-f6e661620491" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="8d5488bb-0472-4bd1-8c3d-ed8a0fd2208f" componentType="Gateway">
                        <name>Check Action</name>
                        <documentation></documentation>
                        <position>
                            <location x="703" y="84" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d5">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="afb60f23-0dec-4ee4-8ef0-07590f68ca18" />
                        </inputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d3">
                            <positionId>rightCenter</positionId>
                            <flow ref="55fa20ba-f7f0-4aa7-8e20-b6f8179ff47c" />
                        </outputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d2">
                            <positionId>topCenter</positionId>
                            <flow ref="e65b9e5f-0fff-4bfc-8899-7b8266e14e83" />
                        </outputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45d4">
                            <positionId>rightCenter</positionId>
                            <flow ref="5a8d21ed-0bb8-4366-824e-f6e661620491" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="5cc9debc-a01c-46df-82f3-935c3a9ca390">
                    <name>System</name>
                    <height>262</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.da7e4d23-78cb-4483-98ed-b9c238308a03</attachedParticipant>
                    <flowObject id="b8ccb671-17d0-4597-8b78-b1c858672409" componentType="Activity">
                        <name>Send Escalation Mail</name>
                        <documentation>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</documentation>
                        <position>
                            <location x="501" y="112" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.d7acf968-6740-4e52-b037-2049466eeeb2</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f3">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f2">
                                    <name>taskId</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.taskId</value>
                                    <parameterId>2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f1">
                                    <serviceType>1</serviceType>
                                    <teamRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45f0">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45cb">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="411f55c7-efea-401d-83fa-637a1a614dcc" />
                        </inputPort>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c9">
                            <positionId>rightTop</positionId>
                            <input>true</input>
                            <flow ref="a45be2a3-5a13-4ad5-8503-1b96f538d134" />
                        </inputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45be">
                            <positionId>rightCenter</positionId>
                            <flow ref="0ccb44e3-2a5f-45c4-885e-74c915e4b2a1" />
                        </outputPort>
                        <attachedEvent id="31deeeb5-4f4b-450e-892b-519f89b6ba64" componentType="Event">
                            <name>Error1</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomLeft</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>true</doCloseTask>
                                <EventAction id="42aecbb1-bc82-42f9-876d-17d2f2e842b2">
                                    <actionType>5</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="31536ba9-7809-4977-82eb-35b7f98a9826">
                                        <faultStyle>1</faultStyle>
                                        <isCatchAll>true</isCatchAll>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c0">
                                <positionId>bottomCenter</positionId>
                                <flow ref="528cd2ca-6545-4614-8638-a383622bd1e2" />
                            </outputPort>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="426ad1b2-fd3f-4430-81c1-9d1d0664c795" componentType="Activity">
                        <name>Send CAD Team Mail</name>
                        <documentation>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</documentation>
                        <position>
                            <location x="499" y="22" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.a6205b87-57cc-47bb-abfc-edff0743b08e</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ed">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.8510e459-4375-44f2-afb0-c0b98b03ee89</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ec">
                                    <name>mailTo</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.mailTo</value>
                                    <parameterId>2055.bfd0ceee-c127-406e-b3f0-24f2539d2ea1</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45eb">
                                    <serviceType>1</serviceType>
                                    <teamRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.da7e4d23-78cb-4483-98ed-b9c238308a03</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ea">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c4">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="407b35e3-4142-4a62-86b0-c68ffa25d1fb" />
                        </inputPort>
                        <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c6">
                            <positionId>rightCenter</positionId>
                            <flow ref="2d535ad4-ad69-4293-850f-e7ea81aeccb3" />
                        </outputPort>
                        <assignment id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45ef">
                            <assignTime>1</assignTime>
                            <to>tw.epv.Mails.CadTeam</to>
                            <from>tw.local.mailTo</from>
                        </assignment>
                        <attachedEvent id="d6e38de3-cffd-4f49-8793-11fd93a0c977" componentType="Event">
                            <name>Error</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>topCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>true</doCloseTask>
                                <EventAction id="a2c6e895-55bc-4f40-877c-b4a06daf693e">
                                    <actionType>5</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="06abad98-ee10-430a-8af6-46e4bdda4792">
                                        <faultStyle>1</faultStyle>
                                        <isCatchAll>true</isCatchAll>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45c2">
                                <positionId>topCenter</positionId>
                                <flow ref="b6c3d9ed-93d3-4698-8888-547b82966e13" />
                            </outputPort>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="e3080c20-6d98-42f1-85ae-ccf62f692b6e" componentType="Event">
                        <name>End Event1</name>
                        <documentation></documentation>
                        <position>
                            <location x="718" y="202" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bf">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="528cd2ca-6545-4614-8638-a383622bd1e2" />
                        </inputPort>
                        <inputPort id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bd">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="0ccb44e3-2a5f-45c4-885e-74c915e4b2a1" />
                        </inputPort>
                    </flowObject>
                </lane>
                <inputParameter id="c2d7852f-6525-4794-8231-2b0bfd65a374">
                    <bpdParameterId>2007.c2d7852f-6525-4794-8231-2b0bfd65a374</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <inputParameter id="e6db1bbe-7fd9-4e06-8f40-f78c1e68547d">
                    <bpdParameterId>2007.e6db1bbe-7fd9-4e06-8f40-f78c1e68547d</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <privateVariable id="389ebfab-ad7c-45f7-80b1-7bcacb85f82c">
                    <name>taskId</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="662a0040-1dd6-4b44-8213-26b31d166e45">
                    <name>mailTo</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="47aab0e9-13a2-4912-8526-d7424de98556">
                    <name>customerAccounts</name>
                    <description></description>
                    <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
                    <arrayOf>true</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="c8bac588-62b1-43ea-8239-3bb0bb495357">
                    <name>parentPath</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <epv id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-460a">
                    <epvId>/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd</epvId>
                </epv>
                <epv id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4609">
                    <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
                </epv>
                <epv id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4608">
                    <epvId>/21.00a114f4-2b01-4c48-aad9-bd62580da24b</epvId>
                </epv>
                <epv id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4607">
                    <epvId>/21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c</epvId>
                </epv>
                <searchableField id="5d556fee-ab55-426f-8fae-c633625c524d">
                    <name>CustomerCIF</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4879">
                        <expression>tw.local.odcRequest.CustomerInfo.cif</expression>
                    </expression>
                </searchableField>
                <searchableField id="8d013837-0e10-4860-8a09-3c720a4eff7c">
                    <name>CustomerName</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4878">
                        <expression>tw.local.odcRequest.CustomerInfo.customerName</expression>
                    </expression>
                </searchableField>
                <searchableField id="0df39e40-549e-4dfe-8620-b81e23db8442">
                    <name>ParentRequestNumber</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4877">
                        <expression>tw.local.odcRequest.parentRequestNo</expression>
                    </expression>
                </searchableField>
                <searchableField id="156d15c5-3e68-43b1-813f-d4342fd62a41">
                    <name>Initiation</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4876">
                        <expression>tw.local.odcRequest.initiator</expression>
                    </expression>
                </searchableField>
                <searchableField id="d1326d94-db9a-43ca-80f9-ed14f69e5b5b">
                    <name>RequestDate</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4875">
                        <expression>tw.local.odcRequest.appInfo.requestDate</expression>
                    </expression>
                </searchableField>
                <searchableField id="2bf36b4f-823f-4d6c-81bb-c850cb059591">
                    <name>RequestStatus</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4874">
                        <expression>tw.local.odcRequest.appInfo.status</expression>
                    </expression>
                </searchableField>
                <searchableField id="f5e084bc-df19-47f8-8e4e-a849582d1912">
                    <name>initiatorUsername</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4873">
                        <expression>tw.local.odcRequest.appInfo.initiator</expression>
                    </expression>
                </searchableField>
            </pool>
            <extension id="bpdid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-45bc" type="CASE">
                <caseFolder id="9ead2f75-765e-4737-9f97-38a0bf8292d2">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

