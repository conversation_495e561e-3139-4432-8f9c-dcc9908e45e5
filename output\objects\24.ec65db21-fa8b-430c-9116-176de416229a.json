{"id": "24.ec65db21-fa8b-430c-9116-176de416229a", "versionId": "af559561-b69f-477c-ab2b-4b4c3fde2004", "name": "Trade FO Managers", "type": "participant", "typeName": "Participant", "details": {}, "_fullObjectData": {"teamworks": {"participant": {"id": "24.ec65db21-fa8b-430c-9116-176de416229a", "name": "Trade FO Managers", "lastModified": "1691144863305", "lastModifiedBy": "heba", "participantId": "24.ec65db21-fa8b-430c-9116-176de416229a", "participantDefinition": {"isNull": "true"}, "simulationGroupSize": "2", "capacityType": "1", "definitionType": "3", "percentAvailable": {"isNull": "true"}, "percentEfficiency": {"isNull": "true"}, "cost": "10.00", "currencyCode": {"isNull": "true"}, "image": {"isNull": "true"}, "serviceMembersRef": {"isNull": "true"}, "managersRef": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"team\":[{\"members\":{\"Users\":[{\"name\":\"heba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"somaia\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"abdelrahman.saleh\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}],\"UserGroups\":[{\"name\":\"BPM_ODC_Trade_FO_MNGR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails\",\"type\":\"StandardMembers\"}]},\"documentation\":[{\"content\":[],\"textFormat\":\"text\\/plain\"}],\"name\":\"Trade FO Managers\",\"declaredType\":\"resource\",\"id\":\"24.ec65db21-fa8b-430c-9116-176de416229a\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.w3.org\\/1999\\/XPath\",\"id\":\"24.ec65db21-fa8b-430c-9116-176de416229a\"}", "externalId": {"isNull": "true"}, "description": "", "guid": "guid:d694a63221635d5b:6baf87c4:18969a9a6e2:774a", "versionId": "af559561-b69f-477c-ab2b-4b4c3fde2004", "standardMembers": {"standardMember": [{"type": "Group", "name": "BPM_ODC_Trade_FO_MNGR"}, {"type": "User", "name": "heba"}, {"type": "User", "name": "so<PERSON>ia"}, {"type": "User", "name": "abdelrahman.saleh"}]}, "teamAssignments": ""}}}}