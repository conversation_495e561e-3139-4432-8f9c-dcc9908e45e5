<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd" name="Authorize FileNet">
        <lastModified>1691317501698</lastModified>
        <lastModifiedBy>abdel<PERSON>man.saleh</lastModifiedBy>
        <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.0b320dc4-6131-4b0f-8ea0-29a9c1b2603c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:62a4244840b5e546:3b32f8f5:189c9d731c5:-d13</guid>
        <versionId>97315d56-b618-4e50-85da-f470f8f8aa63</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:62a4244840b5e546:3b32f8f5:189c9d731c5:-cfe" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.0470a4c5-73b1-44d0-8643-3883ec1e5153"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"47a87089-67b1-4b4b-8f97-cf5f90e85af1"},{"incoming":["63404638-661a-4b89-888b-01fb9541c617"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:62a4244840b5e546:3b32f8f5:189c9d731c5:-d11"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"2af2d26b-bde1-4a0f-8937-91dc1fdddfb6"},{"targetRef":"0b320dc4-6131-4b0f-8ea0-29a9c1b2603c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Authorize","declaredType":"sequenceFlow","id":"2027.0470a4c5-73b1-44d0-8643-3883ec1e5153","sourceRef":"47a87089-67b1-4b4b-8f97-cf5f90e85af1"},{"startQuantity":1,"outgoing":["63404638-661a-4b89-888b-01fb9541c617"],"incoming":["2027.0470a4c5-73b1-44d0-8643-3883ec1e5153"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":276,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Authorize","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0b320dc4-6131-4b0f-8ea0-29a9c1b2603c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.authorized = true;"]}},{"targetRef":"2af2d26b-bde1-4a0f-8937-91dc1fdddfb6","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"63404638-661a-4b89-888b-01fb9541c617","sourceRef":"0b320dc4-6131-4b0f-8ea0-29a9c1b2603c"}],"laneSet":[{"id":"52f648b8-79dc-402f-8e9c-2a4b158a3ec5","lane":[{"flowNodeRef":["47a87089-67b1-4b4b-8f97-cf5f90e85af1","2af2d26b-bde1-4a0f-8937-91dc1fdddfb6","0b320dc4-6131-4b0f-8ea0-29a9c1b2603c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"4f2b86a8-2fcb-4eea-86bd-97cc85f40459","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Authorize FileNet","declaredType":"process","id":"1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"authorized","isCollection":false,"id":"2055.3ae63899-f1d4-4e24-8d35-77ac7c265121","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"documentId","isCollection":false,"id":"2055.df0bbaee-a1b3-4519-88ef-708c5e1c2f8e","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"objectTypeId","isCollection":false,"id":"2055.71a0542e-6fb3-4080-8f33-90229c51cdf9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":false,"id":"2055.96c9980b-0502-441f-841e-3d8f05bcc8a9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"serverName","isCollection":false,"id":"2055.13efc884-62a5-4ee9-8f19-b9bcd9241c29","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}readOnly":"true"}}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="documentId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.df0bbaee-a1b3-4519-88ef-708c5e1c2f8e</processParameterId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6944f359-4684-460b-a27b-9e990bf54f12</guid>
            <versionId>daed816f-4327-4f91-922d-9fd37545216d</versionId>
        </processParameter>
        <processParameter name="objectTypeId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.71a0542e-6fb3-4080-8f33-90229c51cdf9</processParameterId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6b6bda28-c395-4edc-be1a-037e0f460f81</guid>
            <versionId>a453d445-4c3e-4823-a67a-97e97bce8eef</versionId>
        </processParameter>
        <processParameter name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.96c9980b-0502-441f-841e-3d8f05bcc8a9</processParameterId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c6531a7a-ffda-49be-8035-d038a3bb762c</guid>
            <versionId>50e5eaa3-eb32-4844-99ad-ba560d46bdbf</versionId>
        </processParameter>
        <processParameter name="serverName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.13efc884-62a5-4ee9-8f19-b9bcd9241c29</processParameterId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1f080743-54e2-4994-ad29-0ce8497f6746</guid>
            <versionId>398a51c2-7284-48eb-8c15-df6f543517da</versionId>
        </processParameter>
        <processParameter name="authorized">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3ae63899-f1d4-4e24-8d35-77ac7c265121</processParameterId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d725df82-2a70-47a5-b4ec-4cbe291e9b02</guid>
            <versionId>74d60aa5-4ac3-4f11-8dee-3dbc084293b2</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0b320dc4-6131-4b0f-8ea0-29a9c1b2603c</processItemId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <name>Authorize</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.56734f6a-536f-495f-8d2b-5fc4c35db64b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:62a4244840b5e546:3b32f8f5:189c9d731c5:-cff</guid>
            <versionId>a7369584-67ac-42bc-90a2-48360edb989e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="276" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.56734f6a-536f-495f-8d2b-5fc4c35db64b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.authorized = true;</script>
                <isRule>false</isRule>
                <guid>c0489397-a8cb-45b9-b9b3-f4dd28e139ee</guid>
                <versionId>ba41dac3-fecd-4da0-b384-805ce7dbda26</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2af2d26b-bde1-4a0f-8937-91dc1fdddfb6</processItemId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.9930990d-36a9-4531-92ec-9388a9651c2e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:62a4244840b5e546:3b32f8f5:189c9d731c5:-d11</guid>
            <versionId>f9c7d2b5-b093-4214-becc-0384ada3b1eb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.9930990d-36a9-4531-92ec-9388a9651c2e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2df3d25a-bf37-4256-a978-2896293e2851</guid>
                <versionId>6761de5d-07ef-4972-abfe-6ef35caa636d</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.0b320dc4-6131-4b0f-8ea0-29a9c1b2603c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Authorize FileNet" id="1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="documentId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.df0bbaee-a1b3-4519-88ef-708c5e1c2f8e" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="objectTypeId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.71a0542e-6fb3-4080-8f33-90229c51cdf9" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="action" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.96c9980b-0502-441f-841e-3d8f05bcc8a9" ns3:readOnly="true" />
                        
                        
                        <ns16:dataInput name="serverName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.13efc884-62a5-4ee9-8f19-b9bcd9241c29" ns3:readOnly="true" />
                        
                        
                        <ns16:dataOutput name="authorized" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.3ae63899-f1d4-4e24-8d35-77ac7c265121" ns3:readOnly="true" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="52f648b8-79dc-402f-8e9c-2a4b158a3ec5">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="4f2b86a8-2fcb-4eea-86bd-97cc85f40459" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>47a87089-67b1-4b4b-8f97-cf5f90e85af1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2af2d26b-bde1-4a0f-8937-91dc1fdddfb6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0b320dc4-6131-4b0f-8ea0-29a9c1b2603c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="47a87089-67b1-4b4b-8f97-cf5f90e85af1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.0470a4c5-73b1-44d0-8643-3883ec1e5153</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="2af2d26b-bde1-4a0f-8937-91dc1fdddfb6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:62a4244840b5e546:3b32f8f5:189c9d731c5:-d11</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>63404638-661a-4b89-888b-01fb9541c617</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="47a87089-67b1-4b4b-8f97-cf5f90e85af1" targetRef="0b320dc4-6131-4b0f-8ea0-29a9c1b2603c" name="To Authorize" id="2027.0470a4c5-73b1-44d0-8643-3883ec1e5153">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Authorize" id="0b320dc4-6131-4b0f-8ea0-29a9c1b2603c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="276" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.0470a4c5-73b1-44d0-8643-3883ec1e5153</ns16:incoming>
                        
                        
                        <ns16:outgoing>63404638-661a-4b89-888b-01fb9541c617</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.authorized = true;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="0b320dc4-6131-4b0f-8ea0-29a9c1b2603c" targetRef="2af2d26b-bde1-4a0f-8937-91dc1fdddfb6" name="To End" id="63404638-661a-4b89-888b-01fb9541c617">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.63404638-661a-4b89-888b-01fb9541c617</processLinkId>
            <processId>1.2e3af9a6-6929-42f0-88e0-db7dfb775ddd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0b320dc4-6131-4b0f-8ea0-29a9c1b2603c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.2af2d26b-bde1-4a0f-8937-91dc1fdddfb6</toProcessItemId>
            <guid>e808f583-cd08-403b-ac97-94f16c02add0</guid>
            <versionId>5f1daa2f-3cdf-4d5c-b934-7f68825f45ba</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0b320dc4-6131-4b0f-8ea0-29a9c1b2603c</fromProcessItemId>
            <toProcessItemId>2025.2af2d26b-bde1-4a0f-8937-91dc1fdddfb6</toProcessItemId>
        </link>
    </process>
</teamworks>

