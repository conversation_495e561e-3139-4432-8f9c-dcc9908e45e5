<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e" name="ACT04 - ODC Execution Hub Initiation 2">
        <lastModified>1748954062493</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.1af97ac0-dd77-44e4-a232-cc4b0abc55bd</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>5efe43be-e82b-49bb-99c3-085bf2e856fc</guid>
        <versionId>94c77e27-2519-45af-b545-d09e381df821</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.42f80e5f-8a4a-498c-b7a2-16fa8fed4964"],"isInterrupting":true,"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":203,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"897246e0-1ff4-4d8e-8856-f1065c4af670"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.b31c88ee-60cd-49c6-a416-813a2b92f186"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorPanelVIS","isCollection":false,"declaredType":"dataObject","id":"2056.29254c11-4b82-47de-99bf-48ae28dde3da"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"terminateReasonVIS","isCollection":false,"declaredType":"dataObject","id":"2056.81fedb58-c398-4515-ae09-86a82257376a"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.cbc8bfdf-c314-477f-9f37-a30e923dea30"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"deliveryterms","isCollection":false,"declaredType":"dataObject","id":"2056.7c792a93-f234-40ef-aa14-e2dc7216b19e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"paymentTerms","isCollection":false,"declaredType":"dataObject","id":"2056.a1bf9dee-8f71-45ac-af5b-e99ad064620b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"specialInstructions","isCollection":false,"declaredType":"dataObject","id":"2056.8dcd31b9-9016-4ff0-9ce2-c3529cb6fb9d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instructions","isCollection":false,"declaredType":"dataObject","id":"2056.1df3fe6c-0337-4290-b8f3-39ea8d85c6f4"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"flexCubeVIS","isCollection":false,"declaredType":"dataObject","id":"2056.d276328e-57c1-4166-8957-c08c78f8e71f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestTypeVIS","isCollection":false,"declaredType":"dataObject","id":"2056.cb706ead-28bf-435f-9261-652f4bfaa72a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"contractStageVIS","isCollection":false,"declaredType":"dataObject","id":"2056.********-0448-4189-8dde-a0179462f959"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"bankRefVIS","isCollection":false,"declaredType":"dataObject","id":"2056.db36bc77-aa3e-47f6-a593-341ef82cc497"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"event","isCollection":false,"declaredType":"dataObject","id":"2056.4d42ba83-44c8-4e42-9754-0bfc72284a78"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"currencyList","isCollection":true,"declaredType":"dataObject","id":"2056.********-875a-4e58-8708-4fae1d30ee9a"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"addchargeBtnVIS","isCollection":false,"declaredType":"dataObject","id":"2056.ece208c0-ed2b-4966-a126-fa5405ec4a5b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.addressLine1 = \"\";\nautoObject.addressLine2 = \"\";\nautoObject.addressLine3 = \"\";\nautoObject.customerSector = {};\nautoObject.customerSector.name = \"\";\nautoObject.customerSector.value = \"\";\nautoObject.customerType = \"\";\nautoObject.customerNoCBE = \"\";\nautoObject.facilityType = {};\nautoObject.facilityType.name = \"\";\nautoObject.facilityType.value = \"\";\nautoObject.commercialRegistrationNo = \"\";\nautoObject.commercialRegistrationOffice = \"\";\nautoObject.taxCardNo = \"\";\nautoObject.importCardNo = \"\";\nautoObject.initiationHub = \"\";\nautoObject.country = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a","name":"collectingBankInfo","isCollection":false,"declaredType":"dataObject","id":"2056.89708c1b-6b9d-41f2-bf2b-ee892cced83c"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.addressLine1 = \"\";\nautoObject.addressLine2 = \"\";\nautoObject.addressLine3 = \"\";\nautoObject.customerSector = {};\nautoObject.customerSector.name = \"\";\nautoObject.customerSector.value = \"\";\nautoObject.customerType = \"\";\nautoObject.customerNoCBE = \"\";\nautoObject.facilityType = {};\nautoObject.facilityType.name = \"\";\nautoObject.facilityType.value = \"\";\nautoObject.commercialRegistrationNo = \"\";\nautoObject.commercialRegistrationOffice = \"\";\nautoObject.taxCardNo = \"\";\nautoObject.importCardNo = \"\";\nautoObject.initiationHub = \"\";\nautoObject.country = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a","name":"partyTypesInfo","isCollection":false,"declaredType":"dataObject","id":"2056.********-d1aa-468a-bc48-e587ac5eeb31"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"multiTenorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.2f081e13-51c5-4be0-9294-062096f7fd08"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"PartyAccountsList","isCollection":true,"declaredType":"dataObject","id":"2056.d98726b8-6695-4b10-8803-943520ff4178"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"initialAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.55e6ae77-6817-41a2-b1dd-c8747c747bd0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNumberVIS","isCollection":false,"declaredType":"dataObject","id":"2056.ceb8495e-0120-49f7-9dbf-2a0e36592779"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"x","isCollection":false,"declaredType":"dataObject","id":"2056.cc511d7b-d4df-4103-bf78-29086509bb31"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"drawerAccountsList","isCollection":true,"declaredType":"dataObject","id":"2056.79a56e25-fe18-4e7b-9b6c-58618ad88e34"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"selectedIndex","isCollection":false,"declaredType":"dataObject","id":"2056.75a0dbc0-85c6-4b92-8373-90e553ce7cda"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fromCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.a7a621de-c6a0-4d01-b413-2bb5081f1d8e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"toCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.fbcad2b2-b92b-464f-b9c7-f2239a54d7fa"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"rate","isCollection":false,"declaredType":"dataObject","id":"2056.7135a9c7-9a47-4e9b-a8c5-1a4271be6480"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"barCodePdf","isCollection":false,"declaredType":"dataObject","id":"2056.fc8ed296-8617-4d86-a64b-e0c3a129ed8e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"generationStatus","isCollection":false,"declaredType":"dataObject","id":"2056.aca011e8-67f2-44f5-ae21-da53fc945cd6"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"calculatedChangeAmnt","isCollection":false,"declaredType":"dataObject","id":"2056.3eb7b6c6-dd5f-4eba-a979-64469e9fcae7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerAndPartyCifs","isCollection":true,"declaredType":"dataObject","id":"2056.99052b11-6ce0-4f34-b695-4c70ca8ae388"},{"itemSubjectRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","name":"customerFullDetails","isCollection":false,"declaredType":"dataObject","id":"2056.53d49233-94d9-4e5a-8ba3-eaebba2fa02d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"addressBICList","isCollection":true,"declaredType":"dataObject","id":"2056.e1661039-8d68-42ad-b899-7df882f745f1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"partyTypeName","isCollection":false,"declaredType":"dataObject","id":"2056.425b6eb4-8146-4efc-b28f-d6c767a376a5"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"new Date()"}]},"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"todayDate","isCollection":false,"declaredType":"dataObject","id":"2056.a596dbe0-9b59-4be4-b114-6670373fec7c"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.1d6a0c9d-d60a-4be6-bb42-2ed41259a098"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"validLen","isCollection":false,"declaredType":"dataObject","id":"2056.02f42280-6a65-45cd-8887-5d8076e25368"},{"incoming":["2027.6f265f51-afa4-48ef-8737-a2afa20c2f35"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":813,"y":201,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"2025.b942fed5-4b87-4dfc-8d5b-669f5b56415b"},{"outgoing":["2027.6f265f51-afa4-48ef-8737-a2afa20c2f35","2027.8352556c-701f-4ea2-8d56-22bcc5145630"],"incoming":["2027.15174c2b-d9a4-485c-8144-313f9245b2b0"],"default":"2027.6f265f51-afa4-48ef-8737-a2afa20c2f35","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":686,"y":195,"declaredType":"TNodeVisualInfo","height":32}]},"name":"isValid","declaredType":"exclusiveGateway","id":"2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb"},{"startQuantity":1,"outgoing":["2027.15174c2b-d9a4-485c-8144-313f9245b2b0"],"incoming":["2027.34a4d91b-89f1-4dca-9ada-0b13a7a0991f"],"default":"2027.15174c2b-d9a4-485c-8144-313f9245b2b0","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":547,"y":176,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.eca5b84b-de97-4ab6-bdba-a8b9cb7bdc9e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMessage = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0;\r\ntw.local.invalidTabs = [];\r\ntw.system.coachValidation.clearValidationErrors();\r\n\r\n\/\/\/\/-------------------------------------------BASIC DETAILS VALIDATION -----------------------------------\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo,\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate,\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef,\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate,\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate\"\r\n);\r\n\r\nmaxLength(\r\n\ttw.local.odcRequest.BasicDetails.flexCubeContractNo,\r\n\t\"tw.local.odcRequest.BasicDetails.flexCubeContractNo\",\r\n\t16,\r\n\ttw.resource.ValidationMessages.MaxLength16,\r\n\t\"Flex Cube Contract Number: \" + tw.resource.ValidationMessages.MaxLength16\r\n);\r\n\/\/add mess 160 to local file\r\nmaxLength(\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\",\r\n\t160,\r\n\t\"Shouldn't be more than 160 character\",\r\n\t\"Commodity Description: \" + \"Shouldn't be more than 160 character\"\r\n);\r\nfor (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\n\tmaxLength(\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\n\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\",\r\n\t\t20,\r\n\t\t\"Shouldn't be more than 20 character\",\r\n\t\t\"invoice Number: \" + \"Shouldn't be more than 20 character\"\r\n\t);\r\n}\r\n\r\nfor (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceDate\"\r\n\t\t);\r\n\t}\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\"\r\n\t\t);\r\n\t}\r\n}\r\n\r\nfor (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Bills.length; i++) {\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingDate\"\r\n\t\t);\r\n\t}\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingRef\"\r\n\t\t);\r\n\t}\r\n}\r\nvalidateTab(0, \"Basic Details Tab\");\r\n\/\/-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------\r\nmandatory(\r\n\ttw.local.odcRequest.FinancialDetailsBR.maxCollectionDate,\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate\"\r\n);\r\n\r\nminLength(\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\n\t2,\r\n\t\"Shouldn't be less than 14 character\",\r\n\t\"Amount Advanced: Shouldn't be less than 2 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\n\t14,\r\n\t\"Shouldn't be more than 14 character\",\r\n\t\"Amount Advanced:\" + \"Shouldn't be more than 14 character\"\r\n);\r\nvalidateTab(3, \"Financial Details - Branch Tab\");\r\n\/\/-------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------\r\nif (tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0) {\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.currency.name,\r\n\t\t\"tw.local.odcRequest.FcCollections.currency\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\n\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.fromDate,\r\n\t\t\"tw.local.odcRequest.FcCollections.fromDate\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.ToDate,\r\n\t\t\"tw.local.odcRequest.FcCollections.ToDate\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.accountNo.value,\r\n\t\t\"tw.local.odcRequest.FcCollections.accountNo\"\r\n\t);\r\n\r\n\tif (\r\n\t\ttw.local.odcRequest.FcCollections != null &amp;&amp;\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate != null\r\n\t) {\r\n\t\tminLength(\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\n\t\t\t6,\r\n\t\t\t\"Shouldn't be less than 6 character\",\r\n\t\t\t\" Negotiated Exchange Rate: Shouldn't be less than 6 character\"\r\n\t\t);\r\n\t\tmaxLength(\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\n\t\t\t10,\r\n\t\t\t\"Shouldn't be more than 10 character\",\r\n\t\t\t\" Negotiated Exchange Rate:\" + \"Shouldn't be more than 10 character\"\r\n\t\t);\r\n\t}\r\n\r\n\tvalidateTab(4, \"Flexcube collections Tab\");\r\n}\r\n\/\/-----------------------------------------Financial Details - Trade FO VALIDATION ----------------------------\r\n\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.discount,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.discount\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.extraCharges,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.extraCharges\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.ourCharges,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.ourCharges\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\"\r\n);\r\n\r\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\") {\r\n\tif (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length == 0) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates,\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates\"\r\n\t\t);\r\n\t\ttw.local.errorMessage +=\r\n\t\t\t\"&lt;li&gt;\" + \"Fill in at least one entry in Multi Tenordates\" + \"&lt;\/li&gt;\";\r\n\t}\r\n}\r\n\r\nvar sum = 0;\r\n\r\nfor (var i = 0; i &lt; tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length; i++) {\r\n\tif (\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == \"\" ||\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == null\r\n\t) {\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\",\r\n\t\t\t\"Mandatory field\"\r\n\t\t);\r\n\t} else if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount != \"\") {\r\n\t\tcheckNegativeValue(\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount,\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\"\r\n\t\t);\r\n\t}\r\n\tif (\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == \"\" ||\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == null\r\n\t) {\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].date\",\r\n\t\t\t\"Mandatory field\"\r\n\t\t);\r\n\t}\r\n\tsum = sum + tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount;\r\n}\r\n\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\"\r\n);\r\n\r\nif (\r\n\t!!tw.local.odcRequest.FinancialDetailsFO.collectableAmount &amp;&amp;\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; 0\r\n)\r\n\tif (\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount != null &amp;&amp;\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; sum &amp;&amp;\r\n\t\ttw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\"\r\n\t) {\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\",\r\n\t\t\t\"Sum of all Installment Amounts in a request must be &lt;= Amount Collectable by NBE (\u0627\u062c\u0645\u0627\u0644\u0649 \u0627\u0644\u0645\u0628\u0627\u0644\u063a \u0627\u0644\u0645\u0637\u0644\u0648\u0628 \u062a\u062d\u0635\u064a\u0644\u0647\u0627)\"\r\n\t\t);\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length -1 ].date\",\r\n\t\t\t\"Mandatory field\"\r\n\t\t);\r\n\t}\r\n\r\nif (\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization &gt; 0 &amp;&amp;\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization &gt; 0\r\n) {\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\n\t\t\"Partial Avalization isn't allowed\"\r\n\t);\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\n\t\t\"Partial Avalization isn't allowed\"\r\n\t);\r\n}\r\nvar sumAvalization = parseFloat(0);\r\nsumAvalization =\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization +\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization +\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight;\r\nif (sumAvalization != tw.local.odcRequest.FinancialDetailsFO.collectableAmount) {\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\n\t);\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\n\t);\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\",\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\n\t);\r\n}\r\nmandatory(\r\n\ttw.local.odcRequest.FinancialDetailsFO.maturityDate,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.maturityDate\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\"\r\n);\r\n\r\n\/\/--------------------------------------------Importer Details VALIDATION ----------------------------------\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\"\r\n);\r\nmandatory(tw.local.odcRequest.ImporterDetails.bank, \"tw.local.odcRequest.ImporterDetails.bank\");\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\"\r\n);\r\n\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\",\r\n\t250,\r\n\t\"Shouldn't be more than 250 character\",\r\n\t\"Importer Name:\" + \"Shouldn't be more than 250 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\",\r\n\t400,\r\n\t\"Shouldn't be more than 400 character\",\r\n\t\"Importer Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.importerPhoneNo,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerPhoneNo\",\r\n\t20,\r\n\t\"Shouldn't be more than 20 character\",\r\n\t\"Importer Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.bank,\r\n\t\"tw.local.odcRequest.ImporterDetails.bank\",\r\n\t250,\r\n\t\"Shouldn't be more than 250 character\",\r\n\t\"Importer Bank:\" + \"Shouldn't be more than 250 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\",\r\n\t11,\r\n\t\"Shouldn't be more than 11 character\",\r\n\t\"Importer Bank BIC Code:\" + \"Shouldn't be more than 11 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.ibanAccount,\r\n\t\"tw.local.odcRequest.ImporterDetails.ibanAccount\",\r\n\t40,\r\n\t\"Shouldn't be more than 40 character\",\r\n\t\"Importer Account(IBAN):\" + \"Shouldn't be more than 40 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\",\r\n\t400,\r\n\t\"Shouldn't be more than 400 character\",\r\n\t\"Importer Bank Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.bankPhoneNo,\r\n\t\"tw.local.odcRequest.ImporterDetails.bankPhoneNo\",\r\n\t20,\r\n\t\"Shouldn't be more than 20 character\",\r\n\t\"Importer Bank Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.collectingBankReference,\r\n\t\"tw.local.odcRequest.ImporterDetails.collectingBankReference\",\r\n\t30,\r\n\t\"Shouldn't be more than 30 character\",\r\n\t\"Collecting Bank Reference:\" + \"Shouldn't be more than 30 character\"\r\n);\r\nvalidateTab(6, \"Importer Details Tab\");\r\n\r\n\/\/-----------------------------------------Contract Creation VALIDATION -------------------------------------\r\n\r\n\r\nvalidateString(\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\",\r\n\t16,\r\n\t\"Shouldn't be more than 16 character\",\r\n\t\"Contract Creation User Reference\" + \"Shouldn't be more than 16 character\"\r\n);\r\n\r\nvalidateTab(8, \"Contract Creation Tab\");\r\n\/\/---------------------------------------------\/\/Charges and Commissions VALIDATION -------------------------------------\r\nfor (var i = 0; i &lt; tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\n\t\/\/Description - Flat Amount\r\n\tif (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount &lt; 0 ||\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null\r\n\t\t) {\r\n\t\t\taddError(\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\n\t\t\t\t\"Must be &gt;= 0\"\r\n\t\t\t);\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0) {\r\n\t\t\tvalidateDecimal2(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t\/\/Fixed Rate\r\n\t} else {\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage &lt; 0 ||\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null\r\n\t\t) {\r\n\t\t\taddError(\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\n\t\t\t\t\"Must be &gt;= 0\"\r\n\t\t\t);\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage &gt; 0) {\r\n\t\t\tvalidateDecimal2(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\n\t\t\t);\r\n\t\t}\r\n\t}\r\n\r\n\t\/\/skip validation if waiver or changeAmnt &lt; 0\r\n\tif (\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].waiver == false &amp;&amp;\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0\r\n\t) {\r\n\t\t\/\/GL Account\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value ==\r\n\t\t\t\"GL Account\"\r\n\t\t) {\r\n\t\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\n\t\t\t\taddError(\r\n\t\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\t\ti +\r\n\t\t\t\t\t\t\"].debitedAccount.glAccountNo\",\r\n\t\t\t\t\t\"GL Account Not Verified\"\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.glAccountNo\"\r\n\t\t\t);\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.currency.value\"\r\n\t\t\t);\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.branchCode\"\r\n\t\t\t);\r\n\r\n\t\t\t\/\/Customer Account\r\n\t\t} else {\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount\r\n\t\t\t\t\t.customerAccountNo,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.customerAccountNo\"\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t\/\/DebitedAmount\r\n\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate &gt; 0) {\r\n\t\t\tvalidateDecimal(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAmount.negotiatedExRate\",\r\n\t\t\t\t\"Must be Decimal(16,10)\"\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t\/\/Correct Validation but Waiting confirmation on what to do if GL account\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount &gt;\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance &amp;&amp;\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false\r\n\t\t) {\r\n\t\t\taddError(\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAmount.amountInAccount\",\r\n\t\t\t\t\"ERROR: Must be &lt;= Account Balance\"\r\n\t\t\t);\r\n\t\t}\r\n\t}\r\n}\r\nvalidateTab(9, \"Charges and commissions Tab\");\r\n\/\/---------------------------------------------\/\/Parties VALIDATION -------------------------------------\r\n\/\/\/\/\/Drawer Section\r\nmandatory(tw.local.odcRequest.Parties.Drawer.partyId, \"tw.local.odcRequest.Parties.Drawer.partyId\");\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.Drawer.partyName,\r\n\t\"tw.local.odcRequest.Parties.Drawer.partyName\"\r\n);\r\n\r\n\/\/\/\/\/Drawee Section\r\nmandatory(tw.local.odcRequest.Parties.Drawee.partyId, \"tw.local.odcRequest.Parties.Drawee.partyId\");\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.Drawee.partyName,\r\n\t\"tw.local.odcRequest.Parties.Drawee.partyName\"\r\n);\r\n\r\n\/\/\/\/\/Parties Types (Accountee)\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.partyTypes.partyId,\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyId\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.partyTypes.partyName,\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyName\"\r\n);\r\n\r\nvalidateTab(10, \"Parties Tab\");\r\n\/\/---------------------------------------------------------------------------------\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered\r\n\t\t? \"\"\r\n\t\t: (tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\");\r\n}\r\n\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len) {\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\n\tif (field.length &gt; len) {\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction mandatory(field, fieldName, message) {\r\n\tif (!message) {\r\n\t\tmessage = camelCaseToTitle(fieldName) + \" is Mandatory\";\r\n\t}\r\n\r\n\tif (field == null || field == undefined) {\r\n\t\taddError(fieldName, message, message, true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t} else {\r\n\t\tswitch (typeof field) {\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (\r\n\t\t\t\t\tfield.trim() != undefined &amp;&amp;\r\n\t\t\t\t\tfield.trim() != null &amp;&amp;\r\n\t\t\t\t\tfield.trim().length == 0\r\n\t\t\t\t) {\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0) {\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (field &lt; 0) {\r\n\t\t\t\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\n\t\t\t\t\taddError(fieldName, msg, msg, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif (field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime())) {\r\n\t\t\t\t} else {\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction sumDates(fromDate, toDate, fieldName, controlMessage, validationMessage) {\r\n\tif (toDate - fromDate == 0) {\r\n\t\treturn true;\r\n\t}\r\n\taddError(fieldName, controlMessage, validationMessage);\r\n\treturn false;\r\n}\r\n\/\/=========================================================\r\nfunction checkNegativeValue(field, fieldName) {\r\n\tif (field &lt; 0) {\r\n\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\n\t\taddError(fieldName, msg, msg, true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\n\tvar decimalPattern = \/^\\d{1,4}(\\.\\d{1,6})?$\/;\r\n\tif (!decimalPattern.test(field)) {\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t} else {\r\n\t\treturn true;\r\n\t}\r\n}\r\n\/\/========================================================================================\r\nfunction validateTab(index, tabName) {\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength) {\r\n\t\tif (tw.local.errorMessage.length == 0) {\r\n\t\t\ttw.local.errorMessage +=\r\n\t\t\t\t\"&lt;p&gt;\" + \"Please complete fields in the following tabs:\" + \"&lt;\/p&gt;\";\r\n\t\t}\r\n\t\ttw.local.errorMessage += \"&lt;li&gt;\" + tabName + \"&lt;\/li&gt;\";\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\r\n}\r\n\/\/==============================================================\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\n\tvar decimalPattern = \/^\\d{1,12}(\\.\\d{1,2})?$\/;\r\n\tif (!decimalPattern.test(field)) {\r\n\t\t\/\/ Decimal is valid\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t} else {\r\n\t\t\/\/ Decimal is invalid\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\nfunction camelCaseToTitle(camelCase) {\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\n\t\/\/ Convert camelCase to Title Case\r\n\tvar titleCase = fieldName.replace(\/([A-Z])\/g, \" $1\");\r\n\t\/\/ Uppercase the first character\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\n\treturn titleCase;\r\n}\r\n\r\n\r\nfunction validateString(field, fieldName) {\r\n      if (!field) return;\r\n\t\/\/ Regular expression to match only characters (letters)\r\n\tvar regex = \/^[a-zA-Z]+$\/;\r\n\r\n\t\/\/ Test if the inputString matches the regex\r\n      if (regex.test(field)) {\r\n\t\treturn true;\r\n\t} else {\r\n\t\taddError(fieldName, \"Numbers aren't allowed\", \"Numbers aren't allowed\");\r\n\t\treturn false;\r\n\t}\r\n}\r\n\/\/=================================================================================\r\ntw.local.errorMessage != null\r\n\t? (tw.local.errorPanelVIS = \"EDITABLE\")\r\n\t: (tw.local.errorPanelVIS = \"NONE\");\r\n\/\/=================================================================================\r\n"]}},{"outgoing":["2027.894a9c37-37a6-4ce7-8330-51f32d9c3104"],"incoming":["2027.2f851fbb-9f5d-425c-8c61-6b8e8824bf64"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.894a9c37-37a6-4ce7-8330-51f32d9c3104"],"nodeVisualInfo":[{"width":24,"x":413,"y":43,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.c4acccc8-1c3e-4352-a1ad-35c4c1ca7866"},{"outgoing":["2027.2f851fbb-9f5d-425c-8c61-6b8e8824bf64","2027.34a4d91b-89f1-4dca-9ada-0b13a7a0991f"],"incoming":["2027.894a9c37-37a6-4ce7-8330-51f32d9c3104","2027.42f80e5f-8a4a-498c-b7a2-16fa8fed4964","2027.8352556c-701f-4ea2-8d56-22bcc5145630"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":350,"y":177,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":["tw.local.odcRequest.ChargesAndCommissions[0].description = \"First Line of support\"\r\n\r\ntw.local.odcRequest.ChargesAndCommissions[1] = {};\r\n\r\ntw.local.odcRequest.ChargesAndCommissions[1].description = \"Second Line of support\""]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"19529d9f-d64d-4039-8ebc-21c3c703807f","optionName":"@label","value":"12-Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"02e4b7d6-e49c-4a42-8694-12018bb66713","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9f1145e4-7662-48c5-803c-e9f26aefc819","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b12354aa-7d3b-43de-8a8f-d60d5f27d1d1","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4af5978d-5231-452a-81e1-7cb2c0d9bda6","optionName":"remittanceLetterPath","value":"tw.local.odcRequest.folderPath"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d6e8730f-99a2-4b09-8022-0b66ec7c6031","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6255287c-9a4b-4a28-8d89-d62b564dd643","optionName":"canCreate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"07f87978-83ef-45a4-8439-4f00d04d10af","optionName":"canDelete","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b370e808-fdeb-47db-85e0-4fa839351aba","optionName":"updateProperties","value":"tw.local.updateProp[]"}],"viewUUID":"64.3361b968-662d-491d-8e02-666b6b3648ec","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f312bea4-99cb-4f36-8500-6adfd3fed89e","version":"8550"},{"layoutItemId":"FC_Collections_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"31bd691c-bc6c-4922-87e0-da5c2cd0c0bd","optionName":"@label","value":"FC Collections CV"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"63459d65-96ea-4744-8e99-f3bce440944b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e5bc9c24-2aee-449c-80ff-455078eeeef6","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"d1f604ab-1894-4e5b-8161-c3e9f6869ef8","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"e2de5138-6d70-4421-8e61-808389c55a55"}],"layoutItemId":"Tab_section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"36d4c16f-7034-4280-8dfa-4e363ac5762e","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8d0e45d4-7e34-4d93-80b3-eac76cf21f04","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"eac9e22d-000e-470c-80ac-1cd4622827c5","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c86aa6fb-5135-4d26-847b-b77ada28c95e","optionName":"colorStyle","value":"P"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5795308b-8753-4682-841b-11a95a0b0db2","optionName":"tabsStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dc084704-aeef-4e51-8755-e76b6c6a340b","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a1a8942f-4fbc-40fd-82b9-94399f234244","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"REQUIRED\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"76689511-acd8-410f-8205-a211ec9d6524","optionName":"@className","value":""}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"b894011d-380b-4c74-8793-5c0e06f38172","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"ab9e2c92-571b-493d-8989-928a5b29f56b"}],"layoutItemId":"Vertical_layout1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"95409550-dade-4528-80dc-9ade3e5ff388","optionName":"@label","value":"Vertical layout"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d7e6247-8186-4d05-8a73-640dbcdef70e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7c2b46f5-1922-4bb3-86e4-8107f857ca0f","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"b9f2d117-ede7-430f-86f0-e165db9227a1","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"b123275f-32be-40f7-8964-dcd582a3b4b2"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f0b8d194-0a1f-45d3-8f4f-34ebe5ffc6c1","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"96a345f8-c4fb-496f-8e2b-1f58f8313985","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b5ad7167-5358-463d-8da9-2976a31ee15b","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d8ca6191-9bdf-46ea-839b-58c1419471f3","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5c2a322d-d3bd-4713-8af2-b5ca0f6b1d6c","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d56efbcd-53d5-4904-8052-338ddfcd2598","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b223b7ef-c501-4ff8-86c1-4fe33fbfaef8","optionName":"complianceApproval","value":"tw.local.odcRequest.complianceApproval"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fc55dc6b-dded-4a86-8b7d-de352988bd9e","optionName":"complianceApprovalVis","value":"Editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d86ce698-4634-4474-8d1f-fa4606c097fb","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a785a5d6-12d1-4c01-84cd-a8636a09dff8","optionName":"errorPanelVIS","value":"tw.local.errorPanelVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ce808352-b24e-4597-875b-35f7286e543a","optionName":"terminateReasonVIS","value":"tw.local.terminateReasonVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"716a06d8-b72a-42a3-8591-45075ac24750","optionName":"actionConditions","value":"tw.local.actionConditions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"18266a46-1d6a-4a78-80b2-cd8bce8be826","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f981c740-5023-4cff-8ef1-dfee089c4e0c","optionName":"tradeFoCommentVis","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7e0bdc50-45f8-4c66-85dd-bc1c71619202","optionName":"exeHubMkrCommentVis","value":"Editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b9dda471-477b-48f3-8bfe-55eabdcd41e2","optionName":"tradeFoComment","value":"tw.local.odcRequest.tradeFoComment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c3caa996-2687-40af-85f1-5e41da3e2a72","optionName":"exeHubMkrComment","value":"tw.local.odcRequest.exeHubMkrComment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"66adbefd-dd58-4e1b-8391-ff1095de4af6","optionName":"returnReasonVIS","value":"Editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f81c8642-b2d9-435a-86bf-36d81e314b14","optionName":"compcheckerComment","value":"tw.local.odcRequest.compcheckerComment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"be1a2922-4993-47ce-8983-91dc03c386a7","optionName":"compcheckerCommentVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cc7a297e-fab7-4382-80e3-d8ac819d0e37","optionName":"disableSubmit","value":"false"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"72bc594d-868e-42a4-89d4-01bb13108778","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Screen","isForCompensation":false,"completionQuantity":1,"dataChangeScript":"","id":"2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a"},{"targetRef":"2025.b942fed5-4b87-4dfc-8d5b-669f5b56415b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.6f265f51-afa4-48ef-8737-a2afa20c2f35","sourceRef":"2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb"},{"targetRef":"2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.15174c2b-d9a4-485c-8144-313f9245b2b0","sourceRef":"2025.eca5b84b-de97-4ab6-bdba-a8b9cb7bdc9e"},{"targetRef":"2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Screen","declaredType":"sequenceFlow","id":"2027.894a9c37-37a6-4ce7-8330-51f32d9c3104","sourceRef":"2025.c4acccc8-1c3e-4352-a1ad-35c4c1ca7866"},{"targetRef":"2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.42f80e5f-8a4a-498c-b7a2-16fa8fed4964","sourceRef":"897246e0-1ff4-4d8e-8856-f1065c4af670"},{"targetRef":"2025.c4acccc8-1c3e-4352-a1ad-35c4c1ca7866","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"60d64e04-001d-4515-9834-e7d4116fa8b5","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.2f851fbb-9f5d-425c-8c61-6b8e8824bf64","sourceRef":"2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a"},{"targetRef":"2025.eca5b84b-de97-4ab6-bdba-a8b9cb7bdc9e","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"aed447f0-6b9e-43bd-8d65-bd136cad5f16","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.34a4d91b-89f1-4dca-9ada-0b13a7a0991f","sourceRef":"2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a"},{"targetRef":"2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Screen","declaredType":"sequenceFlow","id":"2027.8352556c-701f-4ea2-8d56-22bcc5145630","sourceRef":"2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb"},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":547,"y":271,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Client-Side Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.35bb998e-c429-474a-85f9-432806e7434e","scriptFormat":"text\/x-javascript","script":{}},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].component = \"\";\nautoObject[0].defaultCurrency = {};\nautoObject[0].defaultCurrency.name = \"\";\nautoObject[0].defaultCurrency.value = \"\";\nautoObject[0].changeAmount = 0.0;\nautoObject[0].waiver = false;\nautoObject[0].debitedAccount = {};\nautoObject[0].debitedAccount.accountClass = {};\nautoObject[0].debitedAccount.accountClass.name = \"\";\nautoObject[0].debitedAccount.accountClass.value = \"\";\nautoObject[0].debitedAccount.glAccountNo = \"\";\nautoObject[0].debitedAccount.customerAccountNo = \"\";\nautoObject[0].debitedAccount.branchCode = \"\";\nautoObject[0].debitedAccount.currency = {};\nautoObject[0].debitedAccount.currency.name = \"\";\nautoObject[0].debitedAccount.currency.value = \"\";\nautoObject[0].debitedAccount.balance = 0.0;\nautoObject[0].debitedAccount.balanceSign = \"\";\nautoObject[0].debitedAccount.isOverDraft = false;\nautoObject[0].debitedAmount = {};\nautoObject[0].debitedAmount.standardExRate = 0.0;\nautoObject[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject[0].debitedAmount.amountInAccount = 0.0;\nautoObject[0].rateType = \"\";\nautoObject[0].description = \"\";\nautoObject[0].defaultPercentage = 0.0;\nautoObject[0].changePercentage = 0.0;\nautoObject[0].defaultAmount = 0.0;\nautoObject[0].basicAmountCurrency = \"\";\nautoObject[0].flatAmount = 0.0;\nautoObject[0].isGLFound = false;\nautoObject[0].glVerifyMSG = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061","name":"charges","isCollection":true,"declaredType":"dataObject","id":"2056.7a0d61e2-9b8a-44e6-8a93-3ad60859e232"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = [];\nautoObject[0] = {};\nautoObject[0].name = \"\";\nautoObject[0].value = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"updateProp","isCollection":true,"declaredType":"dataObject","id":"2056.ae5002f1-b15c-461d-8b50-773bcdefb5b9"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"62539589-4302-491b-93a0-2782389d5e1c","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"77c17cd1-09fb-4254-b09c-61feb0c4f96f","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"ACT04 - ODC Execution Hub Initiation 2","declaredType":"globalUserTask","id":"1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.c3af8f62-95a0-42bb-a4de-d76cb3f3190e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"compApprovalInit","isCollection":false,"id":"2055.4383f404-0c48-452d-990f-ae1e8e27821e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.e8a84586-d489-4e0c-b784-e8560cfa8076"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.31cfd076-ef2f-4737-85d1-a256beb27f48"},{"resourceBundleGroupID":"50.41101508-d2e4-4682-b3ef-b9b22266bb5a","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.217edee5-1f2d-4b8b-8502-8576e027daf4"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"bb759592-f2f7-40f3-8d2c-1489ab500ca9","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"a32c52e1-a276-4e4d-81c2-cd41ab5bc551","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"9cd57e59-b7e1-4482-8c4c-588e29e57de4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.769dc134-1d15-4dd4-a967-c5f61cf352dc","epvProcessLinkId":"2d23c808-1ed4-4142-8ddb-d0dcba7601f2","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"0632eb96-065b-4557-887c-01e27486455e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"3171034a-3c65-4e0d-8aee-052ffb8cbeba","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"016704f5-ec73-4162-9ebd-4c4cf6693554"}],"outputSet":[{"id":"37cdd4dc-0416-4277-adc7-d5dd4d7273c3"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.initiator = \"\";\nautoObject.requestNature = {};\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = {};\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new Date();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = {};\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = {};\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = {};\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = {};\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = {};\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = {};\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = {};\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = {};\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = [];\nautoObject.BasicDetails.Bills[0] = {};\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\nautoObject.BasicDetails.Invoice = [];\nautoObject.BasicDetails.Invoice[0] = {};\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\nautoObject.GeneratedDocumentInfo = {};\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = [];\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = {};\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = {};\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = {};\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = [];\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = {};\nautoObject.FcCollections.currency = {};\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new Date();\nautoObject.FcCollections.ToDate = new Date();\nautoObject.FcCollections.accountNo = {};\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = [];\nautoObject.FcCollections.retrievedTransactions[0] = {};\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = [];\nautoObject.FcCollections.selectedTransactions[0] = {};\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = [];\nautoObject.FcCollections.listOfAccounts[0] = {};\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = {};\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new Date();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = {};\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = [];\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = {};\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = {};\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = {};\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new Date();\nautoObject.ProductShipmentDetails.shipmentMethod = {};\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = {};\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = {};\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = {};\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = {};\nautoObject.ContractCreation.productCode = {};\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new Date();\nautoObject.ContractCreation.valueDate = new Date();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new Date();\nautoObject.Parties = {};\nautoObject.Parties.Drawer = {};\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = {};\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = {};\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = {};\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = {};\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.Parties.caseInNeed = {};\nautoObject.Parties.caseInNeed.partyCIF = \"\";\nautoObject.Parties.caseInNeed.partyId = \"\";\nautoObject.Parties.caseInNeed.partyName = \"\";\nautoObject.Parties.caseInNeed.country = \"\";\nautoObject.Parties.caseInNeed.language = \"\";\nautoObject.Parties.caseInNeed.refrence = \"\";\nautoObject.Parties.caseInNeed.address1 = \"\";\nautoObject.Parties.caseInNeed.address2 = \"\";\nautoObject.Parties.caseInNeed.address3 = \"\";\nautoObject.Parties.caseInNeed.partyType = {};\nautoObject.Parties.caseInNeed.partyType.name = \"\";\nautoObject.Parties.caseInNeed.partyType.value = \"\";\nautoObject.ChargesAndCommissions = [];\nautoObject.ChargesAndCommissions[0] = {};\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ChargesAndCommissions[0].isGLFound = false;\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\nautoObject.ContractLiquidation = {};\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new Date();\nautoObject.ContractLiquidation.creditValueDate = new Date();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.debitedAccountName = \"\";\nautoObject.ContractLiquidation.creditedAccount = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = {};\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = {};\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = [];\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = {};\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = {};\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = [];\nautoObject.attachmentDetails.attachment[0] = {};\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = [];\nautoObject.complianceComments[0] = {};\nautoObject.complianceComments[0].startTime = new Date();\nautoObject.complianceComments[0].endTime = new Date();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = [];\nautoObject.History[0] = {};\nautoObject.History[0].startTime = new Date();\nautoObject.History[0].endTime = new Date();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = {};\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject.folderPath = \"\";\nautoObject.templateDocID = \"\";\nautoObject.requestID = 0;\nautoObject.customerAndPartyAccountList = [];\nautoObject.customerAndPartyAccountList[0] = {};\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\nautoObject.customerAndPartyAccountList[0].frozen = false;\nautoObject.customerAndPartyAccountList[0].dormant = false;\nautoObject.customerAndPartyAccountList[0].noDebit = false;\nautoObject.customerAndPartyAccountList[0].noCredit = false;\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\nautoObject.tradeFoComment = \"\";\nautoObject.exeHubMkrComment = \"\";\nautoObject.compcheckerComment = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.211ff6ae-e163-4085-87a7-de8ccdbd9d3d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"regeneratedRemittanceLetterTitleVIS","isCollection":false,"id":"2055.0c656cf2-a9c3-46da-8d92-06744fffefeb"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"fromExeChecker","isCollection":false,"id":"2055.bcaeb4cc-7baf-4da3-bb06-3a5597ab0684"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"5f4ccdb1-7d6c-498e-8a0b-c13847752696"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.211ff6ae-e163-4085-87a7-de8ccdbd9d3d</processParameterId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>833daf8d-efb0-4d59-a246-0cd7cdba375c</guid>
            <versionId>b4debeba-bed8-4a1f-97cc-1809ad035fe6</versionId>
        </processParameter>
        <processParameter name="regeneratedRemittanceLetterTitleVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0c656cf2-a9c3-46da-8d92-06744fffefeb</processParameterId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f8631684-8ba5-48d6-a3e3-93271041a223</guid>
            <versionId>212ec893-f3df-4961-9bf2-cdfb7460499c</versionId>
        </processParameter>
        <processParameter name="fromExeChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bcaeb4cc-7baf-4da3-bb06-3a5597ab0684</processParameterId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2cd62391-98bf-4eb8-97ba-e3c2d11baf56</guid>
            <versionId>bc44f450-e151-40b3-9a12-943daf97f2f1</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c3af8f62-95a0-42bb-a4de-d76cb3f3190e</processParameterId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a69bfefe-2e2e-4f37-ba1a-d5761d41797d</guid>
            <versionId>a262a8fb-f4dd-4af0-ba47-36dd37c3d914</versionId>
        </processParameter>
        <processParameter name="compApprovalInit">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4383f404-0c48-452d-990f-ae1e8e27821e</processParameterId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d0508298-3ee7-4097-8ff6-7097b2c89c12</guid>
            <versionId>df5590d0-ec5b-4c88-b04b-396d31c729cc</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e8a84586-d489-4e0c-b784-e8560cfa8076</processParameterId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>46fe0b3d-ea60-453f-945e-e15a26d44e76</guid>
            <versionId>fdbf5179-251b-4f9d-95ec-d8ce7d69f1ae</versionId>
        </processParameter>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b31c88ee-60cd-49c6-a416-813a2b92f186</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c206c3fa-878a-4d8e-ba0d-237ad3b30ab0</guid>
            <versionId>e697ab31-f8c8-4425-92f9-369d9e925b47</versionId>
        </processVariable>
        <processVariable name="errorPanelVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.29254c11-4b82-47de-99bf-48ae28dde3da</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d3fa3c49-3f8e-47ca-986d-826f6723ba6c</guid>
            <versionId>e0c41730-6479-4006-9ff4-c415655a711a</versionId>
        </processVariable>
        <processVariable name="terminateReasonVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.81fedb58-c398-4515-ae09-86a82257376a</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b9e511b8-ef7e-457b-a441-1e82b2f53159</guid>
            <versionId>2bcd27af-19bf-493e-a626-d983e197161c</versionId>
        </processVariable>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cbc8bfdf-c314-477f-9f37-a30e923dea30</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3b63ba96-6faa-4683-9768-89f2352f6309</guid>
            <versionId>44329e80-1c5e-42f8-98aa-ec8643e2065c</versionId>
        </processVariable>
        <processVariable name="deliveryterms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7c792a93-f234-40ef-aa14-e2dc7216b19e</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b373ed37-5c84-47c9-92c9-e3ead69c6f4d</guid>
            <versionId>73f2c624-a4fe-4fc3-9328-ae5fb09d2f40</versionId>
        </processVariable>
        <processVariable name="paymentTerms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a1bf9dee-8f71-45ac-af5b-e99ad064620b</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a442311c-a6c7-4fce-87a4-2cb5a69305c1</guid>
            <versionId>8431460b-c2b6-4742-865f-4a0d3c43d084</versionId>
        </processVariable>
        <processVariable name="specialInstructions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8dcd31b9-9016-4ff0-9ce2-c3529cb6fb9d</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fd46903e-da9f-408f-ba2a-06a76f75f510</guid>
            <versionId>8e75973f-f39b-49b7-91c3-9b62d531554f</versionId>
        </processVariable>
        <processVariable name="instructions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1df3fe6c-0337-4290-b8f3-39ea8d85c6f4</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>156bb6c3-aa35-4487-a358-e608b3bf1330</guid>
            <versionId>3d3419df-b6ba-40c9-a75d-4937b83580d1</versionId>
        </processVariable>
        <processVariable name="flexCubeVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d276328e-57c1-4166-8957-c08c78f8e71f</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8c0814b5-31f8-4059-8073-4e0b96430da3</guid>
            <versionId>52ea8ce4-9fa9-4ee6-9bdd-85f300157fb0</versionId>
        </processVariable>
        <processVariable name="requestTypeVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cb706ead-28bf-435f-9261-652f4bfaa72a</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3993dbea-d8ec-47ae-951a-787c095ad46f</guid>
            <versionId>df87d884-dc39-4042-9a9f-05fa9a0390b4</versionId>
        </processVariable>
        <processVariable name="contractStageVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.********-0448-4189-8dde-a0179462f959</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>26d1ed6a-3d60-43a2-bf19-f9c379651a21</guid>
            <versionId>74e83879-e371-4470-8f74-7a26a888672f</versionId>
        </processVariable>
        <processVariable name="bankRefVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.db36bc77-aa3e-47f6-a593-341ef82cc497</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cf22e2e7-1142-4523-b828-f44abe1aa643</guid>
            <versionId>252917b0-43f3-417e-9fb6-e58f95918787</versionId>
        </processVariable>
        <processVariable name="event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4d42ba83-44c8-4e42-9754-0bfc72284a78</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a0faf0a0-db3a-462d-8f71-956166018b12</guid>
            <versionId>605c27e9-5b93-4da6-89d5-0df1951f95f7</versionId>
        </processVariable>
        <processVariable name="currencyList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.********-875a-4e58-8708-4fae1d30ee9a</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9c3125c4-1907-4449-af81-1cd04f376b7e</guid>
            <versionId>3efa5173-1995-4d5b-b1da-3646708494d2</versionId>
        </processVariable>
        <processVariable name="addchargeBtnVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ece208c0-ed2b-4966-a126-fa5405ec4a5b</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a77a5ab0-e40c-4e0c-9381-ec4fc8a153ad</guid>
            <versionId>2d85b64b-eb5a-452e-b5f5-514ea353381c</versionId>
        </processVariable>
        <processVariable name="collectingBankInfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.89708c1b-6b9d-41f2-bf2b-ee892cced83c</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b4b5a6f4-b969-49a2-b235-1848320727e7</guid>
            <versionId>71c49715-e3a8-4563-a4e4-e288a68d498e</versionId>
        </processVariable>
        <processVariable name="partyTypesInfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.********-d1aa-468a-bc48-e587ac5eeb31</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>33fcccbf-4618-49a8-93f1-f44826256d6c</guid>
            <versionId>2d664ea1-1f1b-4989-a4a7-06c8ff644298</versionId>
        </processVariable>
        <processVariable name="multiTenorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2f081e13-51c5-4be0-9294-062096f7fd08</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8d0d5beb-af5c-45fd-9306-f003f146374d</guid>
            <versionId>ec7e11ec-fe61-4c9e-b181-b909f5d3154d</versionId>
        </processVariable>
        <processVariable name="PartyAccountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d98726b8-6695-4b10-8803-943520ff4178</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c04be1a8-**************-ac6a4fe9070b</guid>
            <versionId>0c5bf2ee-da0e-41de-8e35-348c4adcd93a</versionId>
        </processVariable>
        <processVariable name="initialAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.55e6ae77-6817-41a2-b1dd-c8747c747bd0</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>70bfc11b-1470-46cd-9159-f3c41d38f8bd</guid>
            <versionId>4eb5401a-30c8-4deb-b98e-3e882ea7b11d</versionId>
        </processVariable>
        <processVariable name="parentRequestNumberVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ceb8495e-0120-49f7-9dbf-2a0e36592779</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2b812b25-77e7-4e9d-9147-accb6c2c8639</guid>
            <versionId>6a6699bb-b4fd-4e94-a7ab-0b34fa728667</versionId>
        </processVariable>
        <processVariable name="x">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cc511d7b-d4df-4103-bf78-29086509bb31</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7010890c-f470-4935-bcb8-02bf530bc526</guid>
            <versionId>411b6b2d-5c00-45e9-869f-fa2496a4d74b</versionId>
        </processVariable>
        <processVariable name="drawerAccountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.79a56e25-fe18-4e7b-9b6c-58618ad88e34</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fb8593a7-f953-4094-a410-30ccdfd299f2</guid>
            <versionId>f1c6835a-978d-47e0-a60e-1636f7c4b9ea</versionId>
        </processVariable>
        <processVariable name="selectedIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.75a0dbc0-85c6-4b92-8373-90e553ce7cda</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a8741a27-8be1-492b-a267-42c1009157f3</guid>
            <versionId>e4b51c97-afb8-4a5b-b0c5-8598f1ec3ce0</versionId>
        </processVariable>
        <processVariable name="fromCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a7a621de-c6a0-4d01-b413-2bb5081f1d8e</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>88ac22a4-a3f0-4ca4-9b85-fbc811bb1c70</guid>
            <versionId>ae00edb1-248a-418b-84a5-cadcd4c4ca48</versionId>
        </processVariable>
        <processVariable name="toCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fbcad2b2-b92b-464f-b9c7-f2239a54d7fa</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2b62392a-edf1-41fc-9f1d-36bbfb90f520</guid>
            <versionId>4e30aa2a-08f4-4581-a277-93a629ca2845</versionId>
        </processVariable>
        <processVariable name="rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7135a9c7-9a47-4e9b-a8c5-1a4271be6480</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b0d08d91-be5b-4124-93e6-f148bfd27179</guid>
            <versionId>af673ffb-5f00-44d9-81fe-c13e5e9910f1</versionId>
        </processVariable>
        <processVariable name="barCodePdf">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fc8ed296-8617-4d86-a64b-e0c3a129ed8e</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3ba124f3-633f-4801-a254-c095d34cb0ea</guid>
            <versionId>55965d57-1028-401e-8a32-b35ed7230635</versionId>
        </processVariable>
        <processVariable name="generationStatus">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.aca011e8-67f2-44f5-ae21-da53fc945cd6</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>29</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f5974072-8485-40a4-a7e1-14898ac38b46</guid>
            <versionId>76ca4a14-fc8a-4600-89b2-d750f76fe5b3</versionId>
        </processVariable>
        <processVariable name="calculatedChangeAmnt">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3eb7b6c6-dd5f-4eba-a979-64469e9fcae7</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>30</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4bafe9a6-5793-46db-b978-f41c432f9f10</guid>
            <versionId>313c02a6-a821-46d3-8107-561f050b3cfc</versionId>
        </processVariable>
        <processVariable name="customerAndPartyCifs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.99052b11-6ce0-4f34-b695-4c70ca8ae388</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>31</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a46ce600-50d1-4b83-905d-74f35fdbff42</guid>
            <versionId>e09a7a95-109e-424d-a706-336a0013ae09</versionId>
        </processVariable>
        <processVariable name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.53d49233-94d9-4e5a-8ba3-eaebba2fa02d</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>32</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e47e2476-41f9-4057-9bc2-5e622802db1a</guid>
            <versionId>7d051788-8f5f-4176-9d5c-3a7eb1cb6c9c</versionId>
        </processVariable>
        <processVariable name="addressBICList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e1661039-8d68-42ad-b899-7df882f745f1</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>33</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c074d6f1-639d-464c-af08-2ee45d59115f</guid>
            <versionId>c7bf5b50-33f2-4b3b-8b10-d8dcd1f33014</versionId>
        </processVariable>
        <processVariable name="partyTypeName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.425b6eb4-8146-4efc-b28f-d6c767a376a5</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>34</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6191fce0-659c-4040-89f9-ee2b068fe23e</guid>
            <versionId>04cba1c9-dbcf-438d-9729-3fb030f8e473</versionId>
        </processVariable>
        <processVariable name="todayDate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a596dbe0-9b59-4be4-b114-6670373fec7c</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>35</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>807e887c-e586-44e2-82b3-10975ace7f53</guid>
            <versionId>205e8ec3-87b3-461c-b2df-339f3e66ff80</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1d6a0c9d-d60a-4be6-bb42-2ed41259a098</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>36</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9dc78460-03df-49c9-9474-920d29a9a24f</guid>
            <versionId>aa42c927-978c-4422-b7c6-2a8c75f1f43e</versionId>
        </processVariable>
        <processVariable name="validLen">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.02f42280-6a65-45cd-8887-5d8076e25368</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>37</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7b78f517-eb35-457e-9743-3d17a501fd39</guid>
            <versionId>2b5edc50-5dcd-4d1a-8485-af03f1abbd42</versionId>
        </processVariable>
        <processVariable name="charges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7a0d61e2-9b8a-44e6-8a93-3ad60859e232</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>38</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.e3018bad-b453-4bf5-96fd-09a5141cd061</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6bc9063b-0739-44c3-93d0-4ea48ce26358</guid>
            <versionId>834ee33a-175b-4ea0-945d-9da4c9d01858</versionId>
        </processVariable>
        <processVariable name="updateProp">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ae5002f1-b15c-461d-8b50-773bcdefb5b9</processVariableId>
            <description isNull="true" />
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <namespace>2</namespace>
            <seq>39</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c8d0d953-6de7-4ae8-9156-d863a405f6b7</guid>
            <versionId>403a6d46-f0ae-4b0b-9148-def8bbb21dd4</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1af97ac0-dd77-44e4-a232-cc4b0abc55bd</processItemId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.6023c317-e473-4ffb-8ab2-302517d1f4b2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4646</guid>
            <versionId>802e4e34-e25d-42ad-a465-f60b81624b17</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1ef90d30-89dc-42d2-8946-eab1145f7899</processItemId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.15aa6163-635d-49c9-a5a7-a778521fadda</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:efd35478cc2d7b98:-17b49730:18d5ef3f1e7:-4648</guid>
            <versionId>d0c2ec17-6e53-46bf-b702-0824c9235cf4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.15aa6163-635d-49c9-a5a7-a778521fadda</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>50192046-3b7f-48b6-a6e4-f8b60b1474c2</guid>
                <versionId>b4c9f673-4111-4136-aaf0-28211feaae93</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.bc85c8b8-8a02-4ca9-9376-1cc39ede8ede</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>7be9f985-3fe9-4994-bf02-762678b74f6a</guid>
            <versionId>36ce484c-6fdb-413f-b428-469d92553434</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.83ce9618-8fc9-4079-b99b-7bc30fcea1ed</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>df069265-2b55-44c1-8857-b0b801ce1dbe</guid>
            <versionId>838085b0-d584-4ce4-a8e5-9d93f50442f4</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.03d8edcf-0257-42e5-bc4a-549a7d016dca</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>40e4e11b-f9d6-4b2a-979e-f462553b2d54</guid>
            <versionId>84e7c1d5-db38-45b3-a223-c978bfe834ce</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.bc7ce280-dbf3-4a97-81ca-76921321d3ca</epvProcessLinkId>
            <epvId>/21.769dc134-1d15-4dd4-a967-c5f61cf352dc</epvId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>1d517635-beff-486b-a5e0-f2b63d73a16d</guid>
            <versionId>aec5c2f1-0ef6-4302-a587-53ea59bba5e2</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.3cb81393-e338-4b16-9962-04c5a3453321</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>52f3e725-5fdb-4300-affe-69f1bfb86e9c</guid>
            <versionId>e1d58dcc-3e1e-42b6-aebb-d92885d47777</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.fadf1bac-ecec-470f-b835-07e559aa5168</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>8417b5c2-7cbf-4cb2-a082-1489e2b7deb1</guid>
            <versionId>f0321f17-2151-40d3-9a75-ec9c3399dcb7</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.cecdcb6a-da45-4882-9850-9496185b3480</resourceProcessLinkId>
            <resourceBundleGroupId>/50.41101508-d2e4-4682-b3ef-b9b22266bb5a</resourceBundleGroupId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>4a4dc65b-ceee-4940-97cf-2a9081aeb201</guid>
            <versionId>87012348-ae25-4942-b405-170342b10750</versionId>
        </RESOURCE_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.acb7abff-bc01-4205-94db-923a49463e3a</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <guid>eef805a9-4b10-4c22-b078-c59b8de859c2</guid>
            <versionId>ba8fc0a8-99fa-4395-9de1-1953c3d132b3</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.1af97ac0-dd77-44e4-a232-cc4b0abc55bd</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="5f4ccdb1-7d6c-498e-8a0b-c13847752696" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns16:globalUserTask name="ACT04 - ODC Execution Hub Initiation 2" id="1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e">
                    <ns16:documentation />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation id="77c17cd1-09fb-4254-b09c-61feb0c4f96f">
                            <ns16:startEvent name="Start" id="897246e0-1ff4-4d8e-8856-f1065c4af670">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="203" y="200" width="24" height="24" color="#F8F8F8" />
                                    <ns3:postAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.42f80e5f-8a4a-498c-b7a2-16fa8fed4964</ns16:outgoing>
                            </ns16:startEvent>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.b31c88ee-60cd-49c6-a416-813a2b92f186" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorPanelVIS" id="2056.29254c11-4b82-47de-99bf-48ae28dde3da" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="terminateReasonVIS" id="2056.81fedb58-c398-4515-ae09-86a82257376a" />
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.cbc8bfdf-c314-477f-9f37-a30e923dea30" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="deliveryterms" id="2056.7c792a93-f234-40ef-aa14-e2dc7216b19e" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="paymentTerms" id="2056.a1bf9dee-8f71-45ac-af5b-e99ad064620b" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="specialInstructions" id="2056.8dcd31b9-9016-4ff0-9ce2-c3529cb6fb9d" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instructions" id="2056.1df3fe6c-0337-4290-b8f3-39ea8d85c6f4" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="flexCubeVIS" id="2056.d276328e-57c1-4166-8957-c08c78f8e71f" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestTypeVIS" id="2056.cb706ead-28bf-435f-9261-652f4bfaa72a">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="contractStageVIS" id="2056.********-0448-4189-8dde-a0179462f959" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="bankRefVIS" id="2056.db36bc77-aa3e-47f6-a593-341ef82cc497" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="event" id="2056.4d42ba83-44c8-4e42-9754-0bfc72284a78" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="currencyList" id="2056.********-875a-4e58-8708-4fae1d30ee9a" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="addchargeBtnVIS" id="2056.ece208c0-ed2b-4966-a126-fa5405ec4a5b" />
                            <ns16:dataObject itemSubjectRef="itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a" isCollection="false" name="collectingBankInfo" id="2056.89708c1b-6b9d-41f2-bf2b-ee892cced83c">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.cif = "";
autoObject.customerName = "";
autoObject.addressLine1 = "";
autoObject.addressLine2 = "";
autoObject.addressLine3 = "";
autoObject.customerSector = {};
autoObject.customerSector.name = "";
autoObject.customerSector.value = "";
autoObject.customerType = "";
autoObject.customerNoCBE = "";
autoObject.facilityType = {};
autoObject.facilityType.name = "";
autoObject.facilityType.value = "";
autoObject.commercialRegistrationNo = "";
autoObject.commercialRegistrationOffice = "";
autoObject.taxCardNo = "";
autoObject.importCardNo = "";
autoObject.initiationHub = "";
autoObject.country = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a" isCollection="false" name="partyTypesInfo" id="2056.********-d1aa-468a-bc48-e587ac5eeb31">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.cif = "";
autoObject.customerName = "";
autoObject.addressLine1 = "";
autoObject.addressLine2 = "";
autoObject.addressLine3 = "";
autoObject.customerSector = {};
autoObject.customerSector.name = "";
autoObject.customerSector.value = "";
autoObject.customerType = "";
autoObject.customerNoCBE = "";
autoObject.facilityType = {};
autoObject.facilityType.name = "";
autoObject.facilityType.value = "";
autoObject.commercialRegistrationNo = "";
autoObject.commercialRegistrationOffice = "";
autoObject.taxCardNo = "";
autoObject.importCardNo = "";
autoObject.initiationHub = "";
autoObject.country = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="multiTenorVIS" id="2056.2f081e13-51c5-4be0-9294-062096f7fd08" />
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="PartyAccountsList" id="2056.d98726b8-6695-4b10-8803-943520ff4178" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="initialAccountList" id="2056.55e6ae77-6817-41a2-b1dd-c8747c747bd0" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentRequestNumberVIS" id="2056.ceb8495e-0120-49f7-9dbf-2a0e36592779" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="x" id="2056.cc511d7b-d4df-4103-bf78-29086509bb31" />
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="drawerAccountsList" id="2056.79a56e25-fe18-4e7b-9b6c-58618ad88e34" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="selectedIndex" id="2056.75a0dbc0-85c6-4b92-8373-90e553ce7cda" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="fromCurrency" id="2056.a7a621de-c6a0-4d01-b413-2bb5081f1d8e" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="toCurrency" id="2056.fbcad2b2-b92b-464f-b9c7-f2239a54d7fa" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="rate" id="2056.7135a9c7-9a47-4e9b-a8c5-1a4271be6480" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="barCodePdf" id="2056.fc8ed296-8617-4d86-a64b-e0c3a129ed8e" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="generationStatus" id="2056.aca011e8-67f2-44f5-ae21-da53fc945cd6" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="calculatedChangeAmnt" id="2056.3eb7b6c6-dd5f-4eba-a979-64469e9fcae7" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="customerAndPartyCifs" id="2056.99052b11-6ce0-4f34-b695-4c70ca8ae388" />
                            <ns16:dataObject itemSubjectRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651" isCollection="false" name="customerFullDetails" id="2056.53d49233-94d9-4e5a-8ba3-eaebba2fa02d" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="addressBICList" id="2056.e1661039-8d68-42ad-b899-7df882f745f1" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="partyTypeName" id="2056.425b6eb4-8146-4efc-b28f-d6c767a376a5" />
                            <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="todayDate" id="2056.a596dbe0-9b59-4be4-b114-6670373fec7c">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">new Date()</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.1d6a0c9d-d60a-4be6-bb42-2ed41259a098" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="validLen" id="2056.02f42280-6a65-45cd-8887-5d8076e25368" />
                            <ns16:endEvent name="End" id="2025.b942fed5-4b87-4dfc-8d5b-669f5b56415b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="813" y="201" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.6f265f51-afa4-48ef-8737-a2afa20c2f35</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:exclusiveGateway default="2027.6f265f51-afa4-48ef-8737-a2afa20c2f35" gatewayDirection="Unspecified" name="isValid" id="2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="686" y="195" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.15174c2b-d9a4-485c-8144-313f9245b2b0</ns16:incoming>
                                <ns16:outgoing>2027.6f265f51-afa4-48ef-8737-a2afa20c2f35</ns16:outgoing>
                                <ns16:outgoing>2027.8352556c-701f-4ea2-8d56-22bcc5145630</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.15174c2b-d9a4-485c-8144-313f9245b2b0" name="Validation" id="2025.eca5b84b-de97-4ab6-bdba-a8b9cb7bdc9e">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="547" y="176" width="95" height="70" color="#95D087" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.34a4d91b-89f1-4dca-9ada-0b13a7a0991f</ns16:incoming>
                                <ns16:outgoing>2027.15174c2b-d9a4-485c-8144-313f9245b2b0</ns16:outgoing>
                                <ns16:script>tw.local.errorMessage = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0;&#xD;
tw.local.invalidTabs = [];&#xD;
tw.system.coachValidation.clearValidationErrors();&#xD;
&#xD;
////-------------------------------------------BASIC DETAILS VALIDATION -----------------------------------&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.commodityDescription,&#xD;
	"tw.local.odcRequest.BasicDetails.commodityDescription"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo,&#xD;
	"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate,&#xD;
	"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef,&#xD;
	"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate,&#xD;
	"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate"&#xD;
);&#xD;
&#xD;
maxLength(&#xD;
	tw.local.odcRequest.BasicDetails.flexCubeContractNo,&#xD;
	"tw.local.odcRequest.BasicDetails.flexCubeContractNo",&#xD;
	16,&#xD;
	tw.resource.ValidationMessages.MaxLength16,&#xD;
	"Flex Cube Contract Number: " + tw.resource.ValidationMessages.MaxLength16&#xD;
);&#xD;
//add mess 160 to local file&#xD;
maxLength(&#xD;
	tw.local.odcRequest.BasicDetails.commodityDescription,&#xD;
	"tw.local.odcRequest.BasicDetails.commodityDescription",&#xD;
	160,&#xD;
	"Shouldn't be more than 160 character",&#xD;
	"Commodity Description: " + "Shouldn't be more than 160 character"&#xD;
);&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {&#xD;
	maxLength(&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,&#xD;
		"tw.local.odcRequest.BasicDetails.Invoice[" + i + "].invoiceNo",&#xD;
		20,&#xD;
		"Shouldn't be more than 20 character",&#xD;
		"invoice Number: " + "Shouldn't be more than 20 character"&#xD;
	);&#xD;
}&#xD;
&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate,&#xD;
			"tw.local.odcRequest.BasicDetails.Invoice[" + i + "].invoiceDate"&#xD;
		);&#xD;
	}&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,&#xD;
			"tw.local.odcRequest.BasicDetails.Invoice[" + i + "].invoiceNo"&#xD;
		);&#xD;
	}&#xD;
}&#xD;
&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Bills.length; i++) {&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate,&#xD;
			"tw.local.odcRequest.BasicDetails.Bills[" + i + "].billOfLadingDate"&#xD;
		);&#xD;
	}&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef,&#xD;
			"tw.local.odcRequest.BasicDetails.Bills[" + i + "].billOfLadingRef"&#xD;
		);&#xD;
	}&#xD;
}&#xD;
validateTab(0, "Basic Details Tab");&#xD;
//-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------&#xD;
mandatory(&#xD;
	tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate,&#xD;
	"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate"&#xD;
);&#xD;
&#xD;
minLength(&#xD;
	tw.local.odcRequest.FinancialDetailsBR.amountAdvanced,&#xD;
	"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced",&#xD;
	2,&#xD;
	"Shouldn't be less than 14 character",&#xD;
	"Amount Advanced: Shouldn't be less than 2 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.FinancialDetailsBR.amountAdvanced,&#xD;
	"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced",&#xD;
	14,&#xD;
	"Shouldn't be more than 14 character",&#xD;
	"Amount Advanced:" + "Shouldn't be more than 14 character"&#xD;
);&#xD;
validateTab(3, "Financial Details - Branch Tab");&#xD;
//-------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------&#xD;
if (tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0) {&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.currency.name,&#xD;
		"tw.local.odcRequest.FcCollections.currency"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.negotiatedExchangeRate,&#xD;
		"tw.local.odcRequest.FcCollections.negotiatedExchangeRate"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.fromDate,&#xD;
		"tw.local.odcRequest.FcCollections.fromDate"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.ToDate,&#xD;
		"tw.local.odcRequest.FcCollections.ToDate"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.accountNo.value,&#xD;
		"tw.local.odcRequest.FcCollections.accountNo"&#xD;
	);&#xD;
&#xD;
	if (&#xD;
		tw.local.odcRequest.FcCollections != null &amp;&amp;&#xD;
		tw.local.odcRequest.FcCollections.negotiatedExchangeRate != null&#xD;
	) {&#xD;
		minLength(&#xD;
			tw.local.odcRequest.FcCollections.negotiatedExchangeRate,&#xD;
			"tw.local.odcRequest.FcCollections.negotiatedExchangeRate",&#xD;
			6,&#xD;
			"Shouldn't be less than 6 character",&#xD;
			" Negotiated Exchange Rate: Shouldn't be less than 6 character"&#xD;
		);&#xD;
		maxLength(&#xD;
			tw.local.odcRequest.FcCollections.negotiatedExchangeRate,&#xD;
			"tw.local.odcRequest.FcCollections.negotiatedExchangeRate",&#xD;
			10,&#xD;
			"Shouldn't be more than 10 character",&#xD;
			" Negotiated Exchange Rate:" + "Shouldn't be more than 10 character"&#xD;
		);&#xD;
	}&#xD;
&#xD;
	validateTab(4, "Flexcube collections Tab");&#xD;
}&#xD;
//-----------------------------------------Financial Details - Trade FO VALIDATION ----------------------------&#xD;
&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.discount,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.discount"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.extraCharges,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.extraCharges"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.ourCharges,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.ourCharges"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountSight,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.amountSight"&#xD;
);&#xD;
&#xD;
if (tw.local.odcRequest.BasicDetails.paymentTerms.name != "001") {&#xD;
	if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length == 0) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.FinancialDetailsFO.multiTenorDates,&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates"&#xD;
		);&#xD;
		tw.local.errorMessage +=&#xD;
			"&lt;li&gt;" + "Fill in at least one entry in Multi Tenordates" + "&lt;/li&gt;";&#xD;
	}&#xD;
}&#xD;
&#xD;
var sum = 0;&#xD;
&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length; i++) {&#xD;
	if (&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == "" ||&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == null&#xD;
	) {&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[" + i + "].amount",&#xD;
			"Mandatory field"&#xD;
		);&#xD;
	} else if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount != "") {&#xD;
		checkNegativeValue(&#xD;
			tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount,&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[" + i + "].amount"&#xD;
		);&#xD;
	}&#xD;
	if (&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == "" ||&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == null&#xD;
	) {&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[" + i + "].date",&#xD;
			"Mandatory field"&#xD;
		);&#xD;
	}&#xD;
	sum = sum + tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount;&#xD;
}&#xD;
&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.collectableAmount,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.collectableAmount"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization"&#xD;
);&#xD;
&#xD;
if (&#xD;
	!!tw.local.odcRequest.FinancialDetailsFO.collectableAmount &amp;&amp;&#xD;
	tw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; 0&#xD;
)&#xD;
	if (&#xD;
		tw.local.odcRequest.FinancialDetailsFO.collectableAmount != null &amp;&amp;&#xD;
		tw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; sum &amp;&amp;&#xD;
		tw.local.odcRequest.BasicDetails.paymentTerms.name != "001"&#xD;
	) {&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.collectableAmount",&#xD;
			"Sum of all Installment Amounts in a request must be &lt;= Amount Collectable by NBE (اجمالى المبالغ المطلوب تحصيلها)"&#xD;
		);&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length -1 ].date",&#xD;
			"Mandatory field"&#xD;
		);&#xD;
	}&#xD;
&#xD;
if (&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization &gt; 0 &amp;&amp;&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization &gt; 0&#xD;
) {&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization",&#xD;
		"Partial Avalization isn't allowed"&#xD;
	);&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization",&#xD;
		"Partial Avalization isn't allowed"&#xD;
	);&#xD;
}&#xD;
var sumAvalization = parseFloat(0);&#xD;
sumAvalization =&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization +&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization +&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountSight;&#xD;
if (sumAvalization != tw.local.odcRequest.FinancialDetailsFO.collectableAmount) {&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization",&#xD;
		"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization"&#xD;
	);&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization",&#xD;
		"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization"&#xD;
	);&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountSight",&#xD;
		"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization"&#xD;
	);&#xD;
}&#xD;
mandatory(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.maturityDate,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.maturityDate"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity"&#xD;
);&#xD;
&#xD;
//--------------------------------------------Importer Details VALIDATION ----------------------------------&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.importerName,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerName"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.importerAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerAddress"&#xD;
);&#xD;
mandatory(tw.local.odcRequest.ImporterDetails.bank, "tw.local.odcRequest.ImporterDetails.bank");&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.BICCode,&#xD;
	"tw.local.odcRequest.ImporterDetails.BICCode"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.bankAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.bankAddress"&#xD;
);&#xD;
&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.importerName,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerName",&#xD;
	250,&#xD;
	"Shouldn't be more than 250 character",&#xD;
	"Importer Name:" + "Shouldn't be more than 250 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.importerAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerAddress",&#xD;
	400,&#xD;
	"Shouldn't be more than 400 character",&#xD;
	"Importer Detailed Address:" + "Shouldn't be more than 400 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.importerPhoneNo,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerPhoneNo",&#xD;
	20,&#xD;
	"Shouldn't be more than 20 character",&#xD;
	"Importer Telephone No.:" + "Shouldn't be more than 20 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.bank,&#xD;
	"tw.local.odcRequest.ImporterDetails.bank",&#xD;
	250,&#xD;
	"Shouldn't be more than 250 character",&#xD;
	"Importer Bank:" + "Shouldn't be more than 250 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.BICCode,&#xD;
	"tw.local.odcRequest.ImporterDetails.BICCode",&#xD;
	11,&#xD;
	"Shouldn't be more than 11 character",&#xD;
	"Importer Bank BIC Code:" + "Shouldn't be more than 11 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.ibanAccount,&#xD;
	"tw.local.odcRequest.ImporterDetails.ibanAccount",&#xD;
	40,&#xD;
	"Shouldn't be more than 40 character",&#xD;
	"Importer Account(IBAN):" + "Shouldn't be more than 40 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.bankAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.bankAddress",&#xD;
	400,&#xD;
	"Shouldn't be more than 400 character",&#xD;
	"Importer Bank Detailed Address:" + "Shouldn't be more than 400 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.bankPhoneNo,&#xD;
	"tw.local.odcRequest.ImporterDetails.bankPhoneNo",&#xD;
	20,&#xD;
	"Shouldn't be more than 20 character",&#xD;
	"Importer Bank Telephone No.:" + "Shouldn't be more than 20 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.collectingBankReference,&#xD;
	"tw.local.odcRequest.ImporterDetails.collectingBankReference",&#xD;
	30,&#xD;
	"Shouldn't be more than 30 character",&#xD;
	"Collecting Bank Reference:" + "Shouldn't be more than 30 character"&#xD;
);&#xD;
validateTab(6, "Importer Details Tab");&#xD;
&#xD;
//-----------------------------------------Contract Creation VALIDATION -------------------------------------&#xD;
&#xD;
&#xD;
validateString(&#xD;
	tw.local.odcRequest.ContractCreation.userReference,&#xD;
	"tw.local.odcRequest.ContractCreation.userReference"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ContractCreation.userReference,&#xD;
	"tw.local.odcRequest.ContractCreation.userReference"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ContractCreation.userReference,&#xD;
	"tw.local.odcRequest.ContractCreation.userReference",&#xD;
	16,&#xD;
	"Shouldn't be more than 16 character",&#xD;
	"Contract Creation User Reference" + "Shouldn't be more than 16 character"&#xD;
);&#xD;
&#xD;
validateTab(8, "Contract Creation Tab");&#xD;
//---------------------------------------------//Charges and Commissions VALIDATION -------------------------------------&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.ChargesAndCommissions.length; i++) {&#xD;
	//Description - Flat Amount&#xD;
	if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == "Flat Amount") {&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &lt; 0 ||&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null&#xD;
		) {&#xD;
			addError(&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changeAmount",&#xD;
				"Must be &gt;= 0"&#xD;
			);&#xD;
		} else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0) {&#xD;
			validateDecimal2(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].changeAmount,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changeAmount",&#xD;
				"Must be Decimal(14,2)"&#xD;
			);&#xD;
		}&#xD;
&#xD;
		//Fixed Rate&#xD;
	} else {&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changePercentage &lt; 0 ||&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null&#xD;
		) {&#xD;
			addError(&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changePercentage",&#xD;
				"Must be &gt;= 0"&#xD;
			);&#xD;
		} else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage &gt; 0) {&#xD;
			validateDecimal2(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].changePercentage,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changePercentage",&#xD;
				"Must be Decimal(14,2)"&#xD;
			);&#xD;
		}&#xD;
	}&#xD;
&#xD;
	//skip validation if waiver or changeAmnt &lt; 0&#xD;
	if (&#xD;
		tw.local.odcRequest.ChargesAndCommissions[i].waiver == false &amp;&amp;&#xD;
		tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0&#xD;
	) {&#xD;
		//GL Account&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value ==&#xD;
			"GL Account"&#xD;
		) {&#xD;
			if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {&#xD;
				addError(&#xD;
					"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
						i +&#xD;
						"].debitedAccount.glAccountNo",&#xD;
					"GL Account Not Verified"&#xD;
				);&#xD;
			}&#xD;
&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.glAccountNo"&#xD;
			);&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.currency.value"&#xD;
			);&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.branchCode"&#xD;
			);&#xD;
&#xD;
			//Customer Account&#xD;
		} else {&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount&#xD;
					.customerAccountNo,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.customerAccountNo"&#xD;
			);&#xD;
		}&#xD;
&#xD;
		//DebitedAmount&#xD;
		if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate &gt; 0) {&#xD;
			validateDecimal(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAmount.negotiatedExRate",&#xD;
				"Must be Decimal(16,10)"&#xD;
			);&#xD;
		}&#xD;
&#xD;
		//Correct Validation but Waiting confirmation on what to do if GL account&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount &gt;&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance &amp;&amp;&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false&#xD;
		) {&#xD;
			addError(&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAmount.amountInAccount",&#xD;
				"ERROR: Must be &lt;= Account Balance"&#xD;
			);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
validateTab(9, "Charges and commissions Tab");&#xD;
//---------------------------------------------//Parties VALIDATION -------------------------------------&#xD;
/////Drawer Section&#xD;
mandatory(tw.local.odcRequest.Parties.Drawer.partyId, "tw.local.odcRequest.Parties.Drawer.partyId");&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.Drawer.partyName,&#xD;
	"tw.local.odcRequest.Parties.Drawer.partyName"&#xD;
);&#xD;
&#xD;
/////Drawee Section&#xD;
mandatory(tw.local.odcRequest.Parties.Drawee.partyId, "tw.local.odcRequest.Parties.Drawee.partyId");&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.Drawee.partyName,&#xD;
	"tw.local.odcRequest.Parties.Drawee.partyName"&#xD;
);&#xD;
&#xD;
/////Parties Types (Accountee)&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.partyTypes.partyId,&#xD;
	"tw.local.odcRequest.Parties.partyTypes.partyId"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.partyTypes.partyName,&#xD;
	"tw.local.odcRequest.Parties.partyTypes.partyName"&#xD;
);&#xD;
&#xD;
validateTab(10, "Parties Tab");&#xD;
//---------------------------------------------------------------------------------&#xD;
function addError(fieldName, controlMessage, validationMessage, fromMandatory) {&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered&#xD;
		? ""&#xD;
		: (tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;");&#xD;
}&#xD;
&#xD;
function minLength(field, fieldName, len, controlMessage, validationMessage) {&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len) {&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function maxLength(field, fieldName, len, controlMessage, validationMessage) {&#xD;
	if (field.length &gt; len) {&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function mandatory(field, fieldName, message) {&#xD;
	if (!message) {&#xD;
		message = camelCaseToTitle(fieldName) + " is Mandatory";&#xD;
	}&#xD;
&#xD;
	if (field == null || field == undefined) {&#xD;
		addError(fieldName, message, message, true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	} else {&#xD;
		switch (typeof field) {&#xD;
			case "string":&#xD;
				if (&#xD;
					field.trim() != undefined &amp;&amp;&#xD;
					field.trim() != null &amp;&amp;&#xD;
					field.trim().length == 0&#xD;
				) {&#xD;
					addError(fieldName, message, message, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0) {&#xD;
					addError(fieldName, message, message, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				if (field &lt; 0) {&#xD;
					var msg = "Invalid Value, This field can not be negative value.";&#xD;
					addError(fieldName, msg, msg, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
&#xD;
			default:&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if (field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime())) {&#xD;
				} else {&#xD;
					addError(fieldName, message, message, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function sumDates(fromDate, toDate, fieldName, controlMessage, validationMessage) {&#xD;
	if (toDate - fromDate == 0) {&#xD;
		return true;&#xD;
	}&#xD;
	addError(fieldName, controlMessage, validationMessage);&#xD;
	return false;&#xD;
}&#xD;
//=========================================================&#xD;
function checkNegativeValue(field, fieldName) {&#xD;
	if (field &lt; 0) {&#xD;
		var msg = "Invalid Value, This field can not be negative value.";&#xD;
		addError(fieldName, msg, msg, true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function validateDecimal(field, fieldName, controlMessage, validationMessage) {&#xD;
	var decimalPattern = /^\d{1,4}(\.\d{1,6})?$/;&#xD;
	if (!decimalPattern.test(field)) {&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	} else {&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
//========================================================================================&#xD;
function validateTab(index, tabName) {&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength) {&#xD;
		if (tw.local.errorMessage.length == 0) {&#xD;
			tw.local.errorMessage +=&#xD;
				"&lt;p&gt;" + "Please complete fields in the following tabs:" + "&lt;/p&gt;";&#xD;
		}&#xD;
		tw.local.errorMessage += "&lt;li&gt;" + tabName + "&lt;/li&gt;";&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}&#xD;
}&#xD;
//==============================================================&#xD;
function validateDecimal2(field, fieldName, controlMessage, validationMessage) {&#xD;
	var decimalPattern = /^\d{1,12}(\.\d{1,2})?$/;&#xD;
	if (!decimalPattern.test(field)) {&#xD;
		// Decimal is valid&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	} else {&#xD;
		// Decimal is invalid&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
function camelCaseToTitle(camelCase) {&#xD;
	var fieldName = camelCase.split(".").pop();&#xD;
	// Convert camelCase to Title Case&#xD;
	var titleCase = fieldName.replace(/([A-Z])/g, " $1");&#xD;
	// Uppercase the first character&#xD;
	titleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);&#xD;
	return titleCase;&#xD;
}&#xD;
&#xD;
&#xD;
function validateString(field, fieldName) {&#xD;
      if (!field) return;&#xD;
	// Regular expression to match only characters (letters)&#xD;
	var regex = /^[a-zA-Z]+$/;&#xD;
&#xD;
	// Test if the inputString matches the regex&#xD;
      if (regex.test(field)) {&#xD;
		return true;&#xD;
	} else {&#xD;
		addError(fieldName, "Numbers aren't allowed", "Numbers aren't allowed");&#xD;
		return false;&#xD;
	}&#xD;
}&#xD;
//=================================================================================&#xD;
tw.local.errorMessage != null&#xD;
	? (tw.local.errorPanelVIS = "EDITABLE")&#xD;
	: (tw.local.errorPanelVIS = "NONE");&#xD;
//=================================================================================&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.c4acccc8-1c3e-4352-a1ad-35c4c1ca7866">
                                <ns16:extensionElements>
                                    <ns3:default>2027.894a9c37-37a6-4ce7-8330-51f32d9c3104</ns3:default>
                                    <ns13:nodeVisualInfo x="413" y="43" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.2f851fbb-9f5d-425c-8c61-6b8e8824bf64</ns16:incoming>
                                <ns16:outgoing>2027.894a9c37-37a6-4ce7-8330-51f32d9c3104</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Screen" id="2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="350" y="177" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns3:preAssignmentScript>tw.local.odcRequest.ChargesAndCommissions[0].description = "First Line of support"&#xD;
&#xD;
tw.local.odcRequest.ChargesAndCommissions[1] = {};&#xD;
&#xD;
tw.local.odcRequest.ChargesAndCommissions[1].description = "Second Line of support"</ns3:preAssignmentScript>
                                    <ns3:postAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.894a9c37-37a6-4ce7-8330-51f32d9c3104</ns16:incoming>
                                <ns16:incoming>2027.42f80e5f-8a4a-498c-b7a2-16fa8fed4964</ns16:incoming>
                                <ns16:incoming>2027.8352556c-701f-4ea2-8d56-22bcc5145630</ns16:incoming>
                                <ns16:outgoing>2027.2f851fbb-9f5d-425c-8c61-6b8e8824bf64</ns16:outgoing>
                                <ns16:outgoing>2027.34a4d91b-89f1-4dca-9ada-0b13a7a0991f</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>72bc594d-868e-42a4-89d4-01bb13108778</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>f0b8d194-0a1f-45d3-8f4f-34ebe5ffc6c1</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>96a345f8-c4fb-496f-8e2b-1f58f8313985</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b5ad7167-5358-463d-8da9-2976a31ee15b</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d8ca6191-9bdf-46ea-839b-58c1419471f3</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>5c2a322d-d3bd-4713-8af2-b5ca0f6b1d6c</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d56efbcd-53d5-4904-8052-338ddfcd2598</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b223b7ef-c501-4ff8-86c1-4fe33fbfaef8</ns19:id>
                                                    <ns19:optionName>complianceApproval</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.complianceApproval</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>fc55dc6b-dded-4a86-8b7d-de352988bd9e</ns19:id>
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    <ns19:value>Editable</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d86ce698-4634-4474-8d1f-fa4606c097fb</ns19:id>
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a785a5d6-12d1-4c01-84cd-a8636a09dff8</ns19:id>
                                                    <ns19:optionName>errorPanelVIS</ns19:optionName>
                                                    <ns19:value>tw.local.errorPanelVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>ce808352-b24e-4597-875b-35f7286e543a</ns19:id>
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    <ns19:value>tw.local.terminateReasonVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>716a06d8-b72a-42a3-8591-45075ac24750</ns19:id>
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>18266a46-1d6a-4a78-80b2-cd8bce8be826</ns19:id>
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>f981c740-5023-4cff-8ef1-dfee089c4e0c</ns19:id>
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    <ns19:value>Readonly</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>7e0bdc50-45f8-4c66-85dd-bc1c71619202</ns19:id>
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    <ns19:value>Editable</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b9dda471-477b-48f3-8bfe-55eabdcd41e2</ns19:id>
                                                    <ns19:optionName>tradeFoComment</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.tradeFoComment</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c3caa996-2687-40af-85f1-5e41da3e2a72</ns19:id>
                                                    <ns19:optionName>exeHubMkrComment</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.exeHubMkrComment</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>66adbefd-dd58-4e1b-8391-ff1095de4af6</ns19:id>
                                                    <ns19:optionName>returnReasonVIS</ns19:optionName>
                                                    <ns19:value>Editable</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>f81c8642-b2d9-435a-86bf-36d81e314b14</ns19:id>
                                                    <ns19:optionName>compcheckerComment</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.compcheckerComment</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>be1a2922-4993-47ce-8983-91dc03c386a7</ns19:id>
                                                    <ns19:optionName>compcheckerCommentVIS</ns19:optionName>
                                                    <ns19:value>Readonly</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>cc7a297e-fab7-4382-80e3-d8ac819d0e37</ns19:id>
                                                    <ns19:optionName>disableSubmit</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>b123275f-32be-40f7-8964-dcd582a3b4b2</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>b9f2d117-ede7-430f-86f0-e165db9227a1</ns19:id>
                                                        <ns19:layoutItemId>Vertical_layout1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>95409550-dade-4528-80dc-9ade3e5ff388</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Vertical layout</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>3d7e6247-8186-4d05-8a73-640dbcdef70e</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>7c2b46f5-1922-4bb3-86e4-8107f857ca0f</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns19:viewUUID>
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>ab9e2c92-571b-493d-8989-928a5b29f56b</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>b894011d-380b-4c74-8793-5c0e06f38172</ns19:id>
                                                                <ns19:layoutItemId>Tab_section1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>36d4c16f-7034-4280-8dfa-4e363ac5762e</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Tab section</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8d0e45d4-7e34-4d93-80b3-eac76cf21f04</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>eac9e22d-000e-470c-80ac-1cd4622827c5</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c86aa6fb-5135-4d26-847b-b77ada28c95e</ns19:id>
                                                                    <ns19:optionName>colorStyle</ns19:optionName>
                                                                    <ns19:value>P</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5795308b-8753-4682-841b-11a95a0b0db2</ns19:id>
                                                                    <ns19:optionName>tabsStyle</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"S"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>dc084704-aeef-4e51-8755-e76b6c6a340b</ns19:id>
                                                                    <ns19:optionName>sizeStyle</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a1a8942f-4fbc-40fd-82b9-94399f234244</ns19:id>
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"REQUIRED"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>76689511-acd8-410f-8205-a211ec9d6524</ns19:id>
                                                                    <ns19:optionName>@className</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                                <ns19:contentBoxContrib>
                                                                    <ns19:id>e2de5138-6d70-4421-8e61-808389c55a55</ns19:id>
                                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        <ns19:id>f312bea4-99cb-4f36-8500-6adfd3fed89e</ns19:id>
                                                                        <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                        <ns19:configData>
                                                                            <ns19:id>19529d9f-d64d-4039-8ebc-21c3c703807f</ns19:id>
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            <ns19:value>12-Attachment</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>02e4b7d6-e49c-4a42-8694-12018bb66713</ns19:id>
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            <ns19:value />
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>9f1145e4-7662-48c5-803c-e9f26aefc819</ns19:id>
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            <ns19:value>SHOW</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>b12354aa-7d3b-43de-8a8f-d60d5f27d1d1</ns19:id>
                                                                            <ns19:optionName>ECMProperties</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>4af5978d-5231-452a-81e1-7cb2c0d9bda6</ns19:id>
                                                                            <ns19:optionName>remittanceLetterPath</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.folderPath</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>d6e8730f-99a2-4b09-8022-0b66ec7c6031</ns19:id>
                                                                            <ns19:optionName>canUpdate</ns19:optionName>
                                                                            <ns19:value>true</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>6255287c-9a4b-4a28-8d89-d62b564dd643</ns19:id>
                                                                            <ns19:optionName>canCreate</ns19:optionName>
                                                                            <ns19:value>true</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>07f87978-83ef-45a4-8439-4f00d04d10af</ns19:id>
                                                                            <ns19:optionName>canDelete</ns19:optionName>
                                                                            <ns19:value>true</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>b370e808-fdeb-47db-85e0-4fa839351aba</ns19:id>
                                                                            <ns19:optionName>updateProperties</ns19:optionName>
                                                                            <ns19:value>tw.local.updateProp[]</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:viewUUID>64.3361b968-662d-491d-8e02-666b6b3648ec</ns19:viewUUID>
                                                                        <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                                    </ns19:contributions>
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        <ns19:id>d1f604ab-1894-4e5b-8161-c3e9f6869ef8</ns19:id>
                                                                        <ns19:layoutItemId>FC_Collections_CV1</ns19:layoutItemId>
                                                                        <ns19:configData>
                                                                            <ns19:id>31bd691c-bc6c-4922-87e0-da5c2cd0c0bd</ns19:id>
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            <ns19:value>FC Collections CV</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>63459d65-96ea-4744-8e99-f3bce440944b</ns19:id>
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            <ns19:value />
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>e5bc9c24-2aee-449c-80ff-455078eeeef6</ns19:id>
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            <ns19:value>SHOW</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                    </ns19:contributions>
                                                                </ns19:contentBoxContrib>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                                <ns3:dataChangeScript />
                            </ns3:formTask>
                            <ns16:sequenceFlow sourceRef="2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb" targetRef="2025.b942fed5-4b87-4dfc-8d5b-669f5b56415b" name="To End" id="2027.6f265f51-afa4-48ef-8737-a2afa20c2f35">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.eca5b84b-de97-4ab6-bdba-a8b9cb7bdc9e" targetRef="2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb" name="To Valid?" id="2027.15174c2b-d9a4-485c-8144-313f9245b2b0">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.c4acccc8-1c3e-4352-a1ad-35c4c1ca7866" targetRef="2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a" name="To Screen" id="2027.894a9c37-37a6-4ce7-8330-51f32d9c3104">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="897246e0-1ff4-4d8e-8856-f1065c4af670" targetRef="2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a" name="To Coach" id="2027.42f80e5f-8a4a-498c-b7a2-16fa8fed4964">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a" targetRef="2025.c4acccc8-1c3e-4352-a1ad-35c4c1ca7866" name="To Postpone" id="2027.2f851fbb-9f5d-425c-8c61-6b8e8824bf64">
                                <ns16:extensionElements>
                                    <ns3:coachEventBinding id="60d64e04-001d-4515-9834-e7d4116fa8b5">
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a" targetRef="2025.eca5b84b-de97-4ab6-bdba-a8b9cb7bdc9e" name="To End" id="2027.34a4d91b-89f1-4dca-9ada-0b13a7a0991f">
                                <ns16:extensionElements>
                                    <ns3:coachEventBinding id="aed447f0-6b9e-43bd-8d65-bd136cad5f16">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.1daf0085-b963-4584-81d3-24ea8d0d4fcb" targetRef="2025.12676a0f-d4d5-43c8-b8da-6d6a2a72fb3a" name="To Screen" id="2027.8352556c-701f-4ea2-8d56-22bcc5145630">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  &gt;	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" name="Client-Side Script" id="2025.35bb998e-c429-474a-85f9-432806e7434e">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="547" y="271" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:script />
                            </ns16:scriptTask>
                            <ns16:dataObject itemSubjectRef="itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061" isCollection="true" name="charges" id="2056.7a0d61e2-9b8a-44e6-8a93-3ad60859e232">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].component = "";
autoObject[0].defaultCurrency = {};
autoObject[0].defaultCurrency.name = "";
autoObject[0].defaultCurrency.value = "";
autoObject[0].changeAmount = 0.0;
autoObject[0].waiver = false;
autoObject[0].debitedAccount = {};
autoObject[0].debitedAccount.accountClass = {};
autoObject[0].debitedAccount.accountClass.name = "";
autoObject[0].debitedAccount.accountClass.value = "";
autoObject[0].debitedAccount.glAccountNo = "";
autoObject[0].debitedAccount.customerAccountNo = "";
autoObject[0].debitedAccount.branchCode = "";
autoObject[0].debitedAccount.currency = {};
autoObject[0].debitedAccount.currency.name = "";
autoObject[0].debitedAccount.currency.value = "";
autoObject[0].debitedAccount.balance = 0.0;
autoObject[0].debitedAccount.balanceSign = "";
autoObject[0].debitedAccount.isOverDraft = false;
autoObject[0].debitedAmount = {};
autoObject[0].debitedAmount.standardExRate = 0.0;
autoObject[0].debitedAmount.negotiatedExRate = 0.0;
autoObject[0].debitedAmount.amountInAccount = 0.0;
autoObject[0].rateType = "";
autoObject[0].description = "";
autoObject[0].defaultPercentage = 0.0;
autoObject[0].changePercentage = 0.0;
autoObject[0].defaultAmount = 0.0;
autoObject[0].basicAmountCurrency = "";
autoObject[0].flatAmount = 0.0;
autoObject[0].isGLFound = false;
autoObject[0].glVerifyMSG = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="updateProp" id="2056.ae5002f1-b15c-461d-8b50-773bcdefb5b9">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = [];
autoObject[0] = {};
autoObject[0].name = "";
autoObject[0].value = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns3:htmlHeaderTag id="62539589-4302-491b-93a0-2782389d5e1c">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="bb759592-f2f7-40f3-8d2c-1489ab500ca9" />
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="a32c52e1-a276-4e4d-81c2-cd41ab5bc551" />
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="9cd57e59-b7e1-4482-8c4c-588e29e57de4" />
                                <ns3:epvProcessLinkRef epvId="21.769dc134-1d15-4dd4-a967-c5f61cf352dc" epvProcessLinkId="2d23c808-1ed4-4142-8ddb-d0dcba7601f2" />
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="0632eb96-065b-4557-887c-01e27486455e" />
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="3171034a-3c65-4e0d-8aee-052ffb8cbeba" />
                            </ns3:epvProcessLinks>
                            <ns3:localizationResourceLinks>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    <ns3:id>69.31cfd076-ef2f-4737-85d1-a256beb27f48</ns3:id>
                                </ns3:resourceRef>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.41101508-d2e4-4682-b3ef-b9b22266bb5a</ns3:resourceBundleGroupID>
                                    <ns3:id>69.217edee5-1f2d-4b8b-8502-8576e027daf4</ns3:id>
                                </ns3:resourceRef>
                            </ns3:localizationResourceLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.211ff6ae-e163-4085-87a7-de8ccdbd9d3d">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.initiator = "";
autoObject.requestNature = {};
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = {};
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new Date();
autoObject.ImporterName = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = {};
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = {};
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = {};
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = {};
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = {};
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = {};
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = {};
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = {};
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = [];
autoObject.BasicDetails.Bills[0] = {};
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();
autoObject.BasicDetails.Invoice = [];
autoObject.BasicDetails.Invoice[0] = {};
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new Date();
autoObject.GeneratedDocumentInfo = {};
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = [];
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = [];
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = [];
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = [];
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = [];
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = {};
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = [];
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = {};
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = {};
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new Date();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = {};
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = [];
autoObject.FinancialDetailsBR.listOfAccounts[0] = {};
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = {};
autoObject.FcCollections.currency = {};
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new Date();
autoObject.FcCollections.ToDate = new Date();
autoObject.FcCollections.accountNo = {};
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = [];
autoObject.FcCollections.retrievedTransactions[0] = {};
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = [];
autoObject.FcCollections.selectedTransactions[0] = {};
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new Date();
autoObject.FcCollections.selectedTransactions[0].valueDate = new Date();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = [];
autoObject.FcCollections.listOfAccounts[0] = {};
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = {};
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new Date();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = {};
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = [];
autoObject.FinancialDetailsFO.multiTenorDates[0] = {};
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = {};
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = {};
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = {};
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = {};
autoObject.ProductShipmentDetails.CBECommodityClassification = {};
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new Date();
autoObject.ProductShipmentDetails.shipmentMethod = {};
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = {};
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = {};
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = {};
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = {};
autoObject.ContractCreation.productCode = {};
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new Date();
autoObject.ContractCreation.valueDate = new Date();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new Date();
autoObject.Parties = {};
autoObject.Parties.Drawer = {};
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = {};
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = {};
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = {};
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = {};
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.Parties.caseInNeed = {};
autoObject.Parties.caseInNeed.partyCIF = "";
autoObject.Parties.caseInNeed.partyId = "";
autoObject.Parties.caseInNeed.partyName = "";
autoObject.Parties.caseInNeed.country = "";
autoObject.Parties.caseInNeed.language = "";
autoObject.Parties.caseInNeed.refrence = "";
autoObject.Parties.caseInNeed.address1 = "";
autoObject.Parties.caseInNeed.address2 = "";
autoObject.Parties.caseInNeed.address3 = "";
autoObject.Parties.caseInNeed.partyType = {};
autoObject.Parties.caseInNeed.partyType.name = "";
autoObject.Parties.caseInNeed.partyType.value = "";
autoObject.ChargesAndCommissions = [];
autoObject.ChargesAndCommissions[0] = {};
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = {};
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = {};
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = {};
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ChargesAndCommissions[0].isGLFound = false;
autoObject.ChargesAndCommissions[0].glVerifyMSG = "";
autoObject.ContractLiquidation = {};
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new Date();
autoObject.ContractLiquidation.creditValueDate = new Date();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.debitedAccountName = "";
autoObject.ContractLiquidation.creditedAccount = {};
autoObject.ContractLiquidation.creditedAccount.accountClass = {};
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = {};
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = {};
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = [];
autoObject.actions[0] = "";
autoObject.attachmentDetails = {};
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = {};
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = [];
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = [];
autoObject.attachmentDetails.attachment[0] = {};
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = [];
autoObject.complianceComments[0] = {};
autoObject.complianceComments[0].startTime = new Date();
autoObject.complianceComments[0].endTime = new Date();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = [];
autoObject.History[0] = {};
autoObject.History[0].startTime = new Date();
autoObject.History[0].endTime = new Date();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = {};
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject.folderPath = "";
autoObject.templateDocID = "";
autoObject.requestID = 0;
autoObject.customerAndPartyAccountList = [];
autoObject.customerAndPartyAccountList[0] = {};
autoObject.customerAndPartyAccountList[0].accountNO = "";
autoObject.customerAndPartyAccountList[0].currencyCode = "";
autoObject.customerAndPartyAccountList[0].branchCode = "";
autoObject.customerAndPartyAccountList[0].balance = 0.0;
autoObject.customerAndPartyAccountList[0].typeCode = "";
autoObject.customerAndPartyAccountList[0].customerName = "";
autoObject.customerAndPartyAccountList[0].customerNo = "";
autoObject.customerAndPartyAccountList[0].frozen = false;
autoObject.customerAndPartyAccountList[0].dormant = false;
autoObject.customerAndPartyAccountList[0].noDebit = false;
autoObject.customerAndPartyAccountList[0].noCredit = false;
autoObject.customerAndPartyAccountList[0].postingAllowed = false;
autoObject.customerAndPartyAccountList[0].ibanAccountNumber = "";
autoObject.customerAndPartyAccountList[0].accountClassCode = "";
autoObject.customerAndPartyAccountList[0].balanceType = "";
autoObject.customerAndPartyAccountList[0].accountStatus = "";
autoObject.tradeFoComment = "";
autoObject.exeHubMkrComment = "";
autoObject.compcheckerComment = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="regeneratedRemittanceLetterTitleVIS" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0c656cf2-a9c3-46da-8d92-06744fffefeb" />
                        <ns16:dataInput name="fromExeChecker" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.bcaeb4cc-7baf-4da3-bb06-3a5597ab0684" />
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.c3af8f62-95a0-42bb-a4de-d76cb3f3190e" />
                        <ns16:dataOutput name="compApprovalInit" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.4383f404-0c48-452d-990f-ae1e8e27821e" />
                        <ns16:dataOutput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.e8a84586-d489-4e0c-b784-e8560cfa8076" />
                        <ns16:inputSet id="016704f5-ec73-4162-9ebd-4c4cf6693554" />
                        <ns16:outputSet id="37cdd4dc-0416-4277-adc7-d5dd4d7273c3" />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4fa3c2cd-9d72-4fa5-a1c3-2d2f4acff2d5</processLinkId>
            <processId>1.b76a5b08-c18f-44fd-ad92-b9cd6f3ad81e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1af97ac0-dd77-44e4-a232-cc4b0abc55bd</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1ef90d30-89dc-42d2-8946-eab1145f7899</toProcessItemId>
            <guid>02666542-700f-4947-878e-7de9fba7e5fa</guid>
            <versionId>032615d4-664a-4bbf-808c-e7ede5bd816e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.1af97ac0-dd77-44e4-a232-cc4b0abc55bd</fromProcessItemId>
            <toProcessItemId>2025.1ef90d30-89dc-42d2-8946-eab1145f7899</toProcessItemId>
        </link>
    </process>
</teamworks>

