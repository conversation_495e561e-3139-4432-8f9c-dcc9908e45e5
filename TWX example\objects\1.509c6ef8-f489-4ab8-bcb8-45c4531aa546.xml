<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.509c6ef8-f489-4ab8-bcb8-45c4531aa546" name="Retrieve Request Number">
        <lastModified>1700640016701</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.0b5388d2-710c-4d60-8273-fc5083f9df41</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3794</guid>
        <versionId>f95d146a-6c23-4ba7-85b6-bf3d0ee3d552</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:542a" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.d485ccef-7aa5-461f-8750-141206f211b6"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":6,"y":74,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"ec1cbe89-1f9d-4798-858b-4facbe857909"},{"incoming":["f56cd9b4-ec5b-4773-869a-66ff2e676749","03214778-213c-410b-87ac-1aeb1579f0c7"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":555,"y":75,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"9a4f7402-5fc1-40f6-82b0-b18ea19b31f9"},{"targetRef":"0b5388d2-710c-4d60-8273-fc5083f9df41","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.d485ccef-7aa5-461f-8750-141206f211b6","sourceRef":"ec1cbe89-1f9d-4798-858b-4facbe857909"},{"startQuantity":1,"outgoing":["5a949aee-cc6f-4dcf-8beb-c49a4a34fabb"],"incoming":["2027.d485ccef-7aa5-461f-8750-141206f211b6"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":127,"y":52,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Sql query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0b5388d2-710c-4d60-8273-fc5083f9df41","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sql = \"select requestNo from ODC_RequestInfo where PARENTREQUESTNO = ? order by requestNo desc;\"\r\n\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\ntw.local.parameters[0] = new tw.object.SQLParameter();\r\ntw.local.parameters[0].value = tw.local.parentRequestNumber;"]}},{"startQuantity":1,"outgoing":["ad377e79-eb89-4219-83f0-19ca1836eea0"],"incoming":["5a949aee-cc6f-4dcf-8beb-c49a4a34fabb"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":296,"y":53,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Sql execute statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.parameters"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a4578e04-1773-48d1-81e1-c385d8bb3b7c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":886,"y":85,"declaredType":"TNodeVisualInfo","height":70}]},"name":"result","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"1bf6bd61-8f48-497f-8ebf-67949c77849b","scriptFormat":"text\/x-javascript","script":{"content":["\tvar requestNumber = tw.local.results[0].rows[0].data[0];\r\n\t\/\/ arr is array to store the splitted request number\r\n\tvar arr = requestNumber.split('-');\r\n\t\/\/ x var the incremental part of request type \r\n\tvar x = parseInt(arr[1]);\r\nif(x == \"\")\r\n\t tw.local.newRequestId = tw.local.parentRequestNumber+\"-01\";\r\nelse\r\n{\t \r\n\tx= x+1;\r\n\t\r\n\tif(x &lt; 9)\r\n\ttw.local.tmpvar = \"0\"+ x;\r\n\telse\r\n\ttw.local.tmpvar = x;\r\n\t\r\n\t\r\n\ttw.local.newRequestId = tw.local.parentRequestNumber+'-'+tw.local.tmpvar;\r\n\t\r\n}\r\n"]}},{"targetRef":"a4578e04-1773-48d1-81e1-c385d8bb3b7c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Sql execute statement","declaredType":"sequenceFlow","id":"5a949aee-cc6f-4dcf-8beb-c49a4a34fabb","sourceRef":"0b5388d2-710c-4d60-8273-fc5083f9df41"},{"targetRef":"8c10d3c7-629b-48bf-88ef-610c7c360cec","extensionElements":{"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To result","declaredType":"sequenceFlow","id":"ad377e79-eb89-4219-83f0-19ca1836eea0","sourceRef":"a4578e04-1773-48d1-81e1-c385d8bb3b7c"},{"targetRef":"9a4f7402-5fc1-40f6-82b0-b18ea19b31f9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"f56cd9b4-ec5b-4773-869a-66ff2e676749","sourceRef":"8c10d3c7-629b-48bf-88ef-610c7c360cec"},{"parallelMultiple":false,"outgoing":["0f6180ed-0fdd-4e8e-8b43-ced7c398f14e"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"c42afc2e-0b79-42bf-8f97-4bc1195ed87b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c0f6d36d-2272-4ade-805d-6fe5c501b5ee","otherAttributes":{"eventImplId":"326a6dce-8657-4ce2-8ccb-3471650944df"}}],"attachedToRef":"a4578e04-1773-48d1-81e1-c385d8bb3b7c","extensionElements":{"nodeVisualInfo":[{"width":24,"x":331,"y":111,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"7e104f2f-8557-453c-83ba-277190edbcac","outputSet":{}},{"parallelMultiple":false,"outgoing":["be825c7f-339e-4b48-8f4c-67246f6be687"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3f003487-ce71-478d-876c-1c4c9505d918"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"9ec4a605-47fa-418f-86c8-59df972271cc","otherAttributes":{"eventImplId":"521505e7-454b-4300-8dde-cbf555d237fa"}}],"attachedToRef":"0b5388d2-710c-4d60-8273-fc5083f9df41","extensionElements":{"nodeVisualInfo":[{"width":24,"x":162,"y":110,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"b5f48c1b-9e18-4afc-8b65-2ec137de13c5","outputSet":{}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.abcd088d-e709-49e0-8fd2-41864cba3248"},{"startQuantity":1,"outgoing":["03214778-213c-410b-87ac-1aeb1579f0c7"],"incoming":["be825c7f-339e-4b48-8f4c-67246f6be687","0f6180ed-0fdd-4e8e-8b43-ced7c398f14e"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":295,"y":166,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Error handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Retrieve Request number\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"37f8f1ce-3d46-40c9-89ed-4426d7549be7","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"37f8f1ce-3d46-40c9-89ed-4426d7549be7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Error handling","declaredType":"sequenceFlow","id":"be825c7f-339e-4b48-8f4c-67246f6be687","sourceRef":"b5f48c1b-9e18-4afc-8b65-2ec137de13c5"},{"targetRef":"9a4f7402-5fc1-40f6-82b0-b18ea19b31f9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"03214778-213c-410b-87ac-1aeb1579f0c7","sourceRef":"37f8f1ce-3d46-40c9-89ed-4426d7549be7"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();\nautoObject[0].type = \"\";\nautoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();\nautoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();\nautoObject[0].columns[0].catalogName = \"\";\nautoObject[0].columns[0].columnClassName = \"\";\nautoObject[0].columns[0].columnDisplaySize = 0;\nautoObject[0].columns[0].columnLabel = \"\";\nautoObject[0].columns[0].columnName = \"\";\nautoObject[0].columns[0].columnTypeName = \"\";\nautoObject[0].columns[0].precision = 0;\nautoObject[0].columns[0].scale = 0;\nautoObject[0].columns[0].schemaName = \"\";\nautoObject[0].columns[0].tableName = \"\";\nautoObject[0].columns[0].autoIncrement = false;\nautoObject[0].columns[0].caseSensitive = false;\nautoObject[0].columns[0].currency = false;\nautoObject[0].columns[0].definitelyWritable = false;\nautoObject[0].columns[0].nullable = 0;\nautoObject[0].columns[0].readOnly = false;\nautoObject[0].columns[0].searchable = false;\nautoObject[0].columns[0].signed = false;\nautoObject[0].columns[0].writable = false;\nautoObject[0].columnIndexes = null;\nautoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();\nautoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();\nautoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();\nautoObject[0].rows[0].data[0] = null;\nautoObject[0].rows[0].indexedMap = null;\nautoObject[0].updateCount = 0;\nautoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();\nautoObject[0].outValues[0] = null;\nautoObject"}]},"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.d14abeae-0693-4705-8321-ca8946b4bfa6"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"parameters","isCollection":true,"declaredType":"dataObject","id":"2056.e16311e3-1714-42d0-8b05-b45a80bc6699"},{"targetRef":"37f8f1ce-3d46-40c9-89ed-4426d7549be7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Error handling","declaredType":"sequenceFlow","id":"0f6180ed-0fdd-4e8e-8b43-ced7c398f14e","sourceRef":"7e104f2f-8557-453c-83ba-277190edbcac"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"tmpvar","isCollection":false,"declaredType":"dataObject","id":"2056.9b12ef2c-51c8-4325-80a1-8e740fcacb5d"},{"startQuantity":1,"outgoing":["f56cd9b4-ec5b-4773-869a-66ff2e676749"],"incoming":["ad377e79-eb89-4219-83f0-19ca1836eea0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":441,"y":53,"declaredType":"TNodeVisualInfo","height":70}]},"name":"output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8c10d3c7-629b-48bf-88ef-610c7c360cec","scriptFormat":"text\/x-javascript","script":{"content":["var requestNumber =  tw.local.results[0].rows[0].data[0];\r\nif(requestNumber.indexOf(\"-\") == -1 )\r\n    tw.local.newRequestId = requestNumber + \"-01\"; \r\nelse {\r\n    var arr = requestNumber.split('-');\r\n    var incrementalPart =  arr[1];\r\n    \r\n    if(incrementalPart[0] == \"0\" &amp;&amp; parseInt(incrementalPart[1]) &lt;9 ){\r\n     var num = parseInt(incrementalPart[1]) + 1;\r\n     tw.local.newRequestId = arr[0] + \"-0\" + num; \r\n     }\r\n    else if (parseInt(incrementalPart[1]) == 9 ){\r\n      var num = parseInt(incrementalPart[1]) + 1;\r\n      tw.local.newRequestId = arr[0] + \"-\" + num;\r\n    }\r\n   else {\r\n      var num = parseInt(incrementalPart) + 1;\r\n      tw.local.newRequestId = arr[0] + \"-\" + num;\r\n     }\r\n}"]}}],"laneSet":[{"id":"6dfe740c-4ad3-4885-8e15-0632cf12d377","lane":[{"flowNodeRef":["ec1cbe89-1f9d-4798-858b-4facbe857909","9a4f7402-5fc1-40f6-82b0-b18ea19b31f9","0b5388d2-710c-4d60-8273-fc5083f9df41","a4578e04-1773-48d1-81e1-c385d8bb3b7c","1bf6bd61-8f48-497f-8ebf-67949c77849b","7e104f2f-8557-453c-83ba-277190edbcac","b5f48c1b-9e18-4afc-8b65-2ec137de13c5","37f8f1ce-3d46-40c9-89ed-4426d7549be7","8c10d3c7-629b-48bf-88ef-610c7c360cec"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"cc2b447a-4251-4882-874d-36323f92a7d5","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Retrieve Request Number","declaredType":"process","id":"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"newRequestId","isCollection":false,"id":"2055.29fabc80-90b8-4cad-81e3-1c319c6f595a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.4ec4b61d-5cd9-43b6-827c-c1801162373f"}],"inputSet":[{"dataInputRefs":["2055.e1ca7465-4084-405d-8ea6-ab3f3762de92"]}],"outputSet":[{"dataOutputRefs":["2055.29fabc80-90b8-4cad-81e3-1c319c6f595a","2055.4ec4b61d-5cd9-43b6-827c-c1801162373f"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"00104230000146\"\r\n\"07704230000198\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNumber","isCollection":false,"id":"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="parentRequestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e1ca7465-4084-405d-8ea6-ab3f3762de92</processParameterId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"00104230000146"&#xD;
"07704230000198"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1bffa9d2-dc85-41b2-8e8e-0034d8e7961c</guid>
            <versionId>a9444d59-f9fb-4269-8d04-735aa2c3533e</versionId>
        </processParameter>
        <processParameter name="newRequestId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.29fabc80-90b8-4cad-81e3-1c319c6f595a</processParameterId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>693f32ec-be1c-4b98-8cee-bdfffa8c5fac</guid>
            <versionId>8b8ea6b0-bc0d-4348-836d-75c72eeb7665</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4ec4b61d-5cd9-43b6-827c-c1801162373f</processParameterId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d1108927-dbae-424b-9b7f-b2b7eda9d536</guid>
            <versionId>b040ce0d-e185-471f-a7f3-b2967298249d</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.abcd088d-e709-49e0-8fd2-41864cba3248</processVariableId>
            <description isNull="true" />
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>697b7bb9-36bf-4401-985d-025304d190ed</guid>
            <versionId>07009dc7-0785-4885-a733-17fc73bf6640</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d14abeae-0693-4705-8321-ca8946b4bfa6</processVariableId>
            <description isNull="true" />
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e4d633d8-0cfe-4ab9-a054-b16ac7f9d659</guid>
            <versionId>62e1531f-3466-475f-b823-955b4c35f15e</versionId>
        </processVariable>
        <processVariable name="parameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e16311e3-1714-42d0-8b05-b45a80bc6699</processVariableId>
            <description isNull="true" />
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bebea286-0849-4745-89df-cd2f02d5638e</guid>
            <versionId>81fa92da-fd76-4bad-9539-bc90447ff67e</versionId>
        </processVariable>
        <processVariable name="tmpvar">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b12ef2c-51c8-4325-80a1-8e740fcacb5d</processVariableId>
            <description isNull="true" />
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>290f5bb5-e54b-4ee4-a967-f16778ca7edb</guid>
            <versionId>62c607c4-497c-4fa1-96bd-1d5db5eb7c96</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7</processItemId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <name>Error handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3cea</guid>
            <versionId>45a350af-cd57-48cc-bdb0-f0c44c14a32c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="295" y="166">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>a2d7bac5-29ba-4902-8811-4d9b53c71494</guid>
                <versionId>15c61a67-be81-4624-b503-674cc3f2d40f</versionId>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3dd9edbf-924a-456c-89df-c31e409cc9b2</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Retrieve Request number"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7dce930c-5191-4773-96e6-e89e41fd4519</guid>
                    <versionId>2acf1e5a-5b0c-469f-a860-8c39283bd3f4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ec645e07-c256-4fe3-aee2-65245704a8dd</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5c59deba-e353-4041-ac13-f0c40396a5b0</guid>
                    <versionId>58d0819a-a76b-4965-a611-214ba69461f2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.08c17bf4-d6d6-4cae-8aaa-dca60ce527ec</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>853d436d-d34e-442e-9b41-d23203b21642</guid>
                    <versionId>fb883f1c-cb88-4fc0-b4de-c82055b2a637</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1bf6bd61-8f48-497f-8ebf-67949c77849b</processItemId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <name>result</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.5bbc1aa6-36ac-4be8-9673-b0b984016af4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:386f</guid>
            <versionId>55cd9cdc-9682-455b-a473-262302858cdc</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="886" y="85">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.5bbc1aa6-36ac-4be8-9673-b0b984016af4</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>	var requestNumber = tw.local.results[0].rows[0].data[0];&#xD;
	// arr is array to store the splitted request number&#xD;
	var arr = requestNumber.split('-');&#xD;
	// x var the incremental part of request type &#xD;
	var x = parseInt(arr[1]);&#xD;
if(x == "")&#xD;
	 tw.local.newRequestId = tw.local.parentRequestNumber+"-01";&#xD;
else&#xD;
{	 &#xD;
	x= x+1;&#xD;
	&#xD;
	if(x &lt; 9)&#xD;
	tw.local.tmpvar = "0"+ x;&#xD;
	else&#xD;
	tw.local.tmpvar = x;&#xD;
	&#xD;
	&#xD;
	tw.local.newRequestId = tw.local.parentRequestNumber+'-'+tw.local.tmpvar;&#xD;
	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>8f9744ef-ee03-4a90-b2f8-e5aebba29937</guid>
                <versionId>8f4f470e-8f2f-44e3-8704-a773e7b61d4a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8c10d3c7-629b-48bf-88ef-610c7c360cec</processItemId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <name>output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9d5b0df1-daf0-4667-a8b3-7d9dd99bfc81</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ca84402e5fd92838:-7c46c9a3:18b90bc5b30:6338</guid>
            <versionId>7bca81a1-51e0-4baa-ae66-3ab78c75e7f4</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="441" y="53">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9d5b0df1-daf0-4667-a8b3-7d9dd99bfc81</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var requestNumber =  tw.local.results[0].rows[0].data[0];&#xD;
if(requestNumber.indexOf("-") == -1 )&#xD;
    tw.local.newRequestId = requestNumber + "-01"; &#xD;
else {&#xD;
    var arr = requestNumber.split('-');&#xD;
    var incrementalPart =  arr[1];&#xD;
    &#xD;
    if(incrementalPart[0] == "0" &amp;&amp; parseInt(incrementalPart[1]) &lt;9 ){&#xD;
     var num = parseInt(incrementalPart[1]) + 1;&#xD;
     tw.local.newRequestId = arr[0] + "-0" + num; &#xD;
     }&#xD;
    else if (parseInt(incrementalPart[1]) == 9 ){&#xD;
      var num = parseInt(incrementalPart[1]) + 1;&#xD;
      tw.local.newRequestId = arr[0] + "-" + num;&#xD;
    }&#xD;
   else {&#xD;
      var num = parseInt(incrementalPart) + 1;&#xD;
      tw.local.newRequestId = arr[0] + "-" + num;&#xD;
     }&#xD;
}</script>
                <isRule>false</isRule>
                <guid>0f9c1629-479d-43c8-bab2-0d025492f631</guid>
                <versionId>a75beee5-241a-4852-9216-c7cc7852be8a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9</processItemId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.f954021d-5b40-47e1-8ab7-936a77ee8977</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796</guid>
            <versionId>b0213b1c-89da-4486-b92e-6e41818095bd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="555" y="75">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.f954021d-5b40-47e1-8ab7-936a77ee8977</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>bb432457-06fd-4b80-b2e2-f80be77d5a2e</guid>
                <versionId>b139f707-b9e0-4afd-954b-cd77390db004</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0b5388d2-710c-4d60-8273-fc5083f9df41</processItemId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <name>Sql query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.16eb9624-d0e3-4f29-994c-4d0da980e532</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7</errorHandlerItemId>
            <guid>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:386d</guid>
            <versionId>f647ea52-7a06-42c6-aabf-80f8e336f527</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.5442262b-69bc-4936-84ba-fd68ea2d694d</processItemPrePostId>
                <processItemId>2025.0b5388d2-710c-4d60-8273-fc5083f9df41</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>2af1ce05-e9ea-427e-8310-a1fc157e2e8f</guid>
                <versionId>eaaeaea6-14b8-4e63-bfa4-5fe49eac08e6</versionId>
            </processPrePosts>
            <layoutData x="127" y="52">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3cea</errorHandlerItem>
                <errorHandlerItemId>2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.16eb9624-d0e3-4f29-994c-4d0da980e532</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql = "select requestNo from ODC_RequestInfo where PARENTREQUESTNO = ? order by requestNo desc;"&#xD;
&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0] = new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value = tw.local.parentRequestNumber;</script>
                <isRule>false</isRule>
                <guid>e9b761eb-39db-4aa6-833a-62d85a468e11</guid>
                <versionId>7c002fad-4ae6-4659-bf72-d6448f54251a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c</processItemId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <name>Sql execute statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7</errorHandlerItemId>
            <guid>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:386e</guid>
            <versionId>f6c3370a-aa1a-462d-9573-8776b543558f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="296" y="53">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3cea</errorHandlerItem>
                <errorHandlerItemId>2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>376463c8-0ea6-4c19-8ea5-690a56760a56</guid>
                <versionId>0f5d4ed2-c9f1-4dee-bcbb-28fbc198eec6</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.08e049c0-4e27-489c-9197-2a79b4517ed0</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b3f6144d-66d5-4229-8471-96ae4d2736a6</guid>
                    <versionId>01bfdc07-5ef0-43f0-848f-8d9782ce1dd8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6a9bb069-57ca-4d6c-b449-fdafae127d45</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>0ee112eb-59c3-4642-b048-8bb553ea73ec</guid>
                    <versionId>6bda7fde-fd6c-4a51-bf75-1a863ddf8654</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.abce46e1-3ad8-4b5f-88f3-9212df531da1</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>07e21211-0590-4e38-97c0-9ef6b67b7ca6</guid>
                    <versionId>70d7c636-c768-4344-b703-72beb96e3d47</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ed500d99-73b0-4322-b775-36ff43b31243</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>638ef13b-9d8c-4fe0-9013-97b308e446ea</guid>
                    <versionId>7f1989de-4829-4d87-a1ca-cf0ed6db91a1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a774a2c9-6df2-4f57-951d-7ef933ee125b</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2742d7ac-7113-4787-a24e-9c65db878578</guid>
                    <versionId>d1e5455a-b91f-4639-92cc-fa2edf7e58cb</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.0b5388d2-710c-4d60-8273-fc5083f9df41</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="6" y="74">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Retrieve Request Number" id="1.509c6ef8-f489-4ab8-bcb8-45c4531aa546" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="parentRequestNumber" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.e1ca7465-4084-405d-8ea6-ab3f3762de92">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"00104230000146"&#xD;
"07704230000198"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="newRequestId" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.29fabc80-90b8-4cad-81e3-1c319c6f595a" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.4ec4b61d-5cd9-43b6-827c-c1801162373f" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.e1ca7465-4084-405d-8ea6-ab3f3762de92</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.29fabc80-90b8-4cad-81e3-1c319c6f595a</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.4ec4b61d-5cd9-43b6-827c-c1801162373f</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="6dfe740c-4ad3-4885-8e15-0632cf12d377">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="cc2b447a-4251-4882-874d-36323f92a7d5" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>ec1cbe89-1f9d-4798-858b-4facbe857909</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9a4f7402-5fc1-40f6-82b0-b18ea19b31f9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0b5388d2-710c-4d60-8273-fc5083f9df41</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a4578e04-1773-48d1-81e1-c385d8bb3b7c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1bf6bd61-8f48-497f-8ebf-67949c77849b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7e104f2f-8557-453c-83ba-277190edbcac</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b5f48c1b-9e18-4afc-8b65-2ec137de13c5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>37f8f1ce-3d46-40c9-89ed-4426d7549be7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8c10d3c7-629b-48bf-88ef-610c7c360cec</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="ec1cbe89-1f9d-4798-858b-4facbe857909">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="6" y="74" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.d485ccef-7aa5-461f-8750-141206f211b6</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="9a4f7402-5fc1-40f6-82b0-b18ea19b31f9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="555" y="75" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f56cd9b4-ec5b-4773-869a-66ff2e676749</ns16:incoming>
                        
                        
                        <ns16:incoming>03214778-213c-410b-87ac-1aeb1579f0c7</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="ec1cbe89-1f9d-4798-858b-4facbe857909" targetRef="0b5388d2-710c-4d60-8273-fc5083f9df41" name="To End" id="2027.d485ccef-7aa5-461f-8750-141206f211b6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Sql query" id="0b5388d2-710c-4d60-8273-fc5083f9df41">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="127" y="52" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.d485ccef-7aa5-461f-8750-141206f211b6</ns16:incoming>
                        
                        
                        <ns16:outgoing>5a949aee-cc6f-4dcf-8beb-c49a4a34fabb</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql = "select requestNo from ODC_RequestInfo where PARENTREQUESTNO = ? order by requestNo desc;"&#xD;
&#xD;
tw.local.parameters = new tw.object.listOf.SQLParameter();&#xD;
tw.local.parameters[0] = new tw.object.SQLParameter();&#xD;
tw.local.parameters[0].value = tw.local.parentRequestNumber;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Sql execute statement" id="a4578e04-1773-48d1-81e1-c385d8bb3b7c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="296" y="53" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5a949aee-cc6f-4dcf-8beb-c49a4a34fabb</ns16:incoming>
                        
                        
                        <ns16:outgoing>ad377e79-eb89-4219-83f0-19ca1836eea0</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="result" id="1bf6bd61-8f48-497f-8ebf-67949c77849b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="886" y="85" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:script>	var requestNumber = tw.local.results[0].rows[0].data[0];&#xD;
	// arr is array to store the splitted request number&#xD;
	var arr = requestNumber.split('-');&#xD;
	// x var the incremental part of request type &#xD;
	var x = parseInt(arr[1]);&#xD;
if(x == "")&#xD;
	 tw.local.newRequestId = tw.local.parentRequestNumber+"-01";&#xD;
else&#xD;
{	 &#xD;
	x= x+1;&#xD;
	&#xD;
	if(x &lt; 9)&#xD;
	tw.local.tmpvar = "0"+ x;&#xD;
	else&#xD;
	tw.local.tmpvar = x;&#xD;
	&#xD;
	&#xD;
	tw.local.newRequestId = tw.local.parentRequestNumber+'-'+tw.local.tmpvar;&#xD;
	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="0b5388d2-710c-4d60-8273-fc5083f9df41" targetRef="a4578e04-1773-48d1-81e1-c385d8bb3b7c" name="To Sql execute statement" id="5a949aee-cc6f-4dcf-8beb-c49a4a34fabb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="a4578e04-1773-48d1-81e1-c385d8bb3b7c" targetRef="8c10d3c7-629b-48bf-88ef-610c7c360cec" name="To result" id="ad377e79-eb89-4219-83f0-19ca1836eea0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8c10d3c7-629b-48bf-88ef-610c7c360cec" targetRef="9a4f7402-5fc1-40f6-82b0-b18ea19b31f9" name="To End" id="f56cd9b4-ec5b-4773-869a-66ff2e676749">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="a4578e04-1773-48d1-81e1-c385d8bb3b7c" parallelMultiple="false" name="Error1" id="7e104f2f-8557-453c-83ba-277190edbcac">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="331" y="111" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>0f6180ed-0fdd-4e8e-8b43-ced7c398f14e</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="c42afc2e-0b79-42bf-8f97-4bc1195ed87b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c0f6d36d-2272-4ade-805d-6fe5c501b5ee" eventImplId="326a6dce-8657-4ce2-8ccb-3471650944df">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="0b5388d2-710c-4d60-8273-fc5083f9df41" parallelMultiple="false" name="Error2" id="b5f48c1b-9e18-4afc-8b65-2ec137de13c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="162" y="110" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>be825c7f-339e-4b48-8f4c-67246f6be687</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3f003487-ce71-478d-876c-1c4c9505d918" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="9ec4a605-47fa-418f-86c8-59df972271cc" eventImplId="521505e7-454b-4300-8dde-cbf555d237fa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.abcd088d-e709-49e0-8fd2-41864cba3248" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Error handling" id="37f8f1ce-3d46-40c9-89ed-4426d7549be7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="295" y="166" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>be825c7f-339e-4b48-8f4c-67246f6be687</ns16:incoming>
                        
                        
                        <ns16:incoming>0f6180ed-0fdd-4e8e-8b43-ced7c398f14e</ns16:incoming>
                        
                        
                        <ns16:outgoing>03214778-213c-410b-87ac-1aeb1579f0c7</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Retrieve Request number"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="b5f48c1b-9e18-4afc-8b65-2ec137de13c5" targetRef="37f8f1ce-3d46-40c9-89ed-4426d7549be7" name="To Error handling" id="be825c7f-339e-4b48-8f4c-67246f6be687">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="37f8f1ce-3d46-40c9-89ed-4426d7549be7" targetRef="9a4f7402-5fc1-40f6-82b0-b18ea19b31f9" name="To End" id="03214778-213c-410b-87ac-1aeb1579f0c7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.d14abeae-0693-4705-8321-ca8946b4bfa6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();
autoObject[0].type = "";
autoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();
autoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();
autoObject[0].columns[0].catalogName = "";
autoObject[0].columns[0].columnClassName = "";
autoObject[0].columns[0].columnDisplaySize = 0;
autoObject[0].columns[0].columnLabel = "";
autoObject[0].columns[0].columnName = "";
autoObject[0].columns[0].columnTypeName = "";
autoObject[0].columns[0].precision = 0;
autoObject[0].columns[0].scale = 0;
autoObject[0].columns[0].schemaName = "";
autoObject[0].columns[0].tableName = "";
autoObject[0].columns[0].autoIncrement = false;
autoObject[0].columns[0].caseSensitive = false;
autoObject[0].columns[0].currency = false;
autoObject[0].columns[0].definitelyWritable = false;
autoObject[0].columns[0].nullable = 0;
autoObject[0].columns[0].readOnly = false;
autoObject[0].columns[0].searchable = false;
autoObject[0].columns[0].signed = false;
autoObject[0].columns[0].writable = false;
autoObject[0].columnIndexes = null;
autoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();
autoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();
autoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();
autoObject[0].rows[0].data[0] = null;
autoObject[0].rows[0].indexedMap = null;
autoObject[0].updateCount = 0;
autoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();
autoObject[0].outValues[0] = null;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameters" id="2056.e16311e3-1714-42d0-8b05-b45a80bc6699" />
                    
                    
                    <ns16:sequenceFlow sourceRef="7e104f2f-8557-453c-83ba-277190edbcac" targetRef="37f8f1ce-3d46-40c9-89ed-4426d7549be7" name="To Error handling" id="0f6180ed-0fdd-4e8e-8b43-ced7c398f14e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="tmpvar" id="2056.9b12ef2c-51c8-4325-80a1-8e740fcacb5d" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="output" id="8c10d3c7-629b-48bf-88ef-610c7c360cec">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="441" y="53" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ad377e79-eb89-4219-83f0-19ca1836eea0</ns16:incoming>
                        
                        
                        <ns16:outgoing>f56cd9b4-ec5b-4773-869a-66ff2e676749</ns16:outgoing>
                        
                        
                        <ns16:script>var requestNumber =  tw.local.results[0].rows[0].data[0];&#xD;
if(requestNumber.indexOf("-") == -1 )&#xD;
    tw.local.newRequestId = requestNumber + "-01"; &#xD;
else {&#xD;
    var arr = requestNumber.split('-');&#xD;
    var incrementalPart =  arr[1];&#xD;
    &#xD;
    if(incrementalPart[0] == "0" &amp;&amp; parseInt(incrementalPart[1]) &lt;9 ){&#xD;
     var num = parseInt(incrementalPart[1]) + 1;&#xD;
     tw.local.newRequestId = arr[0] + "-0" + num; &#xD;
     }&#xD;
    else if (parseInt(incrementalPart[1]) == 9 ){&#xD;
      var num = parseInt(incrementalPart[1]) + 1;&#xD;
      tw.local.newRequestId = arr[0] + "-" + num;&#xD;
    }&#xD;
   else {&#xD;
      var num = parseInt(incrementalPart) + 1;&#xD;
      tw.local.newRequestId = arr[0] + "-" + num;&#xD;
     }&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.03214778-213c-410b-87ac-1aeb1579f0c7</processLinkId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9</toProcessItemId>
            <guid>8c063a75-**************-e82c74a87ca2</guid>
            <versionId>14085622-464c-4517-b181-c4f96af87f13</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7</fromProcessItemId>
            <toProcessItemId>2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9</toProcessItemId>
        </link>
        <link name="To result">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ad377e79-eb89-4219-83f0-19ca1836eea0</processLinkId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.8c10d3c7-629b-48bf-88ef-610c7c360cec</toProcessItemId>
            <guid>c4f62e25-e3fe-4739-8336-78569eb86907</guid>
            <versionId>795a9892-2e0a-4078-8fb8-7676f9b6fe26</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c</fromProcessItemId>
            <toProcessItemId>2025.8c10d3c7-629b-48bf-88ef-610c7c360cec</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f56cd9b4-ec5b-4773-869a-66ff2e676749</processLinkId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8c10d3c7-629b-48bf-88ef-610c7c360cec</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9</toProcessItemId>
            <guid>f12012eb-0340-4bfc-b6c1-e6d781827bb9</guid>
            <versionId>d5deb2ce-692f-4de4-a09e-30102981cbbb</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8c10d3c7-629b-48bf-88ef-610c7c360cec</fromProcessItemId>
            <toProcessItemId>2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9</toProcessItemId>
        </link>
        <link name="To Sql execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5a949aee-cc6f-4dcf-8beb-c49a4a34fabb</processLinkId>
            <processId>1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0b5388d2-710c-4d60-8273-fc5083f9df41</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c</toProcessItemId>
            <guid>5d93acb1-4ce5-4ccf-b9bf-d0d23d951e66</guid>
            <versionId>f5272815-b6bd-4a3e-9109-caf13c72f5e4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0b5388d2-710c-4d60-8273-fc5083f9df41</fromProcessItemId>
            <toProcessItemId>2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c</toProcessItemId>
        </link>
    </process>
</teamworks>

