<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exception Handling">
        <lastModified>1735138665273</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.fd7e244b-12d4-4014-8b2b-75096297f636</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f85</guid>
        <versionId>2fd5fb36-d939-4309-899f-4e0ac13819e5</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:f7f6553919972802:-74de7c2c:193f7b0288a:-5dde" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.3c574b24-2470-4a6a-8753-571e01713764"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":225,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"03bc6a28-aacd-49ae-8da2-a70993957629"},{"targetRef":"fd7e244b-12d4-4014-8b2b-75096297f636","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exp. Handling","declaredType":"sequenceFlow","id":"2027.3c574b24-2470-4a6a-8753-571e01713764","sourceRef":"03bc6a28-aacd-49ae-8da2-a70993957629"},{"startQuantity":1,"outgoing":["14429e3b-709f-4e0c-8571-1a4345a842e0"],"incoming":["2027.3c574b24-2470-4a6a-8753-571e01713764"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":310,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Exp. Handling","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"fd7e244b-12d4-4014-8b2b-75096297f636","scriptFormat":"text\/x-javascript","script":{"content":["\r\n\r\n\r\n\r\ntw.local.errorMessage = String(tw.system.error.getAttribute(\"type\")) +\", \"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n tw.local.isSuccessful= false;\r\n"]}},{"incoming":["14429e3b-709f-4e0c-8571-1a4345a842e0","c4fc4acc-7b7e-4499-862f-783345ff9731"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":490,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"aacf2c29-c8e6-4be3-8353-cdc22d594acd"},{"targetRef":"aacf2c29-c8e6-4be3-8353-cdc22d594acd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"14429e3b-709f-4e0c-8571-1a4345a842e0","sourceRef":"fd7e244b-12d4-4014-8b2b-75096297f636"},{"parallelMultiple":false,"outgoing":["c4fc4acc-7b7e-4499-862f-783345ff9731"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"b6dda5eb-2dde-4fc0-8ea7-fc7a3fbfaba8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"89cf5e84-defe-4c0f-8f68-88907784e16c","otherAttributes":{"eventImplId":"1b40d0e7-473d-4e57-8460-b870c2cec5c3"}}],"attachedToRef":"fd7e244b-12d4-4014-8b2b-75096297f636","extensionElements":{"nodeVisualInfo":[{"width":24,"x":371,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"599029fe-f7be-40d9-880d-4d2c7b112969","outputSet":{}},{"targetRef":"aacf2c29-c8e6-4be3-8353-cdc22d594acd","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"c4fc4acc-7b7e-4499-862f-783345ff9731","sourceRef":"599029fe-f7be-40d9-880d-4d2c7b112969"}],"laneSet":[{"id":"8f1175eb-421c-4ac4-8959-a868bf8f8009","lane":[{"flowNodeRef":["03bc6a28-aacd-49ae-8da2-a70993957629","aacf2c29-c8e6-4be3-8353-cdc22d594acd","fd7e244b-12d4-4014-8b2b-75096297f636","599029fe-f7be-40d9-880d-4d2c7b112969"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"1bf0bcba-2bb4-431f-89eb-d0b37e21c478","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Exception Handling","declaredType":"process","id":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"id":"2055.99eb514d-4b62-401e-8cdb-7edc096adffd"}],"inputSet":[{"dataInputRefs":["2055.5ea77901-7a17-422a-8958-67bb0e9c991b","2055.d1830a67-1c3d-403f-85ac-213a58225a6c"]}],"outputSet":[{"dataOutputRefs":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf","2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"dataInput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"serviceName","isCollection":false,"id":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.d1830a67-1c3d-403f-85ac-213a58225a6c"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="serviceName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
            <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>29a87327-30ab-4931-8bc6-51fe91c8e38e</guid>
            <versionId>7b9db658-04bc-42e6-82c9-4a67ee6dabfb</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d1830a67-1c3d-403f-85ac-213a58225a6c</processParameterId>
            <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>32bff15f-647a-46db-afdc-5311976e4993</guid>
            <versionId>4542c21e-6b9d-423a-83d0-9e6104bfddd2</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
            <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>012f78a5-be00-41c5-a29c-d588e056f89b</guid>
            <versionId>e58905d2-0be6-4350-b87f-e903c208194b</versionId>
        </processParameter>
        <processParameter name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
            <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>25c6a336-00a7-43b7-9243-2edd3f82ad1e</guid>
            <versionId>76e09ffc-cd39-481f-9674-ddaf22570d07</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fd7e244b-12d4-4014-8b2b-75096297f636</processItemId>
            <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
            <name>Exp. Handling</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.4632b54c-ee5a-4b27-9620-a8c73d8f7472</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.aacf2c29-c8e6-4be3-8353-cdc22d594acd</errorHandlerItemId>
            <guid>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f7d</guid>
            <versionId>14e7adfc-2477-4e93-a190-c5011e792d09</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.aa99fd5d-5ba0-4345-b6b4-f3781b54ebb3</processItemPrePostId>
                <processItemId>2025.fd7e244b-12d4-4014-8b2b-75096297f636</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>b0fe7261-3b89-41f1-ab55-929e97b1ee55</guid>
                <versionId>8cfdd3d6-8ccf-49a7-a5d1-ed6c7e4b1f66</versionId>
            </processPrePosts>
            <layoutData x="310" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomRight</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</errorHandlerItem>
                <errorHandlerItemId>2025.aacf2c29-c8e6-4be3-8353-cdc22d594acd</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.4632b54c-ee5a-4b27-9620-a8c73d8f7472</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
&#xD;
&#xD;
&#xD;
tw.local.errorMessage = String(tw.system.error.getAttribute("type")) +", "+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
 tw.local.isSuccessful= false;&#xD;
</script>
                <isRule>false</isRule>
                <guid>20e33766-2c4a-44f0-9418-84742aa8da9e</guid>
                <versionId>1ca4ff53-06e9-4b31-b0d7-33f0f7696091</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.aacf2c29-c8e6-4be3-8353-cdc22d594acd</processItemId>
            <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.a3ce40fa-2a8e-464e-a907-5a96c891dcd1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</guid>
            <versionId>e9c951cc-5ccf-4003-9245-a821cbfe91ad</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="490" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.a3ce40fa-2a8e-464e-a907-5a96c891dcd1</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>4b44ce71-01a2-49ee-aa37-c7e28d9b3cd9</guid>
                <versionId>b2a97ba5-79f7-4285-81a9-6ed7821bab81</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.fd7e244b-12d4-4014-8b2b-75096297f636</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="225" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="Exception Handling" id="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        <ns3:isSecured>false</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:dataInput name="serviceName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5ea77901-7a17-422a-8958-67bb0e9c991b" />
                        <ns16:dataInput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d1830a67-1c3d-403f-85ac-213a58225a6c" />
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.81b82125-a5aa-45f7-8c0f-4870667eebbf" />
                        <ns16:dataOutput name="isSuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.99eb514d-4b62-401e-8cdb-7edc096adffd" />
                        <ns16:inputSet>
                            <ns16:dataInputRefs>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:dataInputRefs>
                            <ns16:dataInputRefs>2055.d1830a67-1c3d-403f-85ac-213a58225a6c</ns16:dataInputRefs>
                        </ns16:inputSet>
                        <ns16:outputSet>
                            <ns16:dataOutputRefs>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:dataOutputRefs>
                            <ns16:dataOutputRefs>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:dataOutputRefs>
                        </ns16:outputSet>
                    </ns16:ioSpecification>
                    <ns16:laneSet id="8f1175eb-421c-4ac4-8959-a868bf8f8009">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="1bf0bcba-2bb4-431f-89eb-d0b37e21c478" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>03bc6a28-aacd-49ae-8da2-a70993957629</ns16:flowNodeRef>
                            <ns16:flowNodeRef>aacf2c29-c8e6-4be3-8353-cdc22d594acd</ns16:flowNodeRef>
                            <ns16:flowNodeRef>fd7e244b-12d4-4014-8b2b-75096297f636</ns16:flowNodeRef>
                            <ns16:flowNodeRef>599029fe-f7be-40d9-880d-4d2c7b112969</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="03bc6a28-aacd-49ae-8da2-a70993957629">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="225" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.3c574b24-2470-4a6a-8753-571e01713764</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:sequenceFlow sourceRef="03bc6a28-aacd-49ae-8da2-a70993957629" targetRef="fd7e244b-12d4-4014-8b2b-75096297f636" name="To Exp. Handling" id="2027.3c574b24-2470-4a6a-8753-571e01713764">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Exp. Handling" id="fd7e244b-12d4-4014-8b2b-75096297f636">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="310" y="57" width="95" height="70" />
                            <ns3:preAssignmentScript />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.3c574b24-2470-4a6a-8753-571e01713764</ns16:incoming>
                        <ns16:outgoing>14429e3b-709f-4e0c-8571-1a4345a842e0</ns16:outgoing>
                        <ns16:script>&#xD;
&#xD;
&#xD;
&#xD;
tw.local.errorMessage = String(tw.system.error.getAttribute("type")) +", "+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
 tw.local.isSuccessful= false;&#xD;
</ns16:script>
                    </ns16:scriptTask>
                    <ns16:endEvent name="End" id="aacf2c29-c8e6-4be3-8353-cdc22d594acd">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="490" y="80" width="24" height="24" color="#F8F8F8" />
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                        </ns16:extensionElements>
                        <ns16:incoming>14429e3b-709f-4e0c-8571-1a4345a842e0</ns16:incoming>
                        <ns16:incoming>c4fc4acc-7b7e-4499-862f-783345ff9731</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="fd7e244b-12d4-4014-8b2b-75096297f636" targetRef="aacf2c29-c8e6-4be3-8353-cdc22d594acd" name="To End" id="14429e3b-709f-4e0c-8571-1a4345a842e0">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="fd7e244b-12d4-4014-8b2b-75096297f636" parallelMultiple="false" name="Error" id="599029fe-f7be-40d9-880d-4d2c7b112969">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="371" y="115" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:outgoing>c4fc4acc-7b7e-4499-862f-783345ff9731</ns16:outgoing>
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="b6dda5eb-2dde-4fc0-8ea7-fc7a3fbfaba8" />
                        <ns16:outputSet />
                        <ns16:errorEventDefinition id="89cf5e84-defe-4c0f-8f68-88907784e16c" eventImplId="1b40d0e7-473d-4e57-8460-b870c2cec5c3">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:boundaryEvent>
                    <ns16:sequenceFlow sourceRef="599029fe-f7be-40d9-880d-4d2c7b112969" targetRef="aacf2c29-c8e6-4be3-8353-cdc22d594acd" name="To End" id="c4fc4acc-7b7e-4499-862f-783345ff9731">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.14429e3b-709f-4e0c-8571-1a4345a842e0</processLinkId>
            <processId>1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fd7e244b-12d4-4014-8b2b-75096297f636</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.aacf2c29-c8e6-4be3-8353-cdc22d594acd</toProcessItemId>
            <guid>c4655b3f-5709-4802-971b-44aea518265e</guid>
            <versionId>59461607-aaef-48d5-bc0c-9a4b75a4767f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fd7e244b-12d4-4014-8b2b-75096297f636</fromProcessItemId>
            <toProcessItemId>2025.aacf2c29-c8e6-4be3-8353-cdc22d594acd</toProcessItemId>
        </link>
    </process>
</teamworks>

