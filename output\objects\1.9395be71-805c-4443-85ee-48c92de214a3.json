{"id": "1.9395be71-805c-4443-85ee-48c92de214a3", "versionId": "388fedc6-f078-4b14-a057-d0465cc60849", "name": "Compare Validations", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [], "output": [], "private": [{"name": "customerName", "hasDefault": false}, {"name": "customerAge", "hasDefault": false}, {"name": "CIF", "hasDefault": false}, {"name": "customerAccount", "hasDefault": false}, {"name": "requestDate", "hasDefault": false}, {"name": "startDate", "hasDefault": false}, {"name": "endDate", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Coach", "id": "2025.f16b3fc6-d351-4a83-aabd-62aa62edb4d6", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [], "exclusiveGateways": [], "scriptTasks": [{"name": "New Validation", "id": "2025.8ce9ed09-4d95-4688-81a3-a6e189e008eb", "script": "//customer name\r\r\nif(tw.local.customerName == null || tw.local.customerName == \"\"){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.customerName\", \"Customer Name is mandatory\")\r\r\n}\r\r\n\r\r\n//customer age\r\r\nif(tw.local.customerAge == null || tw.local.customerAge == 0){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.customerAge\", \"Customer Age must be > 0\")\r\r\n}\r\r\n\r\r\n//cif\r\r\nif(tw.local.CIF == null || tw.local.CIF == \"\"){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.CIF\", \"CIF is mandatory\")\r\r\n}\r\r\n\r\r\nif(tw.local.CIF.length != 8){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.CIF\", \"CIF must be 8 digits\")\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.9395be71-805c-4443-85ee-48c92de214a3", "name": "Compare Validations", "lastModified": "1707746577788", "lastModifiedBy": "mohamed.reda", "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.437b16b6-7958-49ae-8979-c49775de8994", "2025.437b16b6-7958-49ae-8979-c49775de8994"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:709ea623f6099cbd:-68e5c70f:18d7fd15bbe:-113e", "versionId": "388fedc6-f078-4b14-a057-d0465cc60849", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.fd9b62fb-9a45-48f2-bbc2-fb17612b7fbc\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":50,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"b9b0e024-13d2-4774-b0da-09a799289095\"},{\"outgoing\":[\"2027.5e34d060-07cd-415b-b740-1b36b2120ee6\"],\"incoming\":[\"2027.fd9b62fb-9a45-48f2-bbc2-fb17612b7fbc\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":350,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"layoutItemId\":\"okbutton\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"90aa7f61-abe1-47c9-b0cd-49144a103550\",\"optionName\":\"@bpmDefaultCreated\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b814369a-8c08-4255-b09a-6fbb7ae7e87a\",\"optionName\":\"@label\",\"value\":\"OK\"}],\"viewUUID\":\"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"39a9ab55-2cb2-4eeb-8882-aeb7523ef9ca\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Coach\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.f16b3fc6-d351-4a83-aabd-62aa62edb4d6\"},{\"incoming\":[\"2027.5e34d060-07cd-415b-b740-1b36b2120ee6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":700,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"f9d9753b-c760-4d2d-9a54-25ec0c91bba0\"},{\"targetRef\":\"2025.f16b3fc6-d351-4a83-aabd-62aa62edb4d6\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fd9b62fb-9a45-48f2-bbc2-fb17612b7fbc\",\"sourceRef\":\"b9b0e024-13d2-4774-b0da-09a799289095\"},{\"targetRef\":\"f9d9753b-c760-4d2d-9a54-25ec0c91bba0\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"073defd7-071b-4393-9756-571829dd8888\",\"coachEventPath\":\"okbutton\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5e34d060-07cd-415b-b740-1b36b2120ee6\",\"sourceRef\":\"2025.f16b3fc6-d351-4a83-aabd-62aa62edb4d6\"},{\"startQuantity\":1,\"extensionElements\":{\"postAssignmentScript\":[\"var valid = 'bpmext.ui.getView(\\\"vLibrary\\\")';\\r\\n\\r\\n\\/\\/Customer name\\r\\nvalid.mandatory(tw.local.customerName);\\r\\n\\r\\n\\/\\/Customer age\\r\\nvalid.mandatory(tw.local.customerAge);\\r\\n\\r\\n\\/\\/Cif\\r\\nvalid.exactLength(tw.local.CIF,8)\\r\\n\\r\\n\\/\\/Request Date\\r\\nvalid.dateBetween(tw.local.requestDate,tw.local.startDate,tw.local.endDate )\"],\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":478,\"y\":23,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\/\\/Customer name\\r\\nif(tw.local.customerName == null || tw.local.customerName == \\\"\\\"){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.customerName\\\",\\\"Customer Name is mandatory\\\")\\r\\n}\\r\\n\\r\\n\\/\\/Customer age\\r\\nif(tw.local.customerAge == null || tw.local.customerAge == 0){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.customerAge\\\", \\\"Customer Age must be > 0\\\")\\r\\n}\\r\\n\\r\\n\\/\\/Cif\\r\\nif(tw.local.CIF == null || tw.local.CIF == \\\"\\\"){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.CIF\\\", \\\"CIF is mandatory\\\")\\r\\n}\\r\\n\\r\\nif(tw.local.CIF.length != 8){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.CIF\\\", \\\"CIF must be 8 digits\\\")\\r\\n}\\r\\n\\r\\n\\/\\/Request Date\\r\\nif (tw.local.requestDate > tw.local.startDate && tw.local.requestDate < tw.local.endDate \\r\\n\\t&& tw.local.requestDate != null) {\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.requestDate\\\",\\\"Request Date is mandatory and must be between start and end dates\\\")\\r\\n};\"]},\"name\":\"New Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.8ce9ed09-4d95-4688-81a3-a6e189e008eb\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/customer name\\r\\nif(tw.local.customerName == null || tw.local.customerName == \\\"\\\"){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.customerName\\\", \\\"Customer Name is mandatory\\\")\\r\\n}\\r\\n\\r\\n\\/\\/customer age\\r\\nif(tw.local.customerAge == null || tw.local.customerAge == 0){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.customerAge\\\", \\\"Customer Age must be > 0\\\")\\r\\n}\\r\\n\\r\\n\\/\\/cif\\r\\nif(tw.local.CIF == null || tw.local.CIF == \\\"\\\"){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.CIF\\\", \\\"CIF is mandatory\\\")\\r\\n}\\r\\n\\r\\nif(tw.local.CIF.length != 8){\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.CIF\\\", \\\"CIF must be 8 digits\\\")\\r\\n}\"]}},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"customerName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c80a5c77-964f-4ef1-8ec3-4601ac243ec9\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"customerAge\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9de7968f-8a1d-41e0-87f6-d64068eeb5aa\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"CIF\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.75e11ea6-5dda-46e3-8cc1-edc9093e1f30\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"customerAccount\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.58313f34-4ff6-44ce-84cc-026605c97add\"},{\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"requestDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c8a5b8e5-712e-4df5-848d-6b9a2a45c45f\"},{\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"startDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.09c3c291-bf80-43ba-8acc-d999e897ba5b\"},{\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"endDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.88c1048c-5ea3-4f97-87d2-d0639c67644a\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"32d510ac-3753-414d-938a-3a7f974f8718\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"f950e516-1519-4d53-b5ca-6b5d7adf6d7a\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Compare Validations\",\"declaredType\":\"globalUserTask\",\"id\":\"1.9395be71-805c-4443-85ee-48c92de214a3\",\"ioSpecification\":{\"inputSet\":[{\"id\":\"_4e8d5c2d-8409-4654-9bd1-d0f7dd826658\"}],\"outputSet\":[{\"id\":\"_fa550b44-fb50-4664-adf9-d32fb1072901\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"f74ce18a-b74a-44e8-bc21-247c7d4b1475\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processVariable": [{"name": "customerName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c80a5c77-964f-4ef1-8ec3-4601ac243ec9", "description": {"isNull": "true"}, "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fd88f8be-d370-491c-973f-e018487eeed5", "versionId": "d322c29b-ee3d-4350-bacb-dcbc65ba88d0"}, {"name": "customerAge", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9de7968f-8a1d-41e0-87f6-d64068eeb5aa", "description": {"isNull": "true"}, "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "af538deb-6a4e-460a-b91a-1e534b845840", "versionId": "6c11d987-7e23-48a2-840f-360c72f6b2a4"}, {"name": "CIF", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.75e11ea6-5dda-46e3-8cc1-edc9093e1f30", "description": {"isNull": "true"}, "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3aed8caf-13b3-488c-a528-32689be7230c", "versionId": "c64ca835-b42f-464c-9bd2-33abc328de20"}, {"name": "customerAccount", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.58313f34-4ff6-44ce-84cc-026605c97add", "description": {"isNull": "true"}, "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a4393c46-dcb8-4d1d-be57-b6339e8fd3c2", "versionId": "f8238f0c-fcbb-4e58-b509-48a20c290308"}, {"name": "requestDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c8a5b8e5-712e-4df5-848d-6b9a2a45c45f", "description": {"isNull": "true"}, "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "de1f7607-d0ff-4e44-9a94-1bb9b69aaa17", "versionId": "357e96c0-601a-47f4-8c25-145299d18c5c"}, {"name": "startDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.09c3c291-bf80-43ba-8acc-d999e897ba5b", "description": {"isNull": "true"}, "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c0a7f3e3-f452-4600-9807-d2d817dc1c5a", "versionId": "23d54f33-2397-4631-b222-bf8e3c17f7dc"}, {"name": "endDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.88c1048c-5ea3-4f97-87d2-d0639c67644a", "description": {"isNull": "true"}, "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "30d6c5bf-2fa4-42db-b1ec-33959aafa630", "versionId": "1cb8584f-5f34-4e61-b8e3-8fa1ae70aeed"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.76677e00-39c7-456d-89e1-0b5a6e716f72", "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.ea483930-9088-40d9-8c08-e26c4bc7f7d4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:709ea623f6099cbd:-68e5c70f:18d7fd15bbe:-113d", "versionId": "47d1f2f7-a955-4bbd-a9af-9008720e1d09", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.ea483930-9088-40d9-8c08-e26c4bc7f7d4", "haltProcess": "false", "guid": "6d1bdd85-4c9a-4302-a695-e8467b69acf1", "versionId": "eace4120-2a41-4e7a-8f6c-67542e0abfc7"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.437b16b6-7958-49ae-8979-c49775de8994", "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.f2f463b8-7189-4ad5-bb62-a233ef09560d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:709ea623f6099cbd:-68e5c70f:18d7fd15bbe:-113c", "versionId": "fbdc662f-62b9-40a3-b3ab-e31b4950eed1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "f74ce18a-b74a-44e8-bc21-247c7d4b1475", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "Compare Validations", "id": "1.9395be71-805c-4443-85ee-48c92de214a3", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "f950e516-1519-4d53-b5ca-6b5d7adf6d7a", "ns16:startEvent": {"name": "Start", "id": "b9b0e024-13d2-4774-b0da-09a799289095", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "50", "y": "200", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.fd9b62fb-9a45-48f2-bbc2-fb17612b7fbc"}, "ns3:formTask": {"name": "Coach", "id": "2025.f16b3fc6-d351-4a83-aabd-62aa62edb4d6", "ns16:extensionElements": {"ns3:validationStayOnPagePaths": "okbutton", "ns13:nodeVisualInfo": {"x": "350", "y": "177", "width": "95", "height": "70"}}, "ns16:incoming": "2027.fd9b62fb-9a45-48f2-bbc2-fb17612b7fbc", "ns16:outgoing": "2027.5e34d060-07cd-415b-b740-1b36b2120ee6", "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "ns19:id": "39a9ab55-2cb2-4eeb-8882-aeb7523ef9ca", "ns19:layoutItemId": "okbutton", "ns19:configData": [{"ns19:id": "90aa7f61-abe1-47c9-b0cd-49144a103550", "ns19:optionName": "@bpmDefaultCreated", "ns19:value": "true"}, {"ns19:id": "b814369a-8c08-4255-b09a-6fbb7ae7e87a", "ns19:optionName": "@label", "ns19:value": "OK"}], "ns19:viewUUID": "64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36"}}}}}, "ns16:endEvent": {"name": "End", "id": "f9d9753b-c760-4d2d-9a54-25ec0c91bba0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "700", "y": "200", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.5e34d060-07cd-415b-b740-1b36b2120ee6"}, "ns16:sequenceFlow": [{"sourceRef": "b9b0e024-13d2-4774-b0da-09a799289095", "targetRef": "2025.f16b3fc6-d351-4a83-aabd-62aa62edb4d6", "name": "To Coach", "id": "2027.fd9b62fb-9a45-48f2-bbc2-fb17612b7fbc", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.f16b3fc6-d351-4a83-aabd-62aa62edb4d6", "targetRef": "f9d9753b-c760-4d2d-9a54-25ec0c91bba0", "name": "To End", "id": "2027.5e34d060-07cd-415b-b740-1b36b2120ee6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "073defd7-071b-4393-9756-571829dd8888", "ns3:coachEventPath": "okbutton"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "New Validation", "id": "2025.8ce9ed09-4d95-4688-81a3-a6e189e008eb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "478", "y": "23", "width": "95", "height": "70", "color": "#95D087"}, "ns3:postAssignmentScript": "var valid = 'bpmext.ui.getView(\"vLibrary\")';\r\r\n\r\r\n//Customer name\r\r\nvalid.mandatory(tw.local.customerName);\r\r\n\r\r\n//Customer age\r\r\nvalid.mandatory(tw.local.customerAge);\r\r\n\r\r\n//Cif\r\r\nvalid.exactLength(tw.local.CIF,8)\r\r\n\r\r\n//Request Date\r\r\nvalid.dateBetween(tw.local.requestDate,tw.local.startDate,tw.local.endDate )", "ns3:preAssignmentScript": "//Customer name\r\r\nif(tw.local.customerName == null || tw.local.customerName == \"\"){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.customerName\",\"Customer Name is mandatory\")\r\r\n}\r\r\n\r\r\n//Customer age\r\r\nif(tw.local.customerAge == null || tw.local.customerAge == 0){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.customerAge\", \"Customer Age must be > 0\")\r\r\n}\r\r\n\r\r\n//Cif\r\r\nif(tw.local.CIF == null || tw.local.CIF == \"\"){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.CIF\", \"CIF is mandatory\")\r\r\n}\r\r\n\r\r\nif(tw.local.CIF.length != 8){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.CIF\", \"CIF must be 8 digits\")\r\r\n}\r\r\n\r\r\n//Request Date\r\r\nif (tw.local.requestDate > tw.local.startDate && tw.local.requestDate < tw.local.endDate \r\r\n\t&& tw.local.requestDate != null) {\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.requestDate\",\"Request Date is mandatory and must be between start and end dates\")\r\r\n};"}, "ns16:script": "//customer name\r\r\nif(tw.local.customerName == null || tw.local.customerName == \"\"){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.customerName\", \"Customer Name is mandatory\")\r\r\n}\r\r\n\r\r\n//customer age\r\r\nif(tw.local.customerAge == null || tw.local.customerAge == 0){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.customerAge\", \"Customer Age must be > 0\")\r\r\n}\r\r\n\r\r\n//cif\r\r\nif(tw.local.CIF == null || tw.local.CIF == \"\"){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.CIF\", \"CIF is mandatory\")\r\r\n}\r\r\n\r\r\nif(tw.local.CIF.length != 8){\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.CIF\", \"CIF must be 8 digits\")\r\r\n}"}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "customerName", "id": "2056.c80a5c77-964f-4ef1-8ec3-4601ac243ec9"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "customerAge", "id": "2056.9de7968f-8a1d-41e0-87f6-d64068eeb5aa"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "CIF", "id": "2056.75e11ea6-5dda-46e3-8cc1-edc9093e1f30"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "customerAccount", "id": "2056.58313f34-4ff6-44ce-84cc-026605c97add"}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "requestDate", "id": "2056.c8a5b8e5-712e-4df5-848d-6b9a2a45c45f"}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "startDate", "id": "2056.09c3c291-bf80-43ba-8acc-d999e897ba5b"}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "endDate", "id": "2056.88c1048c-5ea3-4f97-87d2-d0639c67644a"}], "ns3:htmlHeaderTag": {"id": "32d510ac-3753-414d-938a-3a7f974f8718", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:inputSet": {"id": "_4e8d5c2d-8409-4654-9bd1-d0f7dd826658"}, "ns16:outputSet": {"id": "_fa550b44-fb50-4664-adf9-d32fb1072901"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.3ccd5ae2-3ae1-4879-be9a-0daa662c3ca5", "processId": "1.9395be71-805c-4443-85ee-48c92de214a3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.437b16b6-7958-49ae-8979-c49775de8994", "2025.437b16b6-7958-49ae-8979-c49775de8994"], "endStateId": "Out", "toProcessItemId": ["2025.76677e00-39c7-456d-89e1-0b5a6e716f72", "2025.76677e00-39c7-456d-89e1-0b5a6e716f72"], "guid": "c3b6880f-501b-4ac9-b30c-eea2d83dcb0f", "versionId": "8602e4ca-af51-44ba-9699-30298fb6320e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}