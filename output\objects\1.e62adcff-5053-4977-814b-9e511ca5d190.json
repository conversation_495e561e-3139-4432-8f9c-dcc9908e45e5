{"id": "1.e62adcff-5053-4977-814b-9e511ca5d190", "versionId": "998f25b5-9684-4c61-abb0-a378b8c9bec3", "name": "retrieve Odc request Data oneToMany Relation", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "retrieve Odc request Data oneToMany Relation", "lastModified": "1697362454660", "lastModifiedBy": "heba", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.33ad9010-0eb1-445c-89c3-fa6974a294e7", "2025.33ad9010-0eb1-445c-89c3-fa6974a294e7"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a20", "versionId": "998f25b5-9684-4c61-abb0-a378b8c9bec3", "dependencySummary": "<dependencySummary id=\"bpdid:aff098473ecd546d:1d42df0a:18b291cb73a:-cd3\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.c5c6f0c8-8260-4d5b-8ace-26ba5937c783\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"5cbd49ec-94fb-413d-8f6a-51eeba385223\"},{\"incoming\":[\"00823ac4-c665-40d3-8ef5-************\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":850,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:cdc758d910e20d2d:-607ed7:18af686ab01:a22\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64\"},{\"targetRef\":\"33ad9010-0eb1-445c-89c3-fa6974a294e7\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c5c6f0c8-8260-4d5b-8ace-26ba5937c783\",\"sourceRef\":\"5cbd49ec-94fb-413d-8f6a-51eeba385223\"},{\"startQuantity\":1,\"outgoing\":[\"add8e598-05fd-40a5-8a15-52c4f2f4907a\"],\"incoming\":[\"2027.c5c6f0c8-8260-4d5b-8ace-26ba5937c783\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":87,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"get Request Id\",\"dataInputAssociation\":[{\"targetRef\":\"2055.672337bc-52de-4e70-8a73-ea1f063ea6dd\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestNumber\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"33ad9010-0eb1-445c-89c3-fa6974a294e7\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestId\"]}}],\"sourceRef\":[\"2055.1e1809fd-304b-42d0-85fe-5c668a0c101d\"]}],\"calledElement\":\"1.4855448a-1c86-4167-afa5-d79d3273d48b\"},{\"startQuantity\":1,\"outgoing\":[\"1cf63648-2b26-478a-8564-82e5b976fbae\"],\"incoming\":[\"add8e598-05fd-40a5-8a15-52c4f2f4907a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":225,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Retrieve Invoice Data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestId\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"5017a0ea-0fd4-4a56-8b15-33c9900e7a49\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.9d46896d-4f98-437e-8770-a36bbe647f2d\"]}],\"calledElement\":\"1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5\"},{\"startQuantity\":1,\"outgoing\":[\"e16f223a-fe21-454c-8501-2a7420141df6\"],\"incoming\":[\"1cf63648-2b26-478a-8564-82e5b976fbae\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":365,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Retrieve Bills Data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.6ff52d30-eb38-49af-8675-b5853cb88523\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestId\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"4d722248-dc8c-43f4-8217-f1878aa86408\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.b6ac116b-6326-4d97-8b93-e99cfc87b708\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1\"]}],\"calledElement\":\"1.f2b5748f-56bf-4336-a74c-88aafd159a23\"},{\"startQuantity\":1,\"outgoing\":[\"00823ac4-c665-40d3-8ef5-************\"],\"incoming\":[\"b1d72b69-d581-4e88-8da5-f353af61856e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":637,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Retrieve Charges and commision Data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestId\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"0fb56800-3393-49c1-800c-dcd8cd08595d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.62e96889-79d7-4e51-9564-d2732313593f\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.09d42e03-72b8-46a0-882c-e4c44d810906\"]}],\"calledElement\":\"1.a446b0e9-d872-4881-895c-c2fb2f239829\"},{\"startQuantity\":1,\"outgoing\":[\"b1d72b69-d581-4e88-8da5-f353af61856e\"],\"incoming\":[\"e16f223a-fe21-454c-8501-2a7420141df6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":490,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Retrieve Multitenor Data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.2631c1a1-529d-4221-9643-ef723e7bb772\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestId\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"55b39391-e31b-40f7-8b7d-ee7b661ab01c\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.597c3250-25af-42c6-b65b-7d280ac3a896\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce\"]}],\"calledElement\":\"1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84\"},{\"targetRef\":\"5017a0ea-0fd4-4a56-8b15-33c9900e7a49\",\"extensionElements\":{\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aeac88b51:3d7a\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Retrieve Invoice Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"add8e598-05fd-40a5-8a15-52c4f2f4907a\",\"sourceRef\":\"33ad9010-0eb1-445c-89c3-fa6974a294e7\"},{\"targetRef\":\"4d722248-dc8c-43f4-8217-f1878aa86408\",\"extensionElements\":{\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6449\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Retrieve Bills Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"1cf63648-2b26-478a-8564-82e5b976fbae\",\"sourceRef\":\"5017a0ea-0fd4-4a56-8b15-33c9900e7a49\"},{\"targetRef\":\"55b39391-e31b-40f7-8b7d-ee7b661ab01c\",\"extensionElements\":{\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aeac88b51:49ac\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Retrieve Multitenor Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"e16f223a-fe21-454c-8501-2a7420141df6\",\"sourceRef\":\"4d722248-dc8c-43f4-8217-f1878aa86408\"},{\"targetRef\":\"0fb56800-3393-49c1-800c-dcd8cd08595d\",\"extensionElements\":{\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f3f\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Retrieve Charges and commision Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"b1d72b69-d581-4e88-8da5-f353af61856e\",\"sourceRef\":\"55b39391-e31b-40f7-8b7d-ee7b661ab01c\"},{\"targetRef\":\"51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4166\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"00823ac4-c665-40d3-8ef5-************\",\"sourceRef\":\"0fb56800-3393-49c1-800c-dcd8cd08595d\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"requestId\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1951f2b9-2fad-451e-8e8f-0a507ee74a9f\"}],\"laneSet\":[{\"id\":\"0fb793ab-abe5-4cac-8fce-f4d7bb3091be\",\"lane\":[{\"flowNodeRef\":[\"5cbd49ec-94fb-413d-8f6a-51eeba385223\",\"51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64\",\"33ad9010-0eb1-445c-89c3-fa6974a294e7\",\"5017a0ea-0fd4-4a56-8b15-33c9900e7a49\",\"4d722248-dc8c-43f4-8217-f1878aa86408\",\"0fb56800-3393-49c1-800c-dcd8cd08595d\",\"55b39391-e31b-40f7-8b7d-ee7b661ab01c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"f6aba104-c08d-451a-8c74-659456bc88d4\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"retrieve Odc request Data oneToMany Relation\",\"declaredType\":\"process\",\"id\":\"1.e62adcff-5053-4977-814b-9e511ca5d190\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.6ff165ef-cd1e-4b27-8678-e809adfd458d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.33b5dccf-**************-560ecde149f2\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.4c3318d7-370d-4d28-80de-b197f1a2aaec\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.6ff165ef-cd1e-4b27-8678-e809adfd458d\",\"2055.33b5dccf-**************-560ecde149f2\"]}],\"dataInput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestNumber\",\"isCollection\":false,\"id\":\"2055.4c3318d7-370d-4d28-80de-b197f1a2aaec\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4c3318d7-370d-4d28-80de-b197f1a2aaec", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "72311716-beb0-4534-845b-85d1a82a9f15", "versionId": "6043bf46-1287-478a-b2b1-f28df971844b"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6ff165ef-cd1e-4b27-8678-e809adfd458d", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "83921aa0-3512-4f23-8ec5-ac7120bbe9d1", "versionId": "5bf0618d-c409-4c55-8504-ee93427a021d"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.33b5dccf-**************-560ecde149f2", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "2c78c270-30b0-47cc-893a-4112a263d6f9", "versionId": "cf539f90-6f50-4008-9cf0-3269893cb8e9"}], "processVariable": {"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1951f2b9-2fad-451e-8e8f-0a507ee74a9f", "description": {"isNull": "true"}, "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a631e024-d3f7-4103-8a24-dd599cbec504", "versionId": "5a9d6019-cb2c-455f-80ad-7dc36271246c"}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.33ad9010-0eb1-445c-89c3-fa6974a294e7", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "get Request Id", "tWComponentName": "SubProcess", "tWComponentId": "3012.4f402dc0-94a1-446b-a010-75122d5e6280", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a29", "versionId": "2076d813-495e-4fdb-af1c-b574e8a0d1dc", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "87", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4f402dc0-94a1-446b-a010-75122d5e6280", "attachedProcessRef": "/1.4855448a-1c86-4167-afa5-d79d3273d48b", "guid": "6fcf387d-c134-443a-a721-c22c737cc973", "versionId": "f7d34cde-98f7-464f-9ced-97bb1727edec", "parameterMapping": [{"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c7b87a11-87b2-4768-829c-0d610dfbbf9e", "processParameterId": "2055.1e1809fd-304b-42d0-85fe-5c668a0c101d", "parameterMappingParentId": "3012.4f402dc0-94a1-446b-a010-75122d5e6280", "useDefault": "false", "value": "tw.local.requestId", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "false", "guid": "12ba9fdf-f5a7-4a03-8b90-898e471c9046", "versionId": "061a60d4-ef3e-450b-b066-868f33285006", "description": {"isNull": "true"}}, {"name": "requestNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7ce2cae3-24f8-4c9a-a403-79786d69b820", "processParameterId": "2055.672337bc-52de-4e70-8a73-ea1f063ea6dd", "parameterMappingParentId": "3012.4f402dc0-94a1-446b-a010-75122d5e6280", "useDefault": "false", "value": "tw.local.requestNumber", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "84c0db0c-45b3-4b3c-9b43-de292a208c74", "versionId": "35a3d2d8-9c3d-4b71-9f3c-24451c3a1309", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.0e76d8a5-bb6f-4004-9a65-d30d0beeab61", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a22", "versionId": "329a2784-e34e-489d-b513-e91b23b28514", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "850", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.0e76d8a5-bb6f-4004-9a65-d30d0beeab61", "haltProcess": "false", "guid": "e1ed169b-6cd3-4ce7-a193-a5dc01be67b8", "versionId": "dea7eddc-374f-487b-bfb4-7a4365b3a673"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.5017a0ea-0fd4-4a56-8b15-33c9900e7a49", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "Retrieve Invoice Data", "tWComponentName": "SubProcess", "tWComponentId": "3012.799f3786-1359-4565-a509-1867fa990556", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a2a", "versionId": "4f135592-fd37-452c-b101-8ef9294ab876", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "225", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.799f3786-1359-4565-a509-1867fa990556", "attachedProcessRef": "/1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5", "guid": "19071302-eae9-4478-973b-7f4f26156bd7", "versionId": "85f97428-8a58-468f-94b0-069f4d9ac52f", "parameterMapping": [{"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a13bac07-7c6e-4f4b-bf94-6f1545d0a1c3", "processParameterId": "2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a", "parameterMappingParentId": "3012.799f3786-1359-4565-a509-1867fa990556", "useDefault": "false", "value": "tw.local.requestId", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "6a21da97-2a23-464f-b552-b7bd70fd08f5", "versionId": "590ff9d6-fccc-45ca-8733-26ee88cc5aac", "description": {"isNull": "true"}}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e608c177-cbf8-41d5-9f7f-e3059da22725", "processParameterId": "2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd", "parameterMappingParentId": "3012.799f3786-1359-4565-a509-1867fa990556", "useDefault": "false", "value": "tw.local.odcRequest", "classRef": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isList": "false", "isInput": "false", "guid": "4d0c7e22-364f-4028-9fbb-6100a9824cde", "versionId": "92eda7c9-eb9b-40f9-8f7b-700efaec032f", "description": {"isNull": "true"}}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b36466e8-7fe2-4ff2-b3da-e87213015c11", "processParameterId": "2055.9d46896d-4f98-437e-8770-a36bbe647f2d", "parameterMappingParentId": "3012.799f3786-1359-4565-a509-1867fa990556", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "a6a539d2-e635-4627-96f2-6e49661e2089", "versionId": "96ace1ae-e6ad-415a-b075-bb2eda8c80bf", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0fb56800-3393-49c1-800c-dcd8cd08595d", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "Retrieve Charges and commision Data", "tWComponentName": "SubProcess", "tWComponentId": "3012.0710aea6-3a4b-42c1-8a11-308f40193d82", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a2d", "versionId": "6ea80117-0838-4b24-ac87-5b433ed63a47", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "637", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.0710aea6-3a4b-42c1-8a11-308f40193d82", "attachedProcessRef": "/1.a446b0e9-d872-4881-895c-c2fb2f239829", "guid": "aa97f6d6-b262-4ad3-9630-04f34cf8171d", "versionId": "737915e9-cd98-4aba-9e55-3586e10a9fcc", "parameterMapping": [{"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3f02d54a-1bb8-48dc-b155-cc36dadd1328", "processParameterId": "2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5", "parameterMappingParentId": "3012.0710aea6-3a4b-42c1-8a11-308f40193d82", "useDefault": "false", "value": "tw.local.requestId", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "f1c8015b-364a-4526-b5e9-da52ee7df3f3", "versionId": "bf4ca942-6b85-4079-93af-5687a61f07e2", "description": {"isNull": "true"}}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.cfc27324-775b-4879-9bb9-aa8bb1e180e6", "processParameterId": "2055.09d42e03-72b8-46a0-882c-e4c44d810906", "parameterMappingParentId": "3012.0710aea6-3a4b-42c1-8a11-308f40193d82", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "71582af5-72ff-4e56-b4d7-07be477e79c4", "versionId": "c72a5c38-308a-4cea-8922-8aaa1992964c", "description": {"isNull": "true"}}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3a878553-b87f-46d1-964c-6c4666c008fe", "processParameterId": "2055.62e96889-79d7-4e51-9564-d2732313593f", "parameterMappingParentId": "3012.0710aea6-3a4b-42c1-8a11-308f40193d82", "useDefault": "false", "value": "tw.local.odcRequest", "classRef": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isList": "false", "isInput": "false", "guid": "dfb2b481-a4e4-44b2-ade4-ba224e084a6c", "versionId": "d76ff205-30e6-4e57-8f05-98fb3f184f10", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.55b39391-e31b-40f7-8b7d-ee7b661ab01c", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "Retrieve Multitenor Data", "tWComponentName": "SubProcess", "tWComponentId": "3012.da952bef-534a-4192-80cc-9aa5d9f032da", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a2e", "versionId": "bea675e0-f9a5-4e35-bca6-5bd623f16dbe", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "490", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.da952bef-534a-4192-80cc-9aa5d9f032da", "attachedProcessRef": "/1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "guid": "b4fa8ec1-7e19-40ea-9ecf-5b68752a72cb", "versionId": "616cc5c7-6c95-43a9-8b33-f342d96dedec", "parameterMapping": [{"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e70b15a6-907c-4e79-abe4-08cef4d12993", "processParameterId": "2055.2631c1a1-529d-4221-9643-ef723e7bb772", "parameterMappingParentId": "3012.da952bef-534a-4192-80cc-9aa5d9f032da", "useDefault": "false", "value": "tw.local.requestId", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "2aeb60b9-c6a7-491a-906e-193443619969", "versionId": "31f7f7cb-f173-4d42-925f-377041595adb", "description": {"isNull": "true"}}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5305a5ea-5f94-4c33-9bb5-7f4431295e15", "processParameterId": "2055.597c3250-25af-42c6-b65b-7d280ac3a896", "parameterMappingParentId": "3012.da952bef-534a-4192-80cc-9aa5d9f032da", "useDefault": "false", "value": "tw.local.odcRequest", "classRef": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isList": "false", "isInput": "false", "guid": "04fa339f-2dae-45d3-a896-db055e3b1f87", "versionId": "8b1f8f77-f00b-44bf-9b83-8d1994dbdab8", "description": {"isNull": "true"}}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.54b458f5-be4a-4214-b7d9-c1f18a2e76d3", "processParameterId": "2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce", "parameterMappingParentId": "3012.da952bef-534a-4192-80cc-9aa5d9f032da", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "7e5a92a5-620c-4d9a-8d12-321bd4b62922", "versionId": "e3ade235-d735-4304-a7ad-7339e087cd73", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4d722248-dc8c-43f4-8217-f1878aa86408", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "name": "Retrieve Bills Data", "tWComponentName": "SubProcess", "tWComponentId": "3012.df1c2abd-9fa7-4994-8462-809744b7f6de", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a2b", "versionId": "c5269d8e-edbf-410f-aea0-2d9002902bfd", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "365", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.df1c2abd-9fa7-4994-8462-809744b7f6de", "attachedProcessRef": "/1.f2b5748f-56bf-4336-a74c-88aafd159a23", "guid": "87ae172a-bef0-4ada-b51b-80089e12613f", "versionId": "fd03d075-31b3-4b9c-86dd-6130549097aa", "parameterMapping": [{"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f1677e31-b6cb-4b51-890e-14be2e31bd94", "processParameterId": "2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1", "parameterMappingParentId": "3012.df1c2abd-9fa7-4994-8462-809744b7f6de", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "6d186172-e9e5-4c9b-855e-a61c56b53720", "versionId": "362da962-93cf-4110-8b9c-faf006b0e3c1", "description": {"isNull": "true"}}, {"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c0f90b1d-b046-49d9-a56e-4a64f2bb619e", "processParameterId": "2055.6ff52d30-eb38-49af-8675-b5853cb88523", "parameterMappingParentId": "3012.df1c2abd-9fa7-4994-8462-809744b7f6de", "useDefault": "false", "value": "tw.local.requestId", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "eebbf3e1-4ba0-40d4-abcd-278387c9b1fb", "versionId": "3921594a-3f28-4247-b327-2ef4670cc818", "description": {"isNull": "true"}}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.300b16e0-7ae2-47f3-ba98-bbd876d7d25a", "processParameterId": "2055.b6ac116b-6326-4d97-8b93-e99cfc87b708", "parameterMappingParentId": "3012.df1c2abd-9fa7-4994-8462-809744b7f6de", "useDefault": "false", "value": "tw.local.odcRequest", "classRef": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isList": "false", "isInput": "false", "guid": "a8fd8fe6-bf7f-41ca-ae20-cd36cd890349", "versionId": "54931af8-cc9f-42d0-ac67-b532604e37f7", "description": {"isNull": "true"}}]}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "retrieve Odc request Data oneToMany Relation", "id": "1.e62adcff-5053-4977-814b-9e511ca5d190", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "requestNumber", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.4c3318d7-370d-4d28-80de-b197f1a2aaec"}, "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.6ff165ef-cd1e-4b27-8678-e809adfd458d"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.33b5dccf-**************-560ecde149f2"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.4c3318d7-370d-4d28-80de-b197f1a2aaec"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.6ff165ef-cd1e-4b27-8678-e809adfd458d", "2055.33b5dccf-**************-560ecde149f2"]}}, "ns16:laneSet": {"id": "0fb793ab-abe5-4cac-8fce-f4d7bb3091be", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "f6aba104-c08d-451a-8c74-659456bc88d4", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["5cbd49ec-94fb-413d-8f6a-51eeba385223", "51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64", "33ad9010-0eb1-445c-89c3-fa6974a294e7", "5017a0ea-0fd4-4a56-8b15-33c9900e7a49", "4d722248-dc8c-43f4-8217-f1878aa86408", "0fb56800-3393-49c1-800c-dcd8cd08595d", "55b39391-e31b-40f7-8b7d-ee7b661ab01c"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "5cbd49ec-94fb-413d-8f6a-51eeba385223", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.c5c6f0c8-8260-4d5b-8ace-26ba5937c783"}, "ns16:endEvent": {"name": "End", "id": "51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "850", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:cdc758d910e20d2d:-607ed7:18af686ab01:a22"}, "ns16:incoming": "00823ac4-c665-40d3-8ef5-************"}, "ns16:sequenceFlow": [{"sourceRef": "5cbd49ec-94fb-413d-8f6a-51eeba385223", "targetRef": "33ad9010-0eb1-445c-89c3-fa6974a294e7", "name": "To End", "id": "2027.c5c6f0c8-8260-4d5b-8ace-26ba5937c783", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "33ad9010-0eb1-445c-89c3-fa6974a294e7", "targetRef": "5017a0ea-0fd4-4a56-8b15-33c9900e7a49", "name": "To Retrieve Invoice Data", "id": "add8e598-05fd-40a5-8a15-52c4f2f4907a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:3d7a"}}, {"sourceRef": "5017a0ea-0fd4-4a56-8b15-33c9900e7a49", "targetRef": "4d722248-dc8c-43f4-8217-f1878aa86408", "name": "To Retrieve Bills Data", "id": "1cf63648-2b26-478a-8564-82e5b976fbae", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6449"}}, {"sourceRef": "4d722248-dc8c-43f4-8217-f1878aa86408", "targetRef": "55b39391-e31b-40f7-8b7d-ee7b661ab01c", "name": "To Retrieve Multitenor Data", "id": "e16f223a-fe21-454c-8501-2a7420141df6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49ac"}}, {"sourceRef": "55b39391-e31b-40f7-8b7d-ee7b661ab01c", "targetRef": "0fb56800-3393-49c1-800c-dcd8cd08595d", "name": "To Retrieve Charges and commision Data", "id": "b1d72b69-d581-4e88-8da5-f353af61856e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f3f"}}, {"sourceRef": "0fb56800-3393-49c1-800c-dcd8cd08595d", "targetRef": "51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64", "name": "To End", "id": "00823ac4-c665-40d3-8ef5-************", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4166"}}], "ns16:callActivity": [{"calledElement": "1.4855448a-1c86-4167-afa5-d79d3273d48b", "name": "get Request Id", "id": "33ad9010-0eb1-445c-89c3-fa6974a294e7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "87", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "2027.c5c6f0c8-8260-4d5b-8ace-26ba5937c783", "ns16:outgoing": "add8e598-05fd-40a5-8a15-52c4f2f4907a", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.672337bc-52de-4e70-8a73-ea1f063ea6dd", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestNumber", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.1e1809fd-304b-42d0-85fe-5c668a0c101d", "ns16:assignment": {"ns16:to": {"_": "tw.local.requestId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.af8d5e03-f3aa-48cf-af3c-098395c8b0e5", "name": "Retrieve Invoice Data", "id": "5017a0ea-0fd4-4a56-8b15-33c9900e7a49", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "225", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "add8e598-05fd-40a5-8a15-52c4f2f4907a", "ns16:outgoing": "1cf63648-2b26-478a-8564-82e5b976fbae", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.94842cf1-d265-4a09-81ad-6f196d2a5b3a", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.e30fe1ac-f446-4e9f-b6d5-e0dd128558fd", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.9d46896d-4f98-437e-8770-a36bbe647f2d", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.f2b5748f-56bf-4336-a74c-88aafd159a23", "name": "Retrieve Bills Data", "id": "4d722248-dc8c-43f4-8217-f1878aa86408", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "365", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "1cf63648-2b26-478a-8564-82e5b976fbae", "ns16:outgoing": "e16f223a-fe21-454c-8501-2a7420141df6", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.6ff52d30-eb38-49af-8675-b5853cb88523", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.b6ac116b-6326-4d97-8b93-e99cfc87b708", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.dbe7d9b8-4412-45c1-81f8-d87cbd69eeb1", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "Retrieve Charges and commision Data", "id": "0fb56800-3393-49c1-800c-dcd8cd08595d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "637", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "b1d72b69-d581-4e88-8da5-f353af61856e", "ns16:outgoing": "00823ac4-c665-40d3-8ef5-************", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.62e96889-79d7-4e51-9564-d2732313593f", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.09d42e03-72b8-46a0-882c-e4c44d810906", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.09f617fd-bf72-4c24-aed3-3e0a5f42dc84", "name": "Retrieve Multitenor Data", "id": "55b39391-e31b-40f7-8b7d-ee7b661ab01c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "490", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "e16f223a-fe21-454c-8501-2a7420141df6", "ns16:outgoing": "b1d72b69-d581-4e88-8da5-f353af61856e", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.2631c1a1-529d-4221-9643-ef723e7bb772", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.597c3250-25af-42c6-b65b-7d280ac3a896", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.2bd48103-5f41-45dc-8b8f-f7feb3ae9cce", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:dataObject": {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "requestId", "id": "2056.1951f2b9-2fad-451e-8e8f-0a507ee74a9f"}}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.00823ac4-c665-40d3-8ef5-************", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.0fb56800-3393-49c1-800c-dcd8cd08595d", "2025.0fb56800-3393-49c1-800c-dcd8cd08595d"], "endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4166", "toProcessItemId": ["2025.51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64", "2025.51cdb3a9-9390-4ef0-8bb6-b713ea1a7a64"], "guid": "e3d47b15-ea70-40d0-aed7-5bbca343cdfb", "versionId": "3b6d765b-5e43-41bc-89b4-ce16187ff467", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Retrieve Invoice Data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.add8e598-05fd-40a5-8a15-52c4f2f4907a", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.33ad9010-0eb1-445c-89c3-fa6974a294e7", "2025.33ad9010-0eb1-445c-89c3-fa6974a294e7"], "endStateId": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:3d7a", "toProcessItemId": ["2025.5017a0ea-0fd4-4a56-8b15-33c9900e7a49", "2025.5017a0ea-0fd4-4a56-8b15-33c9900e7a49"], "guid": "12e88d99-47e0-457d-a71e-19ffd0491e03", "versionId": "8c6b3d9e-de07-416a-beae-afa2709b6cfd", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Retrieve Multitenor Data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e16f223a-fe21-454c-8501-2a7420141df6", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4d722248-dc8c-43f4-8217-f1878aa86408", "2025.4d722248-dc8c-43f4-8217-f1878aa86408"], "endStateId": "guid:f57c3a34823ee126:-91461cc:18aeac88b51:49ac", "toProcessItemId": ["2025.55b39391-e31b-40f7-8b7d-ee7b661ab01c", "2025.55b39391-e31b-40f7-8b7d-ee7b661ab01c"], "guid": "d4bb5035-115c-4596-b5a1-cc96cb9c479e", "versionId": "923a026c-944b-45ff-ae46-15285c4335f3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Retrieve Charges and commision Data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.b1d72b69-d581-4e88-8da5-f353af61856e", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.55b39391-e31b-40f7-8b7d-ee7b661ab01c", "2025.55b39391-e31b-40f7-8b7d-ee7b661ab01c"], "endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-5f3f", "toProcessItemId": ["2025.0fb56800-3393-49c1-800c-dcd8cd08595d", "2025.0fb56800-3393-49c1-800c-dcd8cd08595d"], "guid": "03826c4b-b705-46ce-a1dc-a218a0dee792", "versionId": "9a9e4274-dece-4382-9a53-9e5713b1cd50", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Retrieve Bills Data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1cf63648-2b26-478a-8564-82e5b976fbae", "processId": "1.e62adcff-5053-4977-814b-9e511ca5d190", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.5017a0ea-0fd4-4a56-8b15-33c9900e7a49", "2025.5017a0ea-0fd4-4a56-8b15-33c9900e7a49"], "endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-6449", "toProcessItemId": ["2025.4d722248-dc8c-43f4-8217-f1878aa86408", "2025.4d722248-dc8c-43f4-8217-f1878aa86408"], "guid": "69852be4-c4df-405e-a3fa-51913c4fa830", "versionId": "9fe4d6db-352d-4c7a-b0bf-31e3879753ed", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}