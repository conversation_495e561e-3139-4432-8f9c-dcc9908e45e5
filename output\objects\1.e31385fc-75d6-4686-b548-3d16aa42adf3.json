{"id": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "versionId": "99d1d41f-38c8-4a04-b60c-56bdd6a0e721", "name": "ACT02 Review ODC Request By Compliance Rep", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "regeneratedRemittanceLetterTitleVIS", "hasDefault": false, "type": "1"}, {"name": "lastAction", "hasDefault": false, "type": "1"}, {"name": "role", "hasDefault": false, "type": "1"}, {"name": "parentPath", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "errorPanelVIS", "hasDefault": false}, {"name": "checkValid", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "deliveryTerms", "hasDefault": false}, {"name": "paymentTerms", "hasDefault": false}, {"name": "specialInstructions", "hasDefault": false}, {"name": "instructions", "hasDefault": false}, {"name": "parentRequestNoVIS", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "requestTypeVIS", "hasDefault": false}, {"name": "error", "hasDefault": false}, {"name": "requestIdStr", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Review ODC Request By Compliance", "id": "2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "History", "id": "2025.d6e6e891-944d-46b2-84d8-bdecb8b81130", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Update request state and status in DB", "id": "2025.e525aa08-63da-4864-834f-77cb967dfb2d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit ODC Request", "id": "2025.4e33c805-8685-4d73-8f6a-fcaf0f3bd02d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Cancel and Delete Transactions", "id": "2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel request", "id": "2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "valid?", "id": "2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited?", "id": "2025.8f99d806-a261-46e7-8d06-a3e1591c764c", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited?", "id": "2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel?", "id": "2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Exclusive Gateway", "id": "2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "init script", "id": "2025.753f8b21-2aa2-4771-8d95-6dc4fa71a0da", "script": "tw.local.errorPanelVIS=\"NONE\";\r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime =new Date();\r\r\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT02;\r\r\ntw.local.odcRequest.stepLog.action =\"\";\r\r\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.CACT02;\r\r\n\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT02;\r\r\ntw.local.actionConditions.userRole= tw.epv.userRole.CACT02;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.lastAction;\r\r\n\r\r\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\r\r\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\r\r\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\r\r\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\r\n\ttw.local.parentRequestNoVIS = \"Readonly\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\n/*Document Generation Section*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.requestTypeVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.requestTypeVIS = \"NONE\";\t\r\r\n}\r\r\n\r\r\ntw.local.errorPanelVIS =\"NONE\";\r\r\n\r\r\nif(tw.local.role == tw.epv.userRole.branch )\r\r\n{\r\r\n\ttw.local.role = tw.epv.userRole.branchComp;\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.role = tw.epv.userRole.hubComp;\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Validation", "id": "2025.2129f97a-fb4c-4164-842b-83bc1718efd0", "script": "tw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\nvar tempLength = 0 ;\r\r\n\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tif (field < 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\tvar msg= \"Invalid Value, This field can not be negative value.\";\r\r\n\t\t\t\t\taddError(fieldName , msg , msg , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/* =====================\r\r\n* |\tVALIDATE HERE   |\r\r\n* =====================\r\r\n*/\r\r\n\r\r\n//Actions Validation\r\r\nmandatory(tw.local.odcRequest.stepLog.action,\"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action== tw.epv.CreationActions.returnToInitiator){\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason,\"tw.local.odcRequest.stepLog.returnReason\");\r\r\n} \r\r\n\r\r\n\r\r\n\r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Setting status and substatus", "id": "2025.2fb97341-8137-491c-8d2b-9547f19280fb", "script": "if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.approveRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Trade FO Review\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"Initiated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Returned to Initiator\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \" Canceled\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \" Canceled\";\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "ACT02 Review ODC Request By Compliance Rep", "lastModified": "1700638511161", "lastModifiedBy": "so<PERSON>ia", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.67d12eee-cc85-45c9-b76d-04f1e433af93", "2025.67d12eee-cc85-45c9-b76d-04f1e433af93"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:ef0d56db9ddf1554:-58d13970:189a74c1378:-3707", "versionId": "99d1d41f-38c8-4a04-b60c-56bdd6a0e721", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.e39db3f2-1389-445c-8839-3e30b3064291\"],\"isInterrupting\":true,\"extensionElements\":{\"default\":[\"2027.e39db3f2-1389-445c-8839-3e30b3064291\"],\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":152,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"b4a35fa2-599a-4dec-a080-921482e41c3d\"},{\"outgoing\":[\"2027.fa18eb70-96fc-4b57-839c-591e8f979ff5\",\"2027.eb0b5a58-59e8-42c5-8251-092133493c91\"],\"incoming\":[\"2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd\",\"2027.cc1745b6-77d2-4169-859a-397c2f92cebb\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":350,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"],\"preAssignmentScript\":[]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"18eac37c-43ff-48f6-8ccb-0b4f9ee7a843\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c5b8b14f-df33-4108-8889-4684261b7343\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ab78c9ab-4cbe-4d83-8abd-fabadf4f4a52\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6af6b18e-60c3-4e42-813e-8436e0b66b22\",\"optionName\":\"basicDetailsVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"015cc7e1-9689-40ee-8998-021c1cad1027\",\"optionName\":\"requestVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cbde409d-def7-4a13-8436-4f020547a29f\",\"optionName\":\"requestTypeVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4ef8d5bf-cf5c-4109-8682-36ce5e9e30ee\",\"optionName\":\"parentRequestNoVis\",\"value\":\"tw.local.parentRequestNoVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2f003041-adaa-4cc7-8576-b42f93601d57\",\"optionName\":\"contractStageVIS\",\"value\":\"tw.local.contractStageVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"00a70c0d-eb3a-48f1-8f28-5229301da248\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"24ba0fd7-f7fb-4dc6-8d05-0b3c582bd5c5\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"None\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"9e6973ce-29c1-4c48-869f-4db2202df8b0\",\"version\":\"8550\"},{\"layoutItemId\":\"Document_Generation_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7b824cd1-0f9d-4957-816d-a98a47493bf3\",\"optionName\":\"@label\",\"value\":\"Letter Generation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8f0602f2-37db-43ff-8790-83fed96fe552\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"61622fff-dcd0-44c9-82af-cb1ee36b341a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"75c97b3a-fde7-4ab8-895e-3a22aa75899d\",\"optionName\":\"documentGeneratonVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d61a1fca-448b-4071-8ff0-039a8a346322\",\"optionName\":\"remittanceLetterTitleVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cc8a6032-6949-445f-87de-23c4f2d705b8\",\"optionName\":\"regeneratedRemittanceSelectionVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0f380d74-40e4-4d7a-8d96-6b0fcc65e864\",\"optionName\":\"regeneratedRemittanceSectionVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e66ce3cc-e39d-4c69-8998-cc582d61b7ce\",\"optionName\":\"instructions\",\"value\":\"tw.local.instructions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"96e7bd2a-71fd-44a1-8a53-ff5adf716fc9\",\"optionName\":\"specialInstructions\",\"value\":\"tw.local.specialInstructions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"74ff7e42-072d-4167-8009-eee7bfcc1771\",\"optionName\":\"paymentTerms\",\"value\":\"tw.local.paymentTerms\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e50fa3b1-0da7-40fc-8349-eeb581f8e9ef\",\"optionName\":\"deliveryTerms\",\"value\":\"tw.local.deliveryTerms\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"947cd0eb-ac59-4704-82ee-81557dfed886\",\"optionName\":\"DocumentGenerationVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"eda7032d-d994-441f-860e-4984472522d1\",\"optionName\":\"regeneratedRemittanceLetterTitleVIS\",\"value\":\"tw.local.regeneratedRemittanceLetterTitleVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4fb20ffd-8099-4110-8dcd-930520da7fdb\",\"optionName\":\"requestTypeVIS\",\"value\":\"tw.local.requestTypeVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"52ec0b7f-9c45-4660-8a07-6709799aa75f\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2cd5655a-ebf3-4a4c-83b4-2ae990981f91\",\"optionName\":\"remittanceLetterButton\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b71479a4-b0d2-4423-8c43-91774fdee286\",\"optionName\":\"documentGenerationVIS\",\"value\":\"ReadOnly\"}],\"viewUUID\":\"64.1d99aba8-195f-4eee-ba8c-a47926bc21e2\",\"binding\":\"tw.local.odcRequest.GeneratedDocumentInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"2fdd1e79-4efd-4cfb-8cd9-115bdfcb62bb\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f1d64ca8-398d-49e7-80e3-d4310537a3d8\",\"optionName\":\"@label\",\"value\":\"Customer Info\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d1415609-485f-4338-8f46-572cb32a19e8\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f6eb0495-c971-4d19-8caf-87e695a3c320\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"12552a89-411e-4248-8e59-fe9a44da5e37\",\"optionName\":\"customerInfoVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ad4a8bef-166b-45ff-838b-a8b18cbd1cf2\",\"optionName\":\"requestTypeVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"abed13d6-f8eb-4d4d-8a45-705296ae4028\",\"optionName\":\"listsVIS\",\"value\":\"None\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"bb1111fb-96de-4d05-8e4a-f3673bc6e066\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"07ce3a2c-cd10-4506-83e0-6f12ccbe4a84\",\"optionName\":\"@label\",\"value\":\"Financial Details Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"33dc3b79-2071-47be-80c9-4f06ccb59af8\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6982c46b-7aea-45bf-8e27-615a4654a105\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d02e8ff5-281a-40ad-8360-8385373acce0\",\"optionName\":\"financialDetailsVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"120b498c-d9c4-47f9-8283-7cb2e7239558\",\"optionName\":\"FccollectionVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"720c5701-a461-4a1f-8654-48e0663300c8\",\"optionName\":\"requestTradeFoVis\",\"value\":\"Read only\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"73d0b567-9b51-471d-86fe-d2dc165d5017\",\"optionName\":\"financialDetailsVis\",\"value\":\"Read only\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c33a98a7-b3ca-44cd-840d-c5c9dade8dfa\",\"optionName\":\"fcCollectionVis\",\"value\":\"Read only\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"21162225-3f41-4ea1-8084-d6dd9626c1b3\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a5b46fbc-ebb2-4369-8443-9c741f79dd01\",\"optionName\":\"documentAmountVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"139b2ef1-6b26-46cf-8f27-b94f393df11a\",\"optionName\":\"currencyVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cac6b239-f6b7-43f0-8b06-026ef751a215\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"272e7939-257d-40b3-8828-d2b449991869\",\"version\":\"8550\"},{\"layoutItemId\":\"FC_Collections_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c7fbc6b6-8890-482f-8f06-cb401b7e90f9\",\"optionName\":\"@label\",\"value\":\"FC Collections\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"56fcd145-b707-47f5-8050-0cc303b7a469\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"63f055c0-8db5-47db-8b18-6faa8f584873\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"74437ce6-a36c-474a-89f2-44dd3909c847\",\"optionName\":\"FCVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b6e8f7f1-de43-4cc2-8223-7d55d2b1659d\",\"optionName\":\"retrieveBtnVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"84016cc6-1319-4053-87ba-ef81d77d1527\",\"optionName\":\"addBtnVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"22cfe25d-**************-c09cfcaab520\",\"optionName\":\"collectionCurrencyVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97ef5c9d-abf8-4421-84de-db31dcb26da7\",\"optionName\":\"negotiatedExchangeRateVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4aa53177-c053-4451-8a74-ba81c9dac987\",\"optionName\":\"activityType\",\"value\":\"read\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b2863196-a632-47b1-8576-6ea3a5f2468d\",\"optionName\":\"customerCif\",\"value\":\"tw.local.odcRequest.cif\"}],\"viewUUID\":\"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe\",\"binding\":\"tw.local.odcRequest.FcCollections\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"05d849bd-4ecf-4da6-847f-b51e589f1da4\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e5168e71-1e74-4031-8d4d-024c9077aaa6\",\"optionName\":\"@label\",\"value\":\"Attachment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d8f6972b-b51b-45fb-809d-db1a3cf7aad2\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"228f77fd-73ca-4cdb-8db1-0af921aa8aff\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5f2482ff-90b3-4132-8967-acad5158d0b1\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2b3cd279-b789-4872-85e4-ca92bf7d49ba\",\"optionName\":\"remittanceLetterPath\",\"value\":\"tw.local.odcRequest.folderPath\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"367f8958-cc51-4fc2-8e30-2d88e3948023\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d407c32c-88a6-4123-895a-a58bba671957\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f96429b4-0324-4280-85ba-9c5fb3b6d6ca\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a98e659f-b8a1-4545-8fdd-2d3d801c816e\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"bf624b41-3744-4a19-8f6e-e40ecd214aea\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"190aae32-2fea-4ecd-887a-e33dc8db7e9e\"}],\"layoutItemId\":\"Tab_section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"456a1908-65a6-42a0-8e87-78fce36d1db0\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3f4db105-a258-4efc-8238-fcbe15bbeceb\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5075810f-2938-4ed9-876c-1f5cfeef6601\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"390c6506-dbf0-4594-80f2-04442cded26f\",\"optionName\":\"sizeStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"X\\\"}]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8d837aab-b788-49cf-87c6-37fb6970b432\",\"optionName\":\"tabsStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"S\\\"}]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f529d6d4-1f7e-45b1-8448-ec9c64d5f9bf\",\"optionName\":\"colorStyle\",\"value\":\"P\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"5b1cc906-85ec-46c1-81e5-766770a4d1dc\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"a1f04d10-0c90-48a7-8693-ef97d8871565\"}],\"layoutItemId\":\"Vertical_layout1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4c8c6d79-28f3-4ea4-88c0-99f9bfac0c1f\",\"optionName\":\"@label\",\"value\":\"Vertical layout\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9203d36a-3cd2-4422-8383-47fa90e22fff\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fccc347f-c656-4754-8837-6c650ac84989\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"03e57609-d945-4be3-8641-8439361ebe52\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"2bf44e52-313f-4220-8280-4410d86dd5f7\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d4abaf3f-1494-48ca-8919-11f2a651c917\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c26924ba-5421-4e60-8e4e-8ed0f590e4bf\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3484027e-62ae-4b47-852e-4886a639a7a3\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"17283d70-cda1-457b-83ff-8612b2aebf59\",\"optionName\":\"buttonName\",\"value\":\"Submit\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b5ed0da9-4235-488a-844d-4587f2f9d309\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3c4a1d2c-c478-48f3-8fa3-b2cf32ada4d2\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ec0610bd-6286-4476-8657-2c7163b0496e\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"dec515f9-db53-447a-80f1-c6f6a059bba5\",\"optionName\":\"complianceApprovalVis\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fc435399-0f8c-4ea7-822d-bda36fcf7484\",\"optionName\":\"complianceApproval\",\"value\":\"false\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c2e490c8-f4fc-4a97-80d5-976d43f8b26f\",\"optionName\":\"returnReasonVIS\",\"value\":\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7af2b62b-9d6a-43d1-867a-d9232db7278d\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"27af4219-c953-4de6-8c72-59e5eaad067f\",\"optionName\":\"errorPanelVIS\",\"value\":\"tw.local.errorPanelVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a7bf6b77-cd71-438d-81c9-0f166e3d8285\",\"optionName\":\"terminateReasonVIS\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3973a016-a64d-411a-8384-ceb948826a5a\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7bbd1047-7da3-4385-870e-c9c24acc31a0\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ff2cd7ea-5e51-4aa6-8d48-d4d8751a47cc\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b742699e-49b9-45af-8a0d-c9e97814ec77\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"None\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fe0ee2f3-23de-4dc9-8d5f-c961f89d916c\",\"optionName\":\"exeHubMkrComment\",\"value\":\"tw.local.odcRequest.exeHubMkrComment\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"763f54a8-1d6b-4207-8255-07f2aac8e64b\",\"optionName\":\"tradeFoComment\",\"value\":\"tw.local.odcRequest.tradeFoComment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6f546227-c075-4ec0-865f-9ab2400012ec\",\"optionName\":\"compcheckerCommentVIS\",\"value\":\"None\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"edd9d6ec-b4d1-4c3d-8e00-eb6ea54781ff\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Review ODC Request By Compliance\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577\"},{\"incoming\":[\"2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3\",\"2027.60a2073a-**************-de2ae02cb32b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1970,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":43}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"04cbeb26-4ea0-4196-8bfe-aa11c42d6d18\"},{\"targetRef\":\"2025.2129f97a-fb4c-4164-842b-83bc1718efd0\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"78930829-ac0c-42b6-8a0b-4e5db2d54bc9\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fa18eb70-96fc-4b57-839c-591e8f979ff5\",\"sourceRef\":\"2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577\"},{\"outgoing\":[\"2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd\"],\"incoming\":[\"2027.eb0b5a58-59e8-42c5-8251-092133493c91\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":371,\"y\":10,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.9f687095-7947-4995-831a-394ad760b1da\"},{\"targetRef\":\"2025.9f687095-7947-4995-831a-394ad760b1da\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"744a14ca-bfff-4bf1-8b1e-1be1542b3ba1\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topLeft\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.eb0b5a58-59e8-42c5-8251-092133493c91\",\"sourceRef\":\"2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577\"},{\"targetRef\":\"2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Review ODC Request By Compliance\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd\",\"sourceRef\":\"2025.9f687095-7947-4995-831a-394ad760b1da\"},{\"targetRef\":\"2025.753f8b21-2aa2-4771-8d95-6dc4fa71a0da\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Actions\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e39db3f2-1389-445c-8839-3e30b3064291\",\"sourceRef\":\"b4a35fa2-599a-4dec-a080-921482e41c3d\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.82d232e5-161c-4df4-8e64-f0f5205025b0\"},{\"outgoing\":[\"2027.bdf49d86-d0ba-49e6-8c8b-9e0347f027f7\",\"2027.096a244b-997c-4de4-8baa-e4b767198c6b\"],\"incoming\":[\"2027.cfcef02b-0e08-4959-891c-1e719e6b44d2\"],\"default\":\"2027.096a244b-997c-4de4-8baa-e4b767198c6b\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":714,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db\"},{\"targetRef\":\"2025.2fb97341-8137-491c-8d2b-9547f19280fb\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To cancel?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.bdf49d86-d0ba-49e6-8c8b-9e0347f027f7\",\"sourceRef\":\"2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db\"},{\"incoming\":[\"2027.096a244b-997c-4de4-8baa-e4b767198c6b\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":703,\"y\":87,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.85ba1202-9d8d-4d7d-872c-68be670af17b\"},{\"targetRef\":\"2025.85ba1202-9d8d-4d7d-872c-68be670af17b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.096a244b-997c-4de4-8baa-e4b767198c6b\",\"sourceRef\":\"2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorPanelVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.fa3aafa5-8bc2-4e85-8a68-31d7700a76c3\"},{\"startQuantity\":1,\"outgoing\":[\"2027.cc1745b6-77d2-4169-859a-397c2f92cebb\"],\"incoming\":[\"2027.e39db3f2-1389-445c-8839-3e30b3064291\"],\"default\":\"2027.cc1745b6-77d2-4169-859a-397c2f92cebb\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":220,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"init script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.753f8b21-2aa2-4771-8d95-6dc4fa71a0da\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.errorPanelVIS=\\\"NONE\\\";\\r\\ntw.local.odcRequest.stepLog={};\\r\\ntw.local.odcRequest.stepLog.startTime =new Date();\\r\\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT02;\\r\\ntw.local.odcRequest.stepLog.action =\\\"\\\";\\r\\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.CACT02;\\r\\n\\r\\n\\r\\ntw.local.actionConditions = {};\\r\\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\\r\\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT02;\\r\\ntw.local.actionConditions.userRole= tw.epv.userRole.CACT02;\\r\\ntw.local.actionConditions.lastStepAction= tw.local.lastAction;\\r\\n\\r\\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\\r\\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\\r\\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\\r\\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\\r\\n\\/*Visibilty Conditions*\\/\\r\\n\\/*Basic Details CV Visibility*\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nif(tw.local.odcRequest.requestNature.value == \\\"update\\\"){\\r\\n\\ttw.local.parentRequestNoVIS = \\\"Readonly\\\";\\r\\n}\\t\\r\\nelse{\\r\\n\\ttw.local.parentRequestNoVIS = \\\"None\\\";\\r\\n}\\t\\r\\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\\r\\n\\ttw.local.contractStageVIS = \\\"READONLY\\\";\\r\\n}\\r\\nelse{\\r\\ntw.local.contractStageVIS = \\\"NONE\\\";\\r\\n}\\r\\n\\/*Document Generation Section*\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\\r\\n\\ttw.local.requestTypeVIS = \\\"EDITABLE\\\";\\r\\n}\\r\\nelse{\\r\\n\\ttw.local.requestTypeVIS = \\\"NONE\\\";\\t\\r\\n}\\r\\n\\r\\ntw.local.errorPanelVIS =\\\"NONE\\\";\\r\\n\\r\\nif(tw.local.role == tw.epv.userRole.branch )\\r\\n{\\r\\n\\ttw.local.role = tw.epv.userRole.branchComp;\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\ttw.local.role = tw.epv.userRole.hubComp;\\r\\n}\"]}},{\"targetRef\":\"2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Get Actions\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.cc1745b6-77d2-4169-859a-397c2f92cebb\",\"sourceRef\":\"2025.753f8b21-2aa2-4771-8d95-6dc4fa71a0da\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"checkValid\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.97e42b5a-af86-4eec-8778-255eeedbf3ac\"},{\"startQuantity\":1,\"outgoing\":[\"2027.cfcef02b-0e08-4959-891c-1e719e6b44d2\"],\"incoming\":[\"2027.fa18eb70-96fc-4b57-839c-591e8f979ff5\"],\"default\":\"2027.cfcef02b-0e08-4959-891c-1e719e6b44d2\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":540,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.2129f97a-fb4c-4164-842b-83bc1718efd0\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.errorMessage = \\\"\\\";\\r\\nvar mandatoryTriggered = false;\\r\\nvar tempLength = 0 ;\\r\\n\\r\\n\\/*\\r\\n* =========================================================================================================\\r\\n*  \\r\\n* Add a coach validation error \\r\\n* \\t\\t\\r\\n* EX:\\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\\r\\n*\\r\\n* =========================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n{\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\n\\r\\n\\/*\\r\\n* ==================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the field is null 'Mandatory'\\r\\n*\\t\\r\\n* EX:\\tnotNull(tw.local.name , 'tw.local.name')\\r\\n*\\r\\n* ==================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction mandatory(field , fieldName)\\r\\n{\\r\\n\\tif (field == null || field == undefined )\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\t\\t\\t\\r\\n\\t\\tswitch (typeof field)\\r\\n\\t\\t{\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tif (field < 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\tvar msg= \\\"Invalid Value, This field can not be negative value.\\\";\\r\\n\\t\\t\\t\\t\\taddError(fieldName , msg , msg , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\r\\n\\t\\t\\tdefault:\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\/\\/ VALIDATE DATE OBJECT\\r\\n\\t\\t\\t\\tif( field && field.getTime && isFinite(field.getTime()) ) {}\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\telse\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\t\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\/* =====================\\r\\n* |\\tVALIDATE HERE   |\\r\\n* =====================\\r\\n*\\/\\r\\n\\r\\n\\/\\/Actions Validation\\r\\nmandatory(tw.local.odcRequest.stepLog.action,\\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\r\\nif(tw.local.odcRequest.stepLog.action== tw.epv.CreationActions.returnToInitiator){\\r\\n\\tmandatory(tw.local.odcRequest.stepLog.returnReason,\\\"tw.local.odcRequest.stepLog.returnReason\\\");\\r\\n} \\r\\n\\r\\n\\r\\n\\r\\n\\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\\\"EDITABLE\\\": tw.local.errorPanelVIS =\\\"NONE\\\";\\t\\t\"]}},{\"targetRef\":\"2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.cfcef02b-0e08-4959-891c-1e719e6b44d2\",\"sourceRef\":\"2025.2129f97a-fb4c-4164-842b-83bc1718efd0\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1fcc6040-856e-44cf-81b7-2556dd93cad2\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"deliveryTerms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.056128d4-1a9a-45de-8841-5bef2404cbad\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"paymentTerms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.68892cf8-0371-4402-81c1-1c94565990e9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"specialInstructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6ff26a96-c2e0-4b52-81bc-0b795443090e\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"instructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.524c435f-bda8-485d-80e8-0d1e611a3178\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNoVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.f4caf637-1876-4023-8840-6bc9ec88a1f9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d260c6d5-1b33-4fc4-8da3-0f297a31ea79\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.67f1661b-bb43-43ba-88a4-3848f99abb74\"},{\"startQuantity\":1,\"outgoing\":[\"2027.d7f761d6-ce78-48b5-8088-4e528a82bae8\"],\"incoming\":[\"2027.bdf49d86-d0ba-49e6-8c8b-9e0347f027f7\"],\"default\":\"2027.d7f761d6-ce78-48b5-8088-4e528a82bae8\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":917,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Setting status and substatus\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.2fb97341-8137-491c-8d2b-9547f19280fb\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.approveRequest){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"In Approval\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Trade FO Review\\\";\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\"Initiated\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Returned to Initiator\\\";\\r\\n}\\r\\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest){\\r\\n\\ttw.local.odcRequest.appInfo.status    = \\\" Canceled\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\" Canceled\\\";\\r\\n}\\r\\n\\r\\n\"]}},{\"targetRef\":\"2025.e525aa08-63da-4864-834f-77cb967dfb2d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d7f761d6-ce78-48b5-8088-4e528a82bae8\",\"sourceRef\":\"2025.2fb97341-8137-491c-8d2b-9547f19280fb\"},{\"outgoing\":[\"2027.5cd898bb-4ef7-48a1-8806-60d452836cc8\"],\"incoming\":[\"2027.1346a5a5-3a6f-4401-804e-6eb207da049b\",\"2027.2865cdba-1f38-4b42-80ac-076f8213fa3b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1340,\"y\":179,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.5cd898bb-4ef7-48a1-8806-60d452836cc8\",\"name\":\"History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.role\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.d6e6e891-944d-46b2-84d8-bdecb8b81130\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"outgoing\":[\"2027.2865cdba-1f38-4b42-80ac-076f8213fa3b\"],\"incoming\":[\"2027.d7f761d6-ce78-48b5-8088-4e528a82bae8\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1028,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.2865cdba-1f38-4b42-80ac-076f8213fa3b\",\"name\":\"Update request state and status in DB\",\"dataInputAssociation\":[{\"targetRef\":\"2055.a84d91ec-e620-4417-85c4-7dd7db58ff31\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.c5194a72-2de4-483f-823c-47d5b98b572c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.3974caa7-9f10-4f46-8275-17080a25476e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.b05449cf-c459-4405-809c-888b00e3e968\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}]},{\"targetRef\":\"2055.b537f107-9e83-46e0-8979-6fe5796c7a7d\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.stepName\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.e525aa08-63da-4864-834f-77cb967dfb2d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.8e0cea15-236a-4b79-86a8-f93756a4ac86\"]}],\"calledElement\":\"1.2cab04cd-6063-4a13-b148-ec9788e07bf4\"},{\"targetRef\":\"2025.d6e6e891-944d-46b2-84d8-bdecb8b81130\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2865cdba-1f38-4b42-80ac-076f8213fa3b\",\"sourceRef\":\"2025.e525aa08-63da-4864-834f-77cb967dfb2d\"},{\"startQuantity\":1,\"outgoing\":[\"2027.f42a0127-5919-44a6-8721-ae94737df59a\"],\"default\":\"2027.f42a0127-5919-44a6-8721-ae94737df59a\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1146,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Audit ODC Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.4e33c805-8685-4d73-8f6a-fcaf0f3bd02d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.254cf8eb-2743-4c53-8c52-e51c8c22884e\"]}],\"calledElement\":\"1.7ee96dd0-834b-44cb-af41-b21585627e49\"},{\"outgoing\":[\"2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5\",\"2027.1346a5a5-3a6f-4401-804e-6eb207da049b\"],\"incoming\":[\"2027.f42a0127-5919-44a6-8721-ae94737df59a\"],\"default\":\"2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1241,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.8f99d806-a261-46e7-8d06-a3e1591c764c\"},{\"targetRef\":\"2025.8f99d806-a261-46e7-8d06-a3e1591c764c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f42a0127-5919-44a6-8721-ae94737df59a\",\"sourceRef\":\"2025.4e33c805-8685-4d73-8f6a-fcaf0f3bd02d\"},{\"incoming\":[\"2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1276,\"y\":308,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.57169d86-750d-4aa2-8b4c-d67256b6ec71\"},{\"targetRef\":\"2025.57169d86-750d-4aa2-8b4c-d67256b6ec71\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5\",\"sourceRef\":\"2025.8f99d806-a261-46e7-8d06-a3e1591c764c\"},{\"targetRef\":\"2025.d6e6e891-944d-46b2-84d8-bdecb8b81130\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1346a5a5-3a6f-4401-804e-6eb207da049b\",\"sourceRef\":\"2025.8f99d806-a261-46e7-8d06-a3e1591c764c\"},{\"outgoing\":[\"2027.18c44064-c807-4504-885b-10d194656bc8\",\"2027.3b13555b-6f21-4035-85af-1e18b0ae56e5\"],\"incoming\":[\"2027.5cd898bb-4ef7-48a1-8806-60d452836cc8\"],\"default\":\"2027.3b13555b-6f21-4035-85af-1e18b0ae56e5\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1441,\"y\":198,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26\"},{\"targetRef\":\"2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5cd898bb-4ef7-48a1-8806-60d452836cc8\",\"sourceRef\":\"2025.d6e6e891-944d-46b2-84d8-bdecb8b81130\"},{\"targetRef\":\"2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.18c44064-c807-4504-885b-10d194656bc8\",\"sourceRef\":\"2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26\"},{\"incoming\":[\"2027.3b13555b-6f21-4035-85af-1e18b0ae56e5\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1592,\"y\":300,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 2\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.54b0ec9e-79bb-4f36-8457-59b2bc9a6c62\"},{\"targetRef\":\"2025.54b0ec9e-79bb-4f36-8457-59b2bc9a6c62\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3b13555b-6f21-4035-85af-1e18b0ae56e5\",\"sourceRef\":\"2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26\"},{\"startQuantity\":1,\"outgoing\":[\"2027.db8ac187-7804-443b-87c9-4748945423d7\"],\"incoming\":[\"2027.7480e63c-48d1-4e7d-836c-a0be731df0b1\"],\"default\":\"2027.db8ac187-7804-443b-87c9-4748945423d7\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1550,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.requestIdStr = tw.local.odcRequest.requestID+\\\"\\\";\"]},\"name\":\"Cancel and Delete Transactions\",\"dataInputAssociation\":[{\"targetRef\":\"2055.8c25b7d2-65fe-4ffa-83d3-f054a2ad4209\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestIdStr\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7\",\"calledElement\":\"1.812db3ff-6589-474c-bcc5-21fde39e4d25\"},{\"outgoing\":[\"2027.7480e63c-48d1-4e7d-836c-a0be731df0b1\",\"2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3\"],\"incoming\":[\"2027.18c44064-c807-4504-885b-10d194656bc8\"],\"default\":\"2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1536,\"y\":196,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"cancel?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d\"},{\"targetRef\":\"2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.cancelRequest\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7480e63c-48d1-4e7d-836c-a0be731df0b1\",\"sourceRef\":\"2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d\"},{\"targetRef\":\"04cbeb26-4ea0-4196-8bfe-aa11c42d6d18\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Setting status and substatus\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3\",\"sourceRef\":\"2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d\"},{\"targetRef\":\"2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.db8ac187-7804-443b-87c9-4748945423d7\",\"sourceRef\":\"2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7\"},{\"outgoing\":[\"2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a\"],\"incoming\":[\"2027.db8ac187-7804-443b-87c9-4748945423d7\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":1695,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a\",\"name\":\"cancel request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.55a76aa1-e513-4fd3-835f-7fe160120af7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.77d2b3d6-2a2a-4520-ad08-31686157d431\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderID\"]}}]},{\"targetRef\":\"2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a\"]}],\"calledElement\":\"1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc\"},{\"targetRef\":\"2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Exclusive Gateway\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a\",\"sourceRef\":\"2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c9a0f084-2e55-4ef3-8ca3-e4b6f9cb64d6\"},{\"outgoing\":[\"2027.60a2073a-**************-de2ae02cb32b\",\"2027.7f3da394-2437-4c8b-8cf4-93cf02724b35\"],\"incoming\":[\"2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a\"],\"default\":\"2027.7f3da394-2437-4c8b-8cf4-93cf02724b35\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1835,\"y\":99,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2\"},{\"targetRef\":\"04cbeb26-4ea0-4196-8bfe-aa11c42d6d18\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.60a2073a-**************-de2ae02cb32b\",\"sourceRef\":\"2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2\"},{\"incoming\":[\"2027.7f3da394-2437-4c8b-8cf4-93cf02724b35\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1817,\"y\":-20,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 3\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.adce5ba4-173d-4f56-8555-a2ffb2732ec0\"},{\"targetRef\":\"2025.adce5ba4-173d-4f56-8555-a2ffb2732ec0\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Stay on page 3\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7f3da394-2437-4c8b-8cf4-93cf02724b35\",\"sourceRef\":\"2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestIdStr\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.35396035-ba93-4f02-854c-f355e284d2fa\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"3697938f-e5f6-439b-9298-537463c06376\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"d204569a-6fea-468e-b410-a5835f36c9d2\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"ACT02 Review ODC Request By Compliance Rep\",\"declaredType\":\"globalUserTask\",\"id\":\"1.e31385fc-75d6-4686-b548-3d16aa42adf3\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.22fa5dfa-ab1c-4768-82c7-52b63dfc9e88\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.e02ce3e0-e9af-4169-84e0-ae3aad48c2a0\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"c3072b24-6678-4c5a-8778-9e80acd9f3da\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"a026fa94-dec2-4f29-8e4d-e721a2fbfb24\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.769dc134-1d15-4dd4-a967-c5f61cf352dc\",\"epvProcessLinkId\":\"2a10141b-2c22-488d-8cfe-2bcb6d4f8134\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"e3a0c599-3598-4949-8588-afe78ec6e53d\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"8dff81df-1554-440f-8e43-1a6d4feba7e9\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"_87e1bc19-d9cc-4323-a410-c09c59b06099\"}],\"outputSet\":[{\"id\":\"_d427e326-8080-433e-9ade-d2af4f857284\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = {};\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = [];\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.MultiTenorDates = [];\\nautoObject.MultiTenorDates[0] = {};\\nautoObject.MultiTenorDates[0].date = new Date();\\nautoObject.MultiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.drawer = [];\\nautoObject.Parties.drawer[0] = {};\\nautoObject.Parties.drawer[0].id = \\\"\\\";\\nautoObject.Parties.drawer[0].name = \\\"\\\";\\nautoObject.Parties.drawer[0].country = \\\"\\\";\\nautoObject.Parties.drawer[0].language = \\\"\\\";\\nautoObject.Parties.drawer[0].reference = \\\"\\\";\\nautoObject.Parties.drawer[0].address1 = \\\"\\\";\\nautoObject.Parties.drawer[0].address2 = \\\"\\\";\\nautoObject.Parties.drawer[0].address3 = \\\"\\\";\\nautoObject.Parties.drawer[0].cif = \\\"\\\";\\nautoObject.Parties.Drawee = [];\\nautoObject.Parties.Drawee[0] = {};\\nautoObject.Parties.Drawee[0].id = \\\"\\\";\\nautoObject.Parties.Drawee[0].name = \\\"\\\";\\nautoObject.Parties.Drawee[0].country = \\\"\\\";\\nautoObject.Parties.Drawee[0].language = \\\"\\\";\\nautoObject.Parties.Drawee[0].reference = \\\"\\\";\\nautoObject.Parties.Drawee[0].address1 = \\\"\\\";\\nautoObject.Parties.Drawee[0].address2 = \\\"\\\";\\nautoObject.Parties.Drawee[0].address3 = \\\"\\\";\\nautoObject.Parties.Drawee[0].cif = \\\"\\\";\\nautoObject.Parties.collectingBank = [];\\nautoObject.Parties.collectingBank[0] = {};\\nautoObject.Parties.collectingBank[0].id = \\\"\\\";\\nautoObject.Parties.collectingBank[0].name = \\\"\\\";\\nautoObject.Parties.collectingBank[0].country = \\\"\\\";\\nautoObject.Parties.collectingBank[0].language = \\\"\\\";\\nautoObject.Parties.collectingBank[0].reference = \\\"\\\";\\nautoObject.Parties.collectingBank[0].address1 = \\\"\\\";\\nautoObject.Parties.collectingBank[0].address2 = \\\"\\\";\\nautoObject.Parties.collectingBank[0].address3 = \\\"\\\";\\nautoObject.Parties.collectingBank[0].cif = \\\"\\\";\\nautoObject.Parties.collectingBank[0].media = \\\"\\\";\\nautoObject.Parties.collectingBank[0].address = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed = [];\\nautoObject.Parties.accounteeCaseInNeed[0] = {};\\nautoObject.Parties.accounteeCaseInNeed[0].id = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].name = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].country = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].language = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].reference = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].address1 = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].address2 = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].address3 = \\\"\\\";\\nautoObject.Parties.accounteeCaseInNeed[0].cif = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = \\\"\\\";\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = \\\"\\\";\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.initiator = \\\"\\\";\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.69a1c013-d5ea-4d9f-8e7a-253a4c53ee83\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"regeneratedRemittanceLetterTitleVIS\",\"isCollection\":false,\"id\":\"2055.85bb1b3b-6aff-440d-8a91-036bad32bb3a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"lastAction\",\"isCollection\":false,\"id\":\"2055.06e83dcd-aab9-473a-8905-39ca48c61d56\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"role\",\"isCollection\":false,\"id\":\"2055.2447e3bc-7548-4065-8bbf-89b5de603e41\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.ac5cb29b-06ab-412f-8129-5bd7fb6dc779\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"5e8756bb-0583-4d14-9783-d2d527148195\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.69a1c013-d5ea-4d9f-8e7a-253a4c53ee83", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3c9f614e-564f-41dd-87f6-12306cb557aa", "versionId": "38d7f8ad-fce3-4725-ac24-51007d323f2b"}, {"name": "regeneratedRemittanceLetterTitleVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.85bb1b3b-6aff-440d-8a91-036bad32bb3a", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "cbb5b3dc-2654-4322-b5ab-0e51634d674c", "versionId": "18a9c4b3-c701-4226-b715-5a47334eb516"}, {"name": "lastAction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.06e83dcd-aab9-473a-8905-39ca48c61d56", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1299c779-3749-419d-9f5c-9d3ceb5a3a32", "versionId": "995ead69-73a8-48d4-842c-7293501f6c5a"}, {"name": "role", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2447e3bc-7548-4065-8bbf-89b5de603e41", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "c7ec204a-5d16-40f0-b8f1-29606ed33bc5", "versionId": "14c65453-768b-4835-bae5-fc3cb8e769f3"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ac5cb29b-06ab-412f-8129-5bd7fb6dc779", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "5ac2b782-b853-40f0-aa3b-bd0df2fcdf4a", "versionId": "757e75ce-0fee-4c82-93f9-8de8cebe0e58"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.22fa5dfa-ab1c-4768-82c7-52b63dfc9e88", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9681de2c-3388-4f40-a0de-91afd55cbdb8", "versionId": "422f5c60-a918-4dc7-9fde-99f738f7e50b"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.82d232e5-161c-4df4-8e64-f0f5205025b0", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "073b4afc-1842-4f77-b861-e491517c9a68", "versionId": "69aaee39-a5f5-4ae1-8e53-f3294b6c1f11"}, {"name": "errorPanelVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.fa3aafa5-8bc2-4e85-8a68-31d7700a76c3", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d30ba9b7-bb78-469a-941f-5b7cb3137e20", "versionId": "323f1c43-418b-4c0b-b65c-5e23303787dd"}, {"name": "checkValid", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.97e42b5a-af86-4eec-8778-255eeedbf3ac", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "289fd801-8eb2-46de-bacf-430fe39d2577", "versionId": "36194c91-0c10-43b5-bb65-94129f1228ea"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1fcc6040-856e-44cf-81b7-2556dd93cad2", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f30e6d41-1082-4924-89b9-7263296159b0", "versionId": "d9cce5f5-2f8e-4292-92d6-415e8c55efbe"}, {"name": "deliveryTerms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.056128d4-1a9a-45de-8841-5bef2404cbad", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "39f7084e-7e67-4ab5-8f36-e96a178835c1", "versionId": "73790f99-bea8-433c-8193-bd2f3a3c8ebd"}, {"name": "paymentTerms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.68892cf8-0371-4402-81c1-1c94565990e9", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ae5b3097-3145-4eb3-a531-df91deb72bd4", "versionId": "11c8dff4-9ef9-4b34-a2e9-88dff1f4b13a"}, {"name": "specialInstructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6ff26a96-c2e0-4b52-81bc-0b795443090e", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "8029772e-714b-4967-a082-707ca18595d4", "versionId": "fe4323c7-c177-4be1-bd02-10e4829c4944"}, {"name": "instructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.524c435f-bda8-485d-80e8-0d1e611a3178", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d3d9fb33-00ba-4a00-b3c8-6380c04d1788", "versionId": "93cd87c3-20df-4efd-a254-70cf1ed417fc"}, {"name": "parentRequestNoVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f4caf637-1876-4023-8840-6bc9ec88a1f9", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "2750a2ac-91ba-479c-b35a-478244321c31", "versionId": "27cd89db-0a5d-48ec-9006-003e74226cd1"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d260c6d5-1b33-4fc4-8da3-0f297a31ea79", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0b8f7851-77e2-470b-b5b2-abddc7bbe26d", "versionId": "bd5d8527-b780-41ae-9077-55a162c63539"}, {"name": "requestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.67f1661b-bb43-43ba-88a4-3848f99abb74", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9f2e87a9-2454-4378-a9a0-e297310fb525", "versionId": "68b46628-38c7-4f2e-bd27-4205e49c1b1a"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c9a0f084-2e55-4ef3-8ca3-e4b6f9cb64d6", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "12", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ec570a4a-e546-4ef4-bc8a-4dc41d281036", "versionId": "e04a1b75-5290-485b-96ea-f8e1e14a1acb"}, {"name": "requestIdStr", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.35396035-ba93-4f02-854c-f355e284d2fa", "description": {"isNull": "true"}, "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "namespace": "2", "seq": "13", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5e00eaf7-83f5-4d74-8b6e-e189eb105a41", "versionId": "28f447ac-76d2-4bc3-bf14-714c92d0f7a0"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d6e6e891-944d-46b2-84d8-bdecb8b81130", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "History", "tWComponentName": "SubProcess", "tWComponentId": "3012.7271fca6-8efd-41d6-af8f-846c9a2cd079", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:415d794a2c221205:3dfd662b:18a3676f5a2:-746b", "versionId": "139342ab-b197-445e-b9d8-a5313351e958", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.7271fca6-8efd-41d6-af8f-846c9a2cd079", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "ced963d9-4078-4476-a44e-e40d32333c3a", "versionId": "6caad5af-35ae-4d4e-a76e-e5dd8ecf7c7b"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "cancel request", "tWComponentName": "SubProcess", "tWComponentId": "3012.fc7bb786-9432-4bc4-93c5-ab0c5dabd6f5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:7762", "versionId": "170c6d49-a96d-4225-b99e-5b70715c9b30", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.fc7bb786-9432-4bc4-93c5-ab0c5dabd6f5", "attachedProcessRef": "/1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "guid": "71bda529-7af9-45ee-a773-db3e0e8d518c", "versionId": "e2173754-806d-4621-b511-4b13cdc660a9"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e525aa08-63da-4864-834f-77cb967dfb2d", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "Update request state and status in DB", "tWComponentName": "SubProcess", "tWComponentId": "3012.8ebd9a1a-7a37-46a1-ad7b-0a8472a490a5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:aff098473ecd546d:1d42df0a:18b1b2b8841:-77af", "versionId": "5462526a-5729-4016-b61f-2deda59716dc", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.8ebd9a1a-7a37-46a1-ad7b-0a8472a490a5", "attachedProcessRef": "/1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "guid": "7fa44f9c-ed91-411d-ac62-736635ebafef", "versionId": "a62d8b16-840d-4f24-ac03-b10f420aad2f"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.418aab6d-2e41-47f8-a66b-01450b4c4fc6", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.490ee548-02f2-4623-b74d-1b07caef395d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ef0d56db9ddf1554:-58d13970:189a74c1378:-3706", "versionId": "b0b63664-f730-437d-86a6-cc28fa67b0d9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.490ee548-02f2-4623-b74d-1b07caef395d", "haltProcess": "false", "guid": "40d06a5d-3c57-4d4d-91e4-a577936467c4", "versionId": "82b45a1a-28b7-4644-a043-012d4f14502e"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "Cancel and Delete Transactions", "tWComponentName": "SubProcess", "tWComponentId": "3012.63693ee5-569b-4b14-b0ea-4ff6e3eef760", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3041", "versionId": "c944616c-7e4f-4e37-8c53-a317409fdff1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.63693ee5-569b-4b14-b0ea-4ff6e3eef760", "attachedProcessRef": "/1.812db3ff-6589-474c-bcc5-21fde39e4d25", "guid": "e6ea5ac1-76ed-45da-8ddc-f059d9d7e355", "versionId": "c3e62e78-9df1-47ac-9eb5-780873f30d2b"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.67d12eee-cc85-45c9-b76d-04f1e433af93", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.d3e1a82f-b359-4a10-a416-18fdeb70ab9e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ef0d56db9ddf1554:-58d13970:189a74c1378:-3705", "versionId": "df7f2e59-1faf-4a46-bde9-0917e5a1b621", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4e33c805-8685-4d73-8f6a-fcaf0f3bd02d", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "name": "Audit ODC Request", "tWComponentName": "SubProcess", "tWComponentId": "3012.98721c66-5770-4357-ba66-ec329246e22e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:2770", "versionId": "e95431bf-edee-4e3b-ae3f-8d2e411c8a74", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.98721c66-5770-4357-ba66-ec329246e22e", "attachedProcessRef": "/1.7ee96dd0-834b-44cb-af41-b21585627e49", "guid": "0850e2b1-11b2-4c77-9b38-229e31783891", "versionId": "b314f083-4015-4344-96a9-b914b9640700"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.1f594a80-d412-4acd-9244-3b5533849936", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "guid": "96203f43-296a-4014-98f7-10c6bde7070a", "versionId": "4196e4e0-01ca-4298-99e6-48d436246bcc"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.50ae317c-cbc6-4413-a075-b94d87e122df", "epvId": "/21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "guid": "3f0e7018-a069-4593-82e7-cad690bdf7ac", "versionId": "ada5db19-18e9-40bc-a516-d5c4bf1e6439"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.06b03afc-7dab-490b-9567-5b4d5b47f5ce", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "guid": "d18c720d-1704-48ec-827b-9d12dacb25fc", "versionId": "b122fcd4-c96d-4653-b074-a0b0cbf55bcc"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.fd552ca5-cc3a-4d70-81bd-8e9af66ded61", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "guid": "abda2c3b-876a-4fec-9816-9df05f378eb1", "versionId": "e2ea69e4-3efe-470f-a151-ba0c44df4490"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.6c48ccc1-ac51-4574-af7d-9102a6749a2d", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "guid": "cadad9c4-086e-4b20-a0ec-538d5f3a1c91", "versionId": "e9dbc091-0b9d-4bf9-b341-63d6a6dc5714"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.98412736-6b39-4fb5-a930-4ff7562d53c5", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "guid": "a7b1fd22-84de-4a58-949f-0c881382d150", "versionId": "f2853811-0a81-42cf-bc62-da5f2fc8910e"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "5e8756bb-0583-4d14-9783-d2d527148195", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "ACT02 Review ODC Request By Compliance Rep", "id": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "d204569a-6fea-468e-b410-a5835f36c9d2", "ns16:startEvent": {"name": "Start", "id": "b4a35fa2-599a-4dec-a080-921482e41c3d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "152", "y": "200", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:default": "2027.e39db3f2-1389-445c-8839-3e30b3064291"}, "ns16:outgoing": "2027.e39db3f2-1389-445c-8839-3e30b3064291"}, "ns3:formTask": {"name": "Review ODC Request By Compliance", "id": "2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577", "ns16:extensionElements": {"ns3:validationStayOnPagePaths": "okbutton", "ns13:nodeVisualInfo": {"x": "350", "y": "177", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": ["2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd", "2027.cc1745b6-77d2-4169-859a-397c2f92cebb"], "ns16:outgoing": ["2027.fa18eb70-96fc-4b57-839c-591e8f979ff5", "2027.eb0b5a58-59e8-42c5-8251-092133493c91"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "edd9d6ec-b4d1-4c3d-8e00-eb6ea54781ff", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "d4abaf3f-1494-48ca-8919-11f2a651c917", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "c26924ba-5421-4e60-8e4e-8ed0f590e4bf", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "3484027e-62ae-4b47-852e-4886a639a7a3", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "17283d70-cda1-457b-83ff-8612b2aebf59", "ns19:optionName": "buttonName", "ns19:value": "Submit"}, {"ns19:id": "b5ed0da9-4235-488a-844d-4587f2f9d309", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "3c4a1d2c-c478-48f3-8fa3-b2cf32ada4d2", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "ec0610bd-6286-4476-8657-2c7163b0496e", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "dec515f9-db53-447a-80f1-c6f6a059bba5", "ns19:optionName": "complianceApprovalVis", "ns19:value": "NONE"}, {"ns19:id": "fc435399-0f8c-4ea7-822d-bda36fcf7484", "ns19:optionName": "complianceApproval", "ns19:value": "false"}, {"ns19:id": "c2e490c8-f4fc-4a97-80d5-976d43f8b26f", "ns19:optionName": "returnReasonVIS", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "7af2b62b-9d6a-43d1-867a-d9232db7278d", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "27af4219-c953-4de6-8c72-59e5eaad067f", "ns19:optionName": "errorPanelVIS", "ns19:value": "tw.local.errorPanelVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "a7bf6b77-cd71-438d-81c9-0f166e3d8285", "ns19:optionName": "terminateReasonVIS", "ns19:value": "NONE"}, {"ns19:id": "3973a016-a64d-411a-8384-ceb948826a5a", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "7bbd1047-7da3-4385-870e-c9c24acc31a0", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "ff2cd7ea-5e51-4aa6-8d48-d4d8751a47cc", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "None"}, {"ns19:id": "b742699e-49b9-45af-8a0d-c9e97814ec77", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "None"}, {"ns19:id": "fe0ee2f3-23de-4dc9-8d5f-c961f89d916c", "ns19:optionName": "exeHubMkrComment", "ns19:value": "tw.local.odcRequest.exeHubMkrComment", "ns19:valueType": "dynamic"}, {"ns19:id": "763f54a8-1d6b-4207-8255-07f2aac8e64b", "ns19:optionName": "tradeFoComment", "ns19:value": "tw.local.odcRequest.tradeFoComment", "ns19:valueType": "dynamic"}, {"ns19:id": "6f546227-c075-4ec0-865f-9ab2400012ec", "ns19:optionName": "compcheckerCommentVIS", "ns19:value": "None"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "2bf44e52-313f-4220-8280-4410d86dd5f7", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "03e57609-d945-4be3-8641-8439361ebe52", "ns19:layoutItemId": "Vertical_layout1", "ns19:configData": [{"ns19:id": "4c8c6d79-28f3-4ea4-88c0-99f9bfac0c1f", "ns19:optionName": "@label", "ns19:value": "Vertical layout"}, {"ns19:id": "9203d36a-3cd2-4422-8383-47fa90e22fff", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "fccc347f-c656-4754-8837-6c650ac84989", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67", "ns19:contentBoxContrib": {"ns19:id": "a1f04d10-0c90-48a7-8693-ef97d8871565", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "5b1cc906-85ec-46c1-81e5-766770a4d1dc", "ns19:layoutItemId": "Tab_section1", "ns19:configData": [{"ns19:id": "456a1908-65a6-42a0-8e87-78fce36d1db0", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "3f4db105-a258-4efc-8238-fcbe15<PERSON>ceb", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "5075810f-2938-4ed9-876c-1f5cfeef6601", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "390c6506-dbf0-4594-80f2-04442cded26f", "ns19:optionName": "sizeStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}, {"ns19:id": "8d837aab-b788-49cf-87c6-37fb6970b432", "ns19:optionName": "tabsStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"}, {"ns19:id": "f529d6d4-1f7e-45b1-8448-ec9c64d5f9bf", "ns19:optionName": "colorStyle", "ns19:value": "P"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "190aae32-2fea-4ecd-887a-e33dc8db7e9e", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "9e6973ce-29c1-4c48-869f-4db2202df8b0", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "18eac37c-43ff-48f6-8ccb-0b4f9ee7a843", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "c5b8b14f-df33-4108-8889-4684261b7343", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "ab78c9ab-4cbe-4d83-8abd-fabadf4f4a52", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "6af6b18e-60c3-4e42-813e-8436e0b66b22", "ns19:optionName": "basicDetailsVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "015cc7e1-9689-40ee-8998-021c1cad1027", "ns19:optionName": "requestVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "cbde409d-def7-4a13-8436-4f020547a29f", "ns19:optionName": "requestTypeVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "4ef8d5bf-cf5c-4109-8682-36ce5e9e30ee", "ns19:optionName": "parentRequestNoVis", "ns19:value": "tw.local.parentRequestNoVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "2f003041-adaa-4cc7-8576-b42f93601d57", "ns19:optionName": "contractStageVIS", "ns19:value": "tw.local.contractStageVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "00a70c0d-eb3a-48f1-8f28-5229301da248", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "24ba0fd7-f7fb-4dc6-8d05-0b3c582bd5c5", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "None"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "2fdd1e79-4efd-4cfb-8cd9-115bdfcb62bb", "ns19:layoutItemId": "Document_Generation_CV1", "ns19:configData": [{"ns19:id": "7b824cd1-0f9d-4957-816d-a98a47493bf3", "ns19:optionName": "@label", "ns19:value": "Letter Generation"}, {"ns19:id": "8f0602f2-37db-43ff-8790-83fed96fe552", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "61622fff-dcd0-44c9-82af-cb1ee36b341a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "75c97b3a-fde7-4ab8-895e-3a22aa75899d", "ns19:optionName": "documentGeneratonVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "d61a1fca-448b-4071-8ff0-039a8a346322", "ns19:optionName": "remittanceLetterTitleVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "cc8a6032-6949-445f-87de-23c4f2d705b8", "ns19:optionName": "regeneratedRemittanceSelectionVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "0f380d74-40e4-4d7a-8d96-6b0fcc65e864", "ns19:optionName": "regeneratedRemittanceSectionVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "e66ce3cc-e39d-4c69-8998-cc582d61b7ce", "ns19:optionName": "instructions", "ns19:value": "tw.local.instructions", "ns19:valueType": "dynamic"}, {"ns19:id": "96e7bd2a-71fd-44a1-8a53-ff5adf716fc9", "ns19:optionName": "specialInstructions", "ns19:value": "tw.local.specialInstructions", "ns19:valueType": "dynamic"}, {"ns19:id": "74ff7e42-072d-4167-8009-eee7bfcc1771", "ns19:optionName": "paymentTerms", "ns19:value": "tw.local.paymentTerms", "ns19:valueType": "dynamic"}, {"ns19:id": "e50fa3b1-0da7-40fc-8349-eeb581f8e9ef", "ns19:optionName": "deliveryTerms", "ns19:value": "tw.local.deliveryTerms", "ns19:valueType": "dynamic"}, {"ns19:id": "947cd0eb-ac59-4704-82ee-81557dfed886", "ns19:optionName": "DocumentGenerationVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "eda7032d-d994-441f-860e-4984472522d1", "ns19:optionName": "regeneratedRemittanceLetterTitleVIS", "ns19:value": "tw.local.regeneratedRemittanceLetterTitleVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "4fb20ffd-8099-4110-8dcd-930520da7fdb", "ns19:optionName": "requestTypeVIS", "ns19:value": "tw.local.requestTypeVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "52ec0b7f-9c45-4660-8a07-6709799aa75f", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "2cd5655a-ebf3-4a4c-83b4-2ae990981f91", "ns19:optionName": "remittanceLetter<PERSON><PERSON>on", "ns19:value": "None"}, {"ns19:id": "b71479a4-b0d2-4423-8c43-91774fdee286", "ns19:optionName": "documentGenerationVIS", "ns19:value": "Read<PERSON>nly"}], "ns19:viewUUID": "64.1d99aba8-195f-4eee-ba8c-a47926bc21e2", "ns19:binding": "tw.local.odcRequest.GeneratedDocumentInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "bb1111fb-96de-4d05-8e4a-f3673bc6e066", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "f1d64ca8-398d-49e7-80e3-d4310537a3d8", "ns19:optionName": "@label", "ns19:value": "Customer Info"}, {"ns19:id": "d1415609-485f-4338-8f46-572cb32a19e8", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "f6eb0495-c971-4d19-8caf-87e695a3c320", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "12552a89-411e-4248-8e59-fe9a44da5e37", "ns19:optionName": "customerInfoVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "ad4a8bef-166b-45ff-838b-a8b18cbd1cf2", "ns19:optionName": "requestTypeVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "abed13d6-f8eb-4d4d-8a45-705296ae4028", "ns19:optionName": "listsVIS", "ns19:value": "None"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "272e7939-257d-40b3-8828-d2b449991869", "ns19:layoutItemId": "Financial_Details_Branch1", "ns19:configData": [{"ns19:id": "07ce3a2c-cd10-4506-83e0-6f12ccbe4a84", "ns19:optionName": "@label", "ns19:value": "Financial Details Branch"}, {"ns19:id": "33dc3b79-2071-47be-80c9-4f06ccb59af8", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "6982c46b-7aea-45bf-8e27-615a4654a105", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "d02e8ff5-281a-40ad-8360-8385373acce0", "ns19:optionName": "financialDetailsVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "120b498c-d9c4-47f9-8283-7cb2e7239558", "ns19:optionName": "FccollectionVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "720c5701-a461-4a1f-8654-48e0663300c8", "ns19:optionName": "requestTradeFoVis", "ns19:value": "Read only"}, {"ns19:id": "73d0b567-9b51-471d-86fe-d2dc165d5017", "ns19:optionName": "financialDetailsVis", "ns19:value": "Read only"}, {"ns19:id": "c33a98a7-b3ca-44cd-840d-c5c9dade8dfa", "ns19:optionName": "fcCollectionVis", "ns19:value": "Read only"}, {"ns19:id": "21162225-3f41-4ea1-8084-d6dd9626c1b3", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "a5b46fbc-ebb2-4369-8443-9c741f79dd01", "ns19:optionName": "documentAmountVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "139b2ef1-6b26-46cf-8f27-b94f393df11a", "ns19:optionName": "currencyVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "cac6b239-f6b7-43f0-8b06-026ef751a215", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "05d849bd-4ecf-4da6-847f-b51e589f1da4", "ns19:layoutItemId": "FC_Collections_CV1", "ns19:configData": [{"ns19:id": "c7fbc6b6-8890-482f-8f06-cb401b7e90f9", "ns19:optionName": "@label", "ns19:value": "FC Collections"}, {"ns19:id": "56fcd145-b707-47f5-8050-0cc303b7a469", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "63f055c0-8db5-47db-8b18-6faa8f584873", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "74437ce6-a36c-474a-89f2-44dd3909c847", "ns19:optionName": "FCVIS", "ns19:value": "READONLY"}, {"ns19:id": "b6e8f7f1-de43-4cc2-8223-7d55d2b1659d", "ns19:optionName": "retrieveBtnVis", "ns19:value": "None"}, {"ns19:id": "84016cc6-1319-4053-87ba-ef81d77d1527", "ns19:optionName": "addBtnVIS", "ns19:value": "None"}, {"ns19:id": "22cfe25d-**************-c09cfcaab520", "ns19:optionName": "collectionCurrencyVIS", "ns19:value": "READONLY"}, {"ns19:id": "97ef5c9d-abf8-4421-84de-db31dcb26da7", "ns19:optionName": "negotiatedExchangeRateVIS", "ns19:value": "READONLY"}, {"ns19:id": "4aa53177-c053-4451-8a74-ba81c9dac987", "ns19:optionName": "activityType", "ns19:value": "read"}, {"ns19:id": "b2863196-a632-47b1-8576-6ea3a5f2468d", "ns19:optionName": "customerCif", "ns19:value": "tw.local.odcRequest.cif", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "ns19:binding": "tw.local.odcRequest.FcCollections"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "367f8958-cc51-4fc2-8e30-2d88e3948023", "ns19:layoutItemId": "Attachment1", "ns19:configData": [{"ns19:id": "e5168e71-1e74-4031-8d4d-024c9077aaa6", "ns19:optionName": "@label", "ns19:value": "Attachment"}, {"ns19:id": "d8f6972b-b51b-45fb-809d-db1a3cf7aad2", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "228f77fd-73ca-4cdb-8db1-0af921aa8aff", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "5f2482ff-90b3-4132-8967-acad5158d0b1", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "2b3cd279-b789-4872-85e4-ca92bf7d49ba", "ns19:optionName": "remittanceLetter<PERSON>ath", "ns19:value": "tw.local.odcRequest.folderPath", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "bf624b41-3744-4a19-8f6e-e40ecd214aea", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "d407c32c-88a6-4123-895a-a58bba671957", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "f96429b4-0324-4280-85ba-9c5fb3b6d6ca", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "a98e659f-b8a1-4545-8fdd-2d3d801c816e", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}}}, "ns16:endEvent": {"name": "End", "id": "04cbeb26-4ea0-4196-8bfe-aa11c42d6d18", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1970", "y": "200", "width": "24", "height": "43", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3", "2027.60a2073a-**************-de2ae02cb32b"]}, "ns16:sequenceFlow": [{"sourceRef": "2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577", "targetRef": "2025.2129f97a-fb4c-4164-842b-83bc1718efd0", "name": "To End", "id": "2027.fa18eb70-96fc-4b57-839c-591e8f979ff5", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "78930829-ac0c-42b6-8a0b-4e5db2d54bc9", "ns3:coachEventPath": "DC_Templete1/submit"}}}, {"sourceRef": "2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577", "targetRef": "2025.9f687095-7947-4995-831a-394ad760b1da", "name": "To Postpone", "id": "2027.eb0b5a58-59e8-42c5-8251-092133493c91", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topLeft", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "744a14ca-bfff-4bf1-8b1e-1be1542b3ba1", "ns3:coachEventPath": "DC_Templete1/saveState"}}}, {"sourceRef": "2025.9f687095-7947-4995-831a-394ad760b1da", "targetRef": "2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577", "name": "To Review ODC Request By Compliance", "id": "2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "b4a35fa2-599a-4dec-a080-921482e41c3d", "targetRef": "2025.753f8b21-2aa2-4771-8d95-6dc4fa71a0da", "name": "To Get Actions", "id": "2027.e39db3f2-1389-445c-8839-3e30b3064291", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db", "targetRef": "2025.2fb97341-8137-491c-8d2b-9547f19280fb", "name": "To cancel?", "id": "2027.bdf49d86-d0ba-49e6-8c8b-9e0347f027f7", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db", "targetRef": "2025.85ba1202-9d8d-4d7d-872c-68be670af17b", "name": "no", "id": "2027.096a244b-997c-4de4-8baa-e4b767198c6b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.753f8b21-2aa2-4771-8d95-6dc4fa71a0da", "targetRef": "2025.36a8f3c2-054e-45b5-bfae-5a1de3ae6577", "name": "To Get Actions", "id": "2027.cc1745b6-77d2-4169-859a-397c2f92cebb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.2129f97a-fb4c-4164-842b-83bc1718efd0", "targetRef": "2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db", "name": "To valid?", "id": "2027.cfcef02b-0e08-4959-891c-1e719e6b44d2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.2fb97341-8137-491c-8d2b-9547f19280fb", "targetRef": "2025.e525aa08-63da-4864-834f-77cb967dfb2d", "name": "To End", "id": "2027.d7f761d6-ce78-48b5-8088-4e528a82bae8", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.e525aa08-63da-4864-834f-77cb967dfb2d", "targetRef": "2025.d6e6e891-944d-46b2-84d8-bdecb8b81130", "name": "To End", "id": "2027.2865cdba-1f38-4b42-80ac-076f8213fa3b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.4e33c805-8685-4d73-8f6a-fcaf0f3bd02d", "targetRef": "2025.8f99d806-a261-46e7-8d06-a3e1591c764c", "name": "To Audited?", "id": "2027.f42a0127-5919-44a6-8721-ae94737df59a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.8f99d806-a261-46e7-8d06-a3e1591c764c", "targetRef": "2025.57169d86-750d-4aa2-8b4c-d67256b6ec71", "name": "No", "id": "2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.8f99d806-a261-46e7-8d06-a3e1591c764c", "targetRef": "2025.d6e6e891-944d-46b2-84d8-bdecb8b81130", "name": "Yes", "id": "2027.1346a5a5-3a6f-4401-804e-6eb207da049b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.d6e6e891-944d-46b2-84d8-bdecb8b81130", "targetRef": "2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26", "name": "To Audited?", "id": "2027.5cd898bb-4ef7-48a1-8806-60d452836cc8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26", "targetRef": "2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d", "name": "Yes", "id": "2027.18c44064-c807-4504-885b-10d194656bc8", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26", "targetRef": "2025.54b0ec9e-79bb-4f36-8457-59b2bc9a6c62", "name": "No", "id": "2027.3b13555b-6f21-4035-85af-1e18b0ae56e5", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d", "targetRef": "2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7", "name": "yes", "id": "2027.7480e63c-48d1-4e7d-836c-a0be731df0b1", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d", "targetRef": "04cbeb26-4ea0-4196-8bfe-aa11c42d6d18", "name": "To Setting status and substatus", "id": "2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7", "targetRef": "2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea", "name": "To End", "id": "2027.db8ac187-7804-443b-87c9-4748945423d7", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea", "targetRef": "2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2", "name": "To Exclusive Gateway", "id": "2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2", "targetRef": "04cbeb26-4ea0-4196-8bfe-aa11c42d6d18", "name": "To End", "id": "2027.60a2073a-**************-de2ae02cb32b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2", "targetRef": "2025.adce5ba4-173d-4f56-8555-a2ffb2732ec0", "name": "To Stay on page 3", "id": "2027.7f3da394-2437-4c8b-8cf4-93cf02724b35", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:intermediateThrowEvent": [{"name": "Postpone", "id": "2025.9f687095-7947-4995-831a-394ad760b1da", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "371", "y": "10", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd"}, "ns16:incoming": "2027.eb0b5a58-59e8-42c5-8251-092133493c91", "ns16:outgoing": "2027.aae44b8b-d132-4a5f-8bf6-30e03424dfdd", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page", "id": "2025.85ba1202-9d8d-4d7d-872c-68be670af17b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "703", "y": "87", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.096a244b-997c-4de4-8baa-e4b767198c6b", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.57169d86-750d-4aa2-8b4c-d67256b6ec71", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1276", "y": "308", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 2", "id": "2025.54b0ec9e-79bb-4f36-8457-59b2bc9a6c62", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1592", "y": "300", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.3b13555b-6f21-4035-85af-1e18b0ae56e5", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 3", "id": "2025.adce5ba4-173d-4f56-8555-a2ffb2732ec0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1817", "y": "-20", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.7f3da394-2437-4c8b-8cf4-93cf02724b35", "ns3:stayOnPageEventDefinition": ""}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.82d232e5-161c-4df4-8e64-f0f5205025b0", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorPanelVIS", "id": "2056.fa3aafa5-8bc2-4e85-8a68-31d7700a76c3"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "checkValid", "id": "2056.97e42b5a-af86-4eec-8778-255eeedbf3ac"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.1fcc6040-856e-44cf-81b7-2556dd93cad2"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "deliveryTerms", "id": "2056.056128d4-1a9a-45de-8841-5bef2404cbad"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "paymentTerms", "id": "2056.68892cf8-0371-4402-81c1-1c94565990e9"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "specialInstructions", "id": "2056.6ff26a96-c2e0-4b52-81bc-0b795443090e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "instructions", "id": "2056.524c435f-bda8-485d-80e8-0d1e611a3178"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNoVIS", "id": "2056.f4caf637-1876-4023-8840-6bc9ec88a1f9"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.d260c6d5-1b33-4fc4-8da3-0f297a31ea79"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestTypeVIS", "id": "2056.67f1661b-bb43-43ba-88a4-3848f99abb74"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.c9a0f084-2e55-4ef3-8ca3-e4b6f9cb64d6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestIdStr", "id": "2056.35396035-ba93-4f02-854c-f355e284d2fa"}], "ns16:exclusiveGateway": [{"default": "2027.096a244b-997c-4de4-8baa-e4b767198c6b", "name": "valid?", "id": "2025.97b89fbb-dcf7-47d5-8692-1fbba7f217db", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "714", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.cfcef02b-0e08-4959-891c-1e719e6b44d2", "ns16:outgoing": ["2027.bdf49d86-d0ba-49e6-8c8b-9e0347f027f7", "2027.096a244b-997c-4de4-8baa-e4b767198c6b"]}, {"default": "2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5", "name": "Audited?", "id": "2025.8f99d806-a261-46e7-8d06-a3e1591c764c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1241", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.f42a0127-5919-44a6-8721-ae94737df59a", "ns16:outgoing": ["2027.00ca8390-ea59-4793-89d2-1b46f12bc8a5", "2027.1346a5a5-3a6f-4401-804e-6eb207da049b"]}, {"default": "2027.3b13555b-6f21-4035-85af-1e18b0ae56e5", "name": "Audited?", "id": "2025.2f788b1a-8ec4-400f-8206-eec75fc6ea26", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1441", "y": "198", "width": "32", "height": "32"}}, "ns16:incoming": "2027.5cd898bb-4ef7-48a1-8806-60d452836cc8", "ns16:outgoing": ["2027.18c44064-c807-4504-885b-10d194656bc8", "2027.3b13555b-6f21-4035-85af-1e18b0ae56e5"]}, {"default": "2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3", "name": "cancel?", "id": "2025.5c8e13e8-cc0d-4460-882c-69f102d61b4d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1536", "y": "196", "width": "32", "height": "32"}}, "ns16:incoming": "2027.18c44064-c807-4504-885b-10d194656bc8", "ns16:outgoing": ["2027.7480e63c-48d1-4e7d-836c-a0be731df0b1", "2027.baa8c8ba-e662-4d14-8b6a-b00f6cf193a3"]}, {"default": "2027.7f3da394-2437-4c8b-8cf4-93cf02724b35", "name": "Exclusive Gateway", "id": "2025.fb676fd7-0cfa-40d6-8d72-6bd5e08d97e2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1835", "y": "99", "width": "32", "height": "32"}}, "ns16:incoming": "2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a", "ns16:outgoing": ["2027.60a2073a-**************-de2ae02cb32b", "2027.7f3da394-2437-4c8b-8cf4-93cf02724b35"]}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "default": "2027.cc1745b6-77d2-4169-859a-397c2f92cebb", "name": "init script", "id": "2025.753f8b21-2aa2-4771-8d95-6dc4fa71a0da", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "220", "y": "177", "width": "95", "height": "70"}}, "ns16:incoming": "2027.e39db3f2-1389-445c-8839-3e30b3064291", "ns16:outgoing": "2027.cc1745b6-77d2-4169-859a-397c2f92cebb", "ns16:script": "tw.local.errorPanelVIS=\"NONE\";\r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime =new Date();\r\r\ntw.local.odcRequest.stepLog.step =tw.epv.ScreenNames.CACT02;\r\r\ntw.local.odcRequest.stepLog.action =\"\";\r\r\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.CACT02;\r\r\n\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT02;\r\r\ntw.local.actionConditions.userRole= tw.epv.userRole.CACT02;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.lastAction;\r\r\n\r\r\ntw.local.deliveryTerms = tw.epv.TermsAndConditions.deliveryTerms;\r\r\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\r\r\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\r\r\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\r\n\ttw.local.parentRequestNoVIS = \"Readonly\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\n/*Document Generation Section*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\r\n\ttw.local.requestTypeVIS = \"EDITABLE\";\r\r\n}\r\r\nelse{\r\r\n\ttw.local.requestTypeVIS = \"NONE\";\t\r\r\n}\r\r\n\r\r\ntw.local.errorPanelVIS =\"NONE\";\r\r\n\r\r\nif(tw.local.role == tw.epv.userRole.branch )\r\r\n{\r\r\n\ttw.local.role = tw.epv.userRole.branchComp;\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.role = tw.epv.userRole.hubComp;\r\r\n}"}, {"scriptFormat": "text/x-javascript", "default": "2027.cfcef02b-0e08-4959-891c-1e719e6b44d2", "name": "Validation", "id": "2025.2129f97a-fb4c-4164-842b-83bc1718efd0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "540", "y": "178", "width": "95", "height": "70", "color": "#95D087"}}, "ns16:incoming": "2027.fa18eb70-96fc-4b57-839c-591e8f979ff5", "ns16:outgoing": "2027.cfcef02b-0e08-4959-891c-1e719e6b44d2", "ns16:script": "tw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\nvar tempLength = 0 ;\r\r\n\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tif (field < 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\tvar msg= \"Invalid Value, This field can not be negative value.\";\r\r\n\t\t\t\t\taddError(fieldName , msg , msg , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/* =====================\r\r\n* |\tVALIDATE HERE   |\r\r\n* =====================\r\r\n*/\r\r\n\r\r\n//Actions Validation\r\r\nmandatory(tw.local.odcRequest.stepLog.action,\"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action== tw.epv.CreationActions.returnToInitiator){\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason,\"tw.local.odcRequest.stepLog.returnReason\");\r\r\n} \r\r\n\r\r\n\r\r\n\r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";\t\t"}, {"scriptFormat": "text/x-javascript", "default": "2027.d7f761d6-ce78-48b5-8088-4e528a82bae8", "name": "Setting status and substatus", "id": "2025.2fb97341-8137-491c-8d2b-9547f19280fb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "917", "y": "178", "width": "95", "height": "70"}}, "ns16:incoming": "2027.bdf49d86-d0ba-49e6-8c8b-9e0347f027f7", "ns16:outgoing": "2027.d7f761d6-ce78-48b5-8088-4e528a82bae8", "ns16:script": "if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.approveRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Trade FO Review\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator){\r\r\n\ttw.local.odcRequest.appInfo.status    = \"Initiated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Returned to Initiator\";\r\r\n}\r\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest){\r\r\n\ttw.local.odcRequest.appInfo.status    = \" Canceled\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \" Canceled\";\r\r\n}\r\r\n\r\r\n"}], "ns16:callActivity": [{"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "default": "2027.5cd898bb-4ef7-48a1-8806-60d452836cc8", "name": "History", "id": "2025.d6e6e891-944d-46b2-84d8-bdecb8b81130", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1340", "y": "179", "width": "95", "height": "70"}}, "ns16:incoming": ["2027.1346a5a5-3a6f-4401-804e-6eb207da049b", "2027.2865cdba-1f38-4b42-80ac-076f8213fa3b"], "ns16:outgoing": "2027.5cd898bb-4ef7-48a1-8806-60d452836cc8", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.local.role", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "default": "2027.2865cdba-1f38-4b42-80ac-076f8213fa3b", "name": "Update request state and status in DB", "id": "2025.e525aa08-63da-4864-834f-77cb967dfb2d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1028", "y": "178", "width": "95", "height": "70"}}, "ns16:incoming": "2027.d7f761d6-ce78-48b5-8088-4e528a82bae8", "ns16:outgoing": "2027.2865cdba-1f38-4b42-80ac-076f8213fa3b", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.a84d91ec-e620-4417-85c4-7dd7db58ff31", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.c5194a72-2de4-483f-823c-47d5b98b572c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.3974caa7-9f10-4f46-8275-17080a25476e", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b05449cf-c459-4405-809c-888b00e3e968", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b537f107-9e83-46e0-8979-6fe5796c7a7d", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.stepName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.8e0cea15-236a-4b79-86a8-f93756a4ac86", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "default": "2027.f42a0127-5919-44a6-8721-ae94737df59a", "name": "Audit ODC Request", "id": "2025.4e33c805-8685-4d73-8f6a-fcaf0f3bd02d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1146", "y": "178", "width": "95", "height": "70"}}, "ns16:outgoing": "2027.f42a0127-5919-44a6-8721-ae94737df59a", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.afad40c5-a38b-475c-8154-b4dabd94b6fe", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.254cf8eb-2743-4c53-8c52-e51c8c22884e", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.812db3ff-6589-474c-bcc5-21fde39e4d25", "default": "2027.db8ac187-7804-443b-87c9-4748945423d7", "name": "Cancel and Delete Transactions", "id": "2025.69896e72-0fcc-4de9-8c84-5bb568f02fb7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1550", "y": "80", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.requestIdStr = tw.local.odcRequest.requestID+\"\";"}, "ns16:incoming": "2027.7480e63c-48d1-4e7d-836c-a0be731df0b1", "ns16:outgoing": "2027.db8ac187-7804-443b-87c9-4748945423d7", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.8c25b7d2-65fe-4ffa-83d3-f054a2ad4209", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestIdStr", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "default": "2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a", "name": "cancel request", "id": "2025.f7522408-c4b5-423d-8a0f-d6934ba1c1ea", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1695", "y": "80", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.db8ac187-7804-443b-87c9-4748945423d7", "ns16:outgoing": "2027.81b7b4fe-9450-4cb3-887f-e5fbb97c138a", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.55a76aa1-e513-4fd3-835f-7fe160120af7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.77d2b3d6-2a2a-4520-ad08-31686157d431", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:targetRef": "2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns3:htmlHeaderTag": {"id": "3697938f-e5f6-439b-9298-537463c06376", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "c3072b24-6678-4c5a-8778-9e80acd9f3da"}, {"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "a026fa94-dec2-4f29-8e4d-e721a2fbfb24"}, {"epvId": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "epvProcessLinkId": "2a10141b-2c22-488d-8cfe-2bcb6d4f8134"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "e3a0c599-3598-4949-8588-afe78ec6e53d"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "8dff81df-1554-440f-8e43-1a6d4feba7e9"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.e02ce3e0-e9af-4169-84e0-ae3aad48c2a0"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.69a1c013-d5ea-4d9f-8e7a-253a4c53ee83", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = [];\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.MultiTenorDates = [];\r\nautoObject.MultiTenorDates[0] = {};\r\nautoObject.MultiTenorDates[0].date = new Date();\r\nautoObject.MultiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.drawer = [];\r\nautoObject.Parties.drawer[0] = {};\r\nautoObject.Parties.drawer[0].id = \"\";\r\nautoObject.Parties.drawer[0].name = \"\";\r\nautoObject.Parties.drawer[0].country = \"\";\r\nautoObject.Parties.drawer[0].language = \"\";\r\nautoObject.Parties.drawer[0].reference = \"\";\r\nautoObject.Parties.drawer[0].address1 = \"\";\r\nautoObject.Parties.drawer[0].address2 = \"\";\r\nautoObject.Parties.drawer[0].address3 = \"\";\r\nautoObject.Parties.drawer[0].cif = \"\";\r\nautoObject.Parties.Drawee = [];\r\nautoObject.Parties.Drawee[0] = {};\r\nautoObject.Parties.Drawee[0].id = \"\";\r\nautoObject.Parties.Drawee[0].name = \"\";\r\nautoObject.Parties.Drawee[0].country = \"\";\r\nautoObject.Parties.Drawee[0].language = \"\";\r\nautoObject.Parties.Drawee[0].reference = \"\";\r\nautoObject.Parties.Drawee[0].address1 = \"\";\r\nautoObject.Parties.Drawee[0].address2 = \"\";\r\nautoObject.Parties.Drawee[0].address3 = \"\";\r\nautoObject.Parties.Drawee[0].cif = \"\";\r\nautoObject.Parties.collectingBank = [];\r\nautoObject.Parties.collectingBank[0] = {};\r\nautoObject.Parties.collectingBank[0].id = \"\";\r\nautoObject.Parties.collectingBank[0].name = \"\";\r\nautoObject.Parties.collectingBank[0].country = \"\";\r\nautoObject.Parties.collectingBank[0].language = \"\";\r\nautoObject.Parties.collectingBank[0].reference = \"\";\r\nautoObject.Parties.collectingBank[0].address1 = \"\";\r\nautoObject.Parties.collectingBank[0].address2 = \"\";\r\nautoObject.Parties.collectingBank[0].address3 = \"\";\r\nautoObject.Parties.collectingBank[0].cif = \"\";\r\nautoObject.Parties.collectingBank[0].media = \"\";\r\nautoObject.Parties.collectingBank[0].address = \"\";\r\nautoObject.Parties.accounteeCaseInNeed = [];\r\nautoObject.Parties.accounteeCaseInNeed[0] = {};\r\nautoObject.Parties.accounteeCaseInNeed[0].id = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].name = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].country = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].language = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].reference = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].address1 = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].address2 = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].address3 = \"\";\r\nautoObject.Parties.accounteeCaseInNeed[0].cif = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = \"\";\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = \"\";\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.initiator = \"\";\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject", "useDefault": "false"}}}, {"name": "regeneratedRemittanceLetterTitleVIS", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.85bb1b3b-6aff-440d-8a91-036bad32bb3a"}, {"name": "lastAction", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.06e83dcd-aab9-473a-8905-39ca48c61d56"}, {"name": "role", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.2447e3bc-7548-4065-8bbf-89b5de603e41"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.ac5cb29b-06ab-412f-8129-5bd7fb6dc779"}], "ns16:dataOutput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.22fa5dfa-ab1c-4768-82c7-52b63dfc9e88"}, "ns16:inputSet": {"id": "_87e1bc19-d9cc-4323-a410-c09c59b06099"}, "ns16:outputSet": {"id": "_d427e326-8080-433e-9ade-d2af4f857284"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1b27b613-e829-46a5-8196-1299e20b80b1", "processId": "1.e31385fc-75d6-4686-b548-3d16aa42adf3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.67d12eee-cc85-45c9-b76d-04f1e433af93", "2025.67d12eee-cc85-45c9-b76d-04f1e433af93"], "endStateId": "Out", "toProcessItemId": ["2025.418aab6d-2e41-47f8-a66b-01450b4c4fc6", "2025.418aab6d-2e41-47f8-a66b-01450b4c4fc6"], "guid": "54fae27b-3be4-49e1-9be2-ec29f35f45d6", "versionId": "56a0c93a-1dad-4494-9f9f-b1ec6ca9a47a", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}