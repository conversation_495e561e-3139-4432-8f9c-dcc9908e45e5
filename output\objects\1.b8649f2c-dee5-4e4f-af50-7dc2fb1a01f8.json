{"id": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "versionId": "db4be141-e3c9-4dd3-97a2-997aa022479e", "name": "Set document default properties 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "name": "Set document default properties 2", "lastModified": "1700250894537", "lastModifiedBy": "eslam1", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.8f5b1d5b-266c-4378-8fad-952d8242b937", "2025.8f5b1d5b-266c-4378-8fad-952d8242b937"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.789ca447-01f4-4eae-8c65-1feaabdbe24c", "2025.789ca447-01f4-4eae-8c65-1feaabdbe24c"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "be3818b9-98dc-499b-9147-a58ac16700b7", "versionId": "db4be141-e3c9-4dd3-97a2-997aa022479e", "dependencySummary": "<dependencySummary id=\"bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:7bdc\" />", "jsonData": "{\"rootElement\":[{\"artifact\":[{\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":288,\"x\":792,\"y\":53,\"declaredType\":\"TNodeVisualInfo\",\"height\":171}]},\"declaredType\":\"textAnnotation\",\"text\":{\"content\":[\"-\\tCIF Number\\n-\\tRequest Reference Number\\n-\\tBPM Instance Id\\n-\\tDocument Type = \\u201cODC Document\\u201d\\n-\\tDocument Name\\n\"]},\"id\":\"e4db4e10-4560-4019-8a83-bb3659366a4d\",\"textFormat\":\"text\\/plain\"}],\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.ac07b981-3f89-40d8-886c-daba8cbf8a06\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"27653fd3-260b-41c1-b714-001c95294ec0\"},{\"incoming\":[\"76ff2e03-a220-4324-bcdd-4b504130689e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1986\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"0aedcc3d-a222-4fdc-a13c-571e355397d0\"},{\"targetRef\":\"8f5b1d5b-266c-4378-8fad-952d8242b937\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set ECM default properties\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ac07b981-3f89-40d8-886c-daba8cbf8a06\",\"sourceRef\":\"27653fd3-260b-41c1-b714-001c95294ec0\"},{\"startQuantity\":1,\"outgoing\":[\"76ff2e03-a220-4324-bcdd-4b504130689e\"],\"incoming\":[\"08d8c9bf-263f-432e-8c79-b725e99cf6e7\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":436,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set ECM default properties\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"4ad5b0ca-3520-4099-8d2e-6c8fab55f77d\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.attachmentDetails.ecmProperties.defaultProperties =new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\r\\nvar index= 0;\\r\\n\\r\\nfunction setECMProps(name, hidden, value , editable){\\r\\n\\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\r\\n\\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].name = name;\\r\\n\\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].hidden = hidden;\\r\\n\\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].value = value;\\r\\n\\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].editable = editable;\\r\\n\\tindex++;\\r\\n}\\r\\n\\r\\nsetECMProps(\\\"Customer_Number_CIF\\\", false, tw.local.cif, false);\\r\\nsetECMProps( \\\"BPMInstanceId\\\", false, tw.local.instanceNo, false);\\r\\nsetECMProps(\\\"DocumentType\\\", true, \\\"ODC Document\\\",false);\\r\\nsetECMProps(\\\"RequestReferenceNumber\\\", false,tw.local.requestNumber,false);\\r\\n\\r\\nsetECMProps(\\\"DocumentTitle\\\", true,\\\"\\\",false);\\r\\nsetECMProps(\\\"RecordInformation\\\", true,\\\"\\\",false);\\r\\nsetECMProps(\\\"CmFederatedLockStatus\\\", true,\\\"\\\",false);\\r\\n\\r\\n\\/\\/setECMProps(\\\"cmis:CmFederatedLockStatus\\\", false, \\\"Ts\\\",false);\\r\\n\\/\\/setECMProps(\\\"cmis:name\\\", false,\\\"S\\\",false);\\r\\n\\/\\/setECMProps(\\\"cmis:RecordInformation\\\", true,\\\"RI\\\",false);\\r\\n\\/\\/RecordInformation\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"]}},{\"targetRef\":\"0aedcc3d-a222-4fdc-a13c-571e355397d0\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"76ff2e03-a220-4324-bcdd-4b504130689e\",\"sourceRef\":\"4ad5b0ca-3520-4099-8d2e-6c8fab55f77d\"},{\"startQuantity\":1,\"outgoing\":[\"08d8c9bf-263f-432e-8c79-b725e99cf6e7\"],\"incoming\":[\"2027.ac07b981-3f89-40d8-886c-daba8cbf8a06\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":291,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set attchments\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"8f5b1d5b-266c-4378-8fad-952d8242b937\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"var index= 0;\\r\\n\\r\\nif(!tw.local.attachmentDetails)\\r\\n  tw.local.attachmentDetails = {};\\r\\n\\r\\nif (tw.local.attachmentDetails.attachment == null || tw.local.attachmentDetails.attachment == undefined) {   \\r\\n\\ttw.local.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\r\\n\\t\\r\\n\\tsetProps(\\\"Customer Request\\\",  \\\"Customer Request\\\",  \\\"\\u0637\\u0644\\u0628 \\u0627\\u0644\\u0639\\u0645\\u064a\\u0644\\\"); \\r\\n\\tsetProps(\\\"BILL OF EXCHANGE\\\",  \\\"BILL OF EXCHANGE\\\",  \\\"\\\"); \\r\\n\\tsetProps(\\\"Invoice\\\",  \\\"Invoice\\\",  \\\"\\u0641\\u0627\\u062a\\u0648\\u0631\\u0629\\\"); \\r\\n\\tsetProps(\\\"BILL OF LADING\\\",  \\\"BILL OF LADING\\\",  \\\"\\\"); \\t \\t\\t\\r\\n\\tsetProps(\\\"AIRWAY BILL\\\", \\\"AIRWAY BILL\\\", \\\"\\\" ); \\r\\n\\tsetProps(\\\"TRUCK CONSIGNMENT NOTE\\\",\\\"TRUCK CONSIGNMENT NOTE\\\", \\\"\\\"); \\r\\n\\tsetProps(\\\"N\\/N BILL OF LADING\\\",\\\"N\\/N BILL OF LADING\\\",\\\"\\\"); \\r\\n\\tsetProps(\\\"COURIER \\/ POST RECEIPT\\\", \\\"COURIER \\/ POST RECEIPT\\\" , \\\"\\\" ); \\r\\n\\tsetProps(\\\"PACKING LIST\\\",\\\"PACKING LIST\\\",\\\"\\\"); \\r\\n\\tsetProps(\\\"CERTIFICATE OF ORIGIN\\\",\\\"CERTIFICATE OF ORIGIN\\\", \\\"\\\" );\\r\\n\\tsetProps(\\\"CERTIFICATE OF ANALYSIS\\\", \\\"CERTIFICATE OF ANALYSIS\\\", \\\"\\\");\\r\\n\\tsetProps(\\\"INSURANCE POLICY \\/ CERTIFICATE\\\", \\\"INSURANCE POLICY \\/ CERTIFICATE\\\",\\\"\\\");\\r\\n\\tsetProps(\\\"BENEFECIARY DECLARATION\\\", \\\"BENEFECIARY DECLARATION\\\", \\\"\\\");\\r\\n\\tsetProps(\\\"NON RADIOACTIVE CERTIFICATE\\\", \\\"NON RADIOACTIVE CERTIFICATE\\\",\\\"\\\");\\r\\n\\tsetProps(\\\"PHYTOSANITARY CERTIFICATE\\\", \\\"PHYTOSANITARY CERTIFICATE\\\",\\\"\\\");\\r\\n\\tsetProps(\\\"CERTIFICATE OF ANALYSIS\\\",\\\"Bill of exchange\\/draft\\\", \\\"\\u0627\\u0644\\u0643\\u0645\\u0628\\u064a\\u0627\\u0644\\u0629\\\" );\\r\\n\\tsetProps(\\\"HEALTH CERTIFICATE\\\",\\\"HEALTH CERTIFICATE\\\", \\\"\\\");\\r\\n\\tsetProps(\\\"INSPECTION CERTIFICATE\\\", \\\"INSPECTION CERTIFICATE\\\",  \\\"\\\");\\r\\n\\tsetProps(\\\"WARRANTY CERTIFICATE\\\", \\\"WARRANTY CERTIFICATE\\\",\\\"\\\");\\r\\n\\tsetProps( \\\"TEST CERTIFICATE\\\",\\\"TEST CERTIFICATE\\\", \\\"\\\");\\r\\n\\r\\n\\ttw.local.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\r\\n\\ttw.local.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\r\\n\\ttw.local.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\r\\n\\ttw.local.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\r\\n\\t\\t \\r\\n}\\t\\r\\n\\r\\nfunction setProps(name, desc, arabicName){\\t\\r\\n\\ttw.local.attachmentDetails.attachment[index]= new tw.object.Attachment();\\t\\r\\n\\ttw.local.attachmentDetails.attachment[index].name= name;\\r\\n\\ttw.local.attachmentDetails.attachment[index].description= desc;\\r\\n\\ttw.local.attachmentDetails.attachment[index].arabicName= arabicName;\\r\\n\\ttw.local.attachmentDetails.attachment[index].numOfOriginals= 0;\\r\\n\\ttw.local.attachmentDetails.attachment[index].numOfCopies= 0;\\r\\n\\tindex++;\\t\\r\\n}\\r\\n\\r\\n\"]}},{\"targetRef\":\"4ad5b0ca-3520-4099-8d2e-6c8fab55f77d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set ECM default properties\",\"declaredType\":\"sequenceFlow\",\"id\":\"08d8c9bf-263f-432e-8c79-b725e99cf6e7\",\"sourceRef\":\"8f5b1d5b-266c-4378-8fad-952d8242b937\"},{\"parallelMultiple\":false,\"outgoing\":[\"8ccad284-0e45-496b-8a1e-84d12ae0399d\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"86cf04ff-de31-4f5d-8d6c-b62bc817feb6\",\"otherAttributes\":{\"eventImplId\":\"ff25eef4-fb31-4598-88c7-3d79b71d29e9\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":307,\"y\":225,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"508ac6be-42fd-434a-86d5-c949141a1dc0\"},{\"incoming\":[\"eb50f183-97e9-4d0f-88fa-d9db3bb13b4a\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"b472843d-4929-4a4c-8a2d-3742e7b0632c\",\"otherAttributes\":{\"eventImplId\":\"83f10afc-c9cf-4b5b-8906-d7a65c272219\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":495,\"y\":225,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"dataInputAssociation\":[{\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}]}],\"declaredType\":\"endEvent\",\"id\":\"b02758ea-4c8a-4941-8c22-1f218dbafb5a\"},{\"targetRef\":\"789ca447-01f4-4eae-8c65-1feaabdbe24c\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"8ccad284-0e45-496b-8a1e-84d12ae0399d\",\"sourceRef\":\"508ac6be-42fd-434a-86d5-c949141a1dc0\"},{\"startQuantity\":1,\"outgoing\":[\"eb50f183-97e9-4d0f-88fa-d9db3bb13b4a\"],\"incoming\":[\"8ccad284-0e45-496b-8a1e-84d12ae0399d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":383,\"y\":202,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exp Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Set document default properties 2\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"789ca447-01f4-4eae-8c65-1feaabdbe24c\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"b02758ea-4c8a-4941-8c22-1f218dbafb5a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"eb50f183-97e9-4d0f-88fa-d9db3bb13b4a\",\"sourceRef\":\"789ca447-01f4-4eae-8c65-1feaabdbe24c\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c2264df0-7fe3-410b-8614-1f97060e981f\"}],\"laneSet\":[{\"id\":\"3ad21738-fe63-4f52-a34a-51587745acb5\",\"lane\":[{\"flowNodeRef\":[\"27653fd3-260b-41c1-b714-001c95294ec0\",\"0aedcc3d-a222-4fdc-a13c-571e355397d0\",\"4ad5b0ca-3520-4099-8d2e-6c8fab55f77d\",\"8f5b1d5b-266c-4378-8fad-952d8242b937\",\"e4db4e10-4560-4019-8a83-bb3659366a4d\",\"508ac6be-42fd-434a-86d5-c949141a1dc0\",\"b02758ea-4c8a-4941-8c22-1f218dbafb5a\",\"789ca447-01f4-4eae-8c65-1feaabdbe24c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"9b455e62-b9c8-4de5-b329-438b93b2a05e\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Set document default properties 2\",\"declaredType\":\"process\",\"id\":\"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9\",\"name\":\"attachmentDetails\",\"isCollection\":false,\"id\":\"2055.7d269650-ee48-4101-80db-2807cf921562\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.0261e8ad-a540-4682-88c5-87dff3eab23c\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.399c7a58-00b5-4451-9813-41c0b9652088\",\"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc\",\"2055.25394215-074f-4b79-8e84-9a96d32cc83b\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.7d269650-ee48-4101-80db-2807cf921562\",\"2055.0261e8ad-a540-4682-88c5-87dff3eab23c\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\\\"11112222\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"cif\",\"isCollection\":false,\"id\":\"2055.399c7a58-00b5-4451-9813-41c0b9652088\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\\\"12341234123412\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestNumber\",\"isCollection\":false,\"id\":\"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\\\"1111\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"instanceNo\",\"isCollection\":false,\"id\":\"2055.25394215-074f-4b79-8e84-9a96d32cc83b\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "cif", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.399c7a58-00b5-4451-9813-41c0b9652088", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "97af6472-e794-41cc-840f-4c2ab4f58209", "versionId": "b3804390-dfc8-4d68-92f8-741c075e7661"}, {"name": "requestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c28023fb-b45e-4b63-ae36-97e6df6421bc", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "450d2020-0c66-4bd8-ad2d-13bbedf02254", "versionId": "a0b5e622-f5bd-4de1-a298-b29d40d46320"}, {"name": "instanceNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.25394215-074f-4b79-8e84-9a96d32cc83b", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "c2fbbb58-77a1-4ff5-a44f-8a2a859f97c2", "versionId": "fbb80568-c637-47cd-953f-923b0a1aa020"}, {"name": "attachmentDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.7d269650-ee48-4101-80db-2807cf921562", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "parameterType": "2", "isArrayOf": "false", "classId": "/12.55bb335a-d3b3-4749-a082-859e2a48ace9", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "873988dd-8fde-4ac0-8b76-6a3766d893c5", "versionId": "76fb229f-dca9-48d8-8238-37490d8117d8"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0261e8ad-a540-4682-88c5-87dff3eab23c", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "da415976-4267-4bf6-aa9a-0ee08eb67980", "versionId": "c1038d88-d1ff-4b40-b84e-3e00586eccf4"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.bae0e241-c1b0-4fd1-960a-e76ffa50f5c5", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "parameterType": "3", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "e40f70f7-5ff1-4455-8459-6f28473b7cc9", "versionId": "b6a86379-8fde-4e84-92bc-920919bf63f7"}], "processVariable": {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c2264df0-7fe3-410b-8614-1f97060e981f", "description": {"isNull": "true"}, "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "08334a5d-7df7-4c55-9537-3669d0d2e4aa", "versionId": "c6cc25d7-b9ab-4d3d-af61-a0a3ad1546b9"}, "note": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLabelId": "2028.e4db4e10-4560-4019-8a83-bb3659366a4d", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "description": "-\tCIF Number\r\n-\tRequest Reference Number\r\n-\tBPM Instance Id\r\n-\tDocument Type = “ODC Document”\r\n-\tDocument Name\r\n", "data": {"isNull": "true"}, "guid": "3f3632ca-bb5e-4b9e-980b-caa71de7ab0e", "versionId": "c2da5da4-fc3c-4a49-8b51-550ea490a8c3", "layoutData": {"x": "792", "y": "53", "width": "288", "height": "171"}}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b02758ea-4c8a-4941-8c22-1f218dbafb5a", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.1c29a6ec-74ef-42cb-b780-fd8cef656e0c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:21b1", "versionId": "0c366343-176f-4c2a-b18c-c172d458230a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "495", "y": "225", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.1c29a6ec-74ef-42cb-b780-fd8cef656e0c", "message": "", "faultStyle": "1", "guid": "a5314d40-59c8-4467-9cdc-ce958e065381", "versionId": "b31ca2bd-6fa0-4c03-b4b8-7966d3bc9488", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.2b22f3e1-a039-44c2-8a10-ad98899eb1fd", "processParameterId": "2055.bae0e241-c1b0-4fd1-960a-e76ffa50f5c5", "parameterMappingParentId": "3007.1c29a6ec-74ef-42cb-b780-fd8cef656e0c", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "76ded3f9-1196-45d5-9b3a-d80d718e677b", "versionId": "ab33ae7f-9b15-43d1-9942-b25ae64485f2", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8f5b1d5b-266c-4378-8fad-952d8242b937", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "name": "Set attchments", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.0783c095-c3a1-4f67-a5a6-35a9f894de9f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1eaa", "versionId": "1aeffcc9-d043-417b-bd47-9be31211bcb2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "291", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.0783c095-c3a1-4f67-a5a6-35a9f894de9f", "scriptTypeId": "2", "isActive": "true", "script": "var index= 0;\r\r\n\r\r\nif(!tw.local.attachmentDetails)\r\r\n  tw.local.attachmentDetails = {};\r\r\n\r\r\nif (tw.local.attachmentDetails.attachment == null || tw.local.attachmentDetails.attachment == undefined) {   \r\r\n\ttw.local.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\n\t\r\r\n\tsetProps(\"Customer Request\",  \"Customer Request\",  \"طلب العميل\"); \r\r\n\tsetProps(\"BILL OF EXCHANGE\",  \"BILL OF EXCHANGE\",  \"\"); \r\r\n\tsetProps(\"Invoice\",  \"Invoice\",  \"فاتورة\"); \r\r\n\tsetProps(\"BILL OF LADING\",  \"BILL OF LADING\",  \"\"); \t \t\t\r\r\n\tsetProps(\"AIRWAY BILL\", \"AIRWAY BILL\", \"\" ); \r\r\n\tsetProps(\"TRUCK CONSIGNMENT NOTE\",\"TRUCK CONSIGNMENT NOTE\", \"\"); \r\r\n\tsetProps(\"N/N BILL OF LADING\",\"N/N BILL OF LADING\",\"\"); \r\r\n\tsetProps(\"COURIER / POST RECEIPT\", \"COURIER / POST RECEIPT\" , \"\" ); \r\r\n\tsetProps(\"PACKING LIST\",\"PACKING LIST\",\"\"); \r\r\n\tsetProps(\"CERTIFICATE OF ORIGIN\",\"CERTIFICATE OF ORIGIN\", \"\" );\r\r\n\tsetProps(\"CERTIFICATE OF ANALYSIS\", \"CERTIFICATE OF ANALYSIS\", \"\");\r\r\n\tsetProps(\"INSURANCE POLICY / CERTIFICATE\", \"INSURANCE POLICY / CERTIFICATE\",\"\");\r\r\n\tsetProps(\"BENEFECIARY DECLARATION\", \"BENEFECIARY DECLARATION\", \"\");\r\r\n\tsetProps(\"NON RADIOACTIVE CERTIFICATE\", \"NON RADIOACTIVE CERTIFICATE\",\"\");\r\r\n\tsetProps(\"PHYTOSANITARY CERTIFICATE\", \"PHYTOSANITARY CERTIFICATE\",\"\");\r\r\n\tsetProps(\"CERTIFICATE OF ANALYSIS\",\"Bill of exchange/draft\", \"الكمبيالة\" );\r\r\n\tsetProps(\"HEALTH CERTIFICATE\",\"HEALTH CERTIFICATE\", \"\");\r\r\n\tsetProps(\"INSPECTION CERTIFICATE\", \"INSPECTION CERTIFICATE\",  \"\");\r\r\n\tsetProps(\"WARRANTY CERTIFICATE\", \"WARRANTY CERTIFICATE\",\"\");\r\r\n\tsetProps( \"TEST CERTIFICATE\",\"TEST CERTIFICATE\", \"\");\r\r\n\r\r\n\ttw.local.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\n\ttw.local.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\n\ttw.local.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\n\t\t \r\r\n}\t\r\r\n\r\r\nfunction setProps(name, desc, arabicName){\t\r\r\n\ttw.local.attachmentDetails.attachment[index]= new tw.object.Attachment();\t\r\r\n\ttw.local.attachmentDetails.attachment[index].name= name;\r\r\n\ttw.local.attachmentDetails.attachment[index].description= desc;\r\r\n\ttw.local.attachmentDetails.attachment[index].arabicName= arabicName;\r\r\n\ttw.local.attachmentDetails.attachment[index].numOfOriginals= 0;\r\r\n\ttw.local.attachmentDetails.attachment[index].numOfCopies= 0;\r\r\n\tindex++;\t\r\r\n}\r\r\n\r\r\n", "isRule": "false", "guid": "1c46e760-1601-452a-a6e9-3c7307366dd6", "versionId": "8c5581c1-728d-425f-b37a-df1caf59fb1c"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "name": "Set ECM default properties", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.bd7101c9-b1c1-452d-8811-bab63d6c1ad0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1985", "versionId": "a084a414-0865-4c03-8310-30cd361f5de7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "436", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.bd7101c9-b1c1-452d-8811-bab63d6c1ad0", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.attachmentDetails.ecmProperties.defaultProperties =new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\nvar index= 0;\r\r\n\r\r\nfunction setECMProps(name, hidden, value , editable){\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].name = name;\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].hidden = hidden;\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].value = value;\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].editable = editable;\r\r\n\tindex++;\r\r\n}\r\r\n\r\r\nsetECMProps(\"Customer_Number_CIF\", false, tw.local.cif, false);\r\r\nsetECMProps( \"BPMInstanceId\", false, tw.local.instanceNo, false);\r\r\nsetECMProps(\"DocumentType\", true, \"ODC Document\",false);\r\r\nsetECMProps(\"RequestReferenceNumber\", false,tw.local.requestNumber,false);\r\r\n\r\r\nsetECMProps(\"DocumentTitle\", true,\"\",false);\r\r\nsetECMProps(\"RecordInformation\", true,\"\",false);\r\r\nsetECMProps(\"CmFederatedLockStatus\", true,\"\",false);\r\r\n\r\r\n//setECMProps(\"cmis:CmFederatedLockStatus\", false, \"Ts\",false);\r\r\n//setECMProps(\"cmis:name\", false,\"S\",false);\r\r\n//setECMProps(\"cmis:RecordInformation\", true,\"RI\",false);\r\r\n//RecordInformation\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "c8157413-d4d1-4185-8052-73c69af6e8a3", "versionId": "dd6f1c64-403d-4d34-9363-1b873ccb1d90"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0aedcc3d-a222-4fdc-a13c-571e355397d0", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.3212471c-3aa5-422b-837f-ff6f0fbed697", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1986", "versionId": "a97774a7-ca12-4a7b-9389-7199a3615a5b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.3212471c-3aa5-422b-837f-ff6f0fbed697", "haltProcess": "false", "guid": "b947b996-c784-4e69-94e7-177defeb7207", "versionId": "e3e9b541-61dd-457b-9650-3f5ea88180ef"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.789ca447-01f4-4eae-8c65-1feaabdbe24c", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "name": "Exp Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cde91268c0d1a44e:-10e36a59:18badede6df:-5d05", "versionId": "fe7bd252-e854-477a-abf6-1d70bbff903f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "383", "y": "202", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "7caaed83-a377-4667-bd3f-56a02f9cba2c", "versionId": "68a6f15d-1685-4d34-8f2e-5e0c1f9fb56b", "parameterMapping": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.77195f97-f7ff-4592-a6db-e5086fb97d9c", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "215ad725-eb37-4e3b-9657-ad43830c3cba", "versionId": "b0e845db-a79f-453f-bbb8-b81d7bea4f9d", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.9479e0f2-c7ff-4b15-bfa8-3aa791a4b083", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac", "useDefault": "false", "value": "\"Set document default properties 2\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "e8f93aba-8ee4-4556-baa0-397e2477b2df", "versionId": "ba7b91a3-4a7a-4d79-8914-31e07b4ee914", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a4229ae1-b2db-4b2a-bd03-fcaaf29474e7", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "78c04ed8-7ba4-42af-b296-94a83f644b2e", "versionId": "dde9101b-8079-469d-a99b-a66f361ce125", "description": {"isNull": "true"}}]}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "307", "y": "225", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Set document default properties 2", "id": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "cif", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.399c7a58-00b5-4451-9813-41c0b9652088", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"11112222\"", "useDefault": "false"}}}, {"name": "requestNumber", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.c28023fb-b45e-4b63-ae36-97e6df6421bc", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"12341234123412\"", "useDefault": "false"}}}, {"name": "instanceNo", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.25394215-074f-4b79-8e84-9a96d32cc83b", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"1111\"", "useDefault": "false"}}}], "ns16:dataOutput": [{"name": "attachmentDetails", "itemSubjectRef": "itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9", "isCollection": "false", "id": "2055.7d269650-ee48-4101-80db-2807cf921562"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.0261e8ad-a540-4682-88c5-87dff3eab23c"}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.399c7a58-00b5-4451-9813-41c0b9652088", "2055.c28023fb-b45e-4b63-ae36-97e6df6421bc", "2055.25394215-074f-4b79-8e84-9a96d32cc83b"]}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.7d269650-ee48-4101-80db-2807cf921562", "2055.0261e8ad-a540-4682-88c5-87dff3eab23c"]}}, "ns16:laneSet": {"id": "3ad21738-fe63-4f52-a34a-51587745acb5", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "9b455e62-b9c8-4de5-b329-438b93b2a05e", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["27653fd3-260b-41c1-b714-001c95294ec0", "0aedcc3d-a222-4fdc-a13c-571e355397d0", "4ad5b0ca-3520-4099-8d2e-6c8fab55f77d", "8f5b1d5b-266c-4378-8fad-952d8242b937", "e4db4e10-4560-4019-8a83-bb3659366a4d", "508ac6be-42fd-434a-86d5-c949141a1dc0", "b02758ea-4c8a-4941-8c22-1f218dbafb5a", "789ca447-01f4-4eae-8c65-1feaabdbe24c"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "27653fd3-260b-41c1-b714-001c95294ec0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.ac07b981-3f89-40d8-886c-daba8cbf8a06"}, "ns16:endEvent": [{"name": "End", "id": "0aedcc3d-a222-4fdc-a13c-571e355397d0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1986"}, "ns16:incoming": "76ff2e03-a220-4324-bcdd-4b504130689e"}, {"name": "End Event", "id": "b02758ea-4c8a-4941-8c22-1f218dbafb5a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "495", "y": "225", "width": "24", "height": "24"}}, "ns16:incoming": "eb50f183-97e9-4d0f-88fa-d9db3bb13b4a", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:errorEventDefinition": {"id": "b472843d-4929-4a4c-8a2d-3742e7b0632c", "eventImplId": "83f10afc-c9cf-4b5b-8906-d7a65c272219", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "27653fd3-260b-41c1-b714-001c95294ec0", "targetRef": "8f5b1d5b-266c-4378-8fad-952d8242b937", "name": "To Set ECM default properties", "id": "2027.ac07b981-3f89-40d8-886c-daba8cbf8a06", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4ad5b0ca-3520-4099-8d2e-6c8fab55f77d", "targetRef": "0aedcc3d-a222-4fdc-a13c-571e355397d0", "name": "To End", "id": "76ff2e03-a220-4324-bcdd-4b504130689e", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "8f5b1d5b-266c-4378-8fad-952d8242b937", "targetRef": "4ad5b0ca-3520-4099-8d2e-6c8fab55f77d", "name": "To Set ECM default properties", "id": "08d8c9bf-263f-432e-8c79-b725e99cf6e7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "508ac6be-42fd-434a-86d5-c949141a1dc0", "targetRef": "789ca447-01f4-4eae-8c65-1feaabdbe24c", "name": "To End Event", "id": "8ccad284-0e45-496b-8a1e-84d12ae0399d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "789ca447-01f4-4eae-8c65-1feaabdbe24c", "targetRef": "b02758ea-4c8a-4941-8c22-1f218dbafb5a", "name": "To End Event", "id": "eb50f183-97e9-4d0f-88fa-d9db3bb13b4a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Set ECM default properties", "id": "4ad5b0ca-3520-4099-8d2e-6c8fab55f77d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "436", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "08d8c9bf-263f-432e-8c79-b725e99cf6e7", "ns16:outgoing": "76ff2e03-a220-4324-bcdd-4b504130689e", "ns16:script": "tw.local.attachmentDetails.ecmProperties.defaultProperties =new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\nvar index= 0;\r\r\n\r\r\nfunction setECMProps(name, hidden, value , editable){\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].name = name;\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].hidden = hidden;\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].value = value;\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].editable = editable;\r\r\n\tindex++;\r\r\n}\r\r\n\r\r\nsetECMProps(\"Customer_Number_CIF\", false, tw.local.cif, false);\r\r\nsetECMProps( \"BPMInstanceId\", false, tw.local.instanceNo, false);\r\r\nsetECMProps(\"DocumentType\", true, \"ODC Document\",false);\r\r\nsetECMProps(\"RequestReferenceNumber\", false,tw.local.requestNumber,false);\r\r\n\r\r\nsetECMProps(\"DocumentTitle\", true,\"\",false);\r\r\nsetECMProps(\"RecordInformation\", true,\"\",false);\r\r\nsetECMProps(\"CmFederatedLockStatus\", true,\"\",false);\r\r\n\r\r\n//setECMProps(\"cmis:CmFederatedLockStatus\", false, \"Ts\",false);\r\r\n//setECMProps(\"cmis:name\", false,\"S\",false);\r\r\n//setECMProps(\"cmis:RecordInformation\", true,\"RI\",false);\r\r\n//RecordInformation\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Set attchments", "id": "8f5b1d5b-266c-4378-8fad-952d8242b937", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "291", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.ac07b981-3f89-40d8-886c-daba8cbf8a06", "ns16:outgoing": "08d8c9bf-263f-432e-8c79-b725e99cf6e7", "ns16:script": "var index= 0;\r\r\n\r\r\nif(!tw.local.attachmentDetails)\r\r\n  tw.local.attachmentDetails = {};\r\r\n\r\r\nif (tw.local.attachmentDetails.attachment == null || tw.local.attachmentDetails.attachment == undefined) {   \r\r\n\ttw.local.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\n\t\r\r\n\tsetProps(\"Customer Request\",  \"Customer Request\",  \"طلب العميل\"); \r\r\n\tsetProps(\"BILL OF EXCHANGE\",  \"BILL OF EXCHANGE\",  \"\"); \r\r\n\tsetProps(\"Invoice\",  \"Invoice\",  \"فاتورة\"); \r\r\n\tsetProps(\"BILL OF LADING\",  \"BILL OF LADING\",  \"\"); \t \t\t\r\r\n\tsetProps(\"AIRWAY BILL\", \"AIRWAY BILL\", \"\" ); \r\r\n\tsetProps(\"TRUCK CONSIGNMENT NOTE\",\"TRUCK CONSIGNMENT NOTE\", \"\"); \r\r\n\tsetProps(\"N/N BILL OF LADING\",\"N/N BILL OF LADING\",\"\"); \r\r\n\tsetProps(\"COURIER / POST RECEIPT\", \"COURIER / POST RECEIPT\" , \"\" ); \r\r\n\tsetProps(\"PACKING LIST\",\"PACKING LIST\",\"\"); \r\r\n\tsetProps(\"CERTIFICATE OF ORIGIN\",\"CERTIFICATE OF ORIGIN\", \"\" );\r\r\n\tsetProps(\"CERTIFICATE OF ANALYSIS\", \"CERTIFICATE OF ANALYSIS\", \"\");\r\r\n\tsetProps(\"INSURANCE POLICY / CERTIFICATE\", \"INSURANCE POLICY / CERTIFICATE\",\"\");\r\r\n\tsetProps(\"BENEFECIARY DECLARATION\", \"BENEFECIARY DECLARATION\", \"\");\r\r\n\tsetProps(\"NON RADIOACTIVE CERTIFICATE\", \"NON RADIOACTIVE CERTIFICATE\",\"\");\r\r\n\tsetProps(\"PHYTOSANITARY CERTIFICATE\", \"PHYTOSANITARY CERTIFICATE\",\"\");\r\r\n\tsetProps(\"CERTIFICATE OF ANALYSIS\",\"Bill of exchange/draft\", \"الكمبيالة\" );\r\r\n\tsetProps(\"HEALTH CERTIFICATE\",\"HEALTH CERTIFICATE\", \"\");\r\r\n\tsetProps(\"INSPECTION CERTIFICATE\", \"INSPECTION CERTIFICATE\",  \"\");\r\r\n\tsetProps(\"WARRANTY CERTIFICATE\", \"WARRANTY CERTIFICATE\",\"\");\r\r\n\tsetProps( \"TEST CERTIFICATE\",\"TEST CERTIFICATE\", \"\");\r\r\n\r\r\n\ttw.local.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\n\ttw.local.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\n\ttw.local.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\n\t\t \r\r\n}\t\r\r\n\r\r\nfunction setProps(name, desc, arabicName){\t\r\r\n\ttw.local.attachmentDetails.attachment[index]= new tw.object.Attachment();\t\r\r\n\ttw.local.attachmentDetails.attachment[index].name= name;\r\r\n\ttw.local.attachmentDetails.attachment[index].description= desc;\r\r\n\ttw.local.attachmentDetails.attachment[index].arabicName= arabicName;\r\r\n\ttw.local.attachmentDetails.attachment[index].numOfOriginals= 0;\r\r\n\ttw.local.attachmentDetails.attachment[index].numOfCopies= 0;\r\r\n\tindex++;\t\r\r\n}\r\r\n\r\r\n"}], "ns16:intermediateCatchEvent": {"name": "Error Event", "id": "508ac6be-42fd-434a-86d5-c949141a1dc0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "307", "y": "225", "width": "24", "height": "24"}}, "ns16:outgoing": "8ccad284-0e45-496b-8a1e-84d12ae0399d", "ns16:errorEventDefinition": {"id": "86cf04ff-de31-4f5d-8d6c-b62bc817feb6", "eventImplId": "ff25eef4-fb31-4598-88c7-3d79b71d29e9", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:callActivity": {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Exp Handling", "id": "789ca447-01f4-4eae-8c65-1feaabdbe24c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "383", "y": "202", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "8ccad284-0e45-496b-8a1e-84d12ae0399d", "ns16:outgoing": "eb50f183-97e9-4d0f-88fa-d9db3bb13b4a", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Set document default properties 2\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, "ns16:dataObject": {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.c2264df0-7fe3-410b-8614-1f97060e981f"}, "ns16:textAnnotation": {"textFormat": "text/plain", "id": "e4db4e10-4560-4019-8a83-bb3659366a4d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "792", "y": "53", "width": "288", "height": "171"}}, "ns16:text": "-\tCIF Number\r\n-\tRequest Reference Number\r\n-\tBPM Instance Id\r\n-\tDocument Type = “ODC Document”\r\n-\tDocument Name\r\n"}}}}, "link": [{"name": "To Set ECM default properties", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.08d8c9bf-263f-432e-8c79-b725e99cf6e7", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8f5b1d5b-266c-4378-8fad-952d8242b937", "2025.8f5b1d5b-266c-4378-8fad-952d8242b937"], "endStateId": "Out", "toProcessItemId": ["2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d", "2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d"], "guid": "025ec6bd-af09-405f-b3ed-eafb4da6c8a8", "versionId": "25fecff2-c580-471f-b23f-3e7c9904e0b4", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.76ff2e03-a220-4324-bcdd-4b504130689e", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d", "2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d"], "endStateId": "Out", "toProcessItemId": ["2025.0aedcc3d-a222-4fdc-a13c-571e355397d0", "2025.0aedcc3d-a222-4fdc-a13c-571e355397d0"], "guid": "39f292e9-4ab9-4447-8d9f-688b79eabe2a", "versionId": "655c9c46-d611-414a-914e-4b1841d105b4", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End Event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.eb50f183-97e9-4d0f-88fa-d9db3bb13b4a", "processId": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.789ca447-01f4-4eae-8c65-1feaabdbe24c", "2025.789ca447-01f4-4eae-8c65-1feaabdbe24c"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.b02758ea-4c8a-4941-8c22-1f218dbafb5a", "2025.b02758ea-4c8a-4941-8c22-1f218dbafb5a"], "guid": "dc7077d8-7053-42dc-9bf9-599dc38051d1", "versionId": "69146954-e971-4f77-b09d-aba8755670c9", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}