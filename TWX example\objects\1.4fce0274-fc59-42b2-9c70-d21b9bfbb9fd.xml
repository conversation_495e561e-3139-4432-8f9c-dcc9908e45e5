<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd" name="Calculate Default Amount Complete">
        <lastModified>1700205385188</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.4de511d4-cddf-4499-a64a-ee68cd415fa8</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>2bdc722b-fca9-4763-a101-7e8429edf04b</guid>
        <versionId>44347ee5-e5d4-4e52-902a-0c75ba8f74fb</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:374a" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.75531300-a765-4f91-ad74-d11bd0c4ee50"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"81242eea-adfd-41fb-b355-1fccdee5fc48"},{"incoming":["2b2317ab-8789-41f8-9ed1-92746bf854d3","534e56aa-adfe-4445-ba8d-2b01813e4a2d","81c0cd2d-a295-49d4-8b73-2f70326e4217"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":940,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3674"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"3267fe8b-e5fa-4fe9-8470-34aae4d125bc"},{"targetRef":"4de511d4-cddf-4499-a64a-ee68cd415fa8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Returned?","declaredType":"sequenceFlow","id":"2027.75531300-a765-4f91-ad74-d11bd0c4ee50","sourceRef":"81242eea-adfd-41fb-b355-1fccdee5fc48"},{"startQuantity":1,"outgoing":["1a361f70-459f-4993-8994-533b9fdf8cef"],"incoming":["f12f37ec-ea6a-4e9b-872c-b00f40484269"],"extensionElements":{"postAssignmentScript":["if (tw.local.defaultAmount != null || tw.local.defaultAmount != undefined) {\r\n\ttw.local.defaultAmountList.insertIntoList(tw.local.defaultAmountList.listLength, parseFloat(tw.local.defaultAmount))\r\n}else{\r\n\ttw.local.defaultAmountList.insertIntoList(tw.local.defaultAmountList.listLength, 0.0);\r\n}"],"nodeVisualInfo":[{"width":95,"x":547,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Calculate Change Amount","dataInputAssociation":[{"targetRef":"2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.defaultPercentage"]}}]},{"targetRef":"2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.aa2d49db-103b-4ad9-8e57-95487013a0fc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.8c593a04-d1d3-40d2-8288-991e270ebc48","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.8420386c-2f0f-431a-8b39-de0080607008","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.d08ff4c0-0f8d-47b1-8bd2-ff091c64a490","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.94ff52b2-0d25-41a6-810b-8c71f86202a8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.collateralAmnt"]}}]},{"targetRef":"2055.234755dd-aa6d-47c3-8a5d-f6860d21d2da","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC CREATION AND AMENDMENT\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1112d449-4e94-42e5-ba23-d69090c99a4d","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.9cf99306-b011-40e1-852b-7c1366b243e7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.03bb44c7-2ddc-40f7-8cf9-25e5eacb2c37"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.ec819699-1ccd-4c1b-8a08-e574e43b9765"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.defaultAmount"]}}],"sourceRef":["2055.45c20ab6-7f17-473e-891c-c8352d52a7c0"]}],"calledElement":"1.c63132b4-26e8-4922-945e-0c52767485a5"},{"targetRef":"b94b6753-bcd3-4608-8a6c-1fa9ddf4275b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Counter","declaredType":"sequenceFlow","id":"1a361f70-459f-4993-8994-533b9fdf8cef","sourceRef":"1112d449-4e94-42e5-ba23-d69090c99a4d"},{"startQuantity":1,"outgoing":["975d1fef-7866-4f3a-8524-ae8cd31f9332"],"incoming":["42108b88-dc29-4ff3-9165-3284dd157ee1","2027.75531300-a765-4f91-ad74-d11bd0c4ee50"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":229,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4de511d4-cddf-4499-a64a-ee68cd415fa8","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.counter = 0;\r\ntw.local.defaultPerList = [];\r\ntw.local.defaultAmountList = [];\r\n\r\nfor (var i=0; i&lt;tw.local.charges.listLength; i++) {\r\n\tif (tw.local.charges[i].rateType == \"Fixed Rate\") {\r\n\t\ttw.local.defaultPerList.insertIntoList(tw.local.defaultPerList.listLength, tw.local.charges[i].defaultPercentage+\"\");\t\r\n\t}\r\n}\r\ntw.local.max = tw.local.defaultPerList.listLength;"]}},{"targetRef":"99f0d666-d0b6-4259-bdc7-6f9668853836","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Counter","declaredType":"sequenceFlow","id":"975d1fef-7866-4f3a-8524-ae8cd31f9332","sourceRef":"4de511d4-cddf-4499-a64a-ee68cd415fa8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"defaultPercentage","isCollection":false,"declaredType":"dataObject","id":"2056.ba1f1593-85dd-40e9-9358-4b3ed2126fd6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"changeAmount","isCollection":false,"declaredType":"dataObject","id":"2056.966f8fd5-9709-4d7e-b39c-b3b902d1fa9d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.c32e6829-b58d-4e34-9536-3ff9a914bb4d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.3f104055-01d4-487e-84db-3555665968bc"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.9c03bcb7-5ea5-4882-9cc2-80453adf2251"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"changePercentage","isCollection":false,"declaredType":"dataObject","id":"2056.97803271-83d4-4a47-b27a-fdc337a2d4e0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"defaultAmount","isCollection":false,"declaredType":"dataObject","id":"2056.b0f1429e-bac9-47d6-8ed9-a64b69c547fa"},{"outgoing":["d9c763b6-2e99-4c0d-a642-5918eb1a2a4f","5639e31c-5279-4019-9a28-5a39f78e586a"],"incoming":["1a361f70-459f-4993-8994-533b9fdf8cef"],"default":"d9c763b6-2e99-4c0d-a642-5918eb1a2a4f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":675,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Counter","declaredType":"exclusiveGateway","id":"b94b6753-bcd3-4608-8a6c-1fa9ddf4275b"},{"targetRef":"73de0973-8c3b-4c94-b615-d18af27aa3e4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Set Output","declaredType":"sequenceFlow","id":"d9c763b6-2e99-4c0d-a642-5918eb1a2a4f","sourceRef":"b94b6753-bcd3-4608-8a6c-1fa9ddf4275b"},{"startQuantity":1,"outgoing":["f12f37ec-ea6a-4e9b-872c-b00f40484269"],"incoming":["975d1fef-7866-4f3a-8524-ae8cd31f9332","5639e31c-5279-4019-9a28-5a39f78e586a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":378,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Counter","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"99f0d666-d0b6-4259-bdc7-6f9668853836","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.defaultPercentage = tw.local.defaultPerList[tw.local.counter]\r\ntw.local.counter+=1;"]}},{"targetRef":"1112d449-4e94-42e5-ba23-d69090c99a4d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Calculate Change Amount","declaredType":"sequenceFlow","id":"f12f37ec-ea6a-4e9b-872c-b00f40484269","sourceRef":"99f0d666-d0b6-4259-bdc7-6f9668853836"},{"targetRef":"99f0d666-d0b6-4259-bdc7-6f9668853836","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.counter\t  &lt;\t  tw.local.max"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Counter","declaredType":"sequenceFlow","id":"5639e31c-5279-4019-9a28-5a39f78e586a","sourceRef":"b94b6753-bcd3-4608-8a6c-1fa9ddf4275b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"defaultPerList","isCollection":true,"declaredType":"dataObject","id":"2056.6616e110-0228-421a-aee8-b63e7f5e9385"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"counter","isCollection":false,"declaredType":"dataObject","id":"2056.1c20de3d-de1b-4385-9578-5e54029debff"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"defaultAmountList","isCollection":true,"declaredType":"dataObject","id":"2056.154f857d-5dcb-47f7-b1b9-a35a149c93af"},{"startQuantity":1,"outgoing":["2b2317ab-8789-41f8-9ed1-92746bf854d3"],"incoming":["d9c763b6-2e99-4c0d-a642-5918eb1a2a4f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":793,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"73de0973-8c3b-4c94-b615-d18af27aa3e4","scriptFormat":"text\/x-javascript","script":{"content":["var k = 0;\r\nfor (var i=0; i&lt;tw.local.charges.listLength; i++) {\r\n\tif (tw.local.charges[i].rateType == \"Fixed Rate\") {\r\n\t\ttw.local.charges[i].defaultAmount = Number(tw.local.defaultAmountList[k].toFixed(2));\r\n\t\ttw.local.charges[i].changeAmount = Number(tw.local.defaultAmountList[k].toFixed(2));\r\n\t\tk+=1;\r\n\t}\r\n}\r\n\r\n"]}},{"targetRef":"3267fe8b-e5fa-4fe9-8470-34aae4d125bc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2b2317ab-8789-41f8-9ed1-92746bf854d3","sourceRef":"73de0973-8c3b-4c94-b615-d18af27aa3e4"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"max","isCollection":false,"declaredType":"dataObject","id":"2056.3aaef0bc-a9cf-4075-8d8c-f23edc1dfdac"},{"outgoing":["42108b88-dc29-4ff3-9165-3284dd157ee1","534e56aa-adfe-4445-ba8d-2b01813e4a2d"],"default":"42108b88-dc29-4ff3-9165-3284dd157ee1","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":97,"y":136,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Returned?","declaredType":"exclusiveGateway","id":"aaf39374-2c9d-4d56-bb22-9489a4695b3b"},{"targetRef":"4de511d4-cddf-4499-a64a-ee68cd415fa8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Map Input","declaredType":"sequenceFlow","id":"42108b88-dc29-4ff3-9165-3284dd157ee1","sourceRef":"aaf39374-2c9d-4d56-bb22-9489a4695b3b"},{"targetRef":"3267fe8b-e5fa-4fe9-8470-34aae4d125bc","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isReturned == true || tw.local.hasFixedRate == false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true,"customBendPoint":[{"x":266,"y":345}]}]},"name":"To End","declaredType":"sequenceFlow","id":"534e56aa-adfe-4445-ba8d-2b01813e4a2d","sourceRef":"aaf39374-2c9d-4d56-bb22-9489a4695b3b"},{"parallelMultiple":false,"outgoing":["f6d76dff-9bd6-403f-82a9-0d7f774cba7c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"dd288cda-614a-4b04-b8cb-948ace244388"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"fd1b1498-5835-45c1-ac5a-6d872c26fffc","otherAttributes":{"eventImplId":"ca15f3d6-fde3-4de3-8515-056568a8a220"}}],"attachedToRef":"4de511d4-cddf-4499-a64a-ee68cd415fa8","extensionElements":{"nodeVisualInfo":[{"width":24,"x":264,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"ef14dc79-4df0-4a5c-a505-8f6321355c37","outputSet":{}},{"parallelMultiple":false,"outgoing":["a19e4c08-1274-4389-8be8-7f500acad406"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a93dfe11-729d-447f-80e0-6550a3806bb8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4191f773-83f0-4380-ba0d-e82c9b835d3a","otherAttributes":{"eventImplId":"6d6ba2ef-476f-48e9-8d33-1d85f04c8c9d"}}],"attachedToRef":"99f0d666-d0b6-4259-bdc7-6f9668853836","extensionElements":{"nodeVisualInfo":[{"width":24,"x":413,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"96eb1b91-abfc-4312-a131-5885f18ccb8d","outputSet":{}},{"parallelMultiple":false,"outgoing":["81338d20-144b-413f-8b4f-ec3cdba68089"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"f03b8fc4-5f8e-46ea-94be-2947e833d6e4"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"644c864d-791f-49f0-a4b1-441021055f82","otherAttributes":{"eventImplId":"b487df3e-f796-412e-80fe-13274a858adb"}}],"attachedToRef":"1112d449-4e94-42e5-ba23-d69090c99a4d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":582,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"58d184e2-7ef0-4a8c-b05e-b72a93a807ae","outputSet":{}},{"parallelMultiple":false,"outgoing":["97bea18e-aa86-4fcd-8e62-abc6c9271b7b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"622e9159-75df-4d1e-866d-4fbed4294d39"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"612f1909-451c-41ac-8ac6-edf3e87f4ed1","otherAttributes":{"eventImplId":"4109f61f-1b17-4341-810c-d2d636a84839"}}],"attachedToRef":"73de0973-8c3b-4c94-b615-d18af27aa3e4","extensionElements":{"nodeVisualInfo":[{"width":24,"x":828,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"adbc8ee8-018e-4f88-aa38-bd5b5ea1bbbd","outputSet":{}},{"startQuantity":1,"outgoing":["81c0cd2d-a295-49d4-8b73-2f70326e4217"],"incoming":["f6d76dff-9bd6-403f-82a9-0d7f774cba7c","a19e4c08-1274-4389-8be8-7f500acad406","81338d20-144b-413f-8b4f-ec3cdba68089","97bea18e-aa86-4fcd-8e62-abc6c9271b7b"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":551,"y":210,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Get ODC Charges\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"e9cec704-3ff3-4ec3-8357-f42a21d6e5ba","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"e9cec704-3ff3-4ec3-8357-f42a21d6e5ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"f6d76dff-9bd6-403f-82a9-0d7f774cba7c","sourceRef":"ef14dc79-4df0-4a5c-a505-8f6321355c37"},{"targetRef":"e9cec704-3ff3-4ec3-8357-f42a21d6e5ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"a19e4c08-1274-4389-8be8-7f500acad406","sourceRef":"96eb1b91-abfc-4312-a131-5885f18ccb8d"},{"targetRef":"e9cec704-3ff3-4ec3-8357-f42a21d6e5ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"81338d20-144b-413f-8b4f-ec3cdba68089","sourceRef":"58d184e2-7ef0-4a8c-b05e-b72a93a807ae"},{"targetRef":"e9cec704-3ff3-4ec3-8357-f42a21d6e5ba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"97bea18e-aa86-4fcd-8e62-abc6c9271b7b","sourceRef":"adbc8ee8-018e-4f88-aa38-bd5b5ea1bbbd"},{"targetRef":"3267fe8b-e5fa-4fe9-8470-34aae4d125bc","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"81c0cd2d-a295-49d4-8b73-2f70326e4217","sourceRef":"e9cec704-3ff3-4ec3-8357-f42a21d6e5ba"}],"laneSet":[{"id":"b5d1e583-9582-4eee-a8e7-17cc1f6ae53d","lane":[{"flowNodeRef":["81242eea-adfd-41fb-b355-1fccdee5fc48","3267fe8b-e5fa-4fe9-8470-34aae4d125bc","1112d449-4e94-42e5-ba23-d69090c99a4d","4de511d4-cddf-4499-a64a-ee68cd415fa8","b94b6753-bcd3-4608-8a6c-1fa9ddf4275b","99f0d666-d0b6-4259-bdc7-6f9668853836","73de0973-8c3b-4c94-b615-d18af27aa3e4","aaf39374-2c9d-4d56-bb22-9489a4695b3b","ef14dc79-4df0-4a5c-a505-8f6321355c37","96eb1b91-abfc-4312-a131-5885f18ccb8d","58d184e2-7ef0-4a8c-b05e-b72a93a807ae","adbc8ee8-018e-4f88-aa38-bd5b5ea1bbbd","e9cec704-3ff3-4ec3-8357-f42a21d6e5ba"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"9995add6-87d2-4054-8260-2f9c69a6cfb6","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Calculate Default Amount Complete","declaredType":"process","id":"1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061","name":"charges","isCollection":true,"id":"2055.f3009431-81a1-4331-8f48-100ccdda4aea"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.880a59bf-b853-4f92-94be-160455121ee0"}],"inputSet":[{"dataInputRefs":["2055.737f0e64-d76d-4c76-9b4b-e87c7878b455","2055.ba300aef-d348-49c6-bd13-01d0154c466e"]}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061","name":"charges","isCollection":true,"id":"2055.737f0e64-d76d-4c76-9b4b-e87c7878b455"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"2000\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"collateralAmnt","isCollection":false,"id":"2055.ba300aef-d348-49c6-bd13-01d0154c466e"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="charges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.737f0e64-d76d-4c76-9b4b-e87c7878b455</processParameterId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.e3018bad-b453-4bf5-96fd-09a5141cd061</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c19296d4-7355-4b26-a5a2-bb82f8945ac8</guid>
            <versionId>3fdc3dd3-8985-4cd5-8815-d7690c1c724a</versionId>
        </processParameter>
        <processParameter name="collateralAmnt">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ba300aef-d348-49c6-bd13-01d0154c466e</processParameterId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8c80dfa0-4c23-47ea-82ba-946ca589243c</guid>
            <versionId>6059b40c-6f5a-41f1-9f36-b4908380af45</versionId>
        </processParameter>
        <processParameter name="charges">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f3009431-81a1-4331-8f48-100ccdda4aea</processParameterId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.e3018bad-b453-4bf5-96fd-09a5141cd061</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5600da23-4dfc-4597-9bc4-f8b43d40b255</guid>
            <versionId>8ebbd9d3-9ccb-4e9e-97e1-130fa039bf31</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.880a59bf-b853-4f92-94be-160455121ee0</processParameterId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bb5453aa-fc5e-45e4-94e9-0b7ad1d28a66</guid>
            <versionId>4690bfd5-b106-460d-a3bd-c41945b99aba</versionId>
        </processParameter>
        <processVariable name="defaultPercentage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ba1f1593-85dd-40e9-9358-4b3ed2126fd6</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4e252d1d-5bea-4e7b-a401-93289d4e5934</guid>
            <versionId>e52c48cc-f654-43cd-b5aa-f3a44dd50147</versionId>
        </processVariable>
        <processVariable name="changeAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.966f8fd5-9709-4d7e-b39c-b3b902d1fa9d</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>88bdf80b-96b9-40bd-b7df-af06c95db84a</guid>
            <versionId>f4dc624c-6c55-496c-967e-9c119ff299e4</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c32e6829-b58d-4e34-9536-3ff9a914bb4d</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e9819243-329d-407c-93ce-bd94259f648b</guid>
            <versionId>b7b9104c-ba55-4267-9c90-29e211e5f5e0</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3f104055-01d4-487e-84db-3555665968bc</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>453684ac-da34-4085-932a-a3b1dd9ada2e</guid>
            <versionId>f4df3378-c277-45e2-89f1-a8899aa86cf2</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9c03bcb7-5ea5-4882-9cc2-80453adf2251</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a9a9fc50-8709-4fe7-bc3a-42f48cc00571</guid>
            <versionId>56ea9ca3-5f7c-4829-b354-73b2a8008289</versionId>
        </processVariable>
        <processVariable name="changePercentage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.97803271-83d4-4a47-b27a-fdc337a2d4e0</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7402b8ac-6c56-4051-8e02-4c7f59d952dc</guid>
            <versionId>c2c32e3f-3f5e-41f7-ac0a-d6fe33ffc68b</versionId>
        </processVariable>
        <processVariable name="defaultAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b0f1429e-bac9-47d6-8ed9-a64b69c547fa</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>13f561cb-4a03-4cf9-97d8-1a8d527b3cdf</guid>
            <versionId>0df6207b-1c03-4f85-9058-e76ad47787c9</versionId>
        </processVariable>
        <processVariable name="defaultPerList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6616e110-0228-421a-aee8-b63e7f5e9385</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d70c3041-30c3-4cc7-ab0d-3984839c6dc0</guid>
            <versionId>135ed028-891d-44ee-9399-309d496ddb50</versionId>
        </processVariable>
        <processVariable name="counter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1c20de3d-de1b-4385-9578-5e54029debff</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8f48fd71-6a1a-42b8-aa67-c08d66e8dc2e</guid>
            <versionId>93b7fdb7-1804-4d1e-a1a1-1ec2b7d3e82c</versionId>
        </processVariable>
        <processVariable name="defaultAmountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.154f857d-5dcb-47f7-b1b9-a35a149c93af</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>29f48f41-e489-4923-be89-8cc56f319d9b</guid>
            <versionId>abb18e3e-f1fe-431f-b874-f1056c7655c2</versionId>
        </processVariable>
        <processVariable name="max">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3aaef0bc-a9cf-4075-8d8c-f23edc1dfdac</processVariableId>
            <description isNull="true" />
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e2c56f60-2b05-42a6-8bd8-5f8a3bbb0429</guid>
            <versionId>8a7471bd-f501-4be4-a74f-b33115b1483f</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4de511d4-cddf-4499-a64a-ee68cd415fa8</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>Map Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.*************-408a-9f0b-08dd82d95140</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3673</guid>
            <versionId>24114d2c-839e-4774-a758-32235999f098</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="229" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3742</errorHandlerItem>
                <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.*************-408a-9f0b-08dd82d95140</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.counter = 0;&#xD;
tw.local.defaultPerList = [];&#xD;
tw.local.defaultAmountList = [];&#xD;
&#xD;
for (var i=0; i&lt;tw.local.charges.listLength; i++) {&#xD;
	if (tw.local.charges[i].rateType == "Fixed Rate") {&#xD;
		tw.local.defaultPerList.insertIntoList(tw.local.defaultPerList.listLength, tw.local.charges[i].defaultPercentage+"");	&#xD;
	}&#xD;
}&#xD;
tw.local.max = tw.local.defaultPerList.listLength;</script>
                <isRule>false</isRule>
                <guid>ef11f243-228a-4b5b-9879-32a81b189cbb</guid>
                <versionId>fc88cfbd-6960-4fdf-8d5a-71dc9bbdd1dd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1112d449-4e94-42e5-ba23-d69090c99a4d</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>Calculate Change Amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.526a992b-2893-4251-b458-4d6976662e5f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3678</guid>
            <versionId>395d346f-6a98-4baa-9d17-63648a4b3823</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.bd6e2f68-237e-4465-a3d5-d5de0f8ccc88</processItemPrePostId>
                <processItemId>2025.1112d449-4e94-42e5-ba23-d69090c99a4d</processItemId>
                <location>2</location>
                <script>if (tw.local.defaultAmount != null || tw.local.defaultAmount != undefined) {&#xD;
	tw.local.defaultAmountList.insertIntoList(tw.local.defaultAmountList.listLength, parseFloat(tw.local.defaultAmount))&#xD;
}else{&#xD;
	tw.local.defaultAmountList.insertIntoList(tw.local.defaultAmountList.listLength, 0.0);&#xD;
}</script>
                <guid>9f4d266a-21e8-4075-8484-c66d6e3f3ad8</guid>
                <versionId>b04e0730-8a17-47b6-a227-b84e1d2fe65d</versionId>
            </processPrePosts>
            <layoutData x="547" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3742</errorHandlerItem>
                <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.526a992b-2893-4251-b458-4d6976662e5f</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.c63132b4-26e8-4922-945e-0c52767485a5</attachedProcessRef>
                <guid>4d6a8542-21c7-498a-8c34-b0656c43ea5b</guid>
                <versionId>656b1d89-610d-4bc9-8114-52952e215ec9</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bb3d468e-913c-4451-b2ac-a133b05ce45b</parameterMappingId>
                    <processParameterId>2055.ec819699-1ccd-4c1b-8a08-e574e43b9765</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b4d33b12-6d80-4328-8cc3-69deb94a0d63</guid>
                    <versionId>275518ce-5c6f-4cf6-aaa4-d731adefc826</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c42007b2-3cd5-4d02-954a-33c476c48cf9</parameterMappingId>
                    <processParameterId>2055.8c593a04-d1d3-40d2-8288-991e270ebc48</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>705f5d25-0f35-435b-bd30-25df4f3be279</guid>
                    <versionId>326cbfd7-597f-4b4e-968d-0d35c49f3f3f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2696e7ae-38d8-4325-946f-bceaece8196d</parameterMappingId>
                    <processParameterId>2055.234755dd-aa6d-47c3-8a5d-f6860d21d2da</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC CREATION AND AMENDMENT"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0a98bb0b-2949-4fde-8a98-3e3aaa576c1c</guid>
                    <versionId>3766233d-06f5-48d2-b85f-be82d62bf1b3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b86e1f28-d04c-439d-8491-1b91dfb1e8cf</parameterMappingId>
                    <processParameterId>2055.aa2d49db-103b-4ad9-8e57-95487013a0fc</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>624d3daa-a7f0-44e8-a804-50bddeb7b496</guid>
                    <versionId>4777a9b9-3b7d-4259-9827-a8223c16a3fd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e1db7dfe-c94c-423e-a113-40ce506cb815</parameterMappingId>
                    <processParameterId>2055.9cf99306-b011-40e1-852b-7c1366b243e7</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c0b13a79-d0e0-44c7-bc7a-2a29f55d7f82</guid>
                    <versionId>4bf65d77-f4a3-4ab4-9732-f8f751783b3f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cccfc9b8-df5a-440c-bab2-5a9e661e1f21</parameterMappingId>
                    <processParameterId>2055.03bb44c7-2ddc-40f7-8cf9-25e5eacb2c37</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c69ae220-67a4-4615-ac32-2c5f89d5fd67</guid>
                    <versionId>510d6b2b-**************-8055a76c35b3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7596c05a-98f3-45a9-a64c-52f1e5c4bc43</parameterMappingId>
                    <processParameterId>2055.8420386c-2f0f-431a-8b39-de0080607008</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a71734e8-99e6-4ac7-855f-75420498f95c</guid>
                    <versionId>5f747a1c-1263-4fb8-a3ef-7e091f5c346c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.27a5d4fe-2797-481e-9e41-e050a83f805f</parameterMappingId>
                    <processParameterId>2055.94ff52b2-0d25-41a6-810b-8c71f86202a8</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.collateralAmnt</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>134cccdc-f088-4bd3-a59b-70fcfe3c21ab</guid>
                    <versionId>76e2269e-d079-46f4-9d14-f9e9434ceebe</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="chargeAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7e066b9e-b882-4cd1-915a-f4f0a9e047f5</parameterMappingId>
                    <processParameterId>2055.45c20ab6-7f17-473e-891c-c8352d52a7c0</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.defaultAmount</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4540364f-eb98-477e-8bd5-de9be89af684</guid>
                    <versionId>bef197d9-a8fe-4079-8845-396a56ab6075</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="Percentage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f5955b06-8dac-436b-b4cb-3c3882a29a78</parameterMappingId>
                    <processParameterId>2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.defaultPercentage</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6222da30-3eb8-4d89-9659-a97fc11a51c7</guid>
                    <versionId>d5f9bb8a-1f1d-428d-a80c-425c44543c77</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4c32989d-984a-416f-9df5-abe4f4783cfb</parameterMappingId>
                    <processParameterId>2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>*************-497d-a787-e6562f08ec95</guid>
                    <versionId>e523dfdf-6d71-4f34-b757-c71830c4a0a2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6195f17d-1246-494e-a97c-db0f5c34bf2e</parameterMappingId>
                    <processParameterId>2055.d08ff4c0-0f8d-47b1-8bd2-ff091c64a490</processParameterId>
                    <parameterMappingParentId>3012.526a992b-2893-4251-b458-4d6976662e5f</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2e4d61e0-8339-4bdc-aab5-2d14927f9afd</guid>
                    <versionId>fd7541ea-d8ca-45cd-94e6-3f493e8d1c06</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.73de0973-8c3b-4c94-b615-d18af27aa3e4</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>Set Output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ed7b342a-5dcd-4850-a896-a2d67e92a0f7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3676</guid>
            <versionId>3a0d26f0-75d3-4637-b878-d2321d0dfc4e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="793" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3742</errorHandlerItem>
                <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ed7b342a-5dcd-4850-a896-a2d67e92a0f7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var k = 0;&#xD;
for (var i=0; i&lt;tw.local.charges.listLength; i++) {&#xD;
	if (tw.local.charges[i].rateType == "Fixed Rate") {&#xD;
		tw.local.charges[i].defaultAmount = Number(tw.local.defaultAmountList[k].toFixed(2));&#xD;
		tw.local.charges[i].changeAmount = Number(tw.local.defaultAmountList[k].toFixed(2));&#xD;
		k+=1;&#xD;
	}&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>b029e5a1-2e4d-4365-9c1f-03992bb151c9</guid>
                <versionId>793588a8-d20d-42e3-b299-3b4e623e4a7c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3267fe8b-e5fa-4fe9-8470-34aae4d125bc</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.66301787-e423-41eb-a7f0-a5f7c3e099d4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3674</guid>
            <versionId>55f163a6-6b80-45c6-a11e-3c7e5f537ab5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="940" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.66301787-e423-41eb-a7f0-a5f7c3e099d4</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>6d84771e-11da-4675-b091-cc4e0684686d</guid>
                <versionId>c23e83e2-bbf6-4ba8-be4e-4f08588f7df1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.99f0d666-d0b6-4259-bdc7-6f9668853836</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>Counter</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.50247491-79e2-49aa-853f-b4b205ba5f9b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3675</guid>
            <versionId>7c0e3090-2a86-4214-8e8c-d224f903d95e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="378" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3742</errorHandlerItem>
                <errorHandlerItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.50247491-79e2-49aa-853f-b4b205ba5f9b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.defaultPercentage = tw.local.defaultPerList[tw.local.counter]&#xD;
tw.local.counter+=1;</script>
                <isRule>false</isRule>
                <guid>65964503-562e-4990-8481-a50cf16199c0</guid>
                <versionId>16ee6424-49df-4293-b4ed-8aab3407fc67</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.6a4b834d-afcb-4224-91a8-48ffcafb64ed</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3742</guid>
            <versionId>80a1f938-b680-4dac-8804-ba5f821649fb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="551" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.6a4b834d-afcb-4224-91a8-48ffcafb64ed</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>e6d9b77d-683a-461a-a688-c9a426714382</guid>
                <versionId>edde6897-f563-4f3d-b585-c4b1d669e1a5</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7ba6e5ae-b6eb-4111-8d7e-982ddf471f67</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.6a4b834d-afcb-4224-91a8-48ffcafb64ed</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ccd1c08b-1aa0-4f1c-a9c1-c4b3f97d2d74</guid>
                    <versionId>4bff27e0-9bb4-4ae6-a066-45fcc92c9d05</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.74825d7e-516b-47fb-9fb2-02bb4fe977a4</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.6a4b834d-afcb-4224-91a8-48ffcafb64ed</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6cb91684-adbe-4162-b988-6922c36191b4</guid>
                    <versionId>65c51b88-f1e4-434e-9b80-3d7db3a5fbb5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.512a8a79-d397-46fd-b454-b88a03bc5295</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.6a4b834d-afcb-4224-91a8-48ffcafb64ed</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Get ODC Charges"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8a46b985-547c-4112-925a-35119fc4c30e</guid>
                    <versionId>a1b92693-e1a3-4160-91ff-bca8f05f8475</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>Counter</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.9a51998c-61fc-484b-bc2c-13c251228524</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:367a</guid>
            <versionId>a99d4873-f5df-4088-9b8b-544202dced34</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="675" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.9a51998c-61fc-484b-bc2c-13c251228524</switchId>
                <guid>4b2d571d-f4b5-4376-bcca-2ce8dcbcf735</guid>
                <versionId>799632da-9ee3-4b16-a9d8-9ec76c8fb98c</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.0ee6e7c7-0ddf-4df7-a028-c3d60e4d5857</switchConditionId>
                    <switchId>3013.9a51998c-61fc-484b-bc2c-13c251228524</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3748</endStateId>
                    <condition>tw.local.counter	  &lt;	  tw.local.max</condition>
                    <guid>2b5e6215-19be-4d5b-a673-3f35fe8de219</guid>
                    <versionId>546deda2-9513-4ef1-96fd-93e5addf8a2a</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.aaf39374-2c9d-4d56-bb22-9489a4695b3b</processItemId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <name>Returned?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.7cc7e86a-8c56-4059-9092-dde7484ae4af</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3677</guid>
            <versionId>d3b24142-63fc-4275-91a9-b159ca5ace6e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="97" y="136">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.7cc7e86a-8c56-4059-9092-dde7484ae4af</switchId>
                <guid>acf7d52b-37af-4f08-aacf-140412eb52c0</guid>
                <versionId>cfe6f2fd-385b-4d79-8170-f065f3f00e1b</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.c7f071b5-b202-4f6b-9fb3-89f4c3fd38d5</switchConditionId>
                    <switchId>3013.7cc7e86a-8c56-4059-9092-dde7484ae4af</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3749</endStateId>
                    <condition>tw.local.isReturned == true || tw.local.hasFixedRate == false</condition>
                    <guid>f3b9fcd5-8dee-4e4c-a350-574682bfd865</guid>
                    <versionId>d5c827fd-4e12-4591-9b20-a3f9ef9ce0ef</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.4de511d4-cddf-4499-a64a-ee68cd415fa8</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="topLeft" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Calculate Default Amount Complete" id="1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="charges" itemSubjectRef="itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061" isCollection="true" id="2055.737f0e64-d76d-4c76-9b4b-e87c7878b455">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false" />
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="collateralAmnt" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.ba300aef-d348-49c6-bd13-01d0154c466e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"2000"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="charges" itemSubjectRef="itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061" isCollection="true" id="2055.f3009431-81a1-4331-8f48-100ccdda4aea" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.880a59bf-b853-4f92-94be-160455121ee0" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.737f0e64-d76d-4c76-9b4b-e87c7878b455</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.ba300aef-d348-49c6-bd13-01d0154c466e</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="b5d1e583-9582-4eee-a8e7-17cc1f6ae53d">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="9995add6-87d2-4054-8260-2f9c69a6cfb6" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>81242eea-adfd-41fb-b355-1fccdee5fc48</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3267fe8b-e5fa-4fe9-8470-34aae4d125bc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1112d449-4e94-42e5-ba23-d69090c99a4d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4de511d4-cddf-4499-a64a-ee68cd415fa8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>99f0d666-d0b6-4259-bdc7-6f9668853836</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>73de0973-8c3b-4c94-b615-d18af27aa3e4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>aaf39374-2c9d-4d56-bb22-9489a4695b3b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ef14dc79-4df0-4a5c-a505-8f6321355c37</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>96eb1b91-abfc-4312-a131-5885f18ccb8d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>58d184e2-7ef0-4a8c-b05e-b72a93a807ae</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>adbc8ee8-018e-4f88-aa38-bd5b5ea1bbbd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="81242eea-adfd-41fb-b355-1fccdee5fc48">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.75531300-a765-4f91-ad74-d11bd0c4ee50</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="3267fe8b-e5fa-4fe9-8470-34aae4d125bc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="940" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3674</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2b2317ab-8789-41f8-9ed1-92746bf854d3</ns16:incoming>
                        
                        
                        <ns16:incoming>534e56aa-adfe-4445-ba8d-2b01813e4a2d</ns16:incoming>
                        
                        
                        <ns16:incoming>81c0cd2d-a295-49d4-8b73-2f70326e4217</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="81242eea-adfd-41fb-b355-1fccdee5fc48" targetRef="4de511d4-cddf-4499-a64a-ee68cd415fa8" name="To Returned?" id="2027.75531300-a765-4f91-ad74-d11bd0c4ee50">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c63132b4-26e8-4922-945e-0c52767485a5" name="Calculate Change Amount" id="1112d449-4e94-42e5-ba23-d69090c99a4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="547" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>if (tw.local.defaultAmount != null || tw.local.defaultAmount != undefined) {&#xD;
	tw.local.defaultAmountList.insertIntoList(tw.local.defaultAmountList.listLength, parseFloat(tw.local.defaultAmount))&#xD;
}else{&#xD;
	tw.local.defaultAmountList.insertIntoList(tw.local.defaultAmountList.listLength, 0.0);&#xD;
}</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f12f37ec-ea6a-4e9b-872c-b00f40484269</ns16:incoming>
                        
                        
                        <ns16:outgoing>1a361f70-459f-4993-8994-533b9fdf8cef</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.defaultPercentage</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.aa2d49db-103b-4ad9-8e57-95487013a0fc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8c593a04-d1d3-40d2-8288-991e270ebc48</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8420386c-2f0f-431a-8b39-de0080607008</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d08ff4c0-0f8d-47b1-8bd2-ff091c64a490</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94ff52b2-0d25-41a6-810b-8c71f86202a8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.collateralAmnt</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.234755dd-aa6d-47c3-8a5d-f6860d21d2da</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC CREATION AND AMENDMENT"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9cf99306-b011-40e1-852b-7c1366b243e7</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.03bb44c7-2ddc-40f7-8cf9-25e5eacb2c37</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ec819699-1ccd-4c1b-8a08-e574e43b9765</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.45c20ab6-7f17-473e-891c-c8352d52a7c0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.defaultAmount</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="1112d449-4e94-42e5-ba23-d69090c99a4d" targetRef="b94b6753-bcd3-4608-8a6c-1fa9ddf4275b" name="To Counter" id="1a361f70-459f-4993-8994-533b9fdf8cef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map Input" id="4de511d4-cddf-4499-a64a-ee68cd415fa8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="229" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>42108b88-dc29-4ff3-9165-3284dd157ee1</ns16:incoming>
                        
                        
                        <ns16:incoming>2027.75531300-a765-4f91-ad74-d11bd0c4ee50</ns16:incoming>
                        
                        
                        <ns16:outgoing>975d1fef-7866-4f3a-8524-ae8cd31f9332</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.counter = 0;&#xD;
tw.local.defaultPerList = [];&#xD;
tw.local.defaultAmountList = [];&#xD;
&#xD;
for (var i=0; i&lt;tw.local.charges.listLength; i++) {&#xD;
	if (tw.local.charges[i].rateType == "Fixed Rate") {&#xD;
		tw.local.defaultPerList.insertIntoList(tw.local.defaultPerList.listLength, tw.local.charges[i].defaultPercentage+"");	&#xD;
	}&#xD;
}&#xD;
tw.local.max = tw.local.defaultPerList.listLength;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4de511d4-cddf-4499-a64a-ee68cd415fa8" targetRef="99f0d666-d0b6-4259-bdc7-6f9668853836" name="To Counter" id="975d1fef-7866-4f3a-8524-ae8cd31f9332">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="defaultPercentage" id="2056.ba1f1593-85dd-40e9-9358-4b3ed2126fd6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="changeAmount" id="2056.966f8fd5-9709-4d7e-b39c-b3b902d1fa9d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.c32e6829-b58d-4e34-9536-3ff9a914bb4d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.3f104055-01d4-487e-84db-3555665968bc" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.9c03bcb7-5ea5-4882-9cc2-80453adf2251" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="changePercentage" id="2056.97803271-83d4-4a47-b27a-fdc337a2d4e0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="defaultAmount" id="2056.b0f1429e-bac9-47d6-8ed9-a64b69c547fa" />
                    
                    
                    <ns16:exclusiveGateway default="d9c763b6-2e99-4c0d-a642-5918eb1a2a4f" name="Counter" id="b94b6753-bcd3-4608-8a6c-1fa9ddf4275b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="675" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>1a361f70-459f-4993-8994-533b9fdf8cef</ns16:incoming>
                        
                        
                        <ns16:outgoing>d9c763b6-2e99-4c0d-a642-5918eb1a2a4f</ns16:outgoing>
                        
                        
                        <ns16:outgoing>5639e31c-5279-4019-9a28-5a39f78e586a</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="b94b6753-bcd3-4608-8a6c-1fa9ddf4275b" targetRef="73de0973-8c3b-4c94-b615-d18af27aa3e4" name="To Set Output" id="d9c763b6-2e99-4c0d-a642-5918eb1a2a4f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Counter" id="99f0d666-d0b6-4259-bdc7-6f9668853836">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="378" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>975d1fef-7866-4f3a-8524-ae8cd31f9332</ns16:incoming>
                        
                        
                        <ns16:incoming>5639e31c-5279-4019-9a28-5a39f78e586a</ns16:incoming>
                        
                        
                        <ns16:outgoing>f12f37ec-ea6a-4e9b-872c-b00f40484269</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.defaultPercentage = tw.local.defaultPerList[tw.local.counter]&#xD;
tw.local.counter+=1;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="99f0d666-d0b6-4259-bdc7-6f9668853836" targetRef="1112d449-4e94-42e5-ba23-d69090c99a4d" name="To Calculate Change Amount" id="f12f37ec-ea6a-4e9b-872c-b00f40484269">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="b94b6753-bcd3-4608-8a6c-1fa9ddf4275b" targetRef="99f0d666-d0b6-4259-bdc7-6f9668853836" name="To Counter" id="5639e31c-5279-4019-9a28-5a39f78e586a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.counter	  &lt;	  tw.local.max</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="defaultPerList" id="2056.6616e110-0228-421a-aee8-b63e7f5e9385" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="counter" id="2056.1c20de3d-de1b-4385-9578-5e54029debff" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="true" name="defaultAmountList" id="2056.154f857d-5dcb-47f7-b1b9-a35a149c93af" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Output" id="73de0973-8c3b-4c94-b615-d18af27aa3e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="793" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d9c763b6-2e99-4c0d-a642-5918eb1a2a4f</ns16:incoming>
                        
                        
                        <ns16:outgoing>2b2317ab-8789-41f8-9ed1-92746bf854d3</ns16:outgoing>
                        
                        
                        <ns16:script>var k = 0;&#xD;
for (var i=0; i&lt;tw.local.charges.listLength; i++) {&#xD;
	if (tw.local.charges[i].rateType == "Fixed Rate") {&#xD;
		tw.local.charges[i].defaultAmount = Number(tw.local.defaultAmountList[k].toFixed(2));&#xD;
		tw.local.charges[i].changeAmount = Number(tw.local.defaultAmountList[k].toFixed(2));&#xD;
		k+=1;&#xD;
	}&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="73de0973-8c3b-4c94-b615-d18af27aa3e4" targetRef="3267fe8b-e5fa-4fe9-8470-34aae4d125bc" name="To End" id="2b2317ab-8789-41f8-9ed1-92746bf854d3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="max" id="2056.3aaef0bc-a9cf-4075-8d8c-f23edc1dfdac" />
                    
                    
                    <ns16:exclusiveGateway default="42108b88-dc29-4ff3-9165-3284dd157ee1" name="Returned?" id="aaf39374-2c9d-4d56-bb22-9489a4695b3b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="97" y="136" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>42108b88-dc29-4ff3-9165-3284dd157ee1</ns16:outgoing>
                        
                        
                        <ns16:outgoing>534e56aa-adfe-4445-ba8d-2b01813e4a2d</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="aaf39374-2c9d-4d56-bb22-9489a4695b3b" targetRef="4de511d4-cddf-4499-a64a-ee68cd415fa8" name="To Map Input" id="42108b88-dc29-4ff3-9165-3284dd157ee1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="aaf39374-2c9d-4d56-bb22-9489a4695b3b" targetRef="3267fe8b-e5fa-4fe9-8470-34aae4d125bc" name="To End" id="534e56aa-adfe-4445-ba8d-2b01813e4a2d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="266" y="345" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isReturned == true || tw.local.hasFixedRate == false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="4de511d4-cddf-4499-a64a-ee68cd415fa8" parallelMultiple="false" name="Error" id="ef14dc79-4df0-4a5c-a505-8f6321355c37">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="264" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f6d76dff-9bd6-403f-82a9-0d7f774cba7c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="dd288cda-614a-4b04-b8cb-948ace244388" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="fd1b1498-5835-45c1-ac5a-6d872c26fffc" eventImplId="ca15f3d6-fde3-4de3-8515-056568a8a220">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="99f0d666-d0b6-4259-bdc7-6f9668853836" parallelMultiple="false" name="Error1" id="96eb1b91-abfc-4312-a131-5885f18ccb8d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="413" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a19e4c08-1274-4389-8be8-7f500acad406</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a93dfe11-729d-447f-80e0-6550a3806bb8" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="4191f773-83f0-4380-ba0d-e82c9b835d3a" eventImplId="6d6ba2ef-476f-48e9-8d33-1d85f04c8c9d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1112d449-4e94-42e5-ba23-d69090c99a4d" parallelMultiple="false" name="Error2" id="58d184e2-7ef0-4a8c-b05e-b72a93a807ae">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="582" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>81338d20-144b-413f-8b4f-ec3cdba68089</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="f03b8fc4-5f8e-46ea-94be-2947e833d6e4" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="644c864d-791f-49f0-a4b1-441021055f82" eventImplId="b487df3e-f796-412e-80fe-13274a858adb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="73de0973-8c3b-4c94-b615-d18af27aa3e4" parallelMultiple="false" name="Error3" id="adbc8ee8-018e-4f88-aa38-bd5b5ea1bbbd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="828" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>97bea18e-aa86-4fcd-8e62-abc6c9271b7b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="622e9159-75df-4d1e-866d-4fbed4294d39" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="612f1909-451c-41ac-8ac6-edf3e87f4ed1" eventImplId="4109f61f-1b17-4341-810c-d2d636a84839">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exception Handling" id="e9cec704-3ff3-4ec3-8357-f42a21d6e5ba">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="551" y="210" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f6d76dff-9bd6-403f-82a9-0d7f774cba7c</ns16:incoming>
                        
                        
                        <ns16:incoming>a19e4c08-1274-4389-8be8-7f500acad406</ns16:incoming>
                        
                        
                        <ns16:incoming>81338d20-144b-413f-8b4f-ec3cdba68089</ns16:incoming>
                        
                        
                        <ns16:incoming>97bea18e-aa86-4fcd-8e62-abc6c9271b7b</ns16:incoming>
                        
                        
                        <ns16:outgoing>81c0cd2d-a295-49d4-8b73-2f70326e4217</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Get ODC Charges"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="ef14dc79-4df0-4a5c-a505-8f6321355c37" targetRef="e9cec704-3ff3-4ec3-8357-f42a21d6e5ba" name="To Exception Handling" id="f6d76dff-9bd6-403f-82a9-0d7f774cba7c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="96eb1b91-abfc-4312-a131-5885f18ccb8d" targetRef="e9cec704-3ff3-4ec3-8357-f42a21d6e5ba" name="To Exception Handling" id="a19e4c08-1274-4389-8be8-7f500acad406">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="58d184e2-7ef0-4a8c-b05e-b72a93a807ae" targetRef="e9cec704-3ff3-4ec3-8357-f42a21d6e5ba" name="To Exception Handling" id="81338d20-144b-413f-8b4f-ec3cdba68089">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="adbc8ee8-018e-4f88-aa38-bd5b5ea1bbbd" targetRef="e9cec704-3ff3-4ec3-8357-f42a21d6e5ba" name="To Exception Handling" id="97bea18e-aa86-4fcd-8e62-abc6c9271b7b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e9cec704-3ff3-4ec3-8357-f42a21d6e5ba" targetRef="3267fe8b-e5fa-4fe9-8470-34aae4d125bc" name="To End" id="81c0cd2d-a295-49d4-8b73-2f70326e4217">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.81c0cd2d-a295-49d4-8b73-2f70326e4217</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.3267fe8b-e5fa-4fe9-8470-34aae4d125bc</toProcessItemId>
            <guid>d0562c1e-a724-48cc-9afc-39cacbdf2de2</guid>
            <versionId>09279e83-9fe5-48e6-a66d-17be874fdf76</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.e9cec704-3ff3-4ec3-8357-f42a21d6e5ba</fromProcessItemId>
            <toProcessItemId>2025.3267fe8b-e5fa-4fe9-8470-34aae4d125bc</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.534e56aa-adfe-4445-ba8d-2b01813e4a2d</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.aaf39374-2c9d-4d56-bb22-9489a4695b3b</fromProcessItemId>
            <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3749</endStateId>
            <toProcessItemId>2025.3267fe8b-e5fa-4fe9-8470-34aae4d125bc</toProcessItemId>
            <guid>cfee87a5-f4b5-43e6-89c2-ae79afcde42d</guid>
            <versionId>1886e2f6-ac41-4b91-8b0f-09f8b182ad84</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="266" y="345" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.aaf39374-2c9d-4d56-bb22-9489a4695b3b</fromProcessItemId>
            <toProcessItemId>2025.3267fe8b-e5fa-4fe9-8470-34aae4d125bc</toProcessItemId>
        </link>
        <link name="To Counter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5639e31c-5279-4019-9a28-5a39f78e586a</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</fromProcessItemId>
            <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3748</endStateId>
            <toProcessItemId>2025.99f0d666-d0b6-4259-bdc7-6f9668853836</toProcessItemId>
            <guid>80c70cc5-fcbb-41e9-aa10-38d68bcb5f0e</guid>
            <versionId>1cafd8ac-c536-4d01-bdb5-2074313aa9e1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</fromProcessItemId>
            <toProcessItemId>2025.99f0d666-d0b6-4259-bdc7-6f9668853836</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2b2317ab-8789-41f8-9ed1-92746bf854d3</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.73de0973-8c3b-4c94-b615-d18af27aa3e4</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3267fe8b-e5fa-4fe9-8470-34aae4d125bc</toProcessItemId>
            <guid>308aa9d6-7e92-4a3b-8ab0-79bfd4f83321</guid>
            <versionId>*************-41d8-89d2-1629dbc648e9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.73de0973-8c3b-4c94-b615-d18af27aa3e4</fromProcessItemId>
            <toProcessItemId>2025.3267fe8b-e5fa-4fe9-8470-34aae4d125bc</toProcessItemId>
        </link>
        <link name="To Map Input">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.42108b88-dc29-4ff3-9165-3284dd157ee1</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.aaf39374-2c9d-4d56-bb22-9489a4695b3b</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.4de511d4-cddf-4499-a64a-ee68cd415fa8</toProcessItemId>
            <guid>2a9b1eb0-d3ac-47c1-971c-21536afd1aca</guid>
            <versionId>433d43d3-dda2-4e38-8ca7-eeaea29ff61a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.aaf39374-2c9d-4d56-bb22-9489a4695b3b</fromProcessItemId>
            <toProcessItemId>2025.4de511d4-cddf-4499-a64a-ee68cd415fa8</toProcessItemId>
        </link>
        <link name="To Counter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.975d1fef-7866-4f3a-8524-ae8cd31f9332</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4de511d4-cddf-4499-a64a-ee68cd415fa8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.99f0d666-d0b6-4259-bdc7-6f9668853836</toProcessItemId>
            <guid>70406ad7-e5ac-4aff-b237-6ef2e43b9772</guid>
            <versionId>4a734a99-3f80-4406-9288-5b24f48fa894</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4de511d4-cddf-4499-a64a-ee68cd415fa8</fromProcessItemId>
            <toProcessItemId>2025.99f0d666-d0b6-4259-bdc7-6f9668853836</toProcessItemId>
        </link>
        <link name="To Counter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1a361f70-459f-4993-8994-533b9fdf8cef</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1112d449-4e94-42e5-ba23-d69090c99a4d</fromProcessItemId>
            <endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603</endStateId>
            <toProcessItemId>2025.b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</toProcessItemId>
            <guid>4be057b6-f597-4e67-a88a-ff2a1fb2d7e2</guid>
            <versionId>74618dc8-83c6-43c0-a082-9c788459248b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1112d449-4e94-42e5-ba23-d69090c99a4d</fromProcessItemId>
            <toProcessItemId>2025.b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</toProcessItemId>
        </link>
        <link name="To Set Output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d9c763b6-2e99-4c0d-a642-5918eb1a2a4f</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.73de0973-8c3b-4c94-b615-d18af27aa3e4</toProcessItemId>
            <guid>c0e0231b-8c44-45be-8227-6a08bc8d967b</guid>
            <versionId>9fd79cee-1fa0-42fd-94ec-924d5e570f8f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b94b6753-bcd3-4608-8a6c-1fa9ddf4275b</fromProcessItemId>
            <toProcessItemId>2025.73de0973-8c3b-4c94-b615-d18af27aa3e4</toProcessItemId>
        </link>
        <link name="To Calculate Change Amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f12f37ec-ea6a-4e9b-872c-b00f40484269</processLinkId>
            <processId>1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.99f0d666-d0b6-4259-bdc7-6f9668853836</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1112d449-4e94-42e5-ba23-d69090c99a4d</toProcessItemId>
            <guid>67de4d7d-8e2f-4b2b-a941-9239f2d6f525</guid>
            <versionId>a8734000-0e29-4644-bee1-b51f758c80ef</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.99f0d666-d0b6-4259-bdc7-6f9668853836</fromProcessItemId>
            <toProcessItemId>2025.1112d449-4e94-42e5-ba23-d69090c99a4d</toProcessItemId>
        </link>
    </process>
</teamworks>

