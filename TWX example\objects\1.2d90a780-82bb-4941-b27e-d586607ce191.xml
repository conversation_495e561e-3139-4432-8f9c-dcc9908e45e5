<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2d90a780-82bb-4941-b27e-d586607ce191" name="Audit Request History">
        <lastModified>1698774158295</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.4fc9255f-9a95-4e95-9df4-e22c4be89701</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>d10d1d60-86c0-4d74-ae86-b573eb8368a8</guid>
        <versionId>0df5b9dd-9586-48b7-9813-b17c9e15df1d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:ad" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.441e62d7-7978-4893-bfe8-02c6a519c686"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":65,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"54b5227d-488e-4aec-9ee5-1ec7ddfb4c23"},{"incoming":["7200279f-f341-4473-80ba-9ad6a11a9b21","500ea008-6821-4015-a2e0-6576fea62e3d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":608,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8a"],"preAssignmentScript":["\/\/log.info(\"ODC -- ProcessInstance : \"+tw.local.requestNo +\" - ServiceName : AUDIT COLLECTION DATA : END\");"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"6b687c50-a559-426c-8f3a-9a1a8873bf72"},{"targetRef":"4fc9255f-9a95-4e95-9df4-e22c4be89701","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Test Data","declaredType":"sequenceFlow","id":"2027.441e62d7-7978-4893-bfe8-02c6a519c686","sourceRef":"54b5227d-488e-4aec-9ee5-1ec7ddfb4c23"},{"startQuantity":1,"outgoing":["5231134d-c217-44ee-a0b3-100b1707803e"],"incoming":["2027.441e62d7-7978-4893-bfe8-02c6a519c686"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":319,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["\/\/log.info(\"ODC -- ProcessInstance : \"+tw.local.instanceNo +\" - ServiceName : AUDIT COLLECTION DATA : START\");\r\n\r\n"]},"name":"Set SQL Statement","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4fc9255f-9a95-4e95-9df4-e22c4be89701","scriptFormat":"text\/x-javascript","script":{"content":["\/**-------------------------- Add SQL Parameters --------------------------**\/\r\nfunction addSQLParameter(value){\r\n\tif(value == null &amp;&amp; typeof value == \"string\"){\r\n\t\tvalue= \"\"; \r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"number\"){\r\n\t\tvalue= 0;\r\n\t}\r\n\telse if(value == null &amp;&amp; typeof value == \"boolean\"){\r\n\t\tvalue = \"0\";\r\n\t}\r\n var parameter = new tw.object.SQLParameter();  \r\n   parameter.value = value;\r\n   parameter.mode = \"IN\";\r\nreturn parameter;\r\n}\r\ntw.local.sql = \"INSERT INTO \"+ tw.env.DBSchema + \".ODC_LOGHISTORY\"+\r\n               \"(STARTTIME, ENDTIME, USERNAME, ROLE, STEPNAME, ACTION, RETURNREASON, COMMENT, REQUESTNO, terminateReason)\"+\r\n \t\t   \" VALUES (?,?,?,?,?,?,?,?,?,?)\";\r\n\r\n\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\n\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.startTime));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.endTime));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.userName));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.role));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.step));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.action));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.returnReason));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.comment));\r\n \r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.requestNo));\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.terminateReason));  "]}},{"targetRef":"64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Execute SQL Statement","declaredType":"sequenceFlow","id":"5231134d-c217-44ee-a0b3-100b1707803e","sourceRef":"4fc9255f-9a95-4e95-9df4-e22c4be89701"},{"startQuantity":1,"outgoing":["7200279f-f341-4473-80ba-9ad6a11a9b21"],"incoming":["5231134d-c217-44ee-a0b3-100b1707803e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":469,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Execute SQL Statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.sqlParameters"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["1"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"6b687c50-a559-426c-8f3a-9a1a8873bf72","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"7200279f-f341-4473-80ba-9ad6a11a9b21","sourceRef":"64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"sqlParameters","isCollection":true,"declaredType":"dataObject","id":"2056.59f5349e-ed3a-48ac-8d37-59d60eab80c6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.e3fe14a6-6267-450b-bb5e-7d2aea4402b6"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.3aa92ff8-4196-4dc7-9f3d-a88f7df29104"},{"startQuantity":1,"outgoing":["500ea008-6821-4015-a2e0-6576fea62e3d"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":387,"y":156,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exp Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Audit Request History\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"e82b3ec2-f262-4b64-b612-4e7f7425d4fa","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"6b687c50-a559-426c-8f3a-9a1a8873bf72","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event1","declaredType":"sequenceFlow","id":"500ea008-6821-4015-a2e0-6576fea62e3d","sourceRef":"e82b3ec2-f262-4b64-b612-4e7f7425d4fa"}],"laneSet":[{"id":"c3ef90f8-a399-468b-850b-b0df7f0baa5b","lane":[{"flowNodeRef":["54b5227d-488e-4aec-9ee5-1ec7ddfb4c23","6b687c50-a559-426c-8f3a-9a1a8873bf72","4fc9255f-9a95-4e95-9df4-e22c4be89701","64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8","e82b3ec2-f262-4b64-b612-4e7f7425d4fa"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"326c32f7-1dda-48ed-9dc5-6d8e9020e156","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Audit Request History","declaredType":"process","id":"1.2d90a780-82bb-4941-b27e-d586607ce191","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731"}],"inputSet":[{"dataInputRefs":["2055.668205b9-1d85-4129-8851-98e6d8776f9b","2055.923d5229-24ce-44cd-8282-2a0fdcf33910"]}],"outputSet":[{"dataOutputRefs":["2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.StepLog();\nautoObject.startTime =  new TWDate();\nautoObject.endTime = new TWDate() ;\nautoObject.userName = \"odchubcumkr10\";\nautoObject.role = \"Hub Maker\";\nautoObject.step = \"Create ODC Reversal Request \u2013 \u0627\u0646\u0634\u0627\u0621 \u0637\u0644\u0628 \u0627\u0639\u0627\u062f\u0629 \u0642\u064a\u062f \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631\";\nautoObject.action = \"Submit Request\";\nautoObject.comment = \"No zft\";\nautoObject.terminateReason = \"\";\nautoObject.returnReason = \"L2a\";\nautoObject"}]},"itemSubjectRef":"itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49","name":"stepLog","isCollection":false,"id":"2055.668205b9-1d85-4129-8851-98e6d8776f9b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"12345678911111_02\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNo","isCollection":false,"id":"2055.923d5229-24ce-44cd-8282-2a0fdcf33910"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="stepLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.668205b9-1d85-4129-8851-98e6d8776f9b</processParameterId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.e4654440-58a7-47b2-8f98-3eaa9cccad49</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.StepLog();
autoObject.startTime =  new TWDate();
autoObject.endTime = new TWDate() ;
autoObject.userName = "odchubcumkr10";
autoObject.role = "Hub Maker";
autoObject.step = "Create ODC Reversal Request – انشاء طلب اعادة قيد تحصيل مستندى تصدير";
autoObject.action = "Submit Request";
autoObject.comment = "No zft";
autoObject.terminateReason = "";
autoObject.returnReason = "L2a";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>54857262-a6d9-4963-8de2-8370383497b8</guid>
            <versionId>05e13bd5-4418-4cdf-b71f-224ad6013f4b</versionId>
        </processParameter>
        <processParameter name="requestNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.923d5229-24ce-44cd-8282-2a0fdcf33910</processParameterId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"12345678911111_02"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>61b725ea-6dd3-4b2b-9758-2943223e5409</guid>
            <versionId>f074e3c9-b595-4485-a95a-29a29d0b795f</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731</processParameterId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a7f712e2-6864-4588-be8c-af717d8db59f</guid>
            <versionId>10d62af0-490d-4fde-9d87-3ef90c48fc41</versionId>
        </processParameter>
        <processVariable name="sqlParameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.59f5349e-ed3a-48ac-8d37-59d60eab80c6</processVariableId>
            <description isNull="true" />
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3e40e6c5-b7df-4a7b-9a9e-9b290566cc06</guid>
            <versionId>0d90a410-999a-4197-8ed1-fb2b52b7b584</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e3fe14a6-6267-450b-bb5e-7d2aea4402b6</processVariableId>
            <description isNull="true" />
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fb592912-a35a-4cdc-906e-328bc6abc825</guid>
            <versionId>a11ff38f-a967-459b-a920-e24e2c248f66</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3aa92ff8-4196-4dc7-9f3d-a88f7df29104</processVariableId>
            <description isNull="true" />
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3300badb-5239-4e55-9283-209078572aed</guid>
            <versionId>2c737604-488f-4d50-a9e5-cf15acf49d43</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4fc9255f-9a95-4e95-9df4-e22c4be89701</processItemId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <name>Set SQL Statement</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c7a1570e-2f65-407f-9ff1-0158b148e46f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d88</guid>
            <versionId>1c9e97a2-af90-4c58-a3f8-8ce38fe6e6b9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.9d1e12ea-673c-4aed-9c12-2ffd30069505</processItemPrePostId>
                <processItemId>2025.4fc9255f-9a95-4e95-9df4-e22c4be89701</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>a70c3c20-8570-408e-a27e-7546a0908213</guid>
                <versionId>8a163c73-f6ba-4171-84c1-96011ff5ba8c</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.e4d4694b-f48c-41a6-84ad-a977125cc3b4</processItemPrePostId>
                <processItemId>2025.4fc9255f-9a95-4e95-9df4-e22c4be89701</processItemId>
                <location>1</location>
                <script>//log.info("ODC -- ProcessInstance : "+tw.local.instanceNo +" - ServiceName : AUDIT COLLECTION DATA : START");&#xD;
&#xD;
</script>
                <guid>d621998d-e257-4dc8-bdad-5e8af5366e12</guid>
                <versionId>8d9e9f12-7234-4606-8b8c-8fdf5d60c49a</versionId>
            </processPrePosts>
            <layoutData x="319" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c7a1570e-2f65-407f-9ff1-0158b148e46f</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql = "INSERT INTO "+ tw.env.DBSchema + ".ODC_LOGHISTORY"+&#xD;
               "(STARTTIME, ENDTIME, USERNAME, ROLE, STEPNAME, ACTION, RETURNREASON, COMMENT, REQUESTNO, terminateReason)"+&#xD;
 		   " VALUES (?,?,?,?,?,?,?,?,?,?)";&#xD;
&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.startTime));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.endTime));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.userName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.role));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.step));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.action));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.returnReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.comment));&#xD;
 &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.requestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.terminateReason));  </script>
                <isRule>false</isRule>
                <guid>1f4a8fe7-9607-4267-bd0f-e0b829c50c28</guid>
                <versionId>44cd830f-ca9e-4b8d-8fe4-1c378217f5de</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8</processItemId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <name>Execute SQL Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8e</guid>
            <versionId>445ef061-184f-4a95-8578-6cdc791f89d6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="469" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>57218fd8-e45d-42cd-9911-aa99e0247474</guid>
                <versionId>99b78c18-334b-4296-8ff8-5331bdb5d837</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.10e04d33-941e-4e7e-8fb9-c36fc54f9f5c</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>36869c17-2a23-407c-a414-6767ffd0f9fd</guid>
                    <versionId>1863444b-1280-46d5-a7a1-931434980f72</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4693d20b-2838-45bb-a954-6ab2dc112315</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlParameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>8ad883f0-9416-48fb-989b-acaa5027d5f1</guid>
                    <versionId>bec5f428-dab3-4a17-b3c0-b565a12d3cdd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.afb67f0c-42bb-4943-9f16-47504bc77ba3</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>26d1dd4b-b4b4-4a10-be7a-11f6b93ceea3</guid>
                    <versionId>c876789c-5f63-43be-8942-60b3ad087df8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6a50cec6-4935-4f45-bcb5-352d97c3c664</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>31410a72-2d53-4318-bf11-c115034d2c1b</guid>
                    <versionId>df86fad0-00ac-45f0-89e7-bd34e1f504c9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f9bb8136-31a9-4fef-b017-be63e9a187fd</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>de3ad393-d310-4ea1-9928-dedbe0a9eb93</guid>
                    <versionId>ee8417d9-bf53-4241-90a2-37ebf3934b78</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e82b3ec2-f262-4b64-b612-4e7f7425d4fa</processItemId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <name>Exp Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8c</guid>
            <versionId>52300ee0-e587-4196-8b75-5a4e80d0e7f2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="387" y="156">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>5d3294f3-17cc-47e5-9249-047b85fe0716</guid>
                <versionId>cc16b924-03a6-4557-a167-1d1b92b3b09d</versionId>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.430e80a1-6eeb-4a51-b28d-45d68f3a3fd4</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Audit Request History"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>61e55516-6cc8-49f4-b541-4194f6d55b2c</guid>
                    <versionId>3959613a-ff0e-44c9-92df-b9ae88f38797</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.76774df4-e358-4d89-8efa-769c33f2b72c</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>444892c4-0c7e-4ca1-b378-b82417655a45</guid>
                    <versionId>3bd21585-877b-49c0-ab56-44bd2891123f</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6b687c50-a559-426c-8f3a-9a1a8873bf72</processItemId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.478269d6-0417-4925-850e-2dbc69a075c0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8a</guid>
            <versionId>6d3022e2-51f4-4027-93c5-dae6baf52e24</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.3c5c45d7-d194-477b-ad08-f783f672c4fc</processItemPrePostId>
                <processItemId>2025.6b687c50-a559-426c-8f3a-9a1a8873bf72</processItemId>
                <location>1</location>
                <script>//log.info("ODC -- ProcessInstance : "+tw.local.requestNo +" - ServiceName : AUDIT COLLECTION DATA : END");</script>
                <guid>b806e75d-1367-46d6-b74c-157cba08349a</guid>
                <versionId>17bf992d-8bb7-41e6-ba93-02c96e1d8ec4</versionId>
            </processPrePosts>
            <layoutData x="608" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.478269d6-0417-4925-850e-2dbc69a075c0</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>36decfe2-46c9-4c91-a7ba-3576d5d7ad5b</guid>
                <versionId>2e1e4c1d-9e8b-4cc8-b717-63ce7c8c2e5d</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.4fc9255f-9a95-4e95-9df4-e22c4be89701</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="65" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Audit Request History" id="1.2d90a780-82bb-4941-b27e-d586607ce191" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="stepLog" itemSubjectRef="itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49" isCollection="false" id="2055.668205b9-1d85-4129-8851-98e6d8776f9b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.StepLog();
autoObject.startTime =  new TWDate();
autoObject.endTime = new TWDate() ;
autoObject.userName = "odchubcumkr10";
autoObject.role = "Hub Maker";
autoObject.step = "Create ODC Reversal Request – انشاء طلب اعادة قيد تحصيل مستندى تصدير";
autoObject.action = "Submit Request";
autoObject.comment = "No zft";
autoObject.terminateReason = "";
autoObject.returnReason = "L2a";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="requestNo" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.923d5229-24ce-44cd-8282-2a0fdcf33910">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"12345678911111_02"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.668205b9-1d85-4129-8851-98e6d8776f9b</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.923d5229-24ce-44cd-8282-2a0fdcf33910</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c3ef90f8-a399-468b-850b-b0df7f0baa5b">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="326c32f7-1dda-48ed-9dc5-6d8e9020e156" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>54b5227d-488e-4aec-9ee5-1ec7ddfb4c23</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6b687c50-a559-426c-8f3a-9a1a8873bf72</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4fc9255f-9a95-4e95-9df4-e22c4be89701</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e82b3ec2-f262-4b64-b612-4e7f7425d4fa</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="54b5227d-488e-4aec-9ee5-1ec7ddfb4c23">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="65" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.441e62d7-7978-4893-bfe8-02c6a519c686</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="6b687c50-a559-426c-8f3a-9a1a8873bf72">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="608" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8a</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript>//log.info("ODC -- ProcessInstance : "+tw.local.requestNo +" - ServiceName : AUDIT COLLECTION DATA : END");</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7200279f-f341-4473-80ba-9ad6a11a9b21</ns16:incoming>
                        
                        
                        <ns16:incoming>500ea008-6821-4015-a2e0-6576fea62e3d</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="54b5227d-488e-4aec-9ee5-1ec7ddfb4c23" targetRef="4fc9255f-9a95-4e95-9df4-e22c4be89701" name="To Test Data" id="2027.441e62d7-7978-4893-bfe8-02c6a519c686">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set SQL Statement" id="4fc9255f-9a95-4e95-9df4-e22c4be89701">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="319" y="57" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                            
                            <ns3:preAssignmentScript>//log.info("ODC -- ProcessInstance : "+tw.local.instanceNo +" - ServiceName : AUDIT COLLECTION DATA : START");&#xD;
&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.441e62d7-7978-4893-bfe8-02c6a519c686</ns16:incoming>
                        
                        
                        <ns16:outgoing>5231134d-c217-44ee-a0b3-100b1707803e</ns16:outgoing>
                        
                        
                        <ns16:script>/**-------------------------- Add SQL Parameters --------------------------**/&#xD;
function addSQLParameter(value){&#xD;
	if(value == null &amp;&amp; typeof value == "string"){&#xD;
		value= ""; &#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "number"){&#xD;
		value= 0;&#xD;
	}&#xD;
	else if(value == null &amp;&amp; typeof value == "boolean"){&#xD;
		value = "0";&#xD;
	}&#xD;
 var parameter = new tw.object.SQLParameter();  &#xD;
   parameter.value = value;&#xD;
   parameter.mode = "IN";&#xD;
return parameter;&#xD;
}&#xD;
tw.local.sql = "INSERT INTO "+ tw.env.DBSchema + ".ODC_LOGHISTORY"+&#xD;
               "(STARTTIME, ENDTIME, USERNAME, ROLE, STEPNAME, ACTION, RETURNREASON, COMMENT, REQUESTNO, terminateReason)"+&#xD;
 		   " VALUES (?,?,?,?,?,?,?,?,?,?)";&#xD;
&#xD;
&#xD;
tw.local.sqlParameters = new tw.object.listOf.SQLParameter();&#xD;
&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.startTime));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.endTime));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.userName));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.role));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.step));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.action));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.returnReason));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.comment));&#xD;
 &#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.requestNo));&#xD;
tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.terminateReason));  </ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4fc9255f-9a95-4e95-9df4-e22c4be89701" targetRef="64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8" name="To Execute SQL Statement" id="5231134d-c217-44ee-a0b3-100b1707803e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" name="Execute SQL Statement" id="64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="469" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5231134d-c217-44ee-a0b3-100b1707803e</ns16:incoming>
                        
                        
                        <ns16:outgoing>7200279f-f341-4473-80ba-9ad6a11a9b21</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.sqlParameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8" targetRef="6b687c50-a559-426c-8f3a-9a1a8873bf72" name="To End" id="7200279f-f341-4473-80ba-9ad6a11a9b21">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="sqlParameters" id="2056.59f5349e-ed3a-48ac-8d37-59d60eab80c6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.e3fe14a6-6267-450b-bb5e-7d2aea4402b6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.3aa92ff8-4196-4dc7-9f3d-a88f7df29104" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exp Handling" id="e82b3ec2-f262-4b64-b612-4e7f7425d4fa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="387" y="156" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>500ea008-6821-4015-a2e0-6576fea62e3d</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Audit Request History"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="e82b3ec2-f262-4b64-b612-4e7f7425d4fa" targetRef="6b687c50-a559-426c-8f3a-9a1a8873bf72" name="To End Event1" id="500ea008-6821-4015-a2e0-6576fea62e3d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.500ea008-6821-4015-a2e0-6576fea62e3d</processLinkId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e82b3ec2-f262-4b64-b612-4e7f7425d4fa</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.6b687c50-a559-426c-8f3a-9a1a8873bf72</toProcessItemId>
            <guid>*************-473b-9311-f5dcd85ee190</guid>
            <versionId>04cce99f-0667-4bc2-ba9f-66c074e87e60</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.e82b3ec2-f262-4b64-b612-4e7f7425d4fa</fromProcessItemId>
            <toProcessItemId>2025.6b687c50-a559-426c-8f3a-9a1a8873bf72</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7200279f-f341-4473-80ba-9ad6a11a9b21</processLinkId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.6b687c50-a559-426c-8f3a-9a1a8873bf72</toProcessItemId>
            <guid>e5651fa1-2e1a-4ec1-8dfa-cc3e3ba404d1</guid>
            <versionId>12f3e6a8-08a5-41b3-88a1-7c31c5a2e6b1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8</fromProcessItemId>
            <toProcessItemId>2025.6b687c50-a559-426c-8f3a-9a1a8873bf72</toProcessItemId>
        </link>
        <link name="To Execute SQL Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5231134d-c217-44ee-a0b3-100b1707803e</processLinkId>
            <processId>1.2d90a780-82bb-4941-b27e-d586607ce191</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4fc9255f-9a95-4e95-9df4-e22c4be89701</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8</toProcessItemId>
            <guid>e8d9f216-aca8-40de-9d84-04a0f2c9ff6c</guid>
            <versionId>4ecdfb71-ad2e-460b-8404-9f9a8fea9ed6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4fc9255f-9a95-4e95-9df4-e22c4be89701</fromProcessItemId>
            <toProcessItemId>2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8</toProcessItemId>
        </link>
    </process>
</teamworks>

