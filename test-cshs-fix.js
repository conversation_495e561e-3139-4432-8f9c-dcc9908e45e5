const TWXExtractor = require('./src/parser/twx-extractor')

// Test the processType extraction fix
function testProcessTypeExtraction() {
  const extractor = new TWXExtractor()
  
  // Mock process element with processType = 10 (CSHS)
  const mockProcessElement = {
    processType: ['10']  // XML elements are parsed as arrays by xml2js
  }
  
  // Mock base object
  const baseObject = {
    id: '1.test-cshs',
    name: 'Test CSHS',
    type: 'process',
    details: {}
  }
  
  console.log('Testing processType extraction...')
  console.log('Before extraction:', {
    subType: baseObject.subType,
    processType: baseObject.details.processType
  })
  
  // Call the extraction method
  extractor.extractProcessDetails(mockProcessElement, baseObject)
  
  console.log('After extraction:', {
    subType: baseObject.subType,
    processType: baseObject.details.processType,
    hasDetails: baseObject.hasDetails
  })
  
  // Verify the fix works
  if (baseObject.subType === '10' && baseObject.details.processType === '10' && baseObject.hasDetails === true) {
    console.log('✅ CSHS detection works correctly!')
    console.log('✅ processType = 10 correctly identified as CSHS')
    console.log('✅ hasDetails = true for CSHS objects')
  } else {
    console.log('❌ CSHS detection failed!')
    console.log('Expected: subType="10", processType="10", hasDetails=true')
    console.log(`Got: subType="${baseObject.subType}", processType="${baseObject.details.processType}", hasDetails=${baseObject.hasDetails}`)
  }
  
  // Test with regular process (processType = 0)
  console.log('\nTesting regular process (processType = 0)...')
  const mockRegularProcess = {
    processType: ['0']
  }
  
  const regularObject = {
    id: '1.test-regular',
    name: 'Test Regular Process',
    type: 'process',
    details: {}
  }
  
  extractor.extractProcessDetails(mockRegularProcess, regularObject)
  
  console.log('Regular process result:', {
    subType: regularObject.subType,
    processType: regularObject.details.processType,
    hasDetails: regularObject.hasDetails
  })
  
  if (regularObject.subType === '0' && regularObject.details.processType === '0' && regularObject.hasDetails === false) {
    console.log('✅ Regular process detection works correctly!')
  } else {
    console.log('❌ Regular process detection failed!')
  }
}

// Run the test
testProcessTypeExtraction()
