{"id": "1.d5923909-74e7-467b-afb5-eef943fb9698", "versionId": "b6e7910f-ef9c-4691-89d3-f6b311230cba", "name": "Col01 - test", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "routingDetails", "hasDefault": false, "type": "1"}, {"name": "parentPath", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}, {"name": "customerAccounts", "hasDefault": false, "type": "2"}, {"name": "parentPath", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "errorMessageVis", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "isSuccessful", "hasDefault": false}, {"name": "ldapUserProfile", "hasDefault": false}, {"name": "parentRequestNoVIS", "hasDefault": false}, {"name": "error", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "requestTypeVIS", "hasDefault": false}, {"name": "glAccountVerified", "hasDefault": false}, {"name": "accounteePartyCif", "hasDefault": false}, {"name": "code", "hasDefault": false}, {"name": "fullPath", "hasDefault": false}, {"name": "serviceName", "hasDefault": false}, {"name": "isfound", "hasDefault": false}, {"name": "message", "hasDefault": false}, {"name": "ValidationMessage", "hasDefault": false}, {"name": "customerAndPartyCifs", "hasDefault": false}, {"name": "rate", "hasDefault": false}, {"name": "calculatedChangeAmnt", "hasDefault": false}, {"name": "selectedIndex", "hasDefault": false}, {"name": "exchangeRate", "hasDefault": false}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "hasDefault": false}, {"name": "contractLiquidatedMSG", "hasDefault": false}, {"name": "verifyGLMsg", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Coach", "id": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Set Status And Sub Status", "id": "2025.af01a78b-ba4b-44df-bee8-875288c1ccd2", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Update History", "id": "2025.62723cde-1c43-48bf-b898-cde90ae6d585", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Create ECM Folder", "id": "2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Set ECM default properties", "id": "2025.1e3d2772-6793-4cb1-a62e-54de905566f2", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Customer Account Details", "id": "2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Retrieve Request Number", "id": "2025.32f042c7-4da3-463c-ac2f-8a2d79da9463", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Copy of Set ECM default properties", "id": "2025.bcd34832-e514-4a6c-9c1e-0af7bae14696", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit Collection Data", "id": "2025.ad93dc50-07e4-45ca-a21a-817f396172da", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Charges Completed", "id": "2025.0c491680-083e-4ac5-aa6a-63384b069d01", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Retrieve Product Code", "id": "2025.c1787180-2088-406b-8072-bc0aba3b30ae", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Debited Nostro Vostro Account", "id": "2025.462404fa-2cce-4c58-b7e8-3240a943549d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get Customer and Party Account List", "id": "2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Valid?", "id": "2025.ef877a71-0e37-49d3-9127-63502f4d4069", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Is Liquidated?", "id": "2025.ce2cdb08-0319-4ac0-a07d-6966abcad237", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "is Liquidated?", "id": "2025.c180fd4c-112c-4218-8691-6f7d496b7c1f", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "log history?", "id": "2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "audited successfully?", "id": "2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Has ProductCode?", "id": "2025.31168a88-607c-4da6-ab4a-963438a9f04a", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "Init", "id": "2025.1d579c44-4b9b-4332-b150-91de7a551a5c", "script": "tw.local.isChecker = false;\r\r\n//---------------------- Init appInfo  \"This object is used in Header CV\"---------------------------------\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\t\r\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\ntw.local.odcRequest.appInfo.requestName = \"ODC collection\";\r\r\ntw.local.odcRequest.appInfo.requestType = tw.local.odcRequest.requestType.name;\r\r\ntw.local.odcRequest.appInfo.status =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.subStatus =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo; //tw.system.processInstance.id;\r\r\n\r\r\n//---------------------------------Init Step log ---------------------------------------------------\t \t \r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step = tw.epv.Col_ScreenNames.ODCCol01; \r\r\n\r\r\n //----------------------------------------------------------------------------------------------\r\r\n//Init actionConditions \"This object used by service (Get Actions by ScreenName 2) \"\r\r\ntw.local.actionConditions={};\r\r\ntw.local.actionConditions.complianceApproval= false;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.odcRequest.stepLog.action;\r\r\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\t \r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\t\r\r\n\r\r\n//Init liquidation data\r\r\ntw.local.odcRequest.ContractLiquidation.liqCurrency= tw.local.odcRequest.FinancialDetailsBR.currency.value;\r\r\n//init request state \r\r\ntw.local.odcRequest.BasicDetails.requestState=\"undergoing collection\";\r\r\n\r\r\nif(tw.local.odcRequest.isLiquidated == false)\t{\r\r\n\ttw.local.odcRequest.ContractLiquidation= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAmount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount.currency={};\r\r\n\t}\r\r\n\t\r\r\ntw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\r\r\ntw.local.odcRequest.BasicDetails.requestType = \ttw.local.odcRequest.requestType.name;\r\r\n\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass={};\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.name= \"Customer Account\";\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value= \"Customer Account\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Validation", "id": "2025.fb195954-bc69-4a3d-9008-49e5f46d3201", "script": "//\t debugger;\r\r\ntw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel){\r\r\n    mandatory(tw.local.odcRequest.stepLog.comment, \"tw.local.odcRequest.stepLog.comment\");\r\r\n}\r\r\n\r\r\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.submitLiq || tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.createLiq) {\r\r\n\r\r\n    validateLiq();\r\r\n}\r\r\n\r\r\n//------------------------------------------------- Contract Liquidation VALIDATION -----------------------------------------\r\r\nfunction validateLiq() {\r\r\n    //debugger;\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.liqAmount, \"tw.local.odcRequest.ContractLiquidation.liqAmount\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditValueDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitValueDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\");\r\r\n    \r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.creditValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\", \"Invalid date\", \"Credit Value Date should not be less than request date\");\r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.debitValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\", \"Invalid date\", \"Debit Value Date should not be less than request date\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitedAccountNo, \"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\");\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value == \"GL Account\") {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo\");\r\r\n\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified == false) {\r\r\n            tw.local.errorMessage += \"<li>\" + \"GL Account is not verified.\" + \"</li>\";\r\r\n            tw.system.coachValidation.addValidationError(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"GL Account is not verified.\");\r\r\n        }\r\r\n\r\r\n        //debugger;\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified) {\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode\");\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.currency\");\r\r\n        }\r\r\n\r\r\n        //Validate OverDraft\r\r\n        if (tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount > tw.local.odcRequest.ContractLiquidation.creditedAccount.balance && tw.local.odcRequest.ContractLiquidation.creditedAccount.isOverDraft) {\r\r\n            addError(\"tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\")\r\r\n        }\r\r\n    \r\r\n    } else {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo\");\r\r\n    }\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.liqAmount > tw.local.odcRequest.FinancialDetailsFO.outstandingAmount) {\r\r\n        addError(\"tw.local.odcRequest.ContractLiquidation.liqAmount\", \"Liquidation Amount must be <= ODC Outstanding Amount\");\r\r\n        tw.local.errorMessage += \"<li>\" + \"Liquidation Amount must be less than or equal to ODC Outstanding Amount.\" + \"</li>\";\r\r\n    }\r\r\n\r\r\n    //----------------------------------------------- Charges and Commissions VALIDATION -------------------------------------\t\t\r\r\n    for (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\r\n\r\r\n        //Description - Flat Amount\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            //        mandatory(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\");\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 2, \"Shouldn't be less than 2 character\", \"Change Amount: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 14, \"Shouldn't be more than 14 character\", \"Change Amount:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n        //Fixed Rate\r\r\n        } else {\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 2, \"Shouldn't be less than 2 character\", \"Change Percentage: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 14, \"Shouldn't be more than 14 character\", \"Change Percentage:\" + \"Shouldn't be more than 14 character\");\r\r\n        }\r\r\n\r\r\n        //skip validation if waiver or changeAmnt < 0\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].waiver == false && tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n\r\r\n            //Validate debited accounts\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.accountClass\");\r\r\n\r\r\n            //GL Account\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value == \"GL Account\") {\r\r\n\r\r\n                if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\r\n                    addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\", \"GL Account Not Verified\");\r\r\n                }\r\r\n\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.currency.value\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.branchCode\");\r\r\n\r\r\n                //Customer Account\r\r\n            } else {\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            }\r\r\n\r\r\n            //DebitedAmount\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\r\r\n                validateDecimal(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", \"Must be Decimal(16,10)\")\r\r\n            }\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 2, \"Shouldn't be less than 2 character\", \"Negotiated Exchange Rate: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 14, \"Shouldn't be more than 14 character\", \"Negotiated Exchange Rate:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n            //Correct Validation but Waiting confirmation on what to do if GL account\r\r\n            if ((tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\r\r\n                tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance) && tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\");\r\r\n            }\r\r\n        }\r\r\n    }\r\r\n}   //end of validation function\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\r\n    tw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n    fromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the date1 is less than date2\r\r\n*\t\r\r\n* EX:checkDate( date, requestDate ,  'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction checkDate(date, requestDate, dateName, controlMessage, validationMessage) {\r\r\n    if (date != null && date != undefined && date < requestDate) {\r\r\n        addError(dateName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field != null && field != undefined && field.length < len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field.length > len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field, fieldName) {\r\r\n    if (field == null || field == undefined) {\r\r\n        addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n        mandatoryTriggered = true;\r\r\n        return false;\r\r\n    }\r\r\n    else {\r\r\n        switch (typeof field) {\r\r\n            case \"string\":\r\r\n                if (field.trim() != undefined && field.trim() != null && field.trim().length == 0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            case \"number\":\r\r\n                if (field == 0.0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                if (field < 0) {\r\r\n                    var msg = \"Invalid Value, This field can not be negative value.\";\r\r\n                    addError(fieldName, msg, msg, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n\r\r\n            case \"boolean\":\r\r\n                if (field == false) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            default:\r\r\n\r\r\n                // VALIDATE DATE OBJECT\r\r\n                if (field && field.getTime && isFinite(field.getTime())) { }\r\r\n\r\r\n                else {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n        }\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n\r\r\n//Validate Decimal(10,6)\r\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,4}(\\.\\d{1,6})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\n\r\r\n//Validate Decimal(14,2)\r\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\r\n    validationMessage = controlMessage;\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,12}(\\.\\d{1,2})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\ntw.local.errorMessage != null ? tw.local.errorMessageVis = \"EDITABLE\" : tw.local.errorMessageVis = \"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Set Liq Flag", "id": "2025.d199b9f9-4dde-42e0-906e-f6b7577a099b", "script": "tw.local.odcRequest.isLiquidated= true;\r\r\ntw.local.odcRequest.stepLog.action=\"\";\r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\r\r\n\r\r\ntw.local.isChecker = true;\r\r\n\r\r\n//location.reload(true);\r\r\n//window.location.reload();", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Set Attachments list", "id": "2025.b845eb69-1e4d-4b45-bf66-6bc282fc928e", "script": "if(!tw.local.odcRequest.attachmentDetails)\r\r\n\ttw.local.odcRequest.attachmentDetails= {};\r\r\n\t\r\r\nif (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {\r\r\n\ttw.local.odcRequest.attachmentDetails.attachment = []; \r\r\n\t\r\r\n}\t\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"طلب العميل\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].name = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].description = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].name = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].description = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].arabicName = \"فاتورة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].name = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].description = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].name = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].description = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].name = \"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].description =\"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].name = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].description = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].name = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].description = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].name = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].description = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].name = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].description = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].description = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].name = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].description = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].name = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].description = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].name = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].description = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].name = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].description = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].description = \"Bill of exchange/draft\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].arabicName = \"الكمبيالة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].name = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].description = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].name = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].description = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].arabicName = \"\";;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].name = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].description = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].arabicName = \"\";\r\r\n\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].name = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].description = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = {};\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Col01 - test", "lastModified": "1700642993424", "lastModifiedBy": "mohamed.reda", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.7b84ebd2-b426-417d-922e-fd3f759ce5a8", "2025.7b84ebd2-b426-417d-922e-fd3f759ce5a8"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "true", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "eee7d95d-1b8d-47a9-8251-4d98e4e74df3", "versionId": "b6e7910f-ef9c-4691-89d3-f6b311230cba", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"mobileReady\":[true],\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6\"],\"isInterrupting\":true,\"extensionElements\":{\"default\":[\"2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6\"],\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":-71,\"y\":199,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"a3015b6c-0669-49d2-bc30-5aafe210ef21\"},{\"outgoing\":[\"2027.c783ffa8-30b8-43a2-b551-bc7251648820\",\"2027.2b247745-5401-419b-a4b0-32493f674d7a\",\"2027.64d54f87-102e-4a1c-be15-8eea624e779a\"],\"incoming\":[\"2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6\",\"2027.43b953d0-b231-40c0-9270-0d66179a25f2\",\"2027.49d50c9f-44fc-483c-9b41-ee6096df92a4\",\"2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba\",\"2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7\",\"2027.145b023e-4308-4e49-8ae5-2150277f3c35\",\"2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c\",\"2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":760,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"],\"preAssignmentScript\":[\"tw.local.odcRequest.ContractLiquidation.liqCurrency = \\\"USD\\\"\\r\\ntw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 20000;\\r\\n\\r\\n\\/\\/if(tw.local.odcRequest.ContractLiquidation.liqCurrency== null || tw.local.odcRequest.ContractLiquidation.liqCurrency == \\\"\\\") \\r\\n\\/\\/\\ttw.local.odcRequest.ContractLiquidation.liqCurrency= \\\"USD\\\";\\r\\n\\/\\/\\t\\r\\n\\t\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Contract_Liquidation1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bc906cd5-1b43-49fb-899d-cabca5acfa8e\",\"optionName\":\"@label\",\"value\":\"Contract Liquidation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f43e7550-20ab-40e2-8b6f-555fbc7a7fcf\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97b8c6a1-4a4c-46c3-8636-71178065fd38\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f07b0a4c-42ad-4665-8211-9f61d8014f31\",\"optionName\":\"cif\",\"value\":\"tw.local.odcRequest.Parties.partyTypes.partyCIF\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6738b37a-96d9-4e53-8486-7c8a0308ee42\",\"optionName\":\"isGlAccount\",\"value\":\"tw.local.isGlAccount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5b0023d9-26ad-40fd-84fe-0092dec7a799\",\"optionName\":\"customerAccounts\",\"value\":\"tw.local.odcRequest.customerAndPartyAccountList[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"44e61d5d-d04f-4494-8276-a96d7c26131c\",\"optionName\":\"glAccountVerified\",\"value\":\"tw.local.glAccountVerified\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8e3ffba0-7e90-4319-89d2-2cc47766b791\",\"optionName\":\"isChecker\",\"value\":\"tw.local.isChecker\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b31c7765-e06c-4542-8305-44c03f4abbd8\",\"optionName\":\"exchangeRate\",\"value\":\"tw.local.exchangeRate\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cab4af01-c500-43cc-8197-62dc3f78f537\",\"optionName\":\"contractLiquidatedMSG\",\"value\":\"tw.local.contractLiquidatedMSG\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bb485631-6580-42c2-883c-204d4d28a9a1\",\"optionName\":\"verifyGLMsg\",\"value\":\"tw.local.verifyGLMsg\"}],\"viewUUID\":\"64.14ee925a-157a-48e5-9ab8-7b8879adbe5b\",\"binding\":\"tw.local.odcRequest\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"3a917dbe-ea3c-41a8-8c4e-feb95a130e78\",\"version\":\"8550\"},{\"layoutItemId\":\"Charges_And_Commissions_CV_21\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"79f54cfb-3f33-4ef7-858f-a38674262f9d\",\"optionName\":\"@label\",\"value\":\"Charges And Commissions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"911958c1-a887-449b-8755-389d4e7d2123\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e4eea5bb-2cfa-4d0c-8718-12fe36d5295a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"591022a9-e70d-42e0-81cb-acbfc61aa581\",\"optionName\":\"processType\",\"value\":\"LIQD\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4dacacd4-f2d2-40f2-8d6f-bc8448c0ca8e\",\"optionName\":\"amountCollectableByNBE\",\"value\":\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6fad35b0-6227-4c7c-8f07-630557071f74\",\"optionName\":\"exRate\",\"value\":\"tw.local.rate\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2a3fa55d-c346-4a32-8abd-3c27dd5c51c9\",\"optionName\":\"chargesCustomerAccountList\",\"value\":\"tw.local.odcRequest.customerAndPartyAccountList[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8213ce95-714a-4614-8205-b04208bb4157\",\"optionName\":\"calculatedChangeAmnt\",\"value\":\"tw.local.calculatedChangeAmnt\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c0e9dd94-85a4-426d-88f6-96dbb4bbe633\",\"optionName\":\"index\",\"value\":\"tw.local.selectedIndex\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fa07b132-2d3f-4b7d-898e-6624c941db4d\",\"optionName\":\"isChecker\",\"value\":\"tw.local.isChecker\"}],\"viewUUID\":\"64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52\",\"binding\":\"tw.local.odcRequest.ChargesAndCommissions[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"8a678e26-**************-83943d6c7afa\",\"version\":\"8550\"},{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9b21a244-7684-40a4-87df-e49428d013f3\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7acd67eb-9a60-4b90-88cb-543066e77808\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0293df39-1587-46e3-83d2-4a0918846c3d\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2907c973-dfb0-4c89-8c91-cfb4abf56cb1\",\"optionName\":\"basicDetailsVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f1fd81f3-ec3d-4c01-80ce-3d1a31c5409a\",\"optionName\":\"requestVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9b13236c-9c9a-46f3-8066-7b2f8bb9c70e\",\"optionName\":\"requestTypeVIS\",\"value\":\"READONLY\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fcf313ce-e0ff-452f-87bf-1df7e6fc97c3\",\"optionName\":\"parentRequestNoVis\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"806c40a1-9a12-4b34-8078-7b34d3ab2993\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cc14c2c1-a593-4f82-8b94-7d24629d0279\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"NONE\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cc5fb8aa-9f2a-4e63-859f-210dba97a3e0\",\"optionName\":\"contractStageVIS\",\"value\":\"NONE\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"532735ae-dee4-4bf2-86e4-a116bfe2eecf\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"36b1599e-83de-4212-89c2-db792affb341\",\"optionName\":\"@label\",\"value\":\"Customer Info\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f0b9d1a7-96ab-4c99-868c-2861681fbb50\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"05b59a76-a5a1-4190-8109-0cc2442c3241\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d5bd2cf7-cf68-4e98-8bd6-c9a2e72d69c7\",\"optionName\":\"customerInfoVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2f279f23-07e4-46f0-8f52-cf2f9dc1e04a\",\"optionName\":\"requestTypeVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"45f8ea33-f85b-47dd-8047-babb2c82c869\",\"optionName\":\"listsVIS\",\"value\":\"READONLY\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"8f5f2747-73fd-4c2b-85b0-2f72bc4b2914\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"62a2d995-e92f-4c2d-85c8-6ddc773f9bf7\",\"optionName\":\"@label\",\"value\":\"Financial Details - Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"50285e85-d620-4be2-88e6-3a7d6178e41f\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"05584db9-79dc-423b-875e-e097ca606909\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"aedd8df5-a82e-4e3d-8534-7207a23afe1e\",\"optionName\":\"financialDetailsVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"42614bca-fec8-463d-886d-69c9a2a21408\",\"optionName\":\"fcCollectionVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d2a5acc4-d48e-4922-8ce3-5ef577d5bfbf\",\"optionName\":\"documentAmountVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"236b2450-aa3a-47fd-8e76-1eeea7fa5de7\",\"optionName\":\"currencyVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2c7d4a6b-3469-49b4-8694-9bbd44d5bf6c\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d3b50ee4-6fe5-4d99-8cea-cc90a0ffa85f\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"98c22cf5-d006-4ce1-8578-38fb9d4a10f0\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"078ff71d-c1db-42e2-8b6f-503a29653467\",\"optionName\":\"@label\",\"value\":\"Attachments\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cb030272-bcda-46bd-8dc0-747419cf6bd1\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ca345b3c-21b8-45e4-8ec5-404a5efe4563\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"65b7bfc3-08e8-44ef-856f-d370965a4497\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bf275b39-f339-4c2e-82fa-565dcd7598b9\",\"optionName\":\"canCreate\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e8bc6189-9682-4906-83b6-71dba109fd48\",\"optionName\":\"canDelete\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2af97230-d7dd-46b8-8ebb-dac7832f2594\",\"optionName\":\"canUpdate\",\"value\":\"true\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"9f4746bb-5f14-4387-8e13-15fcb74c27ac\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d2bdd5c0-f21f-4df9-8d4f-703b07f60391\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4e56f2e9-2267-40d7-8c47-6ac04eafcac1\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0b11b893-737b-4a6d-8120-413b3b4ade9a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"820a42fa-e501-408e-8ec1-0f13dc2649af\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"5f7d81ad-8ce2-4bb5-8d2a-df9ddf7a469d\"}],\"layoutItemId\":\"Tab_Section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ea1eb403-5a6a-4696-86fe-0f8d21c9441a\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a833f7fe-412d-4991-814a-beb023555da6\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1556a30e-990f-415e-8d6c-6f02596a5d9a\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"9181030c-bbfb-4bb2-80d3-b075fb0396a0\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"38aa82d9-e867-4713-8baa-0c3620981e06\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97e1a2bf-7518-4b2d-86fd-49a64596da75\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3de82d5d-d1bb-4b53-8d59-8ac22baef4ee\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"06c8e540-e5dc-4f91-896d-1a9407110497\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"df201b11-8810-4f02-8d38-c75474141602\",\"optionName\":\"terminateReasonVIS\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a269fc56-5d1e-4316-85de-067553ef3196\",\"optionName\":\"errorPanelVIS\",\"value\":\"tw.local.errorMessageVis\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0f789f32-ff1a-4347-890d-42b22c7892f4\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5fa863b5-7bf4-4210-8d18-347b03f94cde\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c3994762-d884-480e-8c1c-4d5bca764c34\",\"optionName\":\"complianceApprovalVis\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3ced2ef0-d5a2-4ec9-803b-82b20b2e66dd\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"38d6d627-94c0-4831-8c42-2b008b046c67\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2d4f7514-3c2e-4e83-8e23-539e50a54965\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3398b5f5-fd3a-4f3d-84f8-5b94be63ac8f\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1606c373-1cfb-453e-8652-425992d67950\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7140178b-5f69-4b79-80b6-2c5d72698c5b\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"None\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"77b286bd-3738-49e3-8c24-11f97411a1fe\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Coach\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\"},{\"incoming\":[\"2027.00100c75-cc1c-41f4-a5bf-ef88085b65b2\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1975,\"y\":199,\"declaredType\":\"TNodeVisualInfo\",\"height\":44}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"03ae3039-c478-455b-9e33-0258c9aa586f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.866cb443-0661-46da-9aeb-0f780bfcd299\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessageVis\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.faf760f0-0a4f-4bf6-b946-9c106b55ffcb\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.screenName = \\\"\\\";\\nautoObject.userRole = \\\"\\\";\\nautoObject.complianceApproval = false;\\nautoObject.lastStepAction = \\\"\\\";\\nautoObject.subStatus = \\\"\\\";\\nautoObject.requestType = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d57f46e7-535a-4b65-b3ab-d68140590e50\"},{\"startQuantity\":1,\"outgoing\":[\"2027.145b023e-4308-4e49-8ae5-2150277f3c35\"],\"incoming\":[\"2027.373cfd94-5694-4bea-b674-21f7cf36a133\",\"2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6\"],\"default\":\"2027.145b023e-4308-4e49-8ae5-2150277f3c35\",\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":186,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Init\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.1d579c44-4b9b-4332-b150-91de7a551a5c\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.isChecker = false;\\r\\n\\/\\/---------------------- Init appInfo  \\\"This object is used in Header CV\\\"---------------------------------\\r\\nvar date = new Date();\\r\\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\\/' +(date.getMonth() + 1) + '\\/' + date.getFullYear();\\r\\n\\t\\r\\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\\\"( \\\"+ tw.system.user.name+\\\")\\\";\\r\\ntw.local.odcRequest.appInfo.requestName = \\\"ODC collection\\\";\\r\\ntw.local.odcRequest.appInfo.requestType = tw.local.odcRequest.requestType.name;\\r\\ntw.local.odcRequest.appInfo.status =\\\"Initiated\\\";\\r\\ntw.local.odcRequest.appInfo.subStatus =\\\"Initiated\\\";\\r\\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol01;\\r\\ntw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo; \\/\\/tw.system.processInstance.id;\\r\\n\\r\\n\\/\\/---------------------------------Init Step log ---------------------------------------------------\\t \\t \\r\\ntw.local.odcRequest.stepLog={};\\r\\ntw.local.odcRequest.stepLog.startTime = new Date();\\r\\ntw.local.odcRequest.stepLog.step = tw.epv.Col_ScreenNames.ODCCol01; \\r\\n\\r\\n \\/\\/----------------------------------------------------------------------------------------------\\r\\n\\/\\/Init actionConditions \\\"This object used by service (Get Actions by ScreenName 2) \\\"\\r\\ntw.local.actionConditions={};\\r\\ntw.local.actionConditions.complianceApproval= false;\\r\\ntw.local.actionConditions.lastStepAction= tw.local.odcRequest.stepLog.action;\\r\\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol01;\\r\\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\\r\\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\\t \\r\\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\\t\\r\\n\\r\\n\\/\\/Init liquidation data\\r\\ntw.local.odcRequest.ContractLiquidation.liqCurrency= tw.local.odcRequest.FinancialDetailsBR.currency.value;\\r\\n\\/\\/init request state \\r\\ntw.local.odcRequest.BasicDetails.requestState=\\\"undergoing collection\\\";\\r\\n\\r\\nif(tw.local.odcRequest.isLiquidated == false)\\t{\\r\\n\\ttw.local.odcRequest.ContractLiquidation= {};\\r\\n\\ttw.local.odcRequest.ContractLiquidation.creditedAccount= {};\\r\\n\\ttw.local.odcRequest.ContractLiquidation.creditedAmount= {};\\r\\n\\ttw.local.odcRequest.ContractLiquidation.creditedAccount.currency={};\\r\\n\\t}\\r\\n\\t\\r\\ntw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\\r\\ntw.local.odcRequest.BasicDetails.requestType = \\ttw.local.odcRequest.requestType.name;\\r\\n\\r\\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass={};\\r\\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.name= \\\"Customer Account\\\";\\r\\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value= \\\"Customer Account\\\";\\r\\n\\r\\n\\r\\n\"]}},{\"outgoing\":[\"2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6\"],\"incoming\":[\"2027.2b247745-5401-419b-a4b0-32493f674d7a\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":803,\"y\":304,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.185cde63-01a4-4571-8c47-554e7a6a1de7\"},{\"targetRef\":\"2025.fb195954-bc69-4a3d-9008-49e5f46d3201\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"eb89d296-85ab-4789-8efd-7e3d49c091c4\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c783ffa8-30b8-43a2-b551-bc7251648820\",\"sourceRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\"},{\"startQuantity\":1,\"outgoing\":[\"2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51\"],\"incoming\":[\"2027.c783ffa8-30b8-43a2-b551-bc7251648820\"],\"default\":\"2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":946,\"y\":59,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.fb195954-bc69-4a3d-9008-49e5f46d3201\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/\\t debugger;\\r\\ntw.local.errorMessage = \\\"\\\";\\r\\nvar mandatoryTriggered = false;\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\r\\n\\/\\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\\r\\nmandatory(tw.local.odcRequest.stepLog.action, \\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\r\\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel){\\r\\n    mandatory(tw.local.odcRequest.stepLog.comment, \\\"tw.local.odcRequest.stepLog.comment\\\");\\r\\n}\\r\\n\\r\\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.submitLiq || tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.createLiq) {\\r\\n\\r\\n    validateLiq();\\r\\n}\\r\\n\\r\\n\\/\\/------------------------------------------------- Contract Liquidation VALIDATION -----------------------------------------\\r\\nfunction validateLiq() {\\r\\n    \\/\\/debugger;\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.liqAmount, \\\"tw.local.odcRequest.ContractLiquidation.liqAmount\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditValueDate, \\\"tw.local.odcRequest.ContractLiquidation.creditValueDate\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitValueDate, \\\"tw.local.odcRequest.ContractLiquidation.debitValueDate\\\");\\r\\n    \\r\\n    checkDate(tw.local.odcRequest.ContractLiquidation.creditValueDate, tw.local.odcRequest.requestDate, \\\"tw.local.odcRequest.ContractLiquidation.creditValueDate\\\", \\\"Invalid date\\\", \\\"Credit Value Date should not be less than request date\\\");\\r\\n    checkDate(tw.local.odcRequest.ContractLiquidation.debitValueDate, tw.local.odcRequest.requestDate, \\\"tw.local.odcRequest.ContractLiquidation.debitValueDate\\\", \\\"Invalid date\\\", \\\"Debit Value Date should not be less than request date\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass\\\");\\r\\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitedAccountNo, \\\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\\\");\\r\\n\\r\\n    if (tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value == \\\"GL Account\\\") {\\r\\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo\\\");\\r\\n\\r\\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified == false) {\\r\\n            tw.local.errorMessage += \\\"<li>\\\" + \\\"GL Account is not verified.\\\" + \\\"<\\/li>\\\";\\r\\n            tw.system.coachValidation.addValidationError(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \\\"GL Account is not verified.\\\");\\r\\n        }\\r\\n\\r\\n        \\/\\/debugger;\\r\\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified) {\\r\\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode\\\");\\r\\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.value, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.currency\\\");\\r\\n        }\\r\\n\\r\\n        \\/\\/Validate OverDraft\\r\\n        if (tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount > tw.local.odcRequest.ContractLiquidation.creditedAccount.balance && tw.local.odcRequest.ContractLiquidation.creditedAccount.isOverDraft) {\\r\\n            addError(\\\"tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount\\\", \\\"ERROR: Must be <= Account Balance\\\")\\r\\n        }\\r\\n    \\r\\n    } else {\\r\\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo, \\\"tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo\\\");\\r\\n    }\\r\\n\\r\\n    if (tw.local.odcRequest.ContractLiquidation.liqAmount > tw.local.odcRequest.FinancialDetailsFO.outstandingAmount) {\\r\\n        addError(\\\"tw.local.odcRequest.ContractLiquidation.liqAmount\\\", \\\"Liquidation Amount must be <= ODC Outstanding Amount\\\");\\r\\n        tw.local.errorMessage += \\\"<li>\\\" + \\\"Liquidation Amount must be less than or equal to ODC Outstanding Amount.\\\" + \\\"<\\/li>\\\";\\r\\n    }\\r\\n\\r\\n    \\/\\/----------------------------------------------- Charges and Commissions VALIDATION -------------------------------------\\t\\t\\r\\n    for (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\\r\\n\\r\\n        \\/\\/Description - Flat Amount\\r\\n        if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \\\"Flat Amount\\\") {\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null) {\\r\\n                addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", \\\"Must be >= 0\\\");\\r\\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\\r\\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", \\\"Must be Decimal(14,2)\\\")\\r\\n            }\\r\\n            \\/\\/        mandatory(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\");\\r\\n\\r\\n            \\/\\/ minLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", 2, \\\"Shouldn't be less than 2 character\\\", \\\"Change Amount: Shouldn't be less than 2 character\\\");\\r\\n            \\/\\/ maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\", 14, \\\"Shouldn't be more than 14 character\\\", \\\"Change Amount:\\\" + \\\"Shouldn't be more than 14 character\\\");\\r\\n\\r\\n        \\/\\/Fixed Rate\\r\\n        } else {\\r\\n\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null) {\\r\\n                addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", \\\"Must be >= 0\\\");\\r\\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\\r\\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", \\\"Must be Decimal(14,2)\\\")\\r\\n            }\\r\\n            \\/\\/ minLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", 2, \\\"Shouldn't be less than 2 character\\\", \\\"Change Percentage: Shouldn't be less than 2 character\\\");\\r\\n            \\/\\/ maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\", 14, \\\"Shouldn't be more than 14 character\\\", \\\"Change Percentage:\\\" + \\\"Shouldn't be more than 14 character\\\");\\r\\n        }\\r\\n\\r\\n        \\/\\/skip validation if waiver or changeAmnt < 0\\r\\n        if (tw.local.odcRequest.ChargesAndCommissions[i].waiver == false && tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\\r\\n\\r\\n            \\/\\/Validate debited accounts\\r\\n            \\/\\/ mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.customerAccountNo\\\");\\r\\n            \\/\\/ mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.accountClass\\\");\\r\\n\\r\\n            \\/\\/GL Account\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value == \\\"GL Account\\\") {\\r\\n\\r\\n                if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\\r\\n                    addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.glAccountNo\\\", \\\"GL Account Not Verified\\\");\\r\\n                }\\r\\n\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.glAccountNo\\\");\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.currency.value\\\");\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.branchCode\\\");\\r\\n\\r\\n                \\/\\/Customer Account\\r\\n            } else {\\r\\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAccount.customerAccountNo\\\");\\r\\n            }\\r\\n\\r\\n            \\/\\/DebitedAmount\\r\\n\\r\\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\\r\\n                validateDecimal(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.negotiatedExRate\\\", \\\"Must be Decimal(16,10)\\\")\\r\\n            }\\r\\n\\r\\n            \\/\\/ minLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.negotiatedExRate\\\", 2, \\\"Shouldn't be less than 2 character\\\", \\\"Negotiated Exchange Rate: Shouldn't be less than 2 character\\\");\\r\\n            \\/\\/ maxLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.negotiatedExRate\\\", 14, \\\"Shouldn't be more than 14 character\\\", \\\"Negotiated Exchange Rate:\\\" + \\\"Shouldn't be more than 14 character\\\");\\r\\n\\r\\n            \\/\\/Correct Validation but Waiting confirmation on what to do if GL account\\r\\n            if ((tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\\r\\n                tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance) && tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false) {\\r\\n                addError(\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].debitedAmount.amountInAccount\\\", \\\"ERROR: Must be <= Account Balance\\\");\\r\\n            }\\r\\n        }\\r\\n    }\\r\\n}   \\/\\/end of validation function\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- Validation Functions ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/*\\r\\n* =========================================================================================================\\r\\n*  \\r\\n* Add a coach validation error \\r\\n* \\t\\t\\r\\n* EX:\\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\\r\\n*\\r\\n* =========================================================================================================\\r\\n*\\/\\r\\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\\r\\n    tw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n    fromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\n\\/*\\r\\n* =======================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the date1 is less than date2\\r\\n*\\t\\r\\n* EX:checkDate( date, requestDate ,  'this is a validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =======================================================================================================================\\r\\n*\\/\\r\\nfunction checkDate(date, requestDate, dateName, controlMessage, validationMessage) {\\r\\n    if (date != null && date != undefined && date < requestDate) {\\r\\n        addError(dateName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\/*\\r\\n* =================================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is less than given length\\r\\n*\\t\\r\\n* EX:\\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =================================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\\r\\n    if (field != null && field != undefined && field.length < len) {\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\/*\\r\\n* =======================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is greater than given length\\r\\n*\\t\\r\\n* EX:\\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =======================================================================================================================\\r\\n*\\/\\r\\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\\r\\n    if (field.length > len) {\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\/*\\r\\n* ==================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the field is null 'Mandatory'\\r\\n*\\t\\r\\n* EX:\\tnotNull(tw.local.name , 'tw.local.name')\\r\\n*\\r\\n* ==================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction mandatory(field, fieldName) {\\r\\n    if (field == null || field == undefined) {\\r\\n        addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n        mandatoryTriggered = true;\\r\\n        return false;\\r\\n    }\\r\\n    else {\\r\\n        switch (typeof field) {\\r\\n            case \\\"string\\\":\\r\\n                if (field.trim() != undefined && field.trim() != null && field.trim().length == 0) {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                break;\\r\\n            case \\\"number\\\":\\r\\n                if (field == 0.0) {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                if (field < 0) {\\r\\n                    var msg = \\\"Invalid Value, This field can not be negative value.\\\";\\r\\n                    addError(fieldName, msg, msg, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                break;\\r\\n\\r\\n            case \\\"boolean\\\":\\r\\n                if (field == false) {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n                break;\\r\\n            default:\\r\\n\\r\\n                \\/\\/ VALIDATE DATE OBJECT\\r\\n                if (field && field.getTime && isFinite(field.getTime())) { }\\r\\n\\r\\n                else {\\r\\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\\r\\n                    mandatoryTriggered = true;\\r\\n                    return false;\\r\\n                }\\r\\n        }\\r\\n    }\\r\\n    return true;\\r\\n}\\r\\n\\r\\n\\/\\/Validate Decimal(10,6)\\r\\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\\r\\n    \\/\\/ Regular expression pattern for decimal validation\\r\\n    var decimalPattern = \\/^\\\\d{1,4}(\\\\.\\\\d{1,6})?$\\/;\\r\\n\\r\\n    \\/\\/ Check if the decimal matches the pattern\\r\\n    if (!decimalPattern.test(field)) {\\r\\n        \\/\\/ Decimal is valid\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    } else {\\r\\n        \\/\\/ Decimal is invalid\\r\\n        return true;\\r\\n    }\\r\\n}\\r\\n\\r\\n\\/\\/Validate Decimal(14,2)\\r\\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\\r\\n    validationMessage = controlMessage;\\r\\n    \\/\\/ Regular expression pattern for decimal validation\\r\\n    var decimalPattern = \\/^\\\\d{1,12}(\\\\.\\\\d{1,2})?$\\/;\\r\\n\\r\\n    \\/\\/ Check if the decimal matches the pattern\\r\\n    if (!decimalPattern.test(field)) {\\r\\n        \\/\\/ Decimal is valid\\r\\n        addError(fieldName, controlMessage, validationMessage);\\r\\n        return false;\\r\\n    } else {\\r\\n        \\/\\/ Decimal is invalid\\r\\n        return true;\\r\\n    }\\r\\n}\\r\\ntw.local.errorMessage != null ? tw.local.errorMessageVis = \\\"EDITABLE\\\" : tw.local.errorMessageVis = \\\"NONE\\\";\\t\\t\"]}},{\"outgoing\":[\"2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e\",\"2027.f9539453-1414-4bd4-be3e-5b5a2cd60701\"],\"default\":\"2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1085,\"y\":95,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.ef877a71-0e37-49d3-9127-63502f4d4069\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51\",\"sourceRef\":\"2025.fb195954-bc69-4a3d-9008-49e5f46d3201\"},{\"targetRef\":\"2025.ce2cdb08-0319-4ac0-a07d-6966abcad237\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e\",\"sourceRef\":\"2025.ef877a71-0e37-49d3-9127-63502f4d4069\"},{\"incoming\":[\"2027.f9539453-1414-4bd4-be3e-5b5a2cd60701\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":71,\"x\":1098,\"y\":55,\"declaredType\":\"TNodeVisualInfo\",\"height\":44}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}],\"preAssignmentScript\":[]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.5936a9ea-ae2b-45df-8027-69c29a6528d9\"},{\"targetRef\":\"2025.5936a9ea-ae2b-45df-8027-69c29a6528d9\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  !=\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f9539453-1414-4bd4-be3e-5b5a2cd60701\",\"sourceRef\":\"2025.ef877a71-0e37-49d3-9127-63502f4d4069\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.375043cb-a161-4c6f-9861-21d74928b756\"},{\"itemSubjectRef\":\"itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2\",\"name\":\"ldapUserProfile\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.042bf647-73ed-4939-96e1-397087c17e41\"},{\"outgoing\":[\"2027.e41fd63d-542e-49f4-a3ef-9345679a28c6\"],\"incoming\":[\"2027.e113fb43-7ad2-49ce-a533-8f43259f30c3\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":1351,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.e41fd63d-542e-49f4-a3ef-9345679a28c6\",\"name\":\"Set Status And Sub Status\",\"dataInputAssociation\":[{\"targetRef\":\"2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\"]}}]},{\"targetRef\":\"2055.6010a917-3b0f-4c13-8973-51c4eede4cd9\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.stepName\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.af01a78b-ba4b-44df-bee8-875288c1ccd2\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}],\"sourceRef\":[\"2055.71a67d77-b802-42cf-8cf2-380a9594a9c2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}],\"sourceRef\":[\"2055.ce1f6bfc-b971-4978-80c8-092f5d40c209\"]}],\"calledElement\":\"1.81656d33-5348-479b-a7af-5631356d9476\"},{\"targetRef\":\"2025.62723cde-1c43-48bf-b898-cde90ae6d585\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e41fd63d-542e-49f4-a3ef-9345679a28c6\",\"sourceRef\":\"2025.af01a78b-ba4b-44df-bee8-875288c1ccd2\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNoVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a6fe8490-2b24-48b7-b057-d78825afe626\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.808a58dc-8db2-49a5-8550-c17175ff2ba9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"documentation\":[{\"content\":[\"to be deleted\\u00a0\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7dd53a94-ac61-477d-add6-9c0e922e8d6a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"documentation\":[{\"content\":[\"to be deleted\\u00a0\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"requestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8d124b98-573c-4e76-b668-d7abd7f1d9e1\"},{\"outgoing\":[\"2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de\"],\"incoming\":[\"2027.9fc452a2-e5ed-439a-95b5-f757aa356cdd\",\"2027.e41fd63d-542e-49f4-a3ef-9345679a28c6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1754,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de\",\"name\":\"Update History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.userRole.RevAct01\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.62723cde-1c43-48bf-b898-cde90ae6d585\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.a617c560-c740-484e-89de-0931088cdc6c\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"glAccountVerified\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ced83887-655c-4132-bedd-b5b98b52bd61\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"accounteePartyCif\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a708b15b-3e17-4ab8-9344-671c515db949\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"077\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"documentation\":[{\"content\":[\"to be deleted<div><br \\/><\\/div><div>001<\\/div>\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"code\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2ef1cb2b-c32a-49d8-be07-2c4cfff5ef35\"},{\"targetRef\":\"2025.185cde63-01a4-4571-8c47-554e7a6a1de7\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"c38ca5cd-ef8e-4820-aebd-4db6926cdbaa\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2b247745-5401-419b-a4b0-32493f674d7a\",\"sourceRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6\",\"sourceRef\":\"2025.185cde63-01a4-4571-8c47-554e7a6a1de7\"},{\"outgoing\":[\"2027.e113fb43-7ad2-49ce-a533-8f43259f30c3\",\"2027.663307a2-6be3-4362-82da-e71cb0be1525\"],\"incoming\":[\"2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e\"],\"default\":\"2027.e113fb43-7ad2-49ce-a533-8f43259f30c3\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1204,\"y\":195,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Is Liquidated?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.ce2cdb08-0319-4ac0-a07d-6966abcad237\"},{\"targetRef\":\"2025.af01a78b-ba4b-44df-bee8-875288c1ccd2\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.Col_Actions.createLiq\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e113fb43-7ad2-49ce-a533-8f43259f30c3\",\"sourceRef\":\"2025.ce2cdb08-0319-4ac0-a07d-6966abcad237\"},{\"targetRef\":\"2025.d199b9f9-4dde-42e0-906e-f6b7577a099b\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.Col_Actions.createLiq\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.663307a2-6be3-4362-82da-e71cb0be1525\",\"sourceRef\":\"2025.ce2cdb08-0319-4ac0-a07d-6966abcad237\"},{\"startQuantity\":1,\"outgoing\":[\"2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c\"],\"incoming\":[\"2027.663307a2-6be3-4362-82da-e71cb0be1525\"],\"default\":\"2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1070,\"y\":276,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Liq Flag\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.d199b9f9-4dde-42e0-906e-f6b7577a099b\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.isLiquidated= true;\\r\\ntw.local.odcRequest.stepLog.action=\\\"\\\";\\r\\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\\r\\n\\r\\ntw.local.isChecker = true;\\r\\n\\r\\n\\/\\/location.reload(true);\\r\\n\\/\\/window.location.reload();\\r\\n\"]}},{\"outgoing\":[\"2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b\"],\"incoming\":[\"2027.*************-4f1a-84f5-07640da1a7ec\"],\"extensionElements\":{\"postAssignmentScript\":[\"tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;\\/\\/ tw.local.parentPath\\r\\ntw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;\\r\\n\\r\\nif(!!tw.local.error && tw.local.error.errorText!=null)\\r\\n\\ttw.local.errorMessage+= tw.local.error.errorText;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":466,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.serviceName=\\\"Create\\\"\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b\",\"name\":\"Create ECM Folder\",\"dataInputAssociation\":[{\"targetRef\":\"2055.4156964b-1c67-40bc-8f62-3804c71cf908\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.e2ce0eed-342c-4942-8214-83e964b550e5\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.hubCode\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderID\"]}}],\"sourceRef\":[\"2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}],\"sourceRef\":[\"2055.5f955245-0538-4e40-80a6-12f45c3102f3\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.214c7268-80d0-444d-8702-dd0d5462dbe7\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.fullPath\"]}}],\"sourceRef\":[\"2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}],\"sourceRef\":[\"2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.676e3a06-e2cc-4855-84d6-6f82a350500a\"]}],\"calledElement\":\"1.46b984a3-b4ad-405a-abd3-8631f907efe4\"},{\"startQuantity\":1,\"outgoing\":[\"2027.1bd9e34e-b12b-4985-b405-608eef0daddd\"],\"default\":\"2027.1bd9e34e-b12b-4985-b405-608eef0daddd\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":289,\"y\":333,\"declaredType\":\"TNodeVisualInfo\",\"height\":69}]},\"name\":\"Set Attachments list\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.b845eb69-1e4d-4b45-bf66-6bc282fc928e\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(!tw.local.odcRequest.attachmentDetails)\\r\\n\\ttw.local.odcRequest.attachmentDetails= {};\\r\\n\\t\\r\\nif (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {\\r\\n\\ttw.local.odcRequest.attachmentDetails.attachment = []; \\r\\n\\t\\r\\n}\\t\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \\\"Customer Request\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \\\"Customer Request\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \\\"\\u0637\\u0644\\u0628 \\u0627\\u0644\\u0639\\u0645\\u064a\\u0644\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1].name = \\\"BILL OF EXCHANGE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1].description = \\\"BILL OF EXCHANGE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[1].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2].name = \\\"Invoice\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2].description = \\\"Invoice\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[2].arabicName = \\\"\\u0641\\u0627\\u062a\\u0648\\u0631\\u0629\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3].name = \\\"BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3].description = \\\"BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[3].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4].name = \\\"AIRWAY BILL\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4].description = \\\"AIRWAY BILL\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[4].arabicName = \\\"\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5].name = \\\"TRUCK CONSIGNMENT NOTE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5].description =\\\"TRUCK CONSIGNMENT NOTE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[5].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6].name = \\\"N\\/N BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6].description = \\\"N\\/N BILL OF LADING\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[6].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7].name = \\\"COURIER \\/ POST RECEIPT\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7].description = \\\"COURIER \\/ POST RECEIPT\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[7].arabicName = \\\"\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8].name = \\\"PACKING LIST\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8].description = \\\"PACKING LIST\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[8].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9].name = \\\"CERTIFICATE OF ORIGIN\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9].description = \\\"CERTIFICATE OF ORIGIN\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[9].arabicName = \\\"\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10].name = \\\"CERTIFICATE OF ANALYSIS\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10].description = \\\"CERTIFICATE OF ANALYSIS\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[10].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11].name = \\\"INSURANCE POLICY \\/ CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11].description = \\\"INSURANCE POLICY \\/ CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[11].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12].name = \\\"BENEFECIARY DECLARATION\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12].description = \\\"BENEFECIARY DECLARATION\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[12].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13].name = \\\"NON RADIOACTIVE CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13].description = \\\"NON RADIOACTIVE CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[13].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14].name = \\\"PHYTOSANITARY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14].description = \\\"PHYTOSANITARY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[14].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15].name = \\\"CERTIFICATE OF ANALYSIS\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15].description = \\\"Bill of exchange\\/draft\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[15].arabicName = \\\"\\u0627\\u0644\\u0643\\u0645\\u0628\\u064a\\u0627\\u0644\\u0629\\\" ;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16].name = \\\"HEALTH CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16].description = \\\"HEALTH CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[16].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17].name = \\\"INSPECTION CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17].description = \\\"INSPECTION CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[17].arabicName = \\\"\\\";;\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18].name = \\\"WARRANTY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18].description = \\\"WARRANTY CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[18].arabicName = \\\"\\\";\\r\\n\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19] = {};\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19].name = \\\"TEST CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19].description = \\\"TEST CERTIFICATE\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[19].arabicName = \\\"\\\";\\r\\n\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties = {};\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"2027.*************-4f1a-84f5-07640da1a7ec\"],\"default\":\"2027.*************-4f1a-84f5-07640da1a7ec\",\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":327,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"name\":\"Set ECM default properties\",\"dataInputAssociation\":[{\"targetRef\":\"2055.399c7a58-00b5-4451-9813-41c0b9652088\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.25394215-074f-4b79-8e84-9a96d32cc83b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.1e3d2772-6793-4cb1-a62e-54de905566f2\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.attachmentDetails\"]}}],\"sourceRef\":[\"2055.7d269650-ee48-4101-80db-2807cf921562\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.0261e8ad-a540-4682-88c5-87dff3eab23c\"]}],\"calledElement\":\"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8\"},{\"targetRef\":\"2025.bcd34832-e514-4a6c-9c1e-0af7bae14696\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set ECM default properties\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1bd9e34e-b12b-4985-b405-608eef0daddd\",\"sourceRef\":\"2025.b845eb69-1e4d-4b45-bf66-6bc282fc928e\"},{\"targetRef\":\"2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Create ECM Folder\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.*************-4f1a-84f5-07640da1a7ec\",\"sourceRef\":\"2025.1e3d2772-6793-4cb1-a62e-54de905566f2\"},{\"targetRef\":\"2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b\",\"sourceRef\":\"2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"fullPath\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.4a461249-8c22-48e6-9ae5-11ec1735e022\"},{\"outgoing\":[\"2027.a59c07cb-ff79-4db4-a3c0-493525050a99\"],\"incoming\":[\"2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b\",\"2027.eced07db-62f5-4bef-affe-97f152a2a6b0\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":602,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true],\"preAssignmentScript\":[\"tw.local.accounteePartyCif = \\\"\\\";\\r\\n\\r\\nif(!!tw.local.odcRequest.Parties && !!tw.local.odcRequest.Parties.partyTypes && tw.local.odcRequest.Parties.partyTypes.partyCIF !=null ) \\r\\n\\ttw.local.accounteePartyCif= tw.local.odcRequest.Parties.partyTypes.partyCIF;\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.a59c07cb-ff79-4db4-a3c0-493525050a99\",\"name\":\"Get Customer Account Details\",\"dataInputAssociation\":[{\"targetRef\":\"2055.7a0225d0-1ea8-4907-afba-e3a98de88df1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.d471dcbe-97c5-4911-87c7-7008dadc3a15\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.accounteePartyCif\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerAccounts\"]}}],\"sourceRef\":[\"2055.5855796c-8062-4709-8c2a-18f470d6d879\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.a32d29e2-af5d-4f6f-8041-8778849c9737\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.328cd87f-3306-4c20-8923-b7515b1cb782\"]}],\"calledElement\":\"1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"serviceName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1510aee6-3025-46de-9c86-58a06e67ce84\"},{\"incoming\":[\"2027.4683f817-3c32-411d-b118-a123ffa86a4c\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1939,\"y\":272,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.949e8a1a-c989-4a31-a14a-df108483fd24\"},{\"outgoing\":[\"2027.ab69b6dc-6741-41f1-a1b8-75a533256a48\",\"2027.eced07db-62f5-4bef-affe-97f152a2a6b0\"],\"default\":\"2027.ab69b6dc-6741-41f1-a1b8-75a533256a48\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":-39,\"y\":196,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"is Liquidated?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.c180fd4c-112c-4218-8691-6f7d496b7c1f\"},{\"targetRef\":\"2025.32f042c7-4da3-463c-ac2f-8a2d79da9463\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"NO\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ab69b6dc-6741-41f1-a1b8-75a533256a48\",\"sourceRef\":\"2025.c180fd4c-112c-4218-8691-6f7d496b7c1f\"},{\"targetRef\":\"2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.isLiquidated\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"YES\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.eced07db-62f5-4bef-affe-97f152a2a6b0\",\"sourceRef\":\"2025.c180fd4c-112c-4218-8691-6f7d496b7c1f\"},{\"outgoing\":[\"2027.00100c75-cc1c-41f4-a5bf-ef88085b65b2\",\"2027.4683f817-3c32-411d-b118-a123ffa86a4c\"],\"incoming\":[\"2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de\"],\"default\":\"2027.4683f817-3c32-411d-b118-a123ffa86a4c\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1872,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"log history?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009\"},{\"targetRef\":\"2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To log history?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de\",\"sourceRef\":\"2025.62723cde-1c43-48bf-b898-cde90ae6d585\"},{\"targetRef\":\"03ae3039-c478-455b-9e33-0258c9aa586f\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.00100c75-cc1c-41f4-a5bf-ef88085b65b2\",\"sourceRef\":\"2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009\"},{\"targetRef\":\"2025.949e8a1a-c989-4a31-a14a-df108483fd24\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage!= null) || (tw.local.errorMessage!= \\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4683f817-3c32-411d-b118-a123ffa86a4c\",\"sourceRef\":\"2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009\"},{\"outgoing\":[\"2027.373cfd94-5694-4bea-b674-21f7cf36a133\"],\"incoming\":[\"2027.ab69b6dc-6741-41f1-a1b8-75a533256a48\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":46,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.373cfd94-5694-4bea-b674-21f7cf36a133\",\"name\":\"Retrieve Request Number\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.32f042c7-4da3-463c-ac2f-8a2d79da9463\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}],\"sourceRef\":[\"2055.29fabc80-90b8-4cad-81e3-1c319c6f595a\"]}],\"calledElement\":\"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546\"},{\"targetRef\":\"2025.1d579c44-4b9b-4332-b150-91de7a551a5c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.373cfd94-5694-4bea-b674-21f7cf36a133\",\"sourceRef\":\"2025.32f042c7-4da3-463c-ac2f-8a2d79da9463\"},{\"startQuantity\":1,\"incoming\":[\"2027.1bd9e34e-b12b-4985-b405-608eef0daddd\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":409,\"y\":331,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"name\":\"Copy of Set ECM default properties\",\"dataInputAssociation\":[{\"targetRef\":\"2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.6f216377-34de-4f5e-8ab5-adc2796733ee\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.customerName\"]}}]},{\"targetRef\":\"2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user.fullName\"]}}]},{\"targetRef\":\"2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.processInstance.id\"]}}]},{\"targetRef\":\"2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.bcd34832-e514-4a6c-9c1e-0af7bae14696\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.eac829db-66fe-43f5-810c-6faa514533a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties\"]}}],\"sourceRef\":[\"2055.81cce609-b3fe-4f11-809f-c3a599908595\"]}],\"calledElement\":\"1.2d3ab562-82df-48a5-9de7-f5d964218191\"},{\"startQuantity\":1,\"outgoing\":[\"2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710\"],\"default\":\"2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1476,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\r\\ntw.local.odcRequest.BasicDetails.requestState=tw.epv.RequestState.Collection ;\\r\\n\\r\\n\"]},\"name\":\"Audit Collection Data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e39bfaa6-c863-41f9-8061-0e371dff89cb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.isLiquidated\"]}}]},{\"targetRef\":\"2055.5d4b901c-324e-4bea-8f10-e160a656c696\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.OdcCollection.amount\"]}}]},{\"targetRef\":\"2055.b51575f2-8ce0-48d0-8179-71d12e0440e7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.OdcCollection.currency\"]}}]},{\"targetRef\":\"2055.84daf689-5f95-458f-8b7f-d8c08459d4c1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.20995cf3-6a12-4378-8292-51106389c796\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNature.name\"]}}]},{\"targetRef\":\"2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.name\"]}}]},{\"targetRef\":\"2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestDate\"]}}]},{\"targetRef\":\"2055.85dca7ee-4057-4dcd-878f-b924dff64190\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.328377fd-ccc9-4119-80ca-435deb518aee\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]},{\"targetRef\":\"2055.37b99722-adca-4c0b-8d6d-aa2eeae29994\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}]},{\"targetRef\":\"2055.b0be0c94-0742-4365-875f-1b01b63caf0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]},{\"targetRef\":\"2055.27a871f0-6893-4366-80d9-133f55bffddb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]},{\"targetRef\":\"2055.ebcd1729-7d20-4759-81b3-e98e9f554767\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}]},{\"targetRef\":\"2055.debd9766-ed8e-45c7-8bbb-c471a2567088\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.ad93dc50-07e4-45ca-a21a-817f396172da\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.8d379594-e94f-4a21-8222-396c4ba9b2e1\"]}],\"calledElement\":\"1.40b72dbc-5d84-4b6d-9621-4e738d6838a1\"},{\"outgoing\":[\"2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a\",\"2027.9fc452a2-e5ed-439a-95b5-f757aa356cdd\"],\"incoming\":[\"2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710\"],\"default\":\"2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1574,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"audited successfully?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a\"},{\"incoming\":[\"2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1693,\"y\":279,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Copy of Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.527fdaa6-8210-41a6-8f77-72dd1c03ac09\"},{\"targetRef\":\"2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To audited successfully?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710\",\"sourceRef\":\"2025.ad93dc50-07e4-45ca-a21a-817f396172da\"},{\"targetRef\":\"2025.527fdaa6-8210-41a6-8f77-72dd1c03ac09\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage!= null || tw.local.errorMessage!=\\\"\\\"\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a\",\"sourceRef\":\"2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a\"},{\"targetRef\":\"2025.62723cde-1c43-48bf-b898-cde90ae6d585\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Update History\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9fc452a2-e5ed-439a-95b5-f757aa356cdd\",\"sourceRef\":\"2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set ECM default properties\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.145b023e-4308-4e49-8ae5-2150277f3c35\",\"sourceRef\":\"2025.1d579c44-4b9b-4332-b150-91de7a551a5c\"},{\"outgoing\":[\"2027.c50dcf04-7330-42a1-86d1-35abdff84012\",\"2027.43b953d0-b231-40c0-9270-0d66179a25f2\"],\"default\":\"2027.43b953d0-b231-40c0-9270-0d66179a25f2\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":772,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Has ProductCode?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.31168a88-607c-4da6-ab4a-963438a9f04a\"},{\"outgoing\":[\"2027.f15eff18-e5d7-47eb-85f2-b00277b37810\"],\"incoming\":[\"2027.c50dcf04-7330-42a1-86d1-35abdff84012\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"activityAdHocSettings\":[{\"hidden\":false,\"repeatable\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityAdHocSettings\",\"triggerType\":\"Automatic\",\"option\":\"Required\"}],\"nodeVisualInfo\":[{\"width\":95,\"x\":757,\"y\":-68,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true],\"activityPreconditions\":[{\"documentTriggerMode\":\"LegacyCase\",\"sourceFolderReferenceType\":\"FolderId\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TActivityPreconditions\",\"triggerType\":\"NoPreconditions\",\"matchAll\":true}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.f15eff18-e5d7-47eb-85f2-b00277b37810\",\"name\":\"Get Charges Completed\",\"dataInputAssociation\":[{\"targetRef\":\"2055.12683c54-ad5b-4c68-b734-93d1dea93938\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ChargesAndCommissions\"]}}]},{\"targetRef\":\"2055.7d751a23-0e1b-4987-a9bc-41b25f644f54\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value\"]}}]},{\"targetRef\":\"2055.66b62639-4042-49bf-9544-dbb15f00f3be\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.value\"]}}]},{\"targetRef\":\"2055.86004a6f-9b72-4f74-a69a-aed7c869a281\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"]}}]},{\"targetRef\":\"2055.1001aa15-5c17-4db8-ab30-0263431130b2\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.FinancialDetailsBR.currency.value\"]}}]},{\"targetRef\":\"2055.ad7e0a45-5cb2-437f-b705-2ff310914291\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]},{\"targetRef\":\"2055.25041969-c52c-4425-8c47-741f31a7fa66\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\"}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.0c491680-083e-4ac5-aa6a-63384b069d01\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ChargesAndCommissions\"]}}],\"sourceRef\":[\"2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0\"]}],\"calledElement\":\"1.47164ac0-82bf-4670-b0f1-51d0e36f4463\"},{\"targetRef\":\"2025.0c491680-083e-4ac5-aa6a-63384b069d01\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \\\"\\\"\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c50dcf04-7330-42a1-86d1-35abdff84012\",\"sourceRef\":\"2025.31168a88-607c-4da6-ab4a-963438a9f04a\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \\\"\\\"\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.43b953d0-b231-40c0-9270-0d66179a25f2\",\"sourceRef\":\"2025.31168a88-607c-4da6-ab4a-963438a9f04a\"},{\"outgoing\":[\"2027.49d50c9f-44fc-483c-9b41-ee6096df92a4\"],\"incoming\":[\"2027.a59c07cb-ff79-4db4-a3c0-493525050a99\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":603,\"y\":290,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\/\\/tw.local.odcRequest.parentRequestNo = \\\"00104230000170\\\";\\r\\n\\/\\/tw.local.odcRequest.requestType.value = tw.epv.RequestType.Collection\\r\\n\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.49d50c9f-44fc-483c-9b41-ee6096df92a4\",\"name\":\"Retrieve Product Code\",\"dataInputAssociation\":[{\"targetRef\":\"2055.739ec1bd-4791-48bc-830c-f00e254474a3\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.c1787180-2088-406b-8072-bc0aba3b30ae\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractCreation.productCode.value\"]}}],\"sourceRef\":[\"2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isfound\"]}}],\"sourceRef\":[\"2055.e6db1787-627d-4373-80cc-75c8940bc32d\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.message\"]}}],\"sourceRef\":[\"2055.e1811b7a-e326-4b9c-863e-59e994c41703\"]}],\"calledElement\":\"1.0ed017b2-b582-4d5c-b5b5-3649c89ee192\"},{\"targetRef\":\"2025.c1787180-2088-406b-8072-bc0aba3b30ae\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Retrieve Product Code\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.a59c07cb-ff79-4db4-a3c0-493525050a99\",\"sourceRef\":\"2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.49d50c9f-44fc-483c-9b41-ee6096df92a4\",\"sourceRef\":\"2025.c1787180-2088-406b-8072-bc0aba3b30ae\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isfound\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1c6118ff-6dad-4417-843c-754202d43388\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"message\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ca330556-90cb-4ea9-a843-7b890c771316\"},{\"outgoing\":[\"2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba\"],\"incoming\":[\"2027.64d54f87-102e-4a1c-be15-8eea624e779a\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"postAssignmentScript\":[\"if (tw.local.ValidationMessage == \\\"\\\" && tw.local.ValidationMessage == null) {\\r\\n\\tif (tw.local.errorMessage == \\\"\\\" && tw.local.errorMessage == null) {\\r\\n\\t\\talert(\\\"Valid\\\");\\r\\n\\t}\\r\\n}else{\\r\\n\\t tw.system.coachValidation.addValidationError(\\\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\\\", tw.local.ValidationMessage);\\r\\n}\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":793,\"y\":393,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba\",\"name\":\"Get Debited Nostro Vostro Account\",\"dataInputAssociation\":[{\"targetRef\":\"2055.723db670-6afc-4c61-8bff-61069e97a6df\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\"]}}]},{\"targetRef\":\"2055.a4b64bc1-a6e1-4866-82c6-48a1bd9a6693\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractLiquidation.liqCurrency\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.462404fa-2cce-4c58-b7e8-3240a943549d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.6d466c39-b0d0-4414-8065-f99b230bcf07\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.ValidationMessage\"]}}],\"sourceRef\":[\"2055.63de69c3-31f4-45df-8a71-32f0f2f65fd2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ContractLiquidation.debitedAccountName\"]}}],\"sourceRef\":[\"2055.3387da0f-2282-4ae0-8159-9dc014499f8f\"]}],\"calledElement\":\"1.672c16cb-04d7-4e67-a904-779d9009e1ce\"},{\"targetRef\":\"2025.462404fa-2cce-4c58-b7e8-3240a943549d\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"34bf2c19-a544-4633-a7a6-3735105cddf0\",\"coachEventPath\":\"Contract_Liquidation1\\/verify\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomLeft\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Debited Nostro Vostro Account\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.64d54f87-102e-4a1c-be15-8eea624e779a\",\"sourceRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba\",\"sourceRef\":\"2025.462404fa-2cce-4c58-b7e8-3240a943549d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"ValidationMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2b50c69d-1ead-4a36-9732-86439eb6d40a\"},{\"outgoing\":[\"2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7\"],\"incoming\":[\"2027.f15eff18-e5d7-47eb-85f2-b00277b37810\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":755,\"y\":-168,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true],\"preAssignmentScript\":[\"tw.local.customerAndPartyCifs = [];\\r\\nvar customerCIF = tw.local.odcRequest.CustomerInfo.cif = \\\"********\\\";\\r\\n\\r\\nvar partyCIF = tw.local.odcRequest.Parties.partyTypes.partyId;\\r\\nvar caseInNeedCIF = tw.local.odcRequest.Parties.caseInNeed.partyId;\\r\\n\\r\\n\\/\\/Build Cifs list with valid values or \\\"\\\" as default\\r\\n!!customerCIF ? tw.local.customerAndPartyCifs[0] = customerCIF : tw.local.customerAndPartyCifs[0] = \\\"\\\";\\r\\n\\r\\n!!partyCIF ? tw.local.customerAndPartyCifs[1] = partyCIF : tw.local.customerAndPartyCifs[1] = \\\"\\\";\\r\\n\\r\\n!!caseInNeedCIF ? tw.local.customerAndPartyCifs[2] = caseInNeedCIF : tw.local.customerAndPartyCifs[2] = \\\"\\\";\\r\\n\\r\\n\\/\\/remove duplicate cifs using SET\\r\\ntw.local.customerAndPartyCifs = [...new Set(tw.local.customerAndPartyCifs)];\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7\",\"name\":\"Get Customer and Party Account List\",\"dataInputAssociation\":[{\"targetRef\":\"2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerAndPartyCifs\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.customerAndPartyAccountList\"]}}],\"sourceRef\":[\"2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5\"]}],\"calledElement\":\"1.b92996ee-f375-4dc1-8c83-cfc4a49384a2\"},{\"targetRef\":\"2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Customer and Party Account List\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f15eff18-e5d7-47eb-85f2-b00277b37810\",\"sourceRef\":\"2025.0c491680-083e-4ac5-aa6a-63384b069d01\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"customerAndPartyCifs\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.e1f423f2-f0e9-4b73-9ced-3cc6042b4c8b\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"leftCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7\",\"sourceRef\":\"2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"rate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9fbc7b37-a14a-4538-a6be-949c03a3984b\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"calculatedChangeAmnt\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.25d94a4f-f1c4-481c-90ac-9d1a1086a21a\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"selectedIndex\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ddde124e-fbdc-4f73-98d5-157ddae5f9f2\"},{\"targetRef\":\"2025.1d579c44-4b9b-4332-b150-91de7a551a5c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false,\"customBendPoint\":[{\"x\":14,\"y\":114}]}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6\",\"sourceRef\":\"a3015b6c-0669-49d2-bc30-5aafe210ef21\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"exchangeRate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.0352dcd3-10cf-49e2-82c4-65bec186960a\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isChecker\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a7119a1a-6917-4320-8dc1-5be7ad5cc3f3\"},{\"targetRef\":\"2025.df396f09-7309-4b4b-8cbd-d5042a503b1d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"leftCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c\",\"sourceRef\":\"2025.d199b9f9-4dde-42e0-906e-f6b7577a099b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractLiquidatedMSG\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.43c64a73-38c2-4931-8e84-03cb1ac06779\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"verifyGLMsg\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.32bb146d-a184-4757-8f8d-8cd99307c19a\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"9ada33f2-a35e-4e9a-9ccf-0ede4301cadb\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"6df217cd-87ba-454d-861d-27274fb8952e\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Col01 - test\",\"declaredType\":\"globalUserTask\",\"id\":\"1.d5923909-74e7-467b-afb5-eef943fb9698\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.9e8b85ce-c7d3-4373-88d5-2c6eddf271fa\"},{\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"customerAccounts\",\"isCollection\":true,\"id\":\"2055.599ff5ce-111f-4579-ab02-26f39f7be4ef\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.ded500fb-620c-43b5-9914-95ad1aa8581c\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.a759db86-0dc8-41dc-8faf-40ea72941541\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd\",\"epvProcessLinkId\":\"87b4df33-7920-4ce7-86b1-893067e50ef0\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.e22cd1cb-4788-4edd-beb4-825e8f27f335\",\"epvProcessLinkId\":\"00fa02af-a828-48c4-89b1-5582241f99cb\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.93c5c002-7ac4-4283-83ee-63b8662f9223\",\"epvProcessLinkId\":\"74ed432e-5125-4d4d-8504-d1a1245fb100\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"04161144-e1f9-43bb-8e07-bf171bc54528\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7\",\"epvProcessLinkId\":\"e474676d-2c7a-4a4d-84d2-415932aae2f4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"a86f9fd0-6f96-403d-8b05-bc3945f285e2\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.062854b5-6513-4da8-84ab-0126f90e550d\",\"epvProcessLinkId\":\"e3108d03-1589-46fe-8951-79bfb122d51b\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"6d47d229-6af0-4a82-aa74-6c6b8e4e1303\"}],\"outputSet\":[{\"id\":\"********-c33c-4a43-aa40-61fccf28b856\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = {};\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = [];\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = [];\\nautoObject.FcCollections.listOfAccounts[0] = {};\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = [];\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = {};\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"IAVC\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.Drawer = {};\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = {};\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = {};\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = {};\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = {};\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.Parties.caseInNeed = {};\\nautoObject.Parties.caseInNeed.partyCIF = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyId = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyName = \\\"\\\";\\nautoObject.Parties.caseInNeed.country = \\\"\\\";\\nautoObject.Parties.caseInNeed.language = \\\"\\\";\\nautoObject.Parties.caseInNeed.refrence = \\\"\\\";\\nautoObject.Parties.caseInNeed.address1 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address2 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address3 = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType = {};\\nautoObject.Parties.caseInNeed.partyType.name = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].isGLFound = false;\\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \\\"\\\";\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.debitedAccountName = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject.folderPath = \\\"\\\";\\nautoObject.templateDocID = \\\"\\\";\\nautoObject.requestID = 0;\\nautoObject.customerAndPartyAccountList = [];\\nautoObject.customerAndPartyAccountList[0] = {};\\nautoObject.customerAndPartyAccountList[0].accountNO = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].currencyCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].branchCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\\nautoObject.customerAndPartyAccountList[0].typeCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerName = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerNo = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].frozen = false;\\nautoObject.customerAndPartyAccountList[0].dormant = false;\\nautoObject.customerAndPartyAccountList[0].noDebit = false;\\nautoObject.customerAndPartyAccountList[0].noCredit = false;\\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountClassCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balanceType = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountStatus = \\\"\\\";\\nautoObject.tradeFoComment = \\\"\\\";\\nautoObject.exeHubMkrComment = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"documentation\":[{\"content\":[\"partyCIF = &quot;********&quot;;\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.e5836d7b-ba93-4ddd-b01e-c6e05bb08a5a\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.hubCode = \\\"\\\";\\nautoObject.branchCode = \\\"\\\";\\nautoObject.initiatorUser = \\\"\\\";\\nautoObject.branchName = \\\"\\\";\\nautoObject.hubName = \\\"\\\";\\nautoObject.branchSeq = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"name\":\"routingDetails\",\"isCollection\":false,\"id\":\"2055.950b44e5-147a-4d6f-a8a2-8af9e91270ae\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.fb3d43b4-2150-48d8-bc54-cbf63db6784a\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"70d78878-0f14-4743-ae54-4c3e756bd09f\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e5836d7b-ba93-4ddd-b01e-c6e05bb08a5a", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": "partyCIF = &quot;********&quot;;", "guid": "57c647b2-d8b5-4715-8333-df42582b845e", "versionId": "48eb8e02-b5a6-498e-a483-0ddd3ecf6efe"}, {"name": "routingDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.950b44e5-147a-4d6f-a8a2-8af9e91270ae", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "parameterType": "1", "isArrayOf": "false", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "49f4098c-7a59-448a-89ed-bb0880b12800", "versionId": "027cf859-a412-425a-a076-32e68f86b08c"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.fb3d43b4-2150-48d8-bc54-cbf63db6784a", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1f93ff24-db4e-4907-b95e-ad3404492fe5", "versionId": "25297cad-7119-43bb-a219-1ad2b0ca906d"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.9e8b85ce-c7d3-4373-88d5-2c6eddf271fa", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "57cecdc3-7dbc-4207-8fbe-************", "versionId": "e5b35766-1284-4659-b905-e63a7e73841b"}, {"name": "customerAccounts", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.599ff5ce-111f-4579-ab02-26f39f7be4ef", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "parameterType": "2", "isArrayOf": "true", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "ab1a9edf-0056-4c14-8238-f475be7e6bab", "versionId": "a4a6f421-d4fa-4119-871d-b3f2cc9dd7c1"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ded500fb-620c-43b5-9914-95ad1aa8581c", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f699c94c-c041-41f9-b2b9-f131a22feb03", "versionId": "281558ac-ebde-441c-9eea-d2580d988aee"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.866cb443-0661-46da-9aeb-0f780bfcd299", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "145191f5-5ac0-47fa-8d3e-8432e808cfee", "versionId": "4c7c9f34-b51b-4041-bc3f-a8a36d1bfa2e"}, {"name": "errorMessageVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.faf760f0-0a4f-4bf6-b946-9c106b55ffcb", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c3e35ade-1acb-411a-be3e-17506b40b826", "versionId": "5c41a8b2-fe7b-4d1a-9928-2d690987d696"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d57f46e7-535a-4b65-b3ab-d68140590e50", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "978770a3-f7dd-4001-8b24-ebb1e527224e", "versionId": "4310d5e4-3220-4c04-8272-1a2ded106740"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.375043cb-a161-4c6f-9861-21d74928b756", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "04d1a62e-f8e6-43ff-ba60-eeb4093895ff", "versionId": "7646f825-f260-4cf2-aadb-7cc8e7aceeed"}, {"name": "ldapUserProfile", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.042bf647-73ed-4939-96e1-397087c17e41", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "cf46435b-b094-4678-88e0-8e137e4828f8", "versionId": "31de3cf3-108e-42d7-80af-50cbfdd68fe5"}, {"name": "parentRequestNoVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a6fe8490-2b24-48b7-b057-d78825afe626", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "743a8320-d03a-46d6-93ca-451cee116b83", "versionId": "40601ed1-92bb-4099-8e14-a37eacd3a74d"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.808a58dc-8db2-49a5-8550-c17175ff2ba9", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bbb7111d-924e-4fb5-9da4-add33aa450fd", "versionId": "7f644483-c258-4c64-b2b6-5dd941b711d5"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7dd53a94-ac61-477d-add6-9c0e922e8d6a", "description": "to be deleted ", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "dfad4347-ebe6-4477-98e7-b8028d525970", "versionId": "9d21449d-1702-43bc-8432-bdcad8962dec"}, {"name": "requestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8d124b98-573c-4e76-b668-d7abd7f1d9e1", "description": "to be deleted ", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a2b6a823-bd60-4bbf-ba01-d6775cac4760", "versionId": "20c50c8f-6e4e-408a-8688-db0e36301361"}, {"name": "glAccountVerified", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ced83887-655c-4132-bedd-b5b98b52bd61", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "********-c868-4c24-bc21-244fe12964e5", "versionId": "d873a30f-b7a8-45e8-b23d-bf7c57245aa4"}, {"name": "accounteePartyCif", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a708b15b-3e17-4ab8-9344-671c515db949", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0a29f3e3-c476-48f5-a16a-7516f4bac0e0", "versionId": "76bb8972-e7f0-41ec-8f7a-ac918f960f49"}, {"name": "code", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2ef1cb2b-c32a-49d8-be07-2c4cfff5ef35", "description": "to be deleted<div><br /></div><div>001</div>", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "12", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "8480fecf-da8f-4ea8-adc5-5467ad0e2160", "versionId": "4a0bfee3-3713-400f-a368-e00b0caea3dc"}, {"name": "fullPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.4a461249-8c22-48e6-9ae5-11ec1735e022", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "13", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7ae81a2e-f8af-45ff-a212-9f0e82e60cfd", "versionId": "7a8ab1ff-4586-4774-8feb-97034ae77a96"}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1510aee6-3025-46de-9c86-58a06e67ce84", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "14", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5b185c9f-b64f-465e-8cca-49013b610f42", "versionId": "69b845a0-dc2b-45d1-ade7-1fedbb100dd9"}, {"name": "isfound", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1c6118ff-6dad-4417-843c-754202d43388", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "15", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d733c3e2-b323-451f-9983-6369878b01ba", "versionId": "95403d2d-b8b1-4306-9451-1402bc4a82fe"}, {"name": "message", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ca330556-90cb-4ea9-a843-7b890c771316", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "16", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "459f144a-e4c1-4914-8d06-7f871baf95cb", "versionId": "f53a9362-a8a7-410d-996b-f25c9b86b12e"}, {"name": "ValidationMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2b50c69d-1ead-4a36-9732-86439eb6d40a", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "17", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "35654b81-cb5e-4f9b-9e43-41a668898405", "versionId": "426a2390-c8af-4c1c-973e-1118ec1aa53e"}, {"name": "customerAndPartyCifs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.e1f423f2-f0e9-4b73-9ced-3cc6042b4c8b", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "18", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fe5a38ce-a3ab-4087-953b-238ba6d7935d", "versionId": "320644fb-f7e8-4a9e-9780-0acbaa9f39e1"}, {"name": "rate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9fbc7b37-a14a-4538-a6be-949c03a3984b", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "19", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "29f4b8a2-1185-4921-b67f-6b14c01b40be", "versionId": "b0e037d6-82ff-42e3-b2d4-aa8d1f87a6cb"}, {"name": "calculatedChangeAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.25d94a4f-f1c4-481c-90ac-9d1a1086a21a", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "20", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "2a0b1886-cbab-4132-9696-82fceffe65ee", "versionId": "c74646a6-dda7-451f-8a5b-4894f0f096d1"}, {"name": "selectedIndex", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ddde124e-fbdc-4f73-98d5-157ddae5f9f2", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "21", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "dce847db-9a08-4f58-af59-0600d4beaeb5", "versionId": "62b25e66-d61c-4c11-baf7-880ed58b682c"}, {"name": "exchangeRate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0352dcd3-10cf-49e2-82c4-65bec186960a", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "22", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "93f08010-8fc4-453d-b67c-11439d35e39a", "versionId": "e13bda16-1fe7-47be-a6d9-5390cab6d7db"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a7119a1a-6917-4320-8dc1-5be7ad5cc3f3", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "23", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f9da4fda-2453-4fa9-829a-108e0131d9f8", "versionId": "2e8695b8-faea-4dd1-8039-08130da817cb"}, {"name": "contractLiquidatedMSG", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.43c64a73-38c2-4931-8e84-03cb1ac06779", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "24", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b129a894-e295-483c-ac21-8a5357c412ac", "versionId": "27bf6ce9-a4a7-4735-a6f3-d3b85055f8f6"}, {"name": "verifyGLMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.32bb146d-a184-4757-8f8d-8cd99307c19a", "description": {"isNull": "true"}, "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "namespace": "2", "seq": "25", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "89748b61-9af0-478f-92ac-9991a5e1f329", "versionId": "dfca08cb-7421-4dd6-9625-5c3fdd0677bc"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.af01a78b-ba4b-44df-bee8-875288c1ccd2", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Set Status And Sub Status", "tWComponentName": "SubProcess", "tWComponentId": "3012.5e08d602-0536-4ee9-a5c8-bc3b1dc711cc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b7", "versionId": "063c0c55-deda-401d-a960-a26ff5a75c2e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5e08d602-0536-4ee9-a5c8-bc3b1dc711cc", "attachedProcessRef": "/1.81656d33-5348-479b-a7af-5631356d9476", "guid": "11a333e4-7af4-49b5-be34-5b34f832b7ef", "versionId": "a3c21958-2b8d-44cd-9873-76f2d1280e71"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bcd34832-e514-4a6c-9c1e-0af7bae14696", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Copy of Set ECM default properties", "tWComponentName": "SubProcess", "tWComponentId": "3012.5e8d5074-eb91-4bb9-b0be-ed7f2b9b3d0d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b8", "versionId": "0debe7a2-b7e2-4cd1-9130-ec733670457b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5e8d5074-eb91-4bb9-b0be-ed7f2b9b3d0d", "attachedProcessRef": "/1.2d3ab562-82df-48a5-9de7-f5d964218191", "guid": "1b331fb3-75c5-4516-b00f-63472fd5e19f", "versionId": "74f2a340-fa2b-4a73-80c6-ae605ae0e471"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1e3d2772-6793-4cb1-a62e-54de905566f2", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Set ECM default properties", "tWComponentName": "SubProcess", "tWComponentId": "3012.fb7defdd-5603-448a-aac0-ffdf029eddff", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b2", "versionId": "11f61a6c-d78c-43b2-8e18-c0b668c0570a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.fb7defdd-5603-448a-aac0-ffdf029eddff", "attachedProcessRef": "/1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "guid": "7798bcbe-4894-4bc5-b0f6-601228acb33d", "versionId": "4b1457e6-7f83-4a39-993e-1cec6aa0841e"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0c491680-083e-4ac5-aa6a-63384b069d01", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Get Charges Completed", "tWComponentName": "SubProcess", "tWComponentId": "3012.3e610257-74c2-4fa6-a74c-8a717deebdc1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b3", "versionId": "230b93ed-07a7-4072-9334-b2356ed75b6d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.3e610257-74c2-4fa6-a74c-8a717deebdc1", "attachedProcessRef": "/1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "guid": "14063b5a-076a-439d-9265-b1e0c4fe10de", "versionId": "d78075b7-7ba5-41b5-b656-8e25e6250d57"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.62723cde-1c43-48bf-b898-cde90ae6d585", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Update History", "tWComponentName": "SubProcess", "tWComponentId": "3012.f0ff779f-3194-4ac8-b78d-41f1bc36b157", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61ae", "versionId": "3fc0a9a3-21ce-4439-9dcd-7add49ff211c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.f0ff779f-3194-4ac8-b78d-41f1bc36b157", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "d3bea778-e333-4f19-9334-e6b0d8e744ed", "versionId": "bc3134fe-668d-45fd-b07e-8fc650e64270"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.028fbf98-210b-4803-b91f-7b9d3bf053a6", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.ce8f4b65-b883-4ec3-be4b-19635d1caf76", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b0", "versionId": "538278ca-608a-46f3-927b-2c1c391f8906", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.ce8f4b65-b883-4ec3-be4b-19635d1caf76", "haltProcess": "false", "guid": "066c5d8c-2466-4f9a-8fc4-b035d906ae81", "versionId": "e678216e-559e-4cf2-b975-31e7b05439bd"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7b84ebd2-b426-417d-922e-fd3f759ce5a8", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.dce8bdeb-e405-4783-8ae1-919e29b11342", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61ad", "versionId": "71c61742-2a24-467e-8b33-a7ba4816869d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c1787180-2088-406b-8072-bc0aba3b30ae", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Retrieve Product Code", "tWComponentName": "SubProcess", "tWComponentId": "3012.25f139cf-5c8c-4e5c-b43c-48f1ac415703", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61ac", "versionId": "7f1e2802-41d5-4eb0-b16f-2c4d6b08437d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.25f139cf-5c8c-4e5c-b43c-48f1ac415703", "attachedProcessRef": "/1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "guid": "cd659c05-6988-4fdd-9cb9-e7597a03085f", "versionId": "d62b3f21-1bce-4596-9f0a-5605e14ca811"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Get Customer and Party Account List", "tWComponentName": "SubProcess", "tWComponentId": "3012.c3dcee81-da69-4674-9b14-30ac33bbcaf4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61af", "versionId": "890ff417-cffb-4cde-bca5-a175a01fc9c0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.c3dcee81-da69-4674-9b14-30ac33bbcaf4", "attachedProcessRef": "/1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "guid": "0e9b0bc3-7625-47ef-85af-084d239eaad6", "versionId": "1f692b72-2307-489d-b005-a802855982c2"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.32f042c7-4da3-463c-ac2f-8a2d79da9463", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Retrieve Request Number", "tWComponentName": "SubProcess", "tWComponentId": "3012.91b94e00-d6e5-497a-8699-2f95ea177168", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b1", "versionId": "92491f70-ed86-4000-afbd-c515ecdcdde1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.91b94e00-d6e5-497a-8699-2f95ea177168", "attachedProcessRef": "/1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "guid": "1fa55c25-c678-41eb-bd92-670f6fa52d8c", "versionId": "4facf601-538f-4d54-941e-73ffb797f03d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Get Customer Account Details", "tWComponentName": "SubProcess", "tWComponentId": "3012.cd89a62c-9b8c-4c93-ab79-949049d8abbe", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b4", "versionId": "a9267e7f-cb5f-4316-8828-6e5b5782bba2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.cd89a62c-9b8c-4c93-ab79-949049d8abbe", "attachedProcessRef": "/1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285", "guid": "63a40246-f01b-42f9-8cf9-b9b410199df0", "versionId": "0d5a0d73-8642-4acd-8235-27fcc8c3289d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Create ECM Folder", "tWComponentName": "SubProcess", "tWComponentId": "3012.9fd6ff60-ac4e-4d56-85d6-00d0430867ff", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61ab", "versionId": "ac925cff-3ff5-4532-a5ba-a8104d8e80fa", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.9fd6ff60-ac4e-4d56-85d6-00d0430867ff", "attachedProcessRef": "/1.46b984a3-b4ad-405a-abd3-8631f907efe4", "guid": "46812fb9-4af7-4f45-b928-09b229ee1e46", "versionId": "92e08e1f-2140-4471-9a3a-fc84d438a5a8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ad93dc50-07e4-45ca-a21a-817f396172da", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Audit Collection Data", "tWComponentName": "SubProcess", "tWComponentId": "3012.4d483992-33e3-40e1-8398-4a8b1664c70a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b6", "versionId": "af798659-7160-4265-83d5-38122a6bba13", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4d483992-33e3-40e1-8398-4a8b1664c70a", "attachedProcessRef": "/1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "guid": "c5644b14-d63f-4732-b8bd-f6c60cc7c234", "versionId": "a5333b69-**************-a4284f6395fc"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.462404fa-2cce-4c58-b7e8-3240a943549d", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "name": "Get Debited Nostro Vostro Account", "tWComponentName": "SubProcess", "tWComponentId": "3012.ce02587d-46b5-41fe-9f1a-b2af7f1ede68", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bebf9775e:61b5", "versionId": "c69a0f28-8453-43bb-bc0b-122932c3ac68", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.ce02587d-46b5-41fe-9f1a-b2af7f1ede68", "attachedProcessRef": "/1.672c16cb-04d7-4e67-a904-779d9009e1ce", "guid": "5c458925-d097-4583-ae7e-5b27acac852d", "versionId": "c082e25f-bc54-48be-90a3-7c7d8a76e5be"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.bddc6594-c686-442b-a4e9-a51802364f34", "epvId": "/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "75594676-c38b-49c5-b0b3-f36e4ae6ab2f", "versionId": "0becf694-7e46-477b-960a-d24b5d17a038"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.f73093c9-ef54-4785-a29d-a2d3d26a90a2", "epvId": "/21.93c5c002-7ac4-4283-83ee-63b8662f9223", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "ea25a5ed-1f71-4d65-8830-25e07647ef62", "versionId": "0db78a64-dfd8-419a-b048-78f4f08926fd"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.f530fa60-a3aa-49e0-bb01-317fa74d4261", "epvId": "/21.062854b5-6513-4da8-84ab-0126f90e550d", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "0bf62e1a-c11c-4566-81f1-abe6a36d25b6", "versionId": "4464db1f-e729-4bff-9c05-9a2e7a157522"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.0fb21ae9-e6ff-443d-a9ef-8fe6217d4fc0", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "2cf26fd0-da5b-4cf3-84a7-5bd9ef9ffd2c", "versionId": "4f8a967f-3c6e-468e-baec-a650969efa4b"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.253cab19-ae26-4c83-9b8a-2e3aad3346db", "epvId": "/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "9ae5ecce-a93d-4296-8f34-6445c7ab2e82", "versionId": "ae76e406-93f3-478d-ac4c-9bbb428158bf"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.af9a5cff-f0f7-47a5-bb4d-c81daa9edc50", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "b8570074-c91b-4e11-a672-d4b187ae2ce0", "versionId": "f9d227dc-b609-4e1e-8779-ef880cd70db7"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.34ce868f-1ba3-4be5-a982-fa08b828d532", "epvId": "/21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "3c7a1ff2-5c2a-4d3c-aa1b-773f26306f35", "versionId": "fd22479a-cbc6-4963-adc8-65de04bd57b1"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.c70f3620-df82-4410-b826-d97f6760b8f1", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "guid": "e8fdf3ff-98b9-43d7-b69a-9309e084f402", "versionId": "47cd1598-afa7-41e2-82af-573db718d31a"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "70d78878-0f14-4743-ae54-4c3e756bd09f", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "Col01 - test", "id": "1.d5923909-74e7-467b-afb5-eef943fb9698", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "6df217cd-87ba-454d-861d-27274fb8952e", "ns16:startEvent": {"name": "Start", "id": "a3015b6c-0669-49d2-bc30-5aafe210ef21", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "-71", "y": "199", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:default": "2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6"}, "ns16:outgoing": "2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6"}, "ns3:formTask": {"name": "Coach", "id": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "ns16:extensionElements": {"ns3:validationStayOnPagePaths": "okbutton", "ns13:nodeVisualInfo": {"x": "760", "y": "177", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": "tw.local.odcRequest.ContractLiquidation.liqCurrency = \"USD\"\r\r\ntw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 20000;\r\r\n\r\r\n//if(tw.local.odcRequest.ContractLiquidation.liqCurrency== null || tw.local.odcRequest.ContractLiquidation.liqCurrency == \"\") \r\r\n//\ttw.local.odcRequest.ContractLiquidation.liqCurrency= \"USD\";\r\r\n//\t\r\r\n\t"}, "ns16:incoming": ["2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6", "2027.43b953d0-b231-40c0-9270-0d66179a25f2", "2027.49d50c9f-44fc-483c-9b41-ee6096df92a4", "2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba", "2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7", "2027.145b023e-4308-4e49-8ae5-2150277f3c35", "2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c", "2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51"], "ns16:outgoing": ["2027.c783ffa8-30b8-43a2-b551-bc7251648820", "2027.2b247745-5401-419b-a4b0-32493f674d7a", "2027.64d54f87-102e-4a1c-be15-8eea624e779a"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "77b286bd-3738-49e3-8c24-11f97411a1fe", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "97e1a2bf-7518-4b2d-86fd-49a64596da75", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "3de82d5d-d1bb-4b53-8d59-8ac22baef4ee", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "06c8e540-e5dc-4f91-896d-1a9407110497", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "df201b11-8810-4f02-8d38-c75474141602", "ns19:optionName": "terminateReasonVIS", "ns19:value": "NONE"}, {"ns19:id": "a269fc56-5d1e-4316-85de-067553ef3196", "ns19:optionName": "errorPanelVIS", "ns19:value": "tw.local.errorMessageVis", "ns19:valueType": "dynamic"}, {"ns19:id": "0f789f32-ff1a-4347-890d-42b22c7892f4", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "5fa863b5-7bf4-4210-8d18-347b03f94cde", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "c3994762-d884-480e-8c1c-4d5bca764c34", "ns19:optionName": "complianceApprovalVis", "ns19:value": "NONE"}, {"ns19:id": "3ced2ef0-d5a2-4ec9-803b-82b20b2e66dd", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "38d6d627-94c0-4831-8c42-2b008b046c67", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "2d4f7514-3c2e-4e83-8e23-539e50a54965", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "3398b5f5-fd3a-4f3d-84f8-5b94be63ac8f", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "1606c373-1cfb-453e-8652-425992d67950", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "None"}, {"ns19:id": "7140178b-5f69-4b79-80b6-2c5d72698c5b", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "None"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "38aa82d9-e867-4713-8baa-0c3620981e06", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "9181030c-bbfb-4bb2-80d3-b075fb0396a0", "ns19:layoutItemId": "Tab_Section1", "ns19:configData": [{"ns19:id": "ea1eb403-5a6a-4696-86fe-0f8d21c9441a", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "a833f7fe-412d-4991-814a-beb023555da6", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "1556a30e-990f-415e-8d6c-6f02596a5d9a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "5f7d81ad-8ce2-4bb5-8d2a-df9ddf7a469d", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "3a917dbe-ea3c-41a8-8c4e-feb95a130e78", "ns19:layoutItemId": "Contract_Liquidation1", "ns19:configData": [{"ns19:id": "bc906cd5-1b43-49fb-899d-cabca5acfa8e", "ns19:optionName": "@label", "ns19:value": "Contract Liquidation"}, {"ns19:id": "f43e7550-20ab-40e2-8b6f-555fbc7a7fcf", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "97b8c6a1-4a4c-46c3-8636-71178065fd38", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "f07b0a4c-42ad-4665-8211-9f61d8014f31", "ns19:optionName": "cif", "ns19:value": "tw.local.odcRequest.Parties.partyTypes.partyCIF", "ns19:valueType": "dynamic"}, {"ns19:id": "6738b37a-96d9-4e53-8486-7c8a0308ee42", "ns19:optionName": "isGlAccount", "ns19:value": "tw.local.isGlAccount", "ns19:valueType": "dynamic"}, {"ns19:id": "5b0023d9-26ad-40fd-84fe-0092dec7a799", "ns19:optionName": "customerAccounts", "ns19:value": "tw.local.odcRequest.customerAndPartyAccountList[]", "ns19:valueType": "dynamic"}, {"ns19:id": "44e61d5d-d04f-4494-8276-a96d7c26131c", "ns19:optionName": "glAccountVerified", "ns19:value": "tw.local.glAccountVerified", "ns19:valueType": "dynamic"}, {"ns19:id": "8e3ffba0-7e90-4319-89d2-2cc47766b791", "ns19:optionName": "<PERSON><PERSON><PERSON><PERSON>", "ns19:value": "tw.local.isChecker", "ns19:valueType": "dynamic"}, {"ns19:id": "b31c7765-e06c-4542-8305-44c03f4abbd8", "ns19:optionName": "exchangeRate", "ns19:value": "tw.local.exchangeRate", "ns19:valueType": "dynamic"}, {"ns19:id": "cab4af01-c500-43cc-8197-62dc3f78f537", "ns19:optionName": "contractLiquidatedMSG", "ns19:value": "tw.local.contractLiquidatedMSG", "ns19:valueType": "dynamic"}, {"ns19:id": "bb485631-6580-42c2-883c-204d4d28a9a1", "ns19:optionName": "verifyGLMsg", "ns19:value": "tw.local.verifyGLMsg", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "ns19:binding": "tw.local.odcRequest"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "8a678e26-**************-83943d6c7afa", "ns19:layoutItemId": "Charges_And_Commissions_CV_21", "ns19:configData": [{"ns19:id": "79f54cfb-3f33-4ef7-858f-a38674262f9d", "ns19:optionName": "@label", "ns19:value": "Charges And Commissions"}, {"ns19:id": "911958c1-a887-449b-8755-389d4e7d2123", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "e4eea5bb-2cfa-4d0c-8718-12fe36d5295a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "591022a9-e70d-42e0-81cb-acbfc61aa581", "ns19:optionName": "processType", "ns19:value": "LIQD"}, {"ns19:id": "4dacacd4-f2d2-40f2-8d6f-bc8448c0ca8e", "ns19:optionName": "amountCollectableByNBE", "ns19:value": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "6fad35b0-6227-4c7c-8f07-630557071f74", "ns19:optionName": "exRate", "ns19:value": "tw.local.rate", "ns19:valueType": "dynamic"}, {"ns19:id": "2a3fa55d-c346-4a32-8abd-3c27dd5c51c9", "ns19:optionName": "chargesCustomerAccountList", "ns19:value": "tw.local.odcRequest.customerAndPartyAccountList[]", "ns19:valueType": "dynamic"}, {"ns19:id": "8213ce95-714a-4614-8205-b04208bb4157", "ns19:optionName": "calculatedChangeAmnt", "ns19:value": "tw.local.calculatedChangeAmnt", "ns19:valueType": "dynamic"}, {"ns19:id": "c0e9dd94-85a4-426d-88f6-96dbb4bbe633", "ns19:optionName": "index", "ns19:value": "tw.local.selectedIndex", "ns19:valueType": "dynamic"}, {"ns19:id": "fa07b132-2d3f-4b7d-898e-6624c941db4d", "ns19:optionName": "<PERSON><PERSON><PERSON><PERSON>", "ns19:value": "tw.local.isChecker", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "ns19:binding": "tw.local.odcRequest.ChargesAndCommissions[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "532735ae-dee4-4bf2-86e4-a116bfe2eecf", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "9b21a244-7684-40a4-87df-e49428d013f3", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "7acd67eb-9a60-4b90-88cb-543066e77808", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "0293df39-1587-46e3-83d2-4a0918846c3d", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "2907c973-dfb0-4c89-8c91-cfb4abf56cb1", "ns19:optionName": "basicDetailsVIS", "ns19:value": "READONLY"}, {"ns19:id": "f1fd81f3-ec3d-4c01-80ce-3d1a31c5409a", "ns19:optionName": "requestVIS", "ns19:value": "READONLY"}, {"ns19:id": "9b13236c-9c9a-46f3-8066-7b2f8bb9c70e", "ns19:optionName": "requestTypeVIS", "ns19:value": "READONLY"}, {"ns19:id": "fcf313ce-e0ff-452f-87bf-1df7e6fc97c3", "ns19:optionName": "parentRequestNoVis", "ns19:value": "NONE", "ns19:valueType": "static"}, {"ns19:id": "806c40a1-9a12-4b34-8078-7b34d3ab2993", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "READONLY"}, {"ns19:id": "cc14c2c1-a593-4f82-8b94-7d24629d0279", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "NONE"}, {"ns19:id": "cc5fb8aa-9f2a-4e63-859f-210dba97a3e0", "ns19:optionName": "contractStageVIS", "ns19:value": "NONE", "ns19:valueType": "static"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "8f5f2747-73fd-4c2b-85b0-2f72bc4b2914", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "36b1599e-83de-4212-89c2-db792affb341", "ns19:optionName": "@label", "ns19:value": "Customer Info"}, {"ns19:id": "f0b9d1a7-96ab-4c99-868c-2861681fbb50", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "05b59a76-a5a1-4190-8109-0cc2442c3241", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "d5bd2cf7-cf68-4e98-8bd6-c9a2e72d69c7", "ns19:optionName": "customerInfoVIS", "ns19:value": "READONLY"}, {"ns19:id": "2f279f23-07e4-46f0-8f52-cf2f9dc1e04a", "ns19:optionName": "requestTypeVis", "ns19:value": "READONLY"}, {"ns19:id": "45f8ea33-f85b-47dd-8047-babb2c82c869", "ns19:optionName": "listsVIS", "ns19:value": "READONLY"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "98c22cf5-d006-4ce1-8578-38fb9d4a10f0", "ns19:layoutItemId": "Financial_Details_Branch1", "ns19:configData": [{"ns19:id": "62a2d995-e92f-4c2d-85c8-6ddc773f9bf7", "ns19:optionName": "@label", "ns19:value": "Financial Details - Branch"}, {"ns19:id": "50285e85-d620-4be2-88e6-3a7d6178e41f", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "05584db9-79dc-423b-875e-e097ca606909", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "aedd8df5-a82e-4e3d-8534-7207a23afe1e", "ns19:optionName": "financialDetailsVis", "ns19:value": "READONLY"}, {"ns19:id": "42614bca-fec8-463d-886d-69c9a2a21408", "ns19:optionName": "fcCollectionVis", "ns19:value": "READONLY"}, {"ns19:id": "d2a5acc4-d48e-4922-8ce3-5ef577d5bfbf", "ns19:optionName": "documentAmountVis", "ns19:value": "READONLY"}, {"ns19:id": "236b2450-aa3a-47fd-8e76-1eeea7fa5de7", "ns19:optionName": "currencyVis", "ns19:value": "READONLY"}, {"ns19:id": "2c7d4a6b-3469-49b4-8694-9bbd44d5bf6c", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "d3b50ee4-6fe5-4d99-8cea-cc90a0ffa85f", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "9f4746bb-5f14-4387-8e13-15fcb74c27ac", "ns19:layoutItemId": "Attachment1", "ns19:configData": [{"ns19:id": "078ff71d-c1db-42e2-8b6f-503a29653467", "ns19:optionName": "@label", "ns19:value": "Attachments"}, {"ns19:id": "cb030272-bcda-46bd-8dc0-747419cf6bd1", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "ca345b3c-21b8-45e4-8ec5-404a5efe4563", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "65b7bfc3-08e8-44ef-856f-d370965a4497", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "bf275b39-f339-4c2e-82fa-565dcd7598b9", "ns19:optionName": "canCreate", "ns19:value": "true"}, {"ns19:id": "e8bc6189-9682-4906-83b6-71dba109fd48", "ns19:optionName": "canDelete", "ns19:value": "true"}, {"ns19:id": "2af97230-d7dd-46b8-8ebb-dac7832f2594", "ns19:optionName": "canUpdate", "ns19:value": "true"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "820a42fa-e501-408e-8ec1-0f13dc2649af", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "d2bdd5c0-f21f-4df9-8d4f-703b07f60391", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "4e56f2e9-2267-40d7-8c47-6ac04eafcac1", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "0b11b893-737b-4a6d-8120-413b3b4ade9a", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:endEvent": {"name": "End", "id": "03ae3039-c478-455b-9e33-0258c9aa586f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1975", "y": "199", "width": "24", "height": "44", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.00100c75-cc1c-41f4-a5bf-ef88085b65b2"}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.866cb443-0661-46da-9aeb-0f780bfcd299"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessageVis", "id": "2056.faf760f0-0a4f-4bf6-b946-9c106b55ffcb"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.d57f46e7-535a-4b65-b3ab-d68140590e50", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.screenName = \"\";\r\nautoObject.userRole = \"\";\r\nautoObject.complianceApproval = false;\r\nautoObject.lastStepAction = \"\";\r\nautoObject.subStatus = \"\";\r\nautoObject.requestType = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.375043cb-a161-4c6f-9861-21d74928b756"}, {"itemSubjectRef": "itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2", "isCollection": "false", "name": "ldapUserProfile", "id": "2056.042bf647-73ed-4939-96e1-397087c17e41"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNoVIS", "id": "2056.a6fe8490-2b24-48b7-b057-d78825afe626"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.808a58dc-8db2-49a5-8550-c17175ff2ba9"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.7dd53a94-ac61-477d-add6-9c0e922e8d6a", "ns16:documentation": {"_": "to be deleted ", "textFormat": "text/plain"}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestTypeVIS", "id": "2056.8d124b98-573c-4e76-b668-d7abd7f1d9e1", "ns16:documentation": {"_": "to be deleted ", "textFormat": "text/plain"}}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "glAccountVerified", "id": "2056.ced83887-655c-4132-bedd-b5b98b52bd61"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "accounteePartyCif", "id": "2056.a708b15b-3e17-4ab8-9344-671c515db949"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "code", "id": "2056.2ef1cb2b-c32a-49d8-be07-2c4cfff5ef35", "ns16:documentation": {"_": "to be deleted<div><br /></div><div>001</div>", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"077\"", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "fullPath", "id": "2056.4a461249-8c22-48e6-9ae5-11ec1735e022"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "serviceName", "id": "2056.1510aee6-3025-46de-9c86-58a06e67ce84"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isfound", "id": "2056.1c6118ff-6dad-4417-843c-754202d43388"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "message", "id": "2056.ca330556-90cb-4ea9-a843-7b890c771316"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "ValidationMessage", "id": "2056.2b50c69d-1ead-4a36-9732-86439eb6d40a"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "customerAndPartyCifs", "id": "2056.e1f423f2-f0e9-4b73-9ced-3cc6042b4c8b"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "rate", "id": "2056.9fbc7b37-a14a-4538-a6be-949c03a3984b"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "calculatedChangeAmnt", "id": "2056.25d94a4f-f1c4-481c-90ac-9d1a1086a21a"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "selectedIndex", "id": "2056.ddde124e-fbdc-4f73-98d5-157ddae5f9f2"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "exchangeRate", "id": "2056.0352dcd3-10cf-49e2-82c4-65bec186960a"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "<PERSON><PERSON><PERSON><PERSON>", "id": "2056.a7119a1a-6917-4320-8dc1-5be7ad5cc3f3"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractLiquidatedMSG", "id": "2056.43c64a73-38c2-4931-8e84-03cb1ac06779"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "verifyGLMsg", "id": "2056.32bb146d-a184-4757-8f8d-8cd99307c19a"}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "default": "2027.145b023e-4308-4e49-8ae5-2150277f3c35", "name": "Init", "id": "2025.1d579c44-4b9b-4332-b150-91de7a551a5c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "186", "y": "175", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": ["2027.373cfd94-5694-4bea-b674-21f7cf36a133", "2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6"], "ns16:outgoing": "2027.145b023e-4308-4e49-8ae5-2150277f3c35", "ns16:script": "tw.local.isChecker = false;\r\r\n//---------------------- Init appInfo  \"This object is used in Header CV\"---------------------------------\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\t\r\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\ntw.local.odcRequest.appInfo.requestName = \"ODC collection\";\r\r\ntw.local.odcRequest.appInfo.requestType = tw.local.odcRequest.requestType.name;\r\r\ntw.local.odcRequest.appInfo.status =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.subStatus =\"Initiated\";\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo; //tw.system.processInstance.id;\r\r\n\r\r\n//---------------------------------Init Step log ---------------------------------------------------\t \t \r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step = tw.epv.Col_ScreenNames.ODCCol01; \r\r\n\r\r\n //----------------------------------------------------------------------------------------------\r\r\n//Init actionConditions \"This object used by service (Get Actions by ScreenName 2) \"\r\r\ntw.local.actionConditions={};\r\r\ntw.local.actionConditions.complianceApproval= false;\r\r\ntw.local.actionConditions.lastStepAction= tw.local.odcRequest.stepLog.action;\r\r\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol01;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\t \r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\t\r\r\n\r\r\n//Init liquidation data\r\r\ntw.local.odcRequest.ContractLiquidation.liqCurrency= tw.local.odcRequest.FinancialDetailsBR.currency.value;\r\r\n//init request state \r\r\ntw.local.odcRequest.BasicDetails.requestState=\"undergoing collection\";\r\r\n\r\r\nif(tw.local.odcRequest.isLiquidated == false)\t{\r\r\n\ttw.local.odcRequest.ContractLiquidation= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAmount= {};\r\r\n\ttw.local.odcRequest.ContractLiquidation.creditedAccount.currency={};\r\r\n\t}\r\r\n\t\r\r\ntw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\r\r\ntw.local.odcRequest.BasicDetails.requestType = \ttw.local.odcRequest.requestType.name;\r\r\n\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass={};\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.name= \"Customer Account\";\r\r\ntw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value= \"Customer Account\";\r\r\n\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "default": "2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51", "name": "Validation", "id": "2025.fb195954-bc69-4a3d-9008-49e5f46d3201", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "946", "y": "59", "width": "95", "height": "70", "color": "#95D087"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.c783ffa8-30b8-43a2-b551-bc7251648820", "ns16:outgoing": "2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51", "ns16:script": "//\t debugger;\r\r\ntw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel){\r\r\n    mandatory(tw.local.odcRequest.stepLog.comment, \"tw.local.odcRequest.stepLog.comment\");\r\r\n}\r\r\n\r\r\nif (tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.submitLiq || tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.createLiq) {\r\r\n\r\r\n    validateLiq();\r\r\n}\r\r\n\r\r\n//------------------------------------------------- Contract Liquidation VALIDATION -----------------------------------------\r\r\nfunction validateLiq() {\r\r\n    //debugger;\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.liqAmount, \"tw.local.odcRequest.ContractLiquidation.liqAmount\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditValueDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitValueDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\");\r\r\n    \r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.creditValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.creditValueDate\", \"Invalid date\", \"Credit Value Date should not be less than request date\");\r\r\n    checkDate(tw.local.odcRequest.ContractLiquidation.debitValueDate, tw.local.odcRequest.requestDate, \"tw.local.odcRequest.ContractLiquidation.debitValueDate\", \"Invalid date\", \"Debit Value Date should not be less than request date\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass\");\r\r\n    mandatory(tw.local.odcRequest.ContractLiquidation.debitedAccountNo, \"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\");\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.creditedAccount.accountClass.value == \"GL Account\") {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo\");\r\r\n\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified == false) {\r\r\n            tw.local.errorMessage += \"<li>\" + \"GL Account is not verified.\" + \"</li>\";\r\r\n            tw.system.coachValidation.addValidationError(tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo, \"GL Account is not verified.\");\r\r\n        }\r\r\n\r\r\n        //debugger;\r\r\n        if (!!tw.local.odcRequest.ContractLiquidation.creditedAccount.glAccountNo && tw.local.glAccountVerified) {\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.branchCode\");\r\r\n            mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.currency.value, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.currency\");\r\r\n        }\r\r\n\r\r\n        //Validate OverDraft\r\r\n        if (tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount > tw.local.odcRequest.ContractLiquidation.creditedAccount.balance && tw.local.odcRequest.ContractLiquidation.creditedAccount.isOverDraft) {\r\r\n            addError(\"tw.local.odcRequest.ContractLiquidation.creditedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\")\r\r\n        }\r\r\n    \r\r\n    } else {\r\r\n        mandatory(tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo, \"tw.local.odcRequest.ContractLiquidation.creditedAccount.customerAccountNo\");\r\r\n    }\r\r\n\r\r\n    if (tw.local.odcRequest.ContractLiquidation.liqAmount > tw.local.odcRequest.FinancialDetailsFO.outstandingAmount) {\r\r\n        addError(\"tw.local.odcRequest.ContractLiquidation.liqAmount\", \"Liquidation Amount must be <= ODC Outstanding Amount\");\r\r\n        tw.local.errorMessage += \"<li>\" + \"Liquidation Amount must be less than or equal to ODC Outstanding Amount.\" + \"</li>\";\r\r\n    }\r\r\n\r\r\n    //----------------------------------------------- Charges and Commissions VALIDATION -------------------------------------\t\t\r\r\n    for (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\r\n\r\r\n        //Description - Flat Amount\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            //        mandatory(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\");\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 2, \"Shouldn't be less than 2 character\", \"Change Amount: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\", 14, \"Shouldn't be more than 14 character\", \"Change Amount:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n        //Fixed Rate\r\r\n        } else {\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 || tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be >= 0\");\r\r\n            } else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\r\r\n                validateDecimal2(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", \"Must be Decimal(14,2)\")\r\r\n            }\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 2, \"Shouldn't be less than 2 character\", \"Change Percentage: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].changePercentage, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\", 14, \"Shouldn't be more than 14 character\", \"Change Percentage:\" + \"Shouldn't be more than 14 character\");\r\r\n        }\r\r\n\r\r\n        //skip validation if waiver or changeAmnt < 0\r\r\n        if (tw.local.odcRequest.ChargesAndCommissions[i].waiver == false && tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n\r\r\n            //Validate debited accounts\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            // mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.accountClass\");\r\r\n\r\r\n            //GL Account\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value == \"GL Account\") {\r\r\n\r\r\n                if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\r\n                    addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\", \"GL Account Not Verified\");\r\r\n                }\r\r\n\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.glAccountNo\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.currency.value\");\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.branchCode\");\r\r\n\r\r\n                //Customer Account\r\r\n            } else {\r\r\n                mandatory(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAccount.customerAccountNo\");\r\r\n            }\r\r\n\r\r\n            //DebitedAmount\r\r\n\r\r\n            if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\r\r\n                validateDecimal(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", \"Must be Decimal(16,10)\")\r\r\n            }\r\r\n\r\r\n            // minLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 2, \"Shouldn't be less than 2 character\", \"Negotiated Exchange Rate: Shouldn't be less than 2 character\");\r\r\n            // maxLength(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate, \"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.negotiatedExRate\", 14, \"Shouldn't be more than 14 character\", \"Negotiated Exchange Rate:\" + \"Shouldn't be more than 14 character\");\r\r\n\r\r\n            //Correct Validation but Waiting confirmation on what to do if GL account\r\r\n            if ((tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\r\r\n                tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance) && tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false) {\r\r\n                addError(\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].debitedAmount.amountInAccount\", \"ERROR: Must be <= Account Balance\");\r\r\n            }\r\r\n        }\r\r\n    }\r\r\n}   //end of validation function\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\r\n    tw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n    fromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the date1 is less than date2\r\r\n*\t\r\r\n* EX:checkDate( date, requestDate ,  'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction checkDate(date, requestDate, dateName, controlMessage, validationMessage) {\r\r\n    if (date != null && date != undefined && date < requestDate) {\r\r\n        addError(dateName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field != null && field != undefined && field.length < len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n    if (field.length > len) {\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field, fieldName) {\r\r\n    if (field == null || field == undefined) {\r\r\n        addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n        mandatoryTriggered = true;\r\r\n        return false;\r\r\n    }\r\r\n    else {\r\r\n        switch (typeof field) {\r\r\n            case \"string\":\r\r\n                if (field.trim() != undefined && field.trim() != null && field.trim().length == 0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            case \"number\":\r\r\n                if (field == 0.0) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                if (field < 0) {\r\r\n                    var msg = \"Invalid Value, This field can not be negative value.\";\r\r\n                    addError(fieldName, msg, msg, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n\r\r\n            case \"boolean\":\r\r\n                if (field == false) {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n                break;\r\r\n            default:\r\r\n\r\r\n                // VALIDATE DATE OBJECT\r\r\n                if (field && field.getTime && isFinite(field.getTime())) { }\r\r\n\r\r\n                else {\r\r\n                    addError(fieldName, tw.resource.ValidationMessages.ThisFieldIsMandatory, tw.resource.ValidationMessages.MandatoryFields, true);\r\r\n                    mandatoryTriggered = true;\r\r\n                    return false;\r\r\n                }\r\r\n        }\r\r\n    }\r\r\n    return true;\r\r\n}\r\r\n\r\r\n//Validate Decimal(10,6)\r\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,4}(\\.\\d{1,6})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\n\r\r\n//Validate Decimal(14,2)\r\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\r\n    validationMessage = controlMessage;\r\r\n    // Regular expression pattern for decimal validation\r\r\n    var decimalPattern = /^\\d{1,12}(\\.\\d{1,2})?$/;\r\r\n\r\r\n    // Check if the decimal matches the pattern\r\r\n    if (!decimalPattern.test(field)) {\r\r\n        // Decimal is valid\r\r\n        addError(fieldName, controlMessage, validationMessage);\r\r\n        return false;\r\r\n    } else {\r\r\n        // Decimal is invalid\r\r\n        return true;\r\r\n    }\r\r\n}\r\r\ntw.local.errorMessage != null ? tw.local.errorMessageVis = \"EDITABLE\" : tw.local.errorMessageVis = \"NONE\";\t\t"}, {"scriptFormat": "text/x-javascript", "default": "2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c", "name": "Set Liq Flag", "id": "2025.d199b9f9-4dde-42e0-906e-f6b7577a099b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1070", "y": "276", "width": "95", "height": "70"}}, "ns16:incoming": "2027.663307a2-6be3-4362-82da-e71cb0be1525", "ns16:outgoing": "2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c", "ns16:script": "tw.local.odcRequest.isLiquidated= true;\r\r\ntw.local.odcRequest.stepLog.action=\"\";\r\r\ntw.local.actionConditions.isLiquidated= tw.local.odcRequest.isLiquidated;\r\r\n\r\r\ntw.local.isChecker = true;\r\r\n\r\r\n//location.reload(true);\r\r\n//window.location.reload();\r\r\n"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.1bd9e34e-b12b-4985-b405-608eef0daddd", "name": "Set Attachments list", "id": "2025.b845eb69-1e4d-4b45-bf66-6bc282fc928e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "289", "y": "333", "width": "95", "height": "69"}}, "ns16:outgoing": "2027.1bd9e34e-b12b-4985-b405-608eef0daddd", "ns16:script": "if(!tw.local.odcRequest.attachmentDetails)\r\r\n\ttw.local.odcRequest.attachmentDetails= {};\r\r\n\t\r\r\nif (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {\r\r\n\ttw.local.odcRequest.attachmentDetails.attachment = []; \r\r\n\t\r\r\n}\t\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"Customer Request\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"طلب العميل\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].name = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].description = \"BILL OF EXCHANGE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[1].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].name = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].description = \"Invoice\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[2].arabicName = \"فاتورة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].name = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].description = \"BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[3].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].name = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].description = \"AIRWAY BILL\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[4].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].name = \"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].description =\"TRUCK CONSIGNMENT NOTE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[5].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].name = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].description = \"N/N BILL OF LADING\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[6].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].name = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].description = \"COURIER / POST RECEIPT\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[7].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].name = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].description = \"PACKING LIST\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[8].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].name = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].description = \"CERTIFICATE OF ORIGIN\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[9].arabicName = \"\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].description = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[10].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].name = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].description = \"INSURANCE POLICY / CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[11].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].name = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].description = \"BENEFECIARY DECLARATION\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[12].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].name = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].description = \"NON RADIOACTIVE CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[13].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].name = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].description = \"PHYTOSANITARY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[14].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].name = \"CERTIFICATE OF ANALYSIS\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].description = \"Bill of exchange/draft\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[15].arabicName = \"الكمبيالة\" ;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].name = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].description = \"HEALTH CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[16].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].name = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].description = \"INSPECTION CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[17].arabicName = \"\";;\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].name = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].description = \"WARRANTY CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[18].arabicName = \"\";\r\r\n\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19] = {};\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].name = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].description = \"TEST CERTIFICATE\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[19].arabicName = \"\";\r\r\n\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = {};\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};\r\r\n"}], "ns16:intermediateThrowEvent": [{"name": "Postpone", "id": "2025.185cde63-01a4-4571-8c47-554e7a6a1de7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "803", "y": "304", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6"}, "ns16:incoming": "2027.2b247745-5401-419b-a4b0-32493f674d7a", "ns16:outgoing": "2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page", "id": "2025.5936a9ea-ae2b-45df-8027-69c29a6528d9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1098", "y": "55", "width": "71", "height": "44"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": ""}, "ns16:incoming": "2027.f9539453-1414-4bd4-be3e-5b5a2cd60701", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.949e8a1a-c989-4a31-a14a-df108483fd24", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1939", "y": "272", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.4683f817-3c32-411d-b118-a123ffa86a4c", "ns3:stayOnPageEventDefinition": ""}, {"name": "Copy of Stay on page 1", "id": "2025.527fdaa6-8210-41a6-8f77-72dd1c03ac09", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1693", "y": "279", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a", "ns3:stayOnPageEventDefinition": ""}], "ns16:sequenceFlow": [{"sourceRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "targetRef": "2025.fb195954-bc69-4a3d-9008-49e5f46d3201", "name": "To End", "id": "2027.c783ffa8-30b8-43a2-b551-bc7251648820", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "eb89d296-85ab-4789-8efd-7e3d49c091c4", "ns3:coachEventPath": "DC_Templete1/submit"}}}, {"sourceRef": "2025.fb195954-bc69-4a3d-9008-49e5f46d3201", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "To Valid?", "id": "2027.60a7d4c4-c4f9-409d-9391-cce83b1f3c51", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}}, {"sourceRef": "2025.ef877a71-0e37-49d3-9127-63502f4d4069", "targetRef": "2025.ce2cdb08-0319-4ac0-a07d-6966abcad237", "name": "Yes", "id": "2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.ef877a71-0e37-49d3-9127-63502f4d4069", "targetRef": "2025.5936a9ea-ae2b-45df-8027-69c29a6528d9", "name": "No", "id": "2027.f9539453-1414-4bd4-be3e-5b5a2cd60701", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  !=\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.af01a78b-ba4b-44df-bee8-875288c1ccd2", "targetRef": "2025.62723cde-1c43-48bf-b898-cde90ae6d585", "name": "To End", "id": "2027.e41fd63d-542e-49f4-a3ef-9345679a28c6", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "targetRef": "2025.185cde63-01a4-4571-8c47-554e7a6a1de7", "name": "To Postpone", "id": "2027.2b247745-5401-419b-a4b0-32493f674d7a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "c38ca5cd-ef8e-4820-aebd-4db6926cdbaa", "ns3:coachEventPath": "DC_Templete1/saveState"}}}, {"sourceRef": "2025.185cde63-01a4-4571-8c47-554e7a6a1de7", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "To Coach", "id": "2027.8c51ee99-3b6a-4824-9bb1-bbfca85803d6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.ce2cdb08-0319-4ac0-a07d-6966abcad237", "targetRef": "2025.af01a78b-ba4b-44df-bee8-875288c1ccd2", "name": "No", "id": "2027.e113fb43-7ad2-49ce-a533-8f43259f30c3", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.Col_Actions.createLiq", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.ce2cdb08-0319-4ac0-a07d-6966abcad237", "targetRef": "2025.d199b9f9-4dde-42e0-906e-f6b7577a099b", "name": "Yes", "id": "2027.663307a2-6be3-4362-82da-e71cb0be1525", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.Col_Actions.createLiq", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.b845eb69-1e4d-4b45-bf66-6bc282fc928e", "targetRef": "2025.bcd34832-e514-4a6c-9c1e-0af7bae14696", "name": "To Set ECM default properties", "id": "2027.1bd9e34e-b12b-4985-b405-608eef0daddd", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.1e3d2772-6793-4cb1-a62e-54de905566f2", "targetRef": "2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb", "name": "To Create ECM Folder", "id": "2027.*************-4f1a-84f5-07640da1a7ec", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb", "targetRef": "2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692", "name": "To Coach", "id": "2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.c180fd4c-112c-4218-8691-6f7d496b7c1f", "targetRef": "2025.32f042c7-4da3-463c-ac2f-8a2d79da9463", "name": "NO", "id": "2027.ab69b6dc-6741-41f1-a1b8-75a533256a48", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c180fd4c-112c-4218-8691-6f7d496b7c1f", "targetRef": "2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692", "name": "YES", "id": "2027.eced07db-62f5-4bef-affe-97f152a2a6b0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.isLiquidated", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.62723cde-1c43-48bf-b898-cde90ae6d585", "targetRef": "2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009", "name": "To log history?", "id": "2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009", "targetRef": "03ae3039-c478-455b-9e33-0258c9aa586f", "name": "Yes", "id": "2027.00100c75-cc1c-41f4-a5bf-ef88085b65b2", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009", "targetRef": "2025.949e8a1a-c989-4a31-a14a-df108483fd24", "name": "No", "id": "2027.4683f817-3c32-411d-b118-a123ffa86a4c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage!= null) || (tw.local.errorMessage!= \"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.32f042c7-4da3-463c-ac2f-8a2d79da9463", "targetRef": "2025.1d579c44-4b9b-4332-b150-91de7a551a5c", "name": "To Coach", "id": "2027.373cfd94-5694-4bea-b674-21f7cf36a133", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.ad93dc50-07e4-45ca-a21a-817f396172da", "targetRef": "2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a", "name": "To audited successfully?", "id": "2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a", "targetRef": "2025.527fdaa6-8210-41a6-8f77-72dd1c03ac09", "name": "No", "id": "2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.errorMessage!= null || tw.local.errorMessage!=\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a", "targetRef": "2025.62723cde-1c43-48bf-b898-cde90ae6d585", "name": "To Update History", "id": "2027.9fc452a2-e5ed-439a-95b5-f757aa356cdd", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1d579c44-4b9b-4332-b150-91de7a551a5c", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "To Set ECM default properties", "id": "2027.145b023e-4308-4e49-8ae5-2150277f3c35", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.31168a88-607c-4da6-ab4a-963438a9f04a", "targetRef": "2025.0c491680-083e-4ac5-aa6a-63384b069d01", "name": "yes", "id": "2027.c50dcf04-7330-42a1-86d1-35abdff84012", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.31168a88-607c-4da6-ab4a-963438a9f04a", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "no", "id": "2027.43b953d0-b231-40c0-9270-0d66179a25f2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "rightTop", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.ContractCreation.productCode.value != null && tw.local.odcRequest.ContractCreation.productCode.value != \"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692", "targetRef": "2025.c1787180-2088-406b-8072-bc0aba3b30ae", "name": "To Retrieve Product Code", "id": "2027.a59c07cb-ff79-4db4-a3c0-493525050a99", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c1787180-2088-406b-8072-bc0aba3b30ae", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "To Coach", "id": "2027.49d50c9f-44fc-483c-9b41-ee6096df92a4", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "targetRef": "2025.462404fa-2cce-4c58-b7e8-3240a943549d", "name": "To Get Debited Nostro Vostro Account", "id": "2027.64d54f87-102e-4a1c-be15-8eea624e779a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomLeft", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "34bf2c19-a544-4633-a7a6-3735105cddf0", "ns3:coachEventPath": "Contract_Liquidation1/verify"}}}, {"sourceRef": "2025.462404fa-2cce-4c58-b7e8-3240a943549d", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "To Coach", "id": "2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "rightBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.0c491680-083e-4ac5-aa6a-63384b069d01", "targetRef": "2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5", "name": "To Get Customer and Party Account List", "id": "2027.f15eff18-e5d7-47eb-85f2-b00277b37810", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "To Coach", "id": "2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "leftCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a3015b6c-0669-49d2-bc30-5aafe210ef21", "targetRef": "2025.1d579c44-4b9b-4332-b150-91de7a551a5c", "name": "To Coach", "id": "2027.89d9d1d1-3425-4f91-8e02-c6277950e7c6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "14", "y": "114"}, "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.d199b9f9-4dde-42e0-906e-f6b7577a099b", "targetRef": "2025.df396f09-7309-4b4b-8cbd-d5042a503b1d", "name": "To Coach", "id": "2027.3cf2ae75-2cfe-42a9-81d4-60e34315081c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "leftCenter", "ns13:targetPortLocation": "rightBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}], "ns16:exclusiveGateway": [{"default": "2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e", "name": "Valid?", "id": "2025.ef877a71-0e37-49d3-9127-63502f4d4069", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1085", "y": "95", "width": "32", "height": "32"}}, "ns16:outgoing": ["2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e", "2027.f9539453-1414-4bd4-be3e-5b5a2cd60701"]}, {"default": "2027.e113fb43-7ad2-49ce-a533-8f43259f30c3", "name": "Is Liquidated?", "id": "2025.ce2cdb08-0319-4ac0-a07d-6966abcad237", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1204", "y": "195", "width": "32", "height": "32"}}, "ns16:incoming": "2027.fc6ebbdc-9c56-4509-a90d-3450d1ab746e", "ns16:outgoing": ["2027.e113fb43-7ad2-49ce-a533-8f43259f30c3", "2027.663307a2-6be3-4362-82da-e71cb0be1525"]}, {"default": "2027.ab69b6dc-6741-41f1-a1b8-75a533256a48", "name": "is Liquidated?", "id": "2025.c180fd4c-112c-4218-8691-6f7d496b7c1f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "-39", "y": "196", "width": "32", "height": "32"}}, "ns16:outgoing": ["2027.ab69b6dc-6741-41f1-a1b8-75a533256a48", "2027.eced07db-62f5-4bef-affe-97f152a2a6b0"]}, {"default": "2027.4683f817-3c32-411d-b118-a123ffa86a4c", "name": "log history?", "id": "2025.77916dd2-1501-4d30-8ae9-fd1a9f7cb009", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1872", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de", "ns16:outgoing": ["2027.00100c75-cc1c-41f4-a5bf-ef88085b65b2", "2027.4683f817-3c32-411d-b118-a123ffa86a4c"]}, {"default": "2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a", "gatewayDirection": "Unspecified", "name": "audited successfully?", "id": "2025.06cdfa0b-c4dd-4823-87db-2dbcea1d0c4a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1574", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710", "ns16:outgoing": ["2027.4d4dd668-8c92-461f-8f19-722f9f9afb3a", "2027.9fc452a2-e5ed-439a-95b5-f757aa356cdd"]}, {"default": "2027.43b953d0-b231-40c0-9270-0d66179a25f2", "gatewayDirection": "Unspecified", "name": "Has ProductCode?", "id": "2025.31168a88-607c-4da6-ab4a-963438a9f04a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "772", "y": "57", "width": "32", "height": "32"}}, "ns16:outgoing": ["2027.c50dcf04-7330-42a1-86d1-35abdff84012", "2027.43b953d0-b231-40c0-9270-0d66179a25f2"]}], "ns16:callActivity": [{"calledElement": "1.81656d33-5348-479b-a7af-5631356d9476", "default": "2027.e41fd63d-542e-49f4-a3ef-9345679a28c6", "name": "Set Status And Sub Status", "id": "2025.af01a78b-ba4b-44df-bee8-875288c1ccd2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1351", "y": "177", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": ""}, "ns16:incoming": "2027.e113fb43-7ad2-49ce-a533-8f43259f30c3", "ns16:outgoing": "2027.e41fd63d-542e-49f4-a3ef-9345679a28c6", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.stepLog.action", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.6010a917-3b0f-4c13-8973-51c4eede4cd9", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.stepName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.71a67d77-b802-42cf-8cf2-380a9594a9c2", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.ce1f6bfc-b971-4978-80c8-092f5d40c209", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "default": "2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de", "name": "Update History", "id": "2025.62723cde-1c43-48bf-b898-cde90ae6d585", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1754", "y": "178", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": ["2027.9fc452a2-e5ed-439a-95b5-f757aa356cdd", "2027.e41fd63d-542e-49f4-a3ef-9345679a28c6"], "ns16:outgoing": "2027.6ed68b73-9e4e-497d-85f1-7bf7e68217de", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.epv.userRole.RevAct01", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.a617c560-c740-484e-89de-0931088cdc6c", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.46b984a3-b4ad-405a-abd3-8631f907efe4", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b", "name": "Create ECM Folder", "id": "2025.52ac2b49-b1ff-4a4a-8ff4-55d76100a1cb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "466", "y": "176", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.serviceName=\"Create\"", "ns3:postAssignmentScript": "tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;// tw.local.parentPath\r\r\ntw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;\r\r\n\r\r\nif(!!tw.local.error && tw.local.error.errorText!=null)\r\r\n\ttw.local.errorMessage+= tw.local.error.errorText;"}, "ns16:incoming": "2027.*************-4f1a-84f5-07640da1a7ec", "ns16:outgoing": "2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.4156964b-1c67-40bc-8f62-3804c71cf908", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.e2ce0eed-342c-4942-8214-83e964b550e5", "ns16:assignment": {"ns16:from": {"_": "tw.local.routingDetails.hubCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:sourceRef": "2055.5f955245-0538-4e40-80a6-12f45c3102f3", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.214c7268-80d0-444d-8702-dd0d5462dbe7", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7", "ns16:assignment": {"ns16:to": {"_": "tw.local.fullPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2", "ns16:assignment": {"ns16:to": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.676e3a06-e2cc-4855-84d6-6f82a350500a", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.*************-4f1a-84f5-07640da1a7ec", "name": "Set ECM default properties", "id": "2025.1e3d2772-6793-4cb1-a62e-54de905566f2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "327", "y": "176", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:outgoing": "2027.*************-4f1a-84f5-07640da1a7ec", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.399c7a58-00b5-4451-9813-41c0b9652088", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.c28023fb-b45e-4b63-ae36-97e6df6421bc", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.25394215-074f-4b79-8e84-9a96d32cc83b", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.7d269650-ee48-4101-80db-2807cf921562", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.attachmentDetails", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9"}}}, {"ns16:sourceRef": "2055.0261e8ad-a540-4682-88c5-87dff3eab23c", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.cc6bc8c3-b0c3-4c4a-842d-7f14091d2285", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.a59c07cb-ff79-4db4-a3c0-493525050a99", "name": "Get Customer Account Details", "id": "2025.2ddfcf0c-ce8c-4fef-b3cd-2ee11d3a1692", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "602", "y": "176", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.accounteePartyCif = \"\";\r\r\n\r\r\nif(!!tw.local.odcRequest.Parties && !!tw.local.odcRequest.Parties.partyTypes && tw.local.odcRequest.Parties.partyTypes.partyCIF !=null ) \r\r\n\ttw.local.accounteePartyCif= tw.local.odcRequest.Parties.partyTypes.partyCIF;", "ns3:mode": "InvokeService", "ns3:autoMap": "true", "ns3:postAssignmentScript": ""}, "ns16:incoming": ["2027.e01ebd13-6ec3-4b5b-a9be-7362e1fa983b", "2027.eced07db-62f5-4bef-affe-97f152a2a6b0"], "ns16:outgoing": "2027.a59c07cb-ff79-4db4-a3c0-493525050a99", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.7a0225d0-1ea8-4907-afba-e3a98de88df1", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.d471dcbe-97c5-4911-87c7-7008dadc3a15", "ns16:assignment": {"ns16:from": {"_": "tw.local.accounteePartyCif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.5855796c-8062-4709-8c2a-18f470d6d879", "ns16:assignment": {"ns16:to": {"_": "tw.local.customerAccounts", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"}}}, {"ns16:sourceRef": "2055.a32d29e2-af5d-4f6f-8041-8778849c9737", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.328cd87f-3306-4c20-8923-b7515b1cb782", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "default": "2027.373cfd94-5694-4bea-b674-21f7cf36a133", "name": "Retrieve Request Number", "id": "2025.32f042c7-4da3-463c-ac2f-8a2d79da9463", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "46", "y": "176", "width": "95", "height": "70"}}, "ns16:incoming": "2027.ab69b6dc-6741-41f1-a1b8-75a533256a48", "ns16:outgoing": "2027.373cfd94-5694-4bea-b674-21f7cf36a133", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.e1ca7465-4084-405d-8ea6-ab3f3762de92", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.29fabc80-90b8-4cad-81e3-1c319c6f595a", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.2d3ab562-82df-48a5-9de7-f5d964218191", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Copy of Set ECM default properties", "id": "2025.bcd34832-e514-4a6c-9c1e-0af7bae14696", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "409", "y": "331", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": "2027.1bd9e34e-b12b-4985-b405-608eef0daddd", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.6f216377-34de-4f5e-8ab5-adc2796733ee", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.customerName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d", "ns16:assignment": {"ns16:from": {"_": "tw.system.user.fullName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701", "ns16:assignment": {"ns16:from": {"_": "tw.system.processInstance.id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81cce609-b3fe-4f11-809f-c3a599908595", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.eac829db-66fe-43f5-810c-6faa514533a2"}}}}, {"calledElement": "1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710", "name": "Audit Collection Data", "id": "2025.ad93dc50-07e4-45ca-a21a-817f396172da", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1476", "y": "178", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "\r\r\ntw.local.odcRequest.BasicDetails.requestState=tw.epv.RequestState.Collection ;\r\r\n\r\r\n"}, "ns16:outgoing": "2027.4c5dfb99-c2b3-4520-a563-9ae989a5d710", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.e39bfaa6-c863-41f9-8061-0e371dff89cb", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.isLiquidated", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:targetRef": "2055.5d4b901c-324e-4bea-8f10-e160a656c696", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.OdcCollection.amount", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}}}, {"ns16:targetRef": "2055.b51575f2-8ce0-48d0-8179-71d12e0440e7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.OdcCollection.currency", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.84daf689-5f95-458f-8b7f-d8c08459d4c1", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.20995cf3-6a12-4378-8292-51106389c796", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNature.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestDate", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}}}, {"ns16:targetRef": "2055.85dca7ee-4057-4dcd-878f-b924dff64190", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.328377fd-ccc9-4119-80ca-435deb518aee", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.37b99722-adca-4c0b-8d6d-aa2eeae29994", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b0be0c94-0742-4365-875f-1b01b63caf0c", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.27a871f0-6893-4366-80d9-133f55bffddb", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.ebcd1729-7d20-4759-81b3-e98e9f554767", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.debd9766-ed8e-45c7-8bbb-c471a2567088", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.8d379594-e94f-4a21-8222-396c4ba9b2e1", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.f15eff18-e5d7-47eb-85f2-b00277b37810", "name": "Get Charges Completed", "id": "2025.0c491680-083e-4ac5-aa6a-63384b069d01", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "757", "y": "-68", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:activityAdHocSettings": {"repeatable": "false", "hidden": "false", "triggerType": "Automatic", "option": "Required"}, "ns3:autoMap": "true", "ns3:activityPreconditions": {"triggerType": "NoPreconditions", "documentTriggerMode": "LegacyCase", "matchAll": "true", "sourceFolderReferenceType": "FolderId"}}, "ns16:incoming": "2027.c50dcf04-7330-42a1-86d1-35abdff84012", "ns16:outgoing": "2027.f15eff18-e5d7-47eb-85f2-b00277b37810", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.12683c54-ad5b-4c68-b734-93d1dea93938", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ChargesAndCommissions", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.e3018bad-b453-4bf5-96fd-09a5141cd061"}}}, {"ns16:targetRef": "2055.7d751a23-0e1b-4987-a9bc-41b25f644f54", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ContractCreation.productCode.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.66b62639-4042-49bf-9544-dbb15f00f3be", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.86004a6f-9b72-4f74-a69a-aed7c869a281", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}}}, {"ns16:targetRef": "2055.1001aa15-5c17-4db8-ab30-0263431130b2", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.FinancialDetailsBR.currency.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.ad7e0a45-5cb2-437f-b705-2ff310914291", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.25041969-c52c-4425-8c47-741f31a7fa66", "ns16:assignment": {"ns16:from": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.ChargesAndCommissions", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061"}}}}, {"calledElement": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "default": "2027.49d50c9f-44fc-483c-9b41-ee6096df92a4", "name": "Retrieve Product Code", "id": "2025.c1787180-2088-406b-8072-bc0aba3b30ae", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "603", "y": "290", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "//tw.local.odcRequest.parentRequestNo = \"00104230000170\";\r\r\n//tw.local.odcRequest.requestType.value = tw.epv.RequestType.Collection\r\r\n"}, "ns16:incoming": "2027.a59c07cb-ff79-4db4-a3c0-493525050a99", "ns16:outgoing": "2027.49d50c9f-44fc-483c-9b41-ee6096df92a4", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.739ec1bd-4791-48bc-830c-f00e254474a3", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.ContractCreation.productCode.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.e6db1787-627d-4373-80cc-75c8940bc32d", "ns16:assignment": {"ns16:to": {"_": "tw.local.isfound", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.e1811b7a-e326-4b9c-863e-59e994c41703", "ns16:assignment": {"ns16:to": {"_": "tw.local.message", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.672c16cb-04d7-4e67-a904-779d9009e1ce", "default": "2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba", "name": "Get Debited Nostro Vostro Account", "id": "2025.462404fa-2cce-4c58-b7e8-3240a943549d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "793", "y": "393", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true", "ns3:postAssignmentScript": "if (tw.local.ValidationMessage == \"\" && tw.local.ValidationMessage == null) {\r\r\n\tif (tw.local.errorMessage == \"\" && tw.local.errorMessage == null) {\r\r\n\t\talert(\"Valid\");\r\r\n\t}\r\r\n}else{\r\r\n\t tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.ContractLiquidation.debitedAccountNo\", tw.local.ValidationMessage);\r\r\n}"}, "ns16:incoming": "2027.64d54f87-102e-4a1c-be15-8eea624e779a", "ns16:outgoing": "2027.71c9a2a7-16d8-4fd1-9396-516c2a429eba", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.723db670-6afc-4c61-8bff-61069e97a6df", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ContractLiquidation.debitedAccountNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.a4b64bc1-a6e1-4866-82c6-48a1bd9a6693", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ContractLiquidation.liqCurrency", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.6d466c39-b0d0-4414-8065-f99b230bcf07", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.63de69c3-31f4-45df-8a71-32f0f2f65fd2", "ns16:assignment": {"ns16:to": {"_": "tw.local.ValidationMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.3387da0f-2282-4ae0-8159-9dc014499f8f", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.ContractLiquidation.debitedAccountName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.b92996ee-f375-4dc1-8c83-cfc4a49384a2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7", "name": "Get Customer and Party Account List", "id": "2025.1bf5c87a-f310-4e81-8da7-7b805f990bf5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "755", "y": "-168", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true", "ns3:preAssignmentScript": "tw.local.customerAndPartyCifs = [];\r\r\nvar customerCIF = tw.local.odcRequest.CustomerInfo.cif = \"********\";\r\r\n\r\r\nvar partyCIF = tw.local.odcRequest.Parties.partyTypes.partyId;\r\r\nvar caseInNeedCIF = tw.local.odcRequest.Parties.caseInNeed.partyId;\r\r\n\r\r\n//Build Cifs list with valid values or \"\" as default\r\r\n!!customerCIF ? tw.local.customerAndPartyCifs[0] = customerCIF : tw.local.customerAndPartyCifs[0] = \"\";\r\r\n\r\r\n!!partyCIF ? tw.local.customerAndPartyCifs[1] = partyCIF : tw.local.customerAndPartyCifs[1] = \"\";\r\r\n\r\r\n!!caseInNeedCIF ? tw.local.customerAndPartyCifs[2] = caseInNeedCIF : tw.local.customerAndPartyCifs[2] = \"\";\r\r\n\r\r\n//remove duplicate cifs using SET\r\r\ntw.local.customerAndPartyCifs = [...new Set(tw.local.customerAndPartyCifs)];"}, "ns16:incoming": "2027.f15eff18-e5d7-47eb-85f2-b00277b37810", "ns16:outgoing": "2027.e0cae5eb-647b-4eb1-8a41-ad97c5ea01a7", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.6deafbe1-d811-44ee-9bc0-50e8d88e5518", "ns16:assignment": {"ns16:from": {"_": "tw.local.customerAndPartyCifs", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.5939c4c1-59e5-4771-a2a0-e87ec9676dfe", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.customerAndPartyAccountList", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.b0c377d8-c17f-4d70-9ed0-b60db6773a42"}}}, {"ns16:sourceRef": "2055.5a34f2ce-6f2d-4000-8667-9e8643ed10b5", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns3:htmlHeaderTag": {"id": "9ada33f2-a35e-4e9a-9ccf-0ede4301cadb", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:mobileReady": "true", "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "epvProcessLinkId": "87b4df33-7920-4ce7-86b1-893067e50ef0"}, {"epvId": "21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "epvProcessLinkId": "00fa02af-a828-48c4-89b1-5582241f99cb"}, {"epvId": "21.93c5c002-7ac4-4283-83ee-63b8662f9223", "epvProcessLinkId": "74ed432e-5125-4d4d-8504-d1a1245fb100"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "04161144-e1f9-43bb-8e07-bf171bc54528"}, {"epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "epvProcessLinkId": "e474676d-2c7a-4a4d-84d2-415932aae2f4"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "a86f9fd0-6f96-403d-8b05-bc3945f285e2"}, {"epvId": "21.062854b5-6513-4da8-84ab-0126f90e550d", "epvProcessLinkId": "e3108d03-1589-46fe-8951-79bfb122d51b"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.a759db86-0dc8-41dc-8faf-40ea72941541"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.e5836d7b-ba93-4ddd-b01e-c6e05bb08a5a", "ns16:documentation": {"_": "partyCIF = &quot;********&quot;;", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = {};\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = [];\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = [];\r\nautoObject.FcCollections.listOfAccounts[0] = {};\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = [];\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = {};\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"IAVC\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.Drawer = {};\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = {};\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = {};\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = {};\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = {};\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.Parties.caseInNeed = {};\r\nautoObject.Parties.caseInNeed.partyCIF = \"\";\r\nautoObject.Parties.caseInNeed.partyId = \"\";\r\nautoObject.Parties.caseInNeed.partyName = \"\";\r\nautoObject.Parties.caseInNeed.country = \"\";\r\nautoObject.Parties.caseInNeed.language = \"\";\r\nautoObject.Parties.caseInNeed.refrence = \"\";\r\nautoObject.Parties.caseInNeed.address1 = \"\";\r\nautoObject.Parties.caseInNeed.address2 = \"\";\r\nautoObject.Parties.caseInNeed.address3 = \"\";\r\nautoObject.Parties.caseInNeed.partyType = {};\r\nautoObject.Parties.caseInNeed.partyType.name = \"\";\r\nautoObject.Parties.caseInNeed.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].isGLFound = false;\r\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.debitedAccountName = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"\";\r\nautoObject.templateDocID = \"\";\r\nautoObject.requestID = 0;\r\nautoObject.customerAndPartyAccountList = [];\r\nautoObject.customerAndPartyAccountList[0] = {};\r\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\r\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\r\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\r\nautoObject.customerAndPartyAccountList[0].frozen = false;\r\nautoObject.customerAndPartyAccountList[0].dormant = false;\r\nautoObject.customerAndPartyAccountList[0].noDebit = false;\r\nautoObject.customerAndPartyAccountList[0].noCredit = false;\r\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\r\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\r\nautoObject.tradeFoComment = \"\";\r\nautoObject.exeHubMkrComment = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "routingDetails", "itemSubjectRef": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "isCollection": "false", "id": "2055.950b44e5-147a-4d6f-a8a2-8af9e91270ae", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.hubCode = \"\";\r\nautoObject.branchCode = \"\";\r\nautoObject.initiatorUser = \"\";\r\nautoObject.branchName = \"\";\r\nautoObject.hubName = \"\";\r\nautoObject.branchSeq = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.fb3d43b4-2150-48d8-bc54-cbf63db6784a"}], "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.9e8b85ce-c7d3-4373-88d5-2c6eddf271fa"}, {"name": "customerAccounts", "itemSubjectRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isCollection": "true", "id": "2055.599ff5ce-111f-4579-ab02-26f39f7be4ef"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.ded500fb-620c-43b5-9914-95ad1aa8581c"}], "ns16:inputSet": {"id": "6d47d229-6af0-4a82-aa74-6c6b8e4e1303"}, "ns16:outputSet": {"id": "********-c33c-4a43-aa40-61fccf28b856"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.35d550bb-46d2-4feb-8645-280d19dbb9c5", "processId": "1.d5923909-74e7-467b-afb5-eef943fb9698", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.7b84ebd2-b426-417d-922e-fd3f759ce5a8", "2025.7b84ebd2-b426-417d-922e-fd3f759ce5a8"], "endStateId": "Out", "toProcessItemId": ["2025.028fbf98-210b-4803-b91f-7b9d3bf053a6", "2025.028fbf98-210b-4803-b91f-7b9d3bf053a6"], "guid": "12f394ee-1aef-4354-8135-2e3acd83224c", "versionId": "45500a6a-c221-4d5c-ae7b-e48d0cb3a883", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}