{"id": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "versionId": "2adb820e-e4d2-4b13-812b-a734fd7d3c1e", "name": "Send Escalation Mail", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "Send Escalation Mail", "lastModified": "1700643350556", "lastModifiedBy": "so<PERSON>ia", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.9d37e7cd-f663-483f-8a5a-e7731f5d4b39", "2025.9d37e7cd-f663-483f-8a5a-e7731f5d4b39"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.657aa980-91ac-4921-87a0-0a8611380c6e", "2025.657aa980-91ac-4921-87a0-0a8611380c6e"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": "Escalation mail service: <div>   this service is running when the task has delay to be done </div>", "guid": "guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:70d2", "versionId": "2adb820e-e4d2-4b13-812b-a734fd7d3c1e", "dependencySummary": "<dependencySummary id=\"bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:64a2\" />", "jsonData": "{\"rootElement\":[{\"artifact\":[{\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":267,\"x\":73,\"y\":258,\"declaredType\":\"TNodeVisualInfo\",\"height\":51}]},\"declaredType\":\"textAnnotation\",\"text\":{\"content\":[\"This service is sending mail to the manager of the last overdue activities\"]},\"id\":\"d03a0a49-59cd-4e7a-8af0-d1c641e844e5\",\"textFormat\":\"text\\/plain\"}],\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.106b2c38-6da0-47f0-84c5-3bf2a9961681\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":245,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"********-871a-4db7-8ec4-0bf694ae48fa\"},{\"incoming\":[\"8421cded-560a-403b-88e3-5f75ae14cd21\",\"7ef1ebdb-ab6d-4ecc-8a41-72bfe3ac6d85\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":860,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:70d4\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"b6af4499-6047-4684-89c2-24be85d232d8\"},{\"targetRef\":\"9d37e7cd-f663-483f-8a5a-e7731f5d4b39\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Init Data & Subject\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.106b2c38-6da0-47f0-84c5-3bf2a9961681\",\"sourceRef\":\"********-871a-4db7-8ec4-0bf694ae48fa\"},{\"startQuantity\":1,\"outgoing\":[\"a2d8440f-e555-48b1-81d5-f58ce682a504\"],\"incoming\":[\"2027.106b2c38-6da0-47f0-84c5-3bf2a9961681\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":326,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Init Data & Subject\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"9d37e7cd-f663-483f-8a5a-e7731f5d4b39\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.activityName= tw.system.findTaskByID(tw.local.taskId).processActivityName;\\r\\ntw.local.receivedDate = String(tw.system.findTaskByID(tw.local.taskId).activationTime.formatDate(\\\"short\\\"));\\r\\ntw.local.dueDate= String(tw.system.findTaskByID(tw.local.taskId).dueDate.formatDate(\\\"short\\\"));\\r\\ntw.local.owner = tw.system.findTaskByID(tw.local.taskId).owner;\\r\\n\\r\\n\\/\\/Set Mail Subject\\r\\ntw.local.subject = \\\"ODC Request No. \\\"+tw.local.odcRequest.appInfo.instanceID\\r\\n\\t\\t\\t+\\\" for Customer \\\"+tw.local.odcRequest.CustomerInfo.customerName\\r\\n\\t\\t\\t+\\\" is Past Due \\\"\\r\\n\\t\\t\\t+\\\"\\u0637\\u0644\\u0628 \\u0627\\u0644\\u062a\\u062d\\u0635\\u064a\\u0644 \\u0627\\u0644\\u0645\\u0633\\u062a\\u0646\\u062f\\u0649 \\u062a\\u0635\\u062f\\u064a\\u0631 \\u0631\\u0642\\u0645 \\\"+tw.local.odcRequest.appInfo.instanceID\\r\\n\\t\\t\\t+\\\" \\u0644\\u0644\\u0639\\u0645\\u064a\\u0644 \\\"+tw.local.odcRequest.CustomerInfo.customerName\\r\\n\\t\\t\\t+\\\" \\u0642\\u062f \\u062a\\u062e\\u0637\\u0649 \\u0627\\u0644\\u0648\\u0642\\u062a \\u0627\\u0644\\u0645\\u0639\\u064a\\u0646 \\u0644\\u0644\\u0645\\u0647\\u0645\\u0629\\\";\\r\\n\\r\\n\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"8421cded-560a-403b-88e3-5f75ae14cd21\"],\"incoming\":[\"f6b08286-e1dd-41b8-85e2-9fa5a2f3570d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":731,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Send Mail\",\"dataInputAssociation\":[{\"targetRef\":\"2055.a7c74b41-811f-4581-94ef-69a84c74eb84\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailTo\"]}}]},{\"targetRef\":\"2055.ae81d59d-2fde-4526-9476-d0598d6e8472\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.subject\"]}}]},{\"targetRef\":\"2055.6d9bd911-88b8-4ea3-8823-421a4f690290\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.msgBody\"]}}]},{\"targetRef\":\"2055.1da05789-2131-46bc-aacf-34d84ca37def\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.mailDebugMode\"]}}]},{\"targetRef\":\"2055.20348cf5-023e-4d3a-826e-5b92143ec224\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]},{\"targetRef\":\"2055.268afc2e-a651-49ef-8704-9a6ff22065c6\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"aa6564df-48df-4c67-8648-e6d51effb810\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.ec26c46c-d70b-4881-98d8-40e694dd7362\"]}],\"calledElement\":\"1.338e9f4d-8538-4ceb-a155-c288604435d4\"},{\"targetRef\":\"b6af4499-6047-4684-89c2-24be85d232d8\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:e30c377f7882db6a:324bd248:186d6576310:-2970\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"8421cded-560a-403b-88e3-5f75ae14cd21\",\"sourceRef\":\"aa6564df-48df-4c67-8648-e6d51effb810\"},{\"targetRef\":\"caed069f-8383-4d67-8b17-7e442ecedd8c\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Set Mail to\",\"declaredType\":\"sequenceFlow\",\"id\":\"a2d8440f-e555-48b1-81d5-f58ce682a504\",\"sourceRef\":\"9d37e7cd-f663-483f-8a5a-e7731f5d4b39\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"mailDebugMode\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9e486eb4-075e-4e6f-8c63-85eaf1dc8335\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"msgBody\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7df99c67-05a0-43d2-83bc-c0179babdddb\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"subject\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2a2899da-41c3-4af5-800e-87773f32f75d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.89854078-9ec2-457d-85b7-bb85f419ed7e\"},{\"startQuantity\":1,\"outgoing\":[\"f6b08286-e1dd-41b8-85e2-9fa5a2f3570d\"],\"incoming\":[\"06795a87-b7b0-4d3e-8774-030d0cccab18\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":611,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Mail Body\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"e1dd77f5-d2f0-4883-8e82-ca9bcb766ece\",\"scriptFormat\":\"text\\/plain\",\"script\":{\"content\":[\"tw.local.msgBody\\n<html dir=\\\"ltl\\\" lang=\\\"en\\\">\\r\\n\\t<p>Dear Sir \\/ Madam\\t\\r\\n\\t<\\/br>\\t\\r\\n\\t<p>Kindly be informed that the Request\\r\\n\\t<#=tw.local.odcRequest.BasicDetails.requestType #>\\r\\n\\t with request number\\r\\n\\t <#=tw.local.odcRequest.appInfo.instanceID #>\\r\\n\\t  related to the customer\\r\\n\\t  <#=tw.local.odcRequest.CustomerInfo.customerName #>\\r\\n\\t  is now past due and below are the request details:<\\/p>\\r\\n\\t<\\/p>\\r\\n\\t<ul>\\r\\n\\t\\t<li><b>Activity Name:<\\/b><em> <#=tw.local.activityName#><\\/em><\\/li>\\r\\n\\t\\t<li><b>Participant:<\\/b><em> <#=tw.local.owner#><\\/em><\\/li>\\r\\n\\t\\t<li><b>Received Date:<\\/b><em> <#=tw.local.receivedDate#><\\/em><\\/li>\\r\\n\\t\\t<li><b>Request Status:<\\/b><em> <#=tw.local.odcRequest.appInfo.status#><\\/em><\\/li>\\r\\n\\t\\t<li><b>Request Sub-status:<\\/b><em> <#=tw.local.odcRequest.appInfo.subStatus#><\\/em><\\/li>\\r\\n\\t\\t<li><b>Due Date:<\\/b><em> <#=tw.local.dueDate#><\\/em><\\/li>  \\r\\n\\t<\\/ul>\\t\\t\\t\\r\\n\\t<b  style=\\\"color:red;\\\">Please do not reply to this message.  This is an automatically generated email.<\\/b>\\r\\n\\r\\n\\t<\\/br>\\r\\n\\t<p dir=\\\"rtl\\\" lang=\\\"ar\\\">\\t\\r\\n\\t\\u0627\\u0644\\u0633\\u064a\\u062f \\/ \\u0627\\u0644\\u0633\\u064a\\u062f\\u0629 <\\/br><\\/br>\\r\\n\\t\\u0628\\u0631\\u062c\\u0627\\u0621 \\u0627\\u0644\\u0639\\u0644\\u0645 \\u0623\\u0646  \\u0637\\u0644\\u0628  <#=tw.local.odcRequest.BasicDetails.requestType #> \\u0631\\u0642\\u0645   <#=tw.local.odcRequest.appInfo.instanceID #>  \\u0644\\u0644\\u0639\\u0645\\u064a\\u0644   <#=tw.local.odcRequest.CustomerInfo.customerName #> \\r\\n\\t\\u0642\\u062f \\u062a\\u062e\\u0637\\u0649 \\u0627\\u0644\\u0648\\u0642\\u062a \\u0627\\u0644\\u0645\\u0639\\u064a\\u0646 \\u0644\\u0644\\u0645\\u0647\\u0645\\u0629 .\\r\\n\\t\\u064a\\u0648\\u062c\\u062f \\u0623\\u062f\\u0646\\u0627\\u0647  \\u0627\\u0644\\u0628\\u064a\\u0627\\u0646\\u0627\\u062a \\u0627\\u0644\\u0623\\u0633\\u0627\\u0633\\u064a\\u0629 \\u0627\\u0644\\u062e\\u0627\\u0635\\u0629 \\u0628\\u0627\\u0644\\u0637\\u0644\\u0628.\\r\\n\\t<ul  dir=\\\"rtl\\\" lang=\\\"ar\\\">\\r\\n\\t\\t<li><b>\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0647\\u0645\\u0629:<\\/b><em> <#=tw.local.activityName#><\\/em><\\/li>\\r\\n\\t\\t<li><b>\\u0627\\u0644\\u0645\\u0633\\u062a\\u062e\\u062f\\u0645:<\\/b><em> <#=tw.local.owner#><\\/em><\\/li>\\r\\n\\t\\t<li><b>\\u062a\\u0627\\u0631\\u064a\\u062e \\u0627\\u0633\\u062a\\u0644\\u0627\\u0645 \\u0627\\u0644\\u0645\\u0647\\u0645\\u0629:<\\/b><em> <#=tw.local.receivedDate#><\\/em><\\/li>\\r\\n\\t\\t<li><b>\\u062d\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0637\\u0644\\u0628:<\\/b><em> <#=tw.local.odcRequest.appInfo.status#><\\/em><\\/li>\\r\\n\\t\\t<li><b>\\u0627\\u0644\\u062d\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0641\\u0631\\u0639\\u064a\\u0629 \\u0644\\u0644\\u0637\\u0644\\u0628:<\\/b><em> <#=tw.local.odcRequest.appInfo.subStatus#><\\/em><\\/li>\\r\\n\\t\\t<li><b>\\u062a\\u0627\\u0631\\u064a\\u062e \\u0627\\u0644\\u0627\\u0646\\u062a\\u0647\\u0627\\u0621 \\u0627\\u0644\\u0645\\u0639\\u064a\\u0646:<\\/b><em> <#=tw.local.dueDate#><\\/em><\\/li>  \\r\\n\\t<\\/ul> \\r\\n\\t<\\/p>\\r\\n\\t<b> <p dir=\\\"rtl\\\"; lang=\\\"ar\\\"; style=\\\"color:red;\\\">\\t \\u0627\\u0644\\u0631\\u062c\\u0627\\u0621 \\u0639\\u062f\\u0645 \\u0627\\u0644\\u0631\\u062f \\u0639\\u0644\\u0649 \\u0647\\u0630\\u0647 \\u0627\\u0644\\u0631\\u0633\\u0627\\u0644\\u0629. \\u0647\\u0630\\u0627 \\u0628\\u0631\\u064a\\u062f \\u0625\\u0644\\u0643\\u062a\\u0631\\u0648\\u0646\\u064a \\u062a\\u0645 \\u0625\\u0646\\u0634\\u0627\\u0624\\u0647 \\u062a\\u0644\\u0642\\u0627\\u0626\\u064a\\u064b\\u0627.  <\\/p><\\/b>\\r\\n\\t<\\/html> \\r\\n\\t \\r\\n\\t \"]}},{\"targetRef\":\"aa6564df-48df-4c67-8648-e6d51effb810\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Service\",\"declaredType\":\"sequenceFlow\",\"id\":\"f6b08286-e1dd-41b8-85e2-9fa5a2f3570d\",\"sourceRef\":\"e1dd77f5-d2f0-4883-8e82-ca9bcb766ece\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"activityName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.0b2d1e4e-754f-40b3-868b-0c717a42d493\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"receivedDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.581c2209-aa2d-493a-86c1-48e9441e18d1\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"dueDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.835f15cc-1e21-4b28-8dfa-ae01b3fc2a60\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"owner\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.07eefca8-7724-4e71-8c48-f8f90091f6f7\"},{\"startQuantity\":1,\"outgoing\":[\"7ef1ebdb-ab6d-4ecc-8a41-72bfe3ac6d85\"],\"incoming\":[\"a2be480d-2f50-4f01-885a-6e4bdc912ab7\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":724,\"y\":154,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exp Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Send Escalation Mail\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"657aa980-91ac-4921-87a0-0a8611380c6e\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"parallelMultiple\":false,\"outgoing\":[\"a2be480d-2f50-4f01-885a-6e4bdc912ab7\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"3aa0ab2a-dc0f-4cc8-8f18-32930ba450f1\",\"otherAttributes\":{\"eventImplId\":\"e6388485-4abe-414e-896a-ae6b6d64d77d\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":635,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"686e3738-91c4-4729-87d1-3fdca84d645c\"},{\"targetRef\":\"657aa980-91ac-4921-87a0-0a8611380c6e\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exp Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"a2be480d-2f50-4f01-885a-6e4bdc912ab7\",\"sourceRef\":\"686e3738-91c4-4729-87d1-3fdca84d645c\"},{\"targetRef\":\"b6af4499-6047-4684-89c2-24be85d232d8\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"7ef1ebdb-ab6d-4ecc-8a41-72bfe3ac6d85\",\"sourceRef\":\"657aa980-91ac-4921-87a0-0a8611380c6e\"},{\"startQuantity\":1,\"outgoing\":[\"06795a87-b7b0-4d3e-8774-030d0cccab18\"],\"incoming\":[\"a2d8440f-e555-48b1-81d5-f58ce682a504\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":463,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.odcRequest.stepLog.step = String(tw.epv.ScreenNames.CACT04);\"]},\"name\":\"Set Mail to\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"caed069f-8383-4d67-8b17-7e442ecedd8c\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/Create Process - Act01\\r\\nif( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT01)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.branch)) )\\r\\n\\ttw.local.mailTo = String(tw.epv.Mails.branchMkr_Mngr);\\r\\nelse if( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT01)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.hub)) )\\r\\n\\ttw.local.mailTo = String(tw.epv.Mails.hubMkr_Mngr);\\r\\n\\r\\n\\/\\/Create Process - Act02\\t\\t\\r\\nelse if( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT02)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.branchComp)) )\\r\\n\\ttw.local.mailTo = tw.epv.Mails.branchComp_Mngr;\\t\\r\\n\\r\\n\\/\\/Create Process - Act03\\t\\r\\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT03))\\r\\n\\ttw.local.mailTo = String(tw.epv.Mails.tradeFO_Mngr);\\r\\n\\t\\r\\n\\/\\/Create Process - Act04\\t\\r\\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT04))\\r\\n\\ttw.local.mailTo = String(tw.epv.Mails.exeHubMkr_Mngr);\\t\\r\\n\\r\\n\\/\\/Create Process - Act05\\t\\r\\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT05))\\r\\n\\ttw.local.mailTo = String(tw.epv.Mails.exeHubChkr_Mngr);\\r\\n\\r\\n\\/\\/Create Process - Act06\\r\\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT06))\\r\\n\\ttw.local.mailTo = String(tw.epv.Mails.exeHubChkr_Mngr);\\t\\t\\r\\n\\r\\n\\/\\/228047\\r\\n\\/\\/228047 cs maker\"]}},{\"targetRef\":\"e1dd77f5-d2f0-4883-8e82-ca9bcb766ece\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set Mail Body\",\"declaredType\":\"sequenceFlow\",\"id\":\"06795a87-b7b0-4d3e-8774-030d0cccab18\",\"sourceRef\":\"caed069f-8383-4d67-8b17-7e442ecedd8c\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"mailTo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.1a7d068d-26e5-441b-8a0d-71ed4cc96540\"}],\"laneSet\":[{\"id\":\"e72cf5af-2fec-4261-8cd1-2b8868762096\",\"lane\":[{\"flowNodeRef\":[\"********-871a-4db7-8ec4-0bf694ae48fa\",\"b6af4499-6047-4684-89c2-24be85d232d8\",\"9d37e7cd-f663-483f-8a5a-e7731f5d4b39\",\"aa6564df-48df-4c67-8648-e6d51effb810\",\"e1dd77f5-d2f0-4883-8e82-ca9bcb766ece\",\"657aa980-91ac-4921-87a0-0a8611380c6e\",\"686e3738-91c4-4729-87d1-3fdca84d645c\",\"d03a0a49-59cd-4e7a-8af0-d1c641e844e5\",\"caed069f-8383-4d67-8b17-7e442ecedd8c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":536}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"********-ba6d-452a-8d91-af214e069933\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"content\":[\"Escalation mail service:\\u00a0<div>\\u00a0 \\u00a0this service is running when the task has delay to be done\\u00a0<\\/div>\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"Send Escalation Mail\",\"declaredType\":\"process\",\"id\":\"1.d7acf968-6740-4e52-b037-2049466eeeb2\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c\",\"epvProcessLinkId\":\"e8739fbb-0966-42d1-818a-13b8f484769f\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"97aabdce-bf0c-464c-82f6-cab44e9d5106\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd\",\"epvProcessLinkId\":\"21f48856-314f-4db4-8449-5aa9da1df55e\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"efe5e073-59c3-4090-8c0c-f0b4a4a77a22\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f\",\"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1\"]}],\"outputSet\":[{}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.Parties.caseInNeed = new tw.object.partyTypes();\\nautoObject.Parties.caseInNeed.partyCIF = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyId = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyName = \\\"\\\";\\nautoObject.Parties.caseInNeed.country = \\\"\\\";\\nautoObject.Parties.caseInNeed.language = \\\"\\\";\\nautoObject.Parties.caseInNeed.refrence = \\\"\\\";\\nautoObject.Parties.caseInNeed.address1 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address2 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address3 = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.caseInNeed.partyType.name = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].isGLFound = false;\\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \\\"\\\";\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.debitedAccountName = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject.folderPath = \\\"\\\";\\nautoObject.templateDocID = \\\"\\\";\\nautoObject.requestID = 0;\\nautoObject.customerAndPartyAccountList = new tw.object.listOf.toolkit.NBEINTT.Account();\\nautoObject.customerAndPartyAccountList[0] = new tw.object.toolkit.NBEINTT.Account();\\nautoObject.customerAndPartyAccountList[0].accountNO = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].currencyCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].branchCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\\nautoObject.customerAndPartyAccountList[0].typeCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerName = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerNo = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].frozen = false;\\nautoObject.customerAndPartyAccountList[0].dormant = false;\\nautoObject.customerAndPartyAccountList[0].noDebit = false;\\nautoObject.customerAndPartyAccountList[0].noCredit = false;\\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountClassCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balanceType = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountStatus = \\\"\\\";\\nautoObject.tradeFoComment = \\\"\\\";\\nautoObject.exeHubMkrComment = \\\"\\\";\\nautoObject.compcheckerComment = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"2078.119755\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"taskId\",\"isCollection\":false,\"id\":\"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.Parties.caseInNeed = new tw.object.partyTypes();\r\nautoObject.Parties.caseInNeed.partyCIF = \"\";\r\nautoObject.Parties.caseInNeed.partyId = \"\";\r\nautoObject.Parties.caseInNeed.partyName = \"\";\r\nautoObject.Parties.caseInNeed.country = \"\";\r\nautoObject.Parties.caseInNeed.language = \"\";\r\nautoObject.Parties.caseInNeed.refrence = \"\";\r\nautoObject.Parties.caseInNeed.address1 = \"\";\r\nautoObject.Parties.caseInNeed.address2 = \"\";\r\nautoObject.Parties.caseInNeed.address3 = \"\";\r\nautoObject.Parties.caseInNeed.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.caseInNeed.partyType.name = \"\";\r\nautoObject.Parties.caseInNeed.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].isGLFound = false;\r\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.debitedAccountName = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"\";\r\nautoObject.templateDocID = \"\";\r\nautoObject.requestID = 0;\r\nautoObject.customerAndPartyAccountList = new tw.object.listOf.toolkit.NBEINTT.Account();\r\nautoObject.customerAndPartyAccountList[0] = new tw.object.toolkit.NBEINTT.Account();\r\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\r\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\r\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\r\nautoObject.customerAndPartyAccountList[0].frozen = false;\r\nautoObject.customerAndPartyAccountList[0].dormant = false;\r\nautoObject.customerAndPartyAccountList[0].noDebit = false;\r\nautoObject.customerAndPartyAccountList[0].noCredit = false;\r\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\r\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\r\nautoObject.tradeFoComment = \"\";\r\nautoObject.exeHubMkrComment = \"\";\r\nautoObject.compcheckerComment = \"\";\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "b5223824-ab8c-481a-90fc-76216357cc03", "versionId": "df43b218-460d-4295-97a7-361c1ca9bf87"}, {"name": "taskId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"2078.119755\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "15daac70-d846-4ca6-b974-31141c45acea", "versionId": "6fe84aeb-269a-4e30-ae67-53eb65614a25"}], "processVariable": [{"name": "mailDebugMode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9e486eb4-075e-4e6f-8c63-85eaf1dc8335", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "20cd8c93-e6b6-459e-ba57-11d2d7cdb82a", "versionId": "71e4a5d5-8ef4-4492-a626-a70af3cc1b7b"}, {"name": "msgBody", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7df99c67-05a0-43d2-83bc-c0179babdddb", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5e0e25f9-610f-4917-8c21-b2a762d055f9", "versionId": "80f70bdd-3b02-469e-9850-1c2f33c7f097"}, {"name": "subject", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2a2899da-41c3-4af5-800e-87773f32f75d", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f17e94a3-a9c5-42d1-a45e-43033b67d29f", "versionId": "3ddbf21e-0af5-445d-ac26-d3b1336a1f86"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.89854078-9ec2-457d-85b7-bb85f419ed7e", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f7f8750c-7d83-4367-88d1-27eccd12c9be", "versionId": "436b5d53-39ad-4857-b88d-c7fe1b98f02b"}, {"name": "activityName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0b2d1e4e-754f-40b3-868b-0c717a42d493", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "de063b52-eb5e-4eff-b66c-10bea0d56290", "versionId": "d8c730f0-b9d5-4d26-a9c7-0a17b5593935"}, {"name": "receivedDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.581c2209-aa2d-493a-86c1-48e9441e18d1", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "98b82e36-0cac-4bf3-a39e-77a0d865503d", "versionId": "1f0d985a-078d-474d-88b5-1e00a912288a"}, {"name": "dueDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.835f15cc-1e21-4b28-8dfa-ae01b3fc2a60", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "95b55c44-6dc4-43d8-b3a6-2d9e9341e42c", "versionId": "a8739be6-1cd4-412f-a16e-d848cc72f691"}, {"name": "owner", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.07eefca8-7724-4e71-8c48-f8f90091f6f7", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "917558f6-3342-457f-9a4e-739ad093b04c", "versionId": "1c0a0884-7fb2-498c-83f8-644cd75864ca"}, {"name": "mailTo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1a7d068d-26e5-441b-8a0d-71ed4cc96540", "description": {"isNull": "true"}, "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "45feb0c3-3b13-4551-acce-516e1ff1cb2d", "versionId": "4177fa2a-04f9-4492-b489-21f73643ced6"}], "note": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLabelId": "2028.d03a0a49-59cd-4e7a-8af0-d1c641e844e5", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "description": "This service is sending mail to the manager of the last overdue activities", "data": {"isNull": "true"}, "guid": "ca4ea145-d800-471c-9634-24e634362875", "versionId": "2dab142c-2ec9-403d-b8e2-b98c3bb08263", "layoutData": {"x": "73", "y": "258", "width": "267", "height": "51"}}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e1dd77f5-d2f0-4883-8e82-ca9bcb766ece", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "Set Mail Body", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.c4d0cbe2-8425-4bc9-be7d-0071b8e4b321", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:755f", "versionId": "0cfa9074-598c-4e54-8e49-0ead126151b5", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "611", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.c4d0cbe2-8425-4bc9-be7d-0071b8e4b321", "scriptTypeId": "128", "isActive": "true", "script": "tw.local.msgBody\r\n<html dir=\"ltl\" lang=\"en\">\r\r\n\t<p>Dear Sir / Madam\t\r\r\n\t</br>\t\r\r\n\t<p>Kindly be informed that the Request\r\r\n\t<#=tw.local.odcRequest.BasicDetails.requestType #>\r\r\n\t with request number\r\r\n\t <#=tw.local.odcRequest.appInfo.instanceID #>\r\r\n\t  related to the customer\r\r\n\t  <#=tw.local.odcRequest.CustomerInfo.customerName #>\r\r\n\t  is now past due and below are the request details:</p>\r\r\n\t</p>\r\r\n\t<ul>\r\r\n\t\t<li><b>Activity Name:</b><em> <#=tw.local.activityName#></em></li>\r\r\n\t\t<li><b>Participant:</b><em> <#=tw.local.owner#></em></li>\r\r\n\t\t<li><b>Received Date:</b><em> <#=tw.local.receivedDate#></em></li>\r\r\n\t\t<li><b>Request Status:</b><em> <#=tw.local.odcRequest.appInfo.status#></em></li>\r\r\n\t\t<li><b>Request Sub-status:</b><em> <#=tw.local.odcRequest.appInfo.subStatus#></em></li>\r\r\n\t\t<li><b>Due Date:</b><em> <#=tw.local.dueDate#></em></li>  \r\r\n\t</ul>\t\t\t\r\r\n\t<b  style=\"color:red;\">Please do not reply to this message.  This is an automatically generated email.</b>\r\r\n\r\r\n\t</br>\r\r\n\t<p dir=\"rtl\" lang=\"ar\">\t\r\r\n\tالسيد / السيدة </br></br>\r\r\n\tبرجاء العلم أن  طلب  <#=tw.local.odcRequest.BasicDetails.requestType #> رقم   <#=tw.local.odcRequest.appInfo.instanceID #>  للعميل   <#=tw.local.odcRequest.CustomerInfo.customerName #> \r\r\n\tقد تخطى الوقت المعين للمهمة .\r\r\n\tيوجد أدناه  البيانات الأساسية الخاصة بالطلب.\r\r\n\t<ul  dir=\"rtl\" lang=\"ar\">\r\r\n\t\t<li><b>اسم المهمة:</b><em> <#=tw.local.activityName#></em></li>\r\r\n\t\t<li><b>المستخدم:</b><em> <#=tw.local.owner#></em></li>\r\r\n\t\t<li><b>تاريخ استلام المهمة:</b><em> <#=tw.local.receivedDate#></em></li>\r\r\n\t\t<li><b>حالة الطلب:</b><em> <#=tw.local.odcRequest.appInfo.status#></em></li>\r\r\n\t\t<li><b>الحالة الفرعية للطلب:</b><em> <#=tw.local.odcRequest.appInfo.subStatus#></em></li>\r\r\n\t\t<li><b>تاريخ الانتهاء المعين:</b><em> <#=tw.local.dueDate#></em></li>  \r\r\n\t</ul> \r\r\n\t</p>\r\r\n\t<b> <p dir=\"rtl\"; lang=\"ar\"; style=\"color:red;\">\t الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.  </p></b>\r\r\n\t</html> \r\r\n\t \r\r\n\t ", "isRule": "false", "guid": "cc39fb28-11a7-4109-8dd4-b9f008e70bcf", "versionId": "473f627e-775f-4c31-b8cf-71905091d6ee"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.caed069f-8383-4d67-8b17-7e442ecedd8c", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "Set Mail to", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.0100d02c-3016-4804-a119-1fc93f344d6e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:3014", "versionId": "0fac5aba-423b-415e-a6cc-e65bd9fb8c9c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.8689af9e-a557-4e13-b416-7af7198cd6fb", "processItemId": "2025.caed069f-8383-4d67-8b17-7e442ecedd8c", "location": "1", "script": "tw.local.odcRequest.stepLog.step = String(tw.epv.ScreenNames.CACT04);", "guid": "66c079de-2b05-4a23-912e-da710714a0da", "versionId": "97bdcc9f-ba67-40df-a0a0-fbe689592c3a"}, "layoutData": {"x": "463", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.0100d02c-3016-4804-a119-1fc93f344d6e", "scriptTypeId": "2", "isActive": "true", "script": "//Create Process - Act01\r\r\nif( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT01)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.branch)) )\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.branchMkr_Mngr);\r\r\nelse if( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT01)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.hub)) )\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.hubMkr_Mngr);\r\r\n\r\r\n//Create Process - Act02\t\t\r\r\nelse if( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT02)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.branchComp)) )\r\r\n\ttw.local.mailTo = tw.epv.Mails.branchComp_Mngr;\t\r\r\n\r\r\n//Create Process - Act03\t\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT03))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.tradeFO_Mngr);\r\r\n\t\r\r\n//Create Process - Act04\t\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT04))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.exeHubMkr_Mngr);\t\r\r\n\r\r\n//Create Process - Act05\t\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT05))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.exeHubChkr_Mngr);\r\r\n\r\r\n//Create Process - Act06\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT06))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.exeHubChkr_Mngr);\t\t\r\r\n\r\r\n//228047\r\r\n//228047 cs maker", "isRule": "false", "guid": "c7d6f590-c11c-45bf-a3a4-5722804b4b6c", "versionId": "5b64f3c5-1908-4a4a-b3ed-5a82497b509c"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.aa6564df-48df-4c67-8648-e6d51effb810", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "Send Mail", "tWComponentName": "SubProcess", "tWComponentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:71dd", "versionId": "119780d0-54ec-4455-b26c-6bc35b51a53a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "731", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.338e9f4d-8538-4ceb-a155-c288604435d4", "guid": "a627b839-6e23-4065-83d5-726ee1beb061", "versionId": "76881b03-ac55-437a-9da0-0349d917b8ec", "parameterMapping": [{"name": "mailDebugMode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c05b4b23-1680-43cd-b3de-730ab363a28d", "processParameterId": "2055.1da05789-2131-46bc-aacf-34d84ca37def", "parameterMappingParentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "useDefault": "false", "value": "tw.local.mailDebugMode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "9bb5810d-09b7-426a-802b-005091f8dde9", "versionId": "28d1ef33-a69a-4119-b2ed-e760d01d0ed8", "description": {"isNull": "true"}}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b5060b88-5481-4981-813d-94d54bdc5d05", "processParameterId": "2055.ec26c46c-d70b-4881-98d8-40e694dd7362", "parameterMappingParentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "9442d5ec-7e44-44f7-abf3-dfdf933d2056", "versionId": "4da20ee0-c00c-4a8f-abd6-8f20bbef87db", "description": {"isNull": "true"}}, {"name": "attachments", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0ef6eaea-deb5-4b55-a9ad-56f6e9fd85df", "processParameterId": "2055.268afc2e-a651-49ef-8704-9a6ff22065c6", "parameterMappingParentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "useDefault": "false", "value": "null", "classRef": "/12.8a11240b-682f-4caf-9f03-5ce6a64d720b", "isList": "true", "isInput": "true", "guid": "41c090ec-79e3-4a35-badd-d13808ee2edb", "versionId": "5a58c4ed-d46f-46b5-975e-cbda2ec51e7e", "description": {"isNull": "true"}}, {"name": "subject", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1477090d-fd0c-4f8a-b152-f24776d66e5d", "processParameterId": "2055.ae81d59d-2fde-4526-9476-d0598d6e8472", "parameterMappingParentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "useDefault": "false", "value": "tw.local.subject", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "6272d1c4-5d1f-462b-82d0-e5d808378caa", "versionId": "655de839-21c2-4608-88fa-5c0e84eba166", "description": {"isNull": "true"}}, {"name": "mailTo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.54709dd0-bc2c-4e66-83eb-9680331bef21", "processParameterId": "2055.a7c74b41-811f-4581-94ef-69a84c74eb84", "parameterMappingParentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "useDefault": "false", "value": "tw.local.mailTo", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "74ac6105-b488-4c21-835f-3d93dce074e9", "versionId": "6e180c13-620a-4093-b5cb-ff6b3786bb5f", "description": {"isNull": "true"}}, {"name": "status", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ea6a6f37-447b-4a60-8802-8d2a24933713", "processParameterId": "2055.20348cf5-023e-4d3a-826e-5b92143ec224", "parameterMappingParentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "useDefault": "false", "value": "\"\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "0d6629d3-47d3-48dd-89d8-42cc7ea26114", "versionId": "8558c9e0-913a-46eb-ada3-342c7e269bd1", "description": {"isNull": "true"}}, {"name": "msgBody", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.66eb62f1-3939-47a1-bfb7-40653d7b13d6", "processParameterId": "2055.6d9bd911-88b8-4ea3-8823-421a4f690290", "parameterMappingParentId": "3012.75008252-1ffe-4c92-9c64-bd8d3add5230", "useDefault": "false", "value": "tw.local.msgBody", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ebe0122a-da37-440a-ba0e-4c55b1a4148f", "versionId": "92673b00-e4a5-43d7-bf26-fb4e4b464393", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.657aa980-91ac-4921-87a0-0a8611380c6e", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "Exp Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.5ed7bd3f-22cf-4c8e-9d4a-10283c8c17a0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:2cc7", "versionId": "1a75323c-f4e0-4dc7-90c4-43a1e6e510f2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "724", "y": "154", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5ed7bd3f-22cf-4c8e-9d4a-10283c8c17a0", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "98e2ddac-25a5-4485-a331-113a08d56037", "versionId": "4d025ac1-b4bf-4faf-a19c-fb875bf2d516", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.07d8cd41-1b36-45d4-97b5-2673374489ba", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.5ed7bd3f-22cf-4c8e-9d4a-10283c8c17a0", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "74ba41db-d51f-4caa-b24c-1151087a1f0e", "versionId": "55cd61ec-3b20-434c-9081-690a077ff857", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.75f8cbf8-8e4c-459e-9954-a75b02319f43", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.5ed7bd3f-22cf-4c8e-9d4a-10283c8c17a0", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "f6d7a1ac-b097-43dd-8bcf-ee6d900ceee4", "versionId": "b64cda3c-8c9d-4e1b-b3ee-6c0e73a4ad9f", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0a4d564b-fce7-4d0d-9e2b-24b836344b5a", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.5ed7bd3f-22cf-4c8e-9d4a-10283c8c17a0", "useDefault": "false", "value": "\"Send Escalation Mail\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "de8ec00b-f1be-46cc-b161-1499075d2855", "versionId": "edae5e8b-2e43-4b6c-b85e-77e9a4fa35e4", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b6af4499-6047-4684-89c2-24be85d232d8", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.361825fc-744a-447f-b6a2-b48962af6df4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:70d4", "versionId": "eb90b2a7-b90b-4194-a4b3-cb0fbac816fc", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "860", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.361825fc-744a-447f-b6a2-b48962af6df4", "haltProcess": "false", "guid": "9f1b5ab3-3f5f-4b18-bb12-1b33459fc322", "versionId": "*************-4c70-8436-87c8cd5f9fe6"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9d37e7cd-f663-483f-8a5a-e7731f5d4b39", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "name": "Init Data & Subject", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.f05a5894-c75a-42ca-831f-34de5a13ddca", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:7108", "versionId": "ede6c4fc-3d56-49d3-bc19-068da494b368", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "326", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.f05a5894-c75a-42ca-831f-34de5a13ddca", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.activityName= tw.system.findTaskByID(tw.local.taskId).processActivityName;\r\r\ntw.local.receivedDate = String(tw.system.findTaskByID(tw.local.taskId).activationTime.formatDate(\"short\"));\r\r\ntw.local.dueDate= String(tw.system.findTaskByID(tw.local.taskId).dueDate.formatDate(\"short\"));\r\r\ntw.local.owner = tw.system.findTaskByID(tw.local.taskId).owner;\r\r\n\r\r\n//Set Mail Subject\r\r\ntw.local.subject = \"ODC Request No. \"+tw.local.odcRequest.appInfo.instanceID\r\r\n\t\t\t+\" for Customer \"+tw.local.odcRequest.CustomerInfo.customerName\r\r\n\t\t\t+\" is Past Due \"\r\r\n\t\t\t+\"طلب التحصيل المستندى تصدير رقم \"+tw.local.odcRequest.appInfo.instanceID\r\r\n\t\t\t+\" للعميل \"+tw.local.odcRequest.CustomerInfo.customerName\r\r\n\t\t\t+\" قد تخطى الوقت المعين للمهمة\";\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "e7577637-83bc-4d0b-9183-3452d215597c", "versionId": "c1c54c39-74fb-414b-9504-339fe674719d"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.6be865ba-9cc8-4988-9799-2656bd7a408a", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "guid": "13d34e0a-32ae-44f6-b0b9-5c3179b8390a", "versionId": "0545aeb6-cf16-4cd5-bce7-8df2dba9564a"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.62307be5-065d-4904-90ac-cf8eb6050236", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "guid": "ffe50833-1941-4059-8879-91a681971a2c", "versionId": "23226562-2c98-4970-a760-b30ed84ff451"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.6a1ea3e1-d6de-4a60-bd4a-d0fef3f9b532", "epvId": "/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "guid": "5fa00468-cddf-44a0-8d09-0f5227eae240", "versionId": "492ae7e9-315c-450a-bd2f-a5cc1132a2d6"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.436d0a8c-2893-475c-9632-fd5940131a9a", "epvId": "/21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "guid": "ef74fb8e-564f-4067-a1e7-186bdaba05a6", "versionId": "eae427af-f18e-4972-8ef6-58b0ae92b9c8"}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "245", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "635", "y": "177", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Send Escalation Mail", "id": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "ns3:executionMode": "microflow", "ns16:documentation": {"_": "Escalation mail service: <div>   this service is running when the task has delay to be done </div>", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.6ffb18e6-3e29-431c-9b6f-9e0c9c43aa0c", "epvProcessLinkId": "e8739fbb-0966-42d1-818a-13b8f484769f"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "97aabdce-bf0c-464c-82f6-cab44e9d5106"}, {"epvId": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "epvProcessLinkId": "21f48856-314f-4db4-8449-5aa9da1df55e"}, {"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "efe5e073-59c3-4090-8c0c-f0b4a4a77a22"}]}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.Parties.caseInNeed = new tw.object.partyTypes();\r\nautoObject.Parties.caseInNeed.partyCIF = \"\";\r\nautoObject.Parties.caseInNeed.partyId = \"\";\r\nautoObject.Parties.caseInNeed.partyName = \"\";\r\nautoObject.Parties.caseInNeed.country = \"\";\r\nautoObject.Parties.caseInNeed.language = \"\";\r\nautoObject.Parties.caseInNeed.refrence = \"\";\r\nautoObject.Parties.caseInNeed.address1 = \"\";\r\nautoObject.Parties.caseInNeed.address2 = \"\";\r\nautoObject.Parties.caseInNeed.address3 = \"\";\r\nautoObject.Parties.caseInNeed.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.caseInNeed.partyType.name = \"\";\r\nautoObject.Parties.caseInNeed.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].isGLFound = false;\r\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.debitedAccountName = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"\";\r\nautoObject.templateDocID = \"\";\r\nautoObject.requestID = 0;\r\nautoObject.customerAndPartyAccountList = new tw.object.listOf.toolkit.NBEINTT.Account();\r\nautoObject.customerAndPartyAccountList[0] = new tw.object.toolkit.NBEINTT.Account();\r\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\r\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\r\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\r\nautoObject.customerAndPartyAccountList[0].frozen = false;\r\nautoObject.customerAndPartyAccountList[0].dormant = false;\r\nautoObject.customerAndPartyAccountList[0].noDebit = false;\r\nautoObject.customerAndPartyAccountList[0].noCredit = false;\r\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\r\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\r\nautoObject.tradeFoComment = \"\";\r\nautoObject.exeHubMkrComment = \"\";\r\nautoObject.compcheckerComment = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "taskId", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"2078.119755\"", "useDefault": "true"}}}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f", "2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1"]}, "ns16:outputSet": ""}, "ns16:laneSet": {"id": "e72cf5af-2fec-4261-8cd1-2b8868762096", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "********-ba6d-452a-8d91-af214e069933", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "536", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["********-871a-4db7-8ec4-0bf694ae48fa", "b6af4499-6047-4684-89c2-24be85d232d8", "9d37e7cd-f663-483f-8a5a-e7731f5d4b39", "aa6564df-48df-4c67-8648-e6d51effb810", "e1dd77f5-d2f0-4883-8e82-ca9bcb766ece", "657aa980-91ac-4921-87a0-0a8611380c6e", "686e3738-91c4-4729-87d1-3fdca84d645c", "d03a0a49-59cd-4e7a-8af0-d1c641e844e5", "caed069f-8383-4d67-8b17-7e442ecedd8c"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "********-871a-4db7-8ec4-0bf694ae48fa", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "245", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.106b2c38-6da0-47f0-84c5-3bf2a9961681"}, "ns16:endEvent": {"name": "End", "id": "b6af4499-6047-4684-89c2-24be85d232d8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "860", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:c400b33e5f41dfd9:-18f49999:18a02ce6e31:70d4"}, "ns16:incoming": ["8421cded-560a-403b-88e3-5f75ae14cd21", "7ef1ebdb-ab6d-4ecc-8a41-72bfe3ac6d85"]}, "ns16:sequenceFlow": [{"sourceRef": "********-871a-4db7-8ec4-0bf694ae48fa", "targetRef": "9d37e7cd-f663-483f-8a5a-e7731f5d4b39", "name": "To Init Data & Subject", "id": "2027.106b2c38-6da0-47f0-84c5-3bf2a9961681", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "aa6564df-48df-4c67-8648-e6d51effb810", "targetRef": "b6af4499-6047-4684-89c2-24be85d232d8", "name": "To End", "id": "8421cded-560a-403b-88e3-5f75ae14cd21", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:e30c377f7882db6a:324bd248:186d6576310:-2970"}}, {"sourceRef": "9d37e7cd-f663-483f-8a5a-e7731f5d4b39", "targetRef": "caed069f-8383-4d67-8b17-7e442ecedd8c", "name": "To Set Mail to", "id": "a2d8440f-e555-48b1-81d5-f58ce682a504", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "e1dd77f5-d2f0-4883-8e82-ca9bcb766ece", "targetRef": "aa6564df-48df-4c67-8648-e6d51effb810", "name": "To Service", "id": "f6b08286-e1dd-41b8-85e2-9fa5a2f3570d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "686e3738-91c4-4729-87d1-3fdca84d645c", "targetRef": "657aa980-91ac-4921-87a0-0a8611380c6e", "name": "To Exp Handling", "id": "a2be480d-2f50-4f01-885a-6e4bdc912ab7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "657aa980-91ac-4921-87a0-0a8611380c6e", "targetRef": "b6af4499-6047-4684-89c2-24be85d232d8", "name": "To End", "id": "7ef1ebdb-ab6d-4ecc-8a41-72bfe3ac6d85", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "caed069f-8383-4d67-8b17-7e442ecedd8c", "targetRef": "e1dd77f5-d2f0-4883-8e82-ca9bcb766ece", "name": "To Set Mail Body", "id": "06795a87-b7b0-4d3e-8774-030d0cccab18", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Init Data & Subject", "id": "9d37e7cd-f663-483f-8a5a-e7731f5d4b39", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "326", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.106b2c38-6da0-47f0-84c5-3bf2a9961681", "ns16:outgoing": "a2d8440f-e555-48b1-81d5-f58ce682a504", "ns16:script": "tw.local.activityName= tw.system.findTaskByID(tw.local.taskId).processActivityName;\r\r\ntw.local.receivedDate = String(tw.system.findTaskByID(tw.local.taskId).activationTime.formatDate(\"short\"));\r\r\ntw.local.dueDate= String(tw.system.findTaskByID(tw.local.taskId).dueDate.formatDate(\"short\"));\r\r\ntw.local.owner = tw.system.findTaskByID(tw.local.taskId).owner;\r\r\n\r\r\n//Set Mail Subject\r\r\ntw.local.subject = \"ODC Request No. \"+tw.local.odcRequest.appInfo.instanceID\r\r\n\t\t\t+\" for Customer \"+tw.local.odcRequest.CustomerInfo.customerName\r\r\n\t\t\t+\" is Past Due \"\r\r\n\t\t\t+\"طلب التحصيل المستندى تصدير رقم \"+tw.local.odcRequest.appInfo.instanceID\r\r\n\t\t\t+\" للعميل \"+tw.local.odcRequest.CustomerInfo.customerName\r\r\n\t\t\t+\" قد تخطى الوقت المعين للمهمة\";\r\r\n\r\r\n\r\r\n"}, {"scriptFormat": "text/plain", "name": "Set Mail Body", "id": "e1dd77f5-d2f0-4883-8e82-ca9bcb766ece", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "611", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "06795a87-b7b0-4d3e-8774-030d0cccab18", "ns16:outgoing": "f6b08286-e1dd-41b8-85e2-9fa5a2f3570d", "ns16:script": "tw.local.msgBody\r\n<html dir=\"ltl\" lang=\"en\">\r\r\n\t<p>Dear Sir / Madam\t\r\r\n\t</br>\t\r\r\n\t<p>Kindly be informed that the Request\r\r\n\t<#=tw.local.odcRequest.BasicDetails.requestType #>\r\r\n\t with request number\r\r\n\t <#=tw.local.odcRequest.appInfo.instanceID #>\r\r\n\t  related to the customer\r\r\n\t  <#=tw.local.odcRequest.CustomerInfo.customerName #>\r\r\n\t  is now past due and below are the request details:</p>\r\r\n\t</p>\r\r\n\t<ul>\r\r\n\t\t<li><b>Activity Name:</b><em> <#=tw.local.activityName#></em></li>\r\r\n\t\t<li><b>Participant:</b><em> <#=tw.local.owner#></em></li>\r\r\n\t\t<li><b>Received Date:</b><em> <#=tw.local.receivedDate#></em></li>\r\r\n\t\t<li><b>Request Status:</b><em> <#=tw.local.odcRequest.appInfo.status#></em></li>\r\r\n\t\t<li><b>Request Sub-status:</b><em> <#=tw.local.odcRequest.appInfo.subStatus#></em></li>\r\r\n\t\t<li><b>Due Date:</b><em> <#=tw.local.dueDate#></em></li>  \r\r\n\t</ul>\t\t\t\r\r\n\t<b  style=\"color:red;\">Please do not reply to this message.  This is an automatically generated email.</b>\r\r\n\r\r\n\t</br>\r\r\n\t<p dir=\"rtl\" lang=\"ar\">\t\r\r\n\tالسيد / السيدة </br></br>\r\r\n\tبرجاء العلم أن  طلب  <#=tw.local.odcRequest.BasicDetails.requestType #> رقم   <#=tw.local.odcRequest.appInfo.instanceID #>  للعميل   <#=tw.local.odcRequest.CustomerInfo.customerName #> \r\r\n\tقد تخطى الوقت المعين للمهمة .\r\r\n\tيوجد أدناه  البيانات الأساسية الخاصة بالطلب.\r\r\n\t<ul  dir=\"rtl\" lang=\"ar\">\r\r\n\t\t<li><b>اسم المهمة:</b><em> <#=tw.local.activityName#></em></li>\r\r\n\t\t<li><b>المستخدم:</b><em> <#=tw.local.owner#></em></li>\r\r\n\t\t<li><b>تاريخ استلام المهمة:</b><em> <#=tw.local.receivedDate#></em></li>\r\r\n\t\t<li><b>حالة الطلب:</b><em> <#=tw.local.odcRequest.appInfo.status#></em></li>\r\r\n\t\t<li><b>الحالة الفرعية للطلب:</b><em> <#=tw.local.odcRequest.appInfo.subStatus#></em></li>\r\r\n\t\t<li><b>تاريخ الانتهاء المعين:</b><em> <#=tw.local.dueDate#></em></li>  \r\r\n\t</ul> \r\r\n\t</p>\r\r\n\t<b> <p dir=\"rtl\"; lang=\"ar\"; style=\"color:red;\">\t الرجاء عدم الرد على هذه الرسالة. هذا بريد إلكتروني تم إنشاؤه تلقائيًا.  </p></b>\r\r\n\t</html> \r\r\n\t \r\r\n\t "}, {"scriptFormat": "text/x-javascript", "name": "Set Mail to", "id": "caed069f-8383-4d67-8b17-7e442ecedd8c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "463", "y": "57", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.odcRequest.stepLog.step = String(tw.epv.ScreenNames.CACT04);"}, "ns16:incoming": "a2d8440f-e555-48b1-81d5-f58ce682a504", "ns16:outgoing": "06795a87-b7b0-4d3e-8774-030d0cccab18", "ns16:script": "//Create Process - Act01\r\r\nif( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT01)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.branch)) )\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.branchMkr_Mngr);\r\r\nelse if( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT01)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.hub)) )\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.hubMkr_Mngr);\r\r\n\r\r\n//Create Process - Act02\t\t\r\r\nelse if( (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT02)) && (tw.local.odcRequest.stepLog.role == String(tw.epv.userRole.branchComp)) )\r\r\n\ttw.local.mailTo = tw.epv.Mails.branchComp_Mngr;\t\r\r\n\r\r\n//Create Process - Act03\t\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT03))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.tradeFO_Mngr);\r\r\n\t\r\r\n//Create Process - Act04\t\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT04))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.exeHubMkr_Mngr);\t\r\r\n\r\r\n//Create Process - Act05\t\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT05))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.exeHubChkr_Mngr);\r\r\n\r\r\n//Create Process - Act06\r\r\nelse if (tw.local.odcRequest.stepLog.step == String(tw.epv.ScreenNames.CACT06))\r\r\n\ttw.local.mailTo = String(tw.epv.Mails.exeHubChkr_Mngr);\t\t\r\r\n\r\r\n//228047\r\r\n//228047 cs maker"}], "ns16:callActivity": [{"calledElement": "1.338e9f4d-8538-4ceb-a155-c288604435d4", "name": "Send Mail", "id": "aa6564df-48df-4c67-8648-e6d51effb810", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "731", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "f6b08286-e1dd-41b8-85e2-9fa5a2f3570d", "ns16:outgoing": "8421cded-560a-403b-88e3-5f75ae14cd21", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.a7c74b41-811f-4581-94ef-69a84c74eb84", "ns16:assignment": {"ns16:from": {"_": "tw.local.mailTo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.ae81d59d-2fde-4526-9476-d0598d6e8472", "ns16:assignment": {"ns16:from": {"_": "tw.local.subject", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.6d9bd911-88b8-4ea3-8823-421a4f690290", "ns16:assignment": {"ns16:from": {"_": "tw.local.msgBody", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.1da05789-2131-46bc-aacf-34d84ca37def", "ns16:assignment": {"ns16:from": {"_": "tw.local.mailDebugMode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.20348cf5-023e-4d3a-826e-5b92143ec224", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.268afc2e-a651-49ef-8704-9a6ff22065c6", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.ec26c46c-d70b-4881-98d8-40e694dd7362", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exp Handling", "id": "657aa980-91ac-4921-87a0-0a8611380c6e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "724", "y": "154", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "a2be480d-2f50-4f01-885a-6e4bdc912ab7", "ns16:outgoing": "7ef1ebdb-ab6d-4ecc-8a41-72bfe3ac6d85", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Send Escalation Mail\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "mailDebugMode", "id": "2056.9e486eb4-075e-4e6f-8c63-85eaf1dc8335"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "msgBody", "id": "2056.7df99c67-05a0-43d2-83bc-c0179babdddb"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "subject", "id": "2056.2a2899da-41c3-4af5-800e-87773f32f75d"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMsg", "id": "2056.89854078-9ec2-457d-85b7-bb85f419ed7e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "activityName", "id": "2056.0b2d1e4e-754f-40b3-868b-0c717a42d493"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "receivedDate", "id": "2056.581c2209-aa2d-493a-86c1-48e9441e18d1"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "dueDate", "id": "2056.835f15cc-1e21-4b28-8dfa-ae01b3fc2a60"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "owner", "id": "2056.07eefca8-7724-4e71-8c48-f8f90091f6f7"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "mailTo", "id": "2056.1a7d068d-26e5-441b-8a0d-71ed4cc96540"}], "ns16:intermediateCatchEvent": {"name": "Error Event", "id": "686e3738-91c4-4729-87d1-3fdca84d645c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "635", "y": "177", "width": "24", "height": "24"}}, "ns16:outgoing": "a2be480d-2f50-4f01-885a-6e4bdc912ab7", "ns16:errorEventDefinition": {"id": "3aa0ab2a-dc0f-4cc8-8f18-32930ba450f1", "eventImplId": "e6388485-4abe-414e-896a-ae6b6d64d77d", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:textAnnotation": {"textFormat": "text/plain", "id": "d03a0a49-59cd-4e7a-8af0-d1c641e844e5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "73", "y": "258", "width": "267", "height": "51"}}, "ns16:text": "This service is sending mail to the manager of the last overdue activities"}}}}, "link": [{"name": "To Set Mail Body", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.06795a87-b7b0-4d3e-8774-030d0cccab18", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.caed069f-8383-4d67-8b17-7e442ecedd8c", "2025.caed069f-8383-4d67-8b17-7e442ecedd8c"], "endStateId": "Out", "toProcessItemId": ["2025.e1dd77f5-d2f0-4883-8e82-ca9bcb766ece", "2025.e1dd77f5-d2f0-4883-8e82-ca9bcb766ece"], "guid": "7e3ad577-2da5-4821-ba35-8b963cac159b", "versionId": "4957da6d-7184-4347-9bce-d42c7ccd121e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Set Mail to", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a2d8440f-e555-48b1-81d5-f58ce682a504", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.9d37e7cd-f663-483f-8a5a-e7731f5d4b39", "2025.9d37e7cd-f663-483f-8a5a-e7731f5d4b39"], "endStateId": "Out", "toProcessItemId": ["2025.caed069f-8383-4d67-8b17-7e442ecedd8c", "2025.caed069f-8383-4d67-8b17-7e442ecedd8c"], "guid": "31a7d391-53da-4956-a79c-4a68d2b89653", "versionId": "aa367d67-b093-443e-b4e0-3c4f1f02d6e9", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.8421cded-560a-403b-88e3-5f75ae14cd21", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.aa6564df-48df-4c67-8648-e6d51effb810", "2025.aa6564df-48df-4c67-8648-e6d51effb810"], "endStateId": "guid:e30c377f7882db6a:324bd248:186d6576310:-2970", "toProcessItemId": ["2025.b6af4499-6047-4684-89c2-24be85d232d8", "2025.b6af4499-6047-4684-89c2-24be85d232d8"], "guid": "94f34aeb-49f9-4c81-8e91-b094049b0f90", "versionId": "dca86db4-a952-4445-8af2-b33366005724", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7ef1ebdb-ab6d-4ecc-8a41-72bfe3ac6d85", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.657aa980-91ac-4921-87a0-0a8611380c6e", "2025.657aa980-91ac-4921-87a0-0a8611380c6e"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.b6af4499-6047-4684-89c2-24be85d232d8", "2025.b6af4499-6047-4684-89c2-24be85d232d8"], "guid": "1eff5137-da19-42e4-9a78-ddddfb5308b1", "versionId": "ec2c9389-6fec-4943-b34d-30f7847c2e72", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Service", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f6b08286-e1dd-41b8-85e2-9fa5a2f3570d", "processId": "1.d7acf968-6740-4e52-b037-2049466eeeb2", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e1dd77f5-d2f0-4883-8e82-ca9bcb766ece", "2025.e1dd77f5-d2f0-4883-8e82-ca9bcb766ece"], "endStateId": "Out", "toProcessItemId": ["2025.aa6564df-48df-4c67-8648-e6d51effb810", "2025.aa6564df-48df-4c67-8648-e6d51effb810"], "guid": "ce4f046d-e6e9-43f0-8c73-80514b78606e", "versionId": "f28e712f-f0a0-4f1f-822b-8c1c9ff4ad61", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}