{"id": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "versionId": "21577f8c-2df9-4566-867c-ec0b2d8182db", "name": "ClosureACT02 - Review ODC Closure Request by Compliance Rep", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "routingDetails", "hasDefault": false, "type": "1"}, {"name": "lastAction", "hasDefault": false, "type": "1"}, {"name": "parentPath", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "role", "hasDefault": false}, {"name": "error", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Review ODC Closure Request", "id": "2025.c85f3552-61a8-4721-8b4d-3b45446d865d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "History", "id": "2025.215786c9-52ee-4367-80d3-08665cd9809d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit closure data", "id": "2025.571f6642-fc7c-4f9d-8179-d754278a85b5", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel request", "id": "2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Valid?", "id": "2025.f45ffe81-0484-4595-8cbf-78180725c5fe", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "audited?", "id": "2025.802539d1-9dec-4b1c-82dc-11beed907565", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel", "id": "2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Exclusive Gateway", "id": "2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "init Script", "id": "2025.6bc93cfb-159e-448b-8f79-3c02066338d9", "script": "tw.local.odcRequest.stepLog ={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step      = tw.epv.ScreenNames.closureACT02;//\" Review ODC Closure Request by Compliance Rep – مراجعة طلب اقفال تحصيل مستندى تصدير من ممثل الإلتزام \";\r\r\n\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= false;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT02;\r\r\ntw.local.actionConditions.lastStepAction = tw.local.lastAction;\r\r\n\r\r\n///////////////*Initializing Request header*//////////////////////////\r\r\n//////////////////////////////////////////////////////////////////////\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\r\r\n//tw.local.odcRequest.appInfo.branch = {};\r\r\n\r\r\n\t\r\r\n\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\n\r\r\n\ttw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT02;\r\r\n\r\r\n\tif(tw.local.routingDetails.branchCode == \"\" && tw.local.routingDetails.branchCode == null)\r\r\n\t{\r\r\n\t\ttw.local.role = tw.epv.userRole.hubComp; //\"Hub Compliance Rep\";\t\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\r\r\n\t\ttw.local.role =  tw.epv.userRole.branchComp;//\" Branch Compliance Rep \";\r\r\n\t}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "setting Status and substatus", "id": "2025.c0f030cf-b8a3-4cb6-8a5d-0c6450ad6744", "script": "if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\" Awaiting Trade FO Approval\";\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"Cancelled\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Cancelled\";\r\r\n\t\r\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Final\";\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"Initiated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Return to initiator\";\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "validation script", "id": "2025.a552d3c1-196a-417f-826f-2edc941f16be", "script": "tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n \r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\r\n{\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason , \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n}\t\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "name": "ClosureACT02 - Review ODC Closure Request by Compliance Rep", "lastModified": "1700640471709", "lastModifiedBy": "so<PERSON>ia", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.34c32d45-9e61-40e4-a065-399699c34372", "2025.34c32d45-9e61-40e4-a065-399699c34372"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-4305", "versionId": "21577f8c-2df9-4566-867c-ec0b2d8182db", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.8542666b-8e40-4716-97b8-c95406ef1f83\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":50,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"cfce49bc-91d9-4143-aca1-68775a9bbd8e\"},{\"incoming\":[\"2027.341f7459-**************-1b14cf143ae2\",\"2027.81349889-6cdc-443a-8c26-74e33cd0f398\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1779,\"y\":199,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"c55b6291-c6e2-49d4-b9e3-b031ef8f7b55\"},{\"targetRef\":\"2025.6bc93cfb-159e-448b-8f79-3c02066338d9\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8542666b-8e40-4716-97b8-c95406ef1f83\",\"sourceRef\":\"cfce49bc-91d9-4143-aca1-68775a9bbd8e\"},{\"startQuantity\":1,\"outgoing\":[\"2027.994807e1-1654-4eba-8d9b-ad6573a186b0\"],\"incoming\":[\"2027.8542666b-8e40-4716-97b8-c95406ef1f83\"],\"default\":\"2027.994807e1-1654-4eba-8d9b-ad6573a186b0\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":172,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"init Script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.6bc93cfb-159e-448b-8f79-3c02066338d9\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.stepLog ={};\\r\\ntw.local.odcRequest.stepLog.startTime = new Date();\\r\\ntw.local.odcRequest.stepLog.step      = tw.epv.ScreenNames.closureACT02;\\/\\/\\\" Review ODC Closure Request by Compliance Rep \\u2013 \\u0645\\u0631\\u0627\\u062c\\u0639\\u0629 \\u0637\\u0644\\u0628 \\u0627\\u0642\\u0641\\u0627\\u0644 \\u062a\\u062d\\u0635\\u064a\\u0644 \\u0645\\u0633\\u062a\\u0646\\u062f\\u0649 \\u062a\\u0635\\u062f\\u064a\\u0631 \\u0645\\u0646 \\u0645\\u0645\\u062b\\u0644 \\u0627\\u0644\\u0625\\u0644\\u062a\\u0632\\u0627\\u0645 \\\";\\r\\n\\r\\n\\r\\ntw.local.actionConditions = {};\\r\\ntw.local.actionConditions.complianceApproval= false;\\r\\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT02;\\r\\ntw.local.actionConditions.lastStepAction = tw.local.lastAction;\\r\\n\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/*Initializing Request header*\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nvar date = new Date();\\r\\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\\/' +(date.getMonth() + 1) + '\\/' + date.getFullYear();\\r\\n\\r\\n\\/\\/tw.local.odcRequest.appInfo.branch = {};\\r\\n\\r\\n\\t\\r\\n\\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\\\"( \\\"+ tw.system.user.name+\\\")\\\";\\r\\n\\r\\n\\ttw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT02;\\r\\n\\r\\n\\tif(tw.local.routingDetails.branchCode == \\\"\\\" && tw.local.routingDetails.branchCode == null)\\r\\n\\t{\\r\\n\\t\\ttw.local.role = tw.epv.userRole.hubComp; \\/\\/\\\"Hub Compliance Rep\\\";\\t\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\t\\t\\r\\n\\t\\ttw.local.role =  tw.epv.userRole.branchComp;\\/\\/\\\" Branch Compliance Rep \\\";\\r\\n\\t}\"]}},{\"outgoing\":[\"2027.16d27c63-04c6-48e0-85f2-91efdeeeb67f\",\"2027.7fd4298f-fe10-4701-88ad-e592f00ec68d\"],\"incoming\":[\"2027.994807e1-1654-4eba-8d9b-ad6573a186b0\",\"2027.a9fb5525-6abd-4c3b-813d-49241493765e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":311,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Reversal_Closure_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"02207c68-02f4-4f0f-85b9-5b0a3e5bd772\",\"optionName\":\"@label\",\"value\":\"Closure\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b506621d-e2de-44d3-8e7a-4cb3cc1d0e41\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"67ee8f4b-00e6-4356-8de3-f6b56d07f0a0\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"85541dab-c069-4250-83f7-32edd9967454\",\"optionName\":\"closureReasonVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d68ea079-3467-4c76-8b39-f77326203892\",\"optionName\":\"reversalReasonVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"26b93cce-2248-4674-837d-a8cfa97719d0\",\"optionName\":\"executionHubVIS\",\"value\":\"None\"}],\"viewUUID\":\"64.f0c268ac-0772-4735-af5b-5fc6caec30a1\",\"binding\":\"tw.local.odcRequest.ReversalReason\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"e4c2ed50-5b75-4bc4-8115-221d876da2da\",\"version\":\"8550\"},{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f4d010bc-**************-c32c63f56c02\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b959f472-2efe-4fe8-8843-589f9f30d2d3\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d2bfd12b-b7f7-4bf4-8755-1884955f3c0b\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a8ee2635-f50e-4f91-8c14-ce235c3bddca\",\"optionName\":\"parentRequestNoVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cb5dd3b6-a45b-4866-8096-c41f859f4282\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a24e968e-3bb2-4838-8e8b-71aec76c7764\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3685597e-fe85-4a1e-8caa-997bc2ab4697\",\"optionName\":\"contractStageVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"01f7ffff-7c28-417e-80b4-deba80b56db8\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"None\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"d63eb156-dceb-4c59-8ffb-26cac564c4ec\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6b3b30d4-0183-4200-8703-b31b0ac039d8\",\"optionName\":\"@label\",\"value\":\"Customer Information\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e3bffc1c-23aa-4595-8e06-9d1c38e44eaa\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ff2d7713-5481-4326-8e70-728b39ab1306\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"462c1f23-cdc5-4764-8622-389bfa4c03e3\",\"optionName\":\"customerInfoVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f062e598-64e4-42dd-83a7-5dceeb5075f8\",\"optionName\":\"listsVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"d911c4f2-fa26-4d47-8bf9-f2916adb23f6\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5ce247c2-7b5a-4a23-846e-1747bf6a1680\",\"optionName\":\"@label\",\"value\":\"Financial Details Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a30bf930-cbb2-45af-8b76-676773511ac7\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cbe0fec7-a861-4a21-88df-8a569e1872fd\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"37b9ebc2-c6d3-42de-8fd0-76fd7855e662\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"944416c7-6860-4d08-811e-a9ba99a503a1\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"READONLY\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"df0d268f-ea7d-45bc-8bd9-677d367380af\",\"version\":\"8550\"},{\"layoutItemId\":\"FC_Collections_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9d4983e9-679e-44cb-8d4e-4b4f8b4f7b63\",\"optionName\":\"@label\",\"value\":\"FC Collections\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6a93af79-4b52-4755-8236-0e0134bcefc5\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"36ab4d00-eb2d-4fd2-82c6-d4be8ee40fce\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"69b654cb-c642-4642-8172-be5d65085044\",\"optionName\":\"FCVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2ef52995-6ab7-4e42-8ba2-a4ff995bcb90\",\"optionName\":\"retrieveBtnVis\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"eebfa298-8e3f-43f3-81c7-6ac298abba2e\",\"optionName\":\"addBtnVIS\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"18e45b03-f3e5-4ae7-83bc-f5917528b74a\",\"optionName\":\"customerCif\",\"value\":\"tw.local.odcRequest.cif\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9076ad8d-96a8-4ae7-8a85-11f8b15c45ba\",\"optionName\":\"collectionCurrencyVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2c9384ce-ef3e-44e5-864c-7ceba8d33be3\",\"optionName\":\"negotiatedExchangeRateVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b99e9e42-ea0f-49fe-8bf2-5df03adff4f6\",\"optionName\":\"@width\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b536c604-908c-42f2-8e4c-c3d75804160d\",\"optionName\":\"requestCurrency\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"050530f1-845e-456c-860b-aaae4bd09e33\",\"optionName\":\"activityType\",\"value\":\"read\"}],\"viewUUID\":\"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe\",\"binding\":\"tw.local.odcRequest.FcCollections\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"541981d9-f788-4c74-869a-5eaa9f09393c\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cf9e0f54-64e6-4ad0-8417-ce1e081d4791\",\"optionName\":\"@label\",\"value\":\"Attachment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"847d2850-8dfd-4c03-8479-711ba3a02d54\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cbaaadd8-f5a0-493b-893a-53ce560b30f5\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"26713b52-a7ff-43af-8697-316e0e702b3e\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"855a137b-6ea9-47cb-8f11-204929c8a310\",\"optionName\":\"canCreate\",\"value\":\"false\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"1e284601-1696-4940-86ed-bb5cc36c70e6\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"61c1d0f6-d700-46ee-8e34-09f157c90d9d\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3497825a-b5e9-43ad-8ed3-2622b0753a94\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4a902fe8-ba77-4e81-8ebe-255cc335bc4f\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a324007d-0692-4acd-8643-56c7c2692a05\",\"optionName\":\"@visibility.script\",\"value\":\"{\\\"@class\\\":\\\"com.ibm.bpm.coachNG.visibility.VisibilityRules\\\",\\\"rules\\\":[{\\\"@class\\\":\\\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\\\",\\\"var_conditions\\\":[{\\\"timeInMs\\\":null,\\\"var\\\":\\\"tw.local.isFirstTime\\\",\\\"operand\\\":true,\\\"operator\\\":0}],\\\"action\\\":\\\"NONE\\\"}],\\\"defaultAction\\\":\\\"DEFAULT\\\"}\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"8edc7ed0-14f6-4167-8b40-0882c1ddc81c\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"d963195c-65fd-402b-813f-4b5b5dd13a52\"}],\"layoutItemId\":\"Tab_Section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4dd73a93-c058-449f-87db-db34ba5f34db\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a7513c15-25d8-4c71-81cf-21c665963843\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d0c017a9-2d40-4130-85c4-82b0705883f1\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"90c09f12-ab54-447e-8bcc-56004f6ecbbf\",\"optionName\":\"@width\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"01a60c4b-f642-4558-88b7-ba1eac77f345\",\"optionName\":\"sizeStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"D\\\"}]}\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"533ed613-4620-42bc-8e48-6196a9503a62\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"*************-4bf1-846d-88ca49541cd8\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6478af14-23eb-457c-8eec-e75207660394\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"78e57922-7882-4e0e-8a17-16d4bf57ab5b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7988b959-d756-4e23-87c4-6ef2cff0079e\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"eafec3a3-5c91-4565-827c-078800b0e05c\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1bb99933-7212-4677-815b-fdd1ee69e63a\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3b7ce402-0cd4-4462-8e7f-b21374bcf68c\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"78cc7991-f8c1-4951-8506-744aec4c7363\",\"optionName\":\"complianceApprovalVis\",\"value\":\"None\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"de6ea7cd-f323-47eb-8f83-bf45907e0fd9\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2d488675-b84d-4689-8d01-3010acf1c113\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b753e910-66e9-4055-8eac-12be4b5b7346\",\"optionName\":\"terminateReasonVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"127fa9e6-7b9b-42a8-8553-55d9f1b3381e\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e7578efb-0634-41a7-8d0b-1e7866ceb77b\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f375f891-5010-418b-80a4-d5472f0baaff\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"None\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"a64d4626-f501-4de7-87c4-d889f981b436\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Review ODC Closure Request\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.c85f3552-61a8-4721-8b4d-3b45446d865d\"},{\"targetRef\":\"2025.c85f3552-61a8-4721-8b4d-3b45446d865d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Review ODC Closure Request\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.994807e1-1654-4eba-8d9b-ad6573a186b0\",\"sourceRef\":\"2025.6bc93cfb-159e-448b-8f79-3c02066338d9\"},{\"outgoing\":[\"2027.1786cdbf-044c-438b-8ef6-499a46a76538\"],\"incoming\":[\"2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1022,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.1786cdbf-044c-438b-8ef6-499a46a76538\",\"name\":\"History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\" \\\"Branch \\/ Hub Compliance Rep\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.215786c9-52ee-4367-80d3-08665cd9809d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"startQuantity\":1,\"outgoing\":[\"2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546\"],\"incoming\":[\"2027.83029c21-962f-4cf5-8d57-6a905495b53f\"],\"default\":\"2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":865,\"y\":177,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"setting Status and substatus\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.c0f030cf-b8a3-4cb6-8a5d-0c6450ad6744\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\\r\\n{\\r\\n\\ttw.local.odcRequest.appInfo.status    =\\\"In Approval\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\" Awaiting Trade FO Approval\\\";\\r\\n}\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)\\r\\n{\\r\\n\\ttw.local.odcRequest.appInfo.status    =\\\"Cancelled\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\"Cancelled\\\";\\r\\n\\t\\r\\n\\ttw.local.odcRequest.BasicDetails.requestState = \\\"Final\\\";\\r\\n}\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\\r\\n{\\r\\n\\ttw.local.odcRequest.appInfo.status    =\\\"Initiated\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\"Return to initiator\\\";\\r\\n}\"]}},{\"targetRef\":\"2025.215786c9-52ee-4367-80d3-08665cd9809d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To History\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546\",\"sourceRef\":\"2025.c0f030cf-b8a3-4cb6-8a5d-0c6450ad6744\"},{\"startQuantity\":1,\"outgoing\":[\"2027.328b2c9b-f49d-4c46-86bb-e461713766e6\"],\"incoming\":[\"2027.7fd4298f-fe10-4701-88ad-e592f00ec68d\"],\"default\":\"2027.328b2c9b-f49d-4c46-86bb-e461713766e6\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":531,\"y\":178,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"validation script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.a552d3c1-196a-417f-826f-2edc941f16be\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\" tw.local.errorMessage =\\\"\\\";\\r\\n var mandatoryTriggered = false;\\r\\n \\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/\\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\\r\\nmandatory(tw.local.odcRequest.stepLog.action, \\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\\r\\n{\\r\\n\\tmandatory(tw.local.odcRequest.stepLog.returnReason , \\\"tw.local.odcRequest.stepLog.returnReason\\\");\\r\\n}\\t\\r\\n\\r\\nfunction mandatory(field , fieldName)\\r\\n{\\r\\n\\tif (field == null || field == undefined )\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\t\\t\\t\\r\\n\\t\\tswitch (typeof field)\\r\\n\\t\\t{\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\r\\n\\t\\t\\tdefault:\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\/\\/ VALIDATE DATE OBJECT\\r\\n\\t\\t\\t\\tif( field && field.getTime && isFinite(field.getTime()) ) {}\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\telse\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\t\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n{\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field.length > len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\t\"]}},{\"outgoing\":[\"2027.a9fb5525-6abd-4c3b-813d-49241493765e\"],\"incoming\":[\"2027.16d27c63-04c6-48e0-85f2-91efdeeeb67f\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.a9fb5525-6abd-4c3b-813d-49241493765e\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":331,\"y\":306,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.63d7c4a3-bde9-4651-8413-b95441b0e310\"},{\"targetRef\":\"2025.63d7c4a3-bde9-4651-8413-b95441b0e310\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"40d4bd8d-2932-4141-80ff-36d2d1deaff0\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomRight\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.16d27c63-04c6-48e0-85f2-91efdeeeb67f\",\"sourceRef\":\"2025.c85f3552-61a8-4721-8b4d-3b45446d865d\"},{\"targetRef\":\"2025.c85f3552-61a8-4721-8b4d-3b45446d865d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"leftCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Review ODC Closure Request\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.a9fb5525-6abd-4c3b-813d-49241493765e\",\"sourceRef\":\"2025.63d7c4a3-bde9-4651-8413-b95441b0e310\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.fd8896db-127b-4b17-8f95-6a80c9fb18e8\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.79e1cdcd-1a4a-4811-8ce8-7cfe8e4e3d11\"},{\"outgoing\":[\"2027.8c2856d4-d624-40f8-8570-66de917beee4\",\"2027.83029c21-962f-4cf5-8d57-6a905495b53f\"],\"incoming\":[\"2027.328b2c9b-f49d-4c46-86bb-e461713766e6\"],\"default\":\"2027.8c2856d4-d624-40f8-8570-66de917beee4\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":47,\"x\":743,\"y\":196,\"declaredType\":\"TNodeVisualInfo\",\"height\":71}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.f45ffe81-0484-4595-8cbf-78180725c5fe\"},{\"targetRef\":\"2025.a552d3c1-196a-417f-826f-2edc941f16be\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"8c24ea14-4aff-4764-8f08-01f3b3c6058c\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7fd4298f-fe10-4701-88ad-e592f00ec68d\",\"sourceRef\":\"2025.c85f3552-61a8-4721-8b4d-3b45446d865d\"},{\"incoming\":[\"2027.8c2856d4-d624-40f8-8570-66de917beee4\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":728,\"y\":101,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.48b4cfea-6283-41dd-884e-35244ab28520\"},{\"targetRef\":\"2025.48b4cfea-6283-41dd-884e-35244ab28520\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8c2856d4-d624-40f8-8570-66de917beee4\",\"sourceRef\":\"2025.f45ffe81-0484-4595-8cbf-78180725c5fe\"},{\"targetRef\":\"2025.c0f030cf-b8a3-4cb6-8a5d-0c6450ad6744\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.83029c21-962f-4cf5-8d57-6a905495b53f\",\"sourceRef\":\"2025.f45ffe81-0484-4595-8cbf-78180725c5fe\"},{\"targetRef\":\"2025.f45ffe81-0484-4595-8cbf-78180725c5fe\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.328b2c9b-f49d-4c46-86bb-e461713766e6\",\"sourceRef\":\"2025.a552d3c1-196a-417f-826f-2edc941f16be\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"role\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a09c9c9c-d6d3-48e2-8ade-ca39886be668\"},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1201,\"y\":327,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Audit closure data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.83426577-e8ed-4bb7-9c84-04dc61b1c555\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.e637508c-aacd-4f06-916d-0007180c9ae8\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNature.value\"]}}]},{\"targetRef\":\"2055.a3f14482-9fa2-41dd-8778-5801541b89f7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.value\"]}}]},{\"targetRef\":\"2055.a81e8ea0-65ba-42d9-bac7-cc75f506d145\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ReversalReason.closureReason\"]}}]},{\"targetRef\":\"2055.0b5bfb57-fc6b-4d31-a97e-91edb6a3eaed\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.414b79a9-cfce-497a-9182-a8efef0ca43c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.5d4e4c94-71d4-42e1-a17a-1757c845c500\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]},{\"targetRef\":\"2055.3bee4118-8b6a-4d54-8891-4d307f7c59b9\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"false\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.571f6642-fc7c-4f9d-8179-d754278a85b5\",\"calledElement\":\"1.badb27e5-ab0e-4227-bebf-eb6d54984f36\"},{\"outgoing\":[\"2027.e9d7a6e3-d13b-42fc-860c-dfbd796fd49c\",\"2027.68f1148c-f1c0-4f37-8a63-9706a8544c87\"],\"incoming\":[\"2027.1786cdbf-044c-438b-8ef6-499a46a76538\"],\"default\":\"2027.68f1148c-f1c0-4f37-8a63-9706a8544c87\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1210,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.802539d1-9dec-4b1c-82dc-11beed907565\"},{\"targetRef\":\"2025.802539d1-9dec-4b1c-82dc-11beed907565\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1786cdbf-044c-438b-8ef6-499a46a76538\",\"sourceRef\":\"2025.215786c9-52ee-4367-80d3-08665cd9809d\"},{\"targetRef\":\"2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e9d7a6e3-d13b-42fc-860c-dfbd796fd49c\",\"sourceRef\":\"2025.802539d1-9dec-4b1c-82dc-11beed907565\"},{\"incoming\":[\"2027.68f1148c-f1c0-4f37-8a63-9706a8544c87\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1299,\"y\":245,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.cb62a3ac-fcbe-41fb-8798-d16062a15033\"},{\"targetRef\":\"2025.cb62a3ac-fcbe-41fb-8798-d16062a15033\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.68f1148c-f1c0-4f37-8a63-9706a8544c87\",\"sourceRef\":\"2025.802539d1-9dec-4b1c-82dc-11beed907565\"},{\"outgoing\":[\"2027.341f7459-**************-1b14cf143ae2\",\"2027.e597c20b-d9ce-43ed-87da-57ad48682d5c\"],\"incoming\":[\"2027.e9d7a6e3-d13b-42fc-860c-dfbd796fd49c\"],\"default\":\"2027.341f7459-**************-1b14cf143ae2\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1420,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"cancel\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b\"},{\"targetRef\":\"c55b6291-c6e2-49d4-b9e3-b031ef8f7b55\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.341f7459-**************-1b14cf143ae2\",\"sourceRef\":\"2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b\"},{\"outgoing\":[\"2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3\"],\"incoming\":[\"2027.e597c20b-d9ce-43ed-87da-57ad48682d5c\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1527,\"y\":27,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3\",\"name\":\"cancel request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.55a76aa1-e513-4fd3-835f-7fe160120af7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.77d2b3d6-2a2a-4520-ad08-31686157d431\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderID\"]}}]},{\"targetRef\":\"2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a\"]}],\"calledElement\":\"1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc\"},{\"targetRef\":\"2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.cancelRequest\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To cancel request\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e597c20b-d9ce-43ed-87da-57ad48682d5c\",\"sourceRef\":\"2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b\"},{\"targetRef\":\"2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3\",\"sourceRef\":\"2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.e9cd9534-1c97-4e32-859a-************\"},{\"incoming\":[\"2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1699,\"y\":-7,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 3\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.2d0d2e15-f5e2-4e42-8e16-00cd15179ebd\"},{\"outgoing\":[\"2027.81349889-6cdc-443a-8c26-74e33cd0f398\",\"2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d\"],\"incoming\":[\"2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3\"],\"default\":\"2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1717,\"y\":112,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3\"},{\"targetRef\":\"c55b6291-c6e2-49d4-b9e3-b031ef8f7b55\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Copy of To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.81349889-6cdc-443a-8c26-74e33cd0f398\",\"sourceRef\":\"2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3\"},{\"targetRef\":\"2025.2d0d2e15-f5e2-4e42-8e16-00cd15179ebd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Copy of To Stay on page 3\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d\",\"sourceRef\":\"2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"e2d2f537-21a1-4306-a37d-c825fe9b7d13\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"a5f97547-52af-4622-9753-4ace90509675\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"ClosureACT02 - Review ODC Closure Request by Compliance Rep\",\"declaredType\":\"globalUserTask\",\"id\":\"1.0694c1c7-863e-4809-8c83-bafbba3eb2bb\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.cd4ee7b8-3ac7-4a16-8a0b-023b2838e7f1\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.f37777d2-cd75-44bd-8254-e8f8859e1415\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.93c5c002-7ac4-4283-83ee-63b8662f9223\",\"epvProcessLinkId\":\"2dbda4d3-814d-4db7-8f31-ad87e19f941f\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"11a5abc0-a8a9-4226-85b9-a4997b11884c\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"19a12628-306b-4494-8ea5-bbeee9d6720e\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"80d5b00e-b8b2-4012-812d-8372aef64b2e\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"_6bc0899d-0bcb-4755-93df-a13982ea1f6c\"}],\"outputSet\":[{\"id\":\"_e5fe2948-5bf5-4c76-ac77-481b6787840c\"}],\"dataInput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.d4fa9d21-5e1a-4996-8745-6d9b1ba1d005\"},{\"itemSubjectRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"name\":\"routingDetails\",\"isCollection\":false,\"id\":\"2055.cda6b97c-96b9-488b-8055-47d670e552ac\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"lastAction\",\"isCollection\":false,\"id\":\"2055.a3159314-99a0-4745-8a3b-4be34b9b6f7b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.790399ab-7b67-4adc-8647-410880393efa\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"38d28a47-e970-4d05-9401-aebf7d53957b\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d4fa9d21-5e1a-4996-8745-6d9b1ba1d005", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "ad33b2ac-c801-46c4-8c0b-cf01f5b17b6f", "versionId": "d7780542-e670-4117-bb58-dce3930242f3"}, {"name": "routingDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.cda6b97c-96b9-488b-8055-47d670e552ac", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "parameterType": "1", "isArrayOf": "false", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "2357702c-4e60-4c1c-9b13-3bb153719088", "versionId": "20e1d9ca-5696-4f32-9a6d-1e4f1ae3983d"}, {"name": "lastAction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a3159314-99a0-4745-8a3b-4be34b9b6f7b", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "fbf46c5c-204f-4f70-bd5e-b98f91bb645d", "versionId": "e74da21d-dde3-495a-b1a9-a2d3a31a3d86"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.790399ab-7b67-4adc-8647-410880393efa", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "17c77230-07dc-4057-a7c2-fb6053d83a33", "versionId": "bf9cdcd3-b40c-4ecc-a1ba-d7f2e025ad65"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.cd4ee7b8-3ac7-4a16-8a0b-023b2838e7f1", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "47e742db-820c-4dc0-8e94-6e6ca7c4e438", "versionId": "96f47ee8-c826-4101-ad6b-2d6f46b45432"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.fd8896db-127b-4b17-8f95-6a80c9fb18e8", "description": {"isNull": "true"}, "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "86caef61-c01d-4d4c-900d-ff382863527c", "versionId": "395879c4-80b8-4f3b-aa50-75e49c41d54d"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.79e1cdcd-1a4a-4811-8ce8-7cfe8e4e3d11", "description": {"isNull": "true"}, "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "8bbbdc4e-8037-41ec-bf8b-334c5e0a77a0", "versionId": "1c51d56d-c8c9-475d-96f5-cc2ed91c8041"}, {"name": "role", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a09c9c9c-d6d3-48e2-8ade-ca39886be668", "description": {"isNull": "true"}, "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "75d94ca4-bb84-4aa5-86e8-4ff96aecf765", "versionId": "76ef6574-6b11-4889-aaee-c5123b77f698"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.e9cd9534-1c97-4e32-859a-************", "description": {"isNull": "true"}, "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "028eed9b-c0bc-4537-9190-ed654a528ef9", "versionId": "b2bcf124-5859-4bc9-9dc8-20d87872fbdc"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0b2eb5e6-5cc9-45e0-98ef-80586118baf8", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.68d7b763-6df4-4216-be53-38210fbdb861", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-4304", "versionId": "93df70bc-d79e-4687-a2a0-7ee1941fb8ff", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.68d7b763-6df4-4216-be53-38210fbdb861", "haltProcess": "false", "guid": "c2cf4397-d110-4e53-93c2-d1950a1aa992", "versionId": "735ee450-4b7e-4e04-a95d-17068f007d27"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.215786c9-52ee-4367-80d3-08665cd9809d", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "name": "History", "tWComponentName": "SubProcess", "tWComponentId": "3012.963a7a02-d7f6-4130-95b6-b49cac27a849", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fc53d6da47fd17d0:63ae7219:18a68bd8c93:7f1a", "versionId": "acb01705-22e5-45b0-a500-4c9f62dd0aa2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.963a7a02-d7f6-4130-95b6-b49cac27a849", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "5dbf7da8-6de8-4825-9d13-3cf1a1903ae5", "versionId": "2aa87c20-eb8e-45f3-8cba-3d7fc40ab0a6"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.571f6642-fc7c-4f9d-8179-d754278a85b5", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "name": "Audit closure data", "tWComponentName": "SubProcess", "tWComponentId": "3012.b2289f37-3d3f-4ab5-aaee-9f117c916619", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:989374ae3827db3a:f13aa6c:18aa2e844f6:-7a69", "versionId": "cbaadd3a-4d1c-4c2c-ae3f-88f4fa7710b7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.b2289f37-3d3f-4ab5-aaee-9f117c916619", "attachedProcessRef": "/1.badb27e5-ab0e-4227-bebf-eb6d54984f36", "guid": "3dc9c724-6a13-44a6-b8f6-b59e92c42c5d", "versionId": "b43c75b7-760c-43bc-af7d-a826bf4ae5a9"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "name": "cancel request", "tWComponentName": "SubProcess", "tWComponentId": "3012.4554446b-f812-42bf-87b1-ddbecbd0ff34", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7cff", "versionId": "d0cd5168-51ee-4ec7-94c4-26a54c90c5e4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4554446b-f812-42bf-87b1-ddbecbd0ff34", "attachedProcessRef": "/1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "guid": "0203547f-51e7-4494-aca3-55ad152ef8c0", "versionId": "facd12d7-b8df-42e9-ab39-13424ca22bb6"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.34c32d45-9e61-40e4-a065-399699c34372", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.528c0d27-d275-490e-acbd-da701f9e51dc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-4303", "versionId": "f328a62d-2dad-4a57-98ad-9aa479b74928", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.684084a6-8087-4e37-82ba-ffc5647d3405", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "guid": "d1272c5d-cc95-4d51-90ed-42abd007c9e8", "versionId": "4aae65aa-4b94-487a-9792-be2c71cf5326"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.c78a0fe8-66e0-4116-b395-cafae82126b8", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "guid": "958d691f-1e2c-4b2b-81d8-88dde31af5ce", "versionId": "e5f93b83-968b-41e3-b498-7cb7ce20be4f"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.63d2e489-cde2-4d5f-b678-7d758905dcfa", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "guid": "ba2275ff-391d-4111-a01d-e20eb260a669", "versionId": "eabe9106-60c6-4906-a05b-a7fad24584c2"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.3b66137a-98aa-4bcb-9411-84ed64f1bac5", "epvId": "/21.93c5c002-7ac4-4283-83ee-63b8662f9223", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "guid": "79beb82a-c8bb-412b-a1d0-a14060c21fd7", "versionId": "f43fb8a8-a41a-4946-9bf0-19602e0e9521"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.05272358-4376-4709-bba0-d0e544fe7f44", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "guid": "4e6d75c0-503e-46d7-a68a-ae6a4aa894f6", "versionId": "9cb38f31-d2d8-4642-aac0-92e92216af87"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "38d28a47-e970-4d05-9401-aebf7d53957b", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "ClosureACT02 - Review ODC Closure Request by Compliance Rep", "id": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "a5f97547-52af-4622-9753-4ace90509675", "ns16:startEvent": {"name": "Start", "id": "cfce49bc-91d9-4143-aca1-68775a9bbd8e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "50", "y": "200", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.8542666b-8e40-4716-97b8-c95406ef1f83"}, "ns16:endEvent": {"name": "End", "id": "c55b6291-c6e2-49d4-b9e3-b031ef8f7b55", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1779", "y": "199", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.341f7459-**************-1b14cf143ae2", "2027.81349889-6cdc-443a-8c26-74e33cd0f398"]}, "ns16:sequenceFlow": [{"sourceRef": "cfce49bc-91d9-4143-aca1-68775a9bbd8e", "targetRef": "2025.6bc93cfb-159e-448b-8f79-3c02066338d9", "name": "To Coach", "id": "2027.8542666b-8e40-4716-97b8-c95406ef1f83", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}}}, {"sourceRef": "2025.6bc93cfb-159e-448b-8f79-3c02066338d9", "targetRef": "2025.c85f3552-61a8-4721-8b4d-3b45446d865d", "name": "To Review ODC Closure Request", "id": "2027.994807e1-1654-4eba-8d9b-ad6573a186b0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c0f030cf-b8a3-4cb6-8a5d-0c6450ad6744", "targetRef": "2025.215786c9-52ee-4367-80d3-08665cd9809d", "name": "To History", "id": "2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c85f3552-61a8-4721-8b4d-3b45446d865d", "targetRef": "2025.63d7c4a3-bde9-4651-8413-b95441b0e310", "name": "To Postpone", "id": "2027.16d27c63-04c6-48e0-85f2-91efdeeeb67f", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomRight", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "40d4bd8d-2932-4141-80ff-36d2d1deaff0", "ns3:coachEventPath": "DC_Templete1/saveState"}}}, {"sourceRef": "2025.63d7c4a3-bde9-4651-8413-b95441b0e310", "targetRef": "2025.c85f3552-61a8-4721-8b4d-3b45446d865d", "name": "To Review ODC Closure Request", "id": "2027.a9fb5525-6abd-4c3b-813d-49241493765e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "leftCenter", "ns13:targetPortLocation": "bottomLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c85f3552-61a8-4721-8b4d-3b45446d865d", "targetRef": "2025.a552d3c1-196a-417f-826f-2edc941f16be", "name": "To Valid?", "id": "2027.7fd4298f-fe10-4701-88ad-e592f00ec68d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "8c24ea14-4aff-4764-8f08-01f3b3c6058c", "ns3:coachEventPath": "DC_Templete1/submit"}}}, {"sourceRef": "2025.f45ffe81-0484-4595-8cbf-78180725c5fe", "targetRef": "2025.48b4cfea-6283-41dd-884e-35244ab28520", "name": "no", "id": "2027.8c2856d4-d624-40f8-8570-66de917beee4", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.f45ffe81-0484-4595-8cbf-78180725c5fe", "targetRef": "2025.c0f030cf-b8a3-4cb6-8a5d-0c6450ad6744", "name": "yes", "id": "2027.83029c21-962f-4cf5-8d57-6a905495b53f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.a552d3c1-196a-417f-826f-2edc941f16be", "targetRef": "2025.f45ffe81-0484-4595-8cbf-78180725c5fe", "name": "To Valid?", "id": "2027.328b2c9b-f49d-4c46-86bb-e461713766e6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.215786c9-52ee-4367-80d3-08665cd9809d", "targetRef": "2025.802539d1-9dec-4b1c-82dc-11beed907565", "name": "To audited?", "id": "2027.1786cdbf-044c-438b-8ef6-499a46a76538", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.802539d1-9dec-4b1c-82dc-11beed907565", "targetRef": "2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b", "name": "Yes", "id": "2027.e9d7a6e3-d13b-42fc-860c-dfbd796fd49c", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.802539d1-9dec-4b1c-82dc-11beed907565", "targetRef": "2025.cb62a3ac-fcbe-41fb-8798-d16062a15033", "name": "No", "id": "2027.68f1148c-f1c0-4f37-8a63-9706a8544c87", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b", "targetRef": "c55b6291-c6e2-49d4-b9e3-b031ef8f7b55", "name": "No", "id": "2027.341f7459-**************-1b14cf143ae2", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b", "targetRef": "2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a", "name": "To cancel request", "id": "2027.e597c20b-d9ce-43ed-87da-57ad48682d5c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a", "targetRef": "2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3", "name": "To End", "id": "2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3", "targetRef": "c55b6291-c6e2-49d4-b9e3-b031ef8f7b55", "name": "<PERSON><PERSON> of To End", "id": "2027.81349889-6cdc-443a-8c26-74e33cd0f398", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3", "targetRef": "2025.2d0d2e15-f5e2-4e42-8e16-00cd15179ebd", "name": "Co<PERSON> of To Stay on page 3", "id": "2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "default": "2027.994807e1-1654-4eba-8d9b-ad6573a186b0", "name": "init Script", "id": "2025.6bc93cfb-159e-448b-8f79-3c02066338d9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "172", "y": "177", "width": "95", "height": "70"}}, "ns16:incoming": "2027.8542666b-8e40-4716-97b8-c95406ef1f83", "ns16:outgoing": "2027.994807e1-1654-4eba-8d9b-ad6573a186b0", "ns16:script": "tw.local.odcRequest.stepLog ={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step      = tw.epv.ScreenNames.closureACT02;//\" Review ODC Closure Request by Compliance Rep – مراجعة طلب اقفال تحصيل مستندى تصدير من ممثل الإلتزام \";\r\r\n\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= false;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT02;\r\r\ntw.local.actionConditions.lastStepAction = tw.local.lastAction;\r\r\n\r\r\n///////////////*Initializing Request header*//////////////////////////\r\r\n//////////////////////////////////////////////////////////////////////\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\r\r\n//tw.local.odcRequest.appInfo.branch = {};\r\r\n\r\r\n\t\r\r\n\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\n\r\r\n\ttw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT02;\r\r\n\r\r\n\tif(tw.local.routingDetails.branchCode == \"\" && tw.local.routingDetails.branchCode == null)\r\r\n\t{\r\r\n\t\ttw.local.role = tw.epv.userRole.hubComp; //\"Hub Compliance Rep\";\t\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\r\r\n\t\ttw.local.role =  tw.epv.userRole.branchComp;//\" Branch Compliance Rep \";\r\r\n\t}"}, {"scriptFormat": "text/x-javascript", "default": "2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546", "name": "setting Status and substatus", "id": "2025.c0f030cf-b8a3-4cb6-8a5d-0c6450ad6744", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "865", "y": "177", "width": "95", "height": "70"}}, "ns16:incoming": "2027.83029c21-962f-4cf5-8d57-6a905495b53f", "ns16:outgoing": "2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546", "ns16:script": "if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\" Awaiting Trade FO Approval\";\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"Cancelled\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Cancelled\";\r\r\n\t\r\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Final\";\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"Initiated\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Return to initiator\";\r\r\n}"}, {"scriptFormat": "text/x-javascript", "default": "2027.328b2c9b-f49d-4c46-86bb-e461713766e6", "name": "validation script", "id": "2025.a552d3c1-196a-417f-826f-2edc941f16be", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "531", "y": "178", "width": "95", "height": "70", "color": "#95D087"}}, "ns16:incoming": "2027.7fd4298f-fe10-4701-88ad-e592f00ec68d", "ns16:outgoing": "2027.328b2c9b-f49d-4c46-86bb-e461713766e6", "ns16:script": " tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n \r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\r\n{\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason , \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n}\t\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\t"}], "ns3:formTask": {"name": "Review ODC Closure Request", "id": "2025.c85f3552-61a8-4721-8b4d-3b45446d865d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "311", "y": "178", "width": "95", "height": "70"}, "ns3:validationStayOnPagePaths": "okbutton"}, "ns16:incoming": ["2027.994807e1-1654-4eba-8d9b-ad6573a186b0", "2027.a9fb5525-6abd-4c3b-813d-49241493765e"], "ns16:outgoing": ["2027.16d27c63-04c6-48e0-85f2-91efdeeeb67f", "2027.7fd4298f-fe10-4701-88ad-e592f00ec68d"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "a64d4626-f501-4de7-87c4-d889f981b436", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "6478af14-23eb-457c-8eec-e75207660394", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "78e57922-7882-4e0e-8a17-16d4bf57ab5b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "7988b959-d756-4e23-87c4-6ef2cff0079e", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "eafec3a3-5c91-4565-827c-078800b0e05c", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "1bb99933-7212-4677-815b-fdd1ee69e63a", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "3b7ce402-0cd4-4462-8e7f-b21374bcf68c", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "78cc7991-f8c1-4951-8506-744aec4c7363", "ns19:optionName": "complianceApprovalVis", "ns19:value": "None"}, {"ns19:id": "de6ea7cd-f323-47eb-8f83-bf45907e0fd9", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "2d488675-b84d-4689-8d01-3010acf1c113", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "b753e910-66e9-4055-8eac-12be4b5b7346", "ns19:optionName": "terminateReasonVIS", "ns19:value": "NONE"}, {"ns19:id": "127fa9e6-7b9b-42a8-8553-55d9f1b3381e", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "e7578efb-0634-41a7-8d0b-1e7866ceb77b", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "None"}, {"ns19:id": "f375f891-5010-418b-80a4-d5472f0baaff", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "None"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "*************-4bf1-846d-88ca49541cd8", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "533ed613-4620-42bc-8e48-6196a9503a62", "ns19:layoutItemId": "Tab_Section1", "ns19:configData": [{"ns19:id": "4dd73a93-c058-449f-87db-db34ba5f34db", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "a7513c15-25d8-4c71-81cf-21c665963843", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "d0c017a9-2d40-4130-85c4-82b0705883f1", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "90c09f12-ab54-447e-8bcc-56004f6ecbbf", "ns19:optionName": "@width", "ns19:value": "{\"isResponsiveData\":true,\"values\":[]}"}, {"ns19:id": "01a60c4b-f642-4558-88b7-ba1eac77f345", "ns19:optionName": "sizeStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "d963195c-65fd-402b-813f-4b5b5dd13a52", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "e4c2ed50-5b75-4bc4-8115-221d876da2da", "ns19:layoutItemId": "Reversal_Closure_CV1", "ns19:configData": [{"ns19:id": "02207c68-02f4-4f0f-85b9-5b0a3e5bd772", "ns19:optionName": "@label", "ns19:value": "Closure"}, {"ns19:id": "b506621d-e2de-44d3-8e7a-4cb3cc1d0e41", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "67ee8f4b-00e6-4356-8de3-f6b56d07f0a0", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "85541dab-c069-4250-83f7-32edd9967454", "ns19:optionName": "closureReasonVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "d68ea079-3467-4c76-8b39-f77326203892", "ns19:optionName": "reversalReasonVIS", "ns19:value": "None"}, {"ns19:id": "26b93cce-2248-4674-837d-a8cfa97719d0", "ns19:optionName": "executionHubVIS", "ns19:value": "None"}], "ns19:viewUUID": "64.f0c268ac-0772-4735-af5b-5fc6caec30a1", "ns19:binding": "tw.local.odcRequest.ReversalReason"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "d63eb156-dceb-4c59-8ffb-26cac564c4ec", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "f4d010bc-**************-c32c63f56c02", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "b959f472-2efe-4fe8-8843-589f9f30d2d3", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "d2bfd12b-b7f7-4bf4-8755-1884955f3c0b", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "a8ee2635-f50e-4f91-8c14-ce235c3bddca", "ns19:optionName": "parentRequestNoVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "cb5dd3b6-a45b-4866-8096-c41f859f4282", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "a24e968e-3bb2-4838-8e8b-71aec76c7764", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "None"}, {"ns19:id": "3685597e-fe85-4a1e-8caa-997bc2ab4697", "ns19:optionName": "contractStageVIS", "ns19:value": "None"}, {"ns19:id": "01f7ffff-7c28-417e-80b4-deba80b56db8", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "None"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "d911c4f2-fa26-4d47-8bf9-f2916adb23f6", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "6b3b30d4-0183-4200-8703-b31b0ac039d8", "ns19:optionName": "@label", "ns19:value": "Customer Information"}, {"ns19:id": "e3bffc1c-23aa-4595-8e06-9d1c38e44eaa", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "ff2d7713-5481-4326-8e70-728b39ab1306", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "462c1f23-cdc5-4764-8622-389bfa4c03e3", "ns19:optionName": "customerInfoVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "f062e598-64e4-42dd-83a7-5dceeb5075f8", "ns19:optionName": "listsVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "df0d268f-ea7d-45bc-8bd9-677d367380af", "ns19:layoutItemId": "Financial_Details_Branch1", "ns19:configData": [{"ns19:id": "5ce247c2-7b5a-4a23-846e-1747bf6a1680", "ns19:optionName": "@label", "ns19:value": "Financial Details Branch"}, {"ns19:id": "a30bf930-cbb2-45af-8b76-676773511ac7", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "cbe0fec7-a861-4a21-88df-8a569e1872fd", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "37b9ebc2-c6d3-42de-8fd0-76fd7855e662", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "READONLY"}, {"ns19:id": "944416c7-6860-4d08-811e-a9ba99a503a1", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "READONLY"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "541981d9-f788-4c74-869a-5eaa9f09393c", "ns19:layoutItemId": "FC_Collections_CV1", "ns19:configData": [{"ns19:id": "9d4983e9-679e-44cb-8d4e-4b4f8b4f7b63", "ns19:optionName": "@label", "ns19:value": "FC Collections"}, {"ns19:id": "6a93af79-4b52-4755-8236-0e0134bcefc5", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "36ab4d00-eb2d-4fd2-82c6-d4be8ee40fce", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "69b654cb-c642-4642-8172-be5d65085044", "ns19:optionName": "FCVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "2ef52995-6ab7-4e42-8ba2-a4ff995bcb90", "ns19:optionName": "retrieveBtnVis", "ns19:value": "NONE"}, {"ns19:id": "eebfa298-8e3f-43f3-81c7-6ac298abba2e", "ns19:optionName": "addBtnVIS", "ns19:value": "NONE"}, {"ns19:id": "18e45b03-f3e5-4ae7-83bc-f5917528b74a", "ns19:optionName": "customerCif", "ns19:value": "tw.local.odcRequest.cif", "ns19:valueType": "dynamic"}, {"ns19:id": "9076ad8d-96a8-4ae7-8a85-11f8b15c45ba", "ns19:optionName": "collectionCurrencyVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "2c9384ce-ef3e-44e5-864c-7ceba8d33be3", "ns19:optionName": "negotiatedExchangeRateVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "b99e9e42-ea0f-49fe-8bf2-5df03adff4f6", "ns19:optionName": "@width", "ns19:value": "{\"isResponsiveData\":true,\"values\":[]}"}, {"ns19:id": "b536c604-908c-42f2-8e4c-c3d75804160d", "ns19:optionName": "requestCurrency", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "050530f1-845e-456c-860b-aaae4bd09e33", "ns19:optionName": "activityType", "ns19:value": "read"}], "ns19:viewUUID": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "ns19:binding": "tw.local.odcRequest.FcCollections"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "1e284601-1696-4940-86ed-bb5cc36c70e6", "ns19:layoutItemId": "Attachment1", "ns19:configData": [{"ns19:id": "cf9e0f54-64e6-4ad0-8417-ce1e081d4791", "ns19:optionName": "@label", "ns19:value": "Attachment"}, {"ns19:id": "847d2850-8dfd-4c03-8479-711ba3a02d54", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "cbaaadd8-f5a0-493b-893a-53ce560b30f5", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "26713b52-a7ff-43af-8697-316e0e702b3e", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "855a137b-6ea9-47cb-8f11-204929c8a310", "ns19:optionName": "canCreate", "ns19:value": "false"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "8edc7ed0-14f6-4167-8b40-0882c1ddc81c", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "61c1d0f6-d700-46ee-8e34-09f157c90d9d", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "3497825a-b5e9-43ad-8ed3-2622b0753a94", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "4a902fe8-ba77-4e81-8ebe-255cc335bc4f", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "a324007d-0692-4acd-8643-56c7c2692a05", "ns19:optionName": "@visibility.script", "ns19:value": "{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}", "ns19:valueType": "static"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:callActivity": [{"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "default": "2027.1786cdbf-044c-438b-8ef6-499a46a76538", "name": "History", "id": "2025.215786c9-52ee-4367-80d3-08665cd9809d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1022", "y": "178", "width": "95", "height": "70"}}, "ns16:incoming": "2027.9b8e3bae-6851-4ae2-8ec0-55fac5b4c546", "ns16:outgoing": "2027.1786cdbf-044c-438b-8ef6-499a46a76538", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": " \"Branch / Hub Compliance Rep\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.badb27e5-ab0e-4227-bebf-eb6d54984f36", "name": "Audit closure data", "id": "2025.571f6642-fc7c-4f9d-8179-d754278a85b5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1201", "y": "327", "width": "95", "height": "70"}}, "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.83426577-e8ed-4bb7-9c84-04dc61b1c555", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.e637508c-aacd-4f06-916d-0007180c9ae8", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNature.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.a3f14482-9fa2-41dd-8778-5801541b89f7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.a81e8ea0-65ba-42d9-bac7-cc75f506d145", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ReversalReason.closureReason", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.0b5bfb57-fc6b-4d31-a97e-91edb6a3eaed", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.414b79a9-cfce-497a-9182-a8efef0ca43c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.5d4e4c94-71d4-42e1-a17a-1757c845c500", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.3bee4118-8b6a-4d54-8891-4d307f7c59b9", "ns16:assignment": {"ns16:from": {"_": "false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, {"calledElement": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3", "name": "cancel request", "id": "2025.03db019d-1cf5-4d84-8c2e-3ac6fd8c673a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1527", "y": "27", "width": "95", "height": "70"}}, "ns16:incoming": "2027.e597c20b-d9ce-43ed-87da-57ad48682d5c", "ns16:outgoing": "2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.55a76aa1-e513-4fd3-835f-7fe160120af7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.77d2b3d6-2a2a-4520-ad08-31686157d431", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:targetRef": "2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:intermediateThrowEvent": [{"name": "Postpone", "id": "2025.63d7c4a3-bde9-4651-8413-b95441b0e310", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "331", "y": "306", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.a9fb5525-6abd-4c3b-813d-49241493765e"}, "ns16:incoming": "2027.16d27c63-04c6-48e0-85f2-91efdeeeb67f", "ns16:outgoing": "2027.a9fb5525-6abd-4c3b-813d-49241493765e", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page", "id": "2025.48b4cfea-6283-41dd-884e-35244ab28520", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "728", "y": "101", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.8c2856d4-d624-40f8-8570-66de917beee4", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.cb62a3ac-fcbe-41fb-8798-d16062a15033", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1299", "y": "245", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.68f1148c-f1c0-4f37-8a63-9706a8544c87", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 3", "id": "2025.2d0d2e15-f5e2-4e42-8e16-00cd15179ebd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1699", "y": "-7", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d", "ns3:stayOnPageEventDefinition": ""}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.fd8896db-127b-4b17-8f95-6a80c9fb18e8"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.79e1cdcd-1a4a-4811-8ce8-7cfe8e4e3d11"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "role", "id": "2056.a09c9c9c-d6d3-48e2-8ade-ca39886be668"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.e9cd9534-1c97-4e32-859a-************"}], "ns16:exclusiveGateway": [{"default": "2027.8c2856d4-d624-40f8-8570-66de917beee4", "name": "Valid?", "id": "2025.f45ffe81-0484-4595-8cbf-78180725c5fe", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "743", "y": "196", "width": "47", "height": "71"}}, "ns16:incoming": "2027.328b2c9b-f49d-4c46-86bb-e461713766e6", "ns16:outgoing": ["2027.8c2856d4-d624-40f8-8570-66de917beee4", "2027.83029c21-962f-4cf5-8d57-6a905495b53f"]}, {"default": "2027.68f1148c-f1c0-4f37-8a63-9706a8544c87", "name": "audited?", "id": "2025.802539d1-9dec-4b1c-82dc-11beed907565", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1210", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.1786cdbf-044c-438b-8ef6-499a46a76538", "ns16:outgoing": ["2027.e9d7a6e3-d13b-42fc-860c-dfbd796fd49c", "2027.68f1148c-f1c0-4f37-8a63-9706a8544c87"]}, {"default": "2027.341f7459-**************-1b14cf143ae2", "name": "cancel", "id": "2025.6a79e4a2-2d09-435c-8f4f-a59097913e6b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1420", "y": "197", "width": "32", "height": "32"}}, "ns16:incoming": "2027.e9d7a6e3-d13b-42fc-860c-dfbd796fd49c", "ns16:outgoing": ["2027.341f7459-**************-1b14cf143ae2", "2027.e597c20b-d9ce-43ed-87da-57ad48682d5c"]}, {"default": "2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d", "name": "Exclusive Gateway", "id": "2025.bd6fc82a-1468-42a1-850f-1538f0a5ffa3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1717", "y": "112", "width": "32", "height": "32"}}, "ns16:incoming": "2027.b9a2adac-0335-43e5-8fae-4e8a0d274be3", "ns16:outgoing": ["2027.81349889-6cdc-443a-8c26-74e33cd0f398", "2027.3e452f3d-9c05-433d-8fd3-f05b555bec8d"]}], "ns3:htmlHeaderTag": {"id": "e2d2f537-21a1-4306-a37d-c825fe9b7d13", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.93c5c002-7ac4-4283-83ee-63b8662f9223", "epvProcessLinkId": "2dbda4d3-814d-4db7-8f31-ad87e19f941f"}, {"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "11a5abc0-a8a9-4226-85b9-a4997b11884c"}, {"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "19a12628-306b-4494-8ea5-bbeee9d6720e"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "80d5b00e-b8b2-4012-812d-8372aef64b2e"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.f37777d2-cd75-44bd-8254-e8f8859e1415"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.d4fa9d21-5e1a-4996-8745-6d9b1ba1d005"}, {"name": "routingDetails", "itemSubjectRef": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "isCollection": "false", "id": "2055.cda6b97c-96b9-488b-8055-47d670e552ac"}, {"name": "lastAction", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a3159314-99a0-4745-8a3b-4be34b9b6f7b"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.790399ab-7b67-4adc-8647-410880393efa"}], "ns16:dataOutput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.cd4ee7b8-3ac7-4a16-8a0b-023b2838e7f1"}, "ns16:inputSet": {"id": "_6bc0899d-0bcb-4755-93df-a13982ea1f6c"}, "ns16:outputSet": {"id": "_e5fe2948-5bf5-4c76-ac77-481b6787840c"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.31e3a97e-a1de-4686-a3fa-9ad223c3abca", "processId": "1.0694c1c7-863e-4809-8c83-bafbba3eb2bb", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.34c32d45-9e61-40e4-a065-399699c34372", "2025.34c32d45-9e61-40e4-a065-399699c34372"], "endStateId": "Out", "toProcessItemId": ["2025.0b2eb5e6-5cc9-45e0-98ef-80586118baf8", "2025.0b2eb5e6-5cc9-45e0-98ef-80586118baf8"], "guid": "98a9ade4-34c8-4f65-b18d-c3fa9fa128e0", "versionId": "62ba98ab-6bd8-440c-a5eb-766ab1706306", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}