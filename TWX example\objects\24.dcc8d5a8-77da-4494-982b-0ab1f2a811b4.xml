<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <participant id="24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4" name="Branch compliance Representative Checkers">
        <lastModified>1691143034828</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <participantId>24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4</participantId>
        <participantDefinition isNull="true" />
        <simulationGroupSize>2</simulationGroupSize>
        <capacityType>1</capacityType>
        <definitionType>3</definitionType>
        <percentAvailable isNull="true" />
        <percentEfficiency isNull="true" />
        <cost>10.00</cost>
        <currencyCode isNull="true" />
        <image isNull="true" />
        <serviceMembersRef isNull="true" />
        <managersRef isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"team":[{"members":{"Users":[{"name":"abdelrahman.saleh","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"heba","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"somaia","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}],"UserGroups":[{"name":"BPM_ODC_BR_COMP_REP_CHKR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails","type":"StandardMembers"}]},"documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"Branch compliance Representative Checkers","declaredType":"resource","id":"24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.w3.org\/1999\/XPath","id":"24.dcc8d5a8-77da-4494-982b-0ab1f2a811b4"}</jsonData>
        <externalId isNull="true" />
        <description></description>
        <guid>guid:d694a63221635d5b:6baf87c4:18969a9a6e2:74a9</guid>
        <versionId>810f835e-ee93-4052-91c7-1db6459ebc54</versionId>
        <standardMembers>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_BR_COMP_REP_CHKR</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>abdelrahman.saleh</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>heba</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>somaia</name>
            </standardMember>
        </standardMembers>
        <teamAssignments />
    </participant>
</teamworks>

