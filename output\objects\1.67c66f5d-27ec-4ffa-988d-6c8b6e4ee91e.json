{"id": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "versionId": "9ee21618-4ac4-44bd-9e2a-77dc15c7a31c", "name": "ODC BO Initialization", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "name": "ODC BO Initialization", "lastModified": "1699519439885", "lastModifiedBy": "heba", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb", "2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4942", "versionId": "9ee21618-4ac4-44bd-9e2a-77dc15c7a31c", "dependencySummary": "<dependencySummary id=\"bpdid:651a1a6abf396537:64776e00:18baeba64af:23fa\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.4df43bda-00dd-43c7-819d-1c19f07a5762\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"7d625bde-de84-4fde-8a9a-65f20a475b1c\"},{\"incoming\":[\"5cfabfe4-3dc6-4722-8876-88d28df60370\",\"56a02411-9a5c-4fdc-8a44-e84dbe8ea39d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f\"},{\"targetRef\":\"ce49342f-4b17-433b-8e8d-9fb7f05403cb\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Init\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4df43bda-00dd-43c7-819d-1c19f07a5762\",\"sourceRef\":\"7d625bde-de84-4fde-8a9a-65f20a475b1c\"},{\"startQuantity\":1,\"outgoing\":[\"5cfabfe4-3dc6-4722-8876-88d28df60370\"],\"incoming\":[\"2027.4df43bda-00dd-43c7-819d-1c19f07a5762\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":270,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Init\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"ce49342f-4b17-433b-8e8d-9fb7f05403cb\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if (!tw.local.odcRequest)\\r\\n\\ttw.local.odcRequest = new tw.object.ODCRequest();\\r\\ntw.local.odcRequest.actions = new tw.object.listOf.String();\\r\\nif (!tw.local.odcRequest.appInfo) \\r\\n\\ttw.local.odcRequest.appInfo = new tw.object.AppInfo();\\r\\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\\r\\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties= new tw.object.ECMproperties();\\r\\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\\r\\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\\r\\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\\r\\n\\/\\/tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new tw.object.Date();\\r\\n\\/\\/tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\r\\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\\r\\n\\/\\/tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new tw.object.Date();\\r\\n\\/\\/tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\r\\ntw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\r\\ntw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 0.0;\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].waiver=false;\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\r\\n\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\r\\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\r\\n\\r\\n\\r\\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\\r\\ntw.local.odcRequest.ContractCreation = new tw.object.ContractCreation();\\r\\ntw.local.odcRequest.ContractCreation.baseDate = new Date();\\r\\ntw.local.odcRequest.ContractCreation.valueDate = new tw.object.Date();\\r\\ntw.local.odcRequest.ContractCreation.sourceReference =\\\"\\\";\\r\\ntw.local.odcRequest.ContractLiquidation = new tw.object.ContractLiquidation();\\r\\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\\r\\ntw.local.odcRequest.documentSource = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\r\\ntw.local.odcRequest.FinancialDetailsBR.currency={};\\r\\n\\r\\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\r\\n\\r\\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\r\\n\\r\\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\r\\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\r\\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date =new tw.object.Date();\\r\\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\/\\/tw.local.odcRequest.folderID= new tw.object.ECM;\\r\\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.ECMFolder();\\r\\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\\r\\ntw.local.odcRequest.ImporterDetails = new tw.object.ImporterDetails();\\r\\n\\r\\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\\r\\ntw.local.odcRequest.Parties = new tw.object.odcParties();\\r\\ntw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\\r\\ntw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\\r\\n\\/\\/tw.local.odcRequest.Parties.party = new tw.object.listOf.partyTypes();\\r\\ntw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\\r\\ntw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\\r\\n\\r\\ntw.local.odcRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\r\\ntw.local.odcRequest.requestDate = new tw.object.Date();\\r\\ntw.local.odcRequest.requestNature = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.requestNature.name = \\\"\\\";\\r\\ntw.local.odcRequest.requestNature.value = \\\"\\\";\\r\\ntw.local.odcRequest.requestType = new tw.object.NameValuePair();\\r\\ntw.local.odcRequest.requestType.name = \\\"\\\";\\r\\ntw.local.odcRequest.requestType.value = \\\"\\\";\\r\\ntw.local.odcRequest.ReversalReason = new tw.object.ReversalReason();\\r\\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\\r\\n\\r\\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\r\\n\\r\\n\\r\\n\"]}},{\"targetRef\":\"65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"5cfabfe4-3dc6-4722-8876-88d28df60370\",\"sourceRef\":\"ce49342f-4b17-433b-8e8d-9fb7f05403cb\"},{\"startQuantity\":1,\"outgoing\":[\"56a02411-9a5c-4fdc-8a44-e84dbe8ea39d\"],\"incoming\":[\"ced22e79-23d6-4e88-8bd7-6e2da82e20e0\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":392,\"y\":140,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exp Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"ODC BO Initialization\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"f6b6f3ee-04e7-4475-81d5-5ecdbf209604\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"56a02411-9a5c-4fdc-8a44-e84dbe8ea39d\",\"sourceRef\":\"f6b6f3ee-04e7-4475-81d5-5ecdbf209604\"},{\"parallelMultiple\":false,\"outgoing\":[\"ced22e79-23d6-4e88-8bd7-6e2da82e20e0\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"c52428cc-4b91-4b8a-8739-2af36e1115a5\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"0dc2de68-b5db-4fd5-8665-729cd1e05d24\",\"otherAttributes\":{\"eventImplId\":\"4109dacd-dce3-4098-835c-68dd14f7e876\"}}],\"attachedToRef\":\"ce49342f-4b17-433b-8e8d-9fb7f05403cb\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":305,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"77057b21-26bc-4e8a-889f-e9724bcea10b\",\"outputSet\":{}},{\"targetRef\":\"f6b6f3ee-04e7-4475-81d5-5ecdbf209604\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exp Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"ced22e79-23d6-4e88-8bd7-6e2da82e20e0\",\"sourceRef\":\"77057b21-26bc-4e8a-889f-e9724bcea10b\"}],\"laneSet\":[{\"id\":\"c979ec57-8e23-4209-8bba-3b9939ee5878\",\"lane\":[{\"flowNodeRef\":[\"7d625bde-de84-4fde-8a9a-65f20a475b1c\",\"65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f\",\"ce49342f-4b17-433b-8e8d-9fb7f05403cb\",\"f6b6f3ee-04e7-4475-81d5-5ecdbf209604\",\"77057b21-26bc-4e8a-889f-e9724bcea10b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"59ded5d7-dda4-437b-8ebb-16dc4cd8de36\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"ODC BO Initialization\",\"declaredType\":\"process\",\"id\":\"1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.247d12a8-cf76-495c-8cd4-700f32286023\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"id\":\"2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de\",\"2055.247d12a8-cf76-495c-8cd4-700f32286023\",\"2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"documentation\":[{\"content\":[\"<div><br \\/><\\/div>\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": "<div><br /></div>", "guid": "f5c43f37-1754-4d6d-adb1-fcbec40c42b4", "versionId": "1954078e-6b8a-4092-9bfc-8a277fa6603a"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f5efe421-81dc-4e3e-ab93-b6badfbcd64e", "versionId": "bc69dacd-3313-486d-81ac-9b96cb5bfc35"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.247d12a8-cf76-495c-8cd4-700f32286023", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "12d4e3bc-5095-41e3-8cdc-1bf4b48b7aab", "versionId": "65239ed9-6a92-4f0a-b639-7fd864a926a6"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6dad8549-a860-4746-989f-b6b151c64ec4", "versionId": "dc799e82-8536-454e-9b93-f7d69a520c47"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "name": "Init", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.95a70005-c473-454e-9824-b4e7e1d4f5d8", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "guid": "guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-493c", "versionId": "0be483a3-d9cb-4f81-81da-66bad74eb3bd", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.ef2503e9-5815-4033-81d3-8163af620ee0", "processItemId": "2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb", "location": "1", "script": {"isNull": "true"}, "guid": "344825a3-f211-4142-babe-cd237e2e8f0f", "versionId": "ec63844e-8c55-4130-9f78-c7a3d8ee0f86"}, "layoutData": {"x": "270", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac6fe7417:-4328", "errorHandlerItemId": "2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.95a70005-c473-454e-9824-b4e7e1d4f5d8", "scriptTypeId": "2", "isActive": "true", "script": "if (!tw.local.odcRequest)\r\r\n\ttw.local.odcRequest = new tw.object.ODCRequest();\r\r\ntw.local.odcRequest.actions = new tw.object.listOf.String();\r\r\nif (!tw.local.odcRequest.appInfo) \r\r\n\ttw.local.odcRequest.appInfo = new tw.object.AppInfo();\r\r\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties= new tw.object.ECMproperties();\r\r\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\n//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new tw.object.Date();\r\r\n//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\n//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new tw.object.Date();\r\r\n//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 0.0;\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].waiver=false;\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\r\n\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\r\n\r\r\n\r\r\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.ContractCreation = new tw.object.ContractCreation();\r\r\ntw.local.odcRequest.ContractCreation.baseDate = new Date();\r\r\ntw.local.odcRequest.ContractCreation.valueDate = new tw.object.Date();\r\r\ntw.local.odcRequest.ContractCreation.sourceReference =\"\";\r\r\ntw.local.odcRequest.ContractLiquidation = new tw.object.ContractLiquidation();\r\r\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\r\r\ntw.local.odcRequest.documentSource = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency={};\r\r\n\r\r\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\n\r\r\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\r\n\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date =new tw.object.Date();\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n//tw.local.odcRequest.folderID= new tw.object.ECM;\r\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.ECMFolder();\r\r\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.ImporterDetails = new tw.object.ImporterDetails();\r\r\n\r\r\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\r\r\ntw.local.odcRequest.Parties = new tw.object.odcParties();\r\r\ntw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\r\r\ntw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\r\r\n//tw.local.odcRequest.Parties.party = new tw.object.listOf.partyTypes();\r\r\ntw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\r\r\ntw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\r\r\n\r\r\ntw.local.odcRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\r\ntw.local.odcRequest.requestDate = new tw.object.Date();\r\r\ntw.local.odcRequest.requestNature = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.requestNature.name = \"\";\r\r\ntw.local.odcRequest.requestNature.value = \"\";\r\r\ntw.local.odcRequest.requestType = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.requestType.name = \"\";\r\r\ntw.local.odcRequest.requestType.value = \"\";\r\r\ntw.local.odcRequest.ReversalReason = new tw.object.ReversalReason();\r\r\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\r\r\n\r\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "9b21ca21-1f9d-4bc5-88f2-785f583c2156", "versionId": "0e27f33b-d462-43fa-817b-dee4d5ab58d6"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "name": "Exp Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac6fe7417:-4328", "versionId": "3b2cb8db-5403-4037-8ab2-038f6710e897", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "392", "y": "140", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "0d4ae84c-ffba-4597-ad99-204f82fdb107", "versionId": "f6aa0249-ffd5-4a41-ad2d-8b65dd214dc0", "parameterMapping": [{"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4dc7f40e-c8a3-450f-b7e2-e939c187fca3", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5", "useDefault": "false", "value": "\"ODC BO Initialization\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "4f00d360-e752-4838-a4fc-ff3b73f68759", "versionId": "86cf9931-8fcd-43d4-8b92-32ed62413276", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c824fc10-0703-43e0-92a6-f7036d9a793d", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "fbb2a34f-e8b7-4ac6-98be-f91a38f5786a", "versionId": "91559e7b-d410-40b1-9855-71fb06db83c6", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.685300c7-e659-4b0c-956f-eaad293febce", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "8cecbdf1-b7ab-480d-8997-a867009cdb12", "versionId": "dd71fdef-e0bf-4c60-a212-8d84598ebde5", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.0846ef85-b728-4fa5-8c98-6264d5f93857", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940", "versionId": "d0b0b8ee-d1c9-4342-aec1-cd29b1385706", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.0846ef85-b728-4fa5-8c98-6264d5f93857", "haltProcess": "false", "guid": "1857fbba-1eaa-4a82-bce3-31e7937b2feb", "versionId": "43fe6059-5499-4e01-aa96-2466cc3dd088"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "ODC BO Initialization", "id": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5", "ns16:documentation": {"_": "<div><br /></div>", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "useDefault": "false"}}}, "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de"}, {"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.247d12a8-cf76-495c-8cd4-700f32286023"}, {"name": "isSuccessful", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de", "2055.247d12a8-cf76-495c-8cd4-700f32286023", "2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6"]}}, "ns16:laneSet": {"id": "c979ec57-8e23-4209-8bba-3b9939ee5878", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "59ded5d7-dda4-437b-8ebb-16dc4cd8de36", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["7d625bde-de84-4fde-8a9a-65f20a475b1c", "65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f", "ce49342f-4b17-433b-8e8d-9fb7f05403cb", "f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "77057b21-26bc-4e8a-889f-e9724bcea10b"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "7d625bde-de84-4fde-8a9a-65f20a475b1c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.4df43bda-00dd-43c7-819d-1c19f07a5762"}, "ns16:endEvent": {"name": "End", "id": "65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940"}, "ns16:incoming": ["5cfabfe4-3dc6-4722-8876-88d28df60370", "56a02411-9a5c-4fdc-8a44-e84dbe8ea39d"]}, "ns16:sequenceFlow": [{"sourceRef": "7d625bde-de84-4fde-8a9a-65f20a475b1c", "targetRef": "ce49342f-4b17-433b-8e8d-9fb7f05403cb", "name": "To Init", "id": "2027.4df43bda-00dd-43c7-819d-1c19f07a5762", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "ce49342f-4b17-433b-8e8d-9fb7f05403cb", "targetRef": "65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f", "name": "To End", "id": "5cfabfe4-3dc6-4722-8876-88d28df60370", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "targetRef": "65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f", "name": "To End", "id": "56a02411-9a5c-4fdc-8a44-e84dbe8ea39d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}, {"sourceRef": "77057b21-26bc-4e8a-889f-e9724bcea10b", "targetRef": "f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "name": "To Exp Handling", "id": "ced22e79-23d6-4e88-8bd7-6e2da82e20e0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Init", "id": "ce49342f-4b17-433b-8e8d-9fb7f05403cb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "270", "y": "57", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.4df43bda-00dd-43c7-819d-1c19f07a5762", "ns16:outgoing": "5cfabfe4-3dc6-4722-8876-88d28df60370", "ns16:script": "if (!tw.local.odcRequest)\r\r\n\ttw.local.odcRequest = new tw.object.ODCRequest();\r\r\ntw.local.odcRequest.actions = new tw.object.listOf.String();\r\r\nif (!tw.local.odcRequest.appInfo) \r\r\n\ttw.local.odcRequest.appInfo = new tw.object.AppInfo();\r\r\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties= new tw.object.ECMproperties();\r\r\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\n//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new tw.object.Date();\r\r\n//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\n//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new tw.object.Date();\r\r\n//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 0.0;\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].waiver=false;\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\r\n\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\r\n\r\r\n\r\r\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.ContractCreation = new tw.object.ContractCreation();\r\r\ntw.local.odcRequest.ContractCreation.baseDate = new Date();\r\r\ntw.local.odcRequest.ContractCreation.valueDate = new tw.object.Date();\r\r\ntw.local.odcRequest.ContractCreation.sourceReference =\"\";\r\r\ntw.local.odcRequest.ContractLiquidation = new tw.object.ContractLiquidation();\r\r\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\r\r\ntw.local.odcRequest.documentSource = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency={};\r\r\n\r\r\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\n\r\r\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\r\n\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date =new tw.object.Date();\r\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n//tw.local.odcRequest.folderID= new tw.object.ECM;\r\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.ECMFolder();\r\r\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.ImporterDetails = new tw.object.ImporterDetails();\r\r\n\r\r\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\r\r\ntw.local.odcRequest.Parties = new tw.object.odcParties();\r\r\ntw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\r\r\ntw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\r\r\n//tw.local.odcRequest.Parties.party = new tw.object.listOf.partyTypes();\r\r\ntw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\r\r\ntw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\r\r\n\r\r\ntw.local.odcRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\r\ntw.local.odcRequest.requestDate = new tw.object.Date();\r\r\ntw.local.odcRequest.requestNature = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.requestNature.name = \"\";\r\r\ntw.local.odcRequest.requestNature.value = \"\";\r\r\ntw.local.odcRequest.requestType = new tw.object.NameValuePair();\r\r\ntw.local.odcRequest.requestType.name = \"\";\r\r\ntw.local.odcRequest.requestType.value = \"\";\r\r\ntw.local.odcRequest.ReversalReason = new tw.object.ReversalReason();\r\r\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\r\r\n\r\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\r\n\r\r\n\r\r\n"}, "ns16:callActivity": {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exp Handling", "id": "f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "392", "y": "140", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "ced22e79-23d6-4e88-8bd7-6e2da82e20e0", "ns16:outgoing": "56a02411-9a5c-4fdc-8a44-e84dbe8ea39d", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"ODC BO Initialization\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "ce49342f-4b17-433b-8e8d-9fb7f05403cb", "parallelMultiple": "false", "name": "Error", "id": "77057b21-26bc-4e8a-889f-e9724bcea10b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "305", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "ced22e79-23d6-4e88-8bd7-6e2da82e20e0", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "c52428cc-4b91-4b8a-8739-2af36e1115a5"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "0dc2de68-b5db-4fd5-8665-729cd1e05d24", "eventImplId": "4109dacd-dce3-4098-835c-68dd14f7e876", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.56a02411-9a5c-4fdc-8a44-e84dbe8ea39d", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604", "2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f", "2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f"], "guid": "e27b1c4f-90d5-4533-8daf-fce8dbdd673c", "versionId": "6bc1982f-6680-4eb3-b1cc-2252b8a72eba", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.5cfabfe4-3dc6-4722-8876-88d28df60370", "processId": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb", "2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb"], "endStateId": "Out", "toProcessItemId": ["2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f", "2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f"], "guid": "76f1373a-00ce-41ba-b129-78077d0cf6e1", "versionId": "f01b2ca5-88a2-4d6e-bda2-f6675187ad56", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}