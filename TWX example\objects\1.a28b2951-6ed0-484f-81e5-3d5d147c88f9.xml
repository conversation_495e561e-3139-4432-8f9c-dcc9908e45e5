<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a28b2951-6ed0-484f-81e5-3d5d147c88f9" name="Init Create ODC Request Service">
        <lastModified>1691332859178</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.8d32b007-b41f-4fe8-84ca-31bbde839f1c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:62a4244840b5e546:3b32f8f5:189cadae0da:-1055</guid>
        <versionId>917a87a1-d033-43a3-a2dd-ce56114e8edc</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:e5873b94e72cc4db:-66d3a9fb:189cb72d96f:-7421" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e8db3281-12ca-4097-8abb-0d61285aa4e0"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"c1d44df3-da02-4cfd-8104-1475780a6d06"},{"incoming":["a6f3f9a8-b9cd-48a3-8ba2-74099474b86c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":664,"y":78,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:62a4244840b5e546:3b32f8f5:189cadae0da:-1053"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"7f09b24c-1f20-4030-89d4-6af3b1e33f6e"},{"targetRef":"8d32b007-b41f-4fe8-84ca-31bbde839f1c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.e8db3281-12ca-4097-8abb-0d61285aa4e0","sourceRef":"c1d44df3-da02-4cfd-8104-1475780a6d06"},{"startQuantity":1,"outgoing":["37d58383-7fa3-42cb-8c58-0ae84e9b621a"],"incoming":["2027.e8db3281-12ca-4097-8abb-0d61285aa4e0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":151,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"LDAP Get User Profile","dataInputAssociation":[{"targetRef":"2055.d3668934-f65c-433d-bc52-5dc16734b85a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user.name"]}}]},{"targetRef":"2055.19bf907d-5bb3-4677-8403-cd5628d55c51","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["false"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"8d32b007-b41f-4fe8-84ca-31bbde839f1c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.28deb4a7-5775-4f6c-b526-cc1e1c7dd643"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2","declaredType":"TFormalExpression","content":["tw.local.ldapUserProfile"]}}],"sourceRef":["2055.248c8a0a-74b7-4dfc-97cb-66303411feef"]}],"calledElement":"1.956a8aca-1727-4485-8c3f-261d2eafc464"},{"startQuantity":1,"outgoing":["a6f3f9a8-b9cd-48a3-8ba2-74099474b86c"],"incoming":["37d58383-7fa3-42cb-8c58-0ae84e9b621a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":421,"y":55,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init ODC Request","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"d04612b8-a8b3-4ea0-81a2-019266d34eff","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest == null)\r\n\ttw.local.odcRequest ={};\r\ntw.local.odcRequest.appInfo = new tw.object.AppInfo();\r\ntw.local.odcRequest.appInfo.subStatus =\"\";\r\ntw.local.odcRequest.appInfo.status = \"\";\r\nvar date = new Date();\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\/' +(date.getMonth() + 1) + '\/' + date.getFullYear();\r\ntw.local.odcRequest.appInfo.branch = {};\r\n\ttw.local.odcRequest.appInfo.branch.name = tw.local.ldapUserProfile.branch.name;\r\n\ttw.local.odcRequest.appInfo.branch.value = tw.local.ldapUserProfile.branch.value;\r\n\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"(\"+ tw.system.user.name+\")\";\r\n\t\r\n\ttw.local.odcRequest.appInfo.requestName = tw.epv.ProcessDetails.processName;\r\n\ttw.local.odcRequest.appInfo.requestType = tw.epv.ProcessDetails.suffixName;\r\n\ttw.local.odcRequest.appInfo.status =\"\";\r\n\ttw.local.odcRequest.appInfo.appRef =\"\";\r\n\ttw.local.odcRequest.appInfo.appID = \"\";\r\n\ttw.local.odcRequest.appInfo.instanceID = \"\";\r\n\ttw.local.odcRequest.appInfo.stepName =tw.epv.stepsName.CACT01;\r\n\t\r\n\t\r\n\t"]}},{"targetRef":"d04612b8-a8b3-4ea0-81a2-019266d34eff","extensionElements":{"endStateId":["guid:e30c377f7882db6a:324bd248:186d6576310:-2af8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init ODC Request","declaredType":"sequenceFlow","id":"37d58383-7fa3-42cb-8c58-0ae84e9b621a","sourceRef":"8d32b007-b41f-4fe8-84ca-31bbde839f1c"},{"targetRef":"7f09b24c-1f20-4030-89d4-6af3b1e33f6e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"a6f3f9a8-b9cd-48a3-8ba2-74099474b86c","sourceRef":"d04612b8-a8b3-4ea0-81a2-019266d34eff"},{"itemSubjectRef":"itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2","name":"ldapUserProfile","isCollection":false,"declaredType":"dataObject","id":"2056.abdfa7ab-6ce4-4911-8a2d-6fba6c2bce35"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.eece183b-12e8-424a-847e-efb0c46005e5"}],"laneSet":[{"id":"aa2fcb64-c873-4ad8-85f0-7c4f539191bc","lane":[{"flowNodeRef":["c1d44df3-da02-4cfd-8104-1475780a6d06","7f09b24c-1f20-4030-89d4-6af3b1e33f6e","8d32b007-b41f-4fe8-84ca-31bbde839f1c","d04612b8-a8b3-4ea0-81a2-019266d34eff"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"016d4c2e-e4cb-4d6c-8d7e-71716579b1b9","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Init Create ODC Request Service","declaredType":"process","id":"1.a28b2951-6ed0-484f-81e5-3d5d147c88f9","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.c4ce8b9d-83cc-46eb-883c-22bed6f6209a"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.93c5c002-7ac4-4283-83ee-63b8662f9223","epvProcessLinkId":"e4663bed-bf29-474e-8c85-5a8f257e4b0d","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.96f93187-360b-430a-9042-84a97749fff7","epvProcessLinkId":"35146d51-5103-4cb0-8b48-dfde8c134cac","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.f71d3666-b3a5-4a53-8668-1ae59b9b963c"]}],"outputSet":[{"dataOutputRefs":["2055.c4ce8b9d-83cc-46eb-883c-22bed6f6209a"]}],"dataInput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.f71d3666-b3a5-4a53-8668-1ae59b9b963c"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f71d3666-b3a5-4a53-8668-1ae59b9b963c</processParameterId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3f708695-5fae-4d4c-af3c-3aebbda55a74</guid>
            <versionId>0ef12a16-b24d-4c83-8dcd-baecb4920cff</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c4ce8b9d-83cc-46eb-883c-22bed6f6209a</processParameterId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8058da64-f70b-4840-80ff-d0adce13b4d4</guid>
            <versionId>973525ba-4467-4387-91b5-95deab1d3039</versionId>
        </processParameter>
        <processVariable name="ldapUserProfile">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.abdfa7ab-6ce4-4911-8a2d-6fba6c2bce35</processVariableId>
            <description isNull="true" />
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9e48cc44-14d7-4d4e-a14f-c4fde905d54c</guid>
            <versionId>7c48f0c2-c72d-4752-a20c-ebc8473cd16f</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.eece183b-12e8-424a-847e-efb0c46005e5</processVariableId>
            <description isNull="true" />
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e54ef3f5-6318-40af-aec8-a6a941daeccb</guid>
            <versionId>dab01c2c-8362-48de-a329-8cfe2fd1bd5f</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7f09b24c-1f20-4030-89d4-6af3b1e33f6e</processItemId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.41bf52c7-7542-4c40-b1fd-c5c47bea7dbd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:62a4244840b5e546:3b32f8f5:189cadae0da:-1053</guid>
            <versionId>1d653696-ed27-4379-a024-d3cd7d0f470c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="664" y="78">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.41bf52c7-7542-4c40-b1fd-c5c47bea7dbd</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>140a1202-2651-4f90-a8fc-9f4f82d7693d</guid>
                <versionId>265f458c-95b6-4900-aa3b-4d88437ee0f7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d04612b8-a8b3-4ea0-81a2-019266d34eff</processItemId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <name>Init ODC Request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.2769f0ce-f16c-4a74-a476-239ae59f1c37</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:62a4244840b5e546:3b32f8f5:189cadae0da:-1006</guid>
            <versionId>95381e68-326f-435e-8592-0366f914aac1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="421" y="55">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.2769f0ce-f16c-4a74-a476-239ae59f1c37</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.odcRequest == null)&#xD;
	tw.local.odcRequest ={};&#xD;
tw.local.odcRequest.appInfo = new tw.object.AppInfo();&#xD;
tw.local.odcRequest.appInfo.subStatus ="";&#xD;
tw.local.odcRequest.appInfo.status = "";&#xD;
var date = new Date();&#xD;
tw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();&#xD;
tw.local.odcRequest.appInfo.branch = {};&#xD;
	tw.local.odcRequest.appInfo.branch.name = tw.local.ldapUserProfile.branch.name;&#xD;
	tw.local.odcRequest.appInfo.branch.value = tw.local.ldapUserProfile.branch.value;&#xD;
	tw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+"("+ tw.system.user.name+")";&#xD;
	&#xD;
	tw.local.odcRequest.appInfo.requestName = tw.epv.ProcessDetails.processName;&#xD;
	tw.local.odcRequest.appInfo.requestType = tw.epv.ProcessDetails.suffixName;&#xD;
	tw.local.odcRequest.appInfo.status ="";&#xD;
	tw.local.odcRequest.appInfo.appRef ="";&#xD;
	tw.local.odcRequest.appInfo.appID = "";&#xD;
	tw.local.odcRequest.appInfo.instanceID = "";&#xD;
	tw.local.odcRequest.appInfo.stepName =tw.epv.stepsName.CACT01;&#xD;
	&#xD;
	&#xD;
	</script>
                <isRule>false</isRule>
                <guid>41115996-0dc0-4192-b47e-ddc743e4c2f6</guid>
                <versionId>b55ece2e-5843-449d-a7a1-a890c8789798</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8d32b007-b41f-4fe8-84ca-31bbde839f1c</processItemId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <name>LDAP Get User Profile</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d1a86ba1-8499-477d-83f9-e0bf45517b99</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:62a4244840b5e546:3b32f8f5:189cadae0da:-1007</guid>
            <versionId>f0e24ecc-a091-4720-9892-e9aee7e73572</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="151" y="56">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d1a86ba1-8499-477d-83f9-e0bf45517b99</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.956a8aca-1727-4485-8c3f-261d2eafc464</attachedProcessRef>
                <guid>e9c23995-4aaf-42ba-8de5-fed4884b7668</guid>
                <versionId>d2369157-b348-402e-a760-d0ed701d52b5</versionId>
                <parameterMapping name="debugMode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0d156630-d3b2-4d99-bc92-554c2896af00</parameterMappingId>
                    <processParameterId>2055.19bf907d-5bb3-4677-8403-cd5628d55c51</processParameterId>
                    <parameterMappingParentId>3012.d1a86ba1-8499-477d-83f9-e0bf45517b99</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>false</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1d5bdc7d-36e4-48b7-b74e-24a25dee9073</guid>
                    <versionId>2d9391c0-78ae-44c6-b813-332c43c10538</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="ldapUserProfile">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f72085de-e875-424d-a1b8-99ca0a6f22d1</parameterMappingId>
                    <processParameterId>2055.248c8a0a-74b7-4dfc-97cb-66303411feef</processParameterId>
                    <parameterMappingParentId>3012.d1a86ba1-8499-477d-83f9-e0bf45517b99</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ldapUserProfile</value>
                    <classRef>/12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d1d910a7-716e-49b3-a4c7-8921dfdf932c</guid>
                    <versionId>62cab8ef-6bfe-4c7b-a725-e6ad862864ac</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7bddd8c8-d912-4919-a478-dd4482df795d</parameterMappingId>
                    <processParameterId>2055.d3668934-f65c-433d-bc52-5dc16734b85a</processParameterId>
                    <parameterMappingParentId>3012.d1a86ba1-8499-477d-83f9-e0bf45517b99</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a2784830-df9e-413c-8359-5ec81e6ab7ab</guid>
                    <versionId>893f0aaf-64ec-4a2e-a402-64ecd333b194</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a2d7cdfd-e5f2-4f07-8c94-26b7880d8a70</parameterMappingId>
                    <processParameterId>2055.28deb4a7-5775-4f6c-b526-cc1e1c7dd643</processParameterId>
                    <parameterMappingParentId>3012.d1a86ba1-8499-477d-83f9-e0bf45517b99</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0fc3a844-21b1-4b16-90b8-6e460fe66ef9</guid>
                    <versionId>8b0e22cc-5d6b-4e33-ba61-6b1cf524cb25</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.9b6f1535-70a0-4704-9db6-36f65931a2a0</epvProcessLinkId>
            <epvId>/21.93c5c002-7ac4-4283-83ee-63b8662f9223</epvId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <guid>3082e318-8cba-4f45-8d48-34a80fe34284</guid>
            <versionId>8862f777-0072-4e0c-a27c-3167d7b9b251</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.542b9f7f-de76-47d0-b59f-338454c81112</epvProcessLinkId>
            <epvId>/21.96f93187-360b-430a-9042-84a97749fff7</epvId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <guid>1a4f77e0-5ead-4503-97e5-c5cc1ae20e31</guid>
            <versionId>b4b2b25c-2f8d-432f-879a-e1a78191d4ec</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.8d32b007-b41f-4fe8-84ca-31bbde839f1c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Init Create ODC Request Service" id="1.a28b2951-6ed0-484f-81e5-3d5d147c88f9" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.93c5c002-7ac4-4283-83ee-63b8662f9223" epvProcessLinkId="e4663bed-bf29-474e-8c85-5a8f257e4b0d" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.96f93187-360b-430a-9042-84a97749fff7" epvProcessLinkId="35146d51-5103-4cb0-8b48-dfde8c134cac" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.f71d3666-b3a5-4a53-8668-1ae59b9b963c" />
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.c4ce8b9d-83cc-46eb-883c-22bed6f6209a" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.f71d3666-b3a5-4a53-8668-1ae59b9b963c</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.c4ce8b9d-83cc-46eb-883c-22bed6f6209a</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="aa2fcb64-c873-4ad8-85f0-7c4f539191bc">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="016d4c2e-e4cb-4d6c-8d7e-71716579b1b9" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>c1d44df3-da02-4cfd-8104-1475780a6d06</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7f09b24c-1f20-4030-89d4-6af3b1e33f6e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8d32b007-b41f-4fe8-84ca-31bbde839f1c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d04612b8-a8b3-4ea0-81a2-019266d34eff</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="c1d44df3-da02-4cfd-8104-1475780a6d06">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.e8db3281-12ca-4097-8abb-0d61285aa4e0</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="7f09b24c-1f20-4030-89d4-6af3b1e33f6e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="664" y="78" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:62a4244840b5e546:3b32f8f5:189cadae0da:-1053</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a6f3f9a8-b9cd-48a3-8ba2-74099474b86c</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="c1d44df3-da02-4cfd-8104-1475780a6d06" targetRef="8d32b007-b41f-4fe8-84ca-31bbde839f1c" name="To End" id="2027.e8db3281-12ca-4097-8abb-0d61285aa4e0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.956a8aca-1727-4485-8c3f-261d2eafc464" isForCompensation="false" startQuantity="1" completionQuantity="1" name="LDAP Get User Profile" id="8d32b007-b41f-4fe8-84ca-31bbde839f1c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="151" y="56" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.e8db3281-12ca-4097-8abb-0d61285aa4e0</ns16:incoming>
                        
                        
                        <ns16:outgoing>37d58383-7fa3-42cb-8c58-0ae84e9b621a</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d3668934-f65c-433d-bc52-5dc16734b85a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.19bf907d-5bb3-4677-8403-cd5628d55c51</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">false</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.28deb4a7-5775-4f6c-b526-cc1e1c7dd643</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.248c8a0a-74b7-4dfc-97cb-66303411feef</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2">tw.local.ldapUserProfile</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init ODC Request" id="d04612b8-a8b3-4ea0-81a2-019266d34eff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="421" y="55" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>37d58383-7fa3-42cb-8c58-0ae84e9b621a</ns16:incoming>
                        
                        
                        <ns16:outgoing>a6f3f9a8-b9cd-48a3-8ba2-74099474b86c</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.odcRequest == null)&#xD;
	tw.local.odcRequest ={};&#xD;
tw.local.odcRequest.appInfo = new tw.object.AppInfo();&#xD;
tw.local.odcRequest.appInfo.subStatus ="";&#xD;
tw.local.odcRequest.appInfo.status = "";&#xD;
var date = new Date();&#xD;
tw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();&#xD;
tw.local.odcRequest.appInfo.branch = {};&#xD;
	tw.local.odcRequest.appInfo.branch.name = tw.local.ldapUserProfile.branch.name;&#xD;
	tw.local.odcRequest.appInfo.branch.value = tw.local.ldapUserProfile.branch.value;&#xD;
	tw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+"("+ tw.system.user.name+")";&#xD;
	&#xD;
	tw.local.odcRequest.appInfo.requestName = tw.epv.ProcessDetails.processName;&#xD;
	tw.local.odcRequest.appInfo.requestType = tw.epv.ProcessDetails.suffixName;&#xD;
	tw.local.odcRequest.appInfo.status ="";&#xD;
	tw.local.odcRequest.appInfo.appRef ="";&#xD;
	tw.local.odcRequest.appInfo.appID = "";&#xD;
	tw.local.odcRequest.appInfo.instanceID = "";&#xD;
	tw.local.odcRequest.appInfo.stepName =tw.epv.stepsName.CACT01;&#xD;
	&#xD;
	&#xD;
	</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8d32b007-b41f-4fe8-84ca-31bbde839f1c" targetRef="d04612b8-a8b3-4ea0-81a2-019266d34eff" name="To Init ODC Request" id="37d58383-7fa3-42cb-8c58-0ae84e9b621a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2af8</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d04612b8-a8b3-4ea0-81a2-019266d34eff" targetRef="7f09b24c-1f20-4030-89d4-6af3b1e33f6e" name="To End" id="a6f3f9a8-b9cd-48a3-8ba2-74099474b86c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.e58f8bfd-72dc-41d8-8f58-cdba3cd485b2" isCollection="false" name="ldapUserProfile" id="2056.abdfa7ab-6ce4-4911-8a2d-6fba6c2bce35" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.eece183b-12e8-424a-847e-efb0c46005e5" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a6f3f9a8-b9cd-48a3-8ba2-74099474b86c</processLinkId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d04612b8-a8b3-4ea0-81a2-019266d34eff</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.7f09b24c-1f20-4030-89d4-6af3b1e33f6e</toProcessItemId>
            <guid>d1e5ccec-fcd0-4b4b-9fb4-3da2cfcdb38f</guid>
            <versionId>8ad6cdf0-c9ff-4152-a96c-8df137844e9c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d04612b8-a8b3-4ea0-81a2-019266d34eff</fromProcessItemId>
            <toProcessItemId>2025.7f09b24c-1f20-4030-89d4-6af3b1e33f6e</toProcessItemId>
        </link>
        <link name="To Init ODC Request">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.37d58383-7fa3-42cb-8c58-0ae84e9b621a</processLinkId>
            <processId>1.a28b2951-6ed0-484f-81e5-3d5d147c88f9</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8d32b007-b41f-4fe8-84ca-31bbde839f1c</fromProcessItemId>
            <endStateId>guid:e30c377f7882db6a:324bd248:186d6576310:-2af8</endStateId>
            <toProcessItemId>2025.d04612b8-a8b3-4ea0-81a2-019266d34eff</toProcessItemId>
            <guid>5e109dae-3f09-4eb3-803d-4dacd66bf41d</guid>
            <versionId>b0ad6d77-201f-40b7-938b-d5ae11d92017</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8d32b007-b41f-4fe8-84ca-31bbde839f1c</fromProcessItemId>
            <toProcessItemId>2025.d04612b8-a8b3-4ea0-81a2-019266d34eff</toProcessItemId>
        </link>
    </process>
</teamworks>

