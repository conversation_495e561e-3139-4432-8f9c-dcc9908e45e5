<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.8e36e548-f4cb-44df-9910-c33585076ffa" name="Init ODC Request">
        <lastModified>1738480102663</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.8e36e548-f4cb-44df-9910-c33585076ffa</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2868</guid>
        <versionId>8856132e-d9e1-48a0-99c8-999f2e996e33</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:f7f6553919972802:-74de7c2c:194a0205f0e:298d" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.9d7c8285-0639-40bc-85c7-70dd53b43140"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"048a42a8-dbc4-4c10-8206-713a33ddf554"},{"incoming":["6b52cd5b-72b2-4808-86e5-b29efcacefb1"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2866"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"ddc67558-5d18-4966-8a8a-3880c50d3f63"},{"targetRef":"fdb88fd2-37b3-4259-85d0-c35f4de2843f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.9d7c8285-0639-40bc-85c7-70dd53b43140","sourceRef":"048a42a8-dbc4-4c10-8206-713a33ddf554"},{"startQuantity":1,"outgoing":["6b52cd5b-72b2-4808-86e5-b29efcacefb1"],"incoming":["2027.9d7c8285-0639-40bc-85c7-70dd53b43140"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":360,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init ODC Request","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"fdb88fd2-37b3-4259-85d0-c35f4de2843f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.requestDate=  new TWDate();\r\ntw.local.odcRequest.ImporterName= \"Somaia Galal\";\r\ntw.local.odcRequest.BasicDetails.requestState = \"new\";\r\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \"00104230000788\";\r\ntw.local.odcRequest.BasicDetails.contractStage = \"stage\";\r\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \"\";\r\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \"export purpose\";\r\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \"\";\r\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \"payment terms\";\r\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.productCategory.name = \"\";\r\ntw.local.odcRequest.BasicDetails.productCategory.value = \"product category\";\r\ntw.local.odcRequest.BasicDetails.commodityDescription = \"commodity Description\";\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \"\";\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \"Egypt\";\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"2341234\";\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"7890234\";\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\n\/\/tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 20.0;\r\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \"EGP\";\r\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \"EGP\";\r\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 10000.0;\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"********\";\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \"*********\";\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \"********\";\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \"12345\";\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \"12345\";\r\ntw.local.odcRequest.complianceApproval = false;\r\n\/\/tw.local.odcRequest.stepLog = new tw.object.StepLog();\r\ntw.local.odcRequest.stepLog.startTime = new TWDate();\r\ntw.local.odcRequest.stepLog.endTime = new TWDate();\r\ntw.local.odcRequest.stepLog.userName = \"\";\r\ntw.local.odcRequest.stepLog.role = \"\";\r\ntw.local.odcRequest.stepLog.step = \"\";\r\ntw.local.odcRequest.stepLog.action = \"\";\r\ntw.local.odcRequest.stepLog.comment = \"\";\r\ntw.local.odcRequest.stepLog.terminateReason = \"\";\r\ntw.local.odcRequest.stepLog.returnReason = \"\";\r\n\/\/tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.actions[0] = \"\";\r\n\/\/tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\ntw.local.odcRequest.attachmentDetails.folderID = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"\";\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"\";\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"\";\r\n\/\/tw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\r\ntw.local.odcRequest.History[0].startTime = new TWDate();\r\ntw.local.odcRequest.History[0].endTime = new TWDate();\r\ntw.local.odcRequest.History[0].userName = \"\";\r\ntw.local.odcRequest.History[0].role = \"Maker\";\r\ntw.local.odcRequest.History[0].step = \"act06\";\r\ntw.local.odcRequest.History[0].action = \"submit\";\r\ntw.local.odcRequest.History[0].comment = \"no comment\";\r\ntw.local.odcRequest.History[0].terminateReason = \"\";\r\ntw.local.odcRequest.History[0].returnReason = \"\";\r\n"]}},{"targetRef":"ddc67558-5d18-4966-8a8a-3880c50d3f63","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"6b52cd5b-72b2-4808-86e5-b29efcacefb1","sourceRef":"fdb88fd2-37b3-4259-85d0-c35f4de2843f"},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":362,"y":184,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Copy of Init ODC Request","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"baaed40a-0898-4f73-89dd-f33711c93c61","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest = new tw.object.ODCRequest();\r\ntw.local.odcRequest.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.requestNature.name = \"Update Request\";\r\ntw.local.odcRequest.requestNature.value = \"update\";\r\ntw.local.odcRequest.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.requestType.name = \"collection\" ;\r\ntw.local.odcRequest.requestType.value = \"ODC Collection\";\r\ntw.local.odcRequest.cif = \"02366014\";\r\ntw.local.odcRequest.customerName = \"somaia\";\r\ntw.local.odcRequest.parentRequestNo = \"\";\r\ntw.local.odcRequest.requestDate = new TWDate();\r\ntw.local.odcRequest.ImporterName = \"importer name\";\r\ntw.local.odcRequest.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\ntw.local.odcRequest.appInfo.requestDate = \"\";\r\ntw.local.odcRequest.appInfo.status = \"In progress\";\r\ntw.local.odcRequest.appInfo.subStatus = \"Initiated\";\r\ntw.local.odcRequest.appInfo.initiator = \"odchubcumkr10\";\r\ntw.local.odcRequest.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.appInfo.branch.name = \"\";\r\ntw.local.odcRequest.appInfo.branch.value = \"\";\r\ntw.local.odcRequest.appInfo.requestName = \"ODC Collection\";\r\ntw.local.odcRequest.appInfo.requestType = \"\";\r\ntw.local.odcRequest.appInfo.stepName = \"\";\r\ntw.local.odcRequest.appInfo.appRef = \"\";\r\ntw.local.odcRequest.appInfo.appID = \"\";\r\ntw.local.odcRequest.appInfo.instanceID = tw.system.currentProcessInstance.id;\r\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\r\ntw.local.odcRequest.CustomerInfo.cif = \"02366014\";\r\ntw.local.odcRequest.CustomerInfo.customerName = \"Somaia\";\r\ntw.local.odcRequest.CustomerInfo.addressLine1 = \"Nasr Ciy\";\r\ntw.local.odcRequest.CustomerInfo.addressLine2 = \"addressLine2\";\r\ntw.local.odcRequest.CustomerInfo.addressLine3 = \"\";\r\ntw.local.odcRequest.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.CustomerInfo.customerSector.name = \"customerSector\";\r\ntw.local.odcRequest.CustomerInfo.customerSector.value = \"customerSector\";\r\ntw.local.odcRequest.CustomerInfo.customerType = \"Individual\";\r\ntw.local.odcRequest.CustomerInfo.customerNoCBE = \"\";\r\ntw.local.odcRequest.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.CustomerInfo.facilityType.name = \"facilityType\";\r\ntw.local.odcRequest.CustomerInfo.facilityType.value = \"facilityType\";\r\ntw.local.odcRequest.CustomerInfo.commercialRegistrationNo = \"\";\r\ntw.local.odcRequest.CustomerInfo.commercialRegistrationOffice = \"\";\r\ntw.local.odcRequest.CustomerInfo.taxCardNo = \"222\";\r\ntw.local.odcRequest.CustomerInfo.importCardNo = \"1245\";\r\ntw.local.odcRequest.CustomerInfo.initiationHub = \"initiationHub\";\r\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\r\ntw.local.odcRequest.BasicDetails.requestNature = \"update\";\r\ntw.local.odcRequest.BasicDetails.requestType = \"\";\r\ntw.local.odcRequest.BasicDetails.parentRequestNo = \"\";\r\ntw.local.odcRequest.BasicDetails.requestState = \"\";\r\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \"\";\r\ntw.local.odcRequest.BasicDetails.contractStage = \"\";\r\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \"\";\r\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \"\";\r\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \"\";\r\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \"\";\r\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.productCategory.name = \"\";\r\ntw.local.odcRequest.BasicDetails.productCategory.value = \"\";\r\ntw.local.odcRequest.BasicDetails.commodityDescription = \"\";\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \"\";\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \"\";\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"\";\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\ntw.local.odcRequest.GeneratedDocumentInfo.customerName = \"Somaia\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.customerAddress = \"Nasr City\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions[0] = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = \"Instructions Extra\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = \"special Instructions Extra\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 0.0;\r\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \"EGP\";\r\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \"EGP\";\r\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 0.0;\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \"collectionAccount\";\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \"collectionAccount\";\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\ntw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FcCollections.currency.name = \"\";\r\ntw.local.odcRequest.FcCollections.currency.value = \"\";\r\ntw.local.odcRequest.FcCollections.standardExchangeRate = 0.0;\r\ntw.local.odcRequest.FcCollections.negotiatedExchangeRate = 0.0;\r\ntw.local.odcRequest.FcCollections.fromDate = new TWDate();\r\ntw.local.odcRequest.FcCollections.ToDate = new TWDate();\r\ntw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FcCollections.accountNo.name = \"34566\";\r\ntw.local.odcRequest.FcCollections.accountNo.value = \"\";\r\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \"\";\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \"\";\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 566.0;\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 232.0;\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \"EG\";\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\ntw.local.odcRequest.FcCollections.isReversed = false;\r\ntw.local.odcRequest.FcCollections.usedAmount = 0.0;\r\ntw.local.odcRequest.FcCollections.calculatedAmount = 0.0;\r\ntw.local.odcRequest.FcCollections.totalAllocatedAmount = 0.0;\r\ntw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0].name = \"\";\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0].value = \"\";\r\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\ntw.local.odcRequest.FinancialDetailsFO.discount = 0.0;\r\ntw.local.odcRequest.FinancialDetailsFO.extraCharges = 0.0;\r\ntw.local.odcRequest.FinancialDetailsFO.ourCharges = 0.0;\r\ntw.local.odcRequest.FinancialDetailsFO.amountSight = 340.0;\r\ntw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization = 20.0;\r\ntw.local.odcRequest.FinancialDetailsFO.amountDefAvalization = 30.0;\r\ntw.local.odcRequest.FinancialDetailsFO.collectableAmount = 0.0;\r\ntw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 0.0;\r\ntw.local.odcRequest.FinancialDetailsFO.maturityDate = new TWDate();\r\ntw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity = 2;\r\ntw.local.odcRequest.FinancialDetailsFO.referenceNo = \"\";\r\ntw.local.odcRequest.FinancialDetailsFO.financeApprovalNo = \"234\";\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub.name = \"\";\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub.value = \"\"; \r\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\r\ntw.local.odcRequest.OdcCollection.amount = 0.0;\r\ntw.local.odcRequest.OdcCollection.currency = \"\";\r\ntw.local.odcRequest.OdcCollection.informCADAboutTheCollection = false; \r\n tw.local.odcRequest.complianceApproval = false;\r\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\r\ntw.local.odcRequest.stepLog.startTime = new TWDate();\r\ntw.local.odcRequest.stepLog.endTime = new TWDate();\r\n\/\/tw.local.odcRequest.stepLog.userName = \"\";\r\n\/\/tw.local.odcRequest.stepLog.role = \"\";\r\n\/\/tw.local.odcRequest.stepLog.step = \"\";\r\n\/\/tw.local.odcRequest.stepLog.action = \"\";\r\n\/\/tw.local.odcRequest.stepLog.comment = \"\";\r\n\/\/tw.local.odcRequest.stepLog.terminateReason = \"\";\r\n\/\/tw.local.odcRequest.stepLog.returnReason = \"\";\r\ntw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.actions[0] = \"\";\r\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\ntw.local.odcRequest.attachmentDetails.folderID = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"\";\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"\";\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"\";\r\ntw.local.odcRequest.initiator = \"\";\r\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\r\ntw.local.odcRequest.complianceComments[0] = new tw.object.StepLog();\r\ntw.local.odcRequest.complianceComments[0].startTime = new TWDate();\r\ntw.local.odcRequest.complianceComments[0].endTime = new TWDate();\r\ntw.local.odcRequest.complianceComments[0].userName = \"\";\r\ntw.local.odcRequest.complianceComments[0].role = \"\";\r\ntw.local.odcRequest.complianceComments[0].step = \"\";\r\ntw.local.odcRequest.complianceComments[0].action = \"\";\r\ntw.local.odcRequest.complianceComments[0].comment = \"\";\r\ntw.local.odcRequest.complianceComments[0].terminateReason = \"\";\r\ntw.local.odcRequest.complianceComments[0].returnReason = \"\";\r\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\r\ntw.local.odcRequest.History[0].startTime = new TWDate();\r\ntw.local.odcRequest.History[0].endTime = new TWDate()+5;\r\ntw.local.odcRequest.History[0].userName = \"Maker\";\r\ntw.local.odcRequest.History[0].role = \"Maker\";\r\ntw.local.odcRequest.History[0].step = \"Act06\";\r\ntw.local.odcRequest.History[0].action = \"Approve\";\r\ntw.local.odcRequest.History[0].comment = \"comment1\";\r\ntw.local.odcRequest.History[0].terminateReason = \"\";\r\ntw.local.odcRequest.History[0].returnReason = \"\";\r\ntw.local.odcRequest.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.documentSource.name = \"\";\r\ntw.local.odcRequest.documentSource.value = \"\";\r\ntw.local.odcRequest.folderID = \"\";\r\n\r\n\r\n"]}}],"laneSet":[{"id":"d98653bf-4f2a-4878-8e5f-31117e8c085f","lane":[{"flowNodeRef":["048a42a8-dbc4-4c10-8206-713a33ddf554","ddc67558-5d18-4966-8a8a-3880c50d3f63","fdb88fd2-37b3-4259-85d0-c35f4de2843f","baaed40a-0898-4f73-89dd-f33711c93c61"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"7376be27-ce95-4f86-87f9-4874d70f3b3c","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Init ODC Request","declaredType":"process","id":"1.8e36e548-f4cb-44df-9910-c33585076ffa","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.071eaf11-ca85-4a87-8d0a-3e010885561e"}],"inputSet":[{"dataInputRefs":["2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb"]}],"outputSet":[{"dataOutputRefs":["2055.071eaf11-ca85-4a87-8d0a-3e010885561e"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.MultiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.MultiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.MultiTenorDates[0].date = new TWDate();\nautoObject.MultiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = 0;\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].rate = 0.0;\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].minimumAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.initiator = \"\";\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb</processParameterId>
            <processId>1.8e36e548-f4cb-44df-9910-c33585076ffa</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bb896884-24b4-4bc3-9f79-d13718aa9ed1</guid>
            <versionId>6ca65c9a-0f2b-4d7b-92cd-ddab08279e8f</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.071eaf11-ca85-4a87-8d0a-3e010885561e</processParameterId>
            <processId>1.8e36e548-f4cb-44df-9910-c33585076ffa</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>afe0ecf0-eaaf-475f-85ae-ce4a4125f416</guid>
            <versionId>212b3e0f-3491-41e6-9536-090bd940fac2</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f</processItemId>
            <processId>1.8e36e548-f4cb-44df-9910-c33585076ffa</processId>
            <name>Init ODC Request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.b51189dc-236d-4dda-bd3d-7e03d4eeb5a7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2830</guid>
            <versionId>5a63b2db-e5de-44ee-8b3a-d3ac50bb3c20</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.62769f3c-a6f5-421a-8e71-c2b61e2b897c</processItemPrePostId>
                <processItemId>2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>31abb769-393e-45b3-82dd-0748003e061c</guid>
                <versionId>092fbeac-a900-4d48-b1cb-384864019ad3</versionId>
            </processPrePosts>
            <layoutData x="360" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.b51189dc-236d-4dda-bd3d-7e03d4eeb5a7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.odcRequest.requestDate=  new TWDate();&#xD;
tw.local.odcRequest.ImporterName= "Somaia Galal";&#xD;
tw.local.odcRequest.BasicDetails.requestState = "new";&#xD;
tw.local.odcRequest.BasicDetails.flexCubeContractNo = "00104230000788";&#xD;
tw.local.odcRequest.BasicDetails.contractStage = "stage";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.name = "";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.value = "export purpose";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.name = "";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.value = "payment terms";&#xD;
tw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.productCategory.name = "";&#xD;
tw.local.odcRequest.BasicDetails.productCategory.value = "product category";&#xD;
tw.local.odcRequest.BasicDetails.commodityDescription = "commodity Description";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.name = "";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.value = "Egypt";&#xD;
tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "2341234";&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "7890234";&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
//tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.odcRequest.FinancialDetailsBR.documentAmount = 20.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.name = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.value = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();&#xD;
tw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 10000.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = "********";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = "*********";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = "********";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = "12345";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = "12345";&#xD;
tw.local.odcRequest.complianceApproval = false;&#xD;
//tw.local.odcRequest.stepLog = new tw.object.StepLog();&#xD;
tw.local.odcRequest.stepLog.startTime = new TWDate();&#xD;
tw.local.odcRequest.stepLog.endTime = new TWDate();&#xD;
tw.local.odcRequest.stepLog.userName = "";&#xD;
tw.local.odcRequest.stepLog.role = "";&#xD;
tw.local.odcRequest.stepLog.step = "";&#xD;
tw.local.odcRequest.stepLog.action = "";&#xD;
tw.local.odcRequest.stepLog.comment = "";&#xD;
tw.local.odcRequest.stepLog.terminateReason = "";&#xD;
tw.local.odcRequest.stepLog.returnReason = "";&#xD;
//tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.actions[0] = "";&#xD;
//tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();&#xD;
tw.local.odcRequest.attachmentDetails.folderID = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;&#xD;
tw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].description = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].arabicName = "";&#xD;
//tw.local.odcRequest.History = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.History[0] = new tw.object.StepLog();&#xD;
tw.local.odcRequest.History[0].startTime = new TWDate();&#xD;
tw.local.odcRequest.History[0].endTime = new TWDate();&#xD;
tw.local.odcRequest.History[0].userName = "";&#xD;
tw.local.odcRequest.History[0].role = "Maker";&#xD;
tw.local.odcRequest.History[0].step = "act06";&#xD;
tw.local.odcRequest.History[0].action = "submit";&#xD;
tw.local.odcRequest.History[0].comment = "no comment";&#xD;
tw.local.odcRequest.History[0].terminateReason = "";&#xD;
tw.local.odcRequest.History[0].returnReason = "";&#xD;
</script>
                <isRule>false</isRule>
                <guid>4a613616-4451-45af-a43d-8318d70c959b</guid>
                <versionId>f8286857-d99a-41ce-9790-7b420c784d75</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ddc67558-5d18-4966-8a8a-3880c50d3f63</processItemId>
            <processId>1.8e36e548-f4cb-44df-9910-c33585076ffa</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.f47f3b6c-8a3f-4193-8d91-e25f8553cb70</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2866</guid>
            <versionId>d784f284-d086-4b2f-a312-39fe4efd71f6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.f47f3b6c-8a3f-4193-8d91-e25f8553cb70</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ab7e7089-8fe7-48e7-a7b6-3c3e769a9e7a</guid>
                <versionId>c99af813-92c5-440f-a031-e80f74cf99df</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.baaed40a-0898-4f73-89dd-f33711c93c61</processItemId>
            <processId>1.8e36e548-f4cb-44df-9910-c33585076ffa</processId>
            <name>Copy of Init ODC Request</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.e691e74d-ccdf-446f-89d2-bdc63a6f093f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:228a</guid>
            <versionId>e50bffe1-e699-413f-b4a9-8f3bc29aed26</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="362" y="184">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.e691e74d-ccdf-446f-89d2-bdc63a6f093f</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.odcRequest = new tw.object.ODCRequest();&#xD;
tw.local.odcRequest.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.requestNature.name = "Update Request";&#xD;
tw.local.odcRequest.requestNature.value = "update";&#xD;
tw.local.odcRequest.requestType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.requestType.name = "collection" ;&#xD;
tw.local.odcRequest.requestType.value = "ODC Collection";&#xD;
tw.local.odcRequest.cif = "02366014";&#xD;
tw.local.odcRequest.customerName = "somaia";&#xD;
tw.local.odcRequest.parentRequestNo = "";&#xD;
tw.local.odcRequest.requestDate = new TWDate();&#xD;
tw.local.odcRequest.ImporterName = "importer name";&#xD;
tw.local.odcRequest.appInfo = new tw.object.toolkit.NBEC.AppInfo();&#xD;
tw.local.odcRequest.appInfo.requestDate = "";&#xD;
tw.local.odcRequest.appInfo.status = "In progress";&#xD;
tw.local.odcRequest.appInfo.subStatus = "Initiated";&#xD;
tw.local.odcRequest.appInfo.initiator = "odchubcumkr10";&#xD;
tw.local.odcRequest.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.appInfo.branch.name = "";&#xD;
tw.local.odcRequest.appInfo.branch.value = "";&#xD;
tw.local.odcRequest.appInfo.requestName = "ODC Collection";&#xD;
tw.local.odcRequest.appInfo.requestType = "";&#xD;
tw.local.odcRequest.appInfo.stepName = "";&#xD;
tw.local.odcRequest.appInfo.appRef = "";&#xD;
tw.local.odcRequest.appInfo.appID = "";&#xD;
tw.local.odcRequest.appInfo.instanceID = tw.system.currentProcessInstance.id;&#xD;
tw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();&#xD;
tw.local.odcRequest.CustomerInfo.cif = "02366014";&#xD;
tw.local.odcRequest.CustomerInfo.customerName = "Somaia";&#xD;
tw.local.odcRequest.CustomerInfo.addressLine1 = "Nasr Ciy";&#xD;
tw.local.odcRequest.CustomerInfo.addressLine2 = "addressLine2";&#xD;
tw.local.odcRequest.CustomerInfo.addressLine3 = "";&#xD;
tw.local.odcRequest.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.CustomerInfo.customerSector.name = "customerSector";&#xD;
tw.local.odcRequest.CustomerInfo.customerSector.value = "customerSector";&#xD;
tw.local.odcRequest.CustomerInfo.customerType = "Individual";&#xD;
tw.local.odcRequest.CustomerInfo.customerNoCBE = "";&#xD;
tw.local.odcRequest.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.CustomerInfo.facilityType.name = "facilityType";&#xD;
tw.local.odcRequest.CustomerInfo.facilityType.value = "facilityType";&#xD;
tw.local.odcRequest.CustomerInfo.commercialRegistrationNo = "";&#xD;
tw.local.odcRequest.CustomerInfo.commercialRegistrationOffice = "";&#xD;
tw.local.odcRequest.CustomerInfo.taxCardNo = "222";&#xD;
tw.local.odcRequest.CustomerInfo.importCardNo = "1245";&#xD;
tw.local.odcRequest.CustomerInfo.initiationHub = "initiationHub";&#xD;
tw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();&#xD;
tw.local.odcRequest.BasicDetails.requestNature = "update";&#xD;
tw.local.odcRequest.BasicDetails.requestType = "";&#xD;
tw.local.odcRequest.BasicDetails.parentRequestNo = "";&#xD;
tw.local.odcRequest.BasicDetails.requestState = "";&#xD;
tw.local.odcRequest.BasicDetails.flexCubeContractNo = "";&#xD;
tw.local.odcRequest.BasicDetails.contractStage = "";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.name = "";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.value = "";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.name = "";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.value = "";&#xD;
tw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.productCategory.name = "";&#xD;
tw.local.odcRequest.BasicDetails.productCategory.value = "";&#xD;
tw.local.odcRequest.BasicDetails.commodityDescription = "";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.name = "";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.value = "";&#xD;
tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "";&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "";&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.customerName = "Somaia";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.customerAddress = "Nasr City";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = "Instructions Extra";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = "special Instructions Extra";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.odcRequest.FinancialDetailsBR.documentAmount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.name = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.value = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();&#xD;
tw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = "collectionAccount";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = "collectionAccount";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = "";&#xD;
tw.local.odcRequest.FcCollections = new tw.object.FCCollections();&#xD;
tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.currency.name = "";&#xD;
tw.local.odcRequest.FcCollections.currency.value = "";&#xD;
tw.local.odcRequest.FcCollections.standardExchangeRate = 0.0;&#xD;
tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 0.0;&#xD;
tw.local.odcRequest.FcCollections.fromDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.ToDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.accountNo.name = "34566";&#xD;
tw.local.odcRequest.FcCollections.accountNo.value = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = "";&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = "";&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 566.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 232.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = "EG";&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
tw.local.odcRequest.FcCollections.isReversed = false;&#xD;
tw.local.odcRequest.FcCollections.usedAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.calculatedAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.totalAllocatedAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts[0].name = "";&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts[0].value = "";&#xD;
tw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
tw.local.odcRequest.FinancialDetailsFO.discount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.extraCharges = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.ourCharges = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.amountSight = 340.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization = 20.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization = 30.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.collectableAmount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.maturityDate = new TWDate();&#xD;
tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity = 2;&#xD;
tw.local.odcRequest.FinancialDetailsFO.referenceNo = "";&#xD;
tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo = "234";&#xD;
tw.local.odcRequest.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsFO.executionHub.name = "";&#xD;
tw.local.odcRequest.FinancialDetailsFO.executionHub.value = ""; &#xD;
tw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();&#xD;
tw.local.odcRequest.OdcCollection.amount = 0.0;&#xD;
tw.local.odcRequest.OdcCollection.currency = "";&#xD;
tw.local.odcRequest.OdcCollection.informCADAboutTheCollection = false; &#xD;
 tw.local.odcRequest.complianceApproval = false;&#xD;
tw.local.odcRequest.stepLog = new tw.object.StepLog();&#xD;
tw.local.odcRequest.stepLog.startTime = new TWDate();&#xD;
tw.local.odcRequest.stepLog.endTime = new TWDate();&#xD;
//tw.local.odcRequest.stepLog.userName = "";&#xD;
//tw.local.odcRequest.stepLog.role = "";&#xD;
//tw.local.odcRequest.stepLog.step = "";&#xD;
//tw.local.odcRequest.stepLog.action = "";&#xD;
//tw.local.odcRequest.stepLog.comment = "";&#xD;
//tw.local.odcRequest.stepLog.terminateReason = "";&#xD;
//tw.local.odcRequest.stepLog.returnReason = "";&#xD;
tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.actions[0] = "";&#xD;
tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();&#xD;
tw.local.odcRequest.attachmentDetails.folderID = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;&#xD;
tw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].description = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].arabicName = "";&#xD;
tw.local.odcRequest.initiator = "";&#xD;
tw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.complianceComments[0] = new tw.object.StepLog();&#xD;
tw.local.odcRequest.complianceComments[0].startTime = new TWDate();&#xD;
tw.local.odcRequest.complianceComments[0].endTime = new TWDate();&#xD;
tw.local.odcRequest.complianceComments[0].userName = "";&#xD;
tw.local.odcRequest.complianceComments[0].role = "";&#xD;
tw.local.odcRequest.complianceComments[0].step = "";&#xD;
tw.local.odcRequest.complianceComments[0].action = "";&#xD;
tw.local.odcRequest.complianceComments[0].comment = "";&#xD;
tw.local.odcRequest.complianceComments[0].terminateReason = "";&#xD;
tw.local.odcRequest.complianceComments[0].returnReason = "";&#xD;
tw.local.odcRequest.History = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.History[0] = new tw.object.StepLog();&#xD;
tw.local.odcRequest.History[0].startTime = new TWDate();&#xD;
tw.local.odcRequest.History[0].endTime = new TWDate()+5;&#xD;
tw.local.odcRequest.History[0].userName = "Maker";&#xD;
tw.local.odcRequest.History[0].role = "Maker";&#xD;
tw.local.odcRequest.History[0].step = "Act06";&#xD;
tw.local.odcRequest.History[0].action = "Approve";&#xD;
tw.local.odcRequest.History[0].comment = "comment1";&#xD;
tw.local.odcRequest.History[0].terminateReason = "";&#xD;
tw.local.odcRequest.History[0].returnReason = "";&#xD;
tw.local.odcRequest.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.documentSource.name = "";&#xD;
tw.local.odcRequest.documentSource.value = "";&#xD;
tw.local.odcRequest.folderID = "";&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>ff72e73d-37af-4c64-96aa-71ccbc6f237b</guid>
                <versionId>ac2fa919-e0c1-4b56-8eae-94a7d1874ac2</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="Init ODC Request" id="1.8e36e548-f4cb-44df-9910-c33585076ffa" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:isSecured>true</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.ODCRequest();
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.MultiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.MultiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.MultiTenorDates[0].date = new TWDate();
autoObject.MultiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = 0;
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].rate = 0.0;
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].minimumAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.initiator = "";
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.071eaf11-ca85-4a87-8d0a-3e010885561e" />
                        <ns16:inputSet>
                            <ns16:dataInputRefs>2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb</ns16:dataInputRefs>
                        </ns16:inputSet>
                        <ns16:outputSet>
                            <ns16:dataOutputRefs>2055.071eaf11-ca85-4a87-8d0a-3e010885561e</ns16:dataOutputRefs>
                        </ns16:outputSet>
                    </ns16:ioSpecification>
                    <ns16:laneSet id="d98653bf-4f2a-4878-8e5f-31117e8c085f">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="7376be27-ce95-4f86-87f9-4874d70f3b3c" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>048a42a8-dbc4-4c10-8206-713a33ddf554</ns16:flowNodeRef>
                            <ns16:flowNodeRef>ddc67558-5d18-4966-8a8a-3880c50d3f63</ns16:flowNodeRef>
                            <ns16:flowNodeRef>fdb88fd2-37b3-4259-85d0-c35f4de2843f</ns16:flowNodeRef>
                            <ns16:flowNodeRef>baaed40a-0898-4f73-89dd-f33711c93c61</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="048a42a8-dbc4-4c10-8206-713a33ddf554">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.9d7c8285-0639-40bc-85c7-70dd53b43140</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:endEvent name="End" id="ddc67558-5d18-4966-8a8a-3880c50d3f63">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            <ns3:endStateId>guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2866</ns3:endStateId>
                        </ns16:extensionElements>
                        <ns16:incoming>6b52cd5b-72b2-4808-86e5-b29efcacefb1</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="048a42a8-dbc4-4c10-8206-713a33ddf554" targetRef="fdb88fd2-37b3-4259-85d0-c35f4de2843f" name="To End" id="2027.9d7c8285-0639-40bc-85c7-70dd53b43140">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init ODC Request" id="fdb88fd2-37b3-4259-85d0-c35f4de2843f">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="360" y="57" width="95" height="70" />
                            <ns3:postAssignmentScript />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.9d7c8285-0639-40bc-85c7-70dd53b43140</ns16:incoming>
                        <ns16:outgoing>6b52cd5b-72b2-4808-86e5-b29efcacefb1</ns16:outgoing>
                        <ns16:script>tw.local.odcRequest.requestDate=  new TWDate();&#xD;
tw.local.odcRequest.ImporterName= "Somaia Galal";&#xD;
tw.local.odcRequest.BasicDetails.requestState = "new";&#xD;
tw.local.odcRequest.BasicDetails.flexCubeContractNo = "00104230000788";&#xD;
tw.local.odcRequest.BasicDetails.contractStage = "stage";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.name = "";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.value = "export purpose";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.name = "";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.value = "payment terms";&#xD;
tw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.productCategory.name = "";&#xD;
tw.local.odcRequest.BasicDetails.productCategory.value = "product category";&#xD;
tw.local.odcRequest.BasicDetails.commodityDescription = "commodity Description";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.name = "";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.value = "Egypt";&#xD;
tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "2341234";&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "7890234";&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
//tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.odcRequest.FinancialDetailsBR.documentAmount = 20.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.name = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.value = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();&#xD;
tw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 10000.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = "********";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = "*********";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = "********";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = "12345";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = "12345";&#xD;
tw.local.odcRequest.complianceApproval = false;&#xD;
//tw.local.odcRequest.stepLog = new tw.object.StepLog();&#xD;
tw.local.odcRequest.stepLog.startTime = new TWDate();&#xD;
tw.local.odcRequest.stepLog.endTime = new TWDate();&#xD;
tw.local.odcRequest.stepLog.userName = "";&#xD;
tw.local.odcRequest.stepLog.role = "";&#xD;
tw.local.odcRequest.stepLog.step = "";&#xD;
tw.local.odcRequest.stepLog.action = "";&#xD;
tw.local.odcRequest.stepLog.comment = "";&#xD;
tw.local.odcRequest.stepLog.terminateReason = "";&#xD;
tw.local.odcRequest.stepLog.returnReason = "";&#xD;
//tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.actions[0] = "";&#xD;
//tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();&#xD;
tw.local.odcRequest.attachmentDetails.folderID = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;&#xD;
tw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].description = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].arabicName = "";&#xD;
//tw.local.odcRequest.History = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.History[0] = new tw.object.StepLog();&#xD;
tw.local.odcRequest.History[0].startTime = new TWDate();&#xD;
tw.local.odcRequest.History[0].endTime = new TWDate();&#xD;
tw.local.odcRequest.History[0].userName = "";&#xD;
tw.local.odcRequest.History[0].role = "Maker";&#xD;
tw.local.odcRequest.History[0].step = "act06";&#xD;
tw.local.odcRequest.History[0].action = "submit";&#xD;
tw.local.odcRequest.History[0].comment = "no comment";&#xD;
tw.local.odcRequest.History[0].terminateReason = "";&#xD;
tw.local.odcRequest.History[0].returnReason = "";&#xD;
</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="fdb88fd2-37b3-4259-85d0-c35f4de2843f" targetRef="ddc67558-5d18-4966-8a8a-3880c50d3f63" name="To End" id="6b52cd5b-72b2-4808-86e5-b29efcacefb1">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Copy of Init ODC Request" id="baaed40a-0898-4f73-89dd-f33711c93c61">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="362" y="184" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:script>tw.local.odcRequest = new tw.object.ODCRequest();&#xD;
tw.local.odcRequest.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.requestNature.name = "Update Request";&#xD;
tw.local.odcRequest.requestNature.value = "update";&#xD;
tw.local.odcRequest.requestType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.requestType.name = "collection" ;&#xD;
tw.local.odcRequest.requestType.value = "ODC Collection";&#xD;
tw.local.odcRequest.cif = "02366014";&#xD;
tw.local.odcRequest.customerName = "somaia";&#xD;
tw.local.odcRequest.parentRequestNo = "";&#xD;
tw.local.odcRequest.requestDate = new TWDate();&#xD;
tw.local.odcRequest.ImporterName = "importer name";&#xD;
tw.local.odcRequest.appInfo = new tw.object.toolkit.NBEC.AppInfo();&#xD;
tw.local.odcRequest.appInfo.requestDate = "";&#xD;
tw.local.odcRequest.appInfo.status = "In progress";&#xD;
tw.local.odcRequest.appInfo.subStatus = "Initiated";&#xD;
tw.local.odcRequest.appInfo.initiator = "odchubcumkr10";&#xD;
tw.local.odcRequest.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.appInfo.branch.name = "";&#xD;
tw.local.odcRequest.appInfo.branch.value = "";&#xD;
tw.local.odcRequest.appInfo.requestName = "ODC Collection";&#xD;
tw.local.odcRequest.appInfo.requestType = "";&#xD;
tw.local.odcRequest.appInfo.stepName = "";&#xD;
tw.local.odcRequest.appInfo.appRef = "";&#xD;
tw.local.odcRequest.appInfo.appID = "";&#xD;
tw.local.odcRequest.appInfo.instanceID = tw.system.currentProcessInstance.id;&#xD;
tw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();&#xD;
tw.local.odcRequest.CustomerInfo.cif = "02366014";&#xD;
tw.local.odcRequest.CustomerInfo.customerName = "Somaia";&#xD;
tw.local.odcRequest.CustomerInfo.addressLine1 = "Nasr Ciy";&#xD;
tw.local.odcRequest.CustomerInfo.addressLine2 = "addressLine2";&#xD;
tw.local.odcRequest.CustomerInfo.addressLine3 = "";&#xD;
tw.local.odcRequest.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.CustomerInfo.customerSector.name = "customerSector";&#xD;
tw.local.odcRequest.CustomerInfo.customerSector.value = "customerSector";&#xD;
tw.local.odcRequest.CustomerInfo.customerType = "Individual";&#xD;
tw.local.odcRequest.CustomerInfo.customerNoCBE = "";&#xD;
tw.local.odcRequest.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.CustomerInfo.facilityType.name = "facilityType";&#xD;
tw.local.odcRequest.CustomerInfo.facilityType.value = "facilityType";&#xD;
tw.local.odcRequest.CustomerInfo.commercialRegistrationNo = "";&#xD;
tw.local.odcRequest.CustomerInfo.commercialRegistrationOffice = "";&#xD;
tw.local.odcRequest.CustomerInfo.taxCardNo = "222";&#xD;
tw.local.odcRequest.CustomerInfo.importCardNo = "1245";&#xD;
tw.local.odcRequest.CustomerInfo.initiationHub = "initiationHub";&#xD;
tw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();&#xD;
tw.local.odcRequest.BasicDetails.requestNature = "update";&#xD;
tw.local.odcRequest.BasicDetails.requestType = "";&#xD;
tw.local.odcRequest.BasicDetails.parentRequestNo = "";&#xD;
tw.local.odcRequest.BasicDetails.requestState = "";&#xD;
tw.local.odcRequest.BasicDetails.flexCubeContractNo = "";&#xD;
tw.local.odcRequest.BasicDetails.contractStage = "";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.name = "";&#xD;
tw.local.odcRequest.BasicDetails.exportPurpose.value = "";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.name = "";&#xD;
tw.local.odcRequest.BasicDetails.paymentTerms.value = "";&#xD;
tw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.productCategory.name = "";&#xD;
tw.local.odcRequest.BasicDetails.productCategory.value = "";&#xD;
tw.local.odcRequest.BasicDetails.commodityDescription = "";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.name = "";&#xD;
tw.local.odcRequest.BasicDetails.CountryOfOrigin.value = "";&#xD;
tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "";&#xD;
tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "";&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.customerName = "Somaia";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.customerAddress = "Nasr City";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = "Instructions Extra";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[0] = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = "special Instructions Extra";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.odcRequest.FinancialDetailsBR.documentAmount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.name = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency.value = "EGP";&#xD;
tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();&#xD;
tw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = "collectionAccount";&#xD;
tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = "collectionAccount";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = "";&#xD;
tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = "";&#xD;
tw.local.odcRequest.FcCollections = new tw.object.FCCollections();&#xD;
tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.currency.name = "";&#xD;
tw.local.odcRequest.FcCollections.currency.value = "";&#xD;
tw.local.odcRequest.FcCollections.standardExchangeRate = 0.0;&#xD;
tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 0.0;&#xD;
tw.local.odcRequest.FcCollections.fromDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.ToDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.accountNo.name = "34566";&#xD;
tw.local.odcRequest.FcCollections.accountNo.value = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = "";&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = "";&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = "";&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 566.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 232.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = "EG";&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
tw.local.odcRequest.FcCollections.isReversed = false;&#xD;
tw.local.odcRequest.FcCollections.usedAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.calculatedAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.totalAllocatedAmount = 0.0;&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts[0].name = "";&#xD;
tw.local.odcRequest.FcCollections.listOfAccounts[0].value = "";&#xD;
tw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
tw.local.odcRequest.FinancialDetailsFO.discount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.extraCharges = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.ourCharges = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.amountSight = 340.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization = 20.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization = 30.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.collectableAmount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 0.0;&#xD;
tw.local.odcRequest.FinancialDetailsFO.maturityDate = new TWDate();&#xD;
tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity = 2;&#xD;
tw.local.odcRequest.FinancialDetailsFO.referenceNo = "";&#xD;
tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo = "234";&#xD;
tw.local.odcRequest.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsFO.executionHub.name = "";&#xD;
tw.local.odcRequest.FinancialDetailsFO.executionHub.value = ""; &#xD;
tw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();&#xD;
tw.local.odcRequest.OdcCollection.amount = 0.0;&#xD;
tw.local.odcRequest.OdcCollection.currency = "";&#xD;
tw.local.odcRequest.OdcCollection.informCADAboutTheCollection = false; &#xD;
 tw.local.odcRequest.complianceApproval = false;&#xD;
tw.local.odcRequest.stepLog = new tw.object.StepLog();&#xD;
tw.local.odcRequest.stepLog.startTime = new TWDate();&#xD;
tw.local.odcRequest.stepLog.endTime = new TWDate();&#xD;
//tw.local.odcRequest.stepLog.userName = "";&#xD;
//tw.local.odcRequest.stepLog.role = "";&#xD;
//tw.local.odcRequest.stepLog.step = "";&#xD;
//tw.local.odcRequest.stepLog.action = "";&#xD;
//tw.local.odcRequest.stepLog.comment = "";&#xD;
//tw.local.odcRequest.stepLog.terminateReason = "";&#xD;
//tw.local.odcRequest.stepLog.returnReason = "";&#xD;
tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.actions[0] = "";&#xD;
tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();&#xD;
tw.local.odcRequest.attachmentDetails.folderID = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;&#xD;
tw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].name = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].description = "";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].arabicName = "";&#xD;
tw.local.odcRequest.initiator = "";&#xD;
tw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.complianceComments[0] = new tw.object.StepLog();&#xD;
tw.local.odcRequest.complianceComments[0].startTime = new TWDate();&#xD;
tw.local.odcRequest.complianceComments[0].endTime = new TWDate();&#xD;
tw.local.odcRequest.complianceComments[0].userName = "";&#xD;
tw.local.odcRequest.complianceComments[0].role = "";&#xD;
tw.local.odcRequest.complianceComments[0].step = "";&#xD;
tw.local.odcRequest.complianceComments[0].action = "";&#xD;
tw.local.odcRequest.complianceComments[0].comment = "";&#xD;
tw.local.odcRequest.complianceComments[0].terminateReason = "";&#xD;
tw.local.odcRequest.complianceComments[0].returnReason = "";&#xD;
tw.local.odcRequest.History = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.History[0] = new tw.object.StepLog();&#xD;
tw.local.odcRequest.History[0].startTime = new TWDate();&#xD;
tw.local.odcRequest.History[0].endTime = new TWDate()+5;&#xD;
tw.local.odcRequest.History[0].userName = "Maker";&#xD;
tw.local.odcRequest.History[0].role = "Maker";&#xD;
tw.local.odcRequest.History[0].step = "Act06";&#xD;
tw.local.odcRequest.History[0].action = "Approve";&#xD;
tw.local.odcRequest.History[0].comment = "comment1";&#xD;
tw.local.odcRequest.History[0].terminateReason = "";&#xD;
tw.local.odcRequest.History[0].returnReason = "";&#xD;
tw.local.odcRequest.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.documentSource.name = "";&#xD;
tw.local.odcRequest.documentSource.value = "";&#xD;
tw.local.odcRequest.folderID = "";&#xD;
&#xD;
&#xD;
</ns16:script>
                    </ns16:scriptTask>
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6b52cd5b-72b2-4808-86e5-b29efcacefb1</processLinkId>
            <processId>1.8e36e548-f4cb-44df-9910-c33585076ffa</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ddc67558-5d18-4966-8a8a-3880c50d3f63</toProcessItemId>
            <guid>ee1659db-711d-4c86-9c2b-aa95dc17b3be</guid>
            <versionId>094f94f8-7c5d-4530-8d3b-aa37ef0428e7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f</fromProcessItemId>
            <toProcessItemId>2025.ddc67558-5d18-4966-8a8a-3880c50d3f63</toProcessItemId>
        </link>
    </process>
</teamworks>

