<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.9ad7c20d-998e-4629-84b9-f365c85a6733" name="<PERSON>rrorPojo">
        <lastModified>1693480732497</lastModified>
        <lastModifiedBy>abdel<PERSON>man.saleh</lastModifiedBy>
        <classId>12.9ad7c20d-998e-4629-84b9-f365c85a6733</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>true</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.9ad7c20d-998e-4629-84b9-f365c85a6733</externalId>
        <dependencySummary>&lt;dependencySummary id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1732"&gt;
  &lt;artifactReference id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1731"&gt;
    &lt;refId&gt;/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4&lt;/refId&gt;
    &lt;refType&gt;1&lt;/refType&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1730"&gt;
      &lt;name&gt;externalId&lt;/name&gt;
      &lt;value&gt;http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
    &lt;nameValuePair id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-172f"&gt;
      &lt;name&gt;mimeType&lt;/name&gt;
      &lt;value&gt;xsd&lt;/value&gt;
    &lt;/nameValuePair&gt;
  &lt;/artifactReference&gt;
&lt;/dependencySummary&gt;</dependencySummary>
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","complexType":[{"annotation":{"documentation":[{"content":["ErrorPojo"]}],"appinfo":[{"shared":[false],"advancedProperties":[{"namespace":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/externalservice\/swagger\/xsd\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd","typeName":"ErrorPojo"}],"shadow":[true]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["errorCode"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"string","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":1}]}]},"name":"errorCode","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["errorDesc"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{"typeNamespace":"http:\/\/www.w3.org\/2001\/XMLSchema","_minOccurs":0,"typeName":"string","_nillable":false,"nodeType":1,"_maxOccurs":1,"order":2}]}]},"name":"errorDesc","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}]},"name":"ErrorPojo"}],"id":"_12.9ad7c20d-998e-4629-84b9-f365c85a6733"}</jsonData>
        <description>ErrorPojo</description>
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1764</guid>
        <versionId>229827b8-d6db-4c57-a151-c007e05fff6c</versionId>
        <definition>
            <property>
                <name>errorCode</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>string</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>1</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>errorDesc</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType>1</nodeType>
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName>string</typeName>
                    <typeNamespace>http://www.w3.org/2001/XMLSchema</typeNamespace>
                    <minOccurs>0</minOccurs>
                    <maxOccurs>1</maxOccurs>
                    <nillable>false</nillable>
                    <order>2</order>
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="ErrorPojo">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name>ErrorPojo</name>
                <namespace>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</namespace>
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

