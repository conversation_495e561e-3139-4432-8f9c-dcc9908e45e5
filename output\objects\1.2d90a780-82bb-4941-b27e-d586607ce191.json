{"id": "1.2d90a780-82bb-4941-b27e-d586607ce191", "versionId": "0df5b9dd-9586-48b7-9813-b17c9e15df1d", "name": "Audit Request History", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.2d90a780-82bb-4941-b27e-d586607ce191", "name": "Audit Request History", "lastModified": "1698774158295", "lastModifiedBy": "so<PERSON>ia", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.4fc9255f-9a95-4e95-9df4-e22c4be89701", "2025.4fc9255f-9a95-4e95-9df4-e22c4be89701"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "d10d1d60-86c0-4d74-ae86-b573eb8368a8", "versionId": "0df5b9dd-9586-48b7-9813-b17c9e15df1d", "dependencySummary": "<dependencySummary id=\"bpdid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:ad\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.441e62d7-7978-4893-bfe8-02c6a519c686\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":65,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"54b5227d-488e-4aec-9ee5-1ec7ddfb4c23\"},{\"incoming\":[\"7200279f-f341-4473-80ba-9ad6a11a9b21\",\"500ea008-6821-4015-a2e0-6576fea62e3d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":608,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8a\"],\"preAssignmentScript\":[\"\\/\\/log.info(\\\"ODC -- ProcessInstance : \\\"+tw.local.requestNo +\\\" - ServiceName : AUDIT COLLECTION DATA : END\\\");\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"6b687c50-a559-426c-8f3a-9a1a8873bf72\"},{\"targetRef\":\"4fc9255f-9a95-4e95-9df4-e22c4be89701\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Test Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.441e62d7-7978-4893-bfe8-02c6a519c686\",\"sourceRef\":\"54b5227d-488e-4aec-9ee5-1ec7ddfb4c23\"},{\"startQuantity\":1,\"outgoing\":[\"5231134d-c217-44ee-a0b3-100b1707803e\"],\"incoming\":[\"2027.441e62d7-7978-4893-bfe8-02c6a519c686\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":319,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\/\\/log.info(\\\"ODC -- ProcessInstance : \\\"+tw.local.instanceNo +\\\" - ServiceName : AUDIT COLLECTION DATA : START\\\");\\r\\n\\r\\n\"]},\"name\":\"Set SQL Statement\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"4fc9255f-9a95-4e95-9df4-e22c4be89701\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/**-------------------------- Add SQL Parameters --------------------------**\\/\\r\\nfunction addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\ntw.local.sql = \\\"INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_LOGHISTORY\\\"+\\r\\n               \\\"(STARTTIME, ENDTIME, USERNAME, ROLE, STEPNAME, ACTION, RETURNREASON, COMMENT, REQUESTNO, terminateReason)\\\"+\\r\\n \\t\\t   \\\" VALUES (?,?,?,?,?,?,?,?,?,?)\\\";\\r\\n\\r\\n\\r\\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\\r\\n\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.startTime));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.endTime));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.userName));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.role));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.step));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.action));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.returnReason));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.comment));\\r\\n \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.requestNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.terminateReason));  \"]}},{\"targetRef\":\"64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Execute SQL Statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"5231134d-c217-44ee-a0b3-100b1707803e\",\"sourceRef\":\"4fc9255f-9a95-4e95-9df4-e22c4be89701\"},{\"startQuantity\":1,\"outgoing\":[\"7200279f-f341-4473-80ba-9ad6a11a9b21\"],\"incoming\":[\"5231134d-c217-44ee-a0b3-100b1707803e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":469,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Execute SQL Statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"1\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"6b687c50-a559-426c-8f3a-9a1a8873bf72\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"7200279f-f341-4473-80ba-9ad6a11a9b21\",\"sourceRef\":\"64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8\"},{\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"sqlParameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.59f5349e-ed3a-48ac-8d37-59d60eab80c6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.e3fe14a6-6267-450b-bb5e-7d2aea4402b6\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"results\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.3aa92ff8-4196-4dc7-9f3d-a88f7df29104\"},{\"startQuantity\":1,\"outgoing\":[\"500ea008-6821-4015-a2e0-6576fea62e3d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":387,\"y\":156,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exp Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Audit Request History\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"e82b3ec2-f262-4b64-b612-4e7f7425d4fa\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"6b687c50-a559-426c-8f3a-9a1a8873bf72\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event1\",\"declaredType\":\"sequenceFlow\",\"id\":\"500ea008-6821-4015-a2e0-6576fea62e3d\",\"sourceRef\":\"e82b3ec2-f262-4b64-b612-4e7f7425d4fa\"}],\"laneSet\":[{\"id\":\"c3ef90f8-a399-468b-850b-b0df7f0baa5b\",\"lane\":[{\"flowNodeRef\":[\"54b5227d-488e-4aec-9ee5-1ec7ddfb4c23\",\"6b687c50-a559-426c-8f3a-9a1a8873bf72\",\"4fc9255f-9a95-4e95-9df4-e22c4be89701\",\"64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8\",\"e82b3ec2-f262-4b64-b612-4e7f7425d4fa\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"326c32f7-1dda-48ed-9dc5-6d8e9020e156\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Audit Request History\",\"declaredType\":\"process\",\"id\":\"1.2d90a780-82bb-4941-b27e-d586607ce191\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.668205b9-1d85-4129-8851-98e6d8776f9b\",\"2055.923d5229-24ce-44cd-8282-2a0fdcf33910\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.StepLog();\\nautoObject.startTime =  new TWDate();\\nautoObject.endTime = new TWDate() ;\\nautoObject.userName = \\\"odchubcumkr10\\\";\\nautoObject.role = \\\"Hub Maker\\\";\\nautoObject.step = \\\"Create ODC Reversal Request \\u2013 \\u0627\\u0646\\u0634\\u0627\\u0621 \\u0637\\u0644\\u0628 \\u0627\\u0639\\u0627\\u062f\\u0629 \\u0642\\u064a\\u062f \\u062a\\u062d\\u0635\\u064a\\u0644 \\u0645\\u0633\\u062a\\u0646\\u062f\\u0649 \\u062a\\u0635\\u062f\\u064a\\u0631\\\";\\nautoObject.action = \\\"Submit Request\\\";\\nautoObject.comment = \\\"No zft\\\";\\nautoObject.terminateReason = \\\"\\\";\\nautoObject.returnReason = \\\"L2a\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49\",\"name\":\"stepLog\",\"isCollection\":false,\"id\":\"2055.668205b9-1d85-4129-8851-98e6d8776f9b\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"12345678911111_02\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestNo\",\"isCollection\":false,\"id\":\"2055.923d5229-24ce-44cd-8282-2a0fdcf33910\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "stepLog", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.668205b9-1d85-4129-8851-98e6d8776f9b", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "parameterType": "1", "isArrayOf": "false", "classId": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "seq": "1", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.StepLog();\r\nautoObject.startTime =  new TWDate();\r\nautoObject.endTime = new TWDate() ;\r\nautoObject.userName = \"odchubcumkr10\";\r\nautoObject.role = \"Hub Maker\";\r\nautoObject.step = \"Create ODC Reversal Request – انشاء طلب اعادة قيد تحصيل مستندى تصدير\";\r\nautoObject.action = \"Submit Request\";\r\nautoObject.comment = \"No zft\";\r\nautoObject.terminateReason = \"\";\r\nautoObject.returnReason = \"L2a\";\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "54857262-a6d9-4963-8de2-8370383497b8", "versionId": "05e13bd5-4418-4cdf-b71f-224ad6013f4b"}, {"name": "requestNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.923d5229-24ce-44cd-8282-2a0fdcf33910", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"12345678911111_02\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "61b725ea-6dd3-4b2b-9758-2943223e5409", "versionId": "f074e3c9-b595-4485-a95a-29a29d0b795f"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "a7f712e2-6864-4588-be8c-af717d8db59f", "versionId": "10d62af0-490d-4fde-9d87-3ef90c48fc41"}], "processVariable": [{"name": "sqlParameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.59f5349e-ed3a-48ac-8d37-59d60eab80c6", "description": {"isNull": "true"}, "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3e40e6c5-b7df-4a7b-9a9e-9b290566cc06", "versionId": "0d90a410-999a-4197-8ed1-fb2b52b7b584"}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.e3fe14a6-6267-450b-bb5e-7d2aea4402b6", "description": {"isNull": "true"}, "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fb592912-a35a-4cdc-906e-328bc6abc825", "versionId": "a11ff38f-a967-459b-a920-e24e2c248f66"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3aa92ff8-4196-4dc7-9f3d-a88f7df29104", "description": {"isNull": "true"}, "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3300badb-5239-4e55-9283-209078572aed", "versionId": "2c737604-488f-4d50-a9e5-cf15acf49d43"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4fc9255f-9a95-4e95-9df4-e22c4be89701", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "name": "Set SQL Statement", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.c7a1570e-2f65-407f-9ff1-0158b148e46f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d88", "versionId": "1c9e97a2-af90-4c58-a3f8-8ce38fe6e6b9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.9d1e12ea-673c-4aed-9c12-2ffd30069505", "processItemId": "2025.4fc9255f-9a95-4e95-9df4-e22c4be89701", "location": "2", "script": {"isNull": "true"}, "guid": "a70c3c20-8570-408e-a27e-7546a0908213", "versionId": "8a163c73-f6ba-4171-84c1-96011ff5ba8c"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.e4d4694b-f48c-41a6-84ad-a977125cc3b4", "processItemId": "2025.4fc9255f-9a95-4e95-9df4-e22c4be89701", "location": "1", "script": "//log.info(\"ODC -- ProcessInstance : \"+tw.local.instanceNo +\" - ServiceName : AUDIT COLLECTION DATA : START\");\r\r\n\r\r\n", "guid": "d621998d-e257-4dc8-bdad-5e8af5366e12", "versionId": "8d9e9f12-7234-4606-8b8c-8fdf5d60c49a"}], "layoutData": {"x": "319", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.c7a1570e-2f65-407f-9ff1-0158b148e46f", "scriptTypeId": "2", "isActive": "true", "script": "/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\ntw.local.sql = \"INSERT INTO \"+ tw.env.DBSchema + \".ODC_LOGHISTORY\"+\r\r\n               \"(STARTTIME, ENDTIME, USERNAME, ROLE, STEPNAME, ACTION, RETURNREASON, COMMENT, REQUESTNO, terminateReason)\"+\r\r\n \t\t   \" VALUES (?,?,?,?,?,?,?,?,?,?)\";\r\r\n\r\r\n\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\n\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.startTime));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.endTime));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.userName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.role));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.step));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.action));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.returnReason));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.comment));\r\r\n \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.requestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.terminateReason));  ", "isRule": "false", "guid": "1f4a8fe7-9607-4267-bd0f-e0b829c50c28", "versionId": "44cd830f-ca9e-4b8d-8fe4-1c378217f5de"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "name": "Execute SQL Statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8e", "versionId": "445ef061-184f-4a95-8578-6cdc791f89d6", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "469", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "57218fd8-e45d-42cd-9911-aa99e0247474", "versionId": "99b78c18-334b-4296-8ff8-5331bdb5d837", "parameterMapping": [{"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.10e04d33-941e-4e7e-8fb9-c36fc54f9f5c", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "36869c17-2a23-407c-a414-6767ffd0f9fd", "versionId": "1863444b-1280-46d5-a7a1-931434980f72", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4693d20b-2838-45bb-a954-6ab2dc112315", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "8ad883f0-9416-48fb-989b-acaa5027d5f1", "versionId": "bec5f428-dab3-4a17-b3c0-b565a12d3cdd", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.afb67f0c-42bb-4943-9f16-47504bc77ba3", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "26d1dd4b-b4b4-4a10-be7a-11f6b93ceea3", "versionId": "c876789c-5f63-43be-8942-60b3ad087df8", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6a50cec6-4935-4f45-bcb5-352d97c3c664", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc", "useDefault": "false", "value": "1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "31410a72-2d53-4318-bf11-c115034d2c1b", "versionId": "df86fad0-00ac-45f0-89e7-bd34e1f504c9", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f9bb8136-31a9-4fef-b017-be63e9a187fd", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.e3fbd081-0f79-4ca8-9ad1-a98feb6646bc", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "de3ad393-d310-4ea1-9928-dedbe0a9eb93", "versionId": "ee8417d9-bf53-4241-90a2-37ebf3934b78", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e82b3ec2-f262-4b64-b612-4e7f7425d4fa", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "name": "Exp Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8c", "versionId": "52300ee0-e587-4196-8b75-5a4e80d0e7f2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "387", "y": "156", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "5d3294f3-17cc-47e5-9249-047b85fe0716", "versionId": "cc16b924-03a6-4557-a167-1d1b92b3b09d", "parameterMapping": [{"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.430e80a1-6eeb-4a51-b28d-45d68f3a3fd4", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348", "useDefault": "false", "value": "\"Audit Request History\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "61e55516-6cc8-49f4-b541-4194f6d55b2c", "versionId": "3959613a-ff0e-44c9-92df-b9ae88f38797", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.76774df4-e358-4d89-8efa-769c33f2b72c", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.efb9301f-89a9-45ba-a20b-2f40cb9f7348", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "444892c4-0c7e-4ca1-b378-b82417655a45", "versionId": "3bd21585-877b-49c0-ab56-44bd2891123f", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6b687c50-a559-426c-8f3a-9a1a8873bf72", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.478269d6-0417-4925-850e-2dbc69a075c0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8a", "versionId": "6d3022e2-51f4-4027-93c5-dae6baf52e24", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.3c5c45d7-d194-477b-ad08-f783f672c4fc", "processItemId": "2025.6b687c50-a559-426c-8f3a-9a1a8873bf72", "location": "1", "script": "//log.info(\"ODC -- ProcessInstance : \"+tw.local.requestNo +\" - ServiceName : AUDIT COLLECTION DATA : END\");", "guid": "b806e75d-1367-46d6-b74c-157cba08349a", "versionId": "17bf992d-8bb7-41e6-ba93-02c96e1d8ec4"}, "layoutData": {"x": "608", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.478269d6-0417-4925-850e-2dbc69a075c0", "haltProcess": "false", "guid": "36decfe2-46c9-4c91-a7ba-3576d5d7ad5b", "versionId": "2e1e4c1d-9e8b-4cc8-b717-63ce7c8c2e5d"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "65", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Audit Request History", "id": "1.2d90a780-82bb-4941-b27e-d586607ce191", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "stepLog", "itemSubjectRef": "itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "isCollection": "false", "id": "2055.668205b9-1d85-4129-8851-98e6d8776f9b", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.StepLog();\r\nautoObject.startTime =  new TWDate();\r\nautoObject.endTime = new TWDate() ;\r\nautoObject.userName = \"odchubcumkr10\";\r\nautoObject.role = \"Hub Maker\";\r\nautoObject.step = \"Create ODC Reversal Request – انشاء طلب اعادة قيد تحصيل مستندى تصدير\";\r\nautoObject.action = \"Submit Request\";\r\nautoObject.comment = \"No zft\";\r\nautoObject.terminateReason = \"\";\r\nautoObject.returnReason = \"L2a\";\r\nautoObject", "useDefault": "true"}}}, {"name": "requestNo", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.923d5229-24ce-44cd-8282-2a0fdcf33910", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"12345678911111_02\"", "useDefault": "true"}}}], "ns16:dataOutput": {"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731"}, "ns16:inputSet": {"ns16:dataInputRefs": ["2055.668205b9-1d85-4129-8851-98e6d8776f9b", "2055.923d5229-24ce-44cd-8282-2a0fdcf33910"]}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.6252ef51-a5a1-41d4-a3d2-d8ff1b310731"}}, "ns16:laneSet": {"id": "c3ef90f8-a399-468b-850b-b0df7f0baa5b", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "326c32f7-1dda-48ed-9dc5-6d8e9020e156", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["54b5227d-488e-4aec-9ee5-1ec7ddfb4c23", "6b687c50-a559-426c-8f3a-9a1a8873bf72", "4fc9255f-9a95-4e95-9df4-e22c4be89701", "64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8", "e82b3ec2-f262-4b64-b612-4e7f7425d4fa"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "54b5227d-488e-4aec-9ee5-1ec7ddfb4c23", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "65", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.441e62d7-7978-4893-bfe8-02c6a519c686"}, "ns16:endEvent": {"name": "End", "id": "6b687c50-a559-426c-8f3a-9a1a8873bf72", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "608", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d8a", "ns3:preAssignmentScript": "//log.info(\"ODC -- ProcessInstance : \"+tw.local.requestNo +\" - ServiceName : AUDIT COLLECTION DATA : END\");"}, "ns16:incoming": ["7200279f-f341-4473-80ba-9ad6a11a9b21", "500ea008-6821-4015-a2e0-6576fea62e3d"]}, "ns16:sequenceFlow": [{"sourceRef": "54b5227d-488e-4aec-9ee5-1ec7ddfb4c23", "targetRef": "4fc9255f-9a95-4e95-9df4-e22c4be89701", "name": "To Test Data", "id": "2027.441e62d7-7978-4893-bfe8-02c6a519c686", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4fc9255f-9a95-4e95-9df4-e22c4be89701", "targetRef": "64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8", "name": "To Execute SQL Statement", "id": "5231134d-c217-44ee-a0b3-100b1707803e", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8", "targetRef": "6b687c50-a559-426c-8f3a-9a1a8873bf72", "name": "To End", "id": "7200279f-f341-4473-80ba-9ad6a11a9b21", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "e82b3ec2-f262-4b64-b612-4e7f7425d4fa", "targetRef": "6b687c50-a559-426c-8f3a-9a1a8873bf72", "name": "To End Event1", "id": "500ea008-6821-4015-a2e0-6576fea62e3d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Set SQL Statement", "id": "4fc9255f-9a95-4e95-9df4-e22c4be89701", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "319", "y": "57", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": "//log.info(\"ODC -- ProcessInstance : \"+tw.local.instanceNo +\" - ServiceName : AUDIT COLLECTION DATA : START\");\r\r\n\r\r\n"}, "ns16:incoming": "2027.441e62d7-7978-4893-bfe8-02c6a519c686", "ns16:outgoing": "5231134d-c217-44ee-a0b3-100b1707803e", "ns16:script": "/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\ntw.local.sql = \"INSERT INTO \"+ tw.env.DBSchema + \".ODC_LOGHISTORY\"+\r\r\n               \"(STARTTIME, ENDTIME, USERNAME, ROLE, STEPNAME, ACTION, RETURNREASON, COMMENT, REQUESTNO, terminateReason)\"+\r\r\n \t\t   \" VALUES (?,?,?,?,?,?,?,?,?,?)\";\r\r\n\r\r\n\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\n\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.startTime));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.endTime));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.userName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.role));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.step));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.action));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.returnReason));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.comment));\r\r\n \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.requestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.stepLog.terminateReason));  "}, "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "Execute SQL Statement", "id": "64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "469", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "5231134d-c217-44ee-a0b3-100b1707803e", "ns16:outgoing": "7200279f-f341-4473-80ba-9ad6a11a9b21", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Exp Handling", "id": "e82b3ec2-f262-4b64-b612-4e7f7425d4fa", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "387", "y": "156", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:outgoing": "500ea008-6821-4015-a2e0-6576fea62e3d", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Audit Request History\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "sqlParameters", "id": "2056.59f5349e-ed3a-48ac-8d37-59d60eab80c6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.e3fe14a6-6267-450b-bb5e-7d2aea4402b6"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.3aa92ff8-4196-4dc7-9f3d-a88f7df29104"}]}}}, "link": [{"name": "To End Event1", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.500ea008-6821-4015-a2e0-6576fea62e3d", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e82b3ec2-f262-4b64-b612-4e7f7425d4fa", "2025.e82b3ec2-f262-4b64-b612-4e7f7425d4fa"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.6b687c50-a559-426c-8f3a-9a1a8873bf72", "2025.6b687c50-a559-426c-8f3a-9a1a8873bf72"], "guid": "*************-473b-9311-f5dcd85ee190", "versionId": "04cce99f-0667-4bc2-ba9f-66c074e87e60", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7200279f-f341-4473-80ba-9ad6a11a9b21", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8", "2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.6b687c50-a559-426c-8f3a-9a1a8873bf72", "2025.6b687c50-a559-426c-8f3a-9a1a8873bf72"], "guid": "e5651fa1-2e1a-4ec1-8dfa-cc3e3ba404d1", "versionId": "12f3e6a8-08a5-41b3-88a1-7c31c5a2e6b1", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Execute SQL Statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.5231134d-c217-44ee-a0b3-100b1707803e", "processId": "1.2d90a780-82bb-4941-b27e-d586607ce191", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4fc9255f-9a95-4e95-9df4-e22c4be89701", "2025.4fc9255f-9a95-4e95-9df4-e22c4be89701"], "endStateId": "Out", "toProcessItemId": ["2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8", "2025.64f185c6-2bc7-4a7b-9af7-d8aa5f1b91f8"], "guid": "e8d9f216-aca8-40de-9d84-04a0f2c9ff6c", "versionId": "4ecdfb71-ad2e-460b-8404-9f9a8fea9ed6", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}