<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.9862410e-bba9-43a0-b511-69ae076e3b64" name="test">
        <lastModified>1734363713717</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.606d0e11-4672-4305-b0c1-80cd12305aef</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.da7e4d23-78cb-4483-98ed-b9c238308a03</participantRef>
        <exposedType>4</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:cdc758d910e20d2d:-607ed7:18afb1305a9:-13fc</guid>
        <versionId>06f9ad28-019c-42f1-82d6-ef124c4d347e</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"participantRef":["24.da7e4d23-78cb-4483-98ed-b9c238308a03"],"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.9701bbae-9fdc-4288-b13a-3ee505b9c3e3"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"936adb44-de65-4585-a3fe-77fd59dd5041"},{"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Importer_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6d08f30a-c8e0-4f7b-8e05-98177398cc2a","optionName":"@label","value":"Importer Details CV"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dc3ec603-e37b-4299-8e62-82c2a7566834","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6a78e526-66db-4557-8270-5d7d81acf8aa","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.0ff96fd8-0740-4d17-887e-a56c8ef7921b","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"b9d80baa-e9b4-4e13-8042-5d4018771603","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":748,"y":207,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"name":"Coach","isForCompensation":false,"completionQuantity":1,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"id":"2025.add38abe-a2cc-43e4-99c4-c85b09782fe3","cachePage":false},{"incoming":["2027.e23113d1-614d-4736-8775-05a8253c6226"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1181,"y":196,"declaredType":"TNodeVisualInfo","height":44}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"b3165fad-15fa-44dd-905e-68f51eac6d93"},{"targetRef":"2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.9701bbae-9fdc-4288-b13a-3ee505b9c3e3","sourceRef":"936adb44-de65-4585-a3fe-77fd59dd5041"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.initiator = \"\";\nautoObject.requestNature = {};\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = {};\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new Date();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = {};\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = {};\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = {};\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = {};\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = {};\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = {};\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = {};\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = {};\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = [];\nautoObject.BasicDetails.Bills[0] = {};\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\nautoObject.BasicDetails.Invoice = [];\nautoObject.BasicDetails.Invoice[0] = {};\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\nautoObject.GeneratedDocumentInfo = {};\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = [];\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = {};\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = {};\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = {};\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = [];\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = {};\nautoObject.FcCollections.currency = {};\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new Date();\nautoObject.FcCollections.ToDate = new Date();\nautoObject.FcCollections.accountNo = {};\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = [];\nautoObject.FcCollections.retrievedTransactions[0] = {};\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = [];\nautoObject.FcCollections.selectedTransactions[0] = {};\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = [];\nautoObject.FcCollections.listOfAccounts[0] = {};\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = {};\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new Date();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = {};\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = [];\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = {};\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = {};\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = {};\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new Date();\nautoObject.ProductShipmentDetails.shipmentMethod = {};\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = {};\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = {};\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = {};\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = {};\nautoObject.ContractCreation.productCode = {};\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new Date();\nautoObject.ContractCreation.valueDate = new Date();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new Date();\nautoObject.Parties = {};\nautoObject.Parties.Drawer = {};\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = {};\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = {};\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = {};\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = {};\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = [];\nautoObject.ChargesAndCommissions[0] = {};\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = {};\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new Date();\nautoObject.ContractLiquidation.creditValueDate = new Date();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = {};\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = {};\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = [];\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = {};\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = {};\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = [];\nautoObject.attachmentDetails.attachment[0] = {};\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = [];\nautoObject.complianceComments[0] = {};\nautoObject.complianceComments[0].startTime = new Date();\nautoObject.complianceComments[0].endTime = new Date();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = [];\nautoObject.History[0] = {};\nautoObject.History[0].startTime = new Date();\nautoObject.History[0].endTime = new Date();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = {};\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"declaredType":"dataObject","id":"2056.377f6406-1776-4c53-87d0-696fb720e06f"},{"startQuantity":1,"outgoing":["2027.06b6a779-0cff-4736-8b6a-9502a2a5804a"],"default":"2027.06b6a779-0cff-4736-8b6a-9502a2a5804a","extensionElements":{"nodeVisualInfo":[{"width":95,"x":182,"y":47,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Set attach.","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.47f9b301-580d-481b-8884-09eed62550cd","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.appInfo.instanceID=\"9999\";\r\n\r\ntw.local.odcRequest.CustomerInfo.customerName=\"smsma\";\r\ntw.local.odcRequest.CustomerInfo.cif= \"00000000\";\r\n\r\ntw.local.odcRequest.requestNo=\"11112222333311\";\r\n\r\n\/\/var index= 0;\r\n\/\/\r\n\/\/if(!tw.local.odcRequest.attachmentDetails)\r\n\/\/  tw.local.odcRequest.attachmentDetails = {};\r\n\/\/\r\n\/\/if (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {   \r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment = []; \t\r\n\/\/\t\t\r\n\/\/\tsetProps(\"AIRWAY BILL\", \"AIRWAY BILL\", \"\" ); \r\n\/\/\tsetProps(\"TRUCK CONSIGNMENT NOTE\",\"TRUCK CONSIGNMENT NOTE\", \"\"); \r\n\/\/\tsetProps(\"N\/N BILL OF LADING\",\"N\/N BILL OF LADING\",\"\"); \r\n\/\/\tsetProps(\"COURIER \/ POST RECEIPT\", \"COURIER \/ POST RECEIPT\" , \"\" ); \r\n\/\/\tsetProps(\"PACKING LIST\",\"PACKING LIST\",\"\"); \r\n\/\/\tsetProps(\"CERTIFICATE OF ORIGIN\",\"CERTIFICATE OF ORIGIN\", \"\" );\r\n\/\/\tsetProps(\"CERTIFICATE OF ANALYSIS\", \"CERTIFICATE OF ANALYSIS\", \"\");\r\n\/\/\tsetProps(\"INSURANCE POLICY \/ CERTIFICATE\", \"INSURANCE POLICY \/ CERTIFICATE\",\"\");\r\n\/\/\tsetProps(\"BENEFECIARY DECLARATION\", \"BENEFECIARY DECLARATION\", \"\");\r\n\/\/\tsetProps(\"NON RADIOACTIVE CERTIFICATE\", \"NON RADIOACTIVE CERTIFICATE\",\"\");\r\n\/\/\tsetProps(\"PHYTOSANITARY CERTIFICATE\", \"PHYTOSANITARY CERTIFICATE\",\"\");\r\n\/\/\tsetProps(\"CERTIFICATE OF ANALYSIS\",\"Bill of exchange\/draft\", \"\u0627\u0644\u0643\u0645\u0628\u064a\u0627\u0644\u0629\" );\r\n\/\/\tsetProps(\"HEALTH CERTIFICATE\",\"HEALTH CERTIFICATE\", \"\");\r\n\/\/\tsetProps(\"INSPECTION CERTIFICATE\", \"INSPECTION CERTIFICATE\",  \"\");\r\n\/\/\tsetProps(\"WARRANTY CERTIFICATE\", \"WARRANTY CERTIFICATE\",\"\");\r\n\/\/\tsetProps( \"TEST CERTIFICATE\",\"TEST CERTIFICATE\", \"\");\r\n\/\/\r\n\/\/\ttw.local.odcRequest.attachmentDetails.ecmProperties = {};\r\n\/\/\ttw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\n\/\/\ttw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};\r\n\/\/}\t\r\n\/\/\r\n\/\/function setProps(name, desc, arabicName){\t\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index]= {};\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].name= name;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].description= desc;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].arabicName= arabicName;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].numOfOriginals= 0;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].numOfCopies= 0;\r\n\/\/\tindex++;\t\r\n\/\/}\r\n\/\/\r\n"]}},{"outgoing":["2027.f103f1b1-0053-42a7-8282-eee71ac34d30"],"incoming":["2027.06b6a779-0cff-4736-8b6a-9502a2a5804a"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":383,"y":27,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.f103f1b1-0053-42a7-8282-eee71ac34d30","name":"Set ECM default properties","dataInputAssociation":[{"targetRef":"2055.399c7a58-00b5-4451-9813-41c0b9652088","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.CustomerInfo.cif"]}}]},{"targetRef":"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}]},{"targetRef":"2055.afec08e9-d3db-4a02-a361-299823096b13","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"somaia.galal\""]}}]},{"targetRef":"2055.25394215-074f-4b79-8e84-9a96d32cc83b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}]},{"targetRef":"2055.9bafb7a3-8d61-40e3-8c95-1a46b34fcc75","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.CustomerInfo.customerName"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.e9efb824-60ba-4d72-8aeb-87c755db0abf","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.eac829db-66fe-43f5-810c-6faa514533a2","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties"]}}],"sourceRef":["2055.72f8c4d9-e116-4c90-9dcd-29746fff0925"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails"]}}],"sourceRef":["2055.7d269650-ee48-4101-80db-2807cf921562"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.0261e8ad-a540-4682-88c5-87dff3eab23c"]}],"calledElement":"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8"},{"outgoing":["2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0"],"incoming":["2027.f103f1b1-0053-42a7-8282-eee71ac34d30"],"extensionElements":{"mode":["InvokeService"],"postAssignmentScript":["tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = tw.local.parentPath;\r\n\r\ntw.local.odcRequest.attachmentDetails.folderID = tw.local.odcRequest.folderID;\r\n\r\nconsole.log(\"Folder id=============== \"+ tw.local.odcRequest.folderID);\r\n"],"nodeVisualInfo":[{"width":95,"x":523,"y":27,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0","name":"Create ECM Folder","dataInputAssociation":[{"targetRef":"2055.4156964b-1c67-40bc-8f62-3804c71cf908","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.e2ce0eed-342c-4942-8214-83e964b550e5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"599\""]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.c9703dec-4650-49b5-8b70-534891186ff8","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderID"]}}],"sourceRef":["2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}],"sourceRef":["2055.5f955245-0538-4e40-80a6-12f45c3102f3"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.214c7268-80d0-444d-8702-dd0d5462dbe7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.fullPath"]}}],"sourceRef":["2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.676e3a06-e2cc-4855-84d6-6f82a350500a"]}],"calledElement":"1.46b984a3-b4ad-405a-abd3-8631f907efe4"},{"targetRef":"2025.e9efb824-60ba-4d72-8aeb-87c755db0abf","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set ECM default properties","declaredType":"sequenceFlow","id":"2027.06b6a779-0cff-4736-8b6a-9502a2a5804a","sourceRef":"2025.47f9b301-580d-481b-8884-09eed62550cd"},{"targetRef":"2025.c9703dec-4650-49b5-8b70-534891186ff8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create ECM Folder","declaredType":"sequenceFlow","id":"2027.f103f1b1-0053-42a7-8282-eee71ac34d30","sourceRef":"2025.e9efb824-60ba-4d72-8aeb-87c755db0abf"},{"outgoing":["2027.fdf3e62e-8999-4440-8302-c6ce5ef38788"],"incoming":["2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":718,"y":28,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Attachment_Comments_View1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"322b0be8-e3d8-4a86-81d3-b9e0ca534810","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"350ae7bd-e125-463c-8008-63686e07d1de","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"efc779c3-8031-4e03-889e-03aec49ef1f8","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"629e683d-c38a-44cb-8f66-d5bc5e8735c4","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a3962d7-0743-4e27-8267-f19e284d695a","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"604c3994-63b0-4657-820d-040a09cb2665","optionName":"canCreate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b8a51546-6238-4ba6-892f-83870b990ee1","optionName":"canDelete","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a6eea168-465e-45d6-88f9-701000f4ff5c","optionName":"visible","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6c120f08-c562-4153-8589-c1595f6d7b3f","optionName":"remittanceLetterPath","value":"tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.path"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"66053fcb-bed1-4888-84d9-4318929fb6f1","version":"8550"},{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Button1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"358fa56e-7808-456b-8970-8649ae67277e","optionName":"@label","value":"Submit"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3b82eff9-fb1d-40dd-8dfa-1ffa6a0deb59","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"db96b518-5f41-43dd-8180-d7aabcc67f64","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f20dbc49-8001-426b-8df2-98ff700a53fb","optionName":"colorStyle","value":"S"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"09fb16e8-89cc-4de0-860a-8decebb69028","optionName":"shapeStyle","value":"R"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"87173035-a93e-478b-8d40-7aa6aaf810fd","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"L\"}]}"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"3a1f39dc-2a01-4043-8aa7-96d5f842cb51","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"875f574e-5a7c-46c3-837a-9f1c28013c42"}],"layoutItemId":"Horizontal_Layout1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1f27250c-01f8-4eb9-8e39-1eb896d9c139","optionName":"@label","value":"Horizontal layout"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"14077e59-79a1-49f6-8245-febf1512a8bd","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"08987e5f-b141-4f59-8635-1f1f66ce99c3","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"69bb63cc-d2d9-4ea0-8f1e-6508811ffad9","optionName":"hAlignment","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"R\"}]}"}],"viewUUID":"64.44f463cc-615b-43d0-834f-c398a82e0363","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"7785b915-6f80-4d0f-8b63-ff86421e41ed","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"SG TST","isForCompensation":false,"completionQuantity":1,"id":"2025.3ba93f47-71ec-46a1-88dd-86c5a18d5ff1"},{"targetRef":"2025.3ba93f47-71ec-46a1-88dd-86c5a18d5ff1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SG TST","declaredType":"sequenceFlow","id":"2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0","sourceRef":"2025.c9703dec-4650-49b5-8b70-534891186ff8"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.a1f8c3c8-3422-4ce9-8318-b02d3c77bd16"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.9b7eaa48-b70f-4c20-8bdb-f2afe6d6f254"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderID","isCollection":false,"declaredType":"dataObject","id":"2056.2cf6e963-1068-45b4-8ea5-9984bd0012a7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fullPath","isCollection":false,"declaredType":"dataObject","id":"2056.a2c0cdc2-4d4a-4ca5-8ef3-264200c73106"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"declaredType":"dataObject","id":"2056.4f0d0b02-e780-4150-803a-f091837b8709"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"declaredType":"dataObject","id":"2056.d1b09e62-fac7-4a2c-857b-c51cea8e976a"},{"outgoing":["2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63"],"incoming":["2027.fdf3e62e-8999-4440-8302-c6ce5ef38788"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":882,"y":28,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63","name":"Service","dataInputAssociation":[{"targetRef":"2055.1bf8ade2-bd6b-4af2-87c8-1b269002413f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"create\""]}}]},{"targetRef":"2055.f43a0726-0192-4d4c-942e-83e973ee5015","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderID"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.9a0c88a8-80fa-47d5-8fb9-a82a62f49bdb","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.validationMessage"]}}],"sourceRef":["2055.c965f53e-7627-46ac-8f53-b8a0e4d9e4fb"]}],"calledElement":"1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7"},{"targetRef":"2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63","sourceRef":"2025.9a0c88a8-80fa-47d5-8fb9-a82a62f49bdb"},{"targetRef":"2025.9a0c88a8-80fa-47d5-8fb9-a82a62f49bdb","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"bc67ebe3-046f-47cd-869b-0cbd7300f123","coachEventPath":"Button1"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Service","declaredType":"sequenceFlow","id":"2027.fdf3e62e-8999-4440-8302-c6ce5ef38788","sourceRef":"2025.3ba93f47-71ec-46a1-88dd-86c5a18d5ff1"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validationMessage","isCollection":false,"declaredType":"dataObject","id":"2056.5e8acb05-**************-66ba88634d17"},{"outgoing":["2027.e23113d1-614d-4736-8775-05a8253c6226","2027.12141206-f86b-425c-8650-c2780fb35043"],"incoming":["2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63"],"default":"2027.12141206-f86b-425c-8650-c2780fb35043","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1005,"y":47,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec"},{"targetRef":"b3165fad-15fa-44dd-905e-68f51eac6d93","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.validationMessage== \"\"  || tw.local.validationMessage  == null )"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.e23113d1-614d-4736-8775-05a8253c6226","sourceRef":"2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec"},{"incoming":["2027.12141206-f86b-425c-8650-c2780fb35043"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":988,"y":135,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.c7dce2c1-798c-4348-86bd-74b1f4398cd6"},{"targetRef":"2025.c7dce2c1-798c-4348-86bd-74b1f4398cd6","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.validationMessage"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.12141206-f86b-425c-8650-c2780fb35043","sourceRef":"2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec"},{"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"CustomHTML1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7ac457a8-942d-43f4-8d98-c3865bf337c4","optionName":"@customHTML.contentType","value":"TEXT"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2e93ab6e-72eb-43c5-8db0-65436efcfbc3","optionName":"@customHTML.textContent","value":"&lt;style&gt;\r\n.spark-ui {\r\n    background: rgb(255, 255, 255);\r\n   \/\/ width: 99%;\r\n}\r\n.CoachView.Panel&gt;.panel.SPARKPanel {\r\nborder-collapse: separate;\r\nborder-spacing: 0;\r\nbox-shadow: 0px 0px 12px #001B5929!important;\r\nborder-top: 5px solid #00643e!important;\r\nborder-radius: 10px!important;\r\n}\r\n\r\n.panel-primary.panel-dark&gt;.panel-heading {\r\nbackground: transparent !important;\r\nborder-color: transparent;\r\ncolor: #fff;\r\n}\r\n\r\n.panel-primary.panel-dark {\r\nborder-color: transparent!important;\r\n}\r\n.panel-primary.panel-dark &gt; .panel-heading .panel-title {\r\n    color: rgb(0, 101, 71);\r\n    font: normal normal 600 24px\/45px Cairo;\r\n}\r\n\r\n.form-control {\r\n    border-top-color: rgb(205, 205, 205);\r\n    border-bottom-color: rgb(205,205,205);\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    padding: 12px 12px;\r\n    color: #181A1D !important;\r\n    background: #FFFFFF 0% 0% no-repeat padding-box;\r\n    border: 1px solid #ddd;\r\n    border-radius: 8px;\r\n}\r\n.SPARKWell .stat-cell .bg-icon {\r\n    line-height: normal;\r\n    height: 100%;\r\n    overflow: hidden;\r\n \/\/   width: 97%;\r\n    border-radius: inherit;\r\n    box-shadow: 0px 0px 12px #001B5929!important;\r\n    margin-bottom: 6px;\r\n    border-radius: 10px!important;\r\n    border-top: 5px solid !important;\r\n   \/\/ margin-right: 49px;\r\n}\r\n\r\n\/\/.form-control[disabled] {\r\n\/\/    background: rgb(242 242 242);\r\n\/\/}\r\n\r\n.bg-success {\r\n    background: #00654726 !important;\r\n}\r\n\/\/.Single_Select select.placeHolder {\r\n\/\/    color: #0002037a;\r\n\/\/}\r\n\r\n.panel-group .panel-heading+.panel-collapse .panel-body {\r\n     border-top: 1px solid #fff;\r\n}\r\n\r\n.panel {\r\n\t     margin-bottom: 18px;\r\n\t     border-radius: 2px;\r\n\t     border-width: 0px;\r\n}\r\n\r\n.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {\r\n\t     background: #fff;\r\n\t     color: #006547;\r\n\t     border-color: #fff;\r\n\t     padding: 15px;\r\n\t     border-radius: 10px!important;\r\n}\r\n\r\n.CoachView.Collapsible_Panel&gt;.panel.SPARKCPanel {\r\n\t     border-collapse: collapse;\r\n\t     box-shadow: 0px 0px 22px #001B5929!important;\r\n}\r\n.SPARKCPanel &gt; .panel-heading {\r\n    padding: 0px;\r\n    border-top-left-radius: 1px;\r\n    border-top-right-radius: 1px;\r\n    box-shadow: 0px 0px 0px #001B5929!important;\r\n    border-top: 5px solid #00643e!important;\r\n    border-radius: 10px!important;\r\n    border-spacing: 0;\r\n    border-collapse: separate;\r\n}\r\n.panel-body {\r\n    background: #fff;\r\n    margin: 0;\r\n    padding-bottom: 15px;\r\n    padding-left: 15px;\r\n    padding-right: 15px;\r\n    padding-top: 15px;\r\n    border-bottom-left-radius: 10px;\r\n    border-bottom-right-radius: 10px;\r\n}\r\n\r\n.panel-group .panel {\r\n    border-radius: 10px;\r\n}\r\n\r\n.Radio_Button_Group .radio3, .Radio_Button_Group .checkbox3{\r\n    width: 50%;\r\n}\r\n.radio3 &gt; input + span{\r\n    display: inline-flex;\r\n    flex-direction: row;\r\n    align-items: center;\r\n}\r\n\/\/.input-group.no-border&gt;.input-group-addon {\r\n\/\/    border-radius: 10px;\r\n\/\/    min-width: 54px;\r\n\/\/    height: 54px;\r\n\/\/    top: -25px;\r\n\/\/}\r\n\r\n\/\/.form-group .input{\r\n\/\/    padding-left:73px!important;\r\n\/\/}\r\n\/\/.Input_Group .outer, .control-label{\r\n\/\/padding-left:73px;\r\n\/\/}\r\n\/\/.Single_Select, .control-label{\r\n\/\/padding-left:20!important;\r\n\/\/}\r\n.Input_Group .ContentBox {\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    padding-left: 20px;\r\n}\r\n\r\n.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {\r\n    background: #fff;\r\n    color: #006547;\r\n    border-color: #fff;\r\n    border-radius: 10px!important;\r\n    font-size: 18px;\r\n    font-weight: 900;\r\n    padding: 15px;\r\n    font: normal normal 600 24px\/45px Cairo;\r\n}\r\n\r\n.btn:not(.SPARKIcon), .btn:not(.SPARKIcon).btn-outline:not([role=\"img\"]):not(.SPARKIcon):focus, .btn:not(.SPARKIcon).btn-outline.active:not([role=\"img\"]), .btn:not(.SPARKIcon).btn-outline:not([role=\"img\"]):active {\r\n    padding: 12px 25px;\r\n}\r\n.btn-success, .btn-success:not([role=\"img\"]):focus {\r\n    color: #FFFFFF;\r\n    fill: #006547;\r\n    border-color: #006643;\r\n    border-bottom-color: #006643;\r\n    background: #006643;\r\n    background-image: linear-gradient(to bottom, #006643 0, #006643 100%) !important;\r\n    background-repeat: repeat-x;\r\n}\r\n\r\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{\r\nletter-spacing: 0px;\r\ncolor: #181A1D !important;\r\nopacity: 1;\r\nfont: normal normal normal 16px\/20px Cairo;\r\nbackground: transparent;\r\nfont-weight: bold;\r\nborder: 1px solid #000203;\r\n}\r\n\/\/.Single_Select&gt;.form-group&gt;.input&gt;select {\r\n\/\/\r\n\/\/    border-top-style: ridge;\r\n\/\/    border-bottom-style: outset;\r\n\/\/    border-right-style: outset;\r\n\/\/    border-left-style: ridge;\r\n\/\/    border-top-width: revert;\r\n\/\/    border-left-width: revert;\r\n\/\/    border-bottom-width: revert;\r\n\/\/    border-right-width: revert;\r\n\/\/}\r\nselect.input-sm, .input-sm.form-control, .input-sm.form-control[type='text'] {\r\n    height: 40px;\r\n    line-height: 1.33;\r\n}\r\n\r\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {\r\n    letter-spacing: 0px;\r\n    color: #181A1D !important;\r\n    opacity: 1;\r\n    font: 14px Cairo;\r\n    background: transparent;\r\n    font-weight: lighter;\r\n}\r\n\r\n\r\n.btn-success:not([role=\"img\"]):hover {\r\n    color: #ffffff;\r\n    fill: #3D8A70;\r\n    border-color: #3D8A70;\r\n    border-bottom-color: #429c42;\r\n    background: #3D8A70;\r\n    background-image: -webkit-linear-gradient(top, #3D8A70 0, #3D8A70 100%) !important;\r\n    background-image: linear-gradient(to bottom, #3D8A70 0, #3D8A70 100%) !important;\r\n    background-repeat: repeat-x;\r\n}\r\n\r\n.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle:hover {\r\n    background: rgb(255 255 255 \/ 15%);\r\n}\r\n.panel-group .SPARKTable &gt; .panel-heading {\r\n    border-color: rgb(240, 240, 240);\r\n    border-bottom-width: 2px\r\n;\r\n    border-bottom-style: solid;\r\n    background: #006643 0% 0% no-repeat padding-box;\r\n    color: white;\r\n}\r\n.Output_Text&gt;.form-group&gt;.input&gt;p {\r\n  \r\n    padding-right: 1em;\r\n \r\n}\r\n\r\n\r\n.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {\r\n   \r\n    font-weight: 600;\r\n}\r\n\r\n\r\n\r\n\r\n[class*=\"BPM_Resp_\"] .bpm-label-default {\r\n height: 30px;\r\n    font: normal normal normal 16px\/20px Cairo !important;\r\n    letter-spacing: 0px;\r\n    opacity: 1;\r\n    color: #8B8C8E;\r\n}\r\n\r\n\r\n\r\n\r\n\/\/.ECMPropertiesContainer{\r\n\/\/display: none; \r\n\/\/}\r\n\r\n\r\n.Output_Text&gt;.form-group&gt;.input&gt;p{\r\nunicode-bidi: plaintext ;\r\ntext-align: inherit;\r\n}\r\n\r\n.CoachViewRTL {\r\n \r\n    text-align: right !important;\r\n}\r\n\r\n.CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-RightAlign&gt;*, .CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-CenterAlign&gt;* {\r\n    text-align: right;\r\n}\r\n\r\n.noIScroll.alignJustify {\r\n    text-align: inherit;\r\n}\r\n.radio3 &gt; input + span {\r\n\r\n    unicode-bidi: plaintext;\r\n\r\n}\r\n\r\n.radio3 &gt; input:checked + span::after {\r\n \r\n  top: calc(50% - 3px) !important;\r\n}\r\n.switcher-primary .switcher-state-off {\r\n    color: #000203;\r\n}\r\n.CoachViewRTL .Single_Select &gt;{\r\n unicode-bidi: plaintext;\r\n\r\n}\r\n\r\ninput[type=\"checkbox\"]:checked[disabled] {\r\n    right: 6%;\r\n    opacity: 1;\r\n    width: fit-content;\r\n    left: 3px;\r\n}\r\n\r\n.Tab_Section&gt;div&gt;.nav-tabs-mnu {\r\n    position: unset;\r\n   }\r\n   \r\na {\r\n    color: #333;\r\n    text-decoration: none;\r\n}\r\n.datepicker thead th {\r\n   \r\n    color: #006547;\r\n&lt;\/style&gt;"}],"declaredType":"com.ibm.bpmsdk.model.coach.CustomHTML","id":"e0903732-b88f-4bf2-834a-7936a1d1f1b4","version":"8550"},{"layoutItemId":"Contract_Liquidation1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4026707c-0b48-4058-8b10-5f60f770540e","optionName":"@label","value":"Contract Liquidation"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"793326ea-3fed-4f63-893f-8a7fb6c6a7e0","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c7c38c10-9f99-45dd-8f1d-a84ecd21205b","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.14ee925a-157a-48e5-9ab8-7b8879adbe5b","binding":"tw.local.odcRequest","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c7bd54e0-e9cb-421e-8963-0e6e673d3b2f","version":"8550"},{"layoutItemId":"okbutton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b2e991e1-39e2-492e-834f-6af0b79668fa","optionName":"@label","value":"OK"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"3f6c9a0b-0cf5-4cef-84f0-55aee44f3618"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":131,"y":179,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"name":"Coach 1","isForCompensation":false,"completionQuantity":1,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"id":"2025.e76cc97a-7b50-430f-8ecb-83c544620e83","cachePage":false},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"ValidationMessage","isCollection":false,"declaredType":"dataObject","id":"2056.ea1a02f7-b786-4e99-8fd5-b484d4cbcaf9"},{"outgoing":["2027.7a63343d-0fa0-4995-8925-961ce1723f22"],"incoming":["2027.7f62c1af-6893-4c1f-83cd-c9886a25e5a2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":416,"y":375,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.7a63343d-0fa0-4995-8925-961ce1723f22","name":"Service 1","dataInputAssociation":[{"targetRef":"2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.variable1"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.825a9340-93fe-4ecf-88a8-d6b2b6b9b8db","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","declaredType":"TFormalExpression","content":["tw.local.variable2"]}}],"sourceRef":["2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9"]}],"calledElement":"1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8"},{"outgoing":["2027.7f62c1af-6893-4c1f-83cd-c9886a25e5a2"],"incoming":["2027.7a63343d-0fa0-4995-8925-961ce1723f22","2027.9701bbae-9fdc-4288-b13a-3ee505b9c3e3"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":420,"y":254,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":["console.log( tw.local.variable2 );"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"Text1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","optionName":"showOverflowTooltip","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f8fbe90d-1805-42b5-888e-42b09e05267d","optionName":"@label","value":"Plain text"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"df77d7f3-9384-4da5-8895-0f88453a00bc","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2f63e9c5-8053-49d4-8d6b-f344f497c0f3","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.5663dd71-ff18-4d33-bea0-468d0b869816","binding":"tw.local.variable1","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5c377dd0-4610-4d2f-8400-c7bcba20e670","version":"8550"},{"layoutItemId":"Date_Time_Picker1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e6fddfc6-948f-4387-8895-c2d57f98b9e7","optionName":"@label","value":"Date\/time picker"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"46c9ec43-ebd8-49a5-8bd6-930d7803e170","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"617bbe65-1d19-4608-87ae-9e376ef71428","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b2878203-0d39-4ccb-8d47-f065490c7592","optionName":"colorStyle","value":"S"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"649cb27b-101d-40bd-8d2a-e2330c19a2e2","optionName":"format","value":"dd\/MM\/yyyy"}],"viewUUID":"64.54643ff2-8363-4976-bb5e-d4eb1094cca3","binding":"tw.local.variable2","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ee9b5d26-7307-4d6f-843d-ee53b1a0a0ed","version":"8550"},{"layoutItemId":"okbutton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ca8832e6-fa36-4bcc-8118-b183e9a5d3c5","optionName":"@label","value":"OK"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"30641d79-3729-4525-87e6-7bf887658f54"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Coach 2","isForCompensation":false,"completionQuantity":1,"id":"2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"variable1","isCollection":false,"declaredType":"dataObject","id":"2056.508c3d64-2d02-4f9c-8445-11f364d0f99b"},{"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"variable2","isCollection":false,"declaredType":"dataObject","id":"2056.2a56260c-9e43-4d69-8ded-7c5333e14060"},{"targetRef":"2025.825a9340-93fe-4ecf-88a8-d6b2b6b9b8db","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"3c34cf0b-997b-419e-8ba4-1cfdbf25e1fb","coachEventPath":"okbutton"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Service 1","declaredType":"sequenceFlow","id":"2027.7f62c1af-6893-4c1f-83cd-c9886a25e5a2","sourceRef":"2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f"},{"targetRef":"2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach 2","declaredType":"sequenceFlow","id":"2027.7a63343d-0fa0-4995-8925-961ce1723f22","sourceRef":"2025.825a9340-93fe-4ecf-88a8-d6b2b6b9b8db"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"69449538-1f02-4239-9b35-220d6b8ad59f","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"a8e91352-7e5e-467d-8cf4-6202dadbd201","processType":"None"}],"exposedAs":["URL"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"test","declaredType":"globalUserTask","id":"1.9862410e-bba9-43a0-b511-69ae076e3b64","ioSpecification":{"inputSet":[{"id":"_88c8a8a4-a3bf-4df9-a1ee-6eb30c912376"}],"outputSet":[{"id":"_c6de9e6a-d2fc-4fd9-9db8-98a0295d8bff"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"12444825-169c-4793-a165-4d486374e51a"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processVariable name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.377f6406-1776-4c53-87d0-696fb720e06f</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0051dfd2-5991-477b-8583-55240ef3d31b</guid>
            <versionId>8112b7f8-0561-4634-8b57-db51cf64a53b</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a1f8c3c8-3422-4ce9-8318-b02d3c77bd16</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>720a7afb-959c-43f0-af05-0fdf2a299c1b</guid>
            <versionId>9195d5d8-f920-4356-9ee0-bdafe9e2b8e4</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b7eaa48-b70f-4c20-8bdb-f2afe6d6f254</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f0d62f29-b944-44fc-aeef-142f2033cb02</guid>
            <versionId>f660455f-3c98-4dc1-aba3-afb1aa396751</versionId>
        </processVariable>
        <processVariable name="folderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2cf6e963-1068-45b4-8ea5-9984bd0012a7</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e3f92756-77b2-4049-a8c4-0fa853601fe2</guid>
            <versionId>0825ed22-c7cf-4e77-9f90-d61249aa460b</versionId>
        </processVariable>
        <processVariable name="fullPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a2c0cdc2-4d4a-4ca5-8ef3-264200c73106</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9384dec2-c8f9-4626-b198-323287d2c83b</guid>
            <versionId>ff4ed5a1-ccb2-4454-9b73-27997cb80dc6</versionId>
        </processVariable>
        <processVariable name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4f0d0b02-e780-4150-803a-f091837b8709</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>056a3a12-7a9d-45c9-82d7-ba94339f36cf</guid>
            <versionId>7f252988-b6a8-4023-8f04-492a5eb7eb0e</versionId>
        </processVariable>
        <processVariable name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d1b09e62-fac7-4a2c-857b-c51cea8e976a</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5656b9b1-3287-4b78-9838-58898e6b82e8</guid>
            <versionId>45893752-ef4d-4e9b-ad00-3ba5706b0095</versionId>
        </processVariable>
        <processVariable name="validationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5e8acb05-**************-66ba88634d17</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cea621de-223b-48d6-9b81-7ead9e11320d</guid>
            <versionId>03b7d63f-3134-45bd-8c62-58683f4aaef4</versionId>
        </processVariable>
        <processVariable name="ValidationMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ea1a02f7-b786-4e99-8fd5-b484d4cbcaf9</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1da236c5-1d53-470b-a37b-4132977cf97f</guid>
            <versionId>1d3c25ef-851c-45b2-88da-519cf22fdc3a</versionId>
        </processVariable>
        <processVariable name="variable1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.508c3d64-2d02-4f9c-8445-11f364d0f99b</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>24826f2d-bb49-4266-9dee-5cf2062b9e2a</guid>
            <versionId>f9084df4-5219-45e3-90b7-ca6b0bb060fc</versionId>
        </processVariable>
        <processVariable name="variable2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2a56260c-9e43-4d69-8ded-7c5333e14060</processVariableId>
            <description isNull="true" />
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d1bd6adb-5692-4b9e-ab8a-1ba695094aed</guid>
            <versionId>b4a5f445-3a34-401b-8579-18d6a048e68b</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.606d0e11-4672-4305-b0c1-80cd12305aef</processItemId>
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.0c82e727-207d-4dae-aa46-17c986fd32e0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cdc758d910e20d2d:-607ed7:18afb1305a9:-13fa</guid>
            <versionId>10c7322a-a1a3-4122-9f2b-17ef042bcf4e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c9703dec-4650-49b5-8b70-534891186ff8</processItemId>
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <name>Create ECM Folder</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.538a0201-1db9-4e94-b4e9-e41c0ebb8bcd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1d60</guid>
            <versionId>19601a3a-72bc-4b41-9c02-77d6bccae311</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.538a0201-1db9-4e94-b4e9-e41c0ebb8bcd</subProcessId>
                <attachedProcessRef>/1.46b984a3-b4ad-405a-abd3-8631f907efe4</attachedProcessRef>
                <guid>9b89ec1a-b7b7-416b-acca-c74f1543c85f</guid>
                <versionId>578f6fa6-6482-47da-becd-9bdcc1b33287</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.354f37ff-3315-4cba-b36a-0a1e84895a2f</processItemId>
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.1cbcf92b-1660-4220-9c70-bb772a8430e0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cdc758d910e20d2d:-607ed7:18afb1305a9:-13fb</guid>
            <versionId>8663ad29-21a5-4567-a7a5-3d60c7afb66e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.1cbcf92b-1660-4220-9c70-bb772a8430e0</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>608cbe64-4d9b-485e-bf37-e7650d4f27fd</guid>
                <versionId>d0a20873-b056-4c98-9ec1-fa30b9c80bd9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.825a9340-93fe-4ecf-88a8-d6b2b6b9b8db</processItemId>
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <name>Service 1</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.f5785c23-**************-03dad43f2e40</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-768c</guid>
            <versionId>afdff9dd-e0ab-4369-857a-fc165a78a803</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.f5785c23-**************-03dad43f2e40</subProcessId>
                <attachedProcessRef>/1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</attachedProcessRef>
                <guid>cf08ec76-6989-4529-a6ac-8b883f6f498c</guid>
                <versionId>b58c396d-f076-49b5-bb8f-10981ba14ffc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9a0c88a8-80fa-47d5-8fb9-a82a62f49bdb</processItemId>
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <name>Service</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.22e4e58a-204f-426d-827d-cf57bd6a9e77</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:28b</guid>
            <versionId>bdcaa791-d337-44a6-9bd2-4e90a32f3d81</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.22e4e58a-204f-426d-827d-cf57bd6a9e77</subProcessId>
                <attachedProcessRef>/1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</attachedProcessRef>
                <guid>3a6ffb34-8c27-4930-a3bd-fde6d11ba45a</guid>
                <versionId>4df3c7a3-2bec-4a2c-9844-75fa13724c53</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e9efb824-60ba-4d72-8aeb-87c755db0abf</processItemId>
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <name>Set ECM default properties</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.399fd083-714f-408b-90d1-ea62fb8d9361</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1d5f</guid>
            <versionId>fee1c868-57fa-48fa-91a2-460888106c74</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.399fd083-714f-408b-90d1-ea62fb8d9361</subProcessId>
                <attachedProcessRef>/1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</attachedProcessRef>
                <guid>558479b0-e4f8-48bb-87f1-391cc7f6fb2f</guid>
                <versionId>99ed3a69-0417-4fae-8f35-8470fb82084f</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.606d0e11-4672-4305-b0c1-80cd12305aef</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="12444825-169c-4793-a165-4d486374e51a" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns16:globalUserTask name="test" id="1.9862410e-bba9-43a0-b511-69ae076e3b64">
                    <ns16:documentation />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation id="a8e91352-7e5e-467d-8cf4-6202dadbd201">
                            <ns16:startEvent name="Start" id="936adb44-de65-4585-a3fe-77fd59dd5041">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.9701bbae-9fdc-4288-b13a-3ee505b9c3e3</ns16:outgoing>
                            </ns16:startEvent>
                            <ns3:formTask name="Coach" id="2025.add38abe-a2cc-43e4-99c4-c85b09782fe3">
                                <ns16:extensionElements>
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns13:nodeVisualInfo x="748" y="207" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>b9d80baa-e9b4-4e13-8042-5d4018771603</ns19:id>
                                                <ns19:layoutItemId>Importer_Details_CV1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>6d08f30a-c8e0-4f7b-8e05-98177398cc2a</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Importer Details CV</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>dc3ec603-e37b-4299-8e62-82c2a7566834</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6a78e526-66db-4557-8270-5d7d81acf8aa</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.0ff96fd8-0740-4d17-887e-a56c8ef7921b</ns19:viewUUID>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:endEvent name="End" id="b3165fad-15fa-44dd-905e-68f51eac6d93">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1181" y="196" width="24" height="44" color="#F8F8F8" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.e23113d1-614d-4736-8775-05a8253c6226</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:sequenceFlow sourceRef="936adb44-de65-4585-a3fe-77fd59dd5041" targetRef="2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f" name="To Coach" id="2027.9701bbae-9fdc-4288-b13a-3ee505b9c3e3">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" name="odcRequest" id="2056.377f6406-1776-4c53-87d0-696fb720e06f">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.initiator = "";
autoObject.requestNature = {};
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = {};
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new Date();
autoObject.ImporterName = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = {};
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = {};
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = {};
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = {};
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = {};
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = {};
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = {};
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = {};
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = [];
autoObject.BasicDetails.Bills[0] = {};
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();
autoObject.BasicDetails.Invoice = [];
autoObject.BasicDetails.Invoice[0] = {};
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new Date();
autoObject.GeneratedDocumentInfo = {};
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = [];
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = [];
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = [];
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = [];
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = [];
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = {};
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = [];
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = {};
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = {};
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new Date();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = {};
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = [];
autoObject.FinancialDetailsBR.listOfAccounts[0] = {};
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = {};
autoObject.FcCollections.currency = {};
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new Date();
autoObject.FcCollections.ToDate = new Date();
autoObject.FcCollections.accountNo = {};
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = [];
autoObject.FcCollections.retrievedTransactions[0] = {};
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = [];
autoObject.FcCollections.selectedTransactions[0] = {};
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new Date();
autoObject.FcCollections.selectedTransactions[0].valueDate = new Date();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = [];
autoObject.FcCollections.listOfAccounts[0] = {};
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = {};
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new Date();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = {};
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = [];
autoObject.FinancialDetailsFO.multiTenorDates[0] = {};
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = {};
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = {};
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = {};
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = {};
autoObject.ProductShipmentDetails.CBECommodityClassification = {};
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new Date();
autoObject.ProductShipmentDetails.shipmentMethod = {};
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = {};
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = {};
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = {};
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = {};
autoObject.ContractCreation.productCode = {};
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new Date();
autoObject.ContractCreation.valueDate = new Date();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new Date();
autoObject.Parties = {};
autoObject.Parties.Drawer = {};
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = {};
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = {};
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = {};
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = {};
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = [];
autoObject.ChargesAndCommissions[0] = {};
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = {};
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = {};
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = {};
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = {};
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new Date();
autoObject.ContractLiquidation.creditValueDate = new Date();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = {};
autoObject.ContractLiquidation.creditedAccount.accountClass = {};
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = {};
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = {};
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = [];
autoObject.actions[0] = "";
autoObject.attachmentDetails = {};
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = {};
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = [];
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = [];
autoObject.attachmentDetails.attachment[0] = {};
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = [];
autoObject.complianceComments[0] = {};
autoObject.complianceComments[0].startTime = new Date();
autoObject.complianceComments[0].endTime = new Date();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = [];
autoObject.History[0] = {};
autoObject.History[0].startTime = new Date();
autoObject.History[0].endTime = new Date();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = {};
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.06b6a779-0cff-4736-8b6a-9502a2a5804a" name="Set attach." id="2025.47f9b301-580d-481b-8884-09eed62550cd">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="182" y="47" width="95" height="70" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.06b6a779-0cff-4736-8b6a-9502a2a5804a</ns16:outgoing>
                                <ns16:script>tw.local.odcRequest.appInfo.instanceID="9999";&#xD;
&#xD;
tw.local.odcRequest.CustomerInfo.customerName="smsma";&#xD;
tw.local.odcRequest.CustomerInfo.cif= "00000000";&#xD;
&#xD;
tw.local.odcRequest.requestNo="11112222333311";&#xD;
&#xD;
//var index= 0;&#xD;
//&#xD;
//if(!tw.local.odcRequest.attachmentDetails)&#xD;
//  tw.local.odcRequest.attachmentDetails = {};&#xD;
//&#xD;
//if (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {   &#xD;
//	tw.local.odcRequest.attachmentDetails.attachment = []; 	&#xD;
//		&#xD;
//	setProps("AIRWAY BILL", "AIRWAY BILL", "" ); &#xD;
//	setProps("TRUCK CONSIGNMENT NOTE","TRUCK CONSIGNMENT NOTE", ""); &#xD;
//	setProps("N/N BILL OF LADING","N/N BILL OF LADING",""); &#xD;
//	setProps("COURIER / POST RECEIPT", "COURIER / POST RECEIPT" , "" ); &#xD;
//	setProps("PACKING LIST","PACKING LIST",""); &#xD;
//	setProps("CERTIFICATE OF ORIGIN","CERTIFICATE OF ORIGIN", "" );&#xD;
//	setProps("CERTIFICATE OF ANALYSIS", "CERTIFICATE OF ANALYSIS", "");&#xD;
//	setProps("INSURANCE POLICY / CERTIFICATE", "INSURANCE POLICY / CERTIFICATE","");&#xD;
//	setProps("BENEFECIARY DECLARATION", "BENEFECIARY DECLARATION", "");&#xD;
//	setProps("NON RADIOACTIVE CERTIFICATE", "NON RADIOACTIVE CERTIFICATE","");&#xD;
//	setProps("PHYTOSANITARY CERTIFICATE", "PHYTOSANITARY CERTIFICATE","");&#xD;
//	setProps("CERTIFICATE OF ANALYSIS","Bill of exchange/draft", "الكمبيالة" );&#xD;
//	setProps("HEALTH CERTIFICATE","HEALTH CERTIFICATE", "");&#xD;
//	setProps("INSPECTION CERTIFICATE", "INSPECTION CERTIFICATE",  "");&#xD;
//	setProps("WARRANTY CERTIFICATE", "WARRANTY CERTIFICATE","");&#xD;
//	setProps( "TEST CERTIFICATE","TEST CERTIFICATE", "");&#xD;
//&#xD;
//	tw.local.odcRequest.attachmentDetails.ecmProperties = {};&#xD;
//	tw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
//	tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};&#xD;
//}	&#xD;
//&#xD;
//function setProps(name, desc, arabicName){	&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index]= {};&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].name= name;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].description= desc;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].arabicName= arabicName;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].numOfOriginals= 0;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].numOfCopies= 0;&#xD;
//	index++;	&#xD;
//}&#xD;
//&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:callActivity calledElement="1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f103f1b1-0053-42a7-8282-eee71ac34d30" name="Set ECM default properties" id="2025.e9efb824-60ba-4d72-8aeb-87c755db0abf">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="383" y="27" width="95" height="70" />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.06b6a779-0cff-4736-8b6a-9502a2a5804a</ns16:incoming>
                                <ns16:outgoing>2027.f103f1b1-0053-42a7-8282-eee71ac34d30</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.399c7a58-00b5-4451-9813-41c0b9652088</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.CustomerInfo.cif</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.c28023fb-b45e-4b63-ae36-97e6df6421bc</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.afec08e9-d3db-4a02-a361-299823096b13</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"somaia.galal"</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.25394215-074f-4b79-8e84-9a96d32cc83b</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.9bafb7a3-8d61-40e3-8c95-1a46b34fcc75</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.CustomerInfo.customerName</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.72f8c4d9-e116-4c90-9dcd-29746fff0925</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.eac829db-66fe-43f5-810c-6faa514533a2">tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.7d269650-ee48-4101-80db-2807cf921562</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9">tw.local.odcRequest.attachmentDetails</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.0261e8ad-a540-4682-88c5-87dff3eab23c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:callActivity calledElement="1.46b984a3-b4ad-405a-abd3-8631f907efe4" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0" name="Create ECM Folder" id="2025.c9703dec-4650-49b5-8b70-534891186ff8">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="523" y="27" width="95" height="70" />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                    <ns3:postAssignmentScript>tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = tw.local.parentPath;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.folderID = tw.local.odcRequest.folderID;&#xD;
&#xD;
console.log("Folder id=============== "+ tw.local.odcRequest.folderID);&#xD;
</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.f103f1b1-0053-42a7-8282-eee71ac34d30</ns16:incoming>
                                <ns16:outgoing>2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4156964b-1c67-40bc-8f62-3804c71cf908</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.e2ce0eed-342c-4942-8214-83e964b550e5</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"599"</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.folderID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.5f955245-0538-4e40-80a6-12f45c3102f3</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.214c7268-80d0-444d-8702-dd0d5462dbe7</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fullPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.676e3a06-e2cc-4855-84d6-6f82a350500a</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.47f9b301-580d-481b-8884-09eed62550cd" targetRef="2025.e9efb824-60ba-4d72-8aeb-87c755db0abf" name="To Set ECM default properties" id="2027.06b6a779-0cff-4736-8b6a-9502a2a5804a">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.e9efb824-60ba-4d72-8aeb-87c755db0abf" targetRef="2025.c9703dec-4650-49b5-8b70-534891186ff8" name="To Create ECM Folder" id="2027.f103f1b1-0053-42a7-8282-eee71ac34d30">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:formTask name="SG TST" id="2025.3ba93f47-71ec-46a1-88dd-86c5a18d5ff1">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="718" y="28" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0</ns16:incoming>
                                <ns16:outgoing>2027.fdf3e62e-8999-4440-8302-c6ce5ef38788</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>66053fcb-bed1-4888-84d9-4318929fb6f1</ns19:id>
                                                <ns19:layoutItemId>Attachment_Comments_View1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>322b0be8-e3d8-4a86-81d3-b9e0ca534810</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Attachment</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>350ae7bd-e125-463c-8008-63686e07d1de</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>efc779c3-8031-4e03-889e-03aec49ef1f8</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>629e683d-c38a-44cb-8f66-d5bc5e8735c4</ns19:id>
                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>4a3962d7-0743-4e27-8267-f19e284d695a</ns19:id>
                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>604c3994-63b0-4657-820d-040a09cb2665</ns19:id>
                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b8a51546-6238-4ba6-892f-83870b990ee1</ns19:id>
                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a6eea168-465e-45d6-88f9-701000f4ff5c</ns19:id>
                                                    <ns19:optionName>visible</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6c120f08-c562-4153-8589-c1595f6d7b3f</ns19:id>
                                                    <ns19:optionName>remittanceLetterPath</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.path</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>7785b915-6f80-4d0f-8b63-ff86421e41ed</ns19:id>
                                                <ns19:layoutItemId>Horizontal_Layout1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>1f27250c-01f8-4eb9-8e39-1eb896d9c139</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Horizontal layout</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>14077e59-79a1-49f6-8245-febf1512a8bd</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>08987e5f-b141-4f59-8635-1f1f66ce99c3</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>69bb63cc-d2d9-4ea0-8f1e-6508811ffad9</ns19:id>
                                                    <ns19:optionName>hAlignment</ns19:optionName>
                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns19:viewUUID>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>875f574e-5a7c-46c3-837a-9f1c28013c42</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>3a1f39dc-2a01-4043-8aa7-96d5f842cb51</ns19:id>
                                                        <ns19:layoutItemId>Button1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>358fa56e-7808-456b-8970-8649ae67277e</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Submit</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>3b82eff9-fb1d-40dd-8dfa-1ffa6a0deb59</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>db96b518-5f41-43dd-8180-d7aabcc67f64</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>f20dbc49-8001-426b-8df2-98ff700a53fb</ns19:id>
                                                            <ns19:optionName>colorStyle</ns19:optionName>
                                                            <ns19:value>S</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>09fb16e8-89cc-4de0-860a-8decebb69028</ns19:id>
                                                            <ns19:optionName>shapeStyle</ns19:optionName>
                                                            <ns19:value>R</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>87173035-a93e-478b-8d40-7aa6aaf810fd</ns19:id>
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:sequenceFlow sourceRef="2025.c9703dec-4650-49b5-8b70-534891186ff8" targetRef="2025.3ba93f47-71ec-46a1-88dd-86c5a18d5ff1" name="To SG TST" id="2027.60f2dd23-bdbe-49b4-8a72-af0e1fd4c9f0">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.a1f8c3c8-3422-4ce9-8318-b02d3c77bd16" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.9b7eaa48-b70f-4c20-8bdb-f2afe6d6f254" />
                            <ns16:dataObject itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" name="folderID" id="2056.2cf6e963-1068-45b4-8ea5-9984bd0012a7" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="fullPath" id="2056.a2c0cdc2-4d4a-4ca5-8ef3-264200c73106" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentPath" id="2056.4f0d0b02-e780-4150-803a-f091837b8709" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMsg" id="2056.d1b09e62-fac7-4a2c-857b-c51cea8e976a" />
                            <ns16:callActivity calledElement="1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7" default="2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63" name="Service" id="2025.9a0c88a8-80fa-47d5-8fb9-a82a62f49bdb">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="882" y="28" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.fdf3e62e-8999-4440-8302-c6ce5ef38788</ns16:incoming>
                                <ns16:outgoing>2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.1bf8ade2-bd6b-4af2-87c8-1b269002413f</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"create"</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.f43a0726-0192-4d4c-942e-83e973ee5015</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.folderID</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.c965f53e-7627-46ac-8f53-b8a0e4d9e4fb</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.validationMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.9a0c88a8-80fa-47d5-8fb9-a82a62f49bdb" targetRef="2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec" name="To Valid?" id="2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.3ba93f47-71ec-46a1-88dd-86c5a18d5ff1" targetRef="2025.9a0c88a8-80fa-47d5-8fb9-a82a62f49bdb" name="To Service" id="2027.fdf3e62e-8999-4440-8302-c6ce5ef38788">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="bc67ebe3-046f-47cd-869b-0cbd7300f123">
                                        <ns3:coachEventPath>Button1</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validationMessage" id="2056.5e8acb05-**************-66ba88634d17" />
                            <ns16:exclusiveGateway default="2027.12141206-f86b-425c-8650-c2780fb35043" name="Valid?" id="2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1005" y="47" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4bed5c4d-2c2d-400a-8cb6-30b6a124bd63</ns16:incoming>
                                <ns16:outgoing>2027.e23113d1-614d-4736-8775-05a8253c6226</ns16:outgoing>
                                <ns16:outgoing>2027.12141206-f86b-425c-8650-c2780fb35043</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec" targetRef="b3165fad-15fa-44dd-905e-68f51eac6d93" name="Yes" id="2027.e23113d1-614d-4736-8775-05a8253c6226">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.validationMessage== ""  || tw.local.validationMessage  == null )</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.c7dce2c1-798c-4348-86bd-74b1f4398cd6">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="988" y="135" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.12141206-f86b-425c-8650-c2780fb35043</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.79b4c01e-fc80-4ecc-82cf-d72f1822c7ec" targetRef="2025.c7dce2c1-798c-4348-86bd-74b1f4398cd6" name="No" id="2027.12141206-f86b-425c-8650-c2780fb35043">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.validationMessage</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns3:formTask name="Coach 1" id="2025.e76cc97a-7b50-430f-8ecb-83c544620e83">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="131" y="179" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                </ns16:extensionElements>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:CustomHTML" version="8550">
                                                <ns19:id>e0903732-b88f-4bf2-834a-7936a1d1f1b4</ns19:id>
                                                <ns19:layoutItemId>CustomHTML1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>7ac457a8-942d-43f4-8d98-c3865bf337c4</ns19:id>
                                                    <ns19:optionName>@customHTML.contentType</ns19:optionName>
                                                    <ns19:value>TEXT</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>2e93ab6e-72eb-43c5-8db0-65436efcfbc3</ns19:id>
                                                    <ns19:optionName>@customHTML.textContent</ns19:optionName>
                                                    <ns19:value>&lt;style&gt;&#xD;
.spark-ui {&#xD;
    background: rgb(255, 255, 255);&#xD;
   // width: 99%;&#xD;
}&#xD;
.CoachView.Panel&gt;.panel.SPARKPanel {&#xD;
border-collapse: separate;&#xD;
border-spacing: 0;&#xD;
box-shadow: 0px 0px 12px #001B5929!important;&#xD;
border-top: 5px solid #00643e!important;&#xD;
border-radius: 10px!important;&#xD;
}&#xD;
&#xD;
.panel-primary.panel-dark&gt;.panel-heading {&#xD;
background: transparent !important;&#xD;
border-color: transparent;&#xD;
color: #fff;&#xD;
}&#xD;
&#xD;
.panel-primary.panel-dark {&#xD;
border-color: transparent!important;&#xD;
}&#xD;
.panel-primary.panel-dark &gt; .panel-heading .panel-title {&#xD;
    color: rgb(0, 101, 71);&#xD;
    font: normal normal 600 24px/45px Cairo;&#xD;
}&#xD;
&#xD;
.form-control {&#xD;
    border-top-color: rgb(205, 205, 205);&#xD;
    border-bottom-color: rgb(205,205,205);&#xD;
    overflow: hidden;&#xD;
    text-overflow: ellipsis;&#xD;
    white-space: nowrap;&#xD;
    padding: 12px 12px;&#xD;
    color: #181A1D !important;&#xD;
    background: #FFFFFF 0% 0% no-repeat padding-box;&#xD;
    border: 1px solid #ddd;&#xD;
    border-radius: 8px;&#xD;
}&#xD;
.SPARKWell .stat-cell .bg-icon {&#xD;
    line-height: normal;&#xD;
    height: 100%;&#xD;
    overflow: hidden;&#xD;
 //   width: 97%;&#xD;
    border-radius: inherit;&#xD;
    box-shadow: 0px 0px 12px #001B5929!important;&#xD;
    margin-bottom: 6px;&#xD;
    border-radius: 10px!important;&#xD;
    border-top: 5px solid !important;&#xD;
   // margin-right: 49px;&#xD;
}&#xD;
&#xD;
//.form-control[disabled] {&#xD;
//    background: rgb(242 242 242);&#xD;
//}&#xD;
&#xD;
.bg-success {&#xD;
    background: #00654726 !important;&#xD;
}&#xD;
//.Single_Select select.placeHolder {&#xD;
//    color: #0002037a;&#xD;
//}&#xD;
&#xD;
.panel-group .panel-heading+.panel-collapse .panel-body {&#xD;
     border-top: 1px solid #fff;&#xD;
}&#xD;
&#xD;
.panel {&#xD;
	     margin-bottom: 18px;&#xD;
	     border-radius: 2px;&#xD;
	     border-width: 0px;&#xD;
}&#xD;
&#xD;
.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {&#xD;
	     background: #fff;&#xD;
	     color: #006547;&#xD;
	     border-color: #fff;&#xD;
	     padding: 15px;&#xD;
	     border-radius: 10px!important;&#xD;
}&#xD;
&#xD;
.CoachView.Collapsible_Panel&gt;.panel.SPARKCPanel {&#xD;
	     border-collapse: collapse;&#xD;
	     box-shadow: 0px 0px 22px #001B5929!important;&#xD;
}&#xD;
.SPARKCPanel &gt; .panel-heading {&#xD;
    padding: 0px;&#xD;
    border-top-left-radius: 1px;&#xD;
    border-top-right-radius: 1px;&#xD;
    box-shadow: 0px 0px 0px #001B5929!important;&#xD;
    border-top: 5px solid #00643e!important;&#xD;
    border-radius: 10px!important;&#xD;
    border-spacing: 0;&#xD;
    border-collapse: separate;&#xD;
}&#xD;
.panel-body {&#xD;
    background: #fff;&#xD;
    margin: 0;&#xD;
    padding-bottom: 15px;&#xD;
    padding-left: 15px;&#xD;
    padding-right: 15px;&#xD;
    padding-top: 15px;&#xD;
    border-bottom-left-radius: 10px;&#xD;
    border-bottom-right-radius: 10px;&#xD;
}&#xD;
&#xD;
.panel-group .panel {&#xD;
    border-radius: 10px;&#xD;
}&#xD;
&#xD;
.Radio_Button_Group .radio3, .Radio_Button_Group .checkbox3{&#xD;
    width: 50%;&#xD;
}&#xD;
.radio3 &gt; input + span{&#xD;
    display: inline-flex;&#xD;
    flex-direction: row;&#xD;
    align-items: center;&#xD;
}&#xD;
//.input-group.no-border&gt;.input-group-addon {&#xD;
//    border-radius: 10px;&#xD;
//    min-width: 54px;&#xD;
//    height: 54px;&#xD;
//    top: -25px;&#xD;
//}&#xD;
&#xD;
//.form-group .input{&#xD;
//    padding-left:73px!important;&#xD;
//}&#xD;
//.Input_Group .outer, .control-label{&#xD;
//padding-left:73px;&#xD;
//}&#xD;
//.Single_Select, .control-label{&#xD;
//padding-left:20!important;&#xD;
//}&#xD;
.Input_Group .ContentBox {&#xD;
    width: 100%;&#xD;
    border-collapse: collapse;&#xD;
    padding-left: 20px;&#xD;
}&#xD;
&#xD;
.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle {&#xD;
    background: #fff;&#xD;
    color: #006547;&#xD;
    border-color: #fff;&#xD;
    border-radius: 10px!important;&#xD;
    font-size: 18px;&#xD;
    font-weight: 900;&#xD;
    padding: 15px;&#xD;
    font: normal normal 600 24px/45px Cairo;&#xD;
}&#xD;
&#xD;
.btn:not(.SPARKIcon), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):not(.SPARKIcon):focus, .btn:not(.SPARKIcon).btn-outline.active:not([role="img"]), .btn:not(.SPARKIcon).btn-outline:not([role="img"]):active {&#xD;
    padding: 12px 25px;&#xD;
}&#xD;
.btn-success, .btn-success:not([role="img"]):focus {&#xD;
    color: #FFFFFF;&#xD;
    fill: #006547;&#xD;
    border-color: #006643;&#xD;
    border-bottom-color: #006643;&#xD;
    background: #006643;&#xD;
    background-image: linear-gradient(to bottom, #006643 0, #006643 100%) !important;&#xD;
    background-repeat: repeat-x;&#xD;
}&#xD;
&#xD;
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control{&#xD;
letter-spacing: 0px;&#xD;
color: #181A1D !important;&#xD;
opacity: 1;&#xD;
font: normal normal normal 16px/20px Cairo;&#xD;
background: transparent;&#xD;
font-weight: bold;&#xD;
border: 1px solid #000203;&#xD;
}&#xD;
//.Single_Select&gt;.form-group&gt;.input&gt;select {&#xD;
//&#xD;
//    border-top-style: ridge;&#xD;
//    border-bottom-style: outset;&#xD;
//    border-right-style: outset;&#xD;
//    border-left-style: ridge;&#xD;
//    border-top-width: revert;&#xD;
//    border-left-width: revert;&#xD;
//    border-bottom-width: revert;&#xD;
//    border-right-width: revert;&#xD;
//}&#xD;
select.input-sm, .input-sm.form-control, .input-sm.form-control[type='text'] {&#xD;
    height: 40px;&#xD;
    line-height: 1.33;&#xD;
}&#xD;
&#xD;
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {&#xD;
    letter-spacing: 0px;&#xD;
    color: #181A1D !important;&#xD;
    opacity: 1;&#xD;
    font: 14px Cairo;&#xD;
    background: transparent;&#xD;
    font-weight: lighter;&#xD;
}&#xD;
&#xD;
&#xD;
.btn-success:not([role="img"]):hover {&#xD;
    color: #ffffff;&#xD;
    fill: #3D8A70;&#xD;
    border-color: #3D8A70;&#xD;
    border-bottom-color: #429c42;&#xD;
    background: #3D8A70;&#xD;
    background-image: -webkit-linear-gradient(top, #3D8A70 0, #3D8A70 100%) !important;&#xD;
    background-image: linear-gradient(to bottom, #3D8A70 0, #3D8A70 100%) !important;&#xD;
    background-repeat: repeat-x;&#xD;
}&#xD;
&#xD;
.panel-group.panel-group-primary&gt;.panel&gt;.panel-heading&gt;.accordion-toggle:hover {&#xD;
    background: rgb(255 255 255 / 15%);&#xD;
}&#xD;
.panel-group .SPARKTable &gt; .panel-heading {&#xD;
    border-color: rgb(240, 240, 240);&#xD;
    border-bottom-width: 2px&#xD;
;&#xD;
    border-bottom-style: solid;&#xD;
    background: #006643 0% 0% no-repeat padding-box;&#xD;
    color: white;&#xD;
}&#xD;
.Output_Text&gt;.form-group&gt;.input&gt;p {&#xD;
  &#xD;
    padding-right: 1em;&#xD;
 &#xD;
}&#xD;
&#xD;
&#xD;
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {&#xD;
   &#xD;
    font-weight: 600;&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
[class*="BPM_Resp_"] .bpm-label-default {&#xD;
 height: 30px;&#xD;
    font: normal normal normal 16px/20px Cairo !important;&#xD;
    letter-spacing: 0px;&#xD;
    opacity: 1;&#xD;
    color: #8B8C8E;&#xD;
}&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
//.ECMPropertiesContainer{&#xD;
//display: none; &#xD;
//}&#xD;
&#xD;
&#xD;
.Output_Text&gt;.form-group&gt;.input&gt;p{&#xD;
unicode-bidi: plaintext ;&#xD;
text-align: inherit;&#xD;
}&#xD;
&#xD;
.CoachViewRTL {&#xD;
 &#xD;
    text-align: right !important;&#xD;
}&#xD;
&#xD;
.CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-RightAlign&gt;*, .CoachViewRTL .BPMGridLayoutHorz.BPMGrid-lg-CenterAlign&gt;* {&#xD;
    text-align: right;&#xD;
}&#xD;
&#xD;
.noIScroll.alignJustify {&#xD;
    text-align: inherit;&#xD;
}&#xD;
.radio3 &gt; input + span {&#xD;
&#xD;
    unicode-bidi: plaintext;&#xD;
&#xD;
}&#xD;
&#xD;
.radio3 &gt; input:checked + span::after {&#xD;
 &#xD;
  top: calc(50% - 3px) !important;&#xD;
}&#xD;
.switcher-primary .switcher-state-off {&#xD;
    color: #000203;&#xD;
}&#xD;
.CoachViewRTL .Single_Select &gt;{&#xD;
 unicode-bidi: plaintext;&#xD;
&#xD;
}&#xD;
&#xD;
input[type="checkbox"]:checked[disabled] {&#xD;
    right: 6%;&#xD;
    opacity: 1;&#xD;
    width: fit-content;&#xD;
    left: 3px;&#xD;
}&#xD;
&#xD;
.Tab_Section&gt;div&gt;.nav-tabs-mnu {&#xD;
    position: unset;&#xD;
   }&#xD;
   &#xD;
a {&#xD;
    color: #333;&#xD;
    text-decoration: none;&#xD;
}&#xD;
.datepicker thead th {&#xD;
   &#xD;
    color: #006547;&#xD;
&lt;/style&gt;</ns19:value>
                                                </ns19:configData>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>c7bd54e0-e9cb-421e-8963-0e6e673d3b2f</ns19:id>
                                                <ns19:layoutItemId>Contract_Liquidation1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>4026707c-0b48-4058-8b10-5f60f770540e</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Contract Liquidation</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>793326ea-3fed-4f63-893f-8a7fb6c6a7e0</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c7c38c10-9f99-45dd-8f1d-a84ecd21205b</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.14ee925a-157a-48e5-9ab8-7b8879adbe5b</ns19:viewUUID>
                                                <ns19:binding>tw.local.odcRequest</ns19:binding>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                <ns19:id>3f6c9a0b-0cf5-4cef-84f0-55aee44f3618</ns19:id>
                                                <ns19:layoutItemId>okbutton</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>b2e991e1-39e2-492e-834f-6af0b79668fa</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>OK</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="ValidationMessage" id="2056.ea1a02f7-b786-4e99-8fd5-b484d4cbcaf9" />
                            <ns16:callActivity calledElement="1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8" default="2027.7a63343d-0fa0-4995-8925-961ce1723f22" name="Service 1" id="2025.825a9340-93fe-4ecf-88a8-d6b2b6b9b8db">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="416" y="375" width="95" height="70" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7f62c1af-6893-4c1f-83cd-c9886a25e5a2</ns16:incoming>
                                <ns16:outgoing>2027.7a63343d-0fa0-4995-8925-961ce1723f22</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.variable1</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be">tw.local.variable2</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns3:formTask name="Coach 2" id="2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="420" y="254" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns3:preAssignmentScript>console.log( tw.local.variable2 );</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7a63343d-0fa0-4995-8925-961ce1723f22</ns16:incoming>
                                <ns16:incoming>2027.9701bbae-9fdc-4288-b13a-3ee505b9c3e3</ns16:incoming>
                                <ns16:outgoing>2027.7f62c1af-6893-4c1f-83cd-c9886a25e5a2</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>5c377dd0-4610-4d2f-8400-c7bcba20e670</ns19:id>
                                                <ns19:layoutItemId>Text1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:optionName>showOverflowTooltip</ns19:optionName>
                                                    <ns19:value>true</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>f8fbe90d-1805-42b5-888e-42b09e05267d</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Plain text</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>df77d7f3-9384-4da5-8895-0f88453a00bc</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>2f63e9c5-8053-49d4-8d6b-f344f497c0f3</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns19:viewUUID>
                                                <ns19:binding>tw.local.variable1</ns19:binding>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>ee9b5d26-7307-4d6f-843d-ee53b1a0a0ed</ns19:id>
                                                <ns19:layoutItemId>Date_Time_Picker1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>e6fddfc6-948f-4387-8895-c2d57f98b9e7</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Date/time picker</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>46c9ec43-ebd8-49a5-8bd6-930d7803e170</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>617bbe65-1d19-4608-87ae-9e376ef71428</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b2878203-0d39-4ccb-8d47-f065490c7592</ns19:id>
                                                    <ns19:optionName>colorStyle</ns19:optionName>
                                                    <ns19:value>S</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>649cb27b-101d-40bd-8d2a-e2330c19a2e2</ns19:id>
                                                    <ns19:optionName>format</ns19:optionName>
                                                    <ns19:value>dd/MM/yyyy</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.54643ff2-8363-4976-bb5e-d4eb1094cca3</ns19:viewUUID>
                                                <ns19:binding>tw.local.variable2</ns19:binding>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                <ns19:id>30641d79-3729-4525-87e6-7bf887658f54</ns19:id>
                                                <ns19:layoutItemId>okbutton</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>ca8832e6-fa36-4bcc-8118-b183e9a5d3c5</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>OK</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="variable1" id="2056.508c3d64-2d02-4f9c-8445-11f364d0f99b" />
                            <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="variable2" id="2056.2a56260c-9e43-4d69-8ded-7c5333e14060" />
                            <ns16:sequenceFlow sourceRef="2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f" targetRef="2025.825a9340-93fe-4ecf-88a8-d6b2b6b9b8db" name="To Service 1" id="2027.7f62c1af-6893-4c1f-83cd-c9886a25e5a2">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="3c34cf0b-997b-419e-8ba4-1cfdbf25e1fb">
                                        <ns3:coachEventPath>okbutton</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.825a9340-93fe-4ecf-88a8-d6b2b6b9b8db" targetRef="2025.9fe42101-cb1f-4b1f-895c-529b95b5d48f" name="To Coach 2" id="2027.7a63343d-0fa0-4995-8925-961ce1723f22">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:htmlHeaderTag id="69449538-1f02-4239-9b35-220d6b8ad59f">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:participantRef>24.da7e4d23-78cb-4483-98ed-b9c238308a03</ns3:participantRef>
                        <ns3:exposedAs>URL</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:inputSet id="_88c8a8a4-a3bf-4df9-a1ee-6eb30c912376" />
                        <ns16:outputSet id="_c6de9e6a-d2fc-4fd9-9db8-98a0295d8bff" />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.26339b5e-2a82-4cbc-bba5-de869c47d36a</processLinkId>
            <processId>1.9862410e-bba9-43a0-b511-69ae076e3b64</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.606d0e11-4672-4305-b0c1-80cd12305aef</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.354f37ff-3315-4cba-b36a-0a1e84895a2f</toProcessItemId>
            <guid>bb962783-1120-476e-9783-9224622f58e4</guid>
            <versionId>bcc8bafc-46e1-4be8-ae2f-c39c108e4350</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.606d0e11-4672-4305-b0c1-80cd12305aef</fromProcessItemId>
            <toProcessItemId>2025.354f37ff-3315-4cba-b36a-0a1e84895a2f</toProcessItemId>
        </link>
    </process>
</teamworks>

