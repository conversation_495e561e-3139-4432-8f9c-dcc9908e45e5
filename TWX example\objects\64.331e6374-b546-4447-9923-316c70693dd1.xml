<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.331e6374-b546-4447-9923-316c70693dd1" name="temp">
        <lastModified>1709052338850</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.331e6374-b546-4447-9923-316c70693dd1</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ContentBox" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;9777338d-dfca-4c72-8791-79de42ed3e75&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ContentBox1&lt;/ns2:layoutItemId&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;65152c0b-ec93-4812-8eb3-dd4e2a4745d0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Validation_Helper1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fd8a05f2-484e-4b3f-8caa-e5f2189dca38&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Validation Helper&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;409e9e29-0886-46a1-832a-ea963998b464&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cdd1b7ee-0ff2-41f7-858e-0bef37f6281b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32c13548-605b-45be-869b-60c821e940ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;disableSubmit&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fad8d1db-ba50-46c2-80de-a36016370cb9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;stop&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;577b0f63-e572-4748-855e-33aebd048e7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;runTimeValid&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.72428c7b-aa19-4400-bea7-59743c5442cc&lt;/ns2:viewUUID&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>false</isMobileReady>
        <loadJsFunction>//alert(this.context.viewid)</loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>guid:709ea623f6099cbd:-68e5c70f:18db33126a5:7ba8</guid>
        <versionId>0db3b06a-9f16-4991-8ced-d67558109b4c</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.49798dac-8d44-4bbf-ac79-37b64041cdce</coachViewInlineScriptId>
            <coachViewId>64.331e6374-b546-4447-9923-316c70693dd1</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.mandatory2 = function (me){&#xD;
//	me.setEnabled(false,true)&#xD;
	me.setValid(false,"shit");&#xD;
//	if(me.getData() == null || me.getData() == undefined){&#xD;
//		me.setValid(false,"shit");&#xD;
//		return false&#xD;
//	}else{&#xD;
//		me.setValid(true);&#xD;
//		return true&#xD;
//	} &#xD;
}&#xD;
&#xD;
//---------------------------------------Validation Library------------------------------------&#xD;
/* Add a coach validation error 		&#xD;
EX: addError(tw.local.name , 'validation message!')*/&#xD;
var validationList = [];&#xD;
this.addError = function (path, message) {&#xD;
	if (validationList == null) {&#xD;
		validationList = [];&#xD;
	}&#xD;
	validationList.push({&#xD;
		errorBOPath: path,&#xD;
		errorMessage: message,&#xD;
	});&#xD;
	// return validationList;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the field is null 'Mandatory', &#xD;
   message is OPTIONAL!! , DEFAULT is 'This Field Is Mandatory',&#xD;
   EX: mandatory(tw.local.name , 'tw.local.name', 'validation message') */&#xD;
&#xD;
this.mandatory = function (value, path, message) {&#xD;
	if (!message) {&#xD;
		message = "This Field Is Mandatory";&#xD;
	}&#xD;
&#xD;
	if (value == null) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	} else {&#xD;
		switch (typeof value) {&#xD;
			case "string":&#xD;
				if (value.trim().length == 0) {&#xD;
					this.addError(path, message);&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (value == 0.0 || value &lt; 0.0) {&#xD;
					this.addError(path, message);&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
this.mandatoryList = function (mandatoryList) {&#xD;
	mandatoryList.forEach((element) =&gt; {&#xD;
		this.mandatory(element[0], element[1], element[2]);&#xD;
	});&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is in the past and the last variable is OPTIONAL to exclude today.&#xD;
   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/&#xD;
this.notPastDate = function (value, path, message, exclude) {&#xD;
	if (!value) return;&#xD;
&#xD;
	var checkDate = this.getMidNightDate();&#xD;
	if (exclude) {&#xD;
		if (value != null &amp;&amp; value &lt; checkDate) {&#xD;
			this.addError(path, message);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	} else {&#xD;
		if (value != null &amp;&amp; value &lt;= new Date()) {&#xD;
			this.addError(path, message);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is between two dates.&#xD;
   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/&#xD;
this.dateBetween = function (value, path, date1, date2, message) {&#xD;
	if (value &amp;&amp; value &gt; date1 &amp;&amp; value &lt; date2) {&#xD;
		return true;&#xD;
	}&#xD;
&#xD;
	this.addError(path, message);&#xD;
	return false;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.&#xD;
  EX:	notFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/&#xD;
this.notFutureDate = function (value, path, message, exclude) {&#xD;
	if (!value) return;&#xD;
	var checkDate = this.getMidNightDate();&#xD;
	if (exclude &amp;&amp; exclude === true) {&#xD;
		if (value != null &amp;&amp; value &gt; checkDate) {&#xD;
			this.addError(path, message);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	} else {&#xD;
		if (value != null &amp;&amp; value &gt;= new Date()) {&#xD;
			this.addError(path, message);&#xD;
			return false;&#xD;
		}&#xD;
		return true;&#xD;
	}&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the STRING length is greater than given length (len).&#xD;
  EX:	maxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/&#xD;
this.maxLength = function (value, path, len, message) {&#xD;
	if (value &amp;&amp; value.length &gt; len) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the STRING length is less than given length (len).&#xD;
   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/&#xD;
this.minLength = function (value, path, len, message) {&#xD;
	if (value &amp;&amp; value.length &lt; len) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
this.exactLength = function (value, path, len, message) {&#xD;
	if (value &amp;&amp; value.length != len) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the number is greater than given value (max).&#xD;
 * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/&#xD;
this.maxNumber = function (value, path, max, message) {&#xD;
	if (value &amp;&amp; value &gt; max) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the number is less than given value (min).&#xD;
   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/&#xD;
this.minNumber = function (value, path, min, message) {&#xD;
	if (value &amp;&amp; value &lt; min) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Validates the before and after decimal length (even if string).&#xD;
   message is OPTIONAL , DEFAULT `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it` &#xD;
   Ex: validateDecimal (tw.local.decimal, 'tw.local.decimal', 10, 6, 'validation message')*/&#xD;
this.validateDecimal = function (value, path, beforeDecimal, afterDecimal, message) {&#xD;
	if (!value) return;&#xD;
	if (!message || message == "") {&#xD;
		message = `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it`;&#xD;
	}&#xD;
	// Handle string or number input&#xD;
	if (typeof value === "number") {&#xD;
		value = value.toString();&#xD;
	}&#xD;
	// Regex to check overall format&#xD;
	const regex = new RegExp("^\\d{1," + beforeDecimal + "}\\.?\\d{0," + afterDecimal + "}$");&#xD;
&#xD;
	if (regex.test(value) == false) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
// getter for 'validationErrors', input is (tw.system.coachValidation).&#xD;
this.getErrorList = function (coachValidation) {&#xD;
	coachValidation.validationErrors = validationList;&#xD;
	this.setErrorList();&#xD;
};&#xD;
&#xD;
// setter for 'validationErrors'.&#xD;
this.setErrorList = function () {&#xD;
	validationList = [];&#xD;
};&#xD;
&#xD;
// returns 'new Date()' but reset it to '00:00:00'.&#xD;
this.getMidNightDate = function () {&#xD;
	var midNightDate = new Date();&#xD;
	// Set hours, minutes, seconds and milliseconds to 0&#xD;
	midNightDate.setHours(0);&#xD;
	midNightDate.setMinutes(0);&#xD;
	midNightDate.setSeconds(0);&#xD;
	midNightDate.setMilliseconds(0);&#xD;
&#xD;
	return midNightDate;&#xD;
};&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>ff780c24-d51d-4dc7-8a4b-52f30b868094</guid>
            <versionId>85e06e8b-82c4-440a-af98-24fec9092c09</versionId>
        </inlineScript>
    </coachView>
</teamworks>

