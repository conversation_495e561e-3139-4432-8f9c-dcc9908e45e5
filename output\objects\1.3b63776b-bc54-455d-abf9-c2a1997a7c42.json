{"id": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "versionId": "b2ded4be-8e2d-4411-8c25-3346f14d22f2", "name": "retrieve Search Criteria Result", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "retrieve Search Criteria Result", "lastModified": "1697044011106", "lastModifiedBy": "heba", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "2025.9e9f630b-ab8f-439d-8af5-26bf31e1fde1"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:731d", "versionId": "b2ded4be-8e2d-4411-8c25-3346f14d22f2", "dependencySummary": "<dependencySummary id=\"bpdid:aff098473ecd546d:1d42df0a:18b1b2b8841:27e5\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.b44bf196-2cc8-4670-80e0-6710ea0d6bac\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":280,\"y\":170,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"9190b53a-667a-400e-81a7-496c5b72eef9\"},{\"incoming\":[\"9d817e86-3fa0-4fa6-8a4c-99f02bd83a9c\",\"edc80957-a79c-4321-8fd8-bd8ba51b351a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":794,\"y\":170,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:731f\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"50fcfe94-f6eb-45fa-8fbe-caf3a0d51513\"},{\"targetRef\":\"9e9f630b-ab8f-439d-8af5-26bf31e1fde1\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b44bf196-2cc8-4670-80e0-6710ea0d6bac\",\"sourceRef\":\"9190b53a-667a-400e-81a7-496c5b72eef9\"},{\"startQuantity\":1,\"outgoing\":[\"4ad4d4e9-f817-43e8-804c-19e5e254dcb4\"],\"incoming\":[\"2027.b44bf196-2cc8-4670-80e0-6710ea0d6bac\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":349,\"y\":147,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"sql script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"9e9f630b-ab8f-439d-8af5-26bf31e1fde1\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(tw.local.searchCriteria.CIF != null && tw.local.searchCriteria.CIF != \\\"\\\"  ) \\r\\n{\\r\\n\\ttw.local.sql = \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \\\"\\r\\n\\t                +\\\"where cif = '\\\"+tw.local.searchCriteria.CIF+\\\"' order by REQUESTNO ;\\\"\\r\\n}\\r\\nelse if(tw.local.searchCriteria.requestType != null && tw.local.searchCriteria.requestType != \\\"\\\") \\r\\n{\\r\\n\\ttw.local.sql =  \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus, REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \\\"\\r\\n\\t                +\\\"where REQUESTTYPE = '\\\"+tw.local.searchCriteria.requestType+\\\"' order by REQUESTNO ;\\\"\\r\\n}\\r\\nelse if(tw.local.searchCriteria.BPMRequestNo != null && tw.local.searchCriteria.BPMRequestNo != \\\"\\\") \\r\\n{\\r\\n\\ttw.local.sql =  \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus ,REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \\\"\\r\\n\\t                +\\\"where BPMInstanceNumber = '\\\"+tw.local.searchCriteria.BPMRequestNo+\\\"'  order by REQUESTNO ;\\\"\\r\\n}\\r\\nelse if(tw.local.searchCriteria.requestDate != null ) \\r\\n{\\r\\n\\ttw.local.sql =  \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \\\"\\r\\n\\t               +\\\"where REQUESTDATE = '\\\"+tw.local.searchCriteria.requestDate+\\\"'  order by REQUESTNO ;\\\"\\r\\n}\\r\\nelse if(tw.local.searchCriteria.requestState != null && tw.local.searchCriteria.requestState != \\\"\\\") \\r\\n{\\r\\n\\ttw.local.sql =  \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \\\"\\r\\n\\t               +\\\"where REQUESTSTATUS = '\\\"+tw.local.searchCriteria.requestState+\\\"' order by REQUESTNO ;\\\"\\r\\n}\\r\\nelse if(tw.local.searchCriteria.invoiceNo != null && tw.local.searchCriteria.invoiceNo != \\\"\\\") \\r\\n{\\r\\n\\r\\n\\ttw.local.sql =  \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \\\"\\r\\n\\t\\t\\t    +\\\"Inner join ODC_INVOICE on ODC_REQUESTINFO.ID = ODC_INVOICE.REQUESRID where INVOICENO = '\\\"+tw.local.searchCriteria.invoiceNo+\\\"' order by REQUESTNO ;\\\"\\r\\n}\\r\\nelse if(tw.local.searchCriteria.billOfLading != null && tw.local.searchCriteria.billOfLading != \\\"\\\") \\r\\n{\\r\\n\\ttw.local.sql = \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus ,REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO  Inner join ODC_BILLS \\\"+\\r\\n\\t\\t    \\\"on ODC_REQUESTINFO.ID = ODC_BILLS.REQUESRID where BILLOFLADINGREF = '\\\"+tw.local.searchCriteria.billOfLading+\\\"' order by REQUESTNO ;\\\"\\r\\n}\\r\\n\\r\\nelse if(tw.local.searchCriteria.tradeFoRefrenceNo != null && tw.local.searchCriteria.tradeFoRefrenceNo != \\\"\\\") \\r\\n{\\r\\n\\ttw.local.sql =  \\\"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \\\"\\r\\n\\t+\\\"where TRADEFOREFNO = '\\\"+tw.local.searchCriteria.tradeFoRefrenceNo+\\\"' order by REQUESTNO ;\\\"\\r\\n}\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"c0141879-dcac-4f8e-8f9a-af667140ae04\"],\"incoming\":[\"4ad4d4e9-f817-43e8-804c-19e5e254dcb4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":500,\"y\":147,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"sql execute statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parameters\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"startQuantity\":1,\"outgoing\":[\"9d817e86-3fa0-4fa6-8a4c-99f02bd83a9c\"],\"incoming\":[\"c0141879-dcac-4f8e-8f9a-af667140ae04\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":650,\"y\":147,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Search Result\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"1fb81ce1-0bdd-44d8-871b-90463c6d4a01\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.searchResult = new tw.object.listOf.searchResult();\\r\\n\\r\\nif(tw.local.results.listLength > 0)\\r\\n{\\r\\n\\tfor( var i=0 ; i < tw.local.results[0].rows.listLength ; i++)\\r\\n\\t{\\r\\n\\t\\ttw.local.searchResult[i] = new tw.object.searchResult();\\r\\n\\t\\ttw.local.searchResult[i].customerNumber  \\t    = tw.local.results[0].rows[i].data[0];\\r\\n\\t\\ttw.local.searchResult[i].customerName    \\t    = tw.local.results[0].rows[i].data[1];\\r\\n\\t\\ttw.local.searchResult[i].requestType          = tw.local.results[0].rows[i].data[2];\\r\\n\\t\\ttw.local.searchResult[i].requestNumber        = tw.local.results[0].rows[i].data[3];\\r\\n\\t\\ttw.local.searchResult[i].requestStatus        = tw.local.results[0].rows[i].data[4];\\r\\n\\t\\ttw.local.searchResult[i].requestSubstatus     = tw.local.results[0].rows[i].data[5];\\r\\n\\t\\ttw.local.searchResult[i].requestDate          = tw.local.results[0].rows[i].data[6];\\r\\n\\t\\t\\r\\n\\t\\ttw.local.searchResult[i].currentActivityName  = tw.local.results[0].rows[i].data[7];\\r\\n\\t\\ttw.local.searchResult[i].bpmInstanceID        = tw.local.results[0].rows[i].data[8];\\r\\n\\t\\r\\n\\t\\t\\r\\n\\t}\\t\\t\\r\\n}\"]}},{\"targetRef\":\"1fb81ce1-0bdd-44d8-871b-90463c6d4a01\",\"extensionElements\":{\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Search Result\",\"declaredType\":\"sequenceFlow\",\"id\":\"c0141879-dcac-4f8e-8f9a-af667140ae04\",\"sourceRef\":\"16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a\"},{\"targetRef\":\"50fcfe94-f6eb-45fa-8fbe-caf3a0d51513\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"9d817e86-3fa0-4fa6-8a4c-99f02bd83a9c\",\"sourceRef\":\"1fb81ce1-0bdd-44d8-871b-90463c6d4a01\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ef8d60fc-8a10-456d-808f-6f8a8d34554f\"},{\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"parameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.64f9b234-6cfa-42fd-8de2-bcabe3ead1a5\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"results\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.1a2f26af-5d72-489d-8ec7-afd3fe504c1b\"},{\"targetRef\":\"16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To sql execute statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"4ad4d4e9-f817-43e8-804c-19e5e254dcb4\",\"sourceRef\":\"9e9f630b-ab8f-439d-8af5-26bf31e1fde1\"},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1295,\"y\":82,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Script Task\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"4ce60581-fd62-475a-8b5b-b815c28c8709\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\n\\r\\nselect REQUESTNO, REQUESTNATURE, REQUESTTYPE  , REQUESTDATE ,REQUESTSTATE ,REQUESTSTATUS ,PARENTREQUESTNO ,FCCONTRACTNO , CONTRACTSTAGE\\r\\nEXPORTPURPOSE , PAYMENTTERMS, PRODUCTCATEGORY , COMMODITYDESCRIPTION ,CIF , CUSTOMERNAME , BASEDATE , VALUEDATE, TENORDAYS, TRANSITDAYS\\r\\nCONTRACTMATURITYDATE , USERREF , PRODUCTCODE , PRODUCTDESC ,STAGE , SOURCEREF , TRADEFOREFNO , DISCOUNT , EXTRACHARGES ,OURCHARGES , AMOUNTSIGHT,\\r\\nAMOUNTDEFNOAVALIZATION , AMOUNTDEFAVALIZATION ,COLLECTABLEAMOUNT , OUTSTANDINGAMOUNT , MATURITYDATE , MATURITYDAYS ,FINANCEAPPROVALNO , HUBCODE,HUBNAME\\r\\nLIQAMOUNT , LIQCURRENCY , COLLECTIONCURRENCY , STANDARDEXRATE , NEGOTIATEDEXRATE ,ALLOCATEDAMOUNT , TOTALALLOCATEDAMOUNT ,TRANSACTIONREFNO\\r\\nCBECLASSIFICATION , SHIPMENTMETHOD , SHIPMENTDATE , REVERSALREASON , CLOSUREREASON ,IMPORTERNAME , IMPORTERCOUNTRY , IMPORTERADDRESS \\r\\nIMPORTERPHONENO ,IMPORTERBANK ,BICCODE,IBANACCOUNT , BANKCOUNTRY , BANKADDRESS , BANKPHONENO , COLLECTINGBANKREF ,INVOICENO ,INVOICEDATE,\\r\\nBILLOFLADINGREF , BILLOFLADINGDATE\\r\\nfrom ODC_REQUESTINFO \\r\\nInner join ODC_INVOICE on ODC_REQUESTINFO.ID = ODC_INVOICE.REQUESRID\\r\\nInner join ODC_BILLS   on ODC_REQUESTINFO.ID = ODC_BILLS.REQUESRID\\r\\n\\r\\n\\t               \"]}},{\"parallelMultiple\":false,\"outgoing\":[\"8c80cc95-57df-4bd4-8753-2338dfc2af9e\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"8d606692-d03f-4820-8f7a-28242287f77b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"7d55fdc5-4c1a-4d0e-8e85-4eff82d7d300\",\"otherAttributes\":{\"eventImplId\":\"48528b08-7ccb-4b6c-84ac-b840077842de\"}}],\"attachedToRef\":\"1fb81ce1-0bdd-44d8-871b-90463c6d4a01\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":685,\"y\":205,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"e316c1e1-ca86-425d-8922-d5414d23b61d\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"f9d6bdf2-713c-49e5-8c4b-3f29fe5f1726\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"5a463be2-7aaa-4690-8626-8dcd01855f7a\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"4e9167c7-ad0a-481a-81f4-192458b3c708\",\"otherAttributes\":{\"eventImplId\":\"81812b51-d47f-441a-8dfd-227059d781c0\"}}],\"attachedToRef\":\"16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":535,\"y\":205,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"c1cef9eb-6824-45e0-83b0-78d468e6b586\",\"outputSet\":{}},{\"targetRef\":\"2d7c4da2-2dbd-460f-8b07-c737dcec14d4\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"f9d6bdf2-713c-49e5-8c4b-3f29fe5f1726\",\"sourceRef\":\"c1cef9eb-6824-45e0-83b0-78d468e6b586\"},{\"targetRef\":\"2d7c4da2-2dbd-460f-8b07-c737dcec14d4\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"8c80cc95-57df-4bd4-8753-2338dfc2af9e\",\"sourceRef\":\"e316c1e1-ca86-425d-8922-d5414d23b61d\"},{\"startQuantity\":1,\"outgoing\":[\"edc80957-a79c-4321-8fd8-bd8ba51b351a\"],\"incoming\":[\"f9d6bdf2-713c-49e5-8c4b-3f29fe5f1726\",\"8c80cc95-57df-4bd4-8753-2338dfc2af9e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":649,\"y\":291,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Error Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Retrieve Search criteria Result\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2d7c4da2-2dbd-460f-8b07-c737dcec14d4\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"50fcfe94-f6eb-45fa-8fbe-caf3a0d51513\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"edc80957-a79c-4321-8fd8-bd8ba51b351a\",\"sourceRef\":\"2d7c4da2-2dbd-460f-8b07-c737dcec14d4\"}],\"laneSet\":[{\"id\":\"1e1ab4b7-9dfa-4d9e-8278-faa3770da271\",\"lane\":[{\"flowNodeRef\":[\"9190b53a-667a-400e-81a7-496c5b72eef9\",\"50fcfe94-f6eb-45fa-8fbe-caf3a0d51513\",\"9e9f630b-ab8f-439d-8af5-26bf31e1fde1\",\"16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a\",\"1fb81ce1-0bdd-44d8-871b-90463c6d4a01\",\"4ce60581-fd62-475a-8b5b-b815c28c8709\",\"e316c1e1-ca86-425d-8922-d5414d23b61d\",\"c1cef9eb-6824-45e0-83b0-78d468e6b586\",\"2d7c4da2-2dbd-460f-8b07-c737dcec14d4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"67e09385-2b81-4676-8c53-4f60f09df84e\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"retrieve Search Criteria Result\",\"declaredType\":\"process\",\"id\":\"1.3b63776b-bc54-455d-abf9-c2a1997a7c42\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.76e102f0-ce5e-45ff-b7b0-32b0993a6588\",\"name\":\"searchResult\",\"isCollection\":true,\"id\":\"2055.4bdb7b5c-3648-47d4-8377-4a1ded64d950\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.6ab22aed-66f8-4c6c-882f-c7e894fe2414\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.0ac53667-a1c4-4086-b17a-f78b72a6299a\",\"epvProcessLinkId\":\"2468c863-2e38-41a6-850e-c0deeb373b40\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.e8741b9f-e4ba-4178-8cb1-cba03733201e\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.4bdb7b5c-3648-47d4-8377-4a1ded64d950\",\"2055.6ab22aed-66f8-4c6c-882f-c7e894fe2414\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\"}]},\"itemSubjectRef\":\"itm.12.4e08a264-0cfe-41ef-b4b7-74959345f193\",\"name\":\"searchCriteria\",\"isCollection\":false,\"id\":\"2055.e8741b9f-e4ba-4178-8cb1-cba03733201e\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "searchCriteria", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e8741b9f-e4ba-4178-8cb1-cba03733201e", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "parameterType": "1", "isArrayOf": "false", "classId": "/12.4e08a264-0cfe-41ef-b4b7-74959345f193", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3acb7884-4cab-4efa-84b0-21ac6eb2454d", "versionId": "1d1ffe11-2c04-4c48-8435-f146c4469780"}, {"name": "searchResult", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4bdb7b5c-3648-47d4-8377-4a1ded64d950", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "parameterType": "2", "isArrayOf": "true", "classId": "/12.76e102f0-ce5e-45ff-b7b0-32b0993a6588", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "c81d7467-235d-46b2-a4f2-a085a3182155", "versionId": "ab56636f-1445-4358-8cab-8a55982a95d4"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6ab22aed-66f8-4c6c-882f-c7e894fe2414", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f9f1eb27-8c55-4a63-ba87-38f99a09ac1d", "versionId": "6268f520-68b1-4271-9de9-c69deb4bf8d5"}], "processVariable": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ef8d60fc-8a10-456d-808f-6f8a8d34554f", "description": {"isNull": "true"}, "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a0abb9ea-773c-473d-9bbf-836f0cd7824e", "versionId": "ca60b543-a58f-4a19-9063-2059f29400ba"}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.64f9b234-6cfa-42fd-8de2-bcabe3ead1a5", "description": {"isNull": "true"}, "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b5ac5e8d-230f-44a3-8b49-f083a20b8701", "versionId": "4c38713c-79ac-4dd1-ac3c-40291c1ed7cf"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1a2f26af-5d72-489d-8ec7-afd3fe504c1b", "description": {"isNull": "true"}, "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "28041e5f-0907-483b-a6db-59dedfb050db", "versionId": "5203a41c-d3d5-429e-b59a-be222436f93b"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "Search Result", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.4ab7aa8f-20fd-4e0c-a0fe-2d7a7401f22b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:732f", "versionId": "4a4206b2-83d4-4fb4-afc2-93e3a883a036", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "147", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6ab1", "errorHandlerItemId": "2025.2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.4ab7aa8f-20fd-4e0c-a0fe-2d7a7401f22b", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.searchResult = new tw.object.listOf.searchResult();\r\r\n\r\r\nif(tw.local.results.listLength > 0)\r\r\n{\r\r\n\tfor( var i=0 ; i < tw.local.results[0].rows.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.searchResult[i] = new tw.object.searchResult();\r\r\n\t\ttw.local.searchResult[i].customerNumber  \t    = tw.local.results[0].rows[i].data[0];\r\r\n\t\ttw.local.searchResult[i].customerName    \t    = tw.local.results[0].rows[i].data[1];\r\r\n\t\ttw.local.searchResult[i].requestType          = tw.local.results[0].rows[i].data[2];\r\r\n\t\ttw.local.searchResult[i].requestNumber        = tw.local.results[0].rows[i].data[3];\r\r\n\t\ttw.local.searchResult[i].requestStatus        = tw.local.results[0].rows[i].data[4];\r\r\n\t\ttw.local.searchResult[i].requestSubstatus     = tw.local.results[0].rows[i].data[5];\r\r\n\t\ttw.local.searchResult[i].requestDate          = tw.local.results[0].rows[i].data[6];\r\r\n\t\t\r\r\n\t\ttw.local.searchResult[i].currentActivityName  = tw.local.results[0].rows[i].data[7];\r\r\n\t\ttw.local.searchResult[i].bpmInstanceID        = tw.local.results[0].rows[i].data[8];\r\r\n\t\r\r\n\t\t\r\r\n\t}\t\t\r\r\n}", "isRule": "false", "guid": "6f902f74-f3f5-464e-ba2d-4f33a97ad0da", "versionId": "409457da-be6e-4d45-aaba-a0c97c55fc79"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4ce60581-fd62-475a-8b5b-b815c28c8709", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "Script Task", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.5fc64b68-d021-4540-b374-315d8f8d5156", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f1e1d89a1bc2b9f8:-58282b66:18ab2491716:-d64", "versionId": "4b952cd8-8d69-4fc7-974c-bd4595b6b9ee", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1295", "y": "82", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.5fc64b68-d021-4540-b374-315d8f8d5156", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\n\r\r\nselect REQUE<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  , R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,REQUESTSTAT<PERSON> ,REQUESTSTATUS ,PARENT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> , CONTRACTSTAGE\r\r\nEXPORTPURPOSE , <PERSON><PERSON><PERSON><PERSON>TER<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ORY , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,CIF , CUSTOMERNA<PERSON> , BASE<PERSON>TE , VA<PERSON>UE<PERSON><PERSON>, TENORDAY<PERSON>, TRA<PERSON>ITDAYS\r\r\nCONTRACTMATU<PERSON>TYDATE , USERREF , PRODUCTCODE , PRODUCTDESC ,STAGE , SOURCEREF , TRADEFOREFNO , DIS<PERSON>UNT , EXTRACHARGES ,<PERSON>URCHARGES , AMOUN<PERSON><PERSON>HT,\r\r\nAMOUN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>AT<PERSON> , AMOUN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ZAT<PERSON> ,CO<PERSON>ECTABLEAMOUNT , <PERSON>UTST<PERSON>DINGAMOUNT , <PERSON>TURITYDATE , <PERSON><PERSON>RI<PERSON>DAYS ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NO , H<PERSON>BCODE,H<PERSON><PERSON><PERSON><PERSON>\r\r\nLIQAMOUNT , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NC<PERSON> , <PERSON>AN<PERSON><PERSON>EXRA<PERSON> , NEGOTIATEDEXRA<PERSON> ,AL<PERSON><PERSON><PERSON>EDAMOUN<PERSON> , TO<PERSON><PERSON><PERSON><PERSON>ATEDAMOUNT ,TRANSACTIONREFNO\r\r\nCBECLASSIFICATION , SHIPMENTMETHOD , SHIPMENTDATE , REVERSALREASON , CLOSUREREASON ,IMPORTERNAME , IMPORTERCOUNTRY , IMPORTERADDRESS \r\r\nIMPORTERPHONENO ,IMPORTERBANK ,BICCODE,IBANACCOUNT , BANKCOUNTRY , BANKADDRESS , BANKPHONENO , COLLECTINGBANKREF ,INVOICENO ,INVOICEDATE,\r\r\nBILLOFLADINGREF , BILLOFLADINGDATE\r\r\nfrom ODC_REQUESTINFO \r\r\nInner join ODC_INVOICE on ODC_REQUESTINFO.ID = ODC_INVOICE.REQUESRID\r\r\nInner join ODC_BILLS   on ODC_REQUESTINFO.ID = ODC_BILLS.REQUESRID\r\r\n\r\r\n\t               ", "isRule": "false", "guid": "5b9d1ec6-cdf1-4ebd-816f-3b35fa344a61", "versionId": "f707eb7d-d5c1-42dc-aad8-e4d5270561b2"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "Erro<PERSON>", "tWComponentName": "SubProcess", "tWComponentId": "3012.1fa51652-d87b-4ddd-bd25-00ee5d944222", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6ab1", "versionId": "721bca57-694a-4d37-a16d-281b4863b024", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "649", "y": "291", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.1fa51652-d87b-4ddd-bd25-00ee5d944222", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "63089fad-cbe6-4f44-a9d7-31473d4ee015", "versionId": "37a6aefe-2c0b-4d03-b3e2-368e4b5e41d0", "parameterMapping": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.2df09fa5-7e79-44aa-9571-edd0b4af7ccf", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.1fa51652-d87b-4ddd-bd25-00ee5d944222", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "1e9a138a-9258-4dc6-955b-60b3e64ff3d2", "versionId": "b602f59a-7091-44f6-9945-7ed74a92858a", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4db449b4-0e3a-4e1a-90f0-47675efa76cf", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.1fa51652-d87b-4ddd-bd25-00ee5d944222", "useDefault": "false", "value": "\"Retrieve Search criteria Result\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "4541479a-6384-486d-a0c9-99a32a812617", "versionId": "fa1dee0a-ebe6-4418-a066-048bf76c56e4", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "sql execute statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.c3612119-34ce-4f90-bf99-63945e82f020", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:7330", "versionId": "a817fb47-b91a-408b-9e61-f483244d9edf", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "500", "y": "147", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6ab1", "errorHandlerItemId": "2025.2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.c3612119-34ce-4f90-bf99-63945e82f020", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "efe746ff-cbae-47a3-90bd-5a891c4fe018", "versionId": "388d348f-9787-4a1a-9769-afc40d1bc4e5", "parameterMapping": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.728e837f-913b-49b7-81ae-221d2deafa08", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.c3612119-34ce-4f90-bf99-63945e82f020", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "3dff9fbd-2340-49e8-9860-b1e6ba49e39f", "versionId": "1bab135c-6e84-4d0c-bf27-a883255b67c8", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d1592f58-cafc-4877-a441-7ae8a1bb8de4", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.c3612119-34ce-4f90-bf99-63945e82f020", "useDefault": "false", "value": "tw.local.parameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "ad3e5a7e-0444-4f15-bdd9-2773cb93dfbf", "versionId": "231bab83-4b2d-49aa-b5d4-da6355df5919", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.fdeaff65-faf7-4ce4-9c45-e2071cff849c", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.c3612119-34ce-4f90-bf99-63945e82f020", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "e3ffc150-30e1-4023-80c7-acf84cb5d3de", "versionId": "2b4b49da-4c4b-4436-b387-f51f2abd94a9", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.07a48b79-9d09-4349-8c05-f752e92c07a9", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.c3612119-34ce-4f90-bf99-63945e82f020", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "5ae19bca-0b16-4507-9152-6445b81afd20", "versionId": "7fe619d5-edc7-4685-b46e-fd63b729c6ff", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.09ee984a-231a-49c1-b05a-d6c8bf12a3bc", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.c3612119-34ce-4f90-bf99-63945e82f020", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "b9f8e430-fb9d-4c86-97cd-6cde1f09901d", "versionId": "8b785a29-a47f-477f-a2f4-29a5a55cc803", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "sql script", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.acdcab21-6eb1-4bd0-809c-af9cb9ce680b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:7329", "versionId": "beae5d31-**************-9a2acba59cd3", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.d5c5e963-1aad-4afc-8399-79c4743693ce", "processItemId": "2025.9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "location": "1", "script": {"isNull": "true"}, "guid": "396f78a8-813d-4a90-b548-9b47f7ac2e04", "versionId": "4f3df46c-f0e4-43e0-8d5e-0b39287dc28c"}, "layoutData": {"x": "349", "y": "147", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.acdcab21-6eb1-4bd0-809c-af9cb9ce680b", "scriptTypeId": "2", "isActive": "true", "script": "if(tw.local.searchCriteria.CIF != null && tw.local.searchCriteria.CIF != \"\"  ) \r\r\n{\r\r\n\ttw.local.sql = \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t                +\"where cif = '\"+tw.local.searchCriteria.CIF+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.requestType != null && tw.local.searchCriteria.requestType != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus, REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t                +\"where REQUESTTYPE = '\"+tw.local.searchCriteria.requestType+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.BPMRequestNo != null && tw.local.searchCriteria.BPMRequestNo != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus ,REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t                +\"where BPMInstanceNumber = '\"+tw.local.searchCriteria.BPMRequestNo+\"'  order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.requestDate != null ) \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t               +\"where REQUESTDATE = '\"+tw.local.searchCriteria.requestDate+\"'  order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.requestState != null && tw.local.searchCriteria.requestState != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t               +\"where REQUESTSTATUS = '\"+tw.local.searchCriteria.requestState+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.invoiceNo != null && tw.local.searchCriteria.invoiceNo != \"\") \r\r\n{\r\r\n\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t\t\t    +\"Inner join ODC_INVOICE on ODC_REQUESTINFO.ID = ODC_INVOICE.REQUESRID where INVOICENO = '\"+tw.local.searchCriteria.invoiceNo+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.billOfLading != null && tw.local.searchCriteria.billOfLading != \"\") \r\r\n{\r\r\n\ttw.local.sql = \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus ,REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO  Inner join ODC_BILLS \"+\r\r\n\t\t    \"on ODC_REQUESTINFO.ID = ODC_BILLS.REQUESRID where BILLOFLADINGREF = '\"+tw.local.searchCriteria.billOfLading+\"' order by REQUESTNO ;\"\r\r\n}\r\r\n\r\r\nelse if(tw.local.searchCriteria.tradeFoRefrenceNo != null && tw.local.searchCriteria.tradeFoRefrenceNo != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t+\"where TRADEFOREFNO = '\"+tw.local.searchCriteria.tradeFoRefrenceNo+\"' order by REQUESTNO ;\"\r\r\n}\r\r\n", "isRule": "false", "guid": "2e98b331-1f81-447b-9c32-70fffa985a1f", "versionId": "73c61159-c62b-4f86-9546-942a2efe5a11"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.50fcfe94-f6eb-45fa-8fbe-caf3a0d51513", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.dc09bbe3-cc8f-48cd-a8fc-c342ec719e41", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:731f", "versionId": "e2ccb7ff-c356-47b4-a7ac-0eb39165260c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "794", "y": "170", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.dc09bbe3-cc8f-48cd-a8fc-c342ec719e41", "haltProcess": "false", "guid": "f33c8996-1c7c-4e8b-9291-a4c94b8d8b07", "versionId": "ae9b3c8f-9dcd-496a-a67f-b0ec9d7b51c7"}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.31964641-4cf3-4ca0-a052-ce864e86e1fc", "epvId": "/21.0ac53667-a1c4-4086-b17a-f78b72a6299a", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "guid": "f137e485-5998-4547-8d12-4bb1a2a82bde", "versionId": "6b58a173-455c-4a14-8b63-77b2fa7222ec"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "280", "y": "170", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "retrieve Search Criteria Result", "id": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.0ac53667-a1c4-4086-b17a-f78b72a6299a", "epvProcessLinkId": "2468c863-2e38-41a6-850e-c0deeb373b40"}}}, "ns16:dataInput": {"name": "searchCriteria", "itemSubjectRef": "itm.12.4e08a264-0cfe-41ef-b4b7-74959345f193", "isCollection": "false", "id": "2055.e8741b9f-e4ba-4178-8cb1-cba03733201e", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, "ns16:dataOutput": [{"name": "searchResult", "itemSubjectRef": "itm.12.76e102f0-ce5e-45ff-b7b0-32b0993a6588", "isCollection": "true", "id": "2055.4bdb7b5c-3648-47d4-8377-4a1ded64d950"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.6ab22aed-66f8-4c6c-882f-c7e894fe2414"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.e8741b9f-e4ba-4178-8cb1-cba03733201e"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.4bdb7b5c-3648-47d4-8377-4a1ded64d950", "2055.6ab22aed-66f8-4c6c-882f-c7e894fe2414"]}}, "ns16:laneSet": {"id": "1e1ab4b7-9dfa-4d9e-8278-faa3770da271", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "67e09385-2b81-4676-8c53-4f60f09df84e", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["9190b53a-667a-400e-81a7-496c5b72eef9", "50fcfe94-f6eb-45fa-8fbe-caf3a0d51513", "9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "4ce60581-fd62-475a-8b5b-b815c28c8709", "e316c1e1-ca86-425d-8922-d5414d23b61d", "c1cef9eb-6824-45e0-83b0-78d468e6b586", "2d7c4da2-2dbd-460f-8b07-c737dcec14d4"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "9190b53a-667a-400e-81a7-496c5b72eef9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "280", "y": "170", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.b44bf196-2cc8-4670-80e0-6710ea0d6bac"}, "ns16:endEvent": {"name": "End", "id": "50fcfe94-f6eb-45fa-8fbe-caf3a0d51513", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "794", "y": "170", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:74bf94ca0bd4ad02:bc03eb4:18aad5d8054:731f"}, "ns16:incoming": ["9d817e86-3fa0-4fa6-8a4c-99f02bd83a9c", "edc80957-a79c-4321-8fd8-bd8ba51b351a"]}, "ns16:sequenceFlow": [{"sourceRef": "9190b53a-667a-400e-81a7-496c5b72eef9", "targetRef": "9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "name": "To End", "id": "2027.b44bf196-2cc8-4670-80e0-6710ea0d6bac", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "targetRef": "1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "name": "To Search Result", "id": "c0141879-dcac-4f8e-8f9a-af667140ae04", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "targetRef": "50fcfe94-f6eb-45fa-8fbe-caf3a0d51513", "name": "To End", "id": "9d817e86-3fa0-4fa6-8a4c-99f02bd83a9c", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "targetRef": "16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "name": "To sql execute statement", "id": "4ad4d4e9-f817-43e8-804c-19e5e254dcb4", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "c1cef9eb-6824-45e0-83b0-78d468e6b586", "targetRef": "2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "name": "To <PERSON><PERSON>r <PERSON>", "id": "f9d6bdf2-713c-49e5-8c4b-3f29fe5f1726", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "e316c1e1-ca86-425d-8922-d5414d23b61d", "targetRef": "2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "name": "To <PERSON><PERSON>r <PERSON>", "id": "8c80cc95-57df-4bd4-8753-2338dfc2af9e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "targetRef": "50fcfe94-f6eb-45fa-8fbe-caf3a0d51513", "name": "To End", "id": "edc80957-a79c-4321-8fd8-bd8ba51b351a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "sql script", "id": "9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "349", "y": "147", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.b44bf196-2cc8-4670-80e0-6710ea0d6bac", "ns16:outgoing": "4ad4d4e9-f817-43e8-804c-19e5e254dcb4", "ns16:script": "if(tw.local.searchCriteria.CIF != null && tw.local.searchCriteria.CIF != \"\"  ) \r\r\n{\r\r\n\ttw.local.sql = \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t                +\"where cif = '\"+tw.local.searchCriteria.CIF+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.requestType != null && tw.local.searchCriteria.requestType != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus, REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t                +\"where REQUESTTYPE = '\"+tw.local.searchCriteria.requestType+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.BPMRequestNo != null && tw.local.searchCriteria.BPMRequestNo != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus ,REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t                +\"where BPMInstanceNumber = '\"+tw.local.searchCriteria.BPMRequestNo+\"'  order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.requestDate != null ) \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t               +\"where REQUESTDATE = '\"+tw.local.searchCriteria.requestDate+\"'  order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.requestState != null && tw.local.searchCriteria.requestState != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS ,substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t               +\"where REQUESTSTATUS = '\"+tw.local.searchCriteria.requestState+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.invoiceNo != null && tw.local.searchCriteria.invoiceNo != \"\") \r\r\n{\r\r\n\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t\t\t    +\"Inner join ODC_INVOICE on ODC_REQUESTINFO.ID = ODC_INVOICE.REQUESRID where INVOICENO = '\"+tw.local.searchCriteria.invoiceNo+\"' order by REQUESTNO ;\"\r\r\n}\r\r\nelse if(tw.local.searchCriteria.billOfLading != null && tw.local.searchCriteria.billOfLading != \"\") \r\r\n{\r\r\n\ttw.local.sql = \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus ,REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO  Inner join ODC_BILLS \"+\r\r\n\t\t    \"on ODC_REQUESTINFO.ID = ODC_BILLS.REQUESRID where BILLOFLADINGREF = '\"+tw.local.searchCriteria.billOfLading+\"' order by REQUESTNO ;\"\r\r\n}\r\r\n\r\r\nelse if(tw.local.searchCriteria.tradeFoRefrenceNo != null && tw.local.searchCriteria.tradeFoRefrenceNo != \"\") \r\r\n{\r\r\n\ttw.local.sql =  \"select CIF, CUSTOMERNAME, REQUESTTYPE,REQUESTNO ,REQUESTSTATUS , substatus , REQUESTDATE , currentStepName , BPMInstanceNumber from ODC_REQUESTINFO   \"\r\r\n\t+\"where TRADEFOREFNO = '\"+tw.local.searchCriteria.tradeFoRefrenceNo+\"' order by REQUESTNO ;\"\r\r\n}\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Search Result", "id": "1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "147", "width": "95", "height": "70"}}, "ns16:incoming": "c0141879-dcac-4f8e-8f9a-af667140ae04", "ns16:outgoing": "9d817e86-3fa0-4fa6-8a4c-99f02bd83a9c", "ns16:script": "tw.local.searchResult = new tw.object.listOf.searchResult();\r\r\n\r\r\nif(tw.local.results.listLength > 0)\r\r\n{\r\r\n\tfor( var i=0 ; i < tw.local.results[0].rows.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.searchResult[i] = new tw.object.searchResult();\r\r\n\t\ttw.local.searchResult[i].customerNumber  \t    = tw.local.results[0].rows[i].data[0];\r\r\n\t\ttw.local.searchResult[i].customerName    \t    = tw.local.results[0].rows[i].data[1];\r\r\n\t\ttw.local.searchResult[i].requestType          = tw.local.results[0].rows[i].data[2];\r\r\n\t\ttw.local.searchResult[i].requestNumber        = tw.local.results[0].rows[i].data[3];\r\r\n\t\ttw.local.searchResult[i].requestStatus        = tw.local.results[0].rows[i].data[4];\r\r\n\t\ttw.local.searchResult[i].requestSubstatus     = tw.local.results[0].rows[i].data[5];\r\r\n\t\ttw.local.searchResult[i].requestDate          = tw.local.results[0].rows[i].data[6];\r\r\n\t\t\r\r\n\t\ttw.local.searchResult[i].currentActivityName  = tw.local.results[0].rows[i].data[7];\r\r\n\t\ttw.local.searchResult[i].bpmInstanceID        = tw.local.results[0].rows[i].data[8];\r\r\n\t\r\r\n\t\t\r\r\n\t}\t\t\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "Script Task", "id": "4ce60581-fd62-475a-8b5b-b815c28c8709", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1295", "y": "82", "width": "95", "height": "70"}}, "ns16:script": "\r\r\n\r\r\nselect REQUE<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>  , R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,REQUESTSTAT<PERSON> ,REQUESTSTATUS ,PARENT<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> , CONTRACTSTAGE\r\r\nEXPORTPURPOSE , <PERSON><PERSON><PERSON><PERSON>TER<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ORY , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,CIF , CUSTOMERNA<PERSON> , BASE<PERSON>TE , VA<PERSON>UE<PERSON><PERSON>, TENORDAY<PERSON>, TRA<PERSON>ITDAYS\r\r\nCONTRACTMATU<PERSON>TYDATE , USERREF , PRODUCTCODE , PRODUCTDESC ,STAGE , SOURCEREF , TRADEFOREFNO , DIS<PERSON>UNT , EXTRACHARGES ,<PERSON>URCHARGES , AMOUN<PERSON><PERSON>HT,\r\r\nAMOUN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>AT<PERSON> , AMOUN<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ZAT<PERSON> ,CO<PERSON>ECTABLEAMOUNT , <PERSON>UTST<PERSON>DINGAMOUNT , <PERSON>TURITYDATE , <PERSON><PERSON>RI<PERSON>DAYS ,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NO , H<PERSON>BCODE,H<PERSON><PERSON><PERSON><PERSON>\r\r\nLIQAMOUNT , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> , <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>NC<PERSON> , <PERSON>AN<PERSON><PERSON>EXRA<PERSON> , NEGOTIATEDEXRA<PERSON> ,AL<PERSON><PERSON><PERSON>EDAMOUN<PERSON> , TO<PERSON><PERSON><PERSON><PERSON>ATEDAMOUNT ,TRANSACTIONREFNO\r\r\nCBECLASSIFICATION , SHIPMENTMETHOD , SHIPMENTDATE , REVERSALREASON , CLOSUREREASON ,IMPORTERNAME , IMPORTERCOUNTRY , IMPORTERADDRESS \r\r\nIMPORTERPHONENO ,IMPORTERBANK ,BICCODE,IBANACCOUNT , BANKCOUNTRY , BANKADDRESS , BANKPHONENO , COLLECTINGBANKREF ,INVOICENO ,INVOICEDATE,\r\r\nBILLOFLADINGREF , BILLOFLADINGDATE\r\r\nfrom ODC_REQUESTINFO \r\r\nInner join ODC_INVOICE on ODC_REQUESTINFO.ID = ODC_INVOICE.REQUESRID\r\r\nInner join ODC_BILLS   on ODC_REQUESTINFO.ID = ODC_BILLS.REQUESRID\r\r\n\r\r\n\t               "}], "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "sql execute statement", "id": "16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "500", "y": "147", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "4ad4d4e9-f817-43e8-804c-19e5e254dcb4", "ns16:outgoing": "c0141879-dcac-4f8e-8f9a-af667140ae04", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.parameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Erro<PERSON>", "id": "2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "649", "y": "291", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["f9d6bdf2-713c-49e5-8c4b-3f29fe5f1726", "8c80cc95-57df-4bd4-8753-2338dfc2af9e"], "ns16:outgoing": "edc80957-a79c-4321-8fd8-bd8ba51b351a", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Retrieve Search criteria Result\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.ef8d60fc-8a10-456d-808f-6f8a8d34554f"}, {"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "parameters", "id": "2056.64f9b234-6cfa-42fd-8de2-bcabe3ead1a5"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.1a2f26af-5d72-489d-8ec7-afd3fe504c1b"}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "parallelMultiple": "false", "name": "Error", "id": "e316c1e1-ca86-425d-8922-d5414d23b61d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "685", "y": "205", "width": "24", "height": "24"}}, "ns16:outgoing": "8c80cc95-57df-4bd4-8753-2338dfc2af9e", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "8d606692-d03f-4820-8f7a-28242287f77b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "7d55fdc5-4c1a-4d0e-8e85-4eff82d7d300", "eventImplId": "48528b08-7ccb-4b6c-84ac-b840077842de", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "parallelMultiple": "false", "name": "Error1", "id": "c1cef9eb-6824-45e0-83b0-78d468e6b586", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "535", "y": "205", "width": "24", "height": "24"}}, "ns16:outgoing": "f9d6bdf2-713c-49e5-8c4b-3f29fe5f1726", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "5a463be2-7aaa-4690-8626-8dcd01855f7a"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "4e9167c7-ad0a-481a-81f4-192458b3c708", "eventImplId": "81812b51-d47f-441a-8dfd-227059d781c0", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.edc80957-a79c-4321-8fd8-bd8ba51b351a", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.2d7c4da2-2dbd-460f-8b07-c737dcec14d4", "2025.2d7c4da2-2dbd-460f-8b07-c737dcec14d4"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.50fcfe94-f6eb-45fa-8fbe-caf3a0d51513", "2025.50fcfe94-f6eb-45fa-8fbe-caf3a0d51513"], "guid": "a549c88e-4ee4-44cc-b934-39d0357b0df1", "versionId": "3aa64510-0323-4cbb-8835-e621009abe1f", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To Search Result", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.c0141879-dcac-4f8e-8f9a-af667140ae04", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "2025.16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "2025.1fb81ce1-0bdd-44d8-871b-90463c6d4a01"], "guid": "f0e73bce-f25f-4a28-a6b4-a6dc98690296", "versionId": "4428f2ff-5598-47f1-8d7b-05bf27a3a27d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.9d817e86-3fa0-4fa6-8a4c-99f02bd83a9c", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.1fb81ce1-0bdd-44d8-871b-90463c6d4a01", "2025.1fb81ce1-0bdd-44d8-871b-90463c6d4a01"], "endStateId": "Out", "toProcessItemId": ["2025.50fcfe94-f6eb-45fa-8fbe-caf3a0d51513", "2025.50fcfe94-f6eb-45fa-8fbe-caf3a0d51513"], "guid": "0ce80cb6-c022-48a6-ad22-d9e23af989f9", "versionId": "9956566e-f496-4351-8281-ee908f6ce3a8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To sql execute statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4ad4d4e9-f817-43e8-804c-19e5e254dcb4", "processId": "1.3b63776b-bc54-455d-abf9-c2a1997a7c42", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.9e9f630b-ab8f-439d-8af5-26bf31e1fde1", "2025.9e9f630b-ab8f-439d-8af5-26bf31e1fde1"], "endStateId": "Out", "toProcessItemId": ["2025.16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a", "2025.16f6be13-2fe3-42a7-8a7f-0ec6f8fba11a"], "guid": "081a3fad-0c3b-4ce5-a98f-a8b62f06f90c", "versionId": "fc475492-6ad0-48f4-b880-13618b856dbd", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}