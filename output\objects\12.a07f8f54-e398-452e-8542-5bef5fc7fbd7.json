{"id": "12.a07f8f54-e398-452e-8542-5bef5fc7fbd7", "versionId": "e4ccea8c-6fec-408d-9bc7-ab9c175cd386", "name": "userConditions", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.a07f8f54-e398-452e-8542-5bef5fc7fbd7", "name": "userConditions", "lastModified": "1694976441869", "lastModifiedBy": "heba", "classId": "12.a07f8f54-e398-452e-8542-5bef5fc7fbd7", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.a07f8f54-e398-452e-8542-5bef5fc7fbd7", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/NBEODCR\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{}],\"shadow\":[false]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"role\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"role\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"requestNature\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"requestNature\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}}]},\"name\":\"userConditions\"}],\"id\":\"_12.a07f8f54-e398-452e-8542-5bef5fc7fbd7\"}", "description": {"isNull": "true"}, "guid": "guid:989374ae3827db3a:f13aa6c:18aa2e844f6:1ed3", "versionId": "e4ccea8c-6fec-408d-9bc7-ab9c175cd386", "definition": {"property": [{"name": "role", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "requestNature", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}], "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "userConditions", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}