<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.e0a673da-2e76-4a12-8896-d868bab63e83" name="Deployment Service Flow">
        <lastModified>1689672139581</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.e0a673da-2e76-4a12-8896-d868bab63e83</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.94a0fcd0-ea58-4341-88d4-017a6d51c2b7</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>13</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:2e93acfacc1269a3:3c8702fd:18968452cb5:-52db</guid>
        <versionId>6246d9e2-970f-4112-a5ec-ca82a319deaf</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:2e93acfacc1269a3:3c8702fd:18968452cb5:-52d8" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["8c565166-7a08-4544-86d5-57d9f62cab5f"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"1cd4c9d8-12d3-415d-a395-50f3b43fb64c"},{"incoming":["8c565166-7a08-4544-86d5-57d9f62cab5f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:2e93acfacc1269a3:3c8702fd:18968452cb5:-52dc"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"94a0fcd0-ea58-4341-88d4-017a6d51c2b7"},{"targetRef":"94a0fcd0-ea58-4341-88d4-017a6d51c2b7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"8c565166-7a08-4544-86d5-57d9f62cab5f","sourceRef":"1cd4c9d8-12d3-415d-a395-50f3b43fb64c"}],"laneSet":[{"id":"d979ad1f-ba07-4b3f-bba3-2a77cd6d7e9b","lane":[{"flowNodeRef":["1cd4c9d8-12d3-415d-a395-50f3b43fb64c","94a0fcd0-ea58-4341-88d4-017a6d51c2b7"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"5ba97282-f991-444f-bc93-9a39ea8a364a","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[false],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Deployment Service Flow","declaredType":"process","id":"1.e0a673da-2e76-4a12-8896-d868bab63e83","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"inputSet":[{}],"outputSet":[{}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"b9004306-cba4-487d-b58d-73ad4d32888e"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.94a0fcd0-ea58-4341-88d4-017a6d51c2b7</processItemId>
            <processId>1.e0a673da-2e76-4a12-8896-d868bab63e83</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.9816c7d3-7ef2-4b9a-8b35-faa3f4930c81</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2e93acfacc1269a3:3c8702fd:18968452cb5:-52dc</guid>
            <versionId>a13956ed-6a8e-429f-9eb7-570b235ebfae</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.9816c7d3-7ef2-4b9a-8b35-faa3f4930c81</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>139ccc6d-a0ad-44fb-9928-509edaeb82ed</guid>
                <versionId>1be89d91-2d16-4262-842e-0a9c725c9caf</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.94a0fcd0-ea58-4341-88d4-017a6d51c2b7</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="b9004306-cba4-487d-b58d-73ad4d32888e" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Deployment Service Flow" id="1.e0a673da-2e76-4a12-8896-d868bab63e83" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isAjaxExposed>false</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="d979ad1f-ba07-4b3f-bba3-2a77cd6d7e9b">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="5ba97282-f991-444f-bc93-9a39ea8a364a" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>1cd4c9d8-12d3-415d-a395-50f3b43fb64c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>94a0fcd0-ea58-4341-88d4-017a6d51c2b7</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="1cd4c9d8-12d3-415d-a395-50f3b43fb64c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8c565166-7a08-4544-86d5-57d9f62cab5f</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="94a0fcd0-ea58-4341-88d4-017a6d51c2b7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns3:endStateId>guid:2e93acfacc1269a3:3c8702fd:18968452cb5:-52dc</ns3:endStateId>
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8c565166-7a08-4544-86d5-57d9f62cab5f</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1cd4c9d8-12d3-415d-a395-50f3b43fb64c" targetRef="94a0fcd0-ea58-4341-88d4-017a6d51c2b7" name="To End" id="8c565166-7a08-4544-86d5-57d9f62cab5f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
    </process>
</teamworks>

