<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e" name="Get Exchange Rate New">
        <lastModified>1700297694642</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.211c5f8b-29dc-4b06-a513-872f476cd372</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>f2636200-7d86-473b-bad3-1850a30b0645</guid>
        <versionId>99590f81-1b9d-486b-ac13-6c3e800fdae6</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5e63" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.4f18113c-c547-46e1-b610-ddf890590806"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":70,"y":112,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"9edfe623-f27f-4a96-ac32-34faf9d95222"},{"incoming":["39467e80-23e8-4351-8f6d-9eeb091cec8b","1e3d5a73-eada-4596-8e42-c3f519785dfb"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":704,"y":111,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f19"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3"},{"targetRef":"211c5f8b-29dc-4b06-a513-872f476cd372","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Input","declaredType":"sequenceFlow","id":"2027.4f18113c-c547-46e1-b610-ddf890590806","sourceRef":"9edfe623-f27f-4a96-ac32-34faf9d95222"},{"startQuantity":1,"outgoing":["2fd51bfa-28f6-4623-b09b-5d80868bece6"],"incoming":["4b19d26c-7645-4d60-89a3-93c198e7e25c"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":347,"y":89,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Exchange Rate","dataInputAssociation":[{"targetRef":"2055.0427603a-263e-49f1-8ebc-12396435a8f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"1\""]}}]},{"targetRef":"2055.6acacd41-404d-41cd-8b73-a6a3f53de59c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.rateType"]}}]},{"targetRef":"2055.*************-4e4c-8a23-3a8758392285","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.rateSubType"]}}]},{"targetRef":"2055.f532fd4f-b514-4927-8cce-fd794f488e0d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.497359ed-14f4-4269-82b7-f09ee83dd3dc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.359d19b4-5948-47d2-814d-d3bbc031e650","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.293d7351-d3dc-48dc-896c-14e707fd7cba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"OUTwARD DOCUMENTARY COLLECTION\""]}}]},{"targetRef":"2055.94fc6012-982f-4612-82c9-77bc1256e0b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05","assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.fromCurrency"]}}]},{"targetRef":"2055.282350b1-2727-4aa1-8118-3d5c3316136a","assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.toCurrency"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"5dee0e8c-a1c1-4c11-8b25-281a75caca5e","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.122b436b-8719-4a3f-81ab-c7897c8c2554"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71"]}],"calledElement":"1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de"},{"targetRef":"72ccbb62-e998-4341-8b4f-73dfb2576a5f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Is Successful?","declaredType":"sequenceFlow","id":"2fd51bfa-28f6-4623-b09b-5d80868bece6","sourceRef":"5dee0e8c-a1c1-4c11-8b25-281a75caca5e"},{"startQuantity":1,"outgoing":["4b19d26c-7645-4d60-89a3-93c198e7e25c"],"incoming":["2027.4f18113c-c547-46e1-b610-ddf890590806"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":177,"y":89,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Set Input","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"211c5f8b-29dc-4b06-a513-872f476cd372","scriptFormat":"text\/x-javascript","script":{"content":["try{\r\n\tvar input = JSON.parse(tw.local.data);\r\n\r\n\ttw.local.fromCurrency = input.ccFrom;\r\n\ttw.local.toCurrency = input.ccTo;\r\n\ttw.local.rateSubType = input.sType;\r\n\ttw.local.rateType = input.type;\r\n} catch (err) {\r\n\ttw.local.errorMSG = err.message;\r\n\tthrow new Error(tw.local.errorMSG);\r\n\t\r\n}\r\n"]}},{"targetRef":"5dee0e8c-a1c1-4c11-8b25-281a75caca5e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Exchange Rate","declaredType":"sequenceFlow","id":"4b19d26c-7645-4d60-89a3-93c198e7e25c","sourceRef":"211c5f8b-29dc-4b06-a513-872f476cd372"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.ee1ec426-569c-4d3c-91d1-ae593b9d06eb"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.9b55737f-3bbc-4062-be43-73a6dc7d3596"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.71603788-e41a-4678-833d-1ca7315062a0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateSubType","isCollection":false,"declaredType":"dataObject","id":"2056.d1283647-9abd-4289-bc41-ccd9de181028"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateType","isCollection":false,"declaredType":"dataObject","id":"2056.9c5d4e96-5fc6-46e6-8c49-3f7d11079977"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.ce4b612b-6ac1-41fb-a2f3-5b25aee8c05a"},{"parallelMultiple":false,"outgoing":["ff2a1ccb-ca54-4ee5-8017-9b31ce7792fc"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"935a8e21-5a3e-47f0-9165-46f6753fd52d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"b30f9402-fdf2-442e-ad92-00eceffc0492","otherAttributes":{"eventImplId":"7a146d36-4584-494f-8157-c43d5958da7b"}}],"attachedToRef":"5dee0e8c-a1c1-4c11-8b25-281a75caca5e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":382,"y":147,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"ef2021c4-a83f-49e9-9ace-5492e4683206","outputSet":{}},{"targetRef":"f83a6c35-0e4d-4688-8634-5420a212e894","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"ff2a1ccb-ca54-4ee5-8017-9b31ce7792fc","sourceRef":"ef2021c4-a83f-49e9-9ace-5492e4683206"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceID","isCollection":false,"declaredType":"dataObject","id":"2056.ef26e3e4-08d8-4f55-9172-30cd8e2f8692"},{"startQuantity":1,"outgoing":["1e3d5a73-eada-4596-8e42-c3f519785dfb"],"incoming":["ff2a1ccb-ca54-4ee5-8017-9b31ce7792fc","36ffb331-695f-40d4-83c0-b55842d177b0"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":347,"y":234,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.serviceFlow.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"f83a6c35-0e4d-4688-8634-5420a212e894","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"outgoing":["39467e80-23e8-4351-8f6d-9eeb091cec8b","36ffb331-695f-40d4-83c0-b55842d177b0"],"incoming":["2fd51bfa-28f6-4623-b09b-5d80868bece6"],"default":"39467e80-23e8-4351-8f6d-9eeb091cec8b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":500,"y":108,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Is Successful?","declaredType":"exclusiveGateway","id":"72ccbb62-e998-4341-8b4f-73dfb2576a5f"},{"targetRef":"aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To isSuccessful","declaredType":"sequenceFlow","id":"39467e80-23e8-4351-8f6d-9eeb091cec8b","sourceRef":"72ccbb62-e998-4341-8b4f-73dfb2576a5f"},{"targetRef":"f83a6c35-0e4d-4688-8634-5420a212e894","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"36ffb331-695f-40d4-83c0-b55842d177b0","sourceRef":"72ccbb62-e998-4341-8b4f-73dfb2576a5f"},{"targetRef":"aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"1e3d5a73-eada-4596-8e42-c3f519785dfb","sourceRef":"f83a6c35-0e4d-4688-8634-5420a212e894"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fromCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.3dfb6d40-0764-4a2a-84c0-fe5cbf4f6159"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"toCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.87b2a4ef-80df-49fc-830c-bd18d5f6831a"}],"laneSet":[{"id":"3187097f-50b5-4f45-b88f-0510817a0f53","lane":[{"flowNodeRef":["9edfe623-f27f-4a96-ac32-34faf9d95222","aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3","5dee0e8c-a1c1-4c11-8b25-281a75caca5e","211c5f8b-29dc-4b06-a513-872f476cd372","ef2021c4-a83f-49e9-9ace-5492e4683206","f83a6c35-0e4d-4688-8634-5420a212e894","72ccbb62-e998-4341-8b4f-73dfb2576a5f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":404}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"f5148e83-48e1-4245-8204-5fe15d568717","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[false]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Exchange Rate New","declaredType":"process","id":"1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.742ed6e7-d699-46c8-b6ba-79ce22ef3c0e"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.bfa463ce-d7a4-4473-84fb-9daa9f3df2eb"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"'{\"ccFrom\" : \"USD\", \"ccTo\" : \"EGP\" , \"type\":\"TRANSFER\" , \"sType\":\"S\"}'\r\n"}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.1f9b9516-5223-47ba-88a0-0943eeb73fe7"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1f9b9516-5223-47ba-88a0-0943eeb73fe7</processParameterId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>'{"ccFrom" : "USD", "ccTo" : "EGP" , "type":"TRANSFER" , "sType":"S"}'&#xD;
</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e26b6f80-5d7a-492e-af53-85edcf7e8408</guid>
            <versionId>0d64a345-8b6b-4b73-8694-b55f59f86cde</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.742ed6e7-d699-46c8-b6ba-79ce22ef3c0e</processParameterId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>91ed266b-0bc1-4aee-87b9-942f405d94e5</guid>
            <versionId>702cff6b-57bc-4c63-afbe-4e9366de59d2</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bfa463ce-d7a4-4473-84fb-9daa9f3df2eb</processParameterId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4cf93111-6c53-4b6d-a6e7-0d185da0734f</guid>
            <versionId>c9f91704-cfa2-449b-83c5-62aad5be4811</versionId>
        </processParameter>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ee1ec426-569c-4d3c-91d1-ae593b9d06eb</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>72c983e0-248b-4113-9277-c9d2ed06bf38</guid>
            <versionId>51528a55-131e-4e8f-9638-fe3033bb1d94</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b55737f-3bbc-4062-be43-73a6dc7d3596</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d07d0b75-07c2-47f4-872a-5e305913a830</guid>
            <versionId>b0b3728e-87c4-40cb-bc1d-d3c518c495e7</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.71603788-e41a-4678-833d-1ca7315062a0</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0e3fe452-ca2d-4751-8009-554866dcaa42</guid>
            <versionId>cf488337-6073-4c30-ab8a-71de33f3a2a1</versionId>
        </processVariable>
        <processVariable name="rateSubType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d1283647-9abd-4289-bc41-ccd9de181028</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d88347af-f27e-46d8-8554-302f4a391d1c</guid>
            <versionId>e2b9610f-e7ea-4f0d-8f51-31c56f8239c2</versionId>
        </processVariable>
        <processVariable name="rateType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9c5d4e96-5fc6-46e6-8c49-3f7d11079977</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6a4126ab-5fd5-4249-8e4e-149bed70e688</guid>
            <versionId>44e42da9-0beb-4110-bca9-8a622c5c9613</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ce4b612b-6ac1-41fb-a2f3-5b25aee8c05a</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f8dedcee-cada-4e1b-ba76-c25e9adad957</guid>
            <versionId>5f6832f0-eee7-43e1-9b2d-76c4d2dc311f</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ef26e3e4-08d8-4f55-9172-30cd8e2f8692</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f75fb9fd-d089-47a4-a86f-ce04debacef0</guid>
            <versionId>cb669361-85f9-4cec-9556-fb5a5e9338b8</versionId>
        </processVariable>
        <processVariable name="fromCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3dfb6d40-0764-4a2a-84c0-fe5cbf4f6159</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>337d6cf4-5cfb-4ce9-8ef4-37032c17b909</guid>
            <versionId>b279bfcc-ecf4-45eb-ba32-20e37f6f65bf</versionId>
        </processVariable>
        <processVariable name="toCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.87b2a4ef-80df-49fc-830c-bd18d5f6831a</processVariableId>
            <description isNull="true" />
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>93ead5b2-43ad-472e-a138-2aaf20f6a91f</guid>
            <versionId>ef87dcfc-d0f6-4c61-ad54-035f5196510c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f83a6c35-0e4d-4688-8634-5420a212e894</processItemId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.01f3f1e8-2f7d-479d-a45d-5607794d3fb5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f05</guid>
            <versionId>0c7cdc25-80eb-4c4b-8e97-f8633ffa7a0a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="347" y="234">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.01f3f1e8-2f7d-479d-a45d-5607794d3fb5</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>c1ae63a8-dddc-4deb-8a45-c5db09fbb423</guid>
                <versionId>19f6d537-698e-4882-a0da-8242b94dbc82</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.57bdf314-7042-4832-a103-c4787bc03259</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.01f3f1e8-2f7d-479d-a45d-5607794d3fb5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c6d0fb11-c54c-4b14-a42d-329895eaaec3</guid>
                    <versionId>294ebfea-5e70-4851-bfff-9dfacec3aa3a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cc6e5bc2-db3b-4f0a-82c8-b04427041b5f</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.01f3f1e8-2f7d-479d-a45d-5607794d3fb5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8ffe672b-ff30-4c7e-bcaf-9af8570e8084</guid>
                    <versionId>49a3c05c-a708-4a5e-aaaa-5df07ff0b432</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f2a7bac9-c32e-4f64-8f7e-6254c8bdcd45</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.01f3f1e8-2f7d-479d-a45d-5607794d3fb5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.serviceFlow.name</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>205cc4d1-db9c-4d23-a1ad-1a2c5de31f40</guid>
                    <versionId>7834167d-1764-4f6f-abbb-2ef758606920</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5dee0e8c-a1c1-4c11-8b25-281a75caca5e</processItemId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <name>Get Exchange Rate</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.f83a6c35-0e4d-4688-8634-5420a212e894</errorHandlerItemId>
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f18</guid>
            <versionId>682add4e-6954-43fe-ba1a-b6d19fc10bd9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.d04b1825-c85a-4596-8469-4f71b16b7d47</processItemPrePostId>
                <processItemId>2025.5dee0e8c-a1c1-4c11-8b25-281a75caca5e</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>cd512a9c-3e54-4e24-910f-a44c6872a50b</guid>
                <versionId>fc7c4b5a-0fca-4989-903b-98fa7a77bc72</versionId>
            </processPrePosts>
            <layoutData x="347" y="89">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f05</errorHandlerItem>
                <errorHandlerItemId>2025.f83a6c35-0e4d-4688-8634-5420a212e894</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de</attachedProcessRef>
                <guid>000be1c3-aab3-4e6e-a1c0-cbe7e954edb8</guid>
                <versionId>ba5c1025-a5b9-4f74-a1a0-ab2eb1dca95f</versionId>
                <parameterMapping name="rateType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a7ebf0da-cd03-4e4e-8603-ca938d9a56c4</parameterMappingId>
                    <processParameterId>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.rateType</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1cab3964-8f66-4d40-9f1d-2f7dc1866009</guid>
                    <versionId>0cba9c2c-0741-4edf-b26d-48d8a6545fab</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.48f0eb33-76e3-4cf2-9e31-638386cd7269</parameterMappingId>
                    <processParameterId>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8566ec4e-f7e8-4ae5-9d46-c953e3db2ea4</guid>
                    <versionId>0dfafabb-4047-4ccc-a5d4-0301277892fd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.085ccc5c-d835-4051-810e-943fe82729a9</parameterMappingId>
                    <processParameterId>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"OUTwARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>809d2243-48c0-4600-8cac-730bad8be330</guid>
                    <versionId>174ce5e7-7c1f-4d73-af7e-b9c157d2baf3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.48e4fbfa-5d9e-4155-8248-b9bbb8f75a3d</parameterMappingId>
                    <processParameterId>2055.0427603a-263e-49f1-8ebc-12396435a8f9</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"1"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4573a919-60ff-440f-94ce-948b0319b0a8</guid>
                    <versionId>1a9af48e-83aa-41fe-b42b-39220119e4ba</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3f191ad8-24b0-4c26-8f45-d0703f95ed6d</parameterMappingId>
                    <processParameterId>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>de68efe6-304a-448c-a747-8cd1aabecf4a</guid>
                    <versionId>243b82dc-ddca-4af0-902c-51dcd7cf728e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency1">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fbf68a40-f63b-418e-a52d-4c5248208cf3</parameterMappingId>
                    <processParameterId>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.fromCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f4cec7b5-fa67-428e-8c19-8175a06cb6fb</guid>
                    <versionId>3ea5e17d-34aa-4348-9ef5-059b6c4eded7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0fd85609-7f80-49f0-b798-2ee180aa1d88</parameterMappingId>
                    <processParameterId>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>85537d4f-75d7-46c3-85f3-66f0c50ea306</guid>
                    <versionId>4214e8ff-c685-4bb9-875a-2a0ef6a19c32</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateSubType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c82e1a0d-0174-41fe-a7ad-84e6f1c9cb85</parameterMappingId>
                    <processParameterId>2055.*************-4e4c-8a23-3a8758392285</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.rateSubType</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6b07c122-36b3-4d7b-8787-b4ac5b04eb48</guid>
                    <versionId>50541d1e-4d69-440f-b62b-83b9a4d429d9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f69bc9ca-7d83-4183-ad86-b8dae435e756</parameterMappingId>
                    <processParameterId>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ad94c2bf-16f9-4fcf-9dca-51f8580e508b</guid>
                    <versionId>58f2ee43-361b-4bde-9184-6ce249348d70</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ef3ccb43-**************-089429844bf9</parameterMappingId>
                    <processParameterId>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8250c7cd-8a8c-475b-8654-9725fb78e3a3</guid>
                    <versionId>5df82516-e3a1-4f4c-8f09-bee494467b21</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e39c3573-28f1-4b88-963c-4a76a01d242c</parameterMappingId>
                    <processParameterId>2055.359d19b4-5948-47d2-814d-d3bbc031e650</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f6483bdc-b753-47ac-b47f-9717bb595e9a</guid>
                    <versionId>627ed241-6589-4590-bb18-3bda0e6e9892</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3d5c7190-5cc8-4652-8973-d50271fbcbe6</parameterMappingId>
                    <processParameterId>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>83ad5205-373f-4976-bc95-ca823e941149</guid>
                    <versionId>6dc0f4f9-6b78-4e0a-aee8-12c968959a4a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.*************-42d5-b535-d571c2ecc61e</parameterMappingId>
                    <processParameterId>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8c66e7a6-7d84-4116-b23d-d8ad37ba328a</guid>
                    <versionId>95e5725e-8742-4265-923c-75122323abfc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency2">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.68702586-d79b-4349-b073-c1a02276a5f8</parameterMappingId>
                    <processParameterId>2055.282350b1-2727-4aa1-8118-3d5c3316136a</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.toCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b3605cff-645e-4e1d-81ba-52eb1b36ac93</guid>
                    <versionId>bae72650-c1d1-4a0d-a3eb-a8596021f24b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amountResult">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.65c72d66-0114-4dd9-a989-372f4c948456</parameterMappingId>
                    <processParameterId>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</processParameterId>
                    <parameterMappingParentId>3012.eea9afac-7c9a-4dd6-ad65-3244107b3b9e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e97b037b-c96c-4deb-a14c-4e4d35e7a602</guid>
                    <versionId>e9bfb9e6-3f6e-4f3f-8416-512df5bab366</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3</processItemId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2ccd8503-6317-4440-8f87-dbc47602ca78</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f19</guid>
            <versionId>78ae5ff0-3c5b-4400-b3f0-ba2d08fe037a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="704" y="111">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2ccd8503-6317-4440-8f87-dbc47602ca78</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>1fd68de3-a286-48d1-9a3d-4181d741d377</guid>
                <versionId>8d1f7a18-99b6-4416-badb-14a36df8f06b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.211c5f8b-29dc-4b06-a513-872f476cd372</processItemId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <name>Set Input</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.85538e13-623c-415c-8164-3cc5c318de87</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f1a</guid>
            <versionId>8423e7c8-97bb-4dd7-96f1-416c662e2ad9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.26d69313-79e9-4cab-83fb-fe8194a7db65</processItemPrePostId>
                <processItemId>2025.211c5f8b-29dc-4b06-a513-872f476cd372</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>090cea6d-f3f4-49e8-8346-7483d6035b73</guid>
                <versionId>b4e9a7a8-8aea-4ca6-abe5-6962a7be7ec3</versionId>
            </processPrePosts>
            <layoutData x="177" y="89">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.85538e13-623c-415c-8164-3cc5c318de87</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>try{&#xD;
	var input = JSON.parse(tw.local.data);&#xD;
&#xD;
	tw.local.fromCurrency = input.ccFrom;&#xD;
	tw.local.toCurrency = input.ccTo;&#xD;
	tw.local.rateSubType = input.sType;&#xD;
	tw.local.rateType = input.type;&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>2eff3cd1-bcbf-4e25-969e-383bab443ec5</guid>
                <versionId>156ec3b2-5130-4eb9-9bd2-52d50015cd0a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.72ccbb62-e998-4341-8b4f-73dfb2576a5f</processItemId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <name>Is Successful?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.e2d0531f-584e-49d1-8554-d92f989e0053</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f04</guid>
            <versionId>8be17479-89b9-41f8-8f5f-f183dbdc773b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="500" y="108">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.e2d0531f-584e-49d1-8554-d92f989e0053</switchId>
                <guid>f1a2299d-02eb-4796-a0a2-5a28f716d11c</guid>
                <versionId>310f5c66-7f12-46ca-9b3a-da93ecaea144</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.65e0727c-b28e-4459-be19-7b0b8b8dbc14</switchConditionId>
                    <switchId>3013.e2d0531f-584e-49d1-8554-d92f989e0053</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5e64</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>00e385bf-97cc-4be8-988f-c508e7c0e1ce</guid>
                    <versionId>f392bfb0-6527-4668-8b69-48e32cdc0fa1</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.211c5f8b-29dc-4b06-a513-872f476cd372</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="70" y="112">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Exchange Rate New" id="1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>false</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.1f9b9516-5223-47ba-88a0-0943eeb73fe7">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">'{"ccFrom" : "USD", "ccTo" : "EGP" , "type":"TRANSFER" , "sType":"S"}'&#xD;
</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.742ed6e7-d699-46c8-b6ba-79ce22ef3c0e" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.bfa463ce-d7a4-4473-84fb-9daa9f3df2eb" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="3187097f-50b5-4f45-b88f-0510817a0f53">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="f5148e83-48e1-4245-8204-5fe15d568717" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="404" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>9edfe623-f27f-4a96-ac32-34faf9d95222</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5dee0e8c-a1c1-4c11-8b25-281a75caca5e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>211c5f8b-29dc-4b06-a513-872f476cd372</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ef2021c4-a83f-49e9-9ace-5492e4683206</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f83a6c35-0e4d-4688-8634-5420a212e894</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>72ccbb62-e998-4341-8b4f-73dfb2576a5f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="9edfe623-f27f-4a96-ac32-34faf9d95222">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="70" y="112" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.4f18113c-c547-46e1-b610-ddf890590806</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="704" y="111" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5f19</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>39467e80-23e8-4351-8f6d-9eeb091cec8b</ns16:incoming>
                        
                        
                        <ns16:incoming>1e3d5a73-eada-4596-8e42-c3f519785dfb</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9edfe623-f27f-4a96-ac32-34faf9d95222" targetRef="211c5f8b-29dc-4b06-a513-872f476cd372" name="To Set Input" id="2027.4f18113c-c547-46e1-b610-ddf890590806">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de" name="Get Exchange Rate" id="5dee0e8c-a1c1-4c11-8b25-281a75caca5e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="89" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4b19d26c-7645-4d60-89a3-93c198e7e25c</ns16:incoming>
                        
                        
                        <ns16:outgoing>2fd51bfa-28f6-4623-b09b-5d80868bece6</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0427603a-263e-49f1-8ebc-12396435a8f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"1"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.rateType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4e4c-8a23-3a8758392285</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.rateSubType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.359d19b4-5948-47d2-814d-d3bbc031e650</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"OUTwARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.fromCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.282350b1-2727-4aa1-8118-3d5c3316136a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.toCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="5dee0e8c-a1c1-4c11-8b25-281a75caca5e" targetRef="72ccbb62-e998-4341-8b4f-73dfb2576a5f" name="To Is Successful?" id="2fd51bfa-28f6-4623-b09b-5d80868bece6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Input" id="211c5f8b-29dc-4b06-a513-872f476cd372">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="177" y="89" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.4f18113c-c547-46e1-b610-ddf890590806</ns16:incoming>
                        
                        
                        <ns16:outgoing>4b19d26c-7645-4d60-89a3-93c198e7e25c</ns16:outgoing>
                        
                        
                        <ns16:script>try{&#xD;
	var input = JSON.parse(tw.local.data);&#xD;
&#xD;
	tw.local.fromCurrency = input.ccFrom;&#xD;
	tw.local.toCurrency = input.ccTo;&#xD;
	tw.local.rateSubType = input.sType;&#xD;
	tw.local.rateType = input.type;&#xD;
} catch (err) {&#xD;
	tw.local.errorMSG = err.message;&#xD;
	throw new Error(tw.local.errorMSG);&#xD;
	&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="211c5f8b-29dc-4b06-a513-872f476cd372" targetRef="5dee0e8c-a1c1-4c11-8b25-281a75caca5e" name="To Get Exchange Rate" id="4b19d26c-7645-4d60-89a3-93c198e7e25c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.ee1ec426-569c-4d3c-91d1-ae593b9d06eb" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.9b55737f-3bbc-4062-be43-73a6dc7d3596" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.71603788-e41a-4678-833d-1ca7315062a0" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateSubType" id="2056.d1283647-9abd-4289-bc41-ccd9de181028" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateType" id="2056.9c5d4e96-5fc6-46e6-8c49-3f7d11079977" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.ce4b612b-6ac1-41fb-a2f3-5b25aee8c05a" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="5dee0e8c-a1c1-4c11-8b25-281a75caca5e" parallelMultiple="false" name="Error1" id="ef2021c4-a83f-49e9-9ace-5492e4683206">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="382" y="147" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ff2a1ccb-ca54-4ee5-8017-9b31ce7792fc</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="935a8e21-5a3e-47f0-9165-46f6753fd52d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="b30f9402-fdf2-442e-ad92-00eceffc0492" eventImplId="7a146d36-4584-494f-8157-c43d5958da7b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="ef2021c4-a83f-49e9-9ace-5492e4683206" targetRef="f83a6c35-0e4d-4688-8634-5420a212e894" name="To End Event" id="ff2a1ccb-ca54-4ee5-8017-9b31ce7792fc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.ef26e3e4-08d8-4f55-9172-30cd8e2f8692" />
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exception Handling" id="f83a6c35-0e4d-4688-8634-5420a212e894">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="347" y="234" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ff2a1ccb-ca54-4ee5-8017-9b31ce7792fc</ns16:incoming>
                        
                        
                        <ns16:incoming>36ffb331-695f-40d4-83c0-b55842d177b0</ns16:incoming>
                        
                        
                        <ns16:outgoing>1e3d5a73-eada-4596-8e42-c3f519785dfb</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.serviceFlow.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:exclusiveGateway default="39467e80-23e8-4351-8f6d-9eeb091cec8b" name="Is Successful?" id="72ccbb62-e998-4341-8b4f-73dfb2576a5f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="500" y="108" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2fd51bfa-28f6-4623-b09b-5d80868bece6</ns16:incoming>
                        
                        
                        <ns16:outgoing>39467e80-23e8-4351-8f6d-9eeb091cec8b</ns16:outgoing>
                        
                        
                        <ns16:outgoing>36ffb331-695f-40d4-83c0-b55842d177b0</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="72ccbb62-e998-4341-8b4f-73dfb2576a5f" targetRef="aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3" name="To isSuccessful" id="39467e80-23e8-4351-8f6d-9eeb091cec8b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="72ccbb62-e998-4341-8b4f-73dfb2576a5f" targetRef="f83a6c35-0e4d-4688-8634-5420a212e894" name="To Exception Handling" id="36ffb331-695f-40d4-83c0-b55842d177b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f83a6c35-0e4d-4688-8634-5420a212e894" targetRef="aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3" name="To End" id="1e3d5a73-eada-4596-8e42-c3f519785dfb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="fromCurrency" id="2056.3dfb6d40-0764-4a2a-84c0-fe5cbf4f6159" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="toCurrency" id="2056.87b2a4ef-80df-49fc-830c-bd18d5f6831a" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Is Successful?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2fd51bfa-28f6-4623-b09b-5d80868bece6</processLinkId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5dee0e8c-a1c1-4c11-8b25-281a75caca5e</fromProcessItemId>
            <endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</endStateId>
            <toProcessItemId>2025.72ccbb62-e998-4341-8b4f-73dfb2576a5f</toProcessItemId>
            <guid>59737faa-5552-4689-af2b-1236fb669bd3</guid>
            <versionId>0d25aa20-7909-4e8f-90e3-bafbff229af6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5dee0e8c-a1c1-4c11-8b25-281a75caca5e</fromProcessItemId>
            <toProcessItemId>2025.72ccbb62-e998-4341-8b4f-73dfb2576a5f</toProcessItemId>
        </link>
        <link name="To Exception Handling">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.36ffb331-695f-40d4-83c0-b55842d177b0</processLinkId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.72ccbb62-e998-4341-8b4f-73dfb2576a5f</fromProcessItemId>
            <endStateId>guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-5e64</endStateId>
            <toProcessItemId>2025.f83a6c35-0e4d-4688-8634-5420a212e894</toProcessItemId>
            <guid>521d5113-d8d8-44a1-8d70-130b8c4d3f4e</guid>
            <versionId>4b86f891-ad23-4a42-9bc7-99fcbc9b63d4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.72ccbb62-e998-4341-8b4f-73dfb2576a5f</fromProcessItemId>
            <toProcessItemId>2025.f83a6c35-0e4d-4688-8634-5420a212e894</toProcessItemId>
        </link>
        <link name="To isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.39467e80-23e8-4351-8f6d-9eeb091cec8b</processLinkId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.72ccbb62-e998-4341-8b4f-73dfb2576a5f</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3</toProcessItemId>
            <guid>6f86ad8e-9988-4add-ac75-06d8942d8752</guid>
            <versionId>8640110a-5d27-4b30-8c8a-d4be0b225e52</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.72ccbb62-e998-4341-8b4f-73dfb2576a5f</fromProcessItemId>
            <toProcessItemId>2025.aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3</toProcessItemId>
        </link>
        <link name="To Get Exchange Rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4b19d26c-7645-4d60-89a3-93c198e7e25c</processLinkId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.211c5f8b-29dc-4b06-a513-872f476cd372</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.5dee0e8c-a1c1-4c11-8b25-281a75caca5e</toProcessItemId>
            <guid>600eea6d-87d1-4937-b11f-29a2a6d06222</guid>
            <versionId>918a488c-3bc1-4506-bd71-db90363f8985</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.211c5f8b-29dc-4b06-a513-872f476cd372</fromProcessItemId>
            <toProcessItemId>2025.5dee0e8c-a1c1-4c11-8b25-281a75caca5e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.1e3d5a73-eada-4596-8e42-c3f519785dfb</processLinkId>
            <processId>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f83a6c35-0e4d-4688-8634-5420a212e894</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3</toProcessItemId>
            <guid>3899f3de-2e71-4b7d-8750-2566d069ded4</guid>
            <versionId>d6e9b536-8717-4915-bbb9-b74be9af761a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.f83a6c35-0e4d-4688-8634-5420a212e894</fromProcessItemId>
            <toProcessItemId>2025.aa785ec6-2aa3-46ea-933a-7d6b3e81f5f3</toProcessItemId>
        </link>
    </process>
</teamworks>

