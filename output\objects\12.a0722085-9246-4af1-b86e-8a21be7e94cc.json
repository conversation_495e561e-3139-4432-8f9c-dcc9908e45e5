{"id": "12.a0722085-9246-4af1-b86e-8a21be7e94cc", "versionId": "7c7bae4a-dc4f-49bb-950c-4ad1a8315b92", "name": "DocumentGenerationResponse", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.a0722085-9246-4af1-b86e-8a21be7e94cc", "name": "DocumentGenerationResponse", "lastModified": "1693480732486", "lastModifiedBy": "abdelrahman.saleh", "classId": "12.a0722085-9246-4af1-b86e-8a21be7e94cc", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "true", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.a0722085-9246-4af1-b86e-8a21be7e94cc", "dependencySummary": "<dependencySummary id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-173a\">\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1739\">\r\n    <refId>/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1738\">\r\n      <name>externalId</name>\r\n      <value>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1737\">\r\n      <name>mimeType</name>\r\n      <value>xsd</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n</dependencySummary>", "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"complexType\":[{\"annotation\":{\"documentation\":[{\"content\":[\"Document Generation Response\"]}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{\"namespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"typeName\":\"DocumentGenerationResponse\"}],\"shadow\":[true]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"status\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":1,\"typeName\":\"boolean\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":1}]}]},\"name\":\"status\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Boolean\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"templatesResponse\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"_minOccurs\":0,\"typeName\":\"ResponseTemplatePojo\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":2147483647,\"order\":2}]}]},\"name\":\"templatesResponse\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/NBEODCR}BrokenReference\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8\"}}]},\"name\":\"DocumentGenerationResponse\"}],\"id\":\"_12.a0722085-9246-4af1-b86e-8a21be7e94cc\"}", "description": "Document Generation Response", "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1766", "versionId": "7c7bae4a-dc4f-49bb-950c-4ad1a8315b92", "definition": {"property": [{"name": "status", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "boolean", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "1", "maxOccurs": "1", "nillable": "false", "order": "1", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "templatesResponse", "description": {"isNull": "true"}, "classRef": "/12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "ResponseTemplatePojo", "typeNamespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "minOccurs": "0", "maxOccurs": "unbounded", "nillable": "false", "order": "2", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}], "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "DocumentGenerationResponse", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": "DocumentGenerationResponse", "namespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}