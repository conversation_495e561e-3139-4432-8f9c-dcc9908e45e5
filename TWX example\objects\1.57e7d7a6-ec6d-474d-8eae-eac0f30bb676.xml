<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676" name="Query BC Contract">
        <lastModified>1699596998385</lastModified>
        <lastModifiedBy>Naira</lastModifiedBy>
        <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.fb6629d3-6ae1-457b-8b38-af38db543c30</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdb</guid>
        <versionId>12a25f86-b68a-4af4-958b-1a646d73db0c</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:a6be0630e884ccca:-427a0b52:18bb428309d:-1f82" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4"},{"incoming":["4eb3106f-4d5a-4a4f-80b2-82c6bd88d120","d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"2d13efc7-fd6a-4ee2-88b6-df11c91426d9"},{"targetRef":"fb6629d3-6ae1-457b-8b38-af38db543c30","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d","sourceRef":"a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4"},{"startQuantity":1,"outgoing":["f883374b-953c-4fb6-8cf7-f7a22b978a04"],"incoming":["2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":105,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"MW_FC Query DC Contract","dataInputAssociation":[{"targetRef":"2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.4ff12b5f-6b69-4504-a730-046ee5c2000a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC Creation \/ Amendment Process Details\""]}}]},{"targetRef":"2055.8f786b77-ae66-4547-a444-d6ccb8969c42","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.ContractDetails.BasicDetails.flexCubeContractNo"]}}]},{"targetRef":"2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.42effea6-8d8b-437c-958a-da5cf9119674","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"fb6629d3-6ae1-457b-8b38-af38db543c30","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.95835270-515f-4794-a207-a5e2aa301c0e"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.ee7811d9-22b1-4727-9148-bcac74c306af"]},{"assignment":[{"to":{"declaredType":"TFormalExpression","content":["tw.local.queryBCContractResults"]}}],"sourceRef":["2055.004a9996-2e6b-4d60-821e-5db8e4ba2271"]}],"calledElement":"1.3875c748-15fc-40ef-bef1-ea4d905d7f75"},{"parallelMultiple":false,"outgoing":["c4d36477-c1e7-439b-8bf0-9a1da68c66d9"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e0f92768-0b1d-4714-8693-1a9cae9bed20"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8598f4cb-ae21-49e1-84ea-153ead3bb56f","otherAttributes":{"eventImplId":"d0616793-7785-4615-881e-a59f1e877202"}}],"attachedToRef":"fb6629d3-6ae1-457b-8b38-af38db543c30","extensionElements":{"nodeVisualInfo":[{"width":24,"x":140,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error7","declaredType":"boundaryEvent","id":"3a2bfe99-d7d8-4de6-873a-f7957c638f19","outputSet":{}},{"targetRef":"ab240dfe-8d20-4a88-8e96-fdd10ff72c98","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To isSuccessful?","declaredType":"sequenceFlow","id":"f883374b-953c-4fb6-8cf7-f7a22b978a04","sourceRef":"fb6629d3-6ae1-457b-8b38-af38db543c30"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.ad7cdaa4-504c-4708-8344-a1dc47262608"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.ed0addfb-42f9-4e4d-8bd2-ae08e8d7b003"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.2cd964e1-a67e-485f-8103-0a50c17067c8"},{"itemSubjectRef":"itm.12.*************-4cbb-a781-44d233d577c6","name":"queryBCContractResults","isCollection":false,"declaredType":"dataObject","id":"2056.0ec55024-95a1-4599-8cd5-980f0273de16"},{"outgoing":["725ef966-f992-4b08-889c-ede6a8e46c53","d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619"],"incoming":["f883374b-953c-4fb6-8cf7-f7a22b978a04"],"default":"725ef966-f992-4b08-889c-ede6a8e46c53","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":236,"y":99,"declaredType":"TNodeVisualInfo","height":32}]},"name":"isSuccessful?","declaredType":"exclusiveGateway","id":"ab240dfe-8d20-4a88-8e96-fdd10ff72c98"},{"targetRef":"304679b7-62fb-4dd1-8d05-2a1f24393037","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Mapping","declaredType":"sequenceFlow","id":"725ef966-f992-4b08-889c-ede6a8e46c53","sourceRef":"ab240dfe-8d20-4a88-8e96-fdd10ff72c98"},{"startQuantity":1,"outgoing":["4eb3106f-4d5a-4a4f-80b2-82c6bd88d120"],"incoming":["725ef966-f992-4b08-889c-ede6a8e46c53"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":421,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Mapping","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"304679b7-62fb-4dd1-8d05-2a1f24393037","scriptFormat":"text\/x-javascript","script":{"content":["if(!!tw.local.queryBCContractResults)\r\n{\r\n\r\n\/\/\ttw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();\r\n\/\/\ttw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;\r\n\/\/\ttw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;\r\n\/\/\ttw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;\r\n\/\/\ttw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);\r\n\/\/\ttw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);\r\n\/\/\ttw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;\r\n\/\/\ttw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;\r\n\/\/\ttw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);\r\n\/\/\ttw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);\r\n\/\/\ttw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;\r\n\/\/\ttw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);\r\n\ttw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;\r\n\ttw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);\r\n\t\r\n\/\/\ttw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;\r\n\/\/      tw.local.ContractDetails.CustomerInfo.customerName = tw.local.queryBCContractResults.TXTCUSTNAME;\r\n\t\r\ntw.local.ContractDetails.Parties = new tw.object.odcParties();\r\n\r\n\t\r\n\tfor(var i=0;i&lt;tw.local.queryBCContractResults.Contract_Parties.listLength;i++)\r\n\t{\r\n\t\tif(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"COLLECTING BANK\")\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\n\r\n\t\t}\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWEE\")\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\n\r\n\t\t}\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWER\")\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\n\t\t}\r\n\t\telse\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\n\r\n\t\t}\r\n\r\n\t}\r\n\/\/tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\n\/\/\t\r\n\/\/\tif(tw.local.queryBCContractResults.Contract_Multitnr != null)\r\n\/\/\t{\r\n\/\/\t\tfor(i=0;i&lt;tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)\r\n\/\/\t\t{\r\n\/\/\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();\r\n\/\/\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);\r\n\/\/\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);\r\n\/\/\t\t}\r\n\/\/\r\n\/\/\t}\r\ntw.local.ContractDetails.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\t\r\n\tif(tw.local.queryBCContractResults.Charges != null &amp;&amp; tw.local.queryBCContractResults.Charges[0] != null)\r\n\t{\r\n\t\tfor(i=0;i&lt;tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)\r\n\t\t{\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;\r\n\t\t\tif(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == \"y\")\r\n\t\t\t{\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;\t\t\t\r\n\t\t\t}\r\n\t\t\t\t\t\r\n\t\t}\r\n\t}\r\n\/\/\tif (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency==\"\")\r\n\/\/\t\ttw.local.queryBCContractResults.Currency= \"USD\";\r\n\r\n\/\/\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;\r\n\/\/\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;\t\t\r\n}\r\n\r\n\t\/\/tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();\r\n\t\/\/tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;\r\n\t\/\/tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;\r\n\r\n\t\r\n\t"]}},{"targetRef":"2d13efc7-fd6a-4ee2-88b6-df11c91426d9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"4eb3106f-4d5a-4a4f-80b2-82c6bd88d120","sourceRef":"304679b7-62fb-4dd1-8d05-2a1f24393037"},{"targetRef":"2d13efc7-fd6a-4ee2-88b6-df11c91426d9","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619","sourceRef":"ab240dfe-8d20-4a88-8e96-fdd10ff72c98"},{"startQuantity":1,"outgoing":["f1ca6063-0c89-4796-834d-523c900b68ff"],"incoming":["c4d36477-c1e7-439b-8bf0-9a1da68c66d9"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":423,"y":181,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Catch Errors","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"544bc577-**************-1af3744c36b3","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.error != undefined &amp;&amp; !!tw.local.error.errorText) {\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\n}else if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\n\ttw.local.error = new tw.object.AjaxError();\r\n}else{\r\n\ttw.local.error = new tw.object.AjaxError();\r\n}\r\n\r\ntw.local.error.errorText = \"Error Occured&lt;br&gt; Service Name : \"+tw.system.serviceFlow.name+\"&lt;br&gt; Error Message : \"+tw.local.errorMSG;"]}},{"targetRef":"544bc577-**************-1af3744c36b3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c4d36477-c1e7-439b-8bf0-9a1da68c66d9","sourceRef":"3a2bfe99-d7d8-4de6-873a-f7957c638f19"},{"incoming":["f1ca6063-0c89-4796-834d-523c900b68ff"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"06b11757-8691-404d-88c5-7931dd3e8286","otherAttributes":{"eventImplId":"86a88c86-a921-49e5-861b-2b138e6be302"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":557,"y":204,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"bc2bdd28-628c-42a1-8f18-62a326b9e796"},{"targetRef":"bc2bdd28-628c-42a1-8f18-62a326b9e796","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"f1ca6063-0c89-4796-834d-523c900b68ff","sourceRef":"544bc577-**************-1af3744c36b3"}],"laneSet":[{"id":"b37d2936-f644-4e7e-81bf-f1892f66dcb1","lane":[{"flowNodeRef":["a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4","2d13efc7-fd6a-4ee2-88b6-df11c91426d9","fb6629d3-6ae1-457b-8b38-af38db543c30","3a2bfe99-d7d8-4de6-873a-f7957c638f19","ab240dfe-8d20-4a88-8e96-fdd10ff72c98","304679b7-62fb-4dd1-8d05-2a1f24393037","544bc577-**************-1af3744c36b3","bc2bdd28-628c-42a1-8f18-62a326b9e796"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"ddbc1ae6-7303-4703-85fb-5b76369a2ea0","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Query BC Contract","declaredType":"process","id":"1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"ContractDetails","isCollection":false,"id":"2055.1447a720-69f1-46ca-8d27-88a12b035edf"}],"inputSet":[{"dataInputRefs":["2055.9465f3bb-e80a-444e-8672-899423d668fb"]}],"outputSet":[{"dataOutputRefs":["2055.1447a720-69f1-46ca-8d27-88a12b035edf"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"599IAVC222440002\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"ContractDetails","isCollection":false,"id":"2055.9465f3bb-e80a-444e-8672-899423d668fb"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="ContractDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9465f3bb-e80a-444e-8672-899423d668fb</processParameterId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "599IAVC222440002";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1b123bfd-ab13-4a17-9e35-3b53f1f05858</guid>
            <versionId>c7f8b180-a3c5-4233-b1b1-7ce1e08bc181</versionId>
        </processParameter>
        <processParameter name="ContractDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1447a720-69f1-46ca-8d27-88a12b035edf</processParameterId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3c5b0515-0b0d-4365-b99c-dcffeb7b37f3</guid>
            <versionId>de965ad7-7e69-4bce-8660-1f8714211f0f</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fadbd4a8-eb37-44b9-9536-2302e262f759</processParameterId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>be3ece56-576f-4b02-9ebc-d9aa51f45b71</guid>
            <versionId>3bc42887-1097-4152-a341-81cc27066b51</versionId>
        </processParameter>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ad7cdaa4-504c-4708-8344-a1dc47262608</processVariableId>
            <description isNull="true" />
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f0c3aa9e-4b39-4001-9e9f-0f3b1befc056</guid>
            <versionId>5698ae96-4306-4015-b78b-ed076cfd981b</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ed0addfb-42f9-4e4d-8bd2-ae08e8d7b003</processVariableId>
            <description isNull="true" />
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c8c7c03c-e29d-4e80-8730-5a835d1478b5</guid>
            <versionId>d8167f23-e8a0-4cef-ace0-3dd471577033</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2cd964e1-a67e-485f-8103-0a50c17067c8</processVariableId>
            <description isNull="true" />
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b5a5420a-ca21-425c-9337-3494988d7715</guid>
            <versionId>fd193d35-d1ba-4d42-90d3-68f841051325</versionId>
        </processVariable>
        <processVariable name="queryBCContractResults">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0ec55024-95a1-4599-8cd5-980f0273de16</processVariableId>
            <description isNull="true" />
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>51771243-cb9e-427e-9e3a-b2dbb03f88bb</guid>
            <versionId>11ffe0db-af07-439b-9407-c29fc7ac34f2</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9</processItemId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.cbdb548d-fbc6-4394-b2f9-39f2cea25888</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd</guid>
            <versionId>10ae37e0-00ae-4131-9811-03a6d828dff2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.cbdb548d-fbc6-4394-b2f9-39f2cea25888</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>5b404c69-8937-4eba-9f11-aa9d3a251736</guid>
                <versionId>c58f1025-a3af-44b0-a0e1-cab599bc8f32</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.544bc577-**************-1af3744c36b3</processItemId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0eb26366-fdbc-4a04-a5c5-90d9d502954e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7be1ec94:2ff3</guid>
            <versionId>48b722b8-7913-47f1-9156-e5b124119467</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="423" y="181">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0eb26366-fdbc-4a04-a5c5-90d9d502954e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.error != undefined &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;</script>
                <isRule>false</isRule>
                <guid>96f07f83-4d07-46d6-be52-6d1824632628</guid>
                <versionId>1ab0aade-4f89-42bc-af7e-15174fd3c5b8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.bc2bdd28-628c-42a1-8f18-62a326b9e796</processItemId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.4098f339-f9f2-414a-b616-aa0c01aa06f0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7be1ec94:2ff5</guid>
            <versionId>7ee9f4e6-9f4e-4f66-b798-9555c5bf15d7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="557" y="204">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.4098f339-f9f2-414a-b616-aa0c01aa06f0</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>4eb014f7-e21f-4848-bf57-d8e7840398d4</guid>
                <versionId>b7b57378-dd7e-498a-991a-aaae1ce6633d</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bbcb68e7-1b3d-4961-a72b-9332031a0c85</parameterMappingId>
                    <processParameterId>2055.fadbd4a8-eb37-44b9-9536-2302e262f759</processParameterId>
                    <parameterMappingParentId>3007.4098f339-f9f2-414a-b616-aa0c01aa06f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c154690e-a21a-4f88-b395-4bc9501ae30b</guid>
                    <versionId>1ff0c83d-a787-430a-a99b-66b1004ccaf5</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fb6629d3-6ae1-457b-8b38-af38db543c30</processItemId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <name>MW_FC Query DC Contract</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.544bc577-**************-1af3744c36b3</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1be4</guid>
            <versionId>99be730b-0731-4b2a-b53f-1a7293e390ee</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.bf74b978-b84a-4f28-8ced-6e03868fd39d</processItemPrePostId>
                <processItemId>2025.fb6629d3-6ae1-457b-8b38-af38db543c30</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>e220e3b6-1da5-43dc-a7a7-4e6b001470da</guid>
                <versionId>c9ec6f15-ab5b-441c-911f-9e3b04f30ba9</versionId>
            </processPrePosts>
            <layoutData x="105" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error7</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b7be1ec94:2ff3</errorHandlerItem>
                <errorHandlerItemId>2025.544bc577-**************-1af3744c36b3</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.3875c748-15fc-40ef-bef1-ea4d905d7f75</attachedProcessRef>
                <guid>33ac653c-aa56-4696-bf9e-8aefdc2a3128</guid>
                <versionId>d545cd77-70be-4100-86a3-bfe81f673e82</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cb447438-fb45-4042-b27e-db89711daf62</parameterMappingId>
                    <processParameterId>2055.42effea6-8d8b-437c-958a-da5cf9119674</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>080ffbf6-c44a-492c-af73-900366a175cb</guid>
                    <versionId>052d0cfa-d6a6-45c8-bf64-465104627b5c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a5567a74-22d5-4a26-8f04-706f61d30d84</parameterMappingId>
                    <processParameterId>2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>84bc47bd-b102-4aee-878c-e6a05a12afdd</guid>
                    <versionId>113f0bce-cf07-48fc-9c8a-ef4a55b38587</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d2e9795f-5611-4da5-966a-54ee401ad2ba</parameterMappingId>
                    <processParameterId>2055.ee7811d9-22b1-4727-9148-bcac74c306af</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6424d5e1-dd9c-499d-a953-58d9701a414e</guid>
                    <versionId>13225021-51ba-4716-9a39-8a5e2f8346db</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3a2470dd-5d8f-41c5-9cf3-a8ea1199f4fd</parameterMappingId>
                    <processParameterId>2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4708d74e-6910-4ad3-a6a0-546374f5e00f</guid>
                    <versionId>1b32dacd-6582-48d6-a3a1-9924f12dbeb6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="SCQueryBCContractRes">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5c5f975b-1244-4546-af76-5521962eecc2</parameterMappingId>
                    <processParameterId>2055.004a9996-2e6b-4d60-821e-5db8e4ba2271</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.queryBCContractResults</value>
                    <classRef>/12.*************-4cbb-a781-44d233d577c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>18879049-8241-4238-ba09-532a903afb83</guid>
                    <versionId>22663580-18ec-4336-981f-de51bed73cae</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3492e501-40ba-47c4-a236-ee1edabad6b8</parameterMappingId>
                    <processParameterId>2055.4ff12b5f-6b69-4504-a730-046ee5c2000a</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9b754073-8f62-46c5-99b9-9fc32798e3f5</guid>
                    <versionId>465cccd4-16c3-4862-bb6b-134f80906b45</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.08f48c77-db9f-4fc2-b207-f220d1d2c26f</parameterMappingId>
                    <processParameterId>2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC Creation / Amendment Process Details"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>df1aedb3-7450-4c4b-9372-390a68e4d2ea</guid>
                    <versionId>4f5ec440-c33c-4360-99da-a4bf34adce20</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.88ae70db-0b0b-40c3-97a9-280ec606f5fc</parameterMappingId>
                    <processParameterId>2055.95835270-515f-4794-a207-a5e2aa301c0e</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c9ad9eee-0db4-4feb-b0d5-b32064dfa6c2</guid>
                    <versionId>8bd2c8b0-f9fd-41bd-94e5-9888cf1f6d94</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="contractNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e3eb6d59-f474-425c-9661-0a7f51945d75</parameterMappingId>
                    <processParameterId>2055.8f786b77-ae66-4547-a444-d6ccb8969c42</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ContractDetails.BasicDetails.flexCubeContractNo</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0167defd-d8d4-4a77-9f6f-5cc0e1818b5d</guid>
                    <versionId>903d5d71-4ad9-41db-bd2d-e8d8941a5d31</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.506d7c61-5c88-459b-a025-158a84f9aa63</parameterMappingId>
                    <processParameterId>2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d6c30cc6-de5f-4d4c-a624-5ac7919ecc12</guid>
                    <versionId>c509b46b-24df-4a25-a5f0-641c51f6bf98</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.04b11798-f5ad-4139-9619-af4e05c6f858</parameterMappingId>
                    <processParameterId>2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9</processParameterId>
                    <parameterMappingParentId>3012.67253851-197f-41ec-9b6f-ea87fbbc6165</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8496b783-ea6e-4ff7-bbdf-4204d3e8f9a6</guid>
                    <versionId>e7944257-4e76-4680-bb5c-4f9c9d780b8b</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.304679b7-62fb-4dd1-8d05-2a1f24393037</processItemId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <name>Mapping</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ae3dea46-0837-44b2-8963-ff87bd3105c9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1d02</guid>
            <versionId>bb1caa7f-0a97-4b39-adf6-b5473a188610</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="421" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ae3dea46-0837-44b2-8963-ff87bd3105c9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(!!tw.local.queryBCContractResults)&#xD;
{&#xD;
&#xD;
//	tw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();&#xD;
//	tw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;&#xD;
//	tw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;&#xD;
//	tw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;&#xD;
//	tw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);&#xD;
//	tw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);&#xD;
//	tw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;&#xD;
//	tw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;&#xD;
//	tw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);&#xD;
//	tw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);&#xD;
//	tw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;&#xD;
//	tw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);&#xD;
	tw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;&#xD;
	tw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);&#xD;
	&#xD;
//	tw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;&#xD;
//      tw.local.ContractDetails.CustomerInfo.customerName = tw.local.queryBCContractResults.TXTCUSTNAME;&#xD;
	&#xD;
tw.local.ContractDetails.Parties = new tw.object.odcParties();&#xD;
&#xD;
	&#xD;
	for(var i=0;i&lt;tw.local.queryBCContractResults.Contract_Parties.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "COLLECTING BANK")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
			tw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWEE")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();&#xD;
			tw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWER")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();&#xD;
			tw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
		}&#xD;
		else&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
&#xD;
	}&#xD;
//tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
//	&#xD;
//	if(tw.local.queryBCContractResults.Contract_Multitnr != null)&#xD;
//	{&#xD;
//		for(i=0;i&lt;tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)&#xD;
//		{&#xD;
//			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();&#xD;
//			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);&#xD;
//			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);&#xD;
//		}&#xD;
//&#xD;
//	}&#xD;
tw.local.ContractDetails.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();	&#xD;
	if(tw.local.queryBCContractResults.Charges != null &amp;&amp; tw.local.queryBCContractResults.Charges[0] != null)&#xD;
	{&#xD;
		for(i=0;i&lt;tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)&#xD;
		{&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;&#xD;
			if(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;			&#xD;
			}&#xD;
					&#xD;
		}&#xD;
	}&#xD;
//	if (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency=="")&#xD;
//		tw.local.queryBCContractResults.Currency= "USD";&#xD;
&#xD;
//		tw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;&#xD;
//		tw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;		&#xD;
}&#xD;
&#xD;
	//tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();&#xD;
	//tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;&#xD;
	//tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;&#xD;
&#xD;
	&#xD;
	</script>
                <isRule>false</isRule>
                <guid>1ccc2381-46ad-4ac3-8618-b3233816193d</guid>
                <versionId>78445592-fcd6-472e-8f15-154a1dcaeff8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98</processItemId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <name>isSuccessful?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.24175661-0df5-4c1c-8624-db89f48a7711</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7b4cc468:1cfc</guid>
            <versionId>cc19c15f-c82c-4dda-854c-ce5e61249613</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="236" y="99">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.24175661-0df5-4c1c-8624-db89f48a7711</switchId>
                <guid>047df29e-120c-4e38-8943-bc429b0338cd</guid>
                <versionId>6bd83ed8-a53e-4a83-b018-987a36f2c535</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.843e0b89-4732-4c61-97d3-2933d9d9bef5</switchConditionId>
                    <switchId>3013.24175661-0df5-4c1c-8624-db89f48a7711</switchId>
                    <seq>1</seq>
                    <endStateId>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-1f83</endStateId>
                    <condition>tw.local.isSuccessful	  ==	  false</condition>
                    <guid>be763256-9106-4938-94ac-4f6ba9d17b18</guid>
                    <versionId>2a7d74e8-cda2-438f-b6b5-c474b25e45ce</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.fb6629d3-6ae1-457b-8b38-af38db543c30</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Query BC Contract" id="1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="ContractDetails" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.9465f3bb-e80a-444e-8672-899423d668fb">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "599IAVC222440002";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="ContractDetails" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.1447a720-69f1-46ca-8d27-88a12b035edf" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.9465f3bb-e80a-444e-8672-899423d668fb</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.1447a720-69f1-46ca-8d27-88a12b035edf</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="b37d2936-f644-4e7e-81bf-f1892f66dcb1">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="ddbc1ae6-7303-4703-85fb-5b76369a2ea0" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2d13efc7-fd6a-4ee2-88b6-df11c91426d9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fb6629d3-6ae1-457b-8b38-af38db543c30</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3a2bfe99-d7d8-4de6-873a-f7957c638f19</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ab240dfe-8d20-4a88-8e96-fdd10ff72c98</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>304679b7-62fb-4dd1-8d05-2a1f24393037</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>544bc577-**************-1af3744c36b3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bc2bdd28-628c-42a1-8f18-62a326b9e796</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="2d13efc7-fd6a-4ee2-88b6-df11c91426d9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4eb3106f-4d5a-4a4f-80b2-82c6bd88d120</ns16:incoming>
                        
                        
                        <ns16:incoming>d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4" targetRef="fb6629d3-6ae1-457b-8b38-af38db543c30" name="To End" id="2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.3875c748-15fc-40ef-bef1-ea4d905d7f75" isForCompensation="false" startQuantity="1" completionQuantity="1" name="MW_FC Query DC Contract" id="fb6629d3-6ae1-457b-8b38-af38db543c30">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="105" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d</ns16:incoming>
                        
                        
                        <ns16:outgoing>f883374b-953c-4fb6-8cf7-f7a22b978a04</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4ff12b5f-6b69-4504-a730-046ee5c2000a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC Creation / Amendment Process Details"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8f786b77-ae66-4547-a444-d6ccb8969c42</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.ContractDetails.BasicDetails.flexCubeContractNo</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.42effea6-8d8b-437c-958a-da5cf9119674</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.95835270-515f-4794-a207-a5e2aa301c0e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.ee7811d9-22b1-4727-9148-bcac74c306af</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.004a9996-2e6b-4d60-821e-5db8e4ba2271</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.queryBCContractResults</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="fb6629d3-6ae1-457b-8b38-af38db543c30" parallelMultiple="false" name="Error7" id="3a2bfe99-d7d8-4de6-873a-f7957c638f19">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="140" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c4d36477-c1e7-439b-8bf0-9a1da68c66d9</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e0f92768-0b1d-4714-8693-1a9cae9bed20" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8598f4cb-ae21-49e1-84ea-153ead3bb56f" eventImplId="d0616793-7785-4615-881e-a59f1e877202">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="fb6629d3-6ae1-457b-8b38-af38db543c30" targetRef="ab240dfe-8d20-4a88-8e96-fdd10ff72c98" name="To isSuccessful?" id="f883374b-953c-4fb6-8cf7-f7a22b978a04">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.ad7cdaa4-504c-4708-8344-a1dc47262608" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.ed0addfb-42f9-4e4d-8bd2-ae08e8d7b003" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.2cd964e1-a67e-485f-8103-0a50c17067c8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.*************-4cbb-a781-44d233d577c6" isCollection="false" name="queryBCContractResults" id="2056.0ec55024-95a1-4599-8cd5-980f0273de16" />
                    
                    
                    <ns16:exclusiveGateway default="725ef966-f992-4b08-889c-ede6a8e46c53" name="isSuccessful?" id="ab240dfe-8d20-4a88-8e96-fdd10ff72c98">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="236" y="99" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f883374b-953c-4fb6-8cf7-f7a22b978a04</ns16:incoming>
                        
                        
                        <ns16:outgoing>725ef966-f992-4b08-889c-ede6a8e46c53</ns16:outgoing>
                        
                        
                        <ns16:outgoing>d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="ab240dfe-8d20-4a88-8e96-fdd10ff72c98" targetRef="304679b7-62fb-4dd1-8d05-2a1f24393037" name="To Mapping" id="725ef966-f992-4b08-889c-ede6a8e46c53">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Mapping" id="304679b7-62fb-4dd1-8d05-2a1f24393037">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="421" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>725ef966-f992-4b08-889c-ede6a8e46c53</ns16:incoming>
                        
                        
                        <ns16:outgoing>4eb3106f-4d5a-4a4f-80b2-82c6bd88d120</ns16:outgoing>
                        
                        
                        <ns16:script>if(!!tw.local.queryBCContractResults)&#xD;
{&#xD;
&#xD;
//	tw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();&#xD;
//	tw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;&#xD;
//	tw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;&#xD;
//	tw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;&#xD;
//	tw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);&#xD;
//	tw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);&#xD;
//	tw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;&#xD;
//	tw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;&#xD;
//	tw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);&#xD;
//	tw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);&#xD;
//	tw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;&#xD;
//	tw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);&#xD;
	tw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;&#xD;
	tw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);&#xD;
	&#xD;
//	tw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;&#xD;
//      tw.local.ContractDetails.CustomerInfo.customerName = tw.local.queryBCContractResults.TXTCUSTNAME;&#xD;
	&#xD;
tw.local.ContractDetails.Parties = new tw.object.odcParties();&#xD;
&#xD;
	&#xD;
	for(var i=0;i&lt;tw.local.queryBCContractResults.Contract_Parties.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "COLLECTING BANK")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
			tw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWEE")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();&#xD;
			tw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
&#xD;
		}&#xD;
		else if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == "DRAWER")&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();&#xD;
			tw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;&#xD;
		}&#xD;
		else&#xD;
		{&#xD;
			tw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;&#xD;
			tw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;			&#xD;
&#xD;
		}&#xD;
&#xD;
	}&#xD;
//tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
//	&#xD;
//	if(tw.local.queryBCContractResults.Contract_Multitnr != null)&#xD;
//	{&#xD;
//		for(i=0;i&lt;tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)&#xD;
//		{&#xD;
//			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();&#xD;
//			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);&#xD;
//			tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);&#xD;
//		}&#xD;
//&#xD;
//	}&#xD;
tw.local.ContractDetails.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();	&#xD;
	if(tw.local.queryBCContractResults.Charges != null &amp;&amp; tw.local.queryBCContractResults.Charges[0] != null)&#xD;
	{&#xD;
		for(i=0;i&lt;tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)&#xD;
		{&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;&#xD;
			tw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;&#xD;
			if(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;			&#xD;
			}&#xD;
					&#xD;
		}&#xD;
	}&#xD;
//	if (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency=="")&#xD;
//		tw.local.queryBCContractResults.Currency= "USD";&#xD;
&#xD;
//		tw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;&#xD;
//		tw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;		&#xD;
}&#xD;
&#xD;
	//tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();&#xD;
	//tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;&#xD;
	//tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;&#xD;
&#xD;
	&#xD;
	</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="304679b7-62fb-4dd1-8d05-2a1f24393037" targetRef="2d13efc7-fd6a-4ee2-88b6-df11c91426d9" name="To End" id="4eb3106f-4d5a-4a4f-80b2-82c6bd88d120">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="ab240dfe-8d20-4a88-8e96-fdd10ff72c98" targetRef="2d13efc7-fd6a-4ee2-88b6-df11c91426d9" name="To End" id="d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="544bc577-**************-1af3744c36b3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="423" y="181" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c4d36477-c1e7-439b-8bf0-9a1da68c66d9</ns16:incoming>
                        
                        
                        <ns16:outgoing>f1ca6063-0c89-4796-834d-523c900b68ff</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.error != undefined &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if (tw.system.error != undefined &amp;&amp; tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="3a2bfe99-d7d8-4de6-873a-f7957c638f19" targetRef="544bc577-**************-1af3744c36b3" name="To Catch Errors" id="c4d36477-c1e7-439b-8bf0-9a1da68c66d9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="bc2bdd28-628c-42a1-8f18-62a326b9e796">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="557" y="204" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f1ca6063-0c89-4796-834d-523c900b68ff</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="06b11757-8691-404d-88c5-7931dd3e8286" eventImplId="86a88c86-a921-49e5-861b-2b138e6be302">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="544bc577-**************-1af3744c36b3" targetRef="bc2bdd28-628c-42a1-8f18-62a326b9e796" name="To End Event" id="f1ca6063-0c89-4796-834d-523c900b68ff">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f1ca6063-0c89-4796-834d-523c900b68ff</processLinkId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.544bc577-**************-1af3744c36b3</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.bc2bdd28-628c-42a1-8f18-62a326b9e796</toProcessItemId>
            <guid>fb031cc9-561f-4012-b75c-9da0cfb391ac</guid>
            <versionId>0d9d73eb-27b9-424b-9217-7a380b964051</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.544bc577-**************-1af3744c36b3</fromProcessItemId>
            <toProcessItemId>2025.bc2bdd28-628c-42a1-8f18-62a326b9e796</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619</processLinkId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98</fromProcessItemId>
            <endStateId>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-1f83</endStateId>
            <toProcessItemId>2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9</toProcessItemId>
            <guid>19540ee3-d321-46bd-bd4d-612fe0e8cf51</guid>
            <versionId>14605d33-6baa-44f6-943a-52040b6e4e0a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98</fromProcessItemId>
            <toProcessItemId>2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9</toProcessItemId>
        </link>
        <link name="To Mapping">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.725ef966-f992-4b08-889c-ede6a8e46c53</processLinkId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.304679b7-62fb-4dd1-8d05-2a1f24393037</toProcessItemId>
            <guid>191d8572-b38a-4334-a874-a2d6e937efaa</guid>
            <versionId>b8fad230-d72a-48d5-b6f9-474251d9ec9e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98</fromProcessItemId>
            <toProcessItemId>2025.304679b7-62fb-4dd1-8d05-2a1f24393037</toProcessItemId>
        </link>
        <link name="To isSuccessful?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f883374b-953c-4fb6-8cf7-f7a22b978a04</processLinkId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fb6629d3-6ae1-457b-8b38-af38db543c30</fromProcessItemId>
            <endStateId>guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754</endStateId>
            <toProcessItemId>2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98</toProcessItemId>
            <guid>17f41caa-9812-42fa-8aea-d2ff108016d4</guid>
            <versionId>ce9cf400-f109-49ce-b9ae-a68d1f3a3ba2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fb6629d3-6ae1-457b-8b38-af38db543c30</fromProcessItemId>
            <toProcessItemId>2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4eb3106f-4d5a-4a4f-80b2-82c6bd88d120</processLinkId>
            <processId>1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.304679b7-62fb-4dd1-8d05-2a1f24393037</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9</toProcessItemId>
            <guid>c893897b-59e5-4b12-8778-0baf5f8e78cb</guid>
            <versionId>f072e2e9-a981-4d9a-8461-dfcd5658a2c1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.304679b7-62fb-4dd1-8d05-2a1f24393037</fromProcessItemId>
            <toProcessItemId>2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9</toProcessItemId>
        </link>
    </process>
</teamworks>

